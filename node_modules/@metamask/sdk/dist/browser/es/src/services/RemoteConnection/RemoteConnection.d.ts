import { ChannelConfig, CommunicationLayerPreference, DappMetadata, DisconnectOptions, ECIESProps, EventType, KeyInfo, RemoteCommunication, StorageManagerProps, TrackingEvents } from '@metamask/sdk-communication-layer';
import { MetaMaskInstaller } from '../../Platform/MetaMaskInstaller';
import { PlatformManager } from '../../Platform/PlatfformManager';
import { MetaMaskSDK } from '../../sdk';
import { SDKLoggingOptions } from '../../types/SDKLoggingOptions';
import { Analytics } from '../Analytics';
import { StartConnectionExtras } from './ConnectionManager';
import { EventHandler } from './EventListeners';
export interface RemoteConnectionProps {
    timer?: {
        runBackgroundTimer?: (cb: () => void, ms: number) => number;
        stopBackgroundTimer?: () => void;
    };
    communicationLayerPreference: CommunicationLayerPreference;
    dappMetadata?: DappMetadata;
    _source?: string;
    enableAnalytics?: boolean;
    analytics: Analytics;
    sdk: MetaMaskSDK;
    transports?: string[];
    platformManager: PlatformManager;
    communicationServerUrl?: string;
    ecies?: ECIESProps;
    storage?: StorageManagerProps;
    logging?: SDKLoggingOptions;
    preferDesktop?: boolean;
    getMetaMaskInstaller: () => MetaMaskInstaller;
    connectWithExtensionProvider?: () => void;
    /**
     * @deprecated Use the 'display_uri' event on the provider instead.
     * Listen to this event to get the QR code URL and customize your UI.
     * Example:
     * sdk.getProvider().on('display_uri', (uri: string) => {
     *   // Use the uri to display a QR code or customize your UI
     * });
     */
    modals: {
        onPendingModalDisconnect?: () => void;
        install?: (args: {
            link: string;
            debug?: boolean;
            preferDesktop?: boolean;
            installer: MetaMaskInstaller;
            terminate?: () => void;
            connectWithExtension?: () => void;
            onAnalyticsEvent: ({ event, params, }: {
                event: TrackingEvents;
                params?: Record<string, unknown>;
            }) => void;
        }) => {
            unmount?: (shouldTerminate?: boolean) => void;
            mount?: (link: string) => void;
        };
        otp?: ({ debug, onDisconnect, }: {
            debug?: boolean;
            onDisconnect?: () => void;
        }) => {
            mount?: () => void;
            updateOTPValue?: (otpValue: string) => void;
            unmount?: () => void;
        };
    };
}
export interface RemoteConnectionState {
    connector?: RemoteCommunication;
    qrcodeLink?: string;
    useDeeplink?: boolean;
    developerMode: boolean;
    analytics?: Analytics;
    authorized: boolean;
    reconnection: boolean;
    deeplinkProtocol: boolean;
    preferDesktop?: boolean;
    listeners: {
        event: EventType;
        handler: EventHandler;
    }[];
    communicationLayerPreference?: CommunicationLayerPreference;
    platformManager?: PlatformManager;
    pendingModal?: {
        mount?: (props?: {
            displayOTP?: boolean;
        }) => void;
        updateOTPValue?: (otpValue: string) => void;
        unmount?: () => void;
    };
    installModal?: {
        unmount?: (shouldTerminate: boolean) => void;
        mount?: (link: string) => void;
    };
    /**
     * Wait for value from metamask mobile
     */
    otpAnswer?: string;
}
export declare class RemoteConnection {
    private options;
    state: RemoteConnectionState;
    constructor(options: RemoteConnectionProps);
    /**
     * This will start the installer or pending modal and resolve once it is displayed.
     * It doesn't wait for the actual connection to be authorized.
     */
    startConnection(extras?: StartConnectionExtras): Promise<void>;
    initRemoteCommunication({ sdkInstance, }: {
        sdkInstance: MetaMaskSDK;
    }): Promise<void>;
    showActiveModal(): void;
    closeModal(): void;
    getUniversalLink(): string;
    getChannelConfig(): ChannelConfig | undefined;
    getKeyInfo(): KeyInfo | undefined;
    getConnector(): RemoteCommunication;
    getPlatformManager(): PlatformManager;
    isConnected(): boolean;
    isAuthorized(): boolean;
    isPaused(): boolean | undefined;
    disconnect(options?: DisconnectOptions): void;
}
//# sourceMappingURL=RemoteConnection.d.ts.map