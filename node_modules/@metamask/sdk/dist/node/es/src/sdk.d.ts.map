{"version": 3, "file": "sdk.d.ts", "sourceRoot": "", "sources": ["../../../../src/sdk.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,qBAAqB,CAAC;AAC7D,OAAO,EACL,4BAA4B,EAC5B,YAAY,EACZ,mBAAmB,EACpB,MAAM,mCAAmC,CAAC;AAG3C,OAAO,aAAa,MAAM,eAAe,CAAC;AAE1C,OAAO,EAAE,iBAAiB,EAAE,MAAM,8BAA8B,CAAC;AACjE,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAE9D,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AASjD,OAAO,EAAE,YAAY,EAAE,MAAM,qEAAqE,CAAC;AACnG,OAAO,EACL,gBAAgB,EAChB,qBAAqB,EACtB,MAAM,6BAA6B,CAAC;AACrC,OAAO,EACL,uBAAuB,EACvB,oBAAoB,EACrB,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAGpD,MAAM,WAAW,kBAAkB;IACjC;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,cAAc,CAAC,EAAE,YAAY,CAAC;IAE9B;;OAEG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;IAEzB;;OAEG;IACH,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAE9B;;OAEG;IACH,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAE9B;;OAEG;IACH,4BAA4B,CAAC,EAAE,OAAO,CAAC;IAEvC;;OAEG;IACH,2BAA2B,CAAC,EAAE,OAAO,CAAC;IAEtC;;OAEG;IACH,aAAa,CAAC,EAAE,OAAO,CAAC;IAExB;;OAEG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC;IAEnB;;OAEG;IACH,YAAY,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,IAAI,CAAC;IAErC;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IAEtB;;OAEG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;IAEzB;;OAEG;IACH,4BAA4B,CAAC,EAAE,4BAA4B,CAAC;IAE5D;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC;IAEtB;;OAEG;IACH,YAAY,EAAE,YAAY,CAAC;IAE3B;;OAEG;IACH,KAAK,CAAC,EAAE,GAAG,CAAC;IAEZ;;OAEG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAE1B;;OAEG;IACH,aAAa,CAAC,EAAE,OAAO,CAAC;IAExB;;;;;;;OAOG;IACH,EAAE,CAAC,EAAE,YAAY,CAAC;IAElB;;;;;;;OAOG;IACH,MAAM,CAAC,EAAE,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IAEzC;;OAEG;IACH,sBAAsB,CAAC,EAAE,MAAM,CAAC;IAEhC;;OAEG;IACH,OAAO,CAAC,EAAE,mBAAmB,CAAC;IAE9B;;OAEG;IACH,OAAO,CAAC,EAAE,iBAAiB,CAAC;IAE5B;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAKjB,WAAW,CAAC,EAAE;QACZ,KAAK,CAAC,EAAE,OAAO,CAAC;QAChB,OAAO,CAAC,EAAE,OAAO,CAAC;KACnB,CAAC;CACH;AAED,qBAAa,WAAY,SAAQ,aAAa;IACrC,OAAO,EAAE,kBAAkB,CAAC;IAE5B,cAAc,CAAC,EAAE,WAAW,CAAC;IAE7B,WAAW,CAAC,EAAE,WAAW,CAAC;IAE1B,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;IAEpC,SAAS,CAAC,EAAE,iBAAiB,CAAC;IAE9B,eAAe,CAAC,EAAE,eAAe,CAAC;IAElC,YAAY,CAAC,EAAE,YAAY,CAAC;IAE5B,eAAe,UAAS;IAExB,SAAS,EAAE,sBAAsB,GAAG,SAAS,CAAC;IAE9C,YAAY,UAAS;IAErB,cAAc,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAa;IAEvD,KAAK,UAAS;IAEd,SAAS,CAAC,EAAE,SAAS,CAAC;IAE7B,OAAO,CAAC,gBAAgB,CAAS;IAE1B,kBAAkB,EAAE,MAAM,EAAE,CAAU;gBAG3C,OAAO,GAAE,kBAoBR;IA+CH;;;OAGG;IACU,IAAI;IAIjB;;;OAGG;IACH,iBAAiB;IAIjB;;;;;OAKG;IACH,0BAA0B,IAAI,OAAO;IAOrC;;;OAGG;IACG,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;IAM5B,cAAc,CAAC,EAAE,GAAG,EAAE,EAAE;QAAE,GAAG,EAAE,MAAM,CAAA;KAAE;IAIvC,WAAW,CAAC,GAAG,EAAE;QAAE,MAAM,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,GAAG,EAAE,CAAA;KAAE;IAIxD,MAAM;IAIN;;OAEG;IACH,UAAU;IAKV,YAAY;IAIZ,SAAS;IAIT,aAAa;IAIb,mBAAmB,CAAC,OAAO,EAAE,OAAO;IAIpC,mBAAmB;IAKnB,WAAW,IAAI,WAAW,GAAG,SAAS;IAStC,iBAAiB,IAAI,WAAW;IAQhC;;;OAGG;IACH,gBAAgB;IAYhB,YAAY;IAIZ,aAAa;IAIb,UAAU;IAIV,eAAe;IAMf,iBAAiB;IAIjB,KAAK;IAIL,SAAS;IAIT,iBAAiB;IAIjB,oBAAoB;IAIpB,gBAAgB,IAAI,YAAY,GAAG,SAAS;IAI5C,WAAW;IAIX,UAAU;IAIV,cAAc;IAIP,IAAI,CAAC,CAAC,SAAS,oBAAoB,EACxC,KAAK,EAAE,CAAC,EACR,OAAO,EAAE,uBAAuB,CAAC,CAAC,CAAC,GAClC,OAAO;IAIH,EAAE,CAAC,CAAC,SAAS,oBAAoB,EACtC,KAAK,EAAE,CAAC,EACR,QAAQ,EAAE,CAAC,OAAO,EAAE,uBAAuB,CAAC,CAAC,CAAC,KAAK,IAAI,GACtD,IAAI;CAGR"}