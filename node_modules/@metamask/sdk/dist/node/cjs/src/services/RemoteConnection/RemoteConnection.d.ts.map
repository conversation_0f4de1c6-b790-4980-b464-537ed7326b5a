{"version": 3, "file": "RemoteConnection.d.ts", "sourceRoot": "", "sources": ["../../../../../../src/services/RemoteConnection/RemoteConnection.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,aAAa,EACb,4BAA4B,EAC5B,YAAY,EACZ,iBAAiB,EACjB,UAAU,EACV,SAAS,EACT,OAAO,EACP,mBAAmB,EACnB,mBAAmB,EACnB,cAAc,EACf,MAAM,mCAAmC,CAAC;AAC3C,OAAO,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AACrE,OAAO,EAAE,eAAe,EAAE,MAAM,iCAAiC,CAAC;AAClE,OAAO,EAAE,WAAW,EAAE,MAAM,WAAW,CAAC;AACxC,OAAO,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAC;AAIlE,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAIzC,OAAO,EAAmB,qBAAqB,EAAE,MAAM,qBAAqB,CAAC;AAC7E,OAAO,EAAE,YAAY,EAAkB,MAAM,kBAAkB,CAAC;AAGhE,MAAM,WAAW,qBAAqB;IACpC,KAAK,CAAC,EAAE;QACN,kBAAkB,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,IAAI,EAAE,EAAE,EAAE,MAAM,KAAK,MAAM,CAAC;QAC5D,mBAAmB,CAAC,EAAE,MAAM,IAAI,CAAC;KAClC,CAAC;IACF,4BAA4B,EAAE,4BAA4B,CAAC;IAC3D,YAAY,CAAC,EAAE,YAAY,CAAC;IAC5B,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,SAAS,EAAE,SAAS,CAAC;IACrB,GAAG,EAAE,WAAW,CAAC;IACjB,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC;IACtB,eAAe,EAAE,eAAe,CAAC;IACjC,sBAAsB,CAAC,EAAE,MAAM,CAAC;IAChC,KAAK,CAAC,EAAE,UAAU,CAAC;IACnB,OAAO,CAAC,EAAE,mBAAmB,CAAC;IAC9B,OAAO,CAAC,EAAE,iBAAiB,CAAC;IAC5B,aAAa,CAAC,EAAE,OAAO,CAAC;IAExB,oBAAoB,EAAE,MAAM,iBAAiB,CAAC;IAC9C,4BAA4B,CAAC,EAAE,MAAM,IAAI,CAAC;IAC1C;;;;;;;OAOG;IACH,MAAM,EAAE;QACN,wBAAwB,CAAC,EAAE,MAAM,IAAI,CAAC;QACtC,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE;YACf,IAAI,EAAE,MAAM,CAAC;YACb,KAAK,CAAC,EAAE,OAAO,CAAC;YAChB,aAAa,CAAC,EAAE,OAAO,CAAC;YACxB,SAAS,EAAE,iBAAiB,CAAC;YAC7B,SAAS,CAAC,EAAE,MAAM,IAAI,CAAC;YACvB,oBAAoB,CAAC,EAAE,MAAM,IAAI,CAAC;YAClC,gBAAgB,EAAE,CAAC,EACjB,KAAK,EACL,MAAM,GACP,EAAE;gBACD,KAAK,EAAE,cAAc,CAAC;gBACtB,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;aAClC,KAAK,IAAI,CAAC;SACZ,KAAK;YACJ,OAAO,CAAC,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,KAAK,IAAI,CAAC;YAC9C,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,CAAC;SAChC,CAAC;QACF,GAAG,CAAC,EAAE,CAAC,EACL,KAAK,EACL,YAAY,GACb,EAAE;YACD,KAAK,CAAC,EAAE,OAAO,CAAC;YAChB,YAAY,CAAC,EAAE,MAAM,IAAI,CAAC;SAC3B,KAAK;YACJ,KAAK,CAAC,EAAE,MAAM,IAAI,CAAC;YACnB,cAAc,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,IAAI,CAAC;YAC5C,OAAO,CAAC,EAAE,MAAM,IAAI,CAAC;SACtB,CAAC;KACH,CAAC;CACH;AAED,MAAM,WAAW,qBAAqB;IACpC,SAAS,CAAC,EAAE,mBAAmB,CAAC;IAChC,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,aAAa,EAAE,OAAO,CAAC;IACvB,SAAS,CAAC,EAAE,SAAS,CAAC;IACtB,UAAU,EAAE,OAAO,CAAC;IACpB,YAAY,EAAE,OAAO,CAAC;IACtB,gBAAgB,EAAE,OAAO,CAAC;IAC1B,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,SAAS,EAAE;QAAE,KAAK,EAAE,SAAS,CAAC;QAAC,OAAO,EAAE,YAAY,CAAA;KAAE,EAAE,CAAC;IACzD,4BAA4B,CAAC,EAAE,4BAA4B,CAAC;IAC5D,eAAe,CAAC,EAAE,eAAe,CAAC;IAClC,YAAY,CAAC,EAAE;QACb,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;YAAE,UAAU,CAAC,EAAE,OAAO,CAAA;SAAE,KAAK,IAAI,CAAC;QACnD,cAAc,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,IAAI,CAAC;QAC5C,OAAO,CAAC,EAAE,MAAM,IAAI,CAAC;KACtB,CAAC;IAEF,YAAY,CAAC,EAAE;QACb,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE,OAAO,KAAK,IAAI,CAAC;QAC7C,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,CAAC;KAChC,CAAC;IACF;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB;AAED,qBAAa,gBAAgB;IAC3B,OAAO,CAAC,OAAO,CAAwB;IAEhC,KAAK,EAAE,qBAAqB,CAejC;gBAEU,OAAO,EAAE,qBAAqB;IAwB1C;;;OAGG;IACG,eAAe,CAAC,MAAM,CAAC,EAAE,qBAAqB,GAAG,OAAO,CAAC,IAAI,CAAC;IAI9D,uBAAuB,CAAC,EAC5B,WAAW,GACZ,EAAE;QACD,WAAW,EAAE,WAAW,CAAC;KAC1B,GAAG,OAAO,CAAC,IAAI,CAAC;IAiBjB,eAAe;IAIf,UAAU;IAKV,gBAAgB;IAOhB,gBAAgB,IAAI,aAAa,GAAG,SAAS;IAI7C,UAAU,IAAI,OAAO,GAAG,SAAS;IAIjC,YAAY;IAOZ,kBAAkB,IAAI,eAAe;IAQrC,WAAW;IAIX,YAAY;IAIZ,QAAQ;IAIR,UAAU,CAAC,OAAO,CAAC,EAAE,iBAAiB,GAAG,IAAI;CAa9C"}