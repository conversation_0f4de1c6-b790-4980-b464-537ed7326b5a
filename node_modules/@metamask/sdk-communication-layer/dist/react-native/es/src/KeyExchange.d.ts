import { EventEmitter2 } from 'eventemitter2';
import { ECIESProps } from './ECIES';
import { SocketService } from './SocketService';
import { CommunicationLayerMessage } from './types/CommunicationLayerMessage';
import { KeyExchangeMessageType } from './types/KeyExchangeMessageType';
import { KeyInfo } from './types/KeyInfo';
import { CommunicationLayerLoggingOptions } from './types/LoggingOptions';
export interface KeyExchangeProps {
    communicationLayer: SocketService;
    otherPublicKey?: string;
    sendPublicKey: boolean;
    context: string;
    logging?: CommunicationLayerLoggingOptions;
    ecies?: ECIESProps;
}
export declare class KeyExchange extends EventEmitter2 {
    private keysExchanged;
    private myECIES;
    private otherPublicKey?;
    private communicationLayer;
    private myPublicKey;
    private step;
    private context;
    private debug;
    constructor({ communicationLayer, otherPublicKey, context, ecies, logging, }: KeyExchangeProps);
    onKeyExchangeMessage(keyExchangeMsg: {
        message: CommunicationLayerMessage;
    }): void;
    resetKeys(ecies?: ECIESProps): void;
    clean(): void;
    start({ isOriginator, force, }: {
        isOriginator: boolean;
        force?: boolean;
    }): void;
    setStep(step: KeyExchangeMessageType): void;
    checkStep(stepList: string[]): void;
    setRelayPersistence({ localKey, otherKey, }: {
        localKey: string;
        otherKey: string;
    }): void;
    setKeysExchanged(keysExchanged: boolean): void;
    areKeysExchanged(): boolean;
    getMyPublicKey(): string;
    getOtherPublicKey(): string | undefined;
    setOtherPublicKey(otherPubKey: string): void;
    encryptMessage(message: string): string;
    decryptMessage(message: string): string;
    getKeyInfo(): KeyInfo;
    toString(): string;
}
//# sourceMappingURL=KeyExchange.d.ts.map