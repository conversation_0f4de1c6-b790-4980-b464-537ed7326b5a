{"version": 3, "file": "RemoteCommunication.d.ts", "sourceRoot": "", "sources": ["../../../../src/RemoteCommunication.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AAE9C,OAAO,EAAE,UAAU,EAAE,MAAM,SAAS,CAAC;AACrC,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAmBhD,OAAO,EAAE,kBAAkB,EAAE,MAAM,4BAA4B,CAAC;AAChE,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AACtD,OAAO,EAAE,yBAAyB,EAAE,MAAM,mCAAmC,CAAC;AAC9E,OAAO,EAAE,4BAA4B,EAAE,MAAM,sCAAsC,CAAC;AACpF,OAAO,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAC5D,OAAO,EAAE,sBAAsB,EAAE,MAAM,sBAAsB,CAAC;AAC9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAE9D,OAAO,EAAE,gCAAgC,EAAE,MAAM,wBAAwB,CAAC;AAC1E,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACxD,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AACpD,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AACtD,OAAO,EACL,cAAc,IAAI,qBAAqB,EACvC,mBAAmB,EACpB,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAG1C,KAAK,cAAc,GAAG,iBAAiB,CAAC;AAExC,MAAM,WAAW,wBAAwB;IACvC,YAAY,EAAE,YAAY,GAAG,cAAc,CAAC;IAC5C,4BAA4B,EAAE,4BAA4B,CAAC;IAC3D,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,YAAY,CAAC,EAAE,sBAAsB,CAAC;IACtC,UAAU,CAAC,EAAE,UAAU,CAAC;IACxB,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC;IACtB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,sBAAsB,CAAC,EAAE,MAAM,CAAC;IAChC,KAAK,CAAC,EAAE,UAAU,CAAC;IACnB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,OAAO,CAAC,EAAE,mBAAmB,CAAC;IAC9B,OAAO,EAAE,MAAM,CAAC;IAChB,WAAW,CAAC,EAAE,kBAAkB,CAAC;IACjC,OAAO,CAAC,EAAE,gCAAgC,CAAC;CAC5C;AAED,MAAM,WAAW,wBAAwB;IACvC,KAAK,EAAE,OAAO,CAAC;IACf,UAAU,EAAE,OAAO,CAAC;IACpB,YAAY,EAAE,OAAO,CAAC;IACtB,MAAM,EAAE,OAAO,CAAC;IAChB,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,eAAe,EAAE,MAAM,CAAC;IACxB,yBAAyB,EAAE,OAAO,CAAC;IACnC,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,UAAU,EAAE,OAAO,CAAC;IACpB,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC;IACtB,YAAY,EAAE,YAAY,GAAG,cAAc,CAAC;IAC5C,SAAS,EAAE,OAAO,CAAC;IACnB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,aAAa,CAAC,EAAE,aAAa,CAAC;IAC9B,UAAU,CAAC,EAAE,UAAU,CAAC;IACxB,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,kBAAkB,CAAC,EAAE,aAAa,CAAC;IACnC,cAAc,CAAC,EAAE,cAAc,CAAC;IAChC,kBAAkB,EAAE,OAAO,CAAC;IAC5B,YAAY,EAAE,OAAO,CAAC;IACtB,YAAY,CAAC,EAAE,sBAAsB,CAAC;IACtC,sBAAsB,EAAE,MAAM,CAAC;IAC/B,OAAO,EAAE,MAAM,CAAC;IAChB,cAAc,CAAC,EAAE,qBAAqB,CAAC;IACvC,cAAc,CAAC,EAAE,mBAAmB,CAAC;IACrC,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,kBAAkB,CAAC,EAAE,kBAAkB,CAAC;IACxC,gBAAgB,EAAE,OAAO,CAAC;IAC1B,eAAe,EAAE,MAAM,CAAC;IACxB,wBAAwB,EAAE,OAAO,CAAC;IAClC,KAAK,EAAE,OAAO,CAAC;IACf,OAAO,CAAC,EAAE,gCAAgC,CAAC;IAC3C,iBAAiB,EAAE,gBAAgB,CAAC;CACrC;AACD,qBAAa,mBAAoB,SAAQ,aAAa;IACpD,OAAO,CAAC,QAAQ,CAA2B;IAEpC,KAAK,EAAE,wBAAwB,CA4BpC;gBAEU,OAAO,EAAE,wBAAwB;IA4F7C;;OAEG;IACU,mBAAmB;IAgChC;;OAEG;IACG,wBAAwB,IAAI,OAAO,CAAC,aAAa,GAAG,SAAS,CAAC;IAK9D,wBAAwB,IAAI,OAAO,CAAC,OAAO,CAAC;IAIlD,KAAK;IAIL,gBAAgB,CAAC,EACf,SAAS,EACT,eAAe,EACf,UAAU,GACX,EAAE;QACD,SAAS,EAAE,MAAM,CAAC;QAClB,UAAU,CAAC,EAAE,OAAO,CAAC;QACrB,eAAe,CAAC,EAAE,OAAO,CAAC;KAC3B,GAAG,OAAO,CAAC,IAAI,CAAC;IASjB,WAAW,CAAC,OAAO,EAAE,yBAAyB,GAAG,OAAO,CAAC,OAAO,CAAC;IAI3D,WAAW;IAIjB,mBAAmB;IAInB,gBAAgB;IAIhB;;;;OAIG;IACH,OAAO;IAIP;;;;OAIG;IACH,WAAW;IAIX,YAAY;IAIZ,QAAQ;IAIR,qBAAqB;IAIf,IAAI;IAQV,UAAU;IAOV,QAAQ;IAQR,mBAAmB,CAAC,gBAAgB,EAAE,gBAAgB;IAStD,sBAAsB,CAAC,CAAC,GAAE;QAAE,OAAO,CAAC,EAAE,MAAM,CAAA;KAAO;IAKnD,mBAAmB;IAInB,gBAAgB,IAAI,aAAa;IAUjC,UAAU;IAIV,SAAS;IAIT,iBAAiB,CAAC,cAAc,EAAE,MAAM;IAWlC,KAAK;IASX,UAAU;IAIV,mBAAmB;IAIb,MAAM;IAIZ,OAAO,CAAC,IAAI,EAAE,MAAM;IAYpB,OAAO,CAAC,IAAI,EAAE,MAAM;IAOpB,YAAY;IAIZ,mBAAmB;IAInB,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE;QAAE,SAAS,EAAE,MAAM,CAAA;KAAE;IAOrC,UAAU,CAAC,OAAO,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,OAAO,CAAC;CAMhE"}