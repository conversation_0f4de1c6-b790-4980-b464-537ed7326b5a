"use strict";var e=require("tslib"),t=require("cross-fetch"),n=require("debug"),o=require("buffer"),i=require("eciesjs"),s=require("eventemitter2"),a=require("uuid"),c=require("socket.io-client");function r(e){return e&&e.__esModule?e:{default:e}}var l=r(t),d=r(n);const u=d.default("KeyExchange:Layer"),h=d.default("SocketService:Layer"),y=d.default("Ecies:Layer"),m=d.default("RemoteCommunication:Layer");u.color="##95c44e",h.color="#f638d7",y.color="#465b9c",m.color="#47a2be";const v={KeyExchange:u,SocketService:h,Ecies:y,RemoteCommunication:m};let E,g=[],p=[];const S=(t,n)=>e.__awaiter(void 0,void 0,void 0,(function*(){E=n,p.push(t),function(t){return e.__awaiter(this,void 0,void 0,(function*(){if(!E||!t)return;!function(){const e=p;p=g,g=e}();const e=E.endsWith("/")?`${E}evt`:`${E}/evt`,n=Object.assign({},t);if(delete n.params,t.params)for(const[e,o]of Object.entries(t.params))n[e]=o;const o=JSON.stringify(n);v.RemoteCommunication(`[sendBufferedEvents] Sending ${g.length} analytics events to ${e}`);try{const t=yield l.default(e,{method:"POST",headers:{Accept:"application/json","Content-Type":"application/json"},body:o}),n=yield t.text();v.RemoteCommunication(`[sendBufferedEvents] Response: ${n}`),g.length=0}catch(e){}}))}(t).catch((()=>{}))}));class x{constructor(e){this.enabled=!0,(null==e?void 0:e.debug)&&d.default.enable("Ecies:Layer"),this.ecies=(null==e?void 0:e.privateKey)?i.PrivateKey.fromHex(e.privateKey):new i.PrivateKey,v.Ecies("[ECIES constructor()] initialized secret: ",this.ecies.toHex()),v.Ecies("[ECIES constructor()] initialized public: ",this.ecies.publicKey.toHex()),v.Ecies("[ECIES constructor()] init with",this)}generateECIES(){this.ecies=new i.PrivateKey}getPublicKey(){return this.ecies.publicKey.toHex()}encrypt(e,t){let n=e;if(this.enabled)try{v.Ecies("[ECIES: encrypt()] using otherPublicKey",t);const s=o.Buffer.from(e),a=i.encrypt(t,s);n=o.Buffer.from(a).toString("base64")}catch(n){throw v.Ecies("[ECIES: encrypt()] error encrypt:",n),v.Ecies("[ECIES: encrypt()] private: ",this.ecies.toHex()),v.Ecies("[ECIES: encrypt()] data: ",e),v.Ecies("[ECIES: encrypt()] otherkey: ",t),n}return n}decrypt(e){let t=e;if(this.enabled)try{v.Ecies("[ECIES: decrypt()] using privateKey",this.ecies.toHex());const n=o.Buffer.from(e.toString(),"base64");t=i.decrypt(this.ecies.toHex(),n).toString()}catch(t){throw v.Ecies("[ECIES: decrypt()] error decrypt",t),v.Ecies("[ECIES: decrypt()] private: ",this.ecies.toHex()),v.Ecies("[ECIES: decrypt()] encryptedData: ",e),t}return t}getKeyInfo(){return{private:this.ecies.toHex(),public:this.ecies.publicKey.toHex()}}toString(){v.Ecies("[ECIES: toString()]",this.getKeyInfo())}}var C={name:"@metamask/sdk-communication-layer",version:"0.32.0",description:"",homepage:"https://github.com/MetaMask/metamask-sdk#readme",bugs:{url:"https://github.com/MetaMask/metamask-sdk/issues"},repository:{type:"git",url:"https://github.com/MetaMask/metamask-sdk.git",directory:"packages/sdk-communication-layer"},main:"dist/node/cjs/metamask-sdk-communication-layer.js",unpkg:"dist/browser/umd/metamask-sdk-communication-layer.js",module:"dist/node/es/metamask-sdk-communication-layer.js",browser:"dist/browser/es/metamask-sdk-communication-layer.js","react-native":"dist/react-native/es/metamask-sdk-communication-layer.js",types:"dist/types/src/index.d.ts",files:["/dist"],scripts:{"build:types":"tsc --project tsconfig.build.json --emitDeclarationOnly --outDir dist/types","build:clean":"yarn clean && yarn build",build:"yarn build:types && rollup -c --bundleConfigAsCjs","build:dev":"yarn build:types && NODE_ENV=dev rollup -c --bundleConfigAsCjs",dev:'concurrently "tsc --watch" "rollup -c --bundleConfigAsCjs -w"',"build:post-tsc":"echo 'N/A'","build:pre-tsc":"echo 'N/A'",size:"size-limit",clean:"rimraf ./dist",lint:"yarn lint:eslint && yarn lint:misc --check","lint:changelog":"../../scripts/validate-changelog.sh @metamask/sdk-communication-layer","lint:eslint":"eslint . --cache --ext js,ts","lint:fix":"yarn lint:eslint --fix && yarn lint:misc --write","lint:misc":"prettier '**/*.json' '**/*.md' '!CHANGELOG.md' --ignore-path ../../.gitignore","publish:preview":"yarn npm publish --tag preview",prepack:"../../scripts/prepack.sh",reset:"yarn clean && rimraf ./node_modules/",test:'jest --testPathIgnorePatterns "/e2e/"',"test:e2e":'jest --testPathPattern "/e2e/"',"test:coverage":"jest --coverage","test:ci":'jest --coverage --passWithNoTests --setupFilesAfterEnv ./jest-preload.js --testPathIgnorePatterns "/e2e/"',"test:dev":"jest",watch:"rollup -c --bundleConfigAsCjs -w"},dependencies:{bufferutil:"^4.0.8","date-fns":"^2.29.3",debug:"^4.3.4","utf-8-validate":"^5.0.2",uuid:"^8.3.2"},devDependencies:{"@jest/globals":"^29.3.1","@lavamoat/allow-scripts":"^2.3.1","@metamask/auto-changelog":"3.1.0","@metamask/eslint-config":"^6.0.0","@metamask/eslint-config-nodejs":"^6.0.0","@metamask/eslint-config-typescript":"^6.0.0","@rollup/plugin-commonjs":"^25.0.0","@rollup/plugin-json":"^6.0.0","@rollup/plugin-node-resolve":"^15.0.2","@rollup/plugin-replace":"^6.0.1","@rollup/plugin-terser":"^0.4.4","@size-limit/preset-big-lib":"^11.0.2","@types/jest":"^29.2.4","@types/node":"^20.1.3","@types/uuid":"^9.0.0","@typescript-eslint/eslint-plugin":"^4.26.0","@typescript-eslint/parser":"^4.26.0",concurrently:"^9.1.2","cross-fetch":"^4.0.0",eciesjs:"^0.4.11",eslint:"^7.30.0","eslint-config-prettier":"^8.3.0","eslint-plugin-import":"^2.23.4","eslint-plugin-jest":"^24.4.0","eslint-plugin-jsdoc":"^36.1.0","eslint-plugin-node":"^11.1.0","eslint-plugin-prettier":"^3.4.0",eventemitter2:"^6.4.9",jest:"^29.3.1",prettier:"^2.3.0",rimraf:"^3.0.2",rollup:"^4.26.0","rollup-plugin-jscc":"^2.0.0","rollup-plugin-natives":"^0.7.5","rollup-plugin-node-builtins":"^2.1.2","rollup-plugin-node-globals":"^1.4.0","rollup-plugin-peer-deps-external":"^2.2.4","rollup-plugin-polyfill-node":"^0.13.0","rollup-plugin-sizes":"^1.0.6","rollup-plugin-typescript2":"^0.31.2","rollup-plugin-visualizer":"^5.12.0","size-limit":"^11.1.6","socket.io-client":"^4.5.1","stream-browserify":"^3.0.0","ts-jest":"^29.0.3","ts-node":"^10.9.1",typescript:"^5.6.3"},peerDependencies:{"cross-fetch":"^4.0.0",eciesjs:"*",eventemitter2:"^6.4.9","readable-stream":"^3.6.2","socket.io-client":"^4.5.1"},publishConfig:{access:"public",registry:"https://registry.npmjs.org/"},lavamoat:{allowScripts:{"@lavamoat/preinstall-always-fail":!1,canvas:!0,"eciesjs>secp256k1":!1,"socket.io-client>engine.io-client>ws>bufferutil":!1,"socket.io-client>engine.io-client>ws>utf-8-validate":!1,bufferutil:!1,"utf-8-validate":!1}}};const f="https://metamask-sdk.api.cx.metamask.io/",k=["websocket"],_=6048e5,K=3e3,T={METAMASK_GETPROVIDERSTATE:"metamask_getProviderState",ETH_REQUESTACCOUNTS:"eth_requestAccounts"};function I(e){const{context:t}=e;v.RemoteCommunication(`[RemoteCommunication: clean()] context=${t}`),e.channelConfig=void 0,e.ready=!1,e.originatorConnectStarted=!1}var A,N,O,R,w,b;exports.ConnectionStatus=void 0,(A=exports.ConnectionStatus||(exports.ConnectionStatus={})).DISCONNECTED="disconnected",A.WAITING="waiting",A.TIMEOUT="timeout",A.LINKED="linked",A.PAUSED="paused",A.TERMINATED="terminated",exports.EventType=void 0,(N=exports.EventType||(exports.EventType={})).KEY_INFO="key_info",N.SERVICE_STATUS="service_status",N.PROVIDER_UPDATE="provider_update",N.RPC_UPDATE="rpc_update",N.KEYS_EXCHANGED="keys_exchanged",N.JOIN_CHANNEL="join_channel",N.PUBLIC_KEY="public_key",N.CHANNEL_CREATED="channel_created",N.CLIENTS_CONNECTED="clients_connected",N.CLIENTS_DISCONNECTED="clients_disconnected",N.CLIENTS_WAITING="clients_waiting",N.CLIENTS_READY="clients_ready",N.REJECTED="rejected",N.WALLET_INIT="wallet_init",N.CHANNEL_PERSISTENCE="channel_persistence",N.CONFIG="config",N.MESSAGE_ACK="ack",N.SOCKET_DISCONNECTED="socket_disconnected",N.SOCKET_RECONNECT="socket_reconnect",N.OTP="otp",N.SDK_RPC_CALL="sdk_rpc_call",N.AUTHORIZED="authorized",N.CONNECTION_STATUS="connection_status",N.MESSAGE="message",N.TERMINATE="terminate",function(e){e.KEY_EXCHANGE="key_exchange"}(O||(O={})),exports.KeyExchangeMessageType=void 0,(R=exports.KeyExchangeMessageType||(exports.KeyExchangeMessageType={})).KEY_HANDSHAKE_START="key_handshake_start",R.KEY_HANDSHAKE_CHECK="key_handshake_check",R.KEY_HANDSHAKE_SYN="key_handshake_SYN",R.KEY_HANDSHAKE_SYNACK="key_handshake_SYNACK",R.KEY_HANDSHAKE_ACK="key_handshake_ACK",R.KEY_HANDSHAKE_WALLET="key_handshake_wallet",R.KEY_HANDSHAKE_NONE="none";class P extends s.EventEmitter2{constructor({communicationLayer:e,otherPublicKey:t,context:n,ecies:o,logging:i}){super(),this.keysExchanged=!1,this.step=exports.KeyExchangeMessageType.KEY_HANDSHAKE_NONE,this.debug=!1,this.context=n,this.communicationLayer=e,(null==o?void 0:o.privateKey)&&t&&(v.KeyExchange(`[KeyExchange: constructor()] otherPubKey=${t} set keysExchanged to true!`,o),this.keysExchanged=!0),this.myECIES=new x(Object.assign(Object.assign({},o),{debug:null==i?void 0:i.eciesLayer})),this.communicationLayer.state.eciesInstance=this.myECIES,this.myPublicKey=this.myECIES.getPublicKey(),this.debug=!0===(null==i?void 0:i.keyExchangeLayer),t&&this.setOtherPublicKey(t),this.communicationLayer.on(O.KEY_EXCHANGE,this.onKeyExchangeMessage.bind(this))}onKeyExchangeMessage(e){const{relayPersistence:t}=this.communicationLayer.remote.state;if(v.KeyExchange(`[KeyExchange: onKeyExchangeMessage()] context=${this.context} keysExchanged=${this.keysExchanged} relayPersistence=${t}`,e),t)return void v.KeyExchange("[KeyExchange: onKeyExchangeMessage()] Ignoring key exchange message because relay persistence is activated");const{message:n}=e;this.keysExchanged&&v.KeyExchange(`[KeyExchange: onKeyExchangeMessage()] context=${this.context} received handshake while already exchanged. step=${this.step} otherPubKey=${this.otherPublicKey}`),this.emit(exports.EventType.KEY_INFO,n.type),n.type===exports.KeyExchangeMessageType.KEY_HANDSHAKE_SYN?(this.checkStep([exports.KeyExchangeMessageType.KEY_HANDSHAKE_NONE,exports.KeyExchangeMessageType.KEY_HANDSHAKE_ACK]),v.KeyExchange("[KeyExchange: onKeyExchangeMessage()] KEY_HANDSHAKE_SYN",n),n.pubkey&&this.setOtherPublicKey(n.pubkey),this.communicationLayer.sendMessage({type:exports.KeyExchangeMessageType.KEY_HANDSHAKE_SYNACK,pubkey:this.myPublicKey}).catch((e=>{v.KeyExchange("[KeyExchange: onKeyExchangeMessage()] Error sending KEY_HANDSHAKE_SYNACK",e)})),this.setStep(exports.KeyExchangeMessageType.KEY_HANDSHAKE_ACK)):n.type===exports.KeyExchangeMessageType.KEY_HANDSHAKE_SYNACK?(this.checkStep([exports.KeyExchangeMessageType.KEY_HANDSHAKE_SYNACK,exports.KeyExchangeMessageType.KEY_HANDSHAKE_ACK,exports.KeyExchangeMessageType.KEY_HANDSHAKE_NONE]),v.KeyExchange("[KeyExchange: onKeyExchangeMessage()] KEY_HANDSHAKE_SYNACK"),n.pubkey&&this.setOtherPublicKey(n.pubkey),this.communicationLayer.sendMessage({type:exports.KeyExchangeMessageType.KEY_HANDSHAKE_ACK}).catch((e=>{v.KeyExchange("[KeyExchange: onKeyExchangeMessage()] Error sending KEY_HANDSHAKE_ACK",e)})),this.keysExchanged=!0,this.setStep(exports.KeyExchangeMessageType.KEY_HANDSHAKE_ACK),this.emit(exports.EventType.KEYS_EXCHANGED)):n.type===exports.KeyExchangeMessageType.KEY_HANDSHAKE_ACK&&(v.KeyExchange("[KeyExchange: onKeyExchangeMessage()] KEY_HANDSHAKE_ACK set keysExchanged to true!"),this.checkStep([exports.KeyExchangeMessageType.KEY_HANDSHAKE_ACK,exports.KeyExchangeMessageType.KEY_HANDSHAKE_NONE]),this.keysExchanged=!0,this.setStep(exports.KeyExchangeMessageType.KEY_HANDSHAKE_ACK),this.emit(exports.EventType.KEYS_EXCHANGED))}resetKeys(e){this.clean(),this.myECIES=new x(e)}clean(){v.KeyExchange(`[KeyExchange: clean()] context=${this.context} reset handshake state`),this.setStep(exports.KeyExchangeMessageType.KEY_HANDSHAKE_NONE),this.emit(exports.EventType.KEY_INFO,this.step),this.keysExchanged=!1}start({isOriginator:e,force:t}){const{relayPersistence:n,protocolVersion:o}=this.communicationLayer.remote.state,i=o>=2;n?v.KeyExchange("[KeyExchange: start()] Ignoring key exchange message because relay persistence is activated"):(v.KeyExchange(`[KeyExchange: start()] context=${this.context} protocolVersion=${o} isOriginator=${e} step=${this.step} force=${t} relayPersistence=${n} keysExchanged=${this.keysExchanged}`),e?!(this.keysExchanged||this.step!==exports.KeyExchangeMessageType.KEY_HANDSHAKE_NONE&&this.step!==exports.KeyExchangeMessageType.KEY_HANDSHAKE_SYNACK)||t?(v.KeyExchange(`[KeyExchange: start()] context=${this.context} -- start key exchange (force=${t}) -- step=${this.step}`,this.step),this.clean(),this.setStep(exports.KeyExchangeMessageType.KEY_HANDSHAKE_SYNACK),this.communicationLayer.sendMessage({type:exports.KeyExchangeMessageType.KEY_HANDSHAKE_SYN,pubkey:this.myPublicKey,v:2}).catch((e=>{v.KeyExchange("[KeyExchange: start()] Error sending KEY_HANDSHAKE_SYN",e)}))):v.KeyExchange(`[KeyExchange: start()] context=${this.context} -- key exchange already ${this.keysExchanged?"done":"in progress"} -- aborted.`,this.step):this.keysExchanged&&!0!==t?v.KeyExchange("[KeyExchange: start()] don't send KEY_HANDSHAKE_START -- exchange already done."):i?this.communicationLayer.sendMessage({type:exports.KeyExchangeMessageType.KEY_HANDSHAKE_SYNACK,pubkey:this.myPublicKey,v:2}).catch((e=>{v.KeyExchange("[KeyExchange: start()] Error sending KEY_HANDSHAKE_SYNACK",e)})):(this.communicationLayer.sendMessage({type:exports.KeyExchangeMessageType.KEY_HANDSHAKE_START}).catch((e=>{v.KeyExchange("[KeyExchange: start()] Error sending KEY_HANDSHAKE_START",e)})),this.clean()))}setStep(e){this.step=e,this.emit(exports.EventType.KEY_INFO,e)}checkStep(e){e.length>0&&e.indexOf(this.step.toString())}setRelayPersistence({localKey:e,otherKey:t}){this.otherPublicKey=t,this.myECIES=new x({privateKey:e,debug:this.debug}),this.keysExchanged=!0}setKeysExchanged(e){this.keysExchanged=e}areKeysExchanged(){return this.keysExchanged}getMyPublicKey(){return this.myPublicKey}getOtherPublicKey(){return this.otherPublicKey}setOtherPublicKey(e){v.KeyExchange("[KeyExchange: setOtherPubKey()]",e),this.otherPublicKey=e}encryptMessage(e){if(!this.otherPublicKey)throw new Error("encryptMessage: Keys not exchanged - missing otherPubKey");return this.myECIES.encrypt(e,this.otherPublicKey)}decryptMessage(e){if(!this.otherPublicKey)throw new Error("decryptMessage: Keys not exchanged - missing otherPubKey");return this.myECIES.decrypt(e)}getKeyInfo(){return{ecies:Object.assign(Object.assign({},this.myECIES.getKeyInfo()),{otherPubKey:this.otherPublicKey}),step:this.step,keysExchanged:this.areKeysExchanged()}}toString(){const e={keyInfo:this.getKeyInfo(),keysExchanged:this.keysExchanged,step:this.step};return JSON.stringify(e)}}exports.MessageType=void 0,(w=exports.MessageType||(exports.MessageType={})).TERMINATE="terminate",w.ANSWER="answer",w.OFFER="offer",w.CANDIDATE="candidate",w.JSONRPC="jsonrpc",w.WALLET_INFO="wallet_info",w.WALLET_INIT="wallet_init",w.ORIGINATOR_INFO="originator_info",w.PAUSE="pause",w.OTP="otp",w.AUTHORIZED="authorized",w.PING="ping",w.READY="ready",exports.TrackingEvents=void 0,(b=exports.TrackingEvents||(exports.TrackingEvents={})).REQUEST="sdk_connect_request_started",b.REQUEST_MOBILE="sdk_connect_request_started_mobile",b.RECONNECT="sdk_reconnect_request_started",b.CONNECTED="sdk_connection_established",b.CONNECTED_MOBILE="sdk_connection_established_mobile",b.AUTHORIZED="sdk_connection_authorized",b.REJECTED="sdk_connection_rejected",b.TERMINATED="sdk_connection_terminated",b.DISCONNECTED="sdk_disconnected",b.SDK_USE_EXTENSION="sdk_use_extension",b.SDK_RPC_REQUEST="sdk_rpc_request",b.SDK_RPC_REQUEST_RECEIVED="sdk_rpc_request_received",b.SDK_RPC_REQUEST_DONE="sdk_rpc_request_done",b.SDK_EXTENSION_UTILIZED="sdk_extension_utilized",b.SDK_USE_INAPP_BROWSER="sdk_use_inapp_browser";const D=(t,n,o)=>e.__awaiter(void 0,void 0,void 0,(function*(){var e,i,s,a,c,r;const{remote:l,state:d}=t,{channelId:u,isOriginator:h}=d;if("error_terminated"===n)return v.SocketService(`handleJoinChannelResults: Channel ${u} terminated`),void t.emit(exports.EventType.TERMINATE);if(!o)return void v.SocketService(`handleJoinChannelResults: No result for channel ${u}`);const{persistence:y,walletKey:m,rejected:E}=o;if(v.SocketService(`handleJoinChannelResults: Channel ${u} persistence=${y} walletKey=${m} rejected=${E}`),E)return v.SocketService(`handleJoinChannelResults: Channel ${u} rejected`),yield t.remote.disconnect({terminate:!0}),t.remote.emit(exports.EventType.REJECTED,{channelId:u}),void t.remote.emitServiceStatusEvent();if(m&&!(null===(e=l.state.channelConfig)||void 0===e?void 0:e.otherKey)){t.getKeyExchange().setOtherPublicKey(m),null===(i=t.state.keyExchange)||void 0===i||i.setKeysExchanged(!0),l.state.ready=!0,l.state.authorized=!0,l.emit(exports.EventType.AUTHORIZED);const{communicationLayer:e,storageManager:n}=l.state,o=Object.assign(Object.assign({},l.state.channelConfig),{channelId:null!==(s=l.state.channelId)&&void 0!==s?s:"",validUntil:Date.now()+_,localKey:null==e?void 0:e.getKeyInfo().ecies.private,otherKey:m});t.sendMessage({type:exports.KeyExchangeMessageType.KEY_HANDSHAKE_ACK}).catch((e=>{})),null===(a=t.state.socket)||void 0===a||a.emit(exports.MessageType.PING,{id:u,clientType:h?"dapp":"wallet",context:"on_channel_reconnect",message:""}),yield null==n?void 0:n.persistChannelConfig(o),l.emitServiceStatusEvent(),l.setConnectionStatus(exports.ConnectionStatus.LINKED)}y&&(t.emit(exports.EventType.CHANNEL_PERSISTENCE),null===(c=t.state.keyExchange)||void 0===c||c.setKeysExchanged(!0),l.state.ready=!0,l.state.authorized=!0,l.emit(exports.EventType.AUTHORIZED),S(Object.assign(Object.assign({id:null!=u?u:"",event:h?exports.TrackingEvents.CONNECTED:exports.TrackingEvents.CONNECTED_MOBILE},t.remote.state.originatorInfo),{sdkVersion:t.remote.state.sdkVersion,commLayer:t.state.communicationLayerPreference,commLayerVersion:C.version,walletVersion:null===(r=t.remote.state.walletInfo)||void 0===r?void 0:r.version}),d.communicationServerUrl).catch((e=>{})))})),M=e=>new Promise((t=>{setTimeout(t,e)})),L=(t,n,...o)=>e.__awaiter(void 0,[t,n,...o],void 0,(function*(e,t,n=200){let o;const i=Date.now();let s=!1;for(;!s;){if(s=Date.now()-i>3e5,o=t[e],void 0!==o.elapsedTime)return o;yield M(n)}throw new Error(`RPC ${e} timed out`)})),$=t=>e.__awaiter(void 0,void 0,void 0,(function*(){const{state:n}=t,{socket:o,channelId:i,context:s,isOriginator:a,isReconnecting:c}=n;if(c)return v.SocketService("[SocketService: reconnectSocket()] Reconnection already in progress, skipping",t),!1;if(!o)return v.SocketService("[SocketService: reconnectSocket()] socket is not defined",t),!1;if(!i)return!1;const{connected:r}=o;n.isReconnecting=!0,n.reconnectionAttempts=0,v.SocketService(`[SocketService: reconnectSocket()] connected=${r} trying to reconnect after socketio disconnection`,t);try{for(;3>n.reconnectionAttempts;){if(v.SocketService(`[SocketService: reconnectSocket()] Attempt ${n.reconnectionAttempts+1} of 3`,t),yield M(200),o.connected)return v.SocketService("Socket already connected --- ping to retrieve messages"),o.emit(exports.MessageType.PING,{id:i,clientType:a?"dapp":"wallet",context:"on_channel_config",message:""}),!0;n.resumed=!0,o.connect(),t.emit(exports.EventType.SOCKET_RECONNECT);try{if(yield new Promise(((n,c)=>{o.emit(exports.EventType.JOIN_CHANNEL,{channelId:i,context:`${s}connect_again`,clientType:a?"dapp":"wallet"},((o,i)=>e.__awaiter(void 0,void 0,void 0,(function*(){try{yield D(t,o,i),n()}catch(e){c(e)}}))))})),yield M(100),o.connected)return v.SocketService(`Reconnection successful on attempt ${n.reconnectionAttempts+1}`),!0}catch(e){v.SocketService(`Error during reconnection attempt ${n.reconnectionAttempts+1}:`,e)}n.reconnectionAttempts+=1,3>n.reconnectionAttempts&&(yield M(200))}return v.SocketService("Failed to reconnect after 3 attempts"),!1}finally{n.isReconnecting=!1,n.reconnectionAttempts=0}}));function H(t,n){return e.__awaiter(this,void 0,void 0,(function*(){var e;const o=null===(e=t.state.keyExchange)||void 0===e?void 0:e.encryptMessage(JSON.stringify(n)),i={id:t.state.channelId,context:t.state.context,clientType:t.state.isOriginator?"dapp":"wallet",message:o,plaintext:t.state.hasPlaintext?JSON.stringify(n):void 0};return v.SocketService(`[SocketService: encryptAndSendMessage()] context=${t.state.context}`,i),n.type===exports.MessageType.TERMINATE&&(t.state.manualDisconnect=!0),new Promise(((e,n)=>{var o;null===(o=t.state.socket)||void 0===o||o.emit(exports.EventType.MESSAGE,i,((t,o)=>{var i;t&&(v.SocketService(`[SocketService: encryptAndSendMessage()] error=${t}`),n(t)),v.SocketService("[encryptAndSendMessage] response",o),e(null!==(i=null==o?void 0:o.success)&&void 0!==i&&i)}))}))}))}var j;!function(e){e.RPC_CHECK="rpcCheck",e.SKIPPED_RPC="skippedRpc"}(j||(j={}));const U=["eth_sendTransaction","eth_signTypedData","eth_signTransaction","personal_sign","wallet_requestPermissions","wallet_switchEthereumChain","eth_signTypedData_v3","eth_signTypedData_v4","metamask_connectSign","metamask_connectWith","metamask_batch"].map((e=>e.toLowerCase()));function Y(t,n){return e.__awaiter(this,void 0,void 0,(function*(){var o,i,s;if(!t.state.channelId)throw v.SocketService("handleSendMessage: no channelId - Create a channel first"),new Error("Create a channel first");if(v.SocketService(`[SocketService: handleSendMessage()] context=${t.state.context} areKeysExchanged=${null===(o=t.state.keyExchange)||void 0===o?void 0:o.areKeysExchanged()}`,n),null===(i=null==n?void 0:n.type)||void 0===i?void 0:i.startsWith("key_handshake"))return function(e,t){var n;v.SocketService(`[SocketService: handleKeyHandshake()] context=${e.state.context}`,t),null===(n=e.state.socket)||void 0===n||n.emit(exports.EventType.MESSAGE,{id:e.state.channelId,context:e.state.context,clientType:e.state.isOriginator?"dapp":"wallet",message:t})}(t,n),!0;!function(e,t){var n;if(!(null===(n=e.state.keyExchange)||void 0===n?void 0:n.areKeysExchanged())&&!e.remote.state.relayPersistence)throw v.SocketService(`[SocketService: validateKeyExchange()] context=${e.state.context} ERROR keys not exchanged`,t),new Error("Keys not exchanged BBB")}(t,n),function(e,t){var n;const o=null!==(n=null==t?void 0:t.method)&&void 0!==n?n:"",i=null==t?void 0:t.id;e.state.isOriginator&&i&&(e.state.rpcMethodTracker[i]={id:i,timestamp:Date.now(),method:o},e.emit(exports.EventType.RPC_UPDATE,e.state.rpcMethodTracker[i]))}(t,n);const a=yield H(t,n);return t.remote.state.analytics&&t.remote.state.isOriginator&&n.method&&U.includes(n.method.toLowerCase())&&S({id:null!==(s=t.remote.state.channelId)&&void 0!==s?s:"",event:exports.TrackingEvents.SDK_RPC_REQUEST,params:{method:n.method,from:"mobile"}},t.remote.state.communicationServerUrl).catch((e=>{})),function(t,n){return e.__awaiter(this,void 0,void 0,(function*(){var o;const i=null==n?void 0:n.id,s=null!==(o=null==n?void 0:n.method)&&void 0!==o?o:"";if(t.state.isOriginator&&i)try{const o=L(i,t.state.rpcMethodTracker,200).then((e=>({type:j.RPC_CHECK,result:e}))),a=(()=>e.__awaiter(this,void 0,void 0,(function*(){const n=yield(t=>e.__awaiter(void 0,[t],void 0,(function*({rpcId:e,instance:t}){for(;t.state.lastRpcId===e||void 0===t.state.lastRpcId;)yield M(200);return t.state.lastRpcId})))({instance:t,rpcId:i}),o=yield L(n,t.state.rpcMethodTracker,200);return{type:j.SKIPPED_RPC,result:o}})))(),c=yield Promise.race([o,a]);if(c.type===j.RPC_CHECK){const e=c.result;v.SocketService(`[SocketService:handleRpcReplies()] id=${n.id} ${s} ( ${e.elapsedTime} ms)`,e.result)}else{if(c.type!==j.SKIPPED_RPC)throw new Error(`Error handling RPC replies for ${i}`);{const e=Object.assign(Object.assign({},t.state.rpcMethodTracker[i]),{error:new Error("SDK_CONNECTION_ISSUE")});t.emit(exports.EventType.RPC_UPDATE,e);const n={data:Object.assign(Object.assign({},e),{jsonrpc:"2.0"}),name:"metamask-provider"};t.emit(exports.EventType.MESSAGE,{message:n})}}}catch(e){throw e}}))}(t,n).catch((e=>{})),a}))}const z=[{event:exports.EventType.CLIENTS_CONNECTED,handler:function(t,n){return o=>e.__awaiter(this,void 0,void 0,(function*(){var e,o,i,s,a,c,r,l,d,u,h;const y=null!==(o=null===(e=t.remote.state.channelConfig)||void 0===e?void 0:e.relayPersistence)&&void 0!==o&&o;if(v.SocketService(`[SocketService: handleClientsConnected()] context=${t.state.context} on 'clients_connected-${n}' relayPersistence=${y} resumed=${t.state.resumed}  clientsPaused=${t.state.clientsPaused} keysExchanged=${null===(i=t.state.keyExchange)||void 0===i?void 0:i.areKeysExchanged()} isOriginator=${t.state.isOriginator}`),t.emit(exports.EventType.CLIENTS_CONNECTED,{isOriginator:t.state.isOriginator,keysExchanged:null===(s=t.state.keyExchange)||void 0===s?void 0:s.areKeysExchanged(),context:t.state.context}),t.state.resumed)t.state.isOriginator||(v.SocketService(`[SocketService: handleClientsConnected()] context=${t.state.context} 'clients_connected' / keysExchanged=${null===(a=t.state.keyExchange)||void 0===a?void 0:a.areKeysExchanged()} -- backward compatibility`),null===(c=t.state.keyExchange)||void 0===c||c.start({isOriginator:null!==(r=t.state.isOriginator)&&void 0!==r&&r})),t.state.resumed=!1;else if(t.state.clientsPaused)v.SocketService("[SocketService: handleClientsConnected()] 'clients_connected' skip sending originatorInfo on pause");else if(!t.state.isOriginator){const e=!y;v.SocketService(`[SocketService: handleClientsConnected()] context=${t.state.context} on 'clients_connected' / keysExchanged=${null===(l=t.state.keyExchange)||void 0===l?void 0:l.areKeysExchanged()} -- force=${e} -- backward compatibility`),v.SocketService(`[SocketService: handleClientsConnected()] context=${t.state.context} on 'clients_connected' / keysExchanged=${null===(d=t.state.keyExchange)||void 0===d?void 0:d.areKeysExchanged()} -- force=${e} -- backward compatibility`),null===(u=t.state.keyExchange)||void 0===u||u.start({isOriginator:null!==(h=t.state.isOriginator)&&void 0!==h&&h,force:e})}t.state.clientsConnected=!0,t.state.clientsPaused=!1}))}},{event:exports.EventType.CHANNEL_CREATED,handler:function(e,t){return n=>{v.SocketService(`[SocketService: handleChannelCreated()] context=${e.state.context} on 'channel_created-${t}'`,n),e.emit(exports.EventType.CHANNEL_CREATED,n)}}},{event:exports.EventType.CLIENTS_DISCONNECTED,handler:function(e,t){return()=>{var n;e.state.clientsConnected=!1,v.SocketService(`[SocketService: handlesClientsDisconnected()] context=${e.state.context} on 'clients_disconnected-${t}'`),e.remote.state.relayPersistence?v.SocketService(`[SocketService: handlesClientsDisconnected()] context=${e.state.context} on 'clients_disconnected-${t}' - relayPersistence enabled, skipping key exchange cleanup.`):(e.state.isOriginator&&!e.state.clientsPaused&&(null===(n=e.state.keyExchange)||void 0===n||n.clean()),e.emit(exports.EventType.CLIENTS_DISCONNECTED,t))}}},{event:exports.EventType.CONFIG,handler:function(t,n){return o=>e.__awaiter(this,void 0,void 0,(function*(){var e,i,s;v.SocketService(`[SocketService: handleChannelConfig()] update relayPersistence on 'config-${n}'`,o);const{persistence:a,walletKey:c}=o;t.state.isOriginator&&t.remote.state.channelConfig?(o.walletKey&&!t.remote.state.channelConfig.otherKey&&(v.SocketService(`Setting wallet key ${c}`),t.remote.state.channelConfig.otherKey=c,t.getKeyExchange().setOtherPublicKey(o.walletKey),null===(e=t.state.keyExchange)||void 0===e||e.setKeysExchanged(!0),yield t.remote.sendMessage({type:exports.KeyExchangeMessageType.KEY_HANDSHAKE_ACK}),yield t.remote.sendMessage({type:exports.MessageType.PING}),yield null===(i=t.remote.state.storageManager)||void 0===i?void 0:i.persistChannelConfig(t.remote.state.channelConfig)),!0!==a||t.remote.state.channelConfig.relayPersistence||(v.SocketService(`Setting relay persistence ${a}`),t.remote.state.channelConfig.relayPersistence=a,t.remote.state.relayPersistence=!0,t.remote.emit(exports.EventType.CHANNEL_PERSISTENCE),t.remote.state.authorized=!0,t.remote.state.ready=!0,t.remote.emit(exports.EventType.AUTHORIZED),yield null===(s=t.remote.state.storageManager)||void 0===s?void 0:s.persistChannelConfig(t.remote.state.channelConfig))):t.state.isOriginator||o.persistence&&(t.remote.state.relayPersistence=!0,t.remote.emit(exports.EventType.CHANNEL_PERSISTENCE))}))}},{event:exports.EventType.MESSAGE,handler:function(e,t){return n=>{var o,i,s,a,c,r,l,d,u,h,y,m,E,g,p,x,f,k;const{ackId:_,message:K,error:T}=n,I=null!==(o=e.remote.state.relayPersistence)&&void 0!==o&&o;if(v.SocketService(`[SocketService handleMessage()]  relayPersistence=${I}  context=${e.state.context} on 'message' ${t} keysExchanged=${null===(i=e.state.keyExchange)||void 0===i?void 0:i.areKeysExchanged()}`,n),T)throw v.SocketService(`\n      [SocketService handleMessage()] context=${e.state.context}::on 'message' error=${T}`),new Error(T);const A="string"==typeof K;if(!A&&(null==K?void 0:K.type)===exports.KeyExchangeMessageType.KEY_HANDSHAKE_START){if(I)return;return v.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' received HANDSHAKE_START isOriginator=${e.state.isOriginator}`,K),void(null===(s=e.state.keyExchange)||void 0===s||s.start({isOriginator:null!==(a=e.state.isOriginator)&&void 0!==a&&a,force:!0}))}if(!A&&(null===(c=null==K?void 0:K.type)||void 0===c?void 0:c.startsWith("key_handshake"))){if(I)return;return v.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' emit KEY_EXCHANGE`,K),void e.emit(O.KEY_EXCHANGE,{message:K,context:e.state.context})}if(A&&!(null===(r=e.state.keyExchange)||void 0===r?void 0:r.areKeysExchanged())){let t=!1;try{v.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' trying to decrypt message`),null===(l=e.state.keyExchange)||void 0===l||l.decryptMessage(K),t=!0}catch(t){v.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' error`,t)}if(!t)return e.state.isOriginator?null===(u=e.state.keyExchange)||void 0===u||u.start({isOriginator:null!==(h=e.state.isOriginator)&&void 0!==h&&h}):e.sendMessage({type:exports.KeyExchangeMessageType.KEY_HANDSHAKE_START}).catch((e=>{})),void v.SocketService(`Message ignored because invalid key exchange status. step=${null===(y=e.state.keyExchange)||void 0===y?void 0:y.getKeyInfo().step}`,null===(m=e.state.keyExchange)||void 0===m?void 0:m.getKeyInfo(),K);v.SocketService("Invalid key exchange status detected --- updating it."),null===(d=e.state.keyExchange)||void 0===d||d.setKeysExchanged(!0)}else if(!A&&(null==K?void 0:K.type))return void e.emit(exports.EventType.MESSAGE,K);if(!A)return void e.emit(exports.EventType.MESSAGE,K);const N=null===(E=e.state.keyExchange)||void 0===E?void 0:E.decryptMessage(K),R=JSON.parse(null!=N?N:"{}");if(_&&(null==_?void 0:_.length)>0&&(v.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' ackid=${_} channelId=${t}`),null===(g=e.state.socket)||void 0===g||g.emit(exports.EventType.MESSAGE_ACK,{ackId:_,channelId:t,clientType:e.state.isOriginator?"dapp":"wallet"})),e.state.clientsPaused=(null==R?void 0:R.type)===exports.MessageType.PAUSE,e.state.isOriginator&&R.data){const t=R.data,n=e.state.rpcMethodTracker[t.id];if(n){const o=Date.now()-n.timestamp;v.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' received answer for id=${t.id} method=${n.method} responseTime=${o}`,R),e.remote.state.analytics&&U.includes(n.method.toLowerCase())&&S(Object.assign(Object.assign({id:null!==(p=e.remote.state.channelId)&&void 0!==p?p:"",event:exports.TrackingEvents.SDK_RPC_REQUEST_DONE,sdkVersion:e.remote.state.sdkVersion,commLayerVersion:C.version},e.remote.state.originatorInfo),{walletVersion:null===(x=e.remote.state.walletInfo)||void 0===x?void 0:x.version,params:{method:n.method,from:"mobile"}}),e.remote.state.communicationServerUrl).catch((e=>{}));const i=Object.assign(Object.assign({},n),{result:t.result,error:t.error?{code:null===(f=t.error)||void 0===f?void 0:f.code,message:null===(k=t.error)||void 0===k?void 0:k.message}:void 0,elapsedTime:o});e.state.rpcMethodTracker[t.id]=i,e.emit(exports.EventType.RPC_UPDATE,i)}}e.emit(exports.EventType.MESSAGE,{message:R})}}},{event:exports.EventType.REJECTED,handler:function(t,n){return o=>e.__awaiter(this,void 0,void 0,(function*(){var e;t.state.isOriginator&&!t.remote.state.ready?(v.SocketService(`[SocketService: handleChannelRejected()] context=${t.state.context} channelId=${n} isOriginator=${t.state.isOriginator} ready=${t.remote.state.ready}`,t.remote.state.originatorInfo),S(Object.assign(Object.assign({id:n,event:exports.TrackingEvents.REJECTED},t.remote.state.originatorInfo),{sdkVersion:t.remote.state.sdkVersion,commLayer:t.state.communicationLayerPreference,commLayerVersion:C.version,walletVersion:null===(e=t.remote.state.walletInfo)||void 0===e?void 0:e.version}),t.remote.state.communicationServerUrl).catch((e=>{})),yield t.remote.disconnect({terminate:!0}),t.remote.emit(exports.EventType.REJECTED,{channelId:n}),t.remote.setConnectionStatus(exports.ConnectionStatus.DISCONNECTED)):v.SocketService(`[SocketService: handleChannelRejected()] SKIP -- channelId=${n} isOriginator=${t.state.isOriginator} ready=${t.remote.state.ready}`)}))}},{event:"clients_waiting_to_join",handler:function(e,t){return n=>{v.SocketService(`[SocketService: handleClientsWaitingToJoin()] context=${e.state.context} on 'clients_waiting_to_join-${t}'`,n),e.emit(exports.EventType.CLIENTS_WAITING,n)}}}],V=[{event:exports.EventType.KEY_INFO,handler:function(e){return t=>{v.SocketService("[SocketService: handleKeyInfo()] on 'KEY_INFO'",t),e.emit(exports.EventType.KEY_INFO,t)}}},{event:exports.EventType.KEYS_EXCHANGED,handler:function(e){return()=>{var t,n,o;v.SocketService(`[SocketService: handleKeysExchanged()] on 'keys_exchanged' keyschanged=${null===(t=e.state.keyExchange)||void 0===t?void 0:t.areKeysExchanged()}`);const{channelConfig:i}=e.remote.state;if(i){const t=e.getKeyExchange().getKeyInfo().ecies;i.localKey=t.private,i.otherKey=t.otherPubKey,e.remote.state.channelConfig=i,null===(n=e.remote.state.storageManager)||void 0===n||n.persistChannelConfig(i).catch((e=>{}))}e.emit(exports.EventType.KEYS_EXCHANGED,{keysExchanged:null===(o=e.state.keyExchange)||void 0===o?void 0:o.areKeysExchanged(),isOriginator:e.state.isOriginator});const s={keyInfo:e.getKeyInfo()};e.emit(exports.EventType.SERVICE_STATUS,s)}}}];function G(e,t){v.SocketService(`[SocketService: setupChannelListener()] context=${e.state.context} setting socket listeners for channel ${t}...`);const{socket:n}=e.state,{keyExchange:o}=e.state;n&&e.state.isOriginator&&(e.state.debug&&(null==n||n.io.on("error",(t=>{v.SocketService(`[SocketService: setupChannelListener()] context=${e.state.context} socket event=error`,t)})),null==n||n.io.on("reconnect",(t=>{v.SocketService(`[SocketService: setupChannelListener()] context=${e.state.context} socket event=reconnect`,t),$(e).catch((e=>{}))})),null==n||n.io.on("reconnect_error",(t=>{v.SocketService(`[SocketService: setupChannelListener()] context=${e.state.context} socket event=reconnect_error`,t)})),null==n||n.io.on("reconnect_failed",(()=>{v.SocketService(`[SocketService: setupChannelListener()] context=${e.state.context} socket event=reconnect_failed`)}))),null==n||n.on("disconnect",(t=>(v.SocketService(`[SocketService: setupChannelListener()] on 'disconnect' -- MetaMaskSDK socket disconnected '${t}' begin recovery...`),function(e){return t=>{v.SocketService(`[SocketService: handleDisconnect()] on 'disconnect' manualDisconnect=${e.state.manualDisconnect}`,t),e.state.manualDisconnect||(e.emit(exports.EventType.SOCKET_DISCONNECTED),$(e).catch((e=>{})))}}(e)(t))))),z.forEach((({event:o,handler:i})=>{null==n||n.on(`${o}-${t}`,i(e,t))})),V.forEach((({event:t,handler:n})=>{null==o||o.on(t,n(e))})),e.state.setupChannelListeners=!0}class F extends s.EventEmitter2{constructor(e){super(),this.state={clientsConnected:!1,clientsPaused:!1,manualDisconnect:!1,lastRpcId:void 0,rpcMethodTracker:{},hasPlaintext:!1,communicationServerUrl:"",focusListenerAdded:!1,removeFocusListener:void 0,isReconnecting:!1,reconnectionAttempts:0},this.options=e;const{reconnect:t,communicationLayerPreference:n,communicationServerUrl:o,context:i,remote:s,logging:a}=e;this.state.resumed=t,this.state.context=i,this.state.isOriginator=s.state.isOriginator,this.state.communicationLayerPreference=n,this.state.debug=!0===(null==a?void 0:a.serviceLayer),this.remote=s,!0===(null==a?void 0:a.serviceLayer)&&d.default.enable("SocketService:Layer"),this.state.communicationServerUrl=o,this.state.hasPlaintext=this.state.communicationServerUrl!==f&&!0===(null==a?void 0:a.plaintext),v.SocketService(`[SocketService: constructor()] Socket IO url: ${this.state.communicationServerUrl}`),this.initSocket()}initSocket(){var e;const{otherPublicKey:t,ecies:n,logging:o}=this.options,i={autoConnect:!1,transports:k,withCredentials:!0},s=this.state.communicationServerUrl;v.SocketService(`[SocketService: initSocket()] Socket IO url: ${s}`),this.state.socket=c.io(s,i),function(e){if("undefined"!=typeof window&&"undefined"!=typeof document&&(v.SocketService(`[SocketService: setupSocketFocusListener()] hasFocus=${document.hasFocus()}`,e),!e.state.focusListenerAdded)){const t=()=>{v.SocketService("Document has focus --- reconnecting socket"),$(e).catch((e=>{}))};window.addEventListener("focus",t),e.state.focusListenerAdded=!0,e.state.removeFocusListener=()=>{window.removeEventListener("focus",t),e.state.focusListenerAdded=!1}}}(this);const a={communicationLayer:this,otherPublicKey:t,sendPublicKey:!1,context:null!==(e=this.state.context)&&void 0!==e?e:"",ecies:n,logging:o};this.state.keyExchange=new P(a)}resetKeys(){return this,v.SocketService("[SocketService: resetKeys()] Resetting keys."),void(null===(e=this.state.keyExchange)||void 0===e||e.resetKeys());var e}createChannel(){return e.__awaiter(this,void 0,void 0,(function*(){return function(t){return e.__awaiter(this,void 0,void 0,(function*(){var n,o,i;if(v.SocketService(`[SocketService: createChannel()] context=${t.state.context}`),t.state.socket||t.initSocket(),null===(n=t.state.socket)||void 0===n?void 0:n.connected)throw new Error("socket already connected");null===(o=t.state.socket)||void 0===o||o.connect(),t.state.manualDisconnect=!1,t.state.isOriginator=!0;const s=a.v4();t.state.channelId=s,G(t,s),yield new Promise(((n,o)=>{var i;null===(i=t.state.socket)||void 0===i||i.emit(exports.EventType.JOIN_CHANNEL,{channelId:s,context:`${t.state.context}createChannel`,clientType:"dapp"},((i,s)=>e.__awaiter(this,void 0,void 0,(function*(){try{yield D(t,i,s),n()}catch(e){o(e)}}))))}));const c=null===(i=t.state.keyExchange)||void 0===i?void 0:i.getKeyInfo();return{channelId:s,pubKey:(null==c?void 0:c.ecies.public)||"",privKey:(null==c?void 0:c.ecies.private)||""}}))}(this)}))}connectToChannel({channelId:t,withKeyExchange:n=!1,authorized:o}){return function(t){return e.__awaiter(this,arguments,void 0,(function*({options:t,instance:n}){const{channelId:o,authorized:i,withKeyExchange:s}=t,{state:a,remote:c}=n,{isOriginator:r=!1,socket:l,keyExchange:d}=a,{channelConfig:u}=c.state;if(null==l?void 0:l.connected)throw new Error("socket already connected");if(r&&(null==u?void 0:u.relayPersistence)){const{localKey:e,otherKey:t}=u;e&&t&&(null==d||d.setRelayPersistence({localKey:e,otherKey:t}))}return Object.assign(a,{manualDisconnect:!1,withKeyExchange:s,isOriginator:r,channelId:o}),null==l||l.connect(),G(n,o),!r&&i&&(null==d||d.setKeysExchanged(!0),Object.assign(c.state,{ready:!0,authorized:!0})),new Promise((t=>{var s;const c=null===(s=null==d?void 0:d.getKeyInfo())||void 0===s?void 0:s.ecies.public;null==l||l.emit(exports.EventType.JOIN_CHANNEL,{channelId:o,context:`${a.context}_connectToChannel`,clientType:r?"dapp":"wallet",publicKey:i&&!r?c:void 0},((o,i)=>e.__awaiter(this,void 0,void 0,(function*(){yield D(n,o,i),t()}))))}))}))}({options:{channelId:t,withKeyExchange:n,authorized:o},instance:this})}getKeyInfo(){return this.state.keyExchange.getKeyInfo()}keyCheck(){var e,t;null===(t=(e=this).state.socket)||void 0===t||t.emit(exports.EventType.MESSAGE,{id:e.state.channelId,context:e.state.context,message:{type:exports.KeyExchangeMessageType.KEY_HANDSHAKE_CHECK,pubkey:e.getKeyInfo().ecies.otherPubKey}})}getKeyExchange(){return this.state.keyExchange}sendMessage(t){return e.__awaiter(this,void 0,void 0,(function*(){return Y(this,t)}))}ping(){return function(t){return e.__awaiter(this,void 0,void 0,(function*(){var e,n;v.SocketService(`[SocketService: ping()] context=${t.state.context} originator=${t.state.isOriginator} keysExchanged=${null===(e=t.state.keyExchange)||void 0===e?void 0:e.areKeysExchanged()}`),null===(n=t.state.socket)||void 0===n||n.emit(exports.MessageType.PING,{id:t.state.channelId,context:"ping",clientType:t.remote.state.isOriginator?"dapp":"wallet",message:""})}))}(this)}pause(){return function(t){return e.__awaiter(this,void 0,void 0,(function*(){var e,n;v.SocketService(`[SocketService: pause()] context=${t.state.context}`),t.state.manualDisconnect=!0,(null===(e=t.state.keyExchange)||void 0===e?void 0:e.areKeysExchanged())&&(yield t.sendMessage({type:exports.MessageType.PAUSE})),null===(n=t.state.socket)||void 0===n||n.disconnect()}))}(this)}isConnected(){var e;return null===(e=this.state.socket)||void 0===e?void 0:e.connected}resume(){return function(t){return e.__awaiter(this,void 0,void 0,(function*(){const{state:n,remote:o}=t,{socket:i,channelId:s,context:a,keyExchange:c,isOriginator:r}=n,{isOriginator:l}=o.state;if(v.SocketService(`[SocketService: resume()] channelId=${s} context=${a} connected=${null==i?void 0:i.connected} manualDisconnect=${n.manualDisconnect} resumed=${n.resumed} keysExchanged=${null==c?void 0:c.areKeysExchanged()}`),!s)throw v.SocketService("[SocketService: resume()] channelId is not defined"),new Error("ChannelId is not defined");(null==i?void 0:i.connected)?(v.SocketService("[SocketService: resume()] already connected."),i.emit(exports.MessageType.PING,{id:s,clientType:l?"dapp":"wallet",context:"on_channel_config",message:""}),o.hasRelayPersistence()||(null==c?void 0:c.areKeysExchanged())||(r?yield t.sendMessage({type:exports.MessageType.READY}):null==c||c.start({isOriginator:!1}))):(null==i||i.connect(),v.SocketService(`[SocketService: resume()] after connecting socket --\x3e connected=${null==i?void 0:i.connected}`),null==i||i.emit(exports.EventType.JOIN_CHANNEL,{channelId:s,context:`${a}_resume`,clientType:l?"dapp":"wallet"},((n,o)=>e.__awaiter(this,void 0,void 0,(function*(){try{yield D(t,n,o)}catch(e){}}))))),n.manualDisconnect=!1,n.resumed=!0}))}(this)}getRPCMethodTracker(){return this.state.rpcMethodTracker}disconnect(e){return function(e,t){var n,o,i,s,a;v.SocketService(`[SocketService: disconnect()] context=${e.state.context}`,t),(null==t?void 0:t.terminate)&&(null===(o=(n=e.state).removeFocusListener)||void 0===o||o.call(n),e.state.channelId=t.channelId,null===(i=e.state.socket)||void 0===i||i.removeAllListeners(),null===(s=e.state.keyExchange)||void 0===s||s.clean(),e.remote.state.ready=!1,e.state.socket=void 0,e.state.rpcMethodTracker={}),e.state.manualDisconnect=!0,null===(a=e.state.socket)||void 0===a||a.disconnect()}(this,e)}}var W,J;function B(t){return()=>e.__awaiter(this,void 0,void 0,(function*(){var n,o,i;const{state:s}=t;if(s.authorized)return;yield(()=>e.__awaiter(this,void 0,void 0,(function*(){for(;!s.walletInfo;)yield M(500)})))();const a="7.3".localeCompare((null===(n=s.walletInfo)||void 0===n?void 0:n.version)||"");if(v.RemoteCommunication(`[RemoteCommunication: handleAuthorizedEvent()] HACK 'authorized' version=${null===(o=s.walletInfo)||void 0===o?void 0:o.version} compareValue=${a}`),1!==a)return;const c=s.platformType===exports.PlatformType.MobileWeb||s.platformType===exports.PlatformType.ReactNative||s.platformType===exports.PlatformType.MetaMaskMobileWebview;v.RemoteCommunication(`[RemoteCommunication: handleAuthorizedEvent()] HACK 'authorized' platform=${s.platformType} secure=${c} channel=${s.channelId} walletVersion=${null===(i=s.walletInfo)||void 0===i?void 0:i.version}`),c&&(s.authorized=!0,t.emit(exports.EventType.AUTHORIZED))}))}function q(e){return t=>{const{state:n}=e;v.RemoteCommunication(`[RemoteCommunication: handleChannelCreatedEvent()] context=${n.context} on 'channel_created' channelId=${t}`),e.emit(exports.EventType.CHANNEL_CREATED,t)}}function Z(e,t){return()=>{var n,o,i,s;const{state:a}=e;if(v.RemoteCommunication(`[RemoteCommunication: handleClientsConnectedEvent()] on 'clients_connected' channel=${a.channelId} keysExchanged=${null===(o=null===(n=a.communicationLayer)||void 0===n?void 0:n.getKeyInfo())||void 0===o?void 0:o.keysExchanged}`),a.analytics){const e=a.isOriginator?exports.TrackingEvents.REQUEST:exports.TrackingEvents.REQUEST_MOBILE;S(Object.assign(Object.assign({id:null!==(i=a.channelId)&&void 0!==i?i:"",event:a.reconnection?exports.TrackingEvents.RECONNECT:e},a.originatorInfo),{commLayer:t,sdkVersion:a.sdkVersion,walletVersion:null===(s=a.walletInfo)||void 0===s?void 0:s.version,commLayerVersion:C.version}),a.communicationServerUrl).catch((e=>{}))}a.clientsConnected=!0,a.originatorInfoSent=!1,e.emit(exports.EventType.CLIENTS_CONNECTED)}}function X(e){return t=>{const{state:n}=e;v.RemoteCommunication(`[RemoteCommunication: handleClientsDisconnectedEvent()] context=${n.context} on 'clients_disconnected' channelId=${t}`),n.relayPersistence||(n.clientsConnected=!1,n.ready=!1,n.authorized=!1),e.emit(exports.EventType.CLIENTS_DISCONNECTED,n.channelId),e.setConnectionStatus(exports.ConnectionStatus.DISCONNECTED)}}function Q(e){return t=>{var n;const{state:o}=e;if(v.RemoteCommunication(`[RemoteCommunication: handleClientsWaitingEvent()] context=${o.context} on 'clients_waiting' numberUsers=${t} ready=${o.ready} autoStarted=${o.originatorConnectStarted}`),e.setConnectionStatus(exports.ConnectionStatus.WAITING),e.emit(exports.EventType.CLIENTS_WAITING,t),o.originatorConnectStarted){v.RemoteCommunication(`[RemoteCommunication: handleClientsWaitingEvent()] on 'clients_waiting' watch autoStarted=${o.originatorConnectStarted} timeout`,o.autoConnectOptions);const t=(null===(n=o.autoConnectOptions)||void 0===n?void 0:n.timeout)||3e3,i=setTimeout((()=>{v.RemoteCommunication(`[RemoteCommunication: handleClientsWaitingEvent()] setTimeout(${t}) terminate channelConfig`,o.autoConnectOptions),o.originatorConnectStarted=!1,o.ready||e.setConnectionStatus(exports.ConnectionStatus.TIMEOUT),clearTimeout(i)}),t)}}}function ee(e,t){return n=>{var o,i,s,a,c,r,l,d;const{state:u}=e;if(v.RemoteCommunication(`[RemoteCommunication: handleKeysExchangedEvent()] context=${u.context} on commLayer.'keys_exchanged' channel=${u.channelId}`,n),null===(i=null===(o=u.communicationLayer)||void 0===o?void 0:o.getKeyInfo())||void 0===i?void 0:i.keysExchanged){const t=Object.assign(Object.assign({},u.channelConfig),{channelId:null!==(s=u.channelId)&&void 0!==s?s:"",validUntil:(null===(a=u.channelConfig)||void 0===a?void 0:a.validUntil)||_,localKey:u.communicationLayer.getKeyInfo().ecies.private,otherKey:u.communicationLayer.getKeyInfo().ecies.otherPubKey});null===(c=u.storageManager)||void 0===c||c.persistChannelConfig(t).catch((e=>{})),e.setConnectionStatus(exports.ConnectionStatus.LINKED)}!function(e,t){var n,o,i,s,a,c,r,l;const{state:d}=e;v.RemoteCommunication(`[RemoteCommunication: setLastActiveDate()] channel=${d.channelId}`,t);const u=Object.assign(Object.assign({},d.channelConfig),{channelId:null!==(n=d.channelId)&&void 0!==n?n:"",validUntil:null!==(i=null===(o=d.channelConfig)||void 0===o?void 0:o.validUntil)&&void 0!==i?i:0,relayPersistence:d.relayPersistence,localKey:null===(a=null===(s=d.communicationLayer)||void 0===s?void 0:s.state.keyExchange)||void 0===a?void 0:a.getKeyInfo().ecies.private,otherKey:null===(r=null===(c=d.communicationLayer)||void 0===c?void 0:c.state.keyExchange)||void 0===r?void 0:r.getKeyInfo().ecies.otherPubKey,lastActive:t.getTime()});null===(l=d.storageManager)||void 0===l||l.persistChannelConfig(u)}(e,new Date),u.analytics&&u.channelId&&S(Object.assign(Object.assign({id:u.channelId,event:n.isOriginator?exports.TrackingEvents.CONNECTED:exports.TrackingEvents.CONNECTED_MOBILE},u.originatorInfo),{sdkVersion:u.sdkVersion,commLayer:t,commLayerVersion:C.version,walletVersion:null===(r=u.walletInfo)||void 0===r?void 0:r.version}),u.communicationServerUrl).catch((e=>{})),u.isOriginator=n.isOriginator,n.isOriginator||(null===(l=u.communicationLayer)||void 0===l||l.sendMessage({type:exports.MessageType.READY}),u.ready=!0,u.paused=!1),n.isOriginator&&!u.originatorInfoSent&&(null===(d=u.communicationLayer)||void 0===d||d.sendMessage({type:exports.MessageType.ORIGINATOR_INFO,originatorInfo:u.originatorInfo,originator:u.originatorInfo}),u.originatorInfoSent=!0)}}function te(t){return n=>{let o=n;n.message&&(o=o.message),function(t,n){const{state:o}=n;if(v.RemoteCommunication(`[RemoteCommunication: onCommunicationLayerMessage()] context=${o.context} on 'message' typeof=${typeof t}`,t),n.state.ready=!0,o.isOriginator||t.type!==exports.MessageType.ORIGINATOR_INFO)if(o.isOriginator&&t.type===exports.MessageType.WALLET_INFO)!function(e,t){const{state:n}=e;n.walletInfo=t.walletInfo,n.paused=!1}(n,t);else{if(o.isOriginator&&t.type===exports.MessageType.WALLET_INIT)(function(t,n){return e.__awaiter(this,void 0,void 0,(function*(){var e,o,i;const{state:s}=t;if(s.isOriginator){const s=n.data||{};if("object"==typeof s&&"accounts"in s&&"chainId"in s&&"walletKey"in s)try{const{channelConfig:n}=t.state;if(v.RemoteCommunication("WALLET_INIT: channelConfig",JSON.stringify(n,null,2)),n){const a=s.accounts,c=s.chainId,r=s.walletKey;let l,d=!1;"deeplinkProtocol"in s&&(d=Boolean(s.deeplinkProtocol),t.state.deeplinkProtocolAvailable=d),"walletVersion"in s&&(l=s.walletVersion),yield null===(e=t.state.storageManager)||void 0===e?void 0:e.persistChannelConfig(Object.assign(Object.assign({},n),{otherKey:r,walletVersion:l,deeplinkProtocolAvailable:d,relayPersistence:!0})),yield null===(o=t.state.storageManager)||void 0===o?void 0:o.persistAccounts(a),yield null===(i=t.state.storageManager)||void 0===i?void 0:i.persistChainId(c)}t.emit(exports.EventType.WALLET_INIT,{accounts:s.accounts,chainId:s.chainId})}catch(e){}}}))})(n,t).catch((e=>{v.RemoteCommunication(`[RemoteCommunication: onCommunicationLayerMessage()] error=${e}`)}));else if(t.type===exports.MessageType.TERMINATE)(function(t){return e.__awaiter(this,void 0,void 0,(function*(){const{state:e}=t;e.isOriginator&&(yield ae({options:{terminate:!0,sendMessage:!1},instance:t}),t.emit(exports.EventType.TERMINATE))}))})(n).catch((e=>{v.RemoteCommunication(`[RemoteCommunication: onCommunicationLayerMessage()] error=${e}`)}));else if(t.type===exports.MessageType.PAUSE)!function(e){const{state:t}=e;t.paused=!0,e.setConnectionStatus(exports.ConnectionStatus.PAUSED)}(n);else if(t.type===exports.MessageType.READY&&o.isOriginator)!function(e){const{state:t}=e;e.setConnectionStatus(exports.ConnectionStatus.LINKED);const n=t.paused;t.paused=!1,e.emit(exports.EventType.CLIENTS_READY,{isOriginator:t.isOriginator,walletInfo:t.walletInfo}),n&&(t.authorized=!0,e.emit(exports.EventType.AUTHORIZED))}(n);else{if(t.type===exports.MessageType.OTP&&o.isOriginator)return void function(e,t){var n;const{state:o}=e;e.emit(exports.EventType.OTP,t.otpAnswer),1==="6.6".localeCompare((null===(n=o.walletInfo)||void 0===n?void 0:n.version)||"")&&e.emit(exports.EventType.SDK_RPC_CALL,{method:T.ETH_REQUESTACCOUNTS,params:[]})}(n,t);t.type===exports.MessageType.AUTHORIZED&&o.isOriginator&&function(e){const{state:t}=e;t.authorized=!0,e.emit(exports.EventType.AUTHORIZED)}(n)}n.emit(exports.EventType.MESSAGE,t)}else!function(e,t){var n;const{state:o}=e;null===(n=o.communicationLayer)||void 0===n||n.sendMessage({type:exports.MessageType.WALLET_INFO,walletInfo:o.walletInfo}),o.originatorInfo=t.originatorInfo||t.originator,e.emit(exports.EventType.CLIENTS_READY,{isOriginator:o.isOriginator,originatorInfo:o.originatorInfo}),o.paused=!1}(n,t)}(o,t)}}function ne(e){return()=>{const{state:t}=e;v.RemoteCommunication("[RemoteCommunication: handleSocketReconnectEvent()] on 'socket_reconnect' -- reset key exchange status / set ready to false"),t.ready=!1,t.authorized=!1,I(t),e.emitServiceStatusEvent({context:"socket_reconnect"})}}function oe(e){return()=>{const{state:t}=e;v.RemoteCommunication("[RemoteCommunication: handleSocketDisconnectedEvent()] on 'socket_Disconnected' set ready to false"),t.ready=!1}}function ie(t){return()=>e.__awaiter(this,void 0,void 0,(function*(){var e,n,o,i,s,a,c;const{state:r}=t;v.RemoteCommunication(`[RemoteCommunication: handleFullPersistenceEvent()] context=${r.context}`),t.state.ready=!0,t.state.clientsConnected=!0,t.state.authorized=!0,t.state.relayPersistence=!0,null===(e=t.state.communicationLayer)||void 0===e||e.getKeyExchange().setKeysExchanged(!0),t.emit(exports.EventType.KEYS_EXCHANGED,{keysExchanged:!0,isOriginator:!0}),t.emit(exports.EventType.AUTHORIZED),t.emit(exports.EventType.CLIENTS_READY),t.emit(exports.EventType.CHANNEL_PERSISTENCE);try{r.channelConfig=Object.assign(Object.assign({},r.channelConfig),{localKey:null===(n=r.communicationLayer)||void 0===n?void 0:n.getKeyExchange().getKeyInfo().ecies.private,otherKey:null===(o=r.communicationLayer)||void 0===o?void 0:o.getKeyExchange().getOtherPublicKey(),channelId:null!==(i=r.channelId)&&void 0!==i?i:"",validUntil:null!==(a=null===(s=r.channelConfig)||void 0===s?void 0:s.validUntil)&&void 0!==a?a:_,relayPersistence:!0}),yield null===(c=r.storageManager)||void 0===c?void 0:c.persistChannelConfig(r.channelConfig)}catch(e){}}))}function se({communicationLayerPreference:e,otherPublicKey:t,reconnect:n,ecies:o,communicationServerUrl:i=f,instance:s}){var a,c,r,l,d,u,h,y,m,E,g;const{state:p}=s;if(v.RemoteCommunication("[initCommunicationLayer()] ",JSON.stringify(p,null,2)),e!==exports.CommunicationLayerPreference.SOCKET)throw new Error("Invalid communication protocol");p.communicationLayer=new F({communicationLayerPreference:e,otherPublicKey:t,reconnect:n,transports:p.transports,communicationServerUrl:i,context:p.context,ecies:o,logging:p.logging,remote:s});let S="undefined"!=typeof document&&document.URL||"",x="undefined"!=typeof document&&document.title||"";(null===(a=p.dappMetadata)||void 0===a?void 0:a.url)&&(S=p.dappMetadata.url),(null===(c=p.dappMetadata)||void 0===c?void 0:c.name)&&(x=p.dappMetadata.name);const k=null!==(u=null!==(l=null===(r=p.dappMetadata)||void 0===r?void 0:r.name)&&void 0!==l?l:null===(d=p.dappMetadata)||void 0===d?void 0:d.url)&&void 0!==u?u:"N/A",_="undefined"!=typeof window&&void 0!==window.location&&null!==(h=window.location.hostname)&&void 0!==h?h:k,K={url:S,title:x,source:null===(y=p.dappMetadata)||void 0===y?void 0:y.source,dappId:_,icon:(null===(m=p.dappMetadata)||void 0===m?void 0:m.iconUrl)||(null===(E=p.dappMetadata)||void 0===E?void 0:E.base64Icon),platform:p.platformType,apiVersion:C.version,connector:null===(g=p.dappMetadata)||void 0===g?void 0:g.connector};p.originatorInfo=K;const T={[exports.EventType.AUTHORIZED]:B(s),[exports.EventType.MESSAGE]:te(s),[exports.EventType.CHANNEL_PERSISTENCE]:ie(s),[exports.EventType.CLIENTS_CONNECTED]:Z(s,e),[exports.EventType.KEYS_EXCHANGED]:ee(s,e),[exports.EventType.SOCKET_DISCONNECTED]:oe(s),[exports.EventType.SOCKET_RECONNECT]:ne(s),[exports.EventType.CLIENTS_DISCONNECTED]:X(s),[exports.EventType.KEY_INFO]:()=>{},[exports.EventType.CHANNEL_CREATED]:q(s),[exports.EventType.CLIENTS_WAITING]:Q(s),[exports.EventType.RPC_UPDATE]:e=>{s.emit(exports.EventType.RPC_UPDATE,e)}};for(const[e,t]of Object.entries(T))try{p.communicationLayer.on(e,t)}catch(e){}}function ae(t){return e.__awaiter(this,arguments,void 0,(function*({options:e,instance:t}){const{state:n}=t;return v.RemoteCommunication(`[RemoteCommunication: disconnect()] channel=${n.channelId}`,e),new Promise(((o,i)=>{var s,c,r,l,d,u;(null==e?void 0:e.terminate)?(t.state.ready&&S({id:null!==(s=t.state.channelId)&&void 0!==s?s:"",event:exports.TrackingEvents.TERMINATED},t.state.communicationServerUrl).catch((e=>{})),n.ready=!1,n.paused=!1,null===(c=n.storageManager)||void 0===c||c.terminate(null!==(r=n.channelId)&&void 0!==r?r:""),t.state.terminated=!0,e.sendMessage?(null===(l=n.communicationLayer)||void 0===l?void 0:l.getKeyInfo().keysExchanged)&&t.state.communicationLayer&&H(t.state.communicationLayer,{type:exports.MessageType.TERMINATE}).then((()=>{o(!0)})).catch((e=>{i(e)})):o(!0),n.authorized=!1,n.relayPersistence=!1,n.channelId=a.v4(),e.channelId=n.channelId,n.channelConfig=void 0,n.originatorConnectStarted=!1,null===(d=n.communicationLayer)||void 0===d||d.disconnect(e),t.setConnectionStatus(exports.ConnectionStatus.TERMINATED)):(null===(u=n.communicationLayer)||void 0===u||u.disconnect(e),t.setConnectionStatus(exports.ConnectionStatus.DISCONNECTED),o(!0))}))}))}exports.CommunicationLayerPreference=void 0,(exports.CommunicationLayerPreference||(exports.CommunicationLayerPreference={})).SOCKET="socket",exports.PlatformType=void 0,(W=exports.PlatformType||(exports.PlatformType={})).NonBrowser="nodejs",W.MetaMaskMobileWebview="in-app-browser",W.DesktopWeb="web-desktop",W.MobileWeb="web-mobile",W.ReactNative="react-native",exports.AutoConnectType=void 0,(J=exports.AutoConnectType||(exports.AutoConnectType={})).RENEW="renew",J.LINK="link",exports.DEFAULT_SERVER_URL=f,exports.DEFAULT_SESSION_TIMEOUT_MS=_,exports.ECIES=x,exports.RemoteCommunication=class extends s.EventEmitter2{constructor(e){super(),this.state={ready:!1,authorized:!1,isOriginator:!1,terminated:!1,protocolVersion:1,paused:!1,deeplinkProtocolAvailable:!1,platformType:"metamask-mobile",analytics:!1,reconnection:!1,originatorInfoSent:!1,communicationServerUrl:f,context:"",persist:!1,clientsConnected:!1,sessionDuration:_,originatorConnectStarted:!1,debug:!1,_connectionStatus:exports.ConnectionStatus.DISCONNECTED},this._options=e;const{platformType:t,communicationLayerPreference:n,otherPublicKey:o,reconnect:i,walletInfo:s,dappMetadata:a,protocolVersion:c,transports:r,context:l,relayPersistence:u,ecies:h,analytics:y=!1,storage:m,sdkVersion:E,communicationServerUrl:g=f,logging:p,autoConnect:S={timeout:K}}=e;this.state.otherPublicKey=o,this.state.dappMetadata=a,this.state.walletInfo=s,this.state.transports=r,this.state.platformType=t,this.state.analytics=y,this.state.protocolVersion=null!=c?c:1,this.state.isOriginator=!o,this.state.relayPersistence=u,this.state.communicationServerUrl=g,this.state.context=l,this.state.terminated=!1,this.state.sdkVersion=E,this.setMaxListeners(50),this.setConnectionStatus(exports.ConnectionStatus.DISCONNECTED),(null==m?void 0:m.duration)&&(this.state.sessionDuration=_),this.state.storageOptions=m,this.state.autoConnectOptions=S,this.state.debug=!0===(null==p?void 0:p.remoteLayer),!0===(null==p?void 0:p.remoteLayer)&&d.default.enable("RemoteCommunication:Layer"),!0===(null==p?void 0:p.serviceLayer)&&d.default.enable("SocketService:Layer"),!0===(null==p?void 0:p.eciesLayer)&&d.default.enable("ECIES:Layer"),!0===(null==p?void 0:p.keyExchangeLayer)&&d.default.enable("KeyExchange:Layer"),this.state.logging=p,(null==m?void 0:m.storageManager)&&(this.state.storageManager=m.storageManager),v.RemoteCommunication(`[RemoteCommunication: constructor()] protocolVersion=${c} relayPersistence=${u} isOriginator=${this.state.isOriginator} communicationLayerPreference=${n} otherPublicKey=${o} reconnect=${i}`),this.state.isOriginator||se({communicationLayerPreference:n,otherPublicKey:o,reconnect:i,ecies:h,communicationServerUrl:g,instance:this}),this.emitServiceStatusEvent({context:"constructor"})}initFromDappStorage(){return e.__awaiter(this,void 0,void 0,(function*(){var e;if(this.state.storageManager){const t=yield this.state.storageManager.getPersistedChannelConfig({});t&&(this.state.channelConfig=t,this.state.channelId=t.channelId,this.state.deeplinkProtocolAvailable=null!==(e=t.deeplinkProtocolAvailable)&&void 0!==e&&e,t.relayPersistence&&(this.state.authorized=!0,this.state.ready=!0,this.setConnectionStatus(exports.ConnectionStatus.LINKED),yield this.connectToChannel({channelId:t.channelId})))}se({communicationLayerPreference:exports.CommunicationLayerPreference.SOCKET,otherPublicKey:this.state.otherPublicKey,reconnect:this._options.reconnect,ecies:this._options.ecies,communicationServerUrl:this.state.communicationServerUrl,instance:this})}))}originatorSessionConnect(){return e.__awaiter(this,void 0,void 0,(function*(){return yield function(t){return e.__awaiter(this,void 0,void 0,(function*(){var e;const{state:n}=t;if(!n.storageManager)return void v.RemoteCommunication("[RemoteCommunication: originatorSessionConnect()] no storage manager defined - skip");const o=yield n.storageManager.getPersistedChannelConfig({});if(v.RemoteCommunication(`[RemoteCommunication: originatorSessionConnect()] autoStarted=${n.originatorConnectStarted} channelConfig`,o),null===(e=n.communicationLayer)||void 0===e?void 0:e.isConnected())return v.RemoteCommunication("[RemoteCommunication: originatorSessionConnect()] socket already connected - skip"),o;if(o){if(o.validUntil>Date.now())return n.channelConfig=o,n.originatorConnectStarted=!0,n.channelId=null==o?void 0:o.channelId,n.reconnection=!0,o;v.RemoteCommunication("[RemoteCommunication: autoConnect()] Session has expired")}n.originatorConnectStarted=!1}))}(this)}))}generateChannelIdConnect(){return e.__awaiter(this,void 0,void 0,(function*(){return function(t){return e.__awaiter(this,void 0,void 0,(function*(){var e,n,o,i,s,a;if(!t.communicationLayer)throw new Error("communication layer not initialized");if(t.ready)throw new Error("Channel already connected");if(t.channelId&&(null===(e=t.communicationLayer)||void 0===e?void 0:e.isConnected()))return t.channelConfig=Object.assign(Object.assign({},t.channelConfig),{channelId:t.channelId,validUntil:Date.now()+t.sessionDuration}),null===(n=t.storageManager)||void 0===n||n.persistChannelConfig(t.channelConfig),{channelId:t.channelId,privKey:null===(i=null===(o=t.communicationLayer)||void 0===o?void 0:o.getKeyInfo())||void 0===i?void 0:i.ecies.private,pubKey:null===(a=null===(s=t.communicationLayer)||void 0===s?void 0:s.getKeyInfo())||void 0===a?void 0:a.ecies.public};v.RemoteCommunication("[RemoteCommunication: generateChannelId()]");const c=yield t.communicationLayer.createChannel();v.RemoteCommunication("[RemoteCommunication: generateChannelId()] channel created",c);const r=Object.assign(Object.assign({},t.channelConfig),{channelId:c.channelId,localKey:c.privKey,validUntil:Date.now()+t.sessionDuration});return t.channelId=c.channelId,t.channelConfig=r,{channelId:t.channelId,pubKey:c.pubKey,privKey:c.privKey}}))}(this.state)}))}clean(){return I(this.state)}connectToChannel({channelId:t,withKeyExchange:n,authorized:o}){return function(t){return e.__awaiter(this,arguments,void 0,(function*({channelId:e,withKeyExchange:t,authorized:n,state:o}){var i,s,c;if(!a.validate(e))throw v.RemoteCommunication(`[RemoteCommunication: connectToChannel()] context=${o.context} invalid channel channelId=${e}`),new Error(`Invalid channel ${e}`);if(v.RemoteCommunication(`[RemoteCommunication: connectToChannel()] context=${o.context} channelId=${e} withKeyExchange=${t}`),null===(i=o.communicationLayer)||void 0===i?void 0:i.isConnected())return void v.RemoteCommunication(`[RemoteCommunication: connectToChannel()] context=${o.context} already connected - interrupt connection.`);o.channelId=e,yield null===(s=o.communicationLayer)||void 0===s?void 0:s.connectToChannel({channelId:e,authorized:n,withKeyExchange:t});const r=Object.assign(Object.assign({},o.channelConfig),{channelId:e,validUntil:Date.now()+o.sessionDuration});o.channelConfig=r,null===(c=o.storageManager)||void 0===c||c.persistChannelConfig(r)}))}({channelId:t,authorized:o,withKeyExchange:n,state:this.state})}sendMessage(t){return function(t,n){return e.__awaiter(this,void 0,void 0,(function*(){var o,i;const{state:s}=t;v.RemoteCommunication(`[RemoteCommunication: sendMessage()] context=${s.context} paused=${s.paused} ready=${s.ready} relayPersistence=${s.relayPersistence} authorized=${s.authorized} socket=${null===(o=s.communicationLayer)||void 0===o?void 0:o.isConnected()} clientsConnected=${s.clientsConnected} status=${s._connectionStatus}`,n),s.relayPersistence||s.ready&&(null===(i=s.communicationLayer)||void 0===i?void 0:i.isConnected())&&s.clientsConnected||(v.RemoteCommunication(`[RemoteCommunication: sendMessage()] context=${s.context}  SKIP message waiting for MM mobile readiness.`),yield new Promise((e=>{t.once(exports.EventType.CLIENTS_READY,e)})),v.RemoteCommunication(`[RemoteCommunication: sendMessage()] context=${s.context}  AFTER SKIP / READY -- sending pending message`));try{const o=yield function(t,n){return e.__awaiter(this,void 0,void 0,(function*(){return new Promise((e=>{var o;const{state:i}=t;v.RemoteCommunication(`[RemoteCommunication: handleAuthorization()] context=${i.context} ready=${i.ready} authorized=${i.authorized} method=${n.method}`),!i.isOriginator||i.authorized||i.relayPersistence?null===(o=i.communicationLayer)||void 0===o||o.sendMessage(n).then((t=>{e(t)})).catch((t=>{e(!1)})):t.once(exports.EventType.AUTHORIZED,(()=>{var t;v.RemoteCommunication(`[RemoteCommunication: handleAuthorization()] context=${i.context}  AFTER SKIP / AUTHORIZED -- sending pending message`),null===(t=i.communicationLayer)||void 0===t||t.sendMessage(n).then((t=>{e(t)})).catch((t=>{e(!1)}))}))}))}))}(t,n);return o}catch(e){throw e}}))}(this,t)}testStorage(){return e.__awaiter(this,void 0,void 0,(function*(){return function(t){return e.__awaiter(this,void 0,void 0,(function*(){var e;const n=yield null===(e=t.storageManager)||void 0===e?void 0:e.getPersistedChannelConfig();v.RemoteCommunication("[RemoteCommunication: testStorage()] res",n)}))}(this.state)}))}hasDeeplinkProtocol(){return this.state.deeplinkProtocolAvailable}getChannelConfig(){return this.state.channelConfig}isReady(){return this.state.ready}isConnected(){var e;return null===(e=this.state.communicationLayer)||void 0===e?void 0:e.isConnected()}isAuthorized(){return this.state.authorized}isPaused(){return this.state.paused}getCommunicationLayer(){return this.state.communicationLayer}ping(){return e.__awaiter(this,void 0,void 0,(function*(){var e;v.RemoteCommunication(`[RemoteCommunication: ping()] channel=${this.state.channelId}`),yield null===(e=this.state.communicationLayer)||void 0===e?void 0:e.ping()}))}testLogger(){v.RemoteCommunication(`testLogger() channel=${this.state.channelId}`),v.SocketService(`testLogger() channel=${this.state.channelId}`),v.Ecies(`testLogger() channel=${this.state.channelId}`),v.KeyExchange(`testLogger() channel=${this.state.channelId}`)}keyCheck(){var e;v.RemoteCommunication(`[RemoteCommunication: keyCheck()] channel=${this.state.channelId}`),null===(e=this.state.communicationLayer)||void 0===e||e.keyCheck()}setConnectionStatus(e){this.state._connectionStatus!==e&&(this.state._connectionStatus=e,this.emit(exports.EventType.CONNECTION_STATUS,e),this.emitServiceStatusEvent({context:"setConnectionStatus"}))}emitServiceStatusEvent(e={}){this.emit(exports.EventType.SERVICE_STATUS,this.getServiceStatus())}getConnectionStatus(){return this.state._connectionStatus}getServiceStatus(){return{originatorInfo:this.state.originatorInfo,keyInfo:this.getKeyInfo(),connectionStatus:this.state._connectionStatus,channelConfig:this.state.channelConfig,channelId:this.state.channelId}}getKeyInfo(){var e;return null===(e=this.state.communicationLayer)||void 0===e?void 0:e.getKeyInfo()}resetKeys(){var e;null===(e=this.state.communicationLayer)||void 0===e||e.resetKeys()}setOtherPublicKey(e){var t;const n=null===(t=this.state.communicationLayer)||void 0===t?void 0:t.getKeyExchange();if(!n)throw new Error("KeyExchange is not initialized.");n.getOtherPublicKey()!==e&&n.setOtherPublicKey(e)}pause(){return e.__awaiter(this,void 0,void 0,(function*(){var e;v.RemoteCommunication(`[RemoteCommunication: pause()] channel=${this.state.channelId}`),yield null===(e=this.state.communicationLayer)||void 0===e?void 0:e.pause(),this.setConnectionStatus(exports.ConnectionStatus.PAUSED)}))}getVersion(){return C.version}hasRelayPersistence(){var e;return null!==(e=this.state.relayPersistence)&&void 0!==e&&e}resume(){return e.__awaiter(this,void 0,void 0,(function*(){return function(t){return e.__awaiter(this,void 0,void 0,(function*(){var e;const{state:n}=t;v.RemoteCommunication(`[RemoteCommunication: resume()] channel=${n.channelId}`),yield null===(e=n.communicationLayer)||void 0===e?void 0:e.resume(),t.setConnectionStatus(exports.ConnectionStatus.LINKED)}))}(this)}))}encrypt(e){var t,n,o;const i=null===(t=this.state.communicationLayer)||void 0===t?void 0:t.getKeyExchange(),s=null==i?void 0:i.getOtherPublicKey();if(!s)throw new Error("KeyExchange not completed");return null===(o=null===(n=this.state.communicationLayer)||void 0===n?void 0:n.state.eciesInstance)||void 0===o?void 0:o.encrypt(e,s)}decrypt(e){var t,n,o;if(!(null===(t=this.state.communicationLayer)||void 0===t?void 0:t.state.eciesInstance))throw new Error("ECIES instance is not initialized");return null===(o=null===(n=this.state.communicationLayer)||void 0===n?void 0:n.state.eciesInstance)||void 0===o?void 0:o.decrypt(e)}getChannelId(){return this.state.channelId}getRPCMethodTracker(){var e;return null===(e=this.state.communicationLayer)||void 0===e?void 0:e.getRPCMethodTracker()}reject({channelId:t}){return function(t){return e.__awaiter(this,arguments,void 0,(function*({channelId:e,state:t}){var n,o,i;if(!a.validate(e))throw v.RemoteCommunication(`[RemoteCommunication: connectToChannel()] context=${t.context} invalid channel channelId=${e}`),new Error(`Invalid channel ${e}`);if(t.isOriginator)return void v.RemoteCommunication(`[RemoteCommunication: reject()] context=${t.context} isOriginator=${t.isOriginator} channelId=${e}`);const{socket:s}=null!==(o=null===(n=t.communicationLayer)||void 0===n?void 0:n.state)&&void 0!==o?o:{};(null==s?void 0:s.connected)||(v.RemoteCommunication(`[RemoteCommunication: reject()] context=${t.context} socket already connected`),null==s||s.connect()),S(Object.assign(Object.assign({id:e,event:exports.TrackingEvents.REJECTED},t.originatorInfo),{sdkVersion:t.sdkVersion,commLayerVersion:C.version,walletVersion:null===(i=t.walletInfo)||void 0===i?void 0:i.version}),t.communicationServerUrl).catch((e=>{})),yield new Promise(((n,o)=>{null==s||s.emit(exports.EventType.REJECTED,{channelId:e},((e,i)=>{v.RemoteCommunication(`[RemoteCommunication: reject()] context=${t.context} socket=${null==s?void 0:s.id}`,{error:e,response:i}),e?o(e):n(i)}))}))}))}({channelId:t,state:this.state})}disconnect(t){return e.__awaiter(this,void 0,void 0,(function*(){return ae({options:t,instance:this})}))}},exports.SendAnalytics=S,exports.SocketService=F;
//# sourceMappingURL=metamask-sdk-communication-layer.js.map
