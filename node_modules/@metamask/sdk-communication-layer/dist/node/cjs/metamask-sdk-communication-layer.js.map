{"version": 3, "file": "metamask-sdk-communication-layer.js", "sources": ["../../../src/utils/logger.ts", "../../../src/Analytics.ts", "../../../src/ECIES.ts", "../../../src/config.ts", "../../../src/services/RemoteCommunication/ChannelManager/clean.ts", "../../../src/types/ConnectionStatus.ts", "../../../src/types/EventType.ts", "../../../src/types/InternalEventType.ts", "../../../src/types/KeyExchangeMessageType.ts", "../../../src/types/MessageType.ts", "../../../src/types/TrackingEvent.ts", "../../../src/KeyExchange.ts", "../../../src/services/SocketService/ConnectionManager/handleJoinChannelResult.ts", "../../../src/utils/wait.ts", "../../../src/services/SocketService/ConnectionManager/reconnectSocket.ts", "../../../src/services/SocketService/MessageHandlers/encryptAndSendMessage.ts", "../../../src/services/SocketService/MessageHandlers/handleRpcReplies.ts", "../../../src/services/SocketService/MessageHandlers/handleSendMessage.ts", "../../../src/services/SocketService/KeysManager/handleKeyHandshake.ts", "../../../src/services/SocketService/KeysManager/validateKeyExchange.ts", "../../../src/services/SocketService/MessageHandlers/trackRpcMethod.ts", "../../../src/services/SocketService/ChannelManager/setupChannelListeners.ts", "../../../src/services/SocketService/EventListeners/handleClientsConnected.ts", "../../../src/services/SocketService/EventListeners/handleChannelCreated.ts", "../../../src/services/SocketService/EventListeners/handlesClientsDisconnected.ts", "../../../src/services/SocketService/EventListeners/handleChannelConfig.ts", "../../../src/services/SocketService/EventListeners/handleMessage.ts", "../../../src/services/SocketService/EventListeners/handleChannelRejected.ts", "../../../src/services/SocketService/EventListeners/handleClientsWaitingToJoin.ts", "../../../src/services/SocketService/EventListeners/handleKeyInfo.ts", "../../../src/services/SocketService/EventListeners/handleKeysExchanged.ts", "../../../src/services/SocketService/EventListeners/handleDisconnect.ts", "../../../src/SocketService.ts", "../../../src/services/SocketService/ConnectionManager/setupSocketFocusListener.ts", "../../../src/services/SocketService/KeysManager/resetKeys.ts", "../../../src/services/SocketService/ChannelManager/createChannel.ts", "../../../src/services/SocketService/ConnectionManager/connectToChannel.ts", "../../../src/services/SocketService/KeysManager/keyCheck.ts", "../../../src/services/SocketService/ConnectionManager/ping.ts", "../../../src/services/SocketService/ConnectionManager/pause.ts", "../../../src/services/SocketService/ConnectionManager/resume.ts", "../../../src/services/SocketService/ConnectionManager/disconnect.ts", "../../../src/types/PlatformType.ts", "../../../src/types/AutoConnectType.ts", "../../../src/services/RemoteCommunication/EventListeners/handleAuthorizedEvent.ts", "../../../src/services/RemoteCommunication/EventListeners/handleChannelCreatedEvent.ts", "../../../src/services/RemoteCommunication/EventListeners/handleClientsConnectedEvent.ts", "../../../src/services/RemoteCommunication/EventListeners/handleClientsDisconnectedEvent.ts", "../../../src/services/RemoteCommunication/EventListeners/handleClientsWaitingEvent.ts", "../../../src/services/RemoteCommunication/EventListeners/handleKeysExchangedEvent.ts", "../../../src/services/RemoteCommunication/StateManger/setLastActiveDate.ts", "../../../src/services/RemoteCommunication/EventListeners/handleMessageEvent.ts", "../../../src/services/RemoteCommunication/MessageHandlers/onCommunicationLayerMessage.ts", "../../../src/services/RemoteCommunication/MessageHandlers/handleWalletInfoMessage.ts", "../../../src/services/RemoteCommunication/MessageHandlers/handleWalletInitMessage.ts", "../../../src/services/RemoteCommunication/MessageHandlers/handleTerminateMessage.ts", "../../../src/services/RemoteCommunication/MessageHandlers/handlePauseMessage.ts", "../../../src/services/RemoteCommunication/MessageHandlers/handleReadyMessage.ts", "../../../src/services/RemoteCommunication/MessageHandlers/handleOtpMessage.ts", "../../../src/services/RemoteCommunication/MessageHandlers/handleAuthorizedMessage.ts", "../../../src/services/RemoteCommunication/MessageHandlers/handleOriginatorInfoMessage.ts", "../../../src/services/RemoteCommunication/EventListeners/handleSocketReconnectEvent.ts", "../../../src/services/RemoteCommunication/EventListeners/handleSocketDisconnectedEvent.ts", "../../../src/services/RemoteCommunication/EventListeners/handleFullPersistenceEvent.ts", "../../../src/services/RemoteCommunication/ConnectionManager/initSocketService.ts", "../../../src/services/RemoteCommunication/ConnectionManager/disconnect.ts", "../../../src/types/CommunicationLayerPreference.ts", "../../../src/RemoteCommunication.ts", "../../../src/services/RemoteCommunication/ConnectionManager/originatorSessionConnect.ts", "../../../src/services/RemoteCommunication/ChannelManager/generateChannelIdConnect.ts", "../../../src/services/RemoteCommunication/ConnectionManager/connectToChannel.ts", "../../../src/services/RemoteCommunication/MessageHandlers/sendMessage.ts", "../../../src/services/RemoteCommunication/ConnectionManager/handleAuthorization.ts", "../../../src/services/RemoteCommunication/StorageManager/testStorage.ts", "../../../src/services/RemoteCommunication/ConnectionManager/resume.ts", "../../../src/services/RemoteCommunication/ConnectionManager/rejectChannel.ts"], "sourcesContent": ["import debug from 'debug';\n\n/**\n * Logger for the Key Exchange Layer.\n * Utilizes the 'debug' library to output debug information for the key exchange process.\n */\nconst loggerKeyExchangeLayer = debug('KeyExchange:Layer');\n\n/**\n * Logger for the SocketService Layer, specifically for socket communication.\n * Utilizes the 'debug' library to output debug information for socket services.\n */\nconst loggerServiceLayer = debug('SocketService:Layer');\n\n/**\n * Logger for the Elliptic Curve Integrated Encryption Scheme (ECIES) Layer.\n * Utilizes the 'debug' library to output debug information for ECIES operations.\n */\nconst loggerEciesLayer = debug('Ecies:Layer');\n\n/**\n * Logger for the RemoteCommunication Layer.\n * Utilizes the 'debug' library to output debug information for remote communication.\n */\nconst loggerRemoteLayer = debug('RemoteCommunication:Layer');\n\nloggerKeyExchangeLayer.color = '##95c44e';\nloggerServiceLayer.color = '#f638d7';\nloggerEciesLayer.color = '#465b9c';\nloggerRemoteLayer.color = '#47a2be';\n\n/*\n * Logger object for the communication layer that contains all the individual loggers for each layer.\n * Utilizes the 'debug' library to output debug information.\n */\nexport const logger = {\n  KeyExchange: loggerKeyExchangeLayer,\n  SocketService: loggerServiceLayer,\n  Ecies: loggerEciesLayer,\n  RemoteCommunication: loggerRemoteLayer,\n};\n", "import crossFetch from 'cross-fetch';\nimport { CommunicationLayerPreference } from './types/CommunicationLayerPreference';\nimport { OriginatorInfo } from './types/OriginatorInfo';\nimport { TrackingEvents } from './types/TrackingEvent';\nimport { logger } from './utils/logger';\n\nexport interface AnalyticsProps {\n  id: string;\n  event: TrackingEvents;\n  originatorInfo?: OriginatorInfo;\n  commLayer?: CommunicationLayerPreference;\n  sdkVersion?: string;\n  commLayerVersion?: string;\n  walletVersion?: string;\n  params?: Record<string, unknown>;\n}\n\n// Buffer for storing events\nlet analyticsBuffer: AnalyticsProps[] = [];\nlet tempBuffer: AnalyticsProps[] = []; // Temporary buffer to hold new events during send operation\nlet targetUrl: string | undefined;\n\n// Function to safely add events to the buffer\nfunction addToBuffer(event: AnalyticsProps) {\n  tempBuffer.push(event);\n}\n\n// Function to swap buffers atomically\nfunction swapBuffers() {\n  const swap = tempBuffer;\n  tempBuffer = analyticsBuffer;\n  analyticsBuffer = swap;\n}\n\n// Function to send buffered events\nasync function sendBufferedEvents(parameters: AnalyticsProps) {\n  // TODO:  re-enabled once buffered events activated\n  // if (!targetUrl || (analyticsBuffer.length === 0 && tempBuffer.length === 0)) {\n  //   return;\n  // }\n  if (!targetUrl || !parameters) {\n    return;\n  }\n\n  // Atomically swap the buffers\n  swapBuffers();\n\n  const serverUrl = targetUrl.endsWith('/')\n    ? `${targetUrl}evt`\n    : `${targetUrl}/evt`;\n\n  const flatParams: {\n    [key: string]: unknown;\n  } = { ...parameters };\n  delete flatParams.params;\n\n  // remove params from the event and append each property to the object instead\n  if (parameters.params) {\n    for (const [key, value] of Object.entries(parameters.params)) {\n      flatParams[key] = value;\n    }\n  }\n\n  const body = JSON.stringify(flatParams);\n\n  logger.RemoteCommunication(\n    `[sendBufferedEvents] Sending ${analyticsBuffer.length} analytics events to ${serverUrl}`,\n  );\n\n  try {\n    const response = await crossFetch(serverUrl, {\n      method: 'POST',\n      headers: {\n        Accept: 'application/json',\n        'Content-Type': 'application/json',\n      },\n      body,\n    });\n\n    const text = await response.text();\n    logger.RemoteCommunication(`[sendBufferedEvents] Response: ${text}`);\n\n    // Clear the processed buffer --- operation is atomic and no race condition can happen since we use a separate buffer\n    // eslint-disable-next-line require-atomic-updates\n    analyticsBuffer.length = 0;\n  } catch (error) {\n    console.warn(`Error sending analytics`, error);\n  }\n}\n\n// TODO re-enable whenever we want to activate buffered events and socket code has been updated.\n// // Initialize timer to send analytics in batch every 15 seconds\n// setInterval(() => {\n//   sendBufferedEvents().catch(() => {\n//     // ignore errors\n//   });\n// }, 15000);\n\n// Modified SendAnalytics to add events to buffer instead of sending directly\nexport const SendAnalytics = async (\n  parameters: AnalyticsProps,\n  socketServerUrl: string,\n) => {\n  targetUrl = socketServerUrl;\n\n  // Safely add the analytics event to the buffer\n  addToBuffer(parameters);\n  sendBufferedEvents(parameters).catch(() => {\n    // ignore\n  });\n};\n", "import { <PERSON><PERSON><PERSON> } from 'buffer';\nimport { decrypt, encrypt, PrivateKey } from 'eciesjs';\nimport debug from 'debug';\nimport { logger } from './utils/logger';\n\n/**\n * These properties are optional and should only be used during development for debugging purposes.\n */\nexport interface ECIESProps {\n  debug?: boolean;\n  privateKey?: string;\n}\n\n/**\n * Class that exposes methods to generate and compute\n * Elliptic Curve Integrated Encryption Scheme (ECIES) for key exchange and symmetric encryption/decryption\n *\n * It also exposes encryption/decryption methods that are used\n * by the communication layer to encrypt/decrypt in/out data\n * The encryption/decryption is made using a symmetric key generated from the ECIES key exchange\n */\nexport class ECIES {\n  private ecies: PrivateKey;\n\n  private enabled = true;\n\n  constructor(props?: ECIESProps) {\n    if (props?.debug) {\n      debug.enable('Ecies:Layer');\n    }\n\n    if (props?.privateKey) {\n      this.ecies = PrivateKey.fromHex(props.privateKey);\n    } else {\n      this.ecies = new PrivateKey();\n    }\n\n    logger.Ecies(\n      `[ECIES constructor()] initialized secret: `,\n      this.ecies.toHex(),\n    );\n\n    logger.Ecies(\n      `[ECIES constructor()] initialized public: `,\n      this.ecies.publicKey.toHex(),\n    );\n    logger.Ecies(`[ECIES constructor()] init with`, this);\n  }\n\n  /**\n   * Creates new ECIES instance\n   *\n   * @returns - Generates ECIES instance\n   */\n  generateECIES(): void {\n    this.ecies = new PrivateKey();\n  }\n\n  /**\n   * Returns ECIES instance public key\n   *\n   * @returns - public key in base64 format\n   */\n  getPublicKey(): string {\n    return this.ecies.publicKey.toHex();\n  }\n\n  /**\n   * Encrypts a data message using the public key of the side to encrypt data for\n   *\n   * @param {string} data - data string to be encrypted\n   * @param {string} otherPublicKey - public key of the side to encrypt data for\n   * @returns - encrypted string in base64\n   */\n  encrypt(data: string, otherPublicKey: string): string {\n    let encryptedString = data;\n    if (this.enabled) {\n      try {\n        logger.Ecies(`[ECIES: encrypt()] using otherPublicKey`, otherPublicKey);\n        const payload = Buffer.from(data);\n        const encryptedData = encrypt(otherPublicKey, payload);\n        encryptedString = Buffer.from(encryptedData).toString('base64');\n      } catch (err) {\n        logger.Ecies(`[ECIES: encrypt()] error encrypt:`, err);\n        logger.Ecies(`[ECIES: encrypt()] private: `, this.ecies.toHex());\n        logger.Ecies('[ECIES: encrypt()] data: ', data);\n        logger.Ecies(`[ECIES: encrypt()] otherkey: `, otherPublicKey);\n        throw err;\n      }\n    }\n    return encryptedString;\n  }\n\n  /**\n   * Decrypts a data message using the instance private key\n   *\n   * @param {string} encryptedData - base64 data string to be decrypted\n   * @returns - decrypted data || error message\n   */\n  decrypt(encryptedData: string): string {\n    let decryptedString = encryptedData;\n    if (this.enabled) {\n      try {\n        logger.Ecies(`[ECIES: decrypt()] using privateKey`, this.ecies.toHex());\n        const payload = Buffer.from(encryptedData.toString(), 'base64');\n        const decrypted = decrypt(this.ecies.toHex(), payload);\n\n        decryptedString = decrypted.toString();\n      } catch (error) {\n        logger.Ecies(`[ECIES: decrypt()] error decrypt`, error);\n        logger.Ecies(`[ECIES: decrypt()] private: `, this.ecies.toHex());\n        logger.Ecies(`[ECIES: decrypt()] encryptedData: `, encryptedData);\n        throw error;\n      }\n    }\n\n    return decryptedString;\n  }\n\n  getKeyInfo(): { private: string; public: string } {\n    return {\n      private: this.ecies.toHex(),\n      public: this.ecies.publicKey.toHex(),\n    };\n  }\n\n  toString() {\n    logger.Ecies(`[ECIES: toString()]`, this.getKeyInfo());\n  }\n}\n", "export const DEFAULT_SERVER_URL = 'https://metamask-sdk.api.cx.metamask.io/';\nexport const DEFAULT_SOCKET_TRANSPORTS = ['websocket'];\nexport const MIN_IN_MS = 1000 * 60;\nexport const HOUR_IN_MS = MIN_IN_MS * 60;\nexport const DAY_IN_MS = HOUR_IN_MS * 24;\nexport const DEFAULT_SESSION_TIMEOUT_MS = 7 * DAY_IN_MS;\n\n// time upon which we wait for a metamask reocnnection before creating a new channel\nexport const CHANNEL_MAX_WAITING_TIME = 3 * 1000; // 3 seconds\n\nexport const MAX_RECONNECTION_ATTEMPS = 3;\n\nexport const MAX_RPC_WAIT_TIME = 5 * 60 * 1000; // 5 minutes\n\nexport const PROTOCOL_VERSION = 2;\n\nexport const RPC_METHODS = {\n  METAMASK_GETPROVIDERSTATE: 'metamask_getProviderState',\n  ETH_REQUESTACCOUNTS: 'eth_requestAccounts',\n};\n", "import { logger } from '../../../utils/logger';\nimport { RemoteCommunicationState } from '../../../RemoteCommunication';\n\n/**\n * Cleans the state of the RemoteCommunication, resetting various properties to their default values.\n *\n * @param state Current state of the RemoteCommunication class instance.\n * @returns void\n */\nexport function clean(state: RemoteCommunicationState) {\n  const { context } = state;\n\n  logger.RemoteCommunication(\n    `[RemoteCommunication: clean()] context=${context}`,\n  );\n\n  state.channelConfig = undefined;\n  state.ready = false;\n  state.originatorConnectStarted = false;\n}\n", "export enum ConnectionStatus {\n  // DISCONNECTED: counterparty is disconnected\n  DISCONNECTED = 'disconnected',\n  // WAITING: means connected to the websocket but the counterparty (MetaMask or Dapps) isn't.\n  WAITING = 'waiting',\n  // TIMEOUT: means auto connect didn't establish link within given timeout\n  TIMEOUT = 'timeout',\n  // LINKED: is connected after handshake, using a different verb to avoid confusion to just being connected to the websocket and waiting for counterpart.\n  // LINKED is set when receiving 'READY' message from counterpart.\n  LINKED = 'linked',\n  // PAUSED:\n  PAUSED = 'paused',\n  // TERMINATED: if a user manually disconnect the session.\n  TERMINATED = 'terminated',\n}\n", "export enum EventType {\n  // emitted everytime the current step is updated\n  KEY_INFO = 'key_info',\n  SERVICE_STATUS = 'service_status',\n  PROVIDER_UPDATE = 'provider_update',\n  RPC_UPDATE = 'rpc_update',\n  KEYS_EXCHANGED = 'keys_exchanged',\n  JOIN_CHANNEL = 'join_channel',\n  PUBLIC_KEY = 'public_key',\n  CHANNEL_CREATED = 'channel_created',\n  CLIENTS_CONNECTED = 'clients_connected',\n  CLIENTS_DISCONNECTED = 'clients_disconnected',\n  CLIENTS_WAITING = 'clients_waiting',\n  CLIENTS_READY = 'clients_ready',\n  REJECTED = 'rejected',\n  WALLET_INIT = 'wallet_init',\n  CHANNEL_PERSISTENCE = 'channel_persistence',\n  CONFIG = 'config',\n  MESSAGE_ACK = 'ack',\n  SOCKET_DISCONNECTED = 'socket_disconnected',\n  // socket reconnect should only happen on ios mobile\n  SOCKET_RECONNECT = 'socket_reconnect',\n  OTP = 'otp',\n  // used to trigger R<PERSON> call from comm layer, usually only used for backward compatibility\n  SDK_RPC_CALL = 'sdk_rpc_call',\n  // event emitted when the connection is authorized on the wallet.\n  AUTHORIZED = 'authorized',\n  CONNECTION_STATUS = 'connection_status',\n  MESSAGE = 'message',\n  TERMINATE = 'terminate',\n}\n", "export enum InternalEventType {\n  /**\n   * KEY_EXCHANGE is used between Communication layer and KeyExchange Layer\n   */\n  KEY_EXCHANGE = 'key_exchange',\n}\n", "export enum KeyExchangeMessageType {\n  KEY_<PERSON><PERSON><PERSON><PERSON><PERSON>_START = 'key_handshake_start',\n  KEY_HANDSHAKE_CHECK = 'key_handshake_check',\n  KEY_HANDSHAKE_SYN = 'key_handshake_SYN',\n  <PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>KE_SYNACK = 'key_handshake_SYNAC<PERSON>',\n  KEY_HANDSHAKE_ACK = 'key_handshake_ACK',\n  KEY_HANDSHAKE_WALLET = 'key_handshake_wallet',\n  KEY_HANDSHAKE_NONE = 'none',\n}\n", "export enum MessageType {\n  // TERMINATE: used to inform the other side that connection is terminating and channel id needs to be changed.\n  TERMINATE = 'terminate',\n  ANSWER = 'answer',\n  OFFER = 'offer',\n  CANDIDATE = 'candidate',\n  JSONRPC = 'jsonrpc',\n  WALLET_INFO = 'wallet_info',\n  WALLET_INIT = 'wallet_init',\n  ORIGINATOR_INFO = 'originator_info',\n  PAUSE = 'pause',\n  OTP = 'otp',\n  /**\n   * Sent from the wallet when the user has approved the connection.\n   */\n  AUTHORIZED = 'authorized',\n  /**\n   * Used for debugging purpose and check channel validity.\n   */\n  PING = 'ping',\n  // READY is sent when the connection is linked\n  READY = 'ready',\n}\n", "export enum TrackingEvents {\n  REQUEST = 'sdk_connect_request_started',\n  REQUEST_MOBILE = 'sdk_connect_request_started_mobile',\n  RECONNECT = 'sdk_reconnect_request_started',\n  CONNECTED = 'sdk_connection_established',\n  CONNECTED_MOBILE = 'sdk_connection_established_mobile',\n  AUTHORIZED = 'sdk_connection_authorized',\n  REJECTED = 'sdk_connection_rejected',\n  TERMINATED = 'sdk_connection_terminated',\n  DISCONNECTED = 'sdk_disconnected',\n  SDK_USE_EXTENSION = 'sdk_use_extension',\n  SDK_RPC_REQUEST = 'sdk_rpc_request',\n  SDK_RPC_REQUEST_RECEIVED = 'sdk_rpc_request_received',\n  SDK_RPC_REQUEST_DONE = 'sdk_rpc_request_done',\n  SDK_EXTENSION_UTILIZED = 'sdk_extension_utilized',\n  SDK_USE_INAPP_BROWSER = 'sdk_use_inapp_browser',\n}\n", "import { EventEmitter2 } from 'eventemitter2';\nimport { ECIES, ECIESProps } from './ECIES';\nimport { SocketService } from './SocketService';\nimport { CommunicationLayerMessage } from './types/CommunicationLayerMessage';\nimport { EventType } from './types/EventType';\nimport { InternalEventType } from './types/InternalEventType';\nimport { KeyExchangeMessageType } from './types/KeyExchangeMessageType';\nimport { KeyInfo } from './types/KeyInfo';\nimport { CommunicationLayerLoggingOptions } from './types/LoggingOptions';\nimport { logger } from './utils/logger';\nimport { PROTOCOL_VERSION } from './config';\n\nexport interface KeyExchangeProps {\n  communicationLayer: SocketService;\n  otherPublicKey?: string;\n  sendPublicKey: boolean;\n  context: string;\n  logging?: CommunicationLayerLoggingOptions;\n  ecies?: ECIESProps;\n}\n\nexport class KeyExchange extends EventEmitter2 {\n  private keysExchanged = false;\n\n  private myECIES: ECIES;\n\n  private otherPublicKey?: string;\n\n  private communicationLayer: SocketService;\n\n  private myPublicKey: string;\n\n  private step: KeyExchangeMessageType =\n    KeyExchangeMessageType.KEY_HANDSHAKE_NONE;\n\n  private context: string;\n\n  private debug = false;\n\n  constructor({\n    communicationLayer,\n    otherPublicKey,\n    context,\n    ecies,\n    logging,\n  }: KeyExchangeProps) {\n    super();\n\n    this.context = context;\n\n    this.communicationLayer = communicationLayer;\n\n    if (ecies?.privateKey && otherPublicKey) {\n      logger.KeyExchange(\n        `[KeyExchange: constructor()] otherPubKey=${otherPublicKey} set keysExchanged to true!`,\n        ecies,\n      );\n\n      this.keysExchanged = true;\n    }\n\n    this.myECIES = new ECIES({ ...ecies, debug: logging?.eciesLayer });\n    this.communicationLayer.state.eciesInstance = this.myECIES;\n    this.myPublicKey = this.myECIES.getPublicKey();\n\n    this.debug = logging?.keyExchangeLayer === true;\n\n    if (otherPublicKey) {\n      this.setOtherPublicKey(otherPublicKey);\n    }\n\n    this.communicationLayer.on(\n      InternalEventType.KEY_EXCHANGE,\n      this.onKeyExchangeMessage.bind(this),\n    );\n  }\n\n  public onKeyExchangeMessage(keyExchangeMsg: {\n    message: CommunicationLayerMessage;\n  }) {\n    const { relayPersistence } = this.communicationLayer.remote.state;\n\n    logger.KeyExchange(\n      `[KeyExchange: onKeyExchangeMessage()] context=${this.context} keysExchanged=${this.keysExchanged} relayPersistence=${relayPersistence}`,\n      keyExchangeMsg,\n    );\n\n    if (relayPersistence) {\n      logger.KeyExchange(\n        `[KeyExchange: onKeyExchangeMessage()] Ignoring key exchange message because relay persistence is activated`,\n      );\n      return;\n    }\n\n    const { message } = keyExchangeMsg;\n    if (this.keysExchanged) {\n      logger.KeyExchange(\n        `[KeyExchange: onKeyExchangeMessage()] context=${this.context} received handshake while already exchanged. step=${this.step} otherPubKey=${this.otherPublicKey}`,\n      );\n      // FIXME check if correct way / when is it really happening?\n      // return;\n    }\n\n    this.emit(EventType.KEY_INFO, message.type);\n\n    if (message.type === KeyExchangeMessageType.KEY_HANDSHAKE_SYN) {\n      this.checkStep([\n        KeyExchangeMessageType.KEY_HANDSHAKE_NONE,\n        KeyExchangeMessageType.KEY_HANDSHAKE_ACK,\n      ]);\n\n      logger.KeyExchange(\n        `[KeyExchange: onKeyExchangeMessage()] KEY_HANDSHAKE_SYN`,\n        message,\n      );\n\n      if (message.pubkey) {\n        this.setOtherPublicKey(message.pubkey);\n      }\n\n      this.communicationLayer\n        .sendMessage({\n          type: KeyExchangeMessageType.KEY_HANDSHAKE_SYNACK,\n          pubkey: this.myPublicKey,\n        })\n        .catch((error) => {\n          logger.KeyExchange(\n            `[KeyExchange: onKeyExchangeMessage()] Error sending KEY_HANDSHAKE_SYNACK`,\n            error,\n          );\n        });\n\n      this.setStep(KeyExchangeMessageType.KEY_HANDSHAKE_ACK);\n    } else if (message.type === KeyExchangeMessageType.KEY_HANDSHAKE_SYNACK) {\n      // TODO currently key exchange start from both side so step may be on both SYNACK or NONE or ACK.\n      this.checkStep([\n        KeyExchangeMessageType.KEY_HANDSHAKE_SYNACK,\n        KeyExchangeMessageType.KEY_HANDSHAKE_ACK,\n        KeyExchangeMessageType.KEY_HANDSHAKE_NONE,\n      ]);\n\n      logger.KeyExchange(\n        `[KeyExchange: onKeyExchangeMessage()] KEY_HANDSHAKE_SYNACK`,\n      );\n\n      if (message.pubkey) {\n        this.setOtherPublicKey(message.pubkey);\n      }\n\n      this.communicationLayer\n        .sendMessage({\n          type: KeyExchangeMessageType.KEY_HANDSHAKE_ACK,\n        })\n        .catch((error) => {\n          logger.KeyExchange(\n            `[KeyExchange: onKeyExchangeMessage()] Error sending KEY_HANDSHAKE_ACK`,\n            error,\n          );\n        });\n      this.keysExchanged = true;\n      // Reset step value for next exchange.\n      this.setStep(KeyExchangeMessageType.KEY_HANDSHAKE_ACK);\n      this.emit(EventType.KEYS_EXCHANGED);\n    } else if (message.type === KeyExchangeMessageType.KEY_HANDSHAKE_ACK) {\n      logger.KeyExchange(\n        `[KeyExchange: onKeyExchangeMessage()] KEY_HANDSHAKE_ACK set keysExchanged to true!`,\n      );\n\n      this.checkStep([\n        KeyExchangeMessageType.KEY_HANDSHAKE_ACK,\n        KeyExchangeMessageType.KEY_HANDSHAKE_NONE,\n      ]);\n      this.keysExchanged = true;\n      // Reset step value for next exchange.\n      this.setStep(KeyExchangeMessageType.KEY_HANDSHAKE_ACK);\n      this.emit(EventType.KEYS_EXCHANGED);\n    }\n  }\n\n  resetKeys(ecies?: ECIESProps) {\n    this.clean();\n    this.myECIES = new ECIES(ecies);\n  }\n\n  clean(): void {\n    logger.KeyExchange(\n      `[KeyExchange: clean()] context=${this.context} reset handshake state`,\n    );\n\n    this.setStep(KeyExchangeMessageType.KEY_HANDSHAKE_NONE);\n    this.emit(EventType.KEY_INFO, this.step);\n    this.keysExchanged = false;\n    // Do not uncomment next line otherwise it breaks old sdk compatibility.\n    // this.otherPublicKey = undefined;\n  }\n\n  start({\n    isOriginator,\n    force,\n  }: {\n    isOriginator: boolean;\n    force?: boolean;\n  }): void {\n    const { relayPersistence, protocolVersion } =\n      this.communicationLayer.remote.state;\n\n    const v2Protocol = protocolVersion >= 2;\n\n    if (relayPersistence) {\n      logger.KeyExchange(\n        `[KeyExchange: start()] Ignoring key exchange message because relay persistence is activated`,\n      );\n\n      console.log(\n        `[KeyExchange: start()] relayPersistence=${relayPersistence}`,\n      );\n      return;\n    }\n\n    logger.KeyExchange(\n      `[KeyExchange: start()] context=${this.context} protocolVersion=${protocolVersion} isOriginator=${isOriginator} step=${this.step} force=${force} relayPersistence=${relayPersistence} keysExchanged=${this.keysExchanged}`,\n    );\n\n    if (!isOriginator) {\n      // force is used to redo keyexchange even if already exchanged.\n      if (!this.keysExchanged || force === true) {\n        if (v2Protocol) {\n          // Ask to start exchange only if not already in progress\n          this.communicationLayer\n            .sendMessage({\n              type: KeyExchangeMessageType.KEY_HANDSHAKE_SYNACK,\n              pubkey: this.myPublicKey,\n              v: PROTOCOL_VERSION,\n            })\n            .catch((error) => {\n              logger.KeyExchange(\n                `[KeyExchange: start()] Error sending KEY_HANDSHAKE_SYNACK`,\n                error,\n              );\n            });\n          // Ignore completion --- already consider keys exchanged completed in case mobile to mobile and the client was disconnected\n          // We need to be able to send the walletInfo onto the relayer.\n          // TODO: this.keysExchanged = true;\n        } else {\n          // Ask to start exchange only if not already in progress\n          this.communicationLayer\n            .sendMessage({\n              type: KeyExchangeMessageType.KEY_HANDSHAKE_START,\n            })\n            .catch((error) => {\n              logger.KeyExchange(\n                `[KeyExchange: start()] Error sending KEY_HANDSHAKE_START`,\n                error,\n              );\n            });\n          this.clean();\n        }\n      } else {\n        logger.KeyExchange(\n          `[KeyExchange: start()] don't send KEY_HANDSHAKE_START -- exchange already done.`,\n        );\n      }\n\n      return;\n    }\n\n    if (\n      (this.keysExchanged ||\n        (this.step !== KeyExchangeMessageType.KEY_HANDSHAKE_NONE &&\n          this.step !== KeyExchangeMessageType.KEY_HANDSHAKE_SYNACK)) &&\n      !force\n    ) {\n      // Key exchange can be restarted if the wallet ask for a new key.\n      logger.KeyExchange(\n        `[KeyExchange: start()] context=${\n          this.context\n        } -- key exchange already ${\n          this.keysExchanged ? 'done' : 'in progress'\n        } -- aborted.`,\n        this.step,\n      );\n      return;\n    }\n\n    logger.KeyExchange(\n      `[KeyExchange: start()] context=${this.context} -- start key exchange (force=${force}) -- step=${this.step}`,\n      this.step,\n    );\n\n    this.clean();\n    // except a SYN_ACK for next step\n    this.setStep(KeyExchangeMessageType.KEY_HANDSHAKE_SYNACK);\n    // From v0.2.0, we Always send the public key because exchange can be restarted at any time.\n    this.communicationLayer\n      .sendMessage({\n        type: KeyExchangeMessageType.KEY_HANDSHAKE_SYN,\n        pubkey: this.myPublicKey,\n        v: PROTOCOL_VERSION,\n      })\n      .catch((error) => {\n        logger.KeyExchange(\n          `[KeyExchange: start()] Error sending KEY_HANDSHAKE_SYN`,\n          error,\n        );\n      });\n  }\n\n  setStep(step: KeyExchangeMessageType): void {\n    this.step = step;\n    this.emit(EventType.KEY_INFO, step);\n  }\n\n  checkStep(stepList: string[]): void {\n    if (stepList.length > 0 && stepList.indexOf(this.step.toString()) === -1) {\n      // Graceful warning but continue communication\n      console.warn(\n        `[KeyExchange: checkStep()]  Wrong Step \"${this.step}\" not within ${stepList}`,\n      );\n    }\n  }\n\n  setRelayPersistence({\n    localKey,\n    otherKey,\n  }: {\n    localKey: string;\n    otherKey: string;\n  }) {\n    this.otherPublicKey = otherKey;\n    this.myECIES = new ECIES({ privateKey: localKey, debug: this.debug });\n    this.keysExchanged = true;\n  }\n\n  setKeysExchanged(keysExchanged: boolean) {\n    this.keysExchanged = keysExchanged;\n  }\n\n  areKeysExchanged() {\n    return this.keysExchanged;\n  }\n\n  getMyPublicKey() {\n    return this.myPublicKey;\n  }\n\n  getOtherPublicKey() {\n    return this.otherPublicKey;\n  }\n\n  setOtherPublicKey(otherPubKey: string) {\n    logger.KeyExchange(`[KeyExchange: setOtherPubKey()]`, otherPubKey);\n\n    this.otherPublicKey = otherPubKey;\n  }\n\n  encryptMessage(message: string): string {\n    if (!this.otherPublicKey) {\n      throw new Error(\n        'encryptMessage: Keys not exchanged - missing otherPubKey',\n      );\n    }\n    return this.myECIES.encrypt(message, this.otherPublicKey);\n  }\n\n  decryptMessage(message: string): string {\n    if (!this.otherPublicKey) {\n      throw new Error(\n        'decryptMessage: Keys not exchanged - missing otherPubKey',\n      );\n    }\n\n    return this.myECIES.decrypt(message);\n  }\n\n  getKeyInfo(): KeyInfo {\n    return {\n      ecies: { ...this.myECIES.getKeyInfo(), otherPubKey: this.otherPublicKey },\n      step: this.step,\n      keysExchanged: this.areKeysExchanged(),\n    };\n  }\n\n  toString() {\n    const buf = {\n      keyInfo: this.getKeyInfo(),\n      keysExchanged: this.keysExchanged,\n      step: this.step,\n    };\n    return JSON.stringify(buf);\n  }\n}\n", "import { SocketService } from '../../../SocketService';\nimport { EventType } from '../../../types/EventType';\nimport { KeyExchangeMessageType } from '../../../types/KeyExchangeMessageType';\nimport { MessageType } from '../../../types/MessageType';\nimport { ChannelConfig } from '../../../types/ChannelConfig';\nimport { DEFAULT_SESSION_TIMEOUT_MS } from '../../../config';\nimport { ConnectionStatus } from '../../../types/ConnectionStatus';\nimport { logger } from '../../../utils/logger';\nimport { SendAnalytics } from '../../../Analytics';\nimport { TrackingEvents } from '../../../types/TrackingEvent';\n\nimport packageJson from '../../../../package.json';\n\nexport interface JoinChannelResult {\n  ready: boolean;\n  rejected?: boolean;\n  persistence?: boolean;\n  walletKey?: string;\n}\n\nexport const handleJoinChannelResults = async (\n  instance: SocketService,\n  error: string | null,\n  result?: JoinChannelResult,\n) => {\n  const { remote, state } = instance;\n  const { channelId, isOriginator } = state;\n\n  if (error === 'error_terminated') {\n    logger.SocketService(\n      `handleJoinChannelResults: Channel ${channelId} terminated`,\n    );\n    instance.emit(EventType.TERMINATE);\n    return;\n  }\n\n  if (!result) {\n    logger.SocketService(\n      `handleJoinChannelResults: No result for channel ${channelId}`,\n    );\n    return;\n  }\n\n  const { persistence, walletKey, rejected } = result;\n\n  logger.SocketService(\n    `handleJoinChannelResults: Channel ${channelId} persistence=${persistence} walletKey=${walletKey} rejected=${rejected}`,\n  );\n\n  if (rejected) {\n    logger.SocketService(\n      `handleJoinChannelResults: Channel ${channelId} rejected`,\n    );\n    await instance.remote.disconnect({ terminate: true });\n    instance.remote.emit(EventType.REJECTED, { channelId });\n    instance.remote.emitServiceStatusEvent();\n    return;\n  }\n\n  if (walletKey && !remote.state.channelConfig?.otherKey) {\n    const keyExchange = instance.getKeyExchange();\n    keyExchange.setOtherPublicKey(walletKey);\n    instance.state.keyExchange?.setKeysExchanged(true);\n    remote.state.ready = true;\n    remote.state.authorized = true;\n    remote.emit(EventType.AUTHORIZED);\n\n    const { communicationLayer, storageManager } = remote.state;\n\n    const channelConfig: ChannelConfig = {\n      ...remote.state.channelConfig,\n      channelId: remote.state.channelId ?? '',\n      validUntil: Date.now() + DEFAULT_SESSION_TIMEOUT_MS,\n      localKey: communicationLayer?.getKeyInfo().ecies.private,\n      otherKey: walletKey,\n    };\n\n    instance\n      .sendMessage({\n        type: KeyExchangeMessageType.KEY_HANDSHAKE_ACK,\n      })\n      .catch((err) => {\n        console.error(err);\n      });\n\n    instance.state.socket?.emit(MessageType.PING, {\n      id: channelId,\n      clientType: isOriginator ? 'dapp' : 'wallet',\n      context: 'on_channel_reconnect',\n      message: '',\n    });\n\n    await storageManager?.persistChannelConfig(channelConfig);\n    remote.emitServiceStatusEvent();\n    remote.setConnectionStatus(ConnectionStatus.LINKED);\n  }\n\n  if (persistence) {\n    instance.emit(EventType.CHANNEL_PERSISTENCE);\n    instance.state.keyExchange?.setKeysExchanged(true);\n    remote.state.ready = true;\n    remote.state.authorized = true;\n    remote.emit(EventType.AUTHORIZED);\n\n    SendAnalytics(\n      {\n        id: channelId ?? '',\n        event: isOriginator\n          ? TrackingEvents.CONNECTED\n          : TrackingEvents.CONNECTED_MOBILE,\n        ...instance.remote.state.originatorInfo,\n        sdkVersion: instance.remote.state.sdkVersion,\n        commLayer: instance.state.communicationLayerPreference,\n        commLayerVersion: packageJson.version,\n        walletVersion: instance.remote.state.walletInfo?.version,\n      },\n      state.communicationServerUrl,\n    ).catch((err) => {\n      console.error(`Cannot send analytics`, err);\n    });\n  }\n};\n", "import { MAX_RPC_WAIT_TIME } from '../config';\nimport {\n  RPCMethod<PERSON>ache,\n  RPCMethodResult,\n  SocketService,\n} from '../SocketService';\n\nexport const wait = (ms: number) => {\n  return new Promise((resolve) => {\n    setTimeout(resolve, ms);\n  });\n};\n\nexport const waitForRpc = async (\n  rpcId: string,\n  rpc: RPCMethodCache,\n  interval = 200, // 200ms\n): Promise<RPCMethodResult> => {\n  let result;\n  const startTime = Date.now();\n\n  let hasTimedout = false;\n\n  while (!hasTimedout) {\n    const waitTime = Date.now() - startTime;\n    hasTimedout = waitTime > MAX_RPC_WAIT_TIME;\n\n    // console.debug(`Waiting for RPC ${rpcId}... (${waitTime}ms)`);\n    result = rpc[rpcId];\n    if (result.elapsedTime !== undefined) {\n      return result;\n    }\n    await wait(interval);\n  }\n  throw new Error(`RPC ${rpcId} timed out`);\n};\n\nexport const waitForNextRpcCall = async ({\n  rpcId,\n  instance,\n}: {\n  rpcId: string;\n  instance: SocketService;\n}) => {\n  while (\n    instance.state.lastRpcId === rpcId ||\n    instance.state.lastRpcId === undefined\n  ) {\n    await wait(200);\n  }\n  return instance.state.lastRpcId;\n};\n", "// packages/sdk-communication-layer/src/services/SocketService/ConnectionManager/reconnectSocket.ts\nimport { MAX_RECONNECTION_ATTEMPS } from '../../../config';\nimport { SocketService } from '../../../SocketService';\nimport { EventType } from '../../../types/EventType';\nimport { MessageType } from '../../../types/MessageType';\nimport { logger } from '../../../utils/logger';\nimport { wait } from '../../../utils/wait';\nimport {\n  handleJoinChannelResults,\n  JoinChannelResult,\n} from './handleJoinChannelResult';\n\n/**\n * Attempts to reconnect the socket after a disconnection.\n * It first waits for a brief delay to prevent potential issues, then checks if the socket is not already connected.\n * If the socket is not connected, it sets the `resumed` state to true, reconnects the socket, and emits a SOCKET_RECONNECT event.\n * It also emits a JOIN_CHANNEL event to rejoin the channel.\n *\n * @param instance The current instance of the SocketService.\n */\nexport const reconnectSocket = async (instance: SocketService) => {\n  const { state } = instance;\n  const { socket, channelId, context, isOriginator, isReconnecting } = state;\n\n  if (isReconnecting) {\n    logger.SocketService(\n      `[SocketService: reconnectSocket()] Reconnection already in progress, skipping`,\n      instance,\n    );\n    return false;\n  }\n\n  if (!socket) {\n    logger.SocketService(\n      `[SocketService: reconnectSocket()] socket is not defined`,\n      instance,\n    );\n    return false;\n  }\n\n  if (!channelId) {\n    // ignore reconnect if channelId is not defined\n    return false;\n  }\n\n  const { connected } = socket;\n  state.isReconnecting = true;\n  state.reconnectionAttempts = 0;\n\n  logger.SocketService(\n    `[SocketService: reconnectSocket()] connected=${connected} trying to reconnect after socketio disconnection`,\n    instance,\n  );\n\n  try {\n    while (state.reconnectionAttempts < MAX_RECONNECTION_ATTEMPS) {\n      logger.SocketService(\n        `[SocketService: reconnectSocket()] Attempt ${\n          state.reconnectionAttempts + 1\n        } of ${MAX_RECONNECTION_ATTEMPS}`,\n        instance,\n      );\n\n      // https://stackoverflow.com/questions/53297188/afnetworking-error-53-during-attempted-background-fetch\n      await wait(200); // Delay to prevent IOS error\n\n      if (socket.connected) {\n        logger.SocketService(\n          `Socket already connected --- ping to retrieve messages`,\n        );\n\n        socket.emit(MessageType.PING, {\n          id: channelId,\n          clientType: isOriginator ? 'dapp' : 'wallet',\n          context: 'on_channel_config',\n          message: '',\n        });\n        return true;\n      }\n\n      state.resumed = true;\n      socket.connect();\n\n      instance.emit(EventType.SOCKET_RECONNECT);\n\n      try {\n        await new Promise<void>((resolve, reject) => {\n          socket.emit(\n            EventType.JOIN_CHANNEL,\n            {\n              channelId,\n              context: `${context}connect_again`,\n              clientType: isOriginator ? 'dapp' : 'wallet',\n            },\n            async (error: string | null, result?: JoinChannelResult) => {\n              try {\n                await handleJoinChannelResults(instance, error, result);\n                resolve();\n              } catch (runtimeError) {\n                reject(runtimeError);\n              }\n            },\n          );\n        });\n\n        // Add another delay to make sure connected state is available.\n        await wait(100);\n        if (socket.connected) {\n          logger.SocketService(\n            `Reconnection successful on attempt ${\n              state.reconnectionAttempts + 1\n            }`,\n          );\n          return true;\n        }\n      } catch (error) {\n        logger.SocketService(\n          `Error during reconnection attempt ${\n            state.reconnectionAttempts + 1\n          }:`,\n          error,\n        );\n      }\n\n      state.reconnectionAttempts += 1;\n      if (state.reconnectionAttempts < MAX_RECONNECTION_ATTEMPS) {\n        await wait(200);\n      }\n    }\n\n    logger.SocketService(\n      `Failed to reconnect after ${MAX_RECONNECTION_ATTEMPS} attempts`,\n    );\n    return false;\n  } finally {\n    state.isReconnecting = false;\n    state.reconnectionAttempts = 0;\n  }\n};\n", "import { logger } from '../../../utils/logger';\nimport { SocketService } from '../../../SocketService';\nimport { CommunicationLayerMessage } from '../../../types/CommunicationLayerMessage';\nimport { EventType } from '../../../types/EventType';\nimport { MessageType } from '../../../types/MessageType';\n\n/**\n * Encrypts and sends the provided message using the SocketService instance.\n * It encrypts the message using the key exchange and prepares the message to be sent.\n * If the instance has plaintext debugging enabled, it includes the plaintext version of the message.\n * The function logs debug information about the encrypted message before sending it.\n *\n * @param instance The current instance of the SocketService.\n * @param message The message to be encrypted and sent.\n */\nexport async function encryptAndSendMessage(\n  instance: SocketService,\n  message: CommunicationLayerMessage,\n): Promise<boolean> {\n  const encryptedMessage = instance.state.keyExchange?.encryptMessage(\n    JSON.stringify(message),\n  );\n  const messageToSend = {\n    id: instance.state.channelId,\n    context: instance.state.context,\n    clientType: instance.state.isOriginator ? 'dapp' : 'wallet',\n    message: encryptedMessage,\n    plaintext: instance.state.hasPlaintext\n      ? JSON.stringify(message)\n      : undefined,\n  };\n\n  logger.SocketService(\n    `[SocketService: encryptAndSendMessage()] context=${instance.state.context}`,\n    messageToSend,\n  );\n\n  if (message.type === MessageType.TERMINATE) {\n    instance.state.manualDisconnect = true;\n  }\n\n  return new Promise((resolve, reject) => {\n    instance.state.socket?.emit(\n      EventType.MESSAGE,\n      messageToSend,\n      (error: Error | null, response?: { success: boolean }) => {\n        if (error) {\n          logger.SocketService(\n            `[SocketService: encryptAndSendMessage()] error=${error}`,\n          );\n          reject(error);\n        }\n\n        logger.SocketService(`[encryptAndSendMessage] response`, response);\n        resolve(response?.success ?? false);\n      },\n    );\n  });\n}\n", "import { logger } from '../../../utils/logger';\nimport { EventType } from '../../../types/EventType';\nimport { SocketService } from '../../../SocketService';\nimport { CommunicationLayerMessage } from '../../../types/CommunicationLayerMessage';\nimport { waitForNextRpcCall, waitForRpc } from '../../../utils/wait';\n\nconst DEFAULT_SKIPPED_RPC_ERROR = 'SDK_CONNECTION_ISSUE';\nenum PromiseType {\n  RPC_CHECK = 'rpcCheck',\n  SKIPPED_RPC = 'skippedRpc',\n}\n\n/**\n * Handles the waiting for RPC replies for the provided message.\n * If the instance is the originator of the message and the message has an associated RPC ID,\n * it waits for the corresponding RPC reply using the `waitForRpc` utility function.\n * When the reply is received, it logs debug information about the RPC reply.\n *\n * @param instance The current instance of the SocketService.\n * @param message The message for which to handle RPC replies.\n */\nexport async function handleRpcReplies(\n  instance: SocketService,\n  message: CommunicationLayerMessage,\n) {\n  const rpcId = message?.id;\n  const method = message?.method ?? '';\n\n  if (instance.state.isOriginator && rpcId) {\n    try {\n      const rpcCheckPromise = waitForRpc(\n        rpcId,\n        instance.state.rpcMethodTracker,\n        200,\n      ).then((result) => ({ type: PromiseType.RPC_CHECK, result }));\n\n      // Check for missed rpc calls which could historically happen on older wallet version.\n      // In this case manually trigger an error.\n      const checkForSkippedRpcPromise = (async () => {\n        // wait for next rpc call to be sent and defined.\n        const nextRpcId = await waitForNextRpcCall({ instance, rpcId });\n        const result = await waitForRpc(\n          nextRpcId,\n          instance.state.rpcMethodTracker,\n          200,\n        );\n        // If it returns before a previous rpc calls, it means the previous one was skipped.\n        return { type: PromiseType.SKIPPED_RPC, result };\n      })();\n\n      const winner = await Promise.race([\n        rpcCheckPromise,\n        checkForSkippedRpcPromise,\n      ]);\n\n      if (winner.type === PromiseType.RPC_CHECK) {\n        const rpcCheck = winner.result;\n        // rpcCheck resolved first, handle normally\n        logger.SocketService(\n          `[SocketService:handleRpcReplies()] id=${message.id} ${method} ( ${rpcCheck.elapsedTime} ms)`,\n          rpcCheck.result,\n        );\n      } else if (winner.type === PromiseType.SKIPPED_RPC) {\n        const { result } = winner;\n        // set the rpc has timedout and error.\n        console.warn(\n          `[SocketService handleRpcReplies()] RPC METHOD HAS BEEN SKIPPED rpcid=${rpcId} method=${method}`,\n          result,\n        );\n\n        const rpcResult = {\n          ...instance.state.rpcMethodTracker[rpcId],\n          error: new Error(DEFAULT_SKIPPED_RPC_ERROR),\n        };\n        instance.emit(EventType.RPC_UPDATE, rpcResult);\n\n        // simulate wallet error message if a message was skipped.\n        const errorMessage = {\n          data: { ...rpcResult, jsonrpc: '2.0' },\n          name: 'metamask-provider',\n        };\n        instance.emit(EventType.MESSAGE, { message: errorMessage });\n      } else {\n        throw new Error(`Error handling RPC replies for ${rpcId}`);\n      }\n    } catch (err) {\n      console.warn(\n        `[SocketService handleRpcReplies()] Error rpcId=${message.id} ${method}`,\n        err,\n      );\n      throw err;\n    }\n  }\n}\n", "import { SendAnalytics } from '../../../Analytics';\nimport { SocketService } from '../../../SocketService';\nimport { CommunicationLayerMessage } from '../../../types/CommunicationLayerMessage';\nimport { TrackingEvents } from '../../../types/TrackingEvent';\nimport { logger } from '../../../utils/logger';\nimport { handleKeyHandshake, validateKeyExchange } from '../KeysManager';\nimport { encryptAndSendMessage } from './encryptAndSendMessage';\nimport { handleRpcReplies } from './handleRpcReplies';\nimport { trackRpcMethod } from './trackRpcMethod';\n\nexport const lcLogguedRPCs = [\n  'eth_sendTransaction',\n  'eth_signTypedData',\n  'eth_signTransaction',\n  'personal_sign',\n  'wallet_requestPermissions',\n  'wallet_switchEthereumChain',\n  'eth_signTypedData_v3',\n  'eth_signTypedData_v4',\n  'metamask_connectSign',\n  'metamask_connectWith',\n  'metamask_batch',\n].map((method) => method.toLowerCase());\n\n/**\n * Handles sending a message using the SocketService instance.\n * It first checks if a channel has been created and throws an error if not.\n * Then, it logs debug information about the message and its encryption status.\n * It checks if the message is a key handshake message and handles it if it is.\n * If the message is not a key handshake message, it validates the key exchange status.\n * It tracks the RPC method if applicable.\n * It encrypts and sends the message.\n * Finally, if the user is the originator, it waits for a reply in case of certain messages.\n *\n * @param instance The current instance of the SocketService.\n * @param message The message to be sent.\n */\nexport async function handleSendMessage(\n  instance: SocketService,\n  message: CommunicationLayerMessage,\n): Promise<boolean> {\n  if (!instance.state.channelId) {\n    logger.SocketService(\n      `handleSendMessage: no channelId - Create a channel first`,\n    );\n    // Throw the error asynchronously\n    throw new Error('Create a channel first');\n  }\n\n  logger.SocketService(\n    `[SocketService: handleSendMessage()] context=${\n      instance.state.context\n    } areKeysExchanged=${instance.state.keyExchange?.areKeysExchanged()}`,\n    message,\n  );\n\n  const isKeyHandshakeMessage = message?.type?.startsWith('key_handshake');\n\n  if (isKeyHandshakeMessage) {\n    handleKeyHandshake(instance, message);\n    return true;\n  }\n\n  validateKeyExchange(instance, message);\n\n  trackRpcMethod(instance, message);\n\n  const sent = await encryptAndSendMessage(instance, message);\n\n  if (instance.remote.state.analytics) {\n    // Only logs specific RPCs\n    if (\n      instance.remote.state.isOriginator &&\n      message.method &&\n      lcLogguedRPCs.includes(message.method.toLowerCase())\n    ) {\n      SendAnalytics(\n        {\n          id: instance.remote.state.channelId ?? '',\n          event: TrackingEvents.SDK_RPC_REQUEST,\n          params: {\n            method: message.method,\n            from: 'mobile',\n          },\n        },\n        instance.remote.state.communicationServerUrl,\n      ).catch((err) => {\n        console.error(`[handleSendMessage] Cannot send analytics`, err);\n      });\n    }\n  }\n\n  // Only makes sense on originator side.\n  // wait for reply when eth_requestAccounts is sent.\n  handleRpcReplies(instance, message).catch((err) => {\n    console.warn('[handleSendMessage] Error handleRpcReplies', err);\n  });\n\n  return sent;\n}\n", "import { logger } from '../../../utils/logger';\nimport { SocketService } from '../../../SocketService';\nimport { CommunicationLayerMessage } from '../../../types/CommunicationLayerMessage';\nimport { EventType } from '../../../types/EventType';\n\n/**\n * Handles the sending of key handshake messages.\n * If the message type starts with 'key_handshake', the function sends the message without encryption.\n *\n * @param instance The current instance of the SocketService.\n * @param message The message to be sent.\n * @returns {boolean} Returns true if the message was a key handshake message, otherwise false.\n */\nexport function handleKeyHandshake(\n  instance: SocketService,\n  message: CommunicationLayerMessage,\n) {\n  logger.SocketService(\n    `[SocketService: handleKeyHandshake()] context=${instance.state.context}`,\n    message,\n  );\n\n  instance.state.socket?.emit(EventType.MESSAGE, {\n    id: instance.state.channelId,\n    context: instance.state.context,\n    clientType: instance.state.isOriginator ? 'dapp' : 'wallet',\n    message,\n  });\n}\n", "import { logger } from '../../../utils/logger';\nimport { SocketService } from '../../../SocketService';\nimport { CommunicationLayerMessage } from '../../../types/CommunicationLayerMessage';\n\n/**\n * Validates whether key exchange has been completed before sending a message.\n * If keys are not exchanged, an error is thrown.\n *\n * @param instance The current instance of the SocketService.\n * @param message The message for which to validate key exchange.\n * @throws {Error} Thrown if keys have not been exchanged.\n */\nexport function validateKeyExchange(\n  instance: SocketService,\n  message: CommunicationLayerMessage,\n) {\n  if (\n    !instance.state.keyExchange?.areKeysExchanged() &&\n    !instance.remote.state.relayPersistence\n  ) {\n    logger.SocketService(\n      `[SocketService: validateKeyExchange()] context=${instance.state.context} ERROR keys not exchanged`,\n      message,\n    );\n\n    console.error(\n      `[SocketService: validateKeyExchange()] ERROR keys not exchanged`,\n      message,\n    );\n    throw new Error('Keys not exchanged BBB');\n  }\n}\n", "import { SocketService } from '../../../SocketService';\nimport { CommunicationLayerMessage } from '../../../types/CommunicationLayerMessage';\nimport { EventType } from '../../../types/EventType';\n\n/**\n * Tracks the RPC method for the provided message in the `rpcMethodTracker` object.\n * If the instance is the originator of the message and the message has an associated RPC ID,\n * it records the method and timestamp in the `rpcMethodTracker` for later reference.\n *\n * @param instance The current instance of the SocketService.\n * @param message The message for which to track the RPC method.\n */\nexport function trackRpcMethod(\n  instance: SocketService,\n  message: CommunicationLayerMessage,\n) {\n  const method = message?.method ?? '';\n  const rpcId = message?.id;\n  if (instance.state.isOriginator && rpcId) {\n    instance.state.rpcMethodTracker[rpcId] = {\n      id: rpcId,\n      timestamp: Date.now(),\n      method,\n    };\n    instance.emit(EventType.RPC_UPDATE, instance.state.rpcMethodTracker[rpcId]);\n  }\n}\n", "import { logger } from '../../../utils/logger';\nimport { SocketService } from '../../../SocketService';\nimport { EventType } from '../../../types/EventType';\nimport {\n  handleChannelCreated,\n  handleClientsConnected,\n  handleClientsWaitingToJoin,\n  handleDisconnect,\n  handleKeyInfo,\n  handleKeysExchanged,\n  handleMessage,\n  handlesClientsDisconnected,\n} from '../EventListeners';\nimport { handleChannelConfig } from '../EventListeners/handleChannelConfig';\nimport { reconnectSocket } from '../ConnectionManager/reconnectSocket';\nimport { handleChannelRejected } from '../EventListeners/handleChannelRejected';\n\nconst channelEventListenerMap = [\n  {\n    event: EventType.CLIENTS_CONNECTED,\n    handler: handleClientsConnected,\n  },\n  {\n    event: EventType.CHANNEL_CREATED,\n    handler: handleChannelCreated,\n  },\n  {\n    event: EventType.CLIENTS_DISCONNECTED,\n    handler: handlesClientsDisconnected,\n  },\n  { event: EventType.CONFIG, handler: handleChannelConfig },\n  { event: EventType.MESSAGE, handler: handleMessage },\n  { event: EventType.REJECTED, handler: handleChannelRejected },\n  {\n    event: 'clients_waiting_to_join',\n    handler: handleClientsWaitingToJoin,\n  },\n];\n\nconst keyExchangeEventListenerMap = [\n  {\n    event: EventType.KEY_INFO,\n    handler: handleKeyInfo,\n  },\n  {\n    event: EventType.KEYS_EXCHANGED,\n    handler: handleKeysExchanged,\n  },\n];\n\n/**\n * Sets up event listeners for a SocketService instance associated with a specific channel.\n * If debugging is enabled, a debug message is logged indicating the setup process.\n * Event listeners are added to the socket for events defined in the\n * `socketEventListenerMap`, `channelEventListenerMap`, and `keyExchangeEventListenerMap`.\n *\n * @param instance The current instance of the SocketService.\n * @param channelId The ID of the channel associated with the listeners.\n */\nexport function setupChannelListeners(\n  instance: SocketService,\n  channelId: string,\n) {\n  logger.SocketService(\n    `[SocketService: setupChannelListener()] context=${instance.state.context} setting socket listeners for channel ${channelId}...`,\n  );\n\n  const { socket } = instance.state;\n  const { keyExchange } = instance.state;\n\n  // Only available for the originator -- used for connection recovery\n  if (socket && instance.state.isOriginator) {\n    if (instance.state.debug) {\n      // TODO remove all the handleSocker* functions\n      // They are not required since it is managed via the handleDisconnect function\n      socket?.io.on('error', (error) => {\n        logger.SocketService(\n          `[SocketService: setupChannelListener()] context=${instance.state.context} socket event=error`,\n          error,\n        );\n        // return handleSocketError(instance)(error);\n      });\n\n      socket?.io.on('reconnect', (attempt) => {\n        logger.SocketService(\n          `[SocketService: setupChannelListener()] context=${instance.state.context} socket event=reconnect`,\n          attempt,\n        );\n\n        reconnectSocket(instance).catch((_e) => {\n          // error handled in reconnectSocket\n        });\n      });\n\n      socket?.io.on('reconnect_error', (error) => {\n        logger.SocketService(\n          `[SocketService: setupChannelListener()] context=${instance.state.context} socket event=reconnect_error`,\n          error,\n        );\n        // return handleReconnectError(instance)(error);\n      });\n\n      socket?.io.on('reconnect_failed', () => {\n        logger.SocketService(\n          `[SocketService: setupChannelListener()] context=${instance.state.context} socket event=reconnect_failed`,\n        );\n        // return handleReconnectFailed(instance)();\n      });\n\n      // Keep commented for now, only useful during development\n      // socket?.io.on('ping', () => {\n      //   logger.SocketService(\n      //     `[SocketService: setupChannelListener()] 'ping' context=${instance.state.context} socket`,\n      //   );\n      //   // return handlePing(instance)();\n      // });\n    }\n\n    socket?.on('disconnect', (reason: string) => {\n      logger.SocketService(\n        `[SocketService: setupChannelListener()] on 'disconnect' -- MetaMaskSDK socket disconnected '${reason}' begin recovery...`,\n      );\n\n      return handleDisconnect(instance)(reason);\n    });\n  }\n\n  channelEventListenerMap.forEach(({ event, handler }) => {\n    const fullEventName = `${event}-${channelId}`;\n    socket?.on(fullEventName, handler(instance, channelId));\n  });\n\n  keyExchangeEventListenerMap.forEach(({ event, handler }) => {\n    keyExchange?.on(event, handler(instance));\n  });\n\n  instance.state.setupChannelListeners = true;\n}\n", "import { logger } from '../../../utils/logger';\nimport { SocketService } from '../../../SocketService';\nimport { EventType } from '../../../types/EventType';\n\n/**\n * Returns an asynchronous handler function to handle the 'clients_connected' event for a specific channel.\n * This handler informs the other layer about clients reconnection, emits a CLIENTS_CONNECTED event,\n * and handles key exchange scenarios and reconnection situations.\n *\n * @param instance The current instance of the SocketService.\n * @param channelId The ID of the channel associated with the handler.\n * @returns {Function} An asynchronous handler function for the 'clients_connected' event.\n */\nexport function handleClientsConnected(\n  instance: SocketService,\n  channelId: string,\n) {\n  return async (_id: string) => {\n    const relayPersistence =\n      instance.remote.state.channelConfig?.relayPersistence ?? false;\n\n    logger.SocketService(\n      `[SocketService: handleClientsConnected()] context=${\n        instance.state.context\n      } on 'clients_connected-${channelId}' relayPersistence=${relayPersistence} resumed=${\n        instance.state.resumed\n      }  clientsPaused=${\n        instance.state.clientsPaused\n      } keysExchanged=${instance.state.keyExchange?.areKeysExchanged()} isOriginator=${\n        instance.state.isOriginator\n      }`,\n    );\n\n    // Inform other layer of clients reconnection\n    instance.emit(EventType.CLIENTS_CONNECTED, {\n      isOriginator: instance.state.isOriginator,\n      keysExchanged: instance.state.keyExchange?.areKeysExchanged(),\n      context: instance.state.context,\n    });\n\n    if (instance.state.resumed) {\n      if (!instance.state.isOriginator) {\n        // should ask to redo a key exchange because it wasn't paused.\n        logger.SocketService(\n          `[SocketService: handleClientsConnected()] context=${\n            instance.state.context\n          } 'clients_connected' / keysExchanged=${instance.state.keyExchange?.areKeysExchanged()} -- backward compatibility`,\n        );\n\n        instance.state.keyExchange?.start({\n          isOriginator: instance.state.isOriginator ?? false,\n        });\n      }\n      // resumed switched when connection resume.\n      instance.state.resumed = false;\n    } else if (instance.state.clientsPaused) {\n      logger.SocketService(\n        `[SocketService: handleClientsConnected()] 'clients_connected' skip sending originatorInfo on pause`,\n      );\n    } else if (!instance.state.isOriginator) {\n      // Reconnect scenario --- maybe web dapp got refreshed\n      const force = !relayPersistence;\n      logger.SocketService(\n        `[SocketService: handleClientsConnected()] context=${\n          instance.state.context\n        } on 'clients_connected' / keysExchanged=${instance.state.keyExchange?.areKeysExchanged()} -- force=${force} -- backward compatibility`,\n      );\n\n      logger.SocketService(\n        `[SocketService: handleClientsConnected()] context=${\n          instance.state.context\n        } on 'clients_connected' / keysExchanged=${instance.state.keyExchange?.areKeysExchanged()} -- force=${force} -- backward compatibility`,\n      );\n\n      // Add delay in case exchange was already initiated by dapp.\n      // Always request key exchange from wallet since it looks like a reconnection.\n      instance.state.keyExchange?.start({\n        isOriginator: instance.state.isOriginator ?? false,\n        force,\n      });\n    }\n\n    instance.state.clientsConnected = true;\n    instance.state.clientsPaused = false;\n  };\n}\n", "import { logger } from '../../../utils/logger';\nimport { SocketService } from '../../../SocketService';\nimport { EventType } from '../../../types/EventType';\n\n/**\n * Returns a handler function to handle the 'channel_created' event for a specific channel.\n * This handler emits a CHANNEL_CREATED event using the provided SocketService instance and ID.\n *\n * @param instance The current instance of the SocketService.\n * @param channelId The ID of the channel associated with the handler.\n * @returns {Function} A handler function for the 'channel_created' event.\n */\nexport function handleChannelCreated(\n  instance: SocketService,\n  channelId: string,\n) {\n  return (id: string) => {\n    logger.SocketService(\n      `[SocketService: handleChannelCreated()] context=${instance.state.context} on 'channel_created-${channelId}'`,\n      id,\n    );\n    instance.emit(EventType.CHANNEL_CREATED, id);\n  };\n}\n", "import { logger } from '../../../utils/logger';\nimport { SocketService } from '../../../SocketService';\nimport { EventType } from '../../../types/EventType';\n\n/**\n * Returns a handler function to handle the 'clients_disconnected' event.\n * This handler updates the clientsConnected state to false, and if the instance is the originator\n * and clients are not paused, it cleans the key exchange.\n * It also emits the CLIENTS_DISCONNECTED event.\n *\n * @param instance The current instance of the SocketService.\n * @param channelId The ID of the channel.\n * @returns {Function} A handler function for the 'clients_disconnected' event.\n */\nexport function handlesClientsDisconnected(\n  instance: SocketService,\n  channelId: string,\n) {\n  return () => {\n    instance.state.clientsConnected = false;\n    logger.SocketService(\n      `[SocketService: handlesClientsDisconnected()] context=${instance.state.context} on 'clients_disconnected-${channelId}'`,\n    );\n\n    if (instance.remote.state.relayPersistence) {\n      logger.SocketService(\n        `[SocketService: handlesClientsDisconnected()] context=${instance.state.context} on 'clients_disconnected-${channelId}' - relayPersistence enabled, skipping key exchange cleanup.`,\n      );\n      return;\n    }\n\n    if (instance.state.isOriginator && !instance.state.clientsPaused) {\n      // If it wasn't paused - need to reset keys.\n      instance.state.keyExchange?.clean();\n    }\n\n    instance.emit(EventType.CLIENTS_DISCONNECTED, channelId);\n  };\n}\n", "import { SocketService } from '../../../SocketService';\nimport { EventType } from '../../../types/EventType';\nimport { KeyExchangeMessageType } from '../../../types/KeyExchangeMessageType';\nimport { MessageType } from '../../../types/MessageType';\nimport { logger } from '../../../utils/logger';\n\nexport function handleChannelConfig(\n  instance: SocketService,\n  channelId: string,\n) {\n  return async (config: { persistence: boolean; walletKey: string }) => {\n    logger.SocketService(\n      `[SocketService: handleChannelConfig()] update relayPersistence on 'config-${channelId}'`,\n      config,\n    );\n\n    const { persistence, walletKey } = config;\n\n    if (instance.state.isOriginator && instance.remote.state.channelConfig) {\n      if (config.walletKey && !instance.remote.state.channelConfig.otherKey) {\n        logger.SocketService(`Setting wallet key ${walletKey}`);\n        instance.remote.state.channelConfig.otherKey = walletKey;\n        instance.getKeyExchange().setOtherPublicKey(config.walletKey);\n        instance.state.keyExchange?.setKeysExchanged(true);\n        await instance.remote.sendMessage({\n          type: KeyExchangeMessageType.KEY_HANDSHAKE_ACK,\n        });\n\n        await instance.remote.sendMessage({\n          type: MessageType.PING,\n        });\n\n        await instance.remote.state.storageManager?.persistChannelConfig(\n          instance.remote.state.channelConfig,\n        );\n      }\n\n      if (\n        persistence === true &&\n        !instance.remote.state.channelConfig.relayPersistence\n      ) {\n        logger.SocketService(`Setting relay persistence ${persistence}`);\n        instance.remote.state.channelConfig.relayPersistence = persistence;\n\n        instance.remote.state.relayPersistence = true;\n        instance.remote.emit(EventType.CHANNEL_PERSISTENCE);\n\n        // Force authorized as we have wallet key\n        instance.remote.state.authorized = true;\n        instance.remote.state.ready = true;\n        instance.remote.emit(EventType.AUTHORIZED);\n\n        await instance.remote.state.storageManager?.persistChannelConfig(\n          instance.remote.state.channelConfig,\n        );\n      }\n    } else if (!instance.state.isOriginator) {\n      if (config.persistence) {\n        instance.remote.state.relayPersistence = true;\n        instance.remote.emit(EventType.CHANNEL_PERSISTENCE);\n      }\n    }\n  };\n}\n", "import packageJson from '../../../../package.json';\nimport { SendAnalytics } from '../../../Analytics';\nimport { SocketService } from '../../../SocketService';\nimport { EventType } from '../../../types/EventType';\nimport { InternalEventType } from '../../../types/InternalEventType';\nimport { KeyExchangeMessageType } from '../../../types/KeyExchangeMessageType';\nimport { MessageType } from '../../../types/MessageType';\nimport { TrackingEvents } from '../../../types/TrackingEvent';\nimport { logger } from '../../../utils/logger';\nimport { lcLogguedRPCs } from '../MessageHandlers';\n\n/**\n * Returns a handler function to handle incoming messages.\n * This handler processes the incoming message based on its type and key exchange status.\n *\n * @param instance The current instance of the SocketService.\n * @param channelId The ID of the channel the message belongs to.\n * @returns {Function} A handler function for incoming messages.\n */\nexport function handleMessage(instance: SocketService, channelId: string) {\n  return (rawMsg: {\n    ackId?: string;\n    message: string | { type: string; [key: string]: any };\n    error?: any;\n  }) => {\n    const { ackId, message, error } = rawMsg;\n    const relayPersistence = instance.remote.state.relayPersistence ?? false;\n\n    logger.SocketService(\n      `[SocketService handleMessage()]  relayPersistence=${relayPersistence}  context=${\n        instance.state.context\n      } on 'message' ${channelId} keysExchanged=${instance.state.keyExchange?.areKeysExchanged()}`,\n      rawMsg,\n    );\n\n    if (error) {\n      logger.SocketService(`\n      [SocketService handleMessage()] context=${instance.state.context}::on 'message' error=${error}`);\n\n      throw new Error(error);\n    }\n\n    const isEncryptedMessage = typeof message === 'string';\n\n    if (\n      !isEncryptedMessage &&\n      message?.type === KeyExchangeMessageType.KEY_HANDSHAKE_START\n    ) {\n      if (relayPersistence) {\n        console.warn(\n          `[SocketService handleMessage()] Ignoring key exchange message because relay persistence is activated`,\n          message,\n        );\n        return;\n      }\n\n      logger.SocketService(\n        `[SocketService handleMessage()] context=${instance.state.context}::on 'message' received HANDSHAKE_START isOriginator=${instance.state.isOriginator}`,\n        message,\n      );\n\n      instance.state.keyExchange?.start({\n        isOriginator: instance.state.isOriginator ?? false,\n        force: true,\n      });\n      return;\n    }\n\n    if (!isEncryptedMessage && message?.type?.startsWith('key_handshake')) {\n      if (relayPersistence) {\n        console.warn(\n          `[SocketService handleMessage()] Ignoring key exchange message because relay persistence is activated`,\n          message,\n        );\n        return;\n      }\n\n      logger.SocketService(\n        `[SocketService handleMessage()] context=${instance.state.context}::on 'message' emit KEY_EXCHANGE`,\n        message,\n      );\n\n      instance.emit(InternalEventType.KEY_EXCHANGE, {\n        message,\n        context: instance.state.context,\n      });\n      return;\n    }\n\n    if (isEncryptedMessage && !instance.state.keyExchange?.areKeysExchanged()) {\n      // Sometime the keys exchanged status is not updated correctly\n      // check if we can decrypt the message without errors and if so update the status and continue.\n      let canDecrypt = false;\n      try {\n        logger.SocketService(\n          `[SocketService handleMessage()] context=${instance.state.context}::on 'message' trying to decrypt message`,\n        );\n        instance.state.keyExchange?.decryptMessage(message);\n        canDecrypt = true;\n      } catch (err) {\n        // Ignore error.\n        logger.SocketService(\n          `[SocketService handleMessage()] context=${instance.state.context}::on 'message' error`,\n          err,\n        );\n      }\n\n      if (canDecrypt) {\n        logger.SocketService(\n          `Invalid key exchange status detected --- updating it.`,\n        );\n        instance.state.keyExchange?.setKeysExchanged(true);\n      } else {\n        // received encrypted message before keys were exchanged.\n        if (instance.state.isOriginator) {\n          instance.state.keyExchange?.start({\n            isOriginator: instance.state.isOriginator ?? false,\n          });\n        } else {\n          // Request new key exchange\n          instance\n            .sendMessage({\n              type: KeyExchangeMessageType.KEY_HANDSHAKE_START,\n            })\n            .catch((err) => {\n              console.error(\n                `[SocketService handleMessage()] context=${instance.state.context}::on 'message' error`,\n                err,\n              );\n            });\n        }\n\n        //  ignore message and wait for completion.\n        logger.SocketService(\n          `Message ignored because invalid key exchange status. step=${\n            instance.state.keyExchange?.getKeyInfo().step\n          }`,\n          instance.state.keyExchange?.getKeyInfo(),\n          message,\n        );\n        return;\n      }\n    } else if (!isEncryptedMessage && message?.type) {\n      // Even if keys were exchanged, if the message is not encrypted, emit it.\n      // *** instance is not supposed to happen ***\n      console.warn(\n        `[SocketService handleMessage() ::on 'message' received non encrypted unkwown message`,\n      );\n      instance.emit(EventType.MESSAGE, message);\n      return;\n    }\n\n    if (!isEncryptedMessage) {\n      console.warn(\n        `[SocketService handleMessage() ::on 'message' received unkwown message`,\n        message,\n      );\n      instance.emit(EventType.MESSAGE, message);\n      return;\n    }\n\n    const decryptedMessage =\n      instance.state.keyExchange?.decryptMessage(message);\n    const messageReceived = JSON.parse(decryptedMessage ?? '{}');\n\n    // Acknowledge that the message was received and decryoted\n    if (ackId && ackId?.length > 0) {\n      logger.SocketService(\n        `[SocketService handleMessage()] context=${instance.state.context}::on 'message' ackid=${ackId} channelId=${channelId}`,\n      );\n\n      instance.state.socket?.emit(EventType.MESSAGE_ACK, {\n        ackId,\n        channelId,\n        clientType: instance.state.isOriginator ? 'dapp' : 'wallet',\n      });\n    }\n\n    if (messageReceived?.type === MessageType.PAUSE) {\n      /**\n       * CommunicationLayer shouldn't be aware of the protocol details but we make an exception to manager session persistence.\n       * Receiving pause is the correct way to quit MetaMask app,\n       * but in case it is killed we won't receive a PAUSE signal and thus need to re-create the handshake.\n       */\n      instance.state.clientsPaused = true;\n    } else {\n      instance.state.clientsPaused = false;\n    }\n\n    if (instance.state.isOriginator && messageReceived.data) {\n      // inform cache from result\n      const rpcMessage = messageReceived.data as {\n        id: string;\n        result: unknown;\n        error: {\n          code: number;\n          message: string;\n          stack: string;\n        };\n      };\n      const initialRPCMethod = instance.state.rpcMethodTracker[rpcMessage.id];\n\n      if (initialRPCMethod) {\n        const elapsedTime = Date.now() - initialRPCMethod.timestamp;\n        logger.SocketService(\n          `[SocketService handleMessage()] context=${instance.state.context}::on 'message' received answer for id=${rpcMessage.id} method=${initialRPCMethod.method} responseTime=${elapsedTime}`,\n          messageReceived,\n        );\n\n        // send ack_received tracking message\n        if (\n          instance.remote.state.analytics &&\n          lcLogguedRPCs.includes(initialRPCMethod.method.toLowerCase())\n        ) {\n          SendAnalytics(\n            {\n              id: instance.remote.state.channelId ?? '',\n              event: TrackingEvents.SDK_RPC_REQUEST_DONE,\n              // Do not double send originator info, it should be extracted from cache on server.\n              // Keep below commented out for reference.\n              sdkVersion: instance.remote.state.sdkVersion,\n              commLayerVersion: packageJson.version,\n              ...instance.remote.state.originatorInfo,\n              walletVersion: instance.remote.state.walletInfo?.version,\n              params: {\n                method: initialRPCMethod.method,\n                from: 'mobile',\n              },\n            },\n            instance.remote.state.communicationServerUrl,\n          ).catch((err) => {\n            console.error(`Cannot send analytics`, err);\n          });\n        }\n        const rpcResult = {\n          ...initialRPCMethod,\n          result: rpcMessage.result,\n          error: rpcMessage.error\n            ? {\n                code: rpcMessage.error?.code,\n                message: rpcMessage.error?.message,\n              }\n            : undefined,\n          elapsedTime,\n        };\n        instance.state.rpcMethodTracker[rpcMessage.id] = rpcResult;\n        instance.emit(EventType.RPC_UPDATE, rpcResult);\n      }\n    }\n\n    instance.emit(EventType.MESSAGE, { message: messageReceived });\n  };\n}\n", "import { SendAnalytics } from '../../../Analytics';\nimport { SocketService } from '../../../SocketService';\nimport { ConnectionStatus } from '../../../types/ConnectionStatus';\nimport { EventType } from '../../../types/EventType';\nimport { TrackingEvents } from '../../../types/TrackingEvent';\nimport { logger } from '../../../utils/logger';\n\nimport packageJson from '../../../../package.json';\n\n/**\n * Returns an asynchronous handler function to handle the 'reject' event for a specific channel.\n *\n * @param instance The current instance of the SocketService.\n * @param channelId The ID of the channel associated with the handler.\n * @returns {Function} An asynchronous handler function for the 'clients_connected' event.\n */\nexport function handleChannelRejected(\n  instance: SocketService,\n  channelId: string,\n) {\n  return async (_id: string) => {\n    // Only valid if connection hasn't been ready\n    if (!instance.state.isOriginator || instance.remote.state.ready) {\n      logger.SocketService(\n        `[SocketService: handleChannelRejected()] SKIP -- channelId=${channelId} isOriginator=${instance.state.isOriginator} ready=${instance.remote.state.ready}`,\n      );\n      return;\n    }\n\n    logger.SocketService(\n      `[SocketService: handleChannelRejected()] context=${instance.state.context} channelId=${channelId} isOriginator=${instance.state.isOriginator} ready=${instance.remote.state.ready}`,\n      instance.remote.state.originatorInfo,\n    );\n\n    // Emit analytics event\n    SendAnalytics(\n      {\n        id: channelId,\n        event: TrackingEvents.REJECTED,\n        ...instance.remote.state.originatorInfo,\n        sdkVersion: instance.remote.state.sdkVersion,\n        commLayer: instance.state.communicationLayerPreference,\n        commLayerVersion: packageJson.version,\n        walletVersion: instance.remote.state.walletInfo?.version,\n      },\n      instance.remote.state.communicationServerUrl,\n    ).catch((error) => {\n      console.error(\n        `handleChannelRejected:: Error emitting analytics event`,\n        error,\n      );\n    });\n\n    // Terminate the channel\n    await instance.remote.disconnect({ terminate: true });\n    instance.remote.emit(EventType.REJECTED, { channelId });\n    instance.remote.setConnectionStatus(ConnectionStatus.DISCONNECTED);\n  };\n}\n", "import { logger } from '../../../utils/logger';\nimport { SocketService } from '../../../SocketService';\nimport { EventType } from '../../../types/EventType';\n\n/**\n * Returns a handler function to handle the 'clients_waiting_to_join' event for a specific channel.\n * This handler emits a CLIENTS_WAITING event with the number of waiting users.\n *\n * @param instance The current instance of the SocketService.\n * @param channelId The ID of the channel associated with the handler.\n * @returns {Function} A handler function for the 'clients_waiting_to_join' event.\n */\nexport function handleClientsWaitingToJoin(\n  instance: SocketService,\n  channelId: string,\n) {\n  return (numberUsers: number) => {\n    logger.SocketService(\n      `[SocketService: handleClientsWaitingToJoin()] context=${instance.state.context} on 'clients_waiting_to_join-${channelId}'`,\n      numberUsers,\n    );\n\n    instance.emit(EventType.CLIENTS_WAITING, numberUsers);\n  };\n}\n", "import { logger } from '../../../utils/logger';\nimport { SocketService } from '../../../SocketService';\nimport { EventType } from '../../../types/EventType';\n\n/**\n * Returns a handler function to handle the 'KEY_INFO' event.\n * This handler emits the KEY_INFO event with the provided event data.\n *\n * @param instance The current instance of the SocketService.\n * @param {any} event The event data for the 'KEY_INFO' event.\n * @returns {Function} A handler function for the 'KEY_INFO' event.\n */\nexport function handleKeyInfo(instance: SocketService) {\n  return (event: any) => {\n    logger.SocketService(\n      `[SocketService: handleKeyInfo()] on 'KEY_INFO'`,\n      event,\n    );\n\n    instance.emit(EventType.KEY_INFO, event);\n  };\n}\n", "import { SocketService } from '../../../SocketService';\nimport { EventType } from '../../../types/EventType';\nimport { ServiceStatus } from '../../../types/ServiceStatus';\nimport { logger } from '../../../utils/logger';\n\n/**\n * Returns a handler function to handle the 'keys_exchanged' event.\n * This handler emits the KEYS_EXCHANGED event with the current key exchange status and whether the instance is the originator.\n * Additionally, it emits the SERVICE_STATUS event with the current key information.\n *\n * @param instance The current instance of the SocketService.\n * @returns {Function} A handler function for the 'keys_exchanged' event.\n */\nexport function handleKeysExchanged(instance: SocketService) {\n  return () => {\n    logger.SocketService(\n      `[SocketService: handleKeysExchanged()] on 'keys_exchanged' keyschanged=${instance.state.keyExchange?.areKeysExchanged()}`,\n    );\n\n    // Persist the new channel config\n    const { channelConfig } = instance.remote.state;\n\n    if (channelConfig) {\n      const eciesState = instance.getKeyExchange().getKeyInfo().ecies;\n      channelConfig.localKey = eciesState.private;\n      channelConfig.otherKey = eciesState.otherPubKey;\n      instance.remote.state.channelConfig = channelConfig;\n      instance.remote.state.storageManager\n        ?.persistChannelConfig(channelConfig)\n        .catch((error) => {\n          console.error(`Error persisting channel config`, error);\n        });\n    }\n\n    // Propagate key exchange event\n    instance.emit(EventType.KEYS_EXCHANGED, {\n      keysExchanged: instance.state.keyExchange?.areKeysExchanged(),\n      isOriginator: instance.state.isOriginator,\n    });\n    const serviceStatus: ServiceStatus = {\n      keyInfo: instance.getKeyInfo(),\n    };\n    instance.emit(EventType.SERVICE_STATUS, serviceStatus);\n  };\n}\n", "import { SocketService } from '../../../SocketService';\nimport { EventType } from '../../../types/EventType';\nimport { logger } from '../../../utils/logger';\nimport { reconnectSocket } from '../ConnectionManager/reconnectSocket';\n\n/**\n * Returns a handler function to handle the 'disconnect' event.\n * This handler checks whether the disconnection was manual or due to other reasons.\n * If it wasn't a manual disconnect, it emits the SOCKET_DISCONNECTED event and attempts to reconnect.\n *\n * @param instance The current instance of the SocketService.\n * @returns {Function} A handler function for the 'disconnect' event.\n */\nexport function handleDisconnect(instance: SocketService) {\n  return (reason: string) => {\n    logger.SocketService(\n      `[SocketService: handleDisconnect()] on 'disconnect' manualDisconnect=${instance.state.manualDisconnect}`,\n      reason,\n    );\n\n    if (!instance.state.manualDisconnect) {\n      instance.emit(EventType.SOCKET_DISCONNECTED);\n      reconnectSocket(instance).catch((err) => {\n        console.error(\n          `SocketService::handleDisconnect Error reconnecting socket`,\n          err,\n        );\n      });\n    }\n  };\n}\n", "import debug from 'debug';\nimport { EventEmitter2 } from 'eventemitter2';\nimport { io, ManagerOptions, Socket, SocketOptions } from 'socket.io-client';\nimport { DEFAULT_SERVER_URL, DEFAULT_SOCKET_TRANSPORTS } from './config';\nimport { ECIES, ECIESProps } from './ECIES';\nimport { KeyExchange, KeyExchangeProps } from './KeyExchange';\nimport { RemoteCommunication } from './RemoteCommunication';\nimport { createChannel } from './services/SocketService/ChannelManager';\nimport {\n  connectToChannel,\n  disconnect,\n  pause,\n  ping,\n  resume,\n  setupSocketFocusListener,\n} from './services/SocketService/ConnectionManager';\nimport { keyCheck, resetKeys } from './services/SocketService/KeysManager';\nimport { handleSendMessage } from './services/SocketService/MessageHandlers';\nimport { CommunicationLayerMessage } from './types/CommunicationLayerMessage';\nimport { CommunicationLayerPreference } from './types/CommunicationLayerPreference';\nimport { ConnectToChannelOptions } from './types/ConnectToChannelOptions';\nimport { DisconnectOptions } from './types/DisconnectOptions';\nimport { KeyInfo } from './types/KeyInfo';\nimport { CommunicationLayerLoggingOptions } from './types/LoggingOptions';\nimport { logger } from './utils/logger';\n\nexport interface SocketServiceProps {\n  communicationLayerPreference: CommunicationLayerPreference;\n  reconnect?: boolean;\n  transports?: string[];\n  otherPublicKey?: string;\n  communicationServerUrl: string;\n  context: string;\n  ecies?: ECIESProps;\n  remote: RemoteCommunication;\n  logging?: CommunicationLayerLoggingOptions;\n}\n\nexport interface SocketServiceState {\n  clientsConnected: boolean;\n  clientsPaused: boolean;\n  isOriginator?: boolean;\n  channelId?: string;\n  manualDisconnect: boolean;\n  resumed?: boolean;\n  communicationLayerPreference?: CommunicationLayerPreference;\n  context?: string;\n  eciesInstance?: ECIES;\n  withKeyExchange?: boolean;\n  communicationServerUrl: string;\n  debug?: boolean;\n  rpcMethodTracker: RPCMethodCache;\n  lastRpcId?: string;\n  hasPlaintext: boolean;\n  socket?: Socket;\n  setupChannelListeners?: boolean;\n  analytics?: boolean;\n  keyExchange?: KeyExchange;\n  focusListenerAdded: boolean;\n  removeFocusListener?: () => void;\n  isReconnecting: boolean;\n  reconnectionAttempts: number;\n}\n\nexport interface RPCMethodResult {\n  id: string;\n  timestamp: number; // timestamp of last request\n  method: string;\n  result?: unknown;\n  error?: unknown;\n  elapsedTime?: number; // elapsed time between request and response\n}\nexport interface RPCMethodCache {\n  [id: string]: RPCMethodResult;\n}\n\nexport class SocketService extends EventEmitter2 {\n  public state: SocketServiceState = {\n    clientsConnected: false,\n    /**\n     * Special flag used to session persistence in case MetaMask disconnects without Pause,\n     * it means we need to re-create a new key handshake.\n     */\n    clientsPaused: false,\n    manualDisconnect: false,\n    lastRpcId: undefined,\n    rpcMethodTracker: {},\n    hasPlaintext: false,\n    communicationServerUrl: '',\n    focusListenerAdded: false,\n    removeFocusListener: undefined,\n    isReconnecting: false,\n    reconnectionAttempts: 0,\n  };\n\n  remote: RemoteCommunication;\n\n  options: SocketServiceProps;\n\n  constructor(options: SocketServiceProps) {\n    super();\n\n    this.options = options;\n    const {\n      reconnect,\n      communicationLayerPreference,\n      communicationServerUrl,\n      context,\n      remote,\n      logging,\n    } = options;\n\n    this.state.resumed = reconnect;\n    this.state.context = context;\n    this.state.isOriginator = remote.state.isOriginator;\n    this.state.communicationLayerPreference = communicationLayerPreference;\n    this.state.debug = logging?.serviceLayer === true;\n    this.remote = remote;\n\n    if (logging?.serviceLayer === true) {\n      debug.enable('SocketService:Layer');\n    }\n\n    this.state.communicationServerUrl = communicationServerUrl;\n    this.state.hasPlaintext =\n      this.state.communicationServerUrl !== DEFAULT_SERVER_URL &&\n      logging?.plaintext === true;\n\n    logger.SocketService(\n      `[SocketService: constructor()] Socket IO url: ${this.state.communicationServerUrl}`,\n    );\n\n    this.initSocket();\n  }\n\n  initSocket() {\n    const { otherPublicKey, ecies, logging } = this.options;\n    const socketOptions: Partial<ManagerOptions & SocketOptions> = {\n      autoConnect: false,\n      transports: DEFAULT_SOCKET_TRANSPORTS,\n      withCredentials: true,\n    };\n\n    const url = this.state.communicationServerUrl;\n    logger.SocketService(`[SocketService: initSocket()] Socket IO url: ${url}`);\n\n    this.state.socket = io(url, socketOptions);\n\n    // Make sure to always be connected and retrieve messages\n    setupSocketFocusListener(this);\n\n    const keyExchangeInitParameter: KeyExchangeProps = {\n      communicationLayer: this,\n      otherPublicKey,\n      sendPublicKey: false,\n      context: this.state.context ?? '',\n      ecies,\n      logging,\n    };\n\n    this.state.keyExchange = new KeyExchange(keyExchangeInitParameter);\n  }\n\n  resetKeys(): void {\n    return resetKeys(this);\n  }\n\n  async createChannel() {\n    return createChannel(this);\n  }\n\n  connectToChannel({\n    channelId,\n    withKeyExchange = false,\n    authorized,\n  }: ConnectToChannelOptions): Promise<void> {\n    return connectToChannel({\n      options: {\n        channelId,\n        withKeyExchange,\n        authorized,\n      },\n      instance: this,\n    });\n  }\n\n  getKeyInfo(): KeyInfo {\n    return (this.state.keyExchange as KeyExchange).getKeyInfo();\n  }\n\n  keyCheck() {\n    return keyCheck(this);\n  }\n\n  getKeyExchange() {\n    return this.state.keyExchange as KeyExchange;\n  }\n\n  async sendMessage(message: CommunicationLayerMessage): Promise<boolean> {\n    return handleSendMessage(this, message);\n  }\n\n  ping() {\n    return ping(this);\n  }\n\n  pause(): Promise<void> {\n    return pause(this);\n  }\n\n  isConnected() {\n    return this.state.socket?.connected as boolean;\n  }\n\n  resume(): Promise<void> {\n    return resume(this);\n  }\n\n  getRPCMethodTracker() {\n    return this.state.rpcMethodTracker;\n  }\n\n  disconnect(options?: DisconnectOptions): void {\n    return disconnect(this, options);\n  }\n}\n", "import { logger } from '../../../utils/logger';\nimport { SocketService } from '../../../SocketService';\nimport { reconnectSocket } from './reconnectSocket';\n\n/**\n * Checks the focus status of the document and triggers socket reconnection if necessary.\n * If the document has focus, it immediately calls the reconnectSocket function to attempt reconnection.\n * If the document doesn't have focus, it sets up a focus event listener to trigger reconnection once the document regains focus.\n * The focus event listener is added only once per SocketService instance.\n *\n * @param instance The current instance of the SocketService.\n */\nexport function setupSocketFocusListener(instance: SocketService) {\n  if (typeof window === 'undefined' || typeof document === 'undefined') {\n    return;\n  }\n\n  logger.SocketService(\n    `[SocketService: setupSocketFocusListener()] hasFocus=${document.hasFocus()}`,\n    instance,\n  );\n\n  // Check if we've already added a focus listener for this instance\n  if (!instance.state.focusListenerAdded) {\n    const focusHandler = () => {\n      logger.SocketService(`Document has focus --- reconnecting socket`);\n      reconnectSocket(instance).catch((err) => {\n        console.error(\n          `setupSocketFocusListeners Error reconnecting socket`,\n          err,\n        );\n      });\n    };\n\n    window.addEventListener('focus', focusHandler);\n\n    // Mark that we've added the listener for this instance\n    instance.state.focusListenerAdded = true;\n\n    // Optionally, add a method to remove the listener if needed\n    instance.state.removeFocusListener = () => {\n      window.removeEventListener('focus', focusHandler);\n      instance.state.focusListenerAdded = false;\n    };\n  }\n}\n", "import { logger } from '../../../utils/logger';\nimport { SocketService } from '../../../SocketService';\n\n/**\n * Resets the keys associated with a SocketService instance.\n * If debugging is enabled, a debug message is logged.\n *\n * @param instance The current instance of the SocketService.\n */\nexport function resetKeys(instance: SocketService) {\n  logger.SocketService(`[SocketService: resetKeys()] Resetting keys.`);\n\n  instance.state.keyExchange?.resetKeys();\n}\n", "import { v4 as uuidv4 } from 'uuid';\nimport { SocketService } from '../../../SocketService';\nimport { EventType } from '../../../types/EventType';\nimport { logger } from '../../../utils/logger';\nimport {\n  handleJoinChannelR<PERSON>ults,\n  JoinChannelResult,\n} from '../ConnectionManager/handleJoinChannelResult';\nimport { Channel } from '../../../types/Channel';\nimport { setupChannelListeners } from './setupChannelListeners';\n\n/**\n * Creates a new communication channel for a given SocketService instance.\n * If debugging is enabled, logs the creation process. If the socket is not\n * connected, it initiates a connection. The function also sets up listeners\n * for the new channel and emits a JOIN_CHANNEL event.\n *\n * @param instance The current instance of the SocketService.\n * @returns {Object} An object containing the newly generated channel ID and\n * the public key associated with the instance, if available.\n * @property {string} channelId The unique identifier for the newly created channel.\n * @property {string} pubKey The public key associated with the SocketService\n * instance, or an empty string if not available.\n */\nexport async function createChannel(instance: SocketService): Promise<Channel> {\n  logger.SocketService(\n    `[SocketService: createChannel()] context=${instance.state.context}`,\n  );\n\n  if (!instance.state.socket) {\n    instance.initSocket();\n  }\n\n  if (instance.state.socket?.connected) {\n    console.error(`[SocketService: createChannel()] socket already connected`);\n    throw new Error(`socket already connected`);\n  }\n\n  instance.state.socket?.connect();\n  instance.state.manualDisconnect = false;\n  instance.state.isOriginator = true;\n  const channelId = uuidv4();\n  instance.state.channelId = channelId;\n  setupChannelListeners(instance, channelId);\n  await new Promise<void>((resolve, reject) => {\n    instance.state.socket?.emit(\n      EventType.JOIN_CHANNEL,\n      {\n        channelId,\n        context: `${instance.state.context}createChannel`,\n        clientType: 'dapp', // only dapp can create channel\n      },\n      async (error: string | null, result?: JoinChannelResult) => {\n        try {\n          await handleJoinChannelResults(instance, error, result);\n          resolve();\n        } catch (runtimeError) {\n          reject(runtimeError);\n        }\n      },\n    );\n  });\n  const keyInfo = instance.state.keyExchange?.getKeyInfo();\n  return {\n    channelId,\n    pubKey: keyInfo?.ecies.public || '',\n    privKey: keyInfo?.ecies.private || '',\n  };\n}\n", "// packages/sdk-communication-layer/src/services/SocketService/ConnectionManager/connectToChannel.ts\nimport { SocketService } from '../../../SocketService';\nimport { ConnectToChannelOptions } from '../../../types/ConnectToChannelOptions';\nimport { EventType } from '../../../types/EventType';\nimport { setupChannelListeners } from '../ChannelManager';\nimport {\n  handleJoinChannelResults,\n  JoinChannelResult,\n} from './handleJoinChannelResult';\n\n/**\n * Connects a SocketService instance to a specified channel.\n * If the socket is already connected, an error is thrown.\n * The function sets up listeners for the channel and emits a JOIN_CHANNEL event.\n *\n * @param options The options required to connect to the channel,\n * including the channel ID, whether a key exchange is needed,\n * and if the current instance is the originator.\n * @param instance The current instance of the SocketService.\n * @throws {Error} Throws an error if the socket is already connected.\n */\nexport async function connectToChannel({\n  options,\n  instance,\n}: {\n  options: ConnectToChannelOptions;\n  instance: SocketService;\n}): Promise<void> {\n  const { channelId, authorized, withKeyExchange } = options;\n  const { state, remote } = instance;\n  const { isOriginator = false, socket, keyExchange } = state;\n  const { channelConfig } = remote.state;\n\n  if (socket?.connected) {\n    console.error(\n      `[SocketService: connectToChannel()] socket already connected`,\n    );\n    throw new Error(`socket already connected`);\n  }\n\n  if (isOriginator && channelConfig?.relayPersistence) {\n    const { localKey, otherKey } = channelConfig;\n    if (localKey && otherKey) {\n      keyExchange?.setRelayPersistence({ localKey, otherKey });\n    } else {\n      console.warn(`Missing keys in relay persistence`, channelConfig);\n    }\n  }\n\n  Object.assign(state, {\n    manualDisconnect: false,\n    withKeyExchange,\n    isOriginator,\n    channelId,\n  });\n\n  socket?.connect();\n  setupChannelListeners(instance, channelId);\n\n  if (!isOriginator && authorized) {\n    keyExchange?.setKeysExchanged(true);\n    Object.assign(remote.state, { ready: true, authorized: true });\n  }\n\n  return new Promise((resolve) => {\n    const publicKey = keyExchange?.getKeyInfo()?.ecies.public;\n    const withWalletKey = authorized && !isOriginator ? publicKey : undefined;\n\n    socket?.emit(\n      EventType.JOIN_CHANNEL,\n      {\n        channelId,\n        context: `${state.context}_connectToChannel`,\n        clientType: isOriginator ? 'dapp' : 'wallet',\n        publicKey: withWalletKey,\n      },\n      async (error: string | null, result?: JoinChannelResult) => {\n        await handleJoinChannelResults(instance, error, result);\n        resolve();\n      },\n    );\n  });\n}\n", "import { SocketService } from '../../../SocketService';\nimport { EventType } from '../../../types/EventType';\nimport { KeyExchangeMessageType } from '../../../types/KeyExchangeMessageType';\n\n/**\n * Checks the validity of the keys associated with a SocketService instance.\n * A KEY_HANDSHAKE_CHECK message is emitted to the socket, containing\n * the instance's channel ID and public key. This message can be used to verify\n * the validity of the keys on the other side of the communication.\n *\n * @param instance The current instance of the SocketService.\n */\nexport function keyCheck(instance: SocketService) {\n  instance.state.socket?.emit(EventType.MESSAGE, {\n    id: instance.state.channelId,\n    context: instance.state.context,\n    message: {\n      type: KeyExchangeMessageType.KEY_HANDSHAKE_CHECK,\n      pubkey: instance.getKeyInfo().ecies.otherPubKey,\n    },\n  });\n}\n", "import { SocketService } from '../../../SocketService';\nimport { MessageType } from '../../../types/MessageType';\nimport { logger } from '../../../utils/logger';\n\n/**\n * Sends a PING message using a SocketService instance.\n * If the instance is not the originator and keys have been exchanged,\n * a READY message is sent. If the keys haven't been exchanged,\n * the key exchange process is initiated. Finally, a PING message\n * is emitted to the socket.\n *\n * @param instance The current instance of the SocketService.\n */\nexport async function ping(instance: SocketService) {\n  logger.SocketService(\n    `[SocketService: ping()] context=${instance.state.context} originator=${\n      instance.state.isOriginator\n    } keysExchanged=${instance.state.keyExchange?.areKeysExchanged()}`,\n  );\n\n  instance.state.socket?.emit(MessageType.PING, {\n    id: instance.state.channelId,\n    context: 'ping',\n    clientType: instance.remote.state.isOriginator ? 'dapp' : 'wallet',\n    message: '',\n  });\n}\n", "import { logger } from '../../../utils/logger';\nimport { SocketService } from '../../../SocketService';\nimport { MessageType } from '../../../types/MessageType';\n\n/**\n * Pauses the connection of a SocketService instance.\n * If the keys have been exchanged, a PAUSE message is sent before\n * the socket is manually disconnected.\n *\n * @param instance The current instance of the SocketService.\n */\nexport async function pause(instance: SocketService) {\n  logger.SocketService(\n    `[SocketService: pause()] context=${instance.state.context}`,\n  );\n\n  instance.state.manualDisconnect = true;\n  if (instance.state.keyExchange?.areKeysExchanged()) {\n    await instance.sendMessage({ type: MessageType.PAUSE });\n  }\n  instance.state.socket?.disconnect();\n}\n", "import { logger } from '../../../utils/logger';\nimport { SocketService } from '../../../SocketService';\nimport { EventType } from '../../../types/EventType';\nimport { MessageType } from '../../../types/MessageType';\nimport {\n  handleJoinChannelR<PERSON>ults,\n  JoinChannelResult,\n} from './handleJoinChannelResult';\n\n/**\n * Resumes the connection of a SocketService instance.\n * If the socket is already connected, a debug message is logged.\n * Otherwise, the socket is connected and a JOIN_CHANNEL event is emitted.\n * The function also ensures that the necessary key exchanges are\n * performed before resuming the connection. If keys have been exchanged\n * and the instance is not the originator, a READY message is sent.\n *\n * @param instance The current instance of the SocketService.\n */\nexport async function resume(instance: SocketService): Promise<void> {\n  const { state, remote } = instance;\n  const { socket, channelId, context, keyExchange, isOriginator } = state;\n  const { isOriginator: remoteIsOriginator } = remote.state;\n\n  logger.SocketService(\n    `[SocketService: resume()] channelId=${channelId} context=${context} connected=${\n      socket?.connected\n    } manualDisconnect=${state.manualDisconnect} resumed=${\n      state.resumed\n    } keysExchanged=${keyExchange?.areKeysExchanged()}`,\n  );\n\n  if (!channelId) {\n    logger.SocketService(`[SocketService: resume()] channelId is not defined`);\n    throw new Error('ChannelId is not defined');\n  }\n\n  if (socket?.connected) {\n    logger.SocketService(`[SocketService: resume()] already connected.`);\n    socket.emit(MessageType.PING, {\n      id: channelId,\n      clientType: remoteIsOriginator ? 'dapp' : 'wallet',\n      context: 'on_channel_config',\n      message: '',\n    });\n\n    if (!remote.hasRelayPersistence() && !keyExchange?.areKeysExchanged()) {\n      // Always try to recover key exchange from both side (wallet / dapp)\n      if (isOriginator) {\n        await instance.sendMessage({ type: MessageType.READY });\n      } else {\n        keyExchange?.start({ isOriginator: false });\n      }\n    }\n  } else {\n    socket?.connect();\n\n    logger.SocketService(\n      `[SocketService: resume()] after connecting socket --> connected=${socket?.connected}`,\n    );\n\n    socket?.emit(\n      EventType.JOIN_CHANNEL,\n      {\n        channelId,\n        context: `${context}_resume`,\n        clientType: remoteIsOriginator ? 'dapp' : 'wallet',\n      },\n      async (error: string | null, result?: JoinChannelResult) => {\n        try {\n          await handleJoinChannelResults(instance, error, result);\n        } catch (runtimeError) {\n          console.warn(`Error reconnecting to channel`, runtimeError);\n        }\n      },\n    );\n  }\n\n  state.manualDisconnect = false;\n  state.resumed = true;\n}\n", "import { SocketService } from '../../../SocketService';\nimport { DisconnectOptions } from '../../../types/DisconnectOptions';\nimport { logger } from '../../../utils/logger';\n\n/**\n * Disconnects a SocketService instance from its current connection.\n * If the termination option is provided, the channel ID is reset and\n * any existing key exchanges are cleaned up. Additionally, the rpcMethodTracker\n * is reset and the socket is manually disconnected.\n *\n * @param instance The current instance of the SocketService.\n * @param options Optional parameters for the disconnect process,\n * including whether to terminate the connection and the channel ID.\n */\nexport function disconnect(\n  instance: SocketService,\n  options?: DisconnectOptions,\n) {\n  logger.SocketService(\n    `[SocketService: disconnect()] context=${instance.state.context}`,\n    options,\n  );\n\n  if (options?.terminate) {\n    instance.state.removeFocusListener?.();\n    instance.state.channelId = options.channelId;\n    instance.state.socket?.removeAllListeners();\n    instance.state.keyExchange?.clean();\n    instance.remote.state.ready = false;\n    instance.state.socket = undefined;\n    // Reset rpcMethodTracker\n    instance.state.rpcMethodTracker = {};\n  }\n\n  instance.state.manualDisconnect = true;\n  instance.state.socket?.disconnect();\n}\n", "export enum PlatformType {\n  // React Native or Nodejs\n  NonBrowser = 'nodejs',\n  // MetaMask Mobile in-app browser\n  MetaMaskMobileWebview = 'in-app-browser',\n  // Desktop Browser\n  DesktopWeb = 'web-desktop',\n  // Mobile Browser\n  MobileWeb = 'web-mobile',\n  // ReactNative\n  ReactNative = 'react-native',\n}\n", "export enum AutoConnectType {\n  RENEW = 'renew',\n  LINK = 'link',\n}\n", "import { logger } from '../../../utils/logger';\nimport { RemoteCommunication } from '../../../RemoteCommunication';\nimport { EventType } from '../../../types/EventType';\nimport { PlatformType } from '../../../types/PlatformType';\nimport { wait } from '../../../utils/wait';\n\n/**\n * Creates and returns an event handler function for the \"AUTHORIZED\" event. The handler function manages the authorization process for the given RemoteCommunication instance.\n *\n * This function performs several tasks:\n * 1. Skips processing if the instance is already authorized.\n * 2. Ensures the wallet version info is available, polling if necessary.\n * 3. Implements backward compatibility for wallets with versions earlier than 7.3. It checks against a hardcoded version to decide whether to proceed with the event handling.\n * 4. Identifies if the platform is considered \"secure\" based on predefined platform types.\n * 5. If on a secure platform, the instance's state is updated to indicate it's authorized and the \"AUTHORIZED\" event is emitted.\n *\n * @param instance The instance of RemoteCommunication to be processed.\n * @returns A function which acts as the event handler for the \"AUTHORIZED\" event.\n */\nexport function handleAuthorizedEvent(instance: RemoteCommunication) {\n  return async () => {\n    const { state } = instance;\n\n    if (state.authorized) {\n      // Ignore duplicate event or already authorized\n      return;\n    }\n\n    // Sometime the wallet version is not yet received upon authorized message\n    const waitForWalletVersion = async () => {\n      while (!state.walletInfo) {\n        await wait(500);\n      }\n    };\n    await waitForWalletVersion();\n\n    // The event might be received twice because of a backward compatibility hack in SocketService.\n    // bacward compatibility for wallet <7.3\n    const compareValue = '7.3'.localeCompare(state.walletInfo?.version || '');\n\n    logger.RemoteCommunication(\n      `[RemoteCommunication: handleAuthorizedEvent()] HACK 'authorized' version=${state.walletInfo?.version} compareValue=${compareValue}`,\n    );\n\n    // FIXME remove this hack pending wallet release 7.3+\n    if (compareValue !== 1) {\n      // ignore for version 7.3+\n      return;\n    }\n\n    const isSecurePlatform =\n      state.platformType === PlatformType.MobileWeb ||\n      state.platformType === PlatformType.ReactNative ||\n      state.platformType === PlatformType.MetaMaskMobileWebview;\n\n    logger.RemoteCommunication(\n      `[RemoteCommunication: handleAuthorizedEvent()] HACK 'authorized' platform=${state.platformType} secure=${isSecurePlatform} channel=${state.channelId} walletVersion=${state.walletInfo?.version}`,\n    );\n\n    if (isSecurePlatform) {\n      // Propagate authorized event.\n      state.authorized = true;\n      instance.emit(EventType.AUTHORIZED);\n    }\n  };\n}\n", "import { logger } from '../../../utils/logger';\nimport { RemoteCommunication } from '../../../RemoteCommunication';\nimport { EventType } from '../../../types/EventType';\n\n/**\n * Creates and returns an event handler function for the \"CHANNEL_CREATED\" event. This handler function manages the channel creation process for a given RemoteCommunication instance.\n *\n * Upon receiving the \"CHANNEL_CREATED\" event:\n * 1. If debugging is enabled, the event details are logged for diagnostics.\n * 2. The \"CHANNEL_CREATED\" event is emitted, passing along the channel ID, to inform other parts of the system that a channel has been successfully created.\n *\n * @param instance The instance of RemoteCommunication to be processed.\n * @returns A function which acts as the event handler for the \"CHANNEL_CREATED\" event.\n */\nexport function handleChannelCreatedEvent(instance: RemoteCommunication) {\n  return (id: string) => {\n    const { state } = instance;\n    logger.RemoteCommunication(\n      `[RemoteCommunication: handleChannelCreatedEvent()] context=${state.context} on 'channel_created' channelId=${id}`,\n    );\n\n    instance.emit(EventType.CHANNEL_CREATED, id);\n  };\n}\n", "import { logger } from '../../../utils/logger';\nimport packageJson from '../../../../package.json';\nimport { SendAnalytics } from '../../../Analytics';\nimport { RemoteCommunication } from '../../../RemoteCommunication';\nimport { CommunicationLayerPreference } from '../../../types/CommunicationLayerPreference';\nimport { EventType } from '../../../types/EventType';\nimport { TrackingEvents } from '../../../types/TrackingEvent';\n\n/**\n * Creates and returns an event handler function for the \"CLIENTS_CONNECTED\" event. This handler function manages the connected clients for a given RemoteCommunication instance.\n *\n * When clients successfully connect:\n * 1. If debugging is enabled, the event details (channel ID and keys exchanged status) are logged for diagnostics.\n * 2. If analytics tracking is enabled, the analytics data is collected and sent to the server. This data includes the SDK version, wallet version, communication layer version, and other relevant information.\n * 3. The state of the RemoteCommunication instance is updated to reflect that clients have successfully connected and that the originator information hasn't been sent yet.\n * 4. The \"CLIENTS_CONNECTED\" event is emitted to inform other parts of the system about the successful connection of clients.\n *\n * @param instance The instance of RemoteCommunication for which the event handler function is being created.\n * @param communicationLayerPreference The preferred communication layer used for this connection.\n * @returns A function that acts as the event handler for the \"CLIENTS_CONNECTED\" event.\n */\nexport function handleClientsConnectedEvent(\n  instance: RemoteCommunication,\n  communicationLayerPreference: CommunicationLayerPreference,\n) {\n  return () => {\n    const { state } = instance;\n    // Propagate the event to manage different loading states on the ui.\n    logger.RemoteCommunication(\n      `[RemoteCommunication: handleClientsConnectedEvent()] on 'clients_connected' channel=${\n        state.channelId\n      } keysExchanged=${state.communicationLayer?.getKeyInfo()?.keysExchanged}`,\n    );\n\n    if (state.analytics) {\n      const requestEvent = state.isOriginator\n        ? TrackingEvents.REQUEST\n        : TrackingEvents.REQUEST_MOBILE;\n      SendAnalytics(\n        {\n          id: state.channelId ?? '',\n          event: state.reconnection ? TrackingEvents.RECONNECT : requestEvent,\n          ...state.originatorInfo,\n          commLayer: communicationLayerPreference,\n          sdkVersion: state.sdkVersion,\n          walletVersion: state.walletInfo?.version,\n          commLayerVersion: packageJson.version,\n        },\n        state.communicationServerUrl,\n      ).catch((err) => {\n        console.error(`Cannot send analytics`, err);\n      });\n    }\n\n    state.clientsConnected = true;\n    state.originatorInfoSent = false; // Always re-send originator info.\n    instance.emit(EventType.CLIENTS_CONNECTED);\n  };\n}\n", "import { RemoteCommunication } from '../../../RemoteCommunication';\nimport { ConnectionStatus } from '../../../types/ConnectionStatus';\nimport { EventType } from '../../../types/EventType';\nimport { logger } from '../../../utils/logger';\n\n/**\n * Creates and returns an event handler function for the \"CLIENTS_DISCONNECTED\" event. This handler function manages the state and operations when clients get disconnected from a RemoteCommunication instance.\n *\n * Upon client disconnection:\n * 1. If debugging is enabled, logs the channel ID associated with the disconnection event for diagnostics.\n * 2. Updates the `RemoteCommunication` instance state to indicate that clients are no longer connected, sets connection status to \"DISCONNECTED\", and resets the \"ready\" and \"authorized\" flags.\n * 3. Emits a \"CLIENTS_DISCONNECTED\" event to notify other parts of the system about the disconnection. This is useful for managing UI states or triggering certain operations.\n * 4. If analytics tracking is enabled and a channel ID is available, sends disconnection analytics data to the server. This data includes the SDK version, wallet version, communication layer version, and other relevant details.\n *\n * @param instance The instance of RemoteCommunication for which the event handler function is being created.\n * @param communicationLayerPreference The preferred communication layer used for this connection.\n * @returns A function that acts as the event handler for the \"CLIENTS_DISCONNECTED\" event, expecting a channel ID as its parameter.\n */\nexport function handleClientsDisconnectedEvent(instance: RemoteCommunication) {\n  return (channelId: string) => {\n    const { state } = instance;\n\n    logger.RemoteCommunication(\n      `[RemoteCommunication: handleClientsDisconnectedEvent()] context=${state.context} on 'clients_disconnected' channelId=${channelId}`,\n    );\n\n    // Ignore status change when relay persistence is available\n    if (!state.relayPersistence) {\n      state.clientsConnected = false;\n      state.ready = false;\n      state.authorized = false;\n    }\n\n    // Propagate the disconnect event to clients.\n    instance.emit(EventType.CLIENTS_DISCONNECTED, state.channelId);\n    instance.setConnectionStatus(ConnectionStatus.DISCONNECTED);\n  };\n}\n", "import { logger } from '../../../utils/logger';\nimport { RemoteCommunication } from '../../../RemoteCommunication';\nimport { ConnectionStatus } from '../../../types/ConnectionStatus';\nimport { EventType } from '../../../types/EventType';\n\n/**\n * <PERSON>reates and returns an event handler function for the \"CLIENTS_WAITING\" event. This handler manages state and operations when clients are in a waiting state within a `RemoteCommunication` instance.\n *\n * Upon client entering the waiting state:\n * 1. If debugging is enabled, logs diagnostic information such as the number of users waiting, current connection readiness, and if the originator connection has started automatically.\n * 2. Updates the `RemoteCommunication` instance state to \"WAITING\".\n * 3. Emits a \"CLIENTS_WAITING\" event to notify other parts of the system about the waiting clients. The number of waiting users is passed as an argument, which can be useful for managing UI states or triggering certain operations.\n * 4. If the originator connection started automatically, a timer is set based on the provided timeout (defaulting to 3 seconds if none is provided). When this timer expires:\n *    a. The connection's status is checked. If it hasn't transitioned to \"ready\", the connection status is updated to \"TIMEOUT\".\n *    b. The timer is cleared to prevent any further actions.\n *\n * @param instance The `RemoteCommunication` instance for which the event handler function is being created.\n * @returns A function that acts as the event handler for the \"CLIENTS_WAITING\" event, expecting the number of waiting users as its parameter.\n */\nexport function handleClientsWaitingEvent(instance: RemoteCommunication) {\n  return (numberUsers: number) => {\n    const { state } = instance;\n\n    logger.RemoteCommunication(\n      `[RemoteCommunication: handleClientsWaitingEvent()] context=${state.context} on 'clients_waiting' numberUsers=${numberUsers} ready=${state.ready} autoStarted=${state.originatorConnectStarted}`,\n    );\n\n    instance.setConnectionStatus(ConnectionStatus.WAITING);\n\n    instance.emit(EventType.CLIENTS_WAITING, numberUsers);\n    if (state.originatorConnectStarted) {\n      logger.RemoteCommunication(\n        `[RemoteCommunication: handleClientsWaitingEvent()] on 'clients_waiting' watch autoStarted=${state.originatorConnectStarted} timeout`,\n        state.autoConnectOptions,\n      );\n\n      const timeout = state.autoConnectOptions?.timeout || 3000;\n      const timeoutId = setTimeout(() => {\n        logger.RemoteCommunication(\n          `[RemoteCommunication: handleClientsWaitingEvent()] setTimeout(${timeout}) terminate channelConfig`,\n          state.autoConnectOptions,\n        );\n        // Cleanup previous channelId\n        // state.storageManager?.terminate();\n        state.originatorConnectStarted = false;\n        if (!state.ready) {\n          instance.setConnectionStatus(ConnectionStatus.TIMEOUT);\n        }\n        clearTimeout(timeoutId);\n      }, timeout);\n    }\n  };\n}\n", "import packageJson from '../../../../package.json';\nimport { SendAnalytics } from '../../../Analytics';\nimport { DEFAULT_SESSION_TIMEOUT_MS } from '../../../config';\nimport { RemoteCommunication } from '../../../RemoteCommunication';\nimport { ChannelConfig } from '../../../types/ChannelConfig';\nimport { CommunicationLayerPreference } from '../../../types/CommunicationLayerPreference';\nimport { ConnectionStatus } from '../../../types/ConnectionStatus';\nimport { MessageType } from '../../../types/MessageType';\nimport { OriginatorInfo } from '../../../types/OriginatorInfo';\nimport { TrackingEvents } from '../../../types/TrackingEvent';\nimport { logger } from '../../../utils/logger';\nimport { setLastActiveDate } from '../StateManger';\n\n/**\n * Creates and returns an event handler function for the \"keys_exchanged\" event. This handler is responsible for managing the state and operations associated with the key exchange process within a `RemoteCommunication` instance.\n *\n * Upon successful key exchange:\n * 1. Diagnostic information is logged if debugging is enabled.\n * 2. If keys have been successfully exchanged, the connection status of the `RemoteCommunication` instance is set to \"LINKED\".\n * 3. The last active date for the instance is updated.\n * 4. Analytics data is sent, if applicable, including details such as the SDK version, communication layer preference, package version, and wallet version.\n * 5. The state variable `isOriginator` is updated based on the incoming message.\n * 6. If the current instance is not the originator:\n *    a. It avoids sending the originator message from the wallet side.\n *    b. A message of type \"READY\" is sent to notify that the connection is ready.\n *    c. The instance's readiness and paused states are updated.\n * 7. For backward compatibility, if the instance is the originator and the originator information has not been sent:\n *    a. A message of type \"ORIGINATOR_INFO\" is sent containing the originator details.\n *    b. The state variable `originatorInfoSent` is updated to indicate that the originator information has been transmitted.\n *\n * @param instance The `RemoteCommunication` instance for which the event handler function is being created.\n * @param communicationLayerPreference The communication layer preference, used for analytics.\n * @returns A function that acts as the event handler for the \"keys_exchanged\" event, expecting a message containing details about the key exchange.\n */\nexport function handleKeysExchangedEvent(\n  instance: RemoteCommunication,\n  communicationLayerPreference: CommunicationLayerPreference,\n) {\n  return (message: {\n    isOriginator: boolean;\n    originatorInfo: OriginatorInfo;\n    originator: OriginatorInfo;\n  }) => {\n    const { state } = instance;\n\n    logger.RemoteCommunication(\n      `[RemoteCommunication: handleKeysExchangedEvent()] context=${state.context} on commLayer.'keys_exchanged' channel=${state.channelId}`,\n      message,\n    );\n\n    if (state.communicationLayer?.getKeyInfo()?.keysExchanged) {\n      // Update channelConfig with the new keys\n      const channelConfig: ChannelConfig = {\n        ...state.channelConfig,\n        channelId: state.channelId ?? '',\n        validUntil:\n          state.channelConfig?.validUntil || DEFAULT_SESSION_TIMEOUT_MS,\n        localKey: state.communicationLayer.getKeyInfo().ecies.private,\n        otherKey: state.communicationLayer.getKeyInfo().ecies.otherPubKey,\n      };\n      state.storageManager\n        ?.persistChannelConfig(channelConfig)\n        .catch((error) => {\n          console.error(`Error persisting channel config`, error);\n        });\n      instance.setConnectionStatus(ConnectionStatus.LINKED);\n    }\n\n    setLastActiveDate(instance, new Date());\n\n    if (state.analytics && state.channelId) {\n      SendAnalytics(\n        {\n          id: state.channelId,\n          event: message.isOriginator\n            ? TrackingEvents.CONNECTED\n            : TrackingEvents.CONNECTED_MOBILE,\n          ...state.originatorInfo,\n          sdkVersion: state.sdkVersion,\n          commLayer: communicationLayerPreference,\n          commLayerVersion: packageJson.version,\n          walletVersion: state.walletInfo?.version,\n        },\n        state.communicationServerUrl,\n      ).catch((err) => {\n        console.error(`Cannot send analytics`, err);\n      });\n    }\n\n    state.isOriginator = message.isOriginator;\n\n    if (!message.isOriginator) {\n      // Don't send originator message from wallet.\n      // Always Tell the DAPP metamask is ready\n      // the dapp will send originator message when receiving ready.\n      state.communicationLayer?.sendMessage({\n        type: MessageType.READY,\n      });\n      state.ready = true;\n      state.paused = false;\n    }\n\n    // Keep sending originator info from this location for backward compatibility\n    if (message.isOriginator && !state.originatorInfoSent) {\n      // Always re-send originator info in case the session was deleted on the wallet\n      state.communicationLayer?.sendMessage({\n        type: MessageType.ORIGINATOR_INFO,\n        originatorInfo: state.originatorInfo,\n        originator: state.originatorInfo,\n      });\n      state.originatorInfoSent = true;\n    }\n  };\n}\n", "import { logger } from '../../../utils/logger';\nimport { RemoteCommunication } from '../../../RemoteCommunication';\nimport { ChannelConfig } from '../../../types/ChannelConfig';\n\n/**\n * Updates the last active date for a given `RemoteCommunication` instance and persists the\n * updated channel configuration.\n *\n * The function creates a new channel configuration (`ChannelConfig`) using the `channelId`\n * and `validUntil` values from the instance's state and the provided `lastActiveDate`.\n * This new configuration is then persisted using the instance's `storageManager`.\n *\n * If `debug` mode is enabled in the instance's state, the function logs the current channel\n * and the provided last active date.\n *\n * @param instance The `RemoteCommunication` instance whose channel configuration is to be updated.\n * @param lastActiveDate The date to set as the last active date.\n */\nexport function setLastActiveDate(\n  instance: RemoteCommunication,\n  lastActiveDate: Date,\n) {\n  const { state } = instance;\n\n  logger.RemoteCommunication(\n    `[RemoteCommunication: setLastActiveDate()] channel=${state.channelId}`,\n    lastActiveDate,\n  );\n\n  const newChannelConfig: ChannelConfig = {\n    ...state.channelConfig,\n    channelId: state.channelId ?? '',\n    validUntil: state.channelConfig?.validUntil ?? 0,\n    relayPersistence: state.relayPersistence,\n    localKey:\n      state.communicationLayer?.state.keyExchange?.getKeyInfo().ecies.private,\n    otherKey:\n      state.communicationLayer?.state.keyExchange?.getKeyInfo().ecies\n        .otherPubKey,\n    lastActive: lastActiveDate.getTime(),\n  };\n  state.storageManager?.persistChannelConfig(newChannelConfig);\n}\n", "import { RemoteCommunication } from '../../../RemoteCommunication';\nimport { CommunicationLayerMessage } from '../../../types/CommunicationLayerMessage';\nimport { onCommunicationLayerMessage } from '../MessageHandlers';\n/**\n * Creates and returns an event handler function for the \"message\" event. This handler processes incoming messages intended for a `RemoteCommunication` instance, ensuring they're formatted correctly and delegating to the appropriate message handlers.\n *\n * @param instance The `RemoteCommunication` instance associated with this event handler.\n * @returns A function that acts as the event handler for the \"message\" event, expecting a message of type `CommunicationLayerMessage`.\n */\nexport function handleMessageEvent(instance: RemoteCommunication) {\n  return (_message: CommunicationLayerMessage) => {\n    let message = _message;\n    // check if message is encapsulated for backward compatibility\n    if (_message.message) {\n      message = message.message as CommunicationLayerMessage;\n    }\n    onCommunicationLayerMessage(message, instance);\n  };\n}\n", "import { logger } from '../../../utils/logger';\nimport { RemoteCommunication } from '../../../RemoteCommunication';\nimport { CommunicationLayerMessage } from '../../../types/CommunicationLayerMessage';\nimport { EventType } from '../../../types/EventType';\nimport { MessageType } from '../../../types/MessageType';\nimport { handleAuthorizedMessage } from './handleAuthorizedMessage';\nimport { handleOriginatorInfoMessage } from './handleOriginatorInfoMessage';\nimport { handleOtpMessage } from './handleOtpMessage';\nimport { handlePauseMessage } from './handlePauseMessage';\nimport { handleReadyMessage } from './handleReadyMessage';\nimport { handleTerminateMessage } from './handleTerminateMessage';\nimport { handleWalletInfoMessage } from './handleWalletInfoMessage';\nimport { handleWalletInitMessage } from './handleWalletInitMessage';\n\n/**\n * Central dispatcher function to handle messages for a `RemoteCommunication` instance.\n *\n * The function takes a message of type `CommunicationLayerMessage` and a `RemoteCommunication` instance\n * and based on the `message.type` decides which specific handler function should be invoked.\n *\n * Steps taken by the function:\n *\n * 1. Logs the incoming message if `debug` mode is enabled.\n * 2. Sets the `ready` status of the instance to `true`.\n * 3. Checks the `message.type` and the `isOriginator` status of the instance to determine the relevant handler.\n * 4. Invokes the specific handler function based on the determined conditions.\n * 5. If the message doesn't match specific criteria, it emits a general `MESSAGE` event.\n *\n * @param message The incoming `CommunicationLayerMessage` that needs to be processed.\n * @param instance The `RemoteCommunication` instance that is the target of the message.\n */\nexport function onCommunicationLayerMessage(\n  message: CommunicationLayerMessage,\n  instance: RemoteCommunication,\n) {\n  const { state } = instance;\n\n  logger.RemoteCommunication(\n    `[RemoteCommunication: onCommunicationLayerMessage()] context=${\n      state.context\n    } on 'message' typeof=${typeof message}`,\n    message,\n  );\n\n  instance.state.ready = true;\n\n  if (!state.isOriginator && message.type === MessageType.ORIGINATOR_INFO) {\n    handleOriginatorInfoMessage(instance, message);\n    return;\n  } else if (state.isOriginator && message.type === MessageType.WALLET_INFO) {\n    handleWalletInfoMessage(instance, message);\n    return;\n  } else if (state.isOriginator && message.type === MessageType.WALLET_INIT) {\n    // receive initial account and chainId\n    handleWalletInitMessage(instance, message).catch((error) => {\n      logger.RemoteCommunication(\n        `[RemoteCommunication: onCommunicationLayerMessage()] error=${error}`,\n      );\n    });\n  } else if (message.type === MessageType.TERMINATE) {\n    handleTerminateMessage(instance).catch((error) => {\n      logger.RemoteCommunication(\n        `[RemoteCommunication: onCommunicationLayerMessage()] error=${error}`,\n      );\n    });\n  } else if (message.type === MessageType.PAUSE) {\n    handlePauseMessage(instance);\n  } else if (message.type === MessageType.READY && state.isOriginator) {\n    handleReadyMessage(instance);\n  } else if (message.type === MessageType.OTP && state.isOriginator) {\n    handleOtpMessage(instance, message);\n    return;\n  } else if (message.type === MessageType.AUTHORIZED && state.isOriginator) {\n    handleAuthorizedMessage(instance);\n  }\n\n  // TODO should it check if only emitting JSON-RPC message?\n  instance.emit(EventType.MESSAGE, message);\n}\n", "import { RemoteCommunication } from '../../../RemoteCommunication';\nimport { CommunicationLayerMessage } from '../../../types/CommunicationLayerMessage';\n\n/**\n * Handles the 'walletInfo' message for a `RemoteCommunication` instance.\n *\n * When a 'walletInfo' message is received, this function is responsible for updating the current `RemoteCommunication`\n * instance's state with the received wallet information and resetting the paused state of the instance.\n *\n * The sequence of actions taken on receiving a 'walletInfo' message is as follows:\n *\n * 1. Update the `walletInfo` property of the instance's state with the `walletInfo` received in the message.\n * 2. Reset the `paused` status of the instance to `false`.\n *\n * Previously, there was some additional code (commented out) that handled a specific backward compatibility scenario.\n * When the wallet version was less than '6.6', an AUTHORIZED event was simulated, ensuring compatibility with older\n * versions. This backward compatibility code has been deprecated and is preserved in comments for reference purposes.\n *\n * @param instance The `RemoteCommunication` instance that needs to be acted upon when a walletInfo message is received.\n * @param message The `CommunicationLayerMessage` object containing the wallet information.\n */\nexport function handleWalletInfoMessage(\n  instance: RemoteCommunication,\n  message: CommunicationLayerMessage,\n) {\n  const { state } = instance;\n\n  state.walletInfo = message.walletInfo;\n  state.paused = false;\n\n  // FIXME Remove comment --- but keep temporarily for reference in case of quick rollback\n  // if ('6.6'.localeCompare(state.walletInfo?.version || '') === 1) {\n  //   // SIMULATE AUTHORIZED EVENT\n  //   // FIXME remove hack as soon as ios release 7.x is out\n  //   state.authorized = true;\n  //   emit(EventType.AUTHORIZED);\n\n  //   if (state.debug) {\n  //     // Check for backward compatibility\n  //     console.debug(\n  //       `wallet version ${state.walletInfo?.version} -- Force simulate AUTHORIZED event`,\n  //       state.walletInfo,\n  //     );\n  //   }\n  // }\n}\n", "import { RemoteCommunication } from '../../../RemoteCommunication';\nimport { CommunicationLayerMessage } from '../../../types/CommunicationLayerMessage';\nimport { EventType } from '../../../types/EventType';\nimport { logger } from '../../../utils/logger';\n\nexport async function handleWalletInitMessage(\n  instance: RemoteCommunication,\n  message: CommunicationLayerMessage,\n) {\n  const { state } = instance;\n\n  if (state.isOriginator) {\n    // Parse account and chainId from the message\n    const data = message.data || {};\n    // check if data contains accounts: string[] and chainId: string\n    if (\n      typeof data === 'object' &&\n      'accounts' in data &&\n      'chainId' in data &&\n      'walletKey' in data\n    ) {\n      try {\n        // Persist channel config\n        const { channelConfig } = instance.state;\n        logger.RemoteCommunication(\n          `WALLET_INIT: channelConfig`,\n          JSON.stringify(channelConfig, null, 2),\n        );\n\n        if (channelConfig) {\n          const accounts = data.accounts as string[];\n          const chainId = data.chainId as string;\n          const walletKey = data.walletKey as string;\n          let deeplinkProtocolAvailable = false;\n          let walletVersion: string | undefined;\n          if ('deeplinkProtocol' in data) {\n            deeplinkProtocolAvailable = Boolean(data.deeplinkProtocol);\n            instance.state.deeplinkProtocolAvailable =\n              deeplinkProtocolAvailable;\n          }\n\n          if ('walletVersion' in data) {\n            walletVersion = data.walletVersion as string;\n          }\n\n          await instance.state.storageManager?.persistChannelConfig({\n            ...channelConfig,\n            otherKey: walletKey,\n            walletVersion,\n            deeplinkProtocolAvailable,\n            relayPersistence: true,\n          });\n\n          await instance.state.storageManager?.persistAccounts(accounts);\n          await instance.state.storageManager?.persistChainId(chainId);\n        }\n\n        instance.emit(EventType.WALLET_INIT, {\n          accounts: data.accounts,\n          chainId: data.chainId,\n        });\n      } catch (error) {\n        console.error('RemoteCommunication::on \"wallet_init\" -- error', error);\n      }\n    } else {\n      console.error(\n        'RemoteCommunication::on \"wallet_init\" -- invalid data format',\n        data,\n      );\n    }\n  }\n}\n", "import { RemoteCommunication } from '../../../RemoteCommunication';\nimport { EventType } from '../../../types/EventType';\nimport { disconnect } from '../ConnectionManager';\n\n/**\n * Handles the 'terminate' message for a `RemoteCommunication` instance.\n *\n * The termination process is initiated when a 'terminate' message is received. The purpose of this function is to ensure\n * that the communication channel is closed and the associated configurations are removed from persistence, specifically\n * when the current instance is the originator of the communication.\n *\n * The sequence of actions taken on receiving a 'terminate' message is as follows:\n *\n * 1. Check if the current instance is the originator. If not, this function will not proceed further.\n * 2. If it is the originator, call the `disconnect` function with options to terminate the channel without sending a\n *    'terminate' message back (to avoid recursive termination).\n * 3. Output a debug message to the console. (Note: The `console.debug()` call seems to be missing its arguments. It\n *    should ideally print a meaningful message regarding the termination process.)\n * 4. Emit a `TERMINATE` event to inform other parts of the system that the channel has been terminated.\n *\n * @param instance The `RemoteCommunication` instance that needs to be acted upon when a terminate message is received.\n */\nexport async function handleTerminateMessage(instance: RemoteCommunication) {\n  const { state } = instance;\n\n  // remove channel config from persistence layer and close active connections.\n  if (state.isOriginator) {\n    await disconnect({\n      options: { terminate: true, sendMessage: false },\n      instance,\n    });\n    instance.emit(EventType.TERMINATE);\n  }\n}\n", "import { RemoteCommunication } from '../../../RemoteCommunication';\nimport { ConnectionStatus } from '../../../types/ConnectionStatus';\n/**\n * Handles the pause message for a `RemoteCommunication` instance.\n *\n * When the system receives a pause message, this function updates the current state of the `RemoteCommunication` instance by:\n *\n * 1. Marking the communication as paused (`state.paused = true`).\n * 2. Updating the connection status to `ConnectionStatus.PAUSED`.\n *\n * The pause functionality can be useful in situations where communication needs to be temporarily halted without terminating the connection. This can be due to various reasons, such as waiting for user input, network interruptions, or other operational considerations.\n *\n * @param instance The `RemoteCommunication` instance whose state needs to be updated in response to a pause message.\n */\nexport function handlePauseMessage(instance: RemoteCommunication) {\n  const { state } = instance;\n\n  state.paused = true;\n  instance.setConnectionStatus(ConnectionStatus.PAUSED);\n}\n", "import { RemoteCommunication } from '../../../RemoteCommunication';\nimport { ConnectionStatus } from '../../../types/ConnectionStatus';\nimport { EventType } from '../../../types/EventType';\n\n/**\n * Handles the 'ready' message for a `RemoteCommunication` instance.\n *\n * When the system receives a 'ready' message, this function performs the following actions:\n *\n * 1. Updates the connection status to `ConnectionStatus.LINKED`, indicating that a successful link has been established.\n * 2. Checks the current paused status. If the system was in a paused state, it keeps track of this status so that subsequent actions can be aware of the resumed state.\n * 3. Resets the paused status to indicate that the communication is no longer paused.\n * 4. Emits a `CLIENTS_READY` event with information about the originator and wallet to notify other parts of the system.\n * 5. If the system was previously in a paused state (resumed), it assumes that the connection is authorized. As a result, the authorized status is set to true, and an `AUTHORIZED` event is emitted.\n *\n * @param instance The `RemoteCommunication` instance whose state needs to be updated in response to a ready message.\n */\nexport function handleReadyMessage(instance: RemoteCommunication) {\n  const { state } = instance;\n\n  instance.setConnectionStatus(ConnectionStatus.LINKED);\n\n  // keep track of resumed state before resetting it and emitting messages\n  // Better to reset the paused status before emitting as otherwise it may interfer.\n  const resumed = state.paused;\n  // Reset paused status\n  state.paused = false;\n\n  instance.emit(EventType.CLIENTS_READY, {\n    isOriginator: state.isOriginator,\n    walletInfo: state.walletInfo,\n  });\n\n  if (resumed) {\n    state.authorized = true;\n    // If connection is resumed, automatically assume authorized.\n    instance.emit(EventType.AUTHORIZED);\n  }\n}\n", "import { RemoteCommunication } from '../../../RemoteCommunication';\nimport { RPC_METHODS } from '../../../config';\nimport { CommunicationLayerMessage } from '../../../types/CommunicationLayerMessage';\nimport { EventType } from '../../../types/EventType';\n\n/**\n * Handles the OTP (One-Time Password) message for a `RemoteCommunication` instance.\n *\n * When a message contains an OTP, this function manages the OTP authentication process and ensures compatibility with older versions of the system. Specifically, it:\n *\n * 1. Emits the received OTP answer, making it available to any interested listeners.\n * 2. Checks the version of the wallet currently communicating with the system.\n *    - If the wallet version is below 6.6, the function triggers the `eth_requestAccounts` RPC call for backward compatibility. This is likely because older wallet versions (below 6.6) required an additional step in the OTP authentication process that newer versions might have eliminated or modified.\n *\n * @param instance The `RemoteCommunication` instance on which the OTP message is processed.\n * @param message The received `CommunicationLayerMessage` containing the OTP answer.\n */\nexport function handleOtpMessage(\n  instance: RemoteCommunication,\n  message: CommunicationLayerMessage,\n) {\n  const { state } = instance;\n\n  // OTP message are ignored on the wallet.\n  instance.emit(EventType.OTP, message.otpAnswer);\n\n  // backward compatibility for wallet <6.6\n  if ('6.6'.localeCompare(state.walletInfo?.version || '') === 1) {\n    console.warn(\n      `RemoteCommunication::on 'otp' -- backward compatibility <6.6 -- triger eth_requestAccounts`,\n    );\n\n    instance.emit(EventType.SDK_RPC_CALL, {\n      method: RPC_METHODS.ETH_REQUESTACCOUNTS,\n      params: [],\n    });\n  }\n}\n", "import { RemoteCommunication } from '../../../RemoteCommunication';\nimport { EventType } from '../../../types/EventType';\n\n/**\n * Processes the authorized message for a `RemoteCommunication` instance.\n *\n * When the `handleAuthorizedMessage` function is invoked, it performs the following actions:\n * 1. It updates the `authorized` property of the `RemoteCommunication` instance state to true. This marks the instance as authorized.\n * 2. Emits an `AUTHORIZED` event, which can be listened to by other parts of the application. This event notifies listeners that the instance has successfully been authorized.\n *\n * This function is typically used when the application receives an authorization confirmation, indicating that the necessary authentication and authorization checks have been successfully completed.\n *\n * @param instance The `RemoteCommunication` instance on which the authorized message is processed.\n */\nexport function handleAuthorizedMessage(instance: RemoteCommunication) {\n  const { state } = instance;\n\n  state.authorized = true;\n  instance.emit(EventType.AUTHORIZED);\n}\n", "import { RemoteCommunication } from '../../../RemoteCommunication';\nimport { CommunicationLayerMessage } from '../../../types/CommunicationLayerMessage';\nimport { EventType } from '../../../types/EventType';\nimport { MessageType } from '../../../types/MessageType';\n\n/**\n * Handles the originator information message for a `RemoteCommunication` instance.\n *\n * The originator information message is typically used to exchange identification information between peers or entities within the communication process. This function achieves the following:\n *\n * 1. It sends back a `WALLET_INFO` message type containing the wallet information stored in the instance state. This step is useful for the receiver to identify and understand the capabilities and version of the wallet that is currently communicating.\n * 2. It updates the `originatorInfo` property of the `RemoteCommunication` instance state with the received originator information from the message. This provides a reference to the sender's identification information for future interactions.\n * 3. It emits a `CLIENTS_READY` event with relevant data, which can be consumed by listeners to know when the clients (i.e., both the sender and receiver) are ready for communication.\n * 4. It sets the `paused` state of the instance to `false`, indicating active communication.\n *\n * @param instance The `RemoteCommunication` instance on which the originator information message is processed.\n * @param message The received `CommunicationLayerMessage` containing the originator information.\n */\nexport function handleOriginatorInfoMessage(\n  instance: RemoteCommunication,\n  message: CommunicationLayerMessage,\n) {\n  const { state } = instance;\n\n  // TODO why these hardcoded value?\n  state.communicationLayer?.sendMessage({\n    type: MessageType.WALLET_INFO,\n    walletInfo: state.walletInfo,\n  });\n  state.originatorInfo = message.originatorInfo || message.originator;\n  instance.emit(EventType.CLIENTS_READY, {\n    isOriginator: state.isOriginator,\n    originatorInfo: state.originatorInfo,\n  });\n  state.paused = false;\n}\n", "import { logger } from '../../../utils/logger';\nimport { RemoteCommunication } from '../../../RemoteCommunication';\nimport { clean } from '../ChannelManager';\n\n/**\n * Creates and returns an event handler function for the \"socket_reconnect\" event. This handler processes socket reconnection events for a `RemoteCommunication` instance, updating its internal state and performing necessary cleanup.\n *\n * @param instance The `RemoteCommunication` instance associated with this event handler.\n * @returns A function that acts as the event handler for the \"socket_reconnect\" event.\n */\nexport function handleSocketReconnectEvent(instance: RemoteCommunication) {\n  return () => {\n    const { state } = instance;\n\n    logger.RemoteCommunication(\n      `[RemoteCommunication: handleSocketReconnectEvent()] on 'socket_reconnect' -- reset key exchange status / set ready to false`,\n    );\n\n    state.ready = false;\n    state.authorized = false;\n    clean(state);\n    instance.emitServiceStatusEvent({ context: 'socket_reconnect' });\n  };\n}\n", "import { logger } from '../../../utils/logger';\nimport { RemoteCommunication } from '../../../RemoteCommunication';\n\n/**\n * Creates and returns an event handler function for the \"socket_Disconnected\" event. This handler processes socket disconnection events for a `RemoteCommunication` instance, updating its internal state accordingly.\n *\n * @param instance The `RemoteCommunication` instance associated with this event handler.\n * @returns A function that acts as the event handler for the \"socket_Disconnected\" event.\n */\nexport function handleSocketDisconnectedEvent(instance: RemoteCommunication) {\n  return () => {\n    const { state } = instance;\n\n    logger.RemoteCommunication(\n      `[RemoteCommunication: handleSocketDisconnectedEvent()] on 'socket_Disconnected' set ready to false`,\n    );\n    state.ready = false;\n  };\n}\n", "import { DEFAULT_SESSION_TIMEOUT_MS } from '../../../config';\nimport { RemoteCommunication } from '../../../RemoteCommunication';\nimport { EventType } from '../../../types/EventType';\nimport { logger } from '../../../utils/logger';\n\nexport function handleFullPersistenceEvent(instance: RemoteCommunication) {\n  return async () => {\n    const { state } = instance;\n\n    logger.RemoteCommunication(\n      `[RemoteCommunication: handleFullPersistenceEvent()] context=${state.context}`,\n    );\n\n    instance.state.ready = true;\n    instance.state.clientsConnected = true;\n    instance.state.authorized = true;\n    instance.state.relayPersistence = true;\n    instance.state.communicationLayer?.getKeyExchange().setKeysExchanged(true);\n\n    instance.emit(EventType.KEYS_EXCHANGED, {\n      keysExchanged: true,\n      isOriginator: true,\n    });\n\n    // Automatically ready and authorized\n    instance.emit(EventType.AUTHORIZED);\n    instance.emit(EventType.CLIENTS_READY);\n    instance.emit(EventType.CHANNEL_PERSISTENCE);\n    try {\n      // Update channelConfig with full relay persistence\n      state.channelConfig = {\n        ...state.channelConfig,\n        localKey: state.communicationLayer?.getKeyExchange().getKeyInfo().ecies\n          .private,\n        otherKey: state.communicationLayer\n          ?.getKeyExchange()\n          .getOtherPublicKey(),\n        channelId: state.channelId ?? '',\n        validUntil:\n          state.channelConfig?.validUntil ?? DEFAULT_SESSION_TIMEOUT_MS,\n        relayPersistence: true,\n      };\n\n      await state.storageManager?.persistChannelConfig(state.channelConfig);\n    } catch (error) {\n      console.error(`Error persisting channel config`, error);\n    }\n  };\n}\n", "import packageJson from '../../../../package.json';\nimport { ECIESProps } from '../../../ECIES';\nimport { RemoteCommunication } from '../../../RemoteCommunication';\nimport { SocketService } from '../../../SocketService';\nimport { DEFAULT_SERVER_URL } from '../../../config';\nimport { CommunicationLayerPreference } from '../../../types/CommunicationLayerPreference';\nimport { EventType } from '../../../types/EventType';\nimport { OriginatorInfo } from '../../../types/OriginatorInfo';\nimport { logger } from '../../../utils/logger';\nimport {\n  handleAuthorizedEvent,\n  handleChannelCreatedEvent,\n  handleClientsConnectedEvent,\n  handleClientsDisconnectedEvent,\n  handleClientsWaitingEvent,\n  handleKeysExchangedEvent,\n  handleMessageEvent,\n  handleSocketDisconnectedEvent,\n  handleSocketReconnectEvent,\n} from '../EventListeners';\nimport { handleFullPersistenceEvent } from '../EventListeners/handleFullPersistenceEvent';\n\ntype CommunicationLayerHandledEvents =\n  | EventType.CLIENTS_CONNECTED\n  | EventType.CLIENTS_DISCONNECTED\n  | EventType.CLIENTS_WAITING\n  | EventType.SOCKET_DISCONNECTED\n  | EventType.SOCKET_RECONNECT\n  | EventType.CHANNEL_CREATED\n  | EventType.KEYS_EXCHANGED\n  | EventType.CHANNEL_PERSISTENCE\n  | EventType.KEY_INFO\n  | EventType.AUTHORIZED\n  | EventType.MESSAGE\n  | EventType.RPC_UPDATE;\n\n/**\n * Initializes the communication layer for a given RemoteCommunication  This function creates a communication layer based on the provided preference (e.g., SOCKET), sets up originator information, and attaches necessary event listeners.\n *\n * If the dappMetadata is available, this metadata is used to populate originator information such as the URL and name of the dapp.\n * This function also sets up various event listeners to handle different types of events that can occur in the communication layer, ensuring that the RemoteCommunication instance responds appropriately to each event.\n *\n * @param communicationLayerPreference Specifies the preferred communication protocol (e.g., SOCKET).\n * @param otherPublicKey The public key of the other party for communication (if available).\n * @param reconnect Indicates if the communication layer should attempt to reconnect after disconnection.\n * @param ecies The Elliptic Curve Integrated Encryption Scheme properties.\n * @param communicationServerUrl The URL of the communication server, defaults to the value in DEFAULT_SERVER_URL.\n * @param instance The current instance of the RemoteCommunication class.\n * @throws Error when an invalid communication protocol is specified.\n */\nexport function initSocketService({\n  communicationLayerPreference,\n  otherPublicKey,\n  reconnect,\n  ecies,\n  communicationServerUrl = DEFAULT_SERVER_URL,\n  instance,\n}: {\n  communicationLayerPreference: CommunicationLayerPreference;\n  otherPublicKey?: string;\n  reconnect?: boolean;\n  ecies?: ECIESProps;\n  communicationServerUrl?: string;\n  instance: RemoteCommunication;\n}) {\n  const { state } = instance;\n  // state.communicationLayer?.removeAllListeners();\n  logger.RemoteCommunication(\n    `[initCommunicationLayer()] `,\n    JSON.stringify(state, null, 2),\n  );\n\n  switch (communicationLayerPreference) {\n    case CommunicationLayerPreference.SOCKET:\n      state.communicationLayer = new SocketService({\n        communicationLayerPreference,\n        otherPublicKey,\n        reconnect,\n        transports: state.transports,\n        communicationServerUrl,\n        context: state.context,\n        ecies,\n        logging: state.logging,\n        remote: instance,\n      });\n      break;\n    default:\n      throw new Error('Invalid communication protocol');\n  }\n\n  let url = (typeof document !== 'undefined' && document.URL) || '';\n  let title = (typeof document !== 'undefined' && document.title) || '';\n\n  if (state.dappMetadata?.url) {\n    url = state.dappMetadata.url;\n  }\n\n  if (state.dappMetadata?.name) {\n    title = state.dappMetadata.name;\n  }\n\n  const defaultDappId =\n    state.dappMetadata?.name ?? state.dappMetadata?.url ?? 'N/A';\n  const dappId =\n    typeof window !== 'undefined' && typeof window.location !== 'undefined'\n      ? window.location.hostname ?? defaultDappId\n      : defaultDappId;\n\n  const originatorInfo: OriginatorInfo = {\n    url,\n    title,\n    source: state.dappMetadata?.source,\n    dappId,\n    icon: state.dappMetadata?.iconUrl || state.dappMetadata?.base64Icon,\n    platform: state.platformType,\n    apiVersion: packageJson.version,\n    connector: state.dappMetadata?.connector,\n  };\n  state.originatorInfo = originatorInfo;\n\n  const eventsMapping: {\n    [key in CommunicationLayerHandledEvents]: (...args: any[]) => void;\n  } = {\n    // TODO AUTHORIZED listeners is only added for backward compatibility with wallet < 7.3\n    [EventType.AUTHORIZED]: handleAuthorizedEvent(instance),\n    [EventType.MESSAGE]: handleMessageEvent(instance),\n    [EventType.CHANNEL_PERSISTENCE]: handleFullPersistenceEvent(instance),\n    [EventType.CLIENTS_CONNECTED]: handleClientsConnectedEvent(\n      instance,\n      communicationLayerPreference,\n    ),\n    [EventType.KEYS_EXCHANGED]: handleKeysExchangedEvent(\n      instance,\n      communicationLayerPreference,\n    ),\n    [EventType.SOCKET_DISCONNECTED]: handleSocketDisconnectedEvent(instance),\n    [EventType.SOCKET_RECONNECT]: handleSocketReconnectEvent(instance),\n    [EventType.CLIENTS_DISCONNECTED]: handleClientsDisconnectedEvent(instance),\n    [EventType.KEY_INFO]: () => {\n      // Skip handling KEY_INFO event, not required anymore\n      // instance.emitServiceStatusEvent();\n    },\n    [EventType.CHANNEL_CREATED]: handleChannelCreatedEvent(instance),\n    [EventType.CLIENTS_WAITING]: handleClientsWaitingEvent(instance),\n    [EventType.RPC_UPDATE]: (rpc) => {\n      // TODO use a separate function to isolate unit tests\n      // propagate RPC_UPDATE event to the SDK\n      instance.emit(EventType.RPC_UPDATE, rpc);\n    },\n  };\n\n  for (const [eventType, handler] of Object.entries(eventsMapping)) {\n    try {\n      state.communicationLayer.on(eventType, handler);\n    } catch (error) {\n      console.error(`Error registering handler for ${eventType}:`, error);\n    }\n  }\n}\n", "import { v4 as uuidv4 } from 'uuid';\nimport { logger } from '../../../utils/logger';\nimport { RemoteCommunication } from '../../../RemoteCommunication';\nimport { ConnectionStatus } from '../../../types/ConnectionStatus';\nimport { DisconnectOptions } from '../../../types/DisconnectOptions';\nimport { MessageType } from '../../../types/MessageType';\nimport { encryptAndSendMessage } from '../../SocketService/MessageHandlers';\nimport { SendAnalytics } from '../../../Analytics';\nimport { TrackingEvents } from '../../../types/TrackingEvent';\n\n/**\n * Handles the disconnection process for a RemoteCommunication instance Depending on the provided options, it can terminate the connection and clear related configurations or simply disconnect.\n *\n * @param options Optional settings that determine how the disconnection is handled. It can specify whether to terminate the connection, send a termination message, or reset the channel ID.\n * @param instance The current instance of the RemoteCommunication class that needs to be disconnected.\n * @returns void\n */\nexport async function disconnect({\n  options,\n  instance,\n}: {\n  options?: DisconnectOptions;\n  instance: RemoteCommunication;\n}): Promise<boolean> {\n  const { state } = instance;\n\n  logger.RemoteCommunication(\n    `[RemoteCommunication: disconnect()] channel=${state.channelId}`,\n    options,\n  );\n\n  return new Promise<boolean>((resolve, reject) => {\n    if (options?.terminate) {\n      if (instance.state.ready) {\n        SendAnalytics(\n          {\n            id: instance.state.channelId ?? '',\n            event: TrackingEvents.TERMINATED,\n          },\n          instance.state.communicationServerUrl,\n        ).catch((err) => {\n          console.error(`[handleSendMessage] Cannot send analytics`, err);\n        });\n      }\n\n      state.ready = false;\n      state.paused = false;\n\n      // remove channel config from persistence layer and close active connections.\n      state.storageManager?.terminate(state.channelId ?? '');\n      instance.state.terminated = true;\n      if (options.sendMessage) {\n        // Prevent sending terminate in loop\n        if (\n          state.communicationLayer?.getKeyInfo().keysExchanged &&\n          instance.state.communicationLayer\n        ) {\n          encryptAndSendMessage(instance.state.communicationLayer, {\n            type: MessageType.TERMINATE,\n          })\n            .then(() => {\n              console.warn(\n                `[disconnect] Terminate message sent to the other peer`,\n              );\n              resolve(true);\n            })\n            .catch((error) => {\n              reject(error);\n            });\n        }\n      } else {\n        resolve(true);\n      }\n\n      state.authorized = false;\n      state.relayPersistence = false;\n      state.channelId = uuidv4();\n      options.channelId = state.channelId;\n      state.channelConfig = undefined;\n      state.originatorConnectStarted = false;\n      state.communicationLayer?.disconnect(options);\n      instance.setConnectionStatus(ConnectionStatus.TERMINATED);\n    } else {\n      state.communicationLayer?.disconnect(options);\n      instance.setConnectionStatus(ConnectionStatus.DISCONNECTED);\n      resolve(true);\n    }\n  });\n}\n", "export enum CommunicationLayerPreference {\n  SOCKET = 'socket',\n}\n", "import debug from 'debug';\nimport { EventEmitter2 } from 'eventemitter2';\nimport packageJson from '../package.json';\nimport { ECIESProps } from './ECIES';\nimport { SocketService } from './SocketService';\nimport {\n  CHANNEL_MAX_WAITING_TIME,\n  DEFAULT_SERVER_URL,\n  DEFAULT_SESSION_TIMEOUT_MS,\n} from './config';\nimport {\n  clean,\n  generateChannelIdConnect,\n} from './services/RemoteCommunication/ChannelManager';\nimport {\n  connectToChannel,\n  disconnect,\n  initSocketService,\n  originatorSessionConnect,\n  resume,\n} from './services/RemoteCommunication/ConnectionManager';\nimport { sendMessage } from './services/RemoteCommunication/MessageHandlers';\nimport { testStorage } from './services/RemoteCommunication/StorageManager';\nimport { AutoConnectOptions } from './types/AutoConnectOptions';\nimport { ChannelConfig } from './types/ChannelConfig';\nimport { CommunicationLayerMessage } from './types/CommunicationLayerMessage';\nimport { CommunicationLayerPreference } from './types/CommunicationLayerPreference';\nimport { ConnectionStatus } from './types/ConnectionStatus';\nimport { DappMetadataWithSource } from './types/DappMetadata';\nimport { DisconnectOptions } from './types/DisconnectOptions';\nimport { EventType } from './types/EventType';\nimport { CommunicationLayerLoggingOptions } from './types/LoggingOptions';\nimport { OriginatorInfo } from './types/OriginatorInfo';\nimport { PlatformType } from './types/PlatformType';\nimport { ServiceStatus } from './types/ServiceStatus';\nimport {\n  StorageManager as SessionStorageManager,\n  StorageManagerProps,\n} from './types/StorageManager';\nimport { WalletInfo } from './types/WalletInfo';\nimport { logger } from './utils/logger';\nimport { Channel } from './types/Channel';\nimport { rejectChannel } from './services/RemoteCommunication/ConnectionManager/rejectChannel';\n\ntype MetaMaskMobile = 'metamask-mobile';\n\nexport interface RemoteCommunicationProps {\n  platformType: PlatformType | MetaMaskMobile;\n  communicationLayerPreference: CommunicationLayerPreference;\n  otherPublicKey?: string;\n  protocolVersion?: number;\n  privateKey?: string;\n  reconnect?: boolean;\n  relayPersistence?: boolean; // Used by wallet to start the connection with relayPersistence and avoid the key exchange.\n  dappMetadata?: DappMetadataWithSource;\n  walletInfo?: WalletInfo;\n  transports?: string[];\n  analytics?: boolean;\n  communicationServerUrl?: string;\n  ecies?: ECIESProps;\n  sdkVersion?: string;\n  storage?: StorageManagerProps;\n  context: string;\n  autoConnect?: AutoConnectOptions;\n  logging?: CommunicationLayerLoggingOptions;\n}\n\nexport interface RemoteCommunicationState {\n  ready: boolean;\n  authorized: boolean;\n  isOriginator: boolean;\n  paused: boolean;\n  relayPersistence?: boolean;\n  otherPublicKey?: string;\n  protocolVersion: number;\n  deeplinkProtocolAvailable: boolean;\n  privateKey?: string;\n  terminated: boolean;\n  transports?: string[];\n  platformType: PlatformType | MetaMaskMobile;\n  analytics: boolean;\n  channelId?: string;\n  channelConfig?: ChannelConfig;\n  walletInfo?: WalletInfo;\n  persist?: boolean;\n  communicationLayer?: SocketService;\n  originatorInfo?: OriginatorInfo;\n  originatorInfoSent: boolean;\n  reconnection: boolean;\n  dappMetadata?: DappMetadataWithSource;\n  communicationServerUrl: string;\n  context: string;\n  storageManager?: SessionStorageManager;\n  storageOptions?: StorageManagerProps;\n  sdkVersion?: string;\n  autoConnectOptions?: AutoConnectOptions;\n  clientsConnected: boolean;\n  sessionDuration: number;\n  originatorConnectStarted: boolean;\n  debug: boolean;\n  logging?: CommunicationLayerLoggingOptions;\n  _connectionStatus: ConnectionStatus;\n}\nexport class RemoteCommunication extends EventEmitter2 {\n  private _options: RemoteCommunicationProps;\n\n  public state: RemoteCommunicationState = {\n    // ready flag is turned on after we receive 'clients_ready' message, meaning key exchange is complete.\n    ready: false,\n    // flag turned on once the connection has been authorized on the wallet.\n    authorized: false,\n    isOriginator: false,\n    terminated: false,\n    protocolVersion: 1,\n    paused: false,\n    deeplinkProtocolAvailable: false,\n    platformType: 'metamask-mobile',\n    analytics: false,\n    reconnection: false,\n    originatorInfoSent: false,\n    communicationServerUrl: DEFAULT_SERVER_URL,\n    context: '',\n    persist: false,\n    // Keep track if the other side is connected to the socket\n    clientsConnected: false,\n    sessionDuration: DEFAULT_SESSION_TIMEOUT_MS,\n    // this flag is switched on when the connection is automatically initialized after finding existing channel configuration.\n    originatorConnectStarted: false,\n    debug: false,\n    // Status of the other side of the connection\n    // 1) if I am MetaMask then other is Dapp\n    // 2) If I am Dapp (isOriginator==true) then other side is MetaMask\n    // Should not be set directly, use this.setConnectionStatus() instead to always emit events.\n    _connectionStatus: ConnectionStatus.DISCONNECTED,\n  };\n\n  constructor(options: RemoteCommunicationProps) {\n    super();\n\n    this._options = options;\n\n    const {\n      platformType,\n      communicationLayerPreference,\n      otherPublicKey,\n      reconnect,\n      walletInfo,\n      dappMetadata,\n      protocolVersion,\n      transports,\n      context,\n      relayPersistence,\n      ecies,\n      analytics = false,\n      storage,\n      sdkVersion,\n      communicationServerUrl = DEFAULT_SERVER_URL,\n      logging,\n      autoConnect = {\n        timeout: CHANNEL_MAX_WAITING_TIME,\n      },\n    } = options;\n\n    this.state.otherPublicKey = otherPublicKey;\n    this.state.dappMetadata = dappMetadata;\n    this.state.walletInfo = walletInfo;\n    this.state.transports = transports;\n    this.state.platformType = platformType;\n    this.state.analytics = analytics;\n    this.state.protocolVersion = protocolVersion ?? 1;\n    this.state.isOriginator = !otherPublicKey;\n    this.state.relayPersistence = relayPersistence;\n    this.state.communicationServerUrl = communicationServerUrl;\n    this.state.context = context;\n    this.state.terminated = false;\n    this.state.sdkVersion = sdkVersion;\n\n    this.setMaxListeners(50);\n\n    this.setConnectionStatus(ConnectionStatus.DISCONNECTED);\n    if (storage?.duration) {\n      this.state.sessionDuration = DEFAULT_SESSION_TIMEOUT_MS;\n    }\n    this.state.storageOptions = storage;\n    this.state.autoConnectOptions = autoConnect;\n    this.state.debug = logging?.remoteLayer === true;\n\n    // Enable loggers early\n    if (logging?.remoteLayer === true) {\n      debug.enable('RemoteCommunication:Layer');\n    }\n\n    if (logging?.serviceLayer === true) {\n      debug.enable('SocketService:Layer');\n    }\n\n    if (logging?.eciesLayer === true) {\n      debug.enable('ECIES:Layer');\n    }\n\n    if (logging?.keyExchangeLayer === true) {\n      debug.enable('KeyExchange:Layer');\n    }\n\n    this.state.logging = logging;\n\n    if (storage?.storageManager) {\n      this.state.storageManager = storage.storageManager;\n    }\n\n    logger.RemoteCommunication(\n      `[RemoteCommunication: constructor()] protocolVersion=${protocolVersion} relayPersistence=${relayPersistence} isOriginator=${this.state.isOriginator} communicationLayerPreference=${communicationLayerPreference} otherPublicKey=${otherPublicKey} reconnect=${reconnect}`,\n    );\n\n    if (!this.state.isOriginator) {\n      initSocketService({\n        communicationLayerPreference,\n        otherPublicKey,\n        reconnect,\n        ecies,\n        communicationServerUrl,\n        instance: this,\n      });\n    }\n\n    this.emitServiceStatusEvent({ context: 'constructor' });\n  }\n\n  /**\n   * Initialize the connection from the dapp side.\n   */\n  public async initFromDappStorage() {\n    if (this.state.storageManager) {\n      // Try to get existing channel config from storage\n      const channelConfig =\n        await this.state.storageManager.getPersistedChannelConfig({});\n      if (channelConfig) {\n        this.state.channelConfig = channelConfig;\n        this.state.channelId = channelConfig.channelId;\n        this.state.deeplinkProtocolAvailable =\n          channelConfig.deeplinkProtocolAvailable ?? false;\n\n        if (channelConfig.relayPersistence) {\n          this.state.authorized = true;\n          this.state.ready = true;\n          this.setConnectionStatus(ConnectionStatus.LINKED);\n          await this.connectToChannel({\n            channelId: channelConfig.channelId,\n          });\n        }\n      }\n    }\n\n    initSocketService({\n      communicationLayerPreference: CommunicationLayerPreference.SOCKET,\n      otherPublicKey: this.state.otherPublicKey,\n      reconnect: this._options.reconnect,\n      ecies: this._options.ecies,\n      communicationServerUrl: this.state.communicationServerUrl,\n      instance: this,\n    });\n  }\n\n  /**\n   * Connect from the dapp using session persistence.\n   */\n  async originatorSessionConnect(): Promise<ChannelConfig | undefined> {\n    const channelConfig = await originatorSessionConnect(this);\n    return channelConfig;\n  }\n\n  async generateChannelIdConnect(): Promise<Channel> {\n    return generateChannelIdConnect(this.state);\n  }\n\n  clean() {\n    return clean(this.state);\n  }\n\n  connectToChannel({\n    channelId,\n    withKeyExchange,\n    authorized,\n  }: {\n    channelId: string;\n    authorized?: boolean;\n    withKeyExchange?: boolean;\n  }): Promise<void> {\n    return connectToChannel({\n      channelId,\n      authorized,\n      withKeyExchange,\n      state: this.state,\n    });\n  }\n\n  sendMessage(message: CommunicationLayerMessage): Promise<boolean> {\n    return sendMessage(this, message);\n  }\n\n  async testStorage() {\n    return testStorage(this.state);\n  }\n\n  hasDeeplinkProtocol() {\n    return this.state.deeplinkProtocolAvailable;\n  }\n\n  getChannelConfig() {\n    return this.state.channelConfig;\n  }\n\n  /**\n   * Check if the connection is ready to handle secure communication.\n   *\n   * @returns boolean\n   */\n  isReady() {\n    return this.state.ready;\n  }\n\n  /**\n   * Check the value of the socket io client.\n   *\n   * @returns boolean\n   */\n  isConnected() {\n    return this.state.communicationLayer?.isConnected();\n  }\n\n  isAuthorized() {\n    return this.state.authorized;\n  }\n\n  isPaused() {\n    return this.state.paused;\n  }\n\n  getCommunicationLayer() {\n    return this.state.communicationLayer;\n  }\n\n  async ping() {\n    logger.RemoteCommunication(\n      `[RemoteCommunication: ping()] channel=${this.state.channelId}`,\n    );\n\n    await this.state.communicationLayer?.ping();\n  }\n\n  testLogger() {\n    logger.RemoteCommunication(`testLogger() channel=${this.state.channelId}`);\n    logger.SocketService(`testLogger() channel=${this.state.channelId}`);\n    logger.Ecies(`testLogger() channel=${this.state.channelId}`);\n    logger.KeyExchange(`testLogger() channel=${this.state.channelId}`);\n  }\n\n  keyCheck() {\n    logger.RemoteCommunication(\n      `[RemoteCommunication: keyCheck()] channel=${this.state.channelId}`,\n    );\n\n    this.state.communicationLayer?.keyCheck();\n  }\n\n  setConnectionStatus(connectionStatus: ConnectionStatus) {\n    if (this.state._connectionStatus === connectionStatus) {\n      return; // Don't re-emit current status.\n    }\n    this.state._connectionStatus = connectionStatus;\n    this.emit(EventType.CONNECTION_STATUS, connectionStatus);\n    this.emitServiceStatusEvent({ context: 'setConnectionStatus' });\n  }\n\n  emitServiceStatusEvent(_: { context?: string } = {}) {\n    // only emit if there was a change in the service status\n    this.emit(EventType.SERVICE_STATUS, this.getServiceStatus());\n  }\n\n  getConnectionStatus() {\n    return this.state._connectionStatus;\n  }\n\n  getServiceStatus(): ServiceStatus {\n    return {\n      originatorInfo: this.state.originatorInfo,\n      keyInfo: this.getKeyInfo(),\n      connectionStatus: this.state._connectionStatus,\n      channelConfig: this.state.channelConfig,\n      channelId: this.state.channelId,\n    };\n  }\n\n  getKeyInfo() {\n    return this.state.communicationLayer?.getKeyInfo();\n  }\n\n  resetKeys() {\n    this.state.communicationLayer?.resetKeys();\n  }\n\n  setOtherPublicKey(otherPublicKey: string) {\n    const keyExchange = this.state.communicationLayer?.getKeyExchange();\n    if (!keyExchange) {\n      throw new Error('KeyExchange is not initialized.');\n    }\n\n    if (keyExchange.getOtherPublicKey() !== otherPublicKey) {\n      keyExchange.setOtherPublicKey(otherPublicKey);\n    }\n  }\n\n  async pause() {\n    logger.RemoteCommunication(\n      `[RemoteCommunication: pause()] channel=${this.state.channelId}`,\n    );\n\n    await this.state.communicationLayer?.pause();\n    this.setConnectionStatus(ConnectionStatus.PAUSED);\n  }\n\n  getVersion() {\n    return packageJson.version;\n  }\n\n  hasRelayPersistence() {\n    return this.state.relayPersistence ?? false;\n  }\n\n  async resume() {\n    return resume(this);\n  }\n\n  encrypt(data: string) {\n    const keyExchange = this.state.communicationLayer?.getKeyExchange();\n    const otherPublicKey = keyExchange?.getOtherPublicKey();\n    if (!otherPublicKey) {\n      throw new Error('KeyExchange not completed');\n    }\n    return this.state.communicationLayer?.state.eciesInstance?.encrypt(\n      data,\n      otherPublicKey,\n    );\n  }\n\n  decrypt(data: string) {\n    if (!this.state.communicationLayer?.state.eciesInstance) {\n      throw new Error('ECIES instance is not initialized');\n    }\n    return this.state.communicationLayer?.state.eciesInstance?.decrypt(data);\n  }\n\n  getChannelId() {\n    return this.state.channelId;\n  }\n\n  getRPCMethodTracker() {\n    return this.state.communicationLayer?.getRPCMethodTracker();\n  }\n\n  reject({ channelId }: { channelId: string }) {\n    return rejectChannel({\n      channelId,\n      state: this.state,\n    });\n  }\n\n  async disconnect(options?: DisconnectOptions): Promise<boolean> {\n    return disconnect({\n      options,\n      instance: this,\n    });\n  }\n}\n", "import { logger } from '../../../utils/logger';\nimport { RemoteCommunication } from '../../../RemoteCommunication';\n\n/**\n * Attempts to establish a session connection for an originator based on stored channel configuration.\n * If a storage manager is defined for the given RemoteCommunication instance, this function will retrieve the persisted channel configuration. Based on the configuration and the current state of the communication layer, various actions are taken:\n * - If the socket is already connected, it skips further actions and returns the channel configuration.\n * - If the stored channel configuration is valid (i.e., not expired), the function sets up the necessary state variables and attempts to connect to the channel.\n *\n * This function is particularly useful for re-establishing connections with saved session configurations.\n *\n * @param instance The current instance of the RemoteCommunication class.\n * @returns The channel configuration if available and valid, otherwise undefined.\n */\nexport async function originatorSessionConnect(instance: RemoteCommunication) {\n  const { state } = instance;\n\n  if (!state.storageManager) {\n    logger.RemoteCommunication(\n      `[RemoteCommunication: originatorSessionConnect()] no storage manager defined - skip`,\n    );\n    return undefined;\n  }\n\n  const channelConfig = await state.storageManager.getPersistedChannelConfig(\n    {},\n  );\n  logger.RemoteCommunication(\n    `[RemoteCommunication: originatorSessionConnect()] autoStarted=${state.originatorConnectStarted} channelConfig`,\n    channelConfig,\n  );\n\n  const connected = state.communicationLayer?.isConnected();\n  if (connected) {\n    logger.RemoteCommunication(\n      `[RemoteCommunication: originatorSessionConnect()] socket already connected - skip`,\n    );\n\n    return channelConfig;\n  }\n\n  if (channelConfig) {\n    const validSession = channelConfig.validUntil > Date.now();\n\n    if (validSession) {\n      state.channelConfig = channelConfig;\n      state.originatorConnectStarted = true;\n      state.channelId = channelConfig?.channelId;\n      state.reconnection = true;\n      return channelConfig;\n    }\n\n    logger.RemoteCommunication(\n      `[RemoteCommunication: autoConnect()] Session has expired`,\n    );\n  }\n  state.originatorConnectStarted = false;\n  return undefined;\n}\n", "import { RemoteCommunicationState } from '../../../RemoteCommunication';\nimport { ChannelConfig } from '../../../types/ChannelConfig';\nimport { logger } from '../../../utils/logger';\n\n/**\n * Generates a new channel ID for the communication layer or reuses an existing one.\n * Also establishes necessary configurations and throws errors if the layer isn't initialized or if the channel is already connected.\n *\n * @param state Current state of the RemoteCommunication class instance.\n * @returns An object containing the channelId and its corresponding public/private key.\n */\n\nexport async function generateChannelIdConnect(\n  state: RemoteCommunicationState,\n) {\n  if (!state.communicationLayer) {\n    throw new Error('communication layer not initialized');\n  }\n\n  if (state.ready) {\n    throw new Error('Channel already connected');\n  }\n\n  if (state.channelId && state.communicationLayer?.isConnected()) {\n    state.channelConfig = {\n      ...state.channelConfig,\n      channelId: state.channelId,\n      validUntil: Date.now() + state.sessionDuration,\n    };\n\n    state.storageManager?.persistChannelConfig(state.channelConfig);\n\n    return {\n      channelId: state.channelId,\n      privKey: state.communicationLayer?.getKeyInfo()?.ecies.private,\n      pubKey: state.communicationLayer?.getKeyInfo()?.ecies.public,\n    };\n  }\n\n  logger.RemoteCommunication(`[RemoteCommunication: generateChannelId()]`);\n  const channel = await state.communicationLayer.createChannel();\n\n  logger.RemoteCommunication(\n    `[RemoteCommunication: generateChannelId()] channel created`,\n    channel,\n  );\n\n  const channelConfig: ChannelConfig = {\n    ...state.channelConfig,\n    channelId: channel.channelId,\n    localKey: channel.privKey,\n    validUntil: Date.now() + state.sessionDuration,\n  };\n  state.channelId = channel.channelId;\n  state.channelConfig = channelConfig;\n\n  return {\n    channelId: state.channelId,\n    pubKey: channel.pubKey,\n    privKey: channel.privKey,\n  };\n}\n", "// packages/sdk-communication-layer/src/services/RemoteCommunication/ConnectionManager/connectToChannel.ts\nimport { validate } from 'uuid';\nimport { logger } from '../../../utils/logger';\nimport { RemoteCommunicationState } from '../../../RemoteCommunication';\nimport { ChannelConfig } from '../../../types/ChannelConfig';\n\n/**\n * Initiates a connection to a specified channel. Validates the channel ID, establishes a new connection if not connected, and sets necessary configurations.\n * Also persists the new channel configuration if a storage manager is available.\n *\n * @param channelId Unique identifier for the channel.\n * @param withKeyExchange Optional flag indicating if key exchange should occur during the connection process.\n * @param state Current state of the RemoteCommunication class instance.\n * @returns void\n */\nexport async function connectToChannel({\n  channelId,\n  withKeyExchange,\n  authorized,\n  state,\n}: {\n  channelId: string;\n  authorized?: boolean;\n  withKeyExchange?: boolean;\n  state: RemoteCommunicationState;\n}) {\n  if (!validate(channelId)) {\n    logger.RemoteCommunication(\n      `[RemoteCommunication: connectToChannel()] context=${state.context} invalid channel channelId=${channelId}`,\n    );\n    throw new Error(`Invalid channel ${channelId}`);\n  }\n\n  logger.RemoteCommunication(\n    `[RemoteCommunication: connectToChannel()] context=${state.context} channelId=${channelId} withKeyExchange=${withKeyExchange}`,\n  );\n\n  if (state.communicationLayer?.isConnected()) {\n    // Adding a check on previous connection to prevent reconnecting during dev when HMR is enabled\n    logger.RemoteCommunication(\n      `[RemoteCommunication: connectToChannel()] context=${state.context} already connected - interrupt connection.`,\n    );\n    return;\n  }\n\n  state.channelId = channelId;\n  await state.communicationLayer?.connectToChannel({\n    channelId,\n    authorized,\n    withKeyExchange,\n  });\n  const newChannelConfig: ChannelConfig = {\n    ...state.channelConfig,\n    channelId,\n    validUntil: Date.now() + state.sessionDuration,\n  };\n  state.channelConfig = newChannelConfig;\n  state.storageManager?.persistChannelConfig(newChannelConfig);\n}\n", "import { logger } from '../../../utils/logger';\nimport { RemoteCommunication } from '../../../RemoteCommunication';\nimport { CommunicationLayerMessage } from '../../../types/CommunicationLayerMessage';\nimport { EventType } from '../../../types/EventType';\nimport { handleAuthorization } from '../ConnectionManager';\n\n/**\n * Asynchronously sends a message using the given `RemoteCommunication` instance.\n *\n * The function first checks if the system is in an appropriate state to send the message.\n * This includes ensuring that the communication isn't paused, the system is ready,\n * it's connected, and the clients are also connected.\n *\n * If the system isn't in a ready state, the function waits for the `CLIENTS_READY` event\n * to be emitted, signaling that it can proceed with sending the message. Once this event\n * is triggered, the function tries to authorize and send the message.\n *\n * If the system is already in a ready state, it proceeds directly to authorize and send\n * the message, handling any potential errors.\n *\n * If `debug` mode is enabled, the function logs crucial information, providing visibility\n * into its operations, which can be valuable for debugging.\n *\n * @param instance The `RemoteCommunication` instance used to send the message.\n * @param message The message of type `CommunicationLayerMessage` to be sent.\n * @returns A Promise that resolves once the message is sent or rejects with an error.\n */\nexport async function sendMessage(\n  instance: RemoteCommunication,\n  message: CommunicationLayerMessage,\n): Promise<boolean> {\n  const { state } = instance;\n\n  logger.RemoteCommunication(\n    `[RemoteCommunication: sendMessage()] context=${state.context} paused=${\n      state.paused\n    } ready=${state.ready} relayPersistence=${\n      state.relayPersistence\n    } authorized=${\n      state.authorized\n    } socket=${state.communicationLayer?.isConnected()} clientsConnected=${\n      state.clientsConnected\n    } status=${state._connectionStatus}`,\n    message,\n  );\n\n  if (\n    !state.relayPersistence && // Ignore status change when relay persistence is available\n    (!state.ready ||\n      !state.communicationLayer?.isConnected() ||\n      !state.clientsConnected)\n  ) {\n    logger.RemoteCommunication(\n      `[RemoteCommunication: sendMessage()] context=${state.context}  SKIP message waiting for MM mobile readiness.`,\n    );\n\n    await new Promise<void>((resolve) => {\n      instance.once(EventType.CLIENTS_READY, resolve);\n    });\n\n    logger.RemoteCommunication(\n      `[RemoteCommunication: sendMessage()] context=${state.context}  AFTER SKIP / READY -- sending pending message`,\n    );\n  }\n\n  try {\n    const success = await handleAuthorization(instance, message);\n    return success;\n  } catch (err) {\n    console.error(\n      `[RemoteCommunication: sendMessage()] context=${state.context}  ERROR`,\n      err,\n    );\n    throw err;\n  }\n}\n", "import { logger } from '../../../utils/logger';\nimport { RemoteCommunication } from '../../../RemoteCommunication';\nimport { CommunicationLayerMessage } from '../../../types/CommunicationLayerMessage';\nimport { EventType } from '../../../types/EventType';\n\n/**\n * Manages the message authorization process for a RemoteCommunication instance. It ensures that only authorized messages are sent through the communication layer. For backwards compatibility, messages sent by wallets older than version 7.3 are also handled.\n *\n * @param instance The current instance of the RemoteCommunication class.\n * @param message The message from the CommunicationLayer that needs authorization before being sent.\n * @returns Promise<void> Resolves when the message has been processed, either by sending it or by ensuring the necessary authorization.\n */\nexport async function handleAuthorization(\n  instance: RemoteCommunication,\n  message: CommunicationLayerMessage,\n): Promise<boolean> {\n  return new Promise((resolve) => {\n    const { state } = instance;\n\n    logger.RemoteCommunication(\n      `[RemoteCommunication: handleAuthorization()] context=${state.context} ready=${state.ready} authorized=${state.authorized} method=${message.method}`,\n    );\n\n    if (!state.isOriginator || state.authorized || state.relayPersistence) {\n      state.communicationLayer\n        ?.sendMessage(message)\n        .then((sent) => {\n          resolve(sent);\n        })\n        .catch((error) => {\n          console.error(\n            `[RemoteCommunication: handleAuthorization()] context=${state.context}  ERROR`,\n            error,\n          );\n          resolve(false);\n        });\n    } else {\n      instance.once(EventType.AUTHORIZED, () => {\n        logger.RemoteCommunication(\n          `[RemoteCommunication: handleAuthorization()] context=${state.context}  AFTER SKIP / AUTHORIZED -- sending pending message`,\n        );\n\n        // only send the message after the clients have awaken.\n        state.communicationLayer\n          ?.sendMessage(message)\n          .then((sent) => {\n            resolve(sent);\n          })\n          .catch((error) => {\n            console.error(\n              `[RemoteCommunication: handleAuthorization()] context=${state.context}  ERROR`,\n              error,\n            );\n            resolve(false);\n          });\n      });\n    }\n  });\n}\n", "import { logger } from '../../../utils/logger';\nimport { RemoteCommunicationState } from '../../../RemoteCommunication';\n\n/**\n * Updates the last active date for a given `RemoteCommunication` instance and persists the\n * updated channel configuration.\n *\n * The function creates a new channel configuration (`ChannelConfig`) using the `channelId`\n * and `validUntil` values from the instance's state and the provided `lastActiveDate`.\n * This new configuration is then persisted using the instance's `storageManager`.\n *\n * If `debug` mode is enabled in the instance's state, the function logs the current channel\n * and the provided last active date.\n *\n * @param instance The `RemoteCommunication` instance whose channel configuration is to be updated.\n * @param lastActiveDate The date to set as the last active date.\n */\nexport async function testStorage(state: RemoteCommunicationState) {\n  const res = await state.storageManager?.getPersistedChannelConfig();\n\n  logger.RemoteCommunication(`[RemoteCommunication: testStorage()] res`, res);\n}\n", "import { logger } from '../../../utils/logger';\nimport { RemoteCommunication } from '../../../RemoteCommunication';\nimport { ConnectionStatus } from '../../../types/ConnectionStatus';\n\n/**\n * Resumes the communication of a previously paused `RemoteCommunication` instance.\n * This function primarily instructs the underlying communication layer to resume its activities. After resuming, the connection status of the instance is set to 'LINKED'.\n * Debug logs are generated if the debug state is enabled, indicating the current channel being resumed.\n *\n * @param instance The current instance of the RemoteCommunication class.\n */\nexport async function resume(instance: RemoteCommunication) {\n  const { state } = instance;\n\n  logger.RemoteCommunication(\n    `[RemoteCommunication: resume()] channel=${state.channelId}`,\n  );\n\n  await state.communicationLayer?.resume();\n  instance.setConnectionStatus(ConnectionStatus.LINKED);\n}\n", "// packages/sdk-communication-layer/src/services/RemoteCommunication/ConnectionManager/connectToChannel.ts\nimport { validate } from 'uuid';\nimport { RemoteCommunicationState } from '../../../RemoteCommunication';\nimport { EventType } from '../../../types/EventType';\nimport { SendAnalytics } from '../../../Analytics';\nimport { TrackingEvents } from '../../../types/TrackingEvent';\nimport { logger } from '../../../utils/logger';\n\nimport packageJson from '../../../../package.json';\n\n/**\n * Rejects a channel connection from the wallet.\n *\n * @param channelId Unique identifier for the channel.\n * @param state Current state of the RemoteCommunication class instance.\n * @returns void\n */\nexport async function rejectChannel({\n  channelId,\n  state,\n}: {\n  channelId: string;\n  state: RemoteCommunicationState;\n}): Promise<unknown> {\n  if (!validate(channelId)) {\n    logger.RemoteCommunication(\n      `[RemoteCommunication: connectToChannel()] context=${state.context} invalid channel channelId=${channelId}`,\n    );\n    throw new Error(`Invalid channel ${channelId}`);\n  }\n\n  if (state.isOriginator) {\n    // only wallet can reject\n    logger.RemoteCommunication(\n      `[RemoteCommunication: reject()] context=${state.context} isOriginator=${state.isOriginator} channelId=${channelId}`,\n    );\n    return;\n  }\n\n  const { socket } = state.communicationLayer?.state ?? {};\n\n  if (!socket?.connected) {\n    logger.RemoteCommunication(\n      `[RemoteCommunication: reject()] context=${state.context} socket already connected`,\n    );\n    socket?.connect();\n  }\n\n  // Send analytics event\n  SendAnalytics(\n    {\n      id: channelId,\n      event: TrackingEvents.REJECTED,\n      ...state.originatorInfo,\n      sdkVersion: state.sdkVersion,\n      commLayerVersion: packageJson.version,\n      walletVersion: state.walletInfo?.version,\n    },\n    state.communicationServerUrl,\n  ).catch((error) => {\n    console.error(`rejectChannel:: Error emitting analytics event`, error);\n  });\n\n  // emit reject event\n  await new Promise<unknown>((resolve, reject) => {\n    socket?.emit(\n      EventType.REJECTED,\n      {\n        channelId,\n      },\n      (error: unknown, response: unknown) => {\n        logger.RemoteCommunication(\n          `[RemoteCommunication: reject()] context=${state.context} socket=${socket?.id}`,\n          { error, response },\n        );\n\n        if (error) {\n          reject(error);\n        } else {\n          resolve(response);\n        }\n      },\n    );\n  });\n}\n"], "names": ["loggerKeyExchangeLayer", "debug", "default", "loggerServiceLayer", "loggerEciesLayer", "loggerRemoteLayer", "color", "logger", "KeyExchange", "SocketService", "Ecies", "RemoteCommunication", "targetUrl", "analyticsBuffer", "temp<PERSON><PERSON><PERSON>", "SendAnalytics", "parameters", "socketServerUrl", "__awaiter", "push", "swap", "swapBuffers", "serverUrl", "endsWith", "flatParams", "Object", "assign", "params", "key", "value", "entries", "body", "JSON", "stringify", "length", "response", "crossFetch", "method", "headers", "Accept", "text", "error", "sendBufferedEvents", "catch", "ECIES", "constructor", "props", "this", "enabled", "enable", "ecies", "privateKey", "Private<PERSON><PERSON>", "fromHex", "toHex", "public<PERSON>ey", "generateECIES", "getPublicKey", "encrypt", "data", "otherPublicKey", "encryptedString", "payload", "<PERSON><PERSON><PERSON>", "from", "encryptedData", "toString", "err", "decrypt", "decryptedString", "getKeyInfo", "private", "public", "DEFAULT_SERVER_URL", "DEFAULT_SOCKET_TRANSPORTS", "DEFAULT_SESSION_TIMEOUT_MS", "CHANNEL_MAX_WAITING_TIME", "RPC_METHODS", "METAMASK_GETPROVIDERSTATE", "ETH_REQUESTACCOUNTS", "clean", "state", "context", "channelConfig", "undefined", "ready", "originatorConnectStarted", "ConnectionStatus", "EventType", "InternalEventType", "KeyExchangeMessageType", "MessageType", "TrackingEvents", "EventEmitter2", "communicationLayer", "logging", "super", "keysExchanged", "step", "KEY_HANDSHAKE_NONE", "myECIES", "e<PERSON><PERSON><PERSON><PERSON>", "eciesInstance", "myPublicKey", "keyExchange<PERSON><PERSON><PERSON>", "setOtherPublic<PERSON>ey", "on", "KEY_EXCHANGE", "onKeyExchangeMessage", "bind", "keyExchangeMsg", "relayPersistence", "remote", "message", "emit", "KEY_INFO", "type", "KEY_HANDSHAKE_SYN", "checkStep", "K<PERSON>Y_HANDSHAKE_ACK", "pubkey", "sendMessage", "KEY_HANDSHAKE_SYNACK", "setStep", "KEYS_EXCHANGED", "resetKeys", "start", "isOriginator", "force", "protocolVersion", "v2Protocol", "v", "KEY_HANDSHAKE_START", "stepList", "indexOf", "setRelayPersistence", "localKey", "otherKey", "setKeysExchanged", "areKeysExchanged", "getMyPublicKey", "getOtherPublic<PERSON>ey", "otherPubKey", "encryptMessage", "Error", "decryptMessage", "buf", "keyInfo", "handleJoinChannelResults", "instance", "result", "channelId", "TERMINATE", "persistence", "<PERSON><PERSON><PERSON>", "rejected", "disconnect", "terminate", "REJECTED", "emitServiceStatusEvent", "_a", "getKeyExchange", "_b", "keyExchange", "authorized", "AUTHORIZED", "storageManager", "validUntil", "Date", "now", "_d", "socket", "PING", "id", "clientType", "persistChannelConfig", "setConnectionStatus", "LINKED", "CHANNEL_PERSISTENCE", "_e", "event", "CONNECTED", "CONNECTED_MOBILE", "originatorInfo", "sdkVersion", "comm<PERSON>ayer", "communicationLayerPreference", "commLayerVersion", "packageJson", "version", "walletVersion", "_f", "walletInfo", "communicationServerUrl", "wait", "ms", "Promise", "resolve", "setTimeout", "waitForRpc", "rpcId_1", "rpc_1", "args_1", "rpcId", "rpc", "interval", "startTime", "hasTimedout", "elapsedTime", "reconnectSocket", "isReconnecting", "connected", "reconnectionAttempts", "resumed", "connect", "SOCKET_RECONNECT", "reject", "JOIN_CHANNEL", "runtimeError", "encryptAndSendMessage", "encryptedMessage", "messageToSend", "plaintext", "hasPlaintext", "manualDisconnect", "MESSAGE", "success", "PromiseType", "lcLogguedRPCs", "map", "toLowerCase", "handleSendMessage", "startsWith", "handleKeyHandshake", "validateKeyExchange", "rpcMethodTracker", "timestamp", "RPC_UPDATE", "trackRpcMethod", "sent", "analytics", "includes", "_c", "SDK_RPC_REQUEST", "rpcCheckPromise", "then", "RPC_CHECK", "checkForSkippedRpcPromise", "nextRpcId", "lastRpcId", "waitForNextRpcCall", "SKIPPED_RPC", "winner", "race", "rpcCheck", "rpcResult", "errorMessage", "jsonrpc", "name", "handleRpcReplies", "channelEventListenerMap", "CLIENTS_CONNECTED", "handler", "_id", "clientsPaused", "_h", "_j", "_k", "clientsConnected", "CHANNEL_CREATED", "CLIENTS_DISCONNECTED", "CONFIG", "config", "rawMsg", "ackId", "isEncryptedMessage", "canDecrypt", "_g", "_l", "_m", "decryptedMessage", "_o", "messageReceived", "parse", "_p", "MESSAGE_ACK", "PAUSE", "rpcMessage", "initialRPCMethod", "SDK_RPC_REQUEST_DONE", "code", "_s", "_t", "DISCONNECTED", "numberUsers", "CLIENTS_WAITING", "keyExchangeEventListenerMap", "eciesState", "serviceStatus", "SERVICE_STATUS", "setupChannelListeners", "io", "attempt", "reason", "SOCKET_DISCONNECTED", "handleDisconnect", "for<PERSON>ach", "options", "focusListenerAdded", "removeFocusListener", "reconnect", "serviceLayer", "initSocket", "socketOptions", "autoConnect", "transports", "withCredentials", "url", "window", "document", "hasFocus", "focusHandler", "addEventListener", "removeEventListener", "setupSocketFocusListener", "keyExchangeInitParameter", "sendPublicKey", "createChannel", "uuidv4", "pubKey", "privKey", "connectToChannel", "withKeyExchange", "key<PERSON><PERSON><PERSON>", "KE<PERSON>_HANDSHAKE_CHECK", "ping", "pause", "isConnected", "resume", "remoteIsOriginator", "hasRelayPersistence", "READY", "getRPCMethodTracker", "removeAllListeners", "PlatformType", "AutoConnectType", "handleAuthorizedEvent", "waitForWalletVersion", "compareValue", "localeCompare", "isSecurePlatform", "platformType", "MobileWeb", "ReactNative", "MetaMaskMobileWebview", "handleChannelCreatedEvent", "handleClientsConnectedEvent", "requestEvent", "REQUEST", "REQUEST_MOBILE", "reconnection", "RECONNECT", "originatorInfoSent", "handleClientsDisconnectedEvent", "handleClientsWaitingEvent", "WAITING", "autoConnectOptions", "timeout", "timeoutId", "TIMEOUT", "clearTimeout", "handleKeysExchangedEvent", "lastActiveDate", "newChannelConfig", "lastActive", "getTime", "setLastActiveDate", "paused", "ORIGINATOR_INFO", "originator", "handleMessageEvent", "_message", "WALLET_INFO", "handleWalletInfoMessage", "WALLET_INIT", "accounts", "chainId", "deeplinkProtocolAvailable", "Boolean", "deeplinkProtocol", "persistAccounts", "persist<PERSON><PERSON><PERSON><PERSON>d", "handleWalletInitMessage", "handleTerminateMessage", "PAUSED", "handlePauseMessage", "CLIENTS_READY", "handleReadyMessage", "OTP", "otpAnswer", "SDK_RPC_CALL", "handleOtpMessage", "handleAuthorizedMessage", "handleOriginatorInfoMessage", "onCommunicationLayerMessage", "handleSocketReconnectEvent", "handleSocketDisconnectedEvent", "handleFullPersistenceEvent", "initSocketService", "CommunicationLayerPreference", "SOCKET", "URL", "title", "dappMetadata", "defaultDappId", "dappId", "location", "hostname", "source", "icon", "iconUrl", "base64Icon", "platform", "apiVersion", "connector", "eventsMapping", "eventType", "TERMINATED", "terminated", "persist", "sessionDuration", "_connectionStatus", "_options", "storage", "setMaxListeners", "duration", "storageOptions", "remoteLayer", "initFromDappStorage", "getPersistedChannelConfig", "originatorSessionConnect", "generateChannelIdConnect", "channel", "arguments", "validate", "once", "handleAuthorization", "testStorage", "res", "hasDeeplinkProtocol", "getChannelConfig", "isReady", "isAuthorized", "isPaused", "getCommunicationLayer", "testLogger", "connectionStatus", "CONNECTION_STATUS", "_", "getServiceStatus", "getConnectionStatus", "getVersion", "getChannelId", "rejectChannel"], "mappings": "yQAMA,MAAMA,EAAyBC,EAAKC,QAAC,qBAM/BC,EAAqBF,EAAKC,QAAC,uBAM3BE,EAAmBH,EAAKC,QAAC,eAMzBG,EAAoBJ,EAAKC,QAAC,6BAEhCF,EAAuBM,MAAQ,WAC/BH,EAAmBG,MAAQ,UAC3BF,EAAiBE,MAAQ,UACzBD,EAAkBC,MAAQ,UAMnB,MAAMC,EAAS,CACpBC,YAAaR,EACbS,cAAeN,EACfO,MAAON,EACPO,oBAAqBN,GCrBvB,IAEIO,EAFAC,EAAoC,GACpCC,EAA+B,SAgFtBC,EAAgB,CAC3BC,EACAC,IACEC,EAAAA,eAAA,OAAA,OAAA,GAAA,YACFN,EAAYK,EA/EZH,EAAWK,KAkFCH,GAvEd,SAAkCA,sDAKhC,IAAKJ,IAAcI,EACjB,QAbJ,WACE,MAAMI,EAAON,EACbA,EAAaD,EACbA,EAAkBO,CACpB,CAaEC,GAEA,MAAMC,EAAYV,EAAUW,SAAS,KACjC,GAAGX,OACH,GAAGA,QAEDY,EAAUC,OAAAC,OAAA,CAAA,EAEPV,GAIT,UAHOQ,EAAWG,OAGdX,EAAWW,OACb,IAAK,MAAOC,EAAKC,KAAUJ,OAAOK,QAAQd,EAAWW,QACnDH,EAAWI,GAAOC,EAItB,MAAME,EAAOC,KAAKC,UAAUT,GAE5BjB,EAAOI,oBACL,gCAAgCE,EAAgBqB,8BAA8BZ,KAGhF,IACE,MAAMa,QAAiBC,EAAUlC,QAACoB,EAAW,CAC3Ce,OAAQ,OACRC,QAAS,CACPC,OAAQ,mBACR,eAAgB,oBAElBR,SAGIS,QAAaL,EAASK,OAC5BjC,EAAOI,oBAAoB,kCAAkC6B,KAI7D3B,EAAgBqB,OAAS,EACzB,MAAOO,OAGV,CAmBCC,CAAmB1B,GAAY2B,OAAM,QAGvC,UCzFaC,EAKX,WAAAC,CAAYC,GAFJC,KAAOC,SAAG,GAGZF,aAAK,EAALA,EAAO7C,QACTA,EAAKC,QAAC+C,OAAO,eAIbF,KAAKG,OADHJ,aAAK,EAALA,EAAOK,YACIC,EAAAA,WAAWC,QAAQP,EAAMK,YAEzB,IAAIC,aAGnB7C,EAAOG,MACL,6CACAqC,KAAKG,MAAMI,SAGb/C,EAAOG,MACL,6CACAqC,KAAKG,MAAMK,UAAUD,SAEvB/C,EAAOG,MAAM,kCAAmCqC,MAQlD,aAAAS,GACET,KAAKG,MAAQ,IAAIE,aAQnB,YAAAK,GACE,OAAOV,KAAKG,MAAMK,UAAUD,QAU9B,OAAAI,CAAQC,EAAcC,GACpB,IAAIC,EAAkBF,EACtB,GAAIZ,KAAKC,QACP,IACEzC,EAAOG,MAAM,0CAA2CkD,GACxD,MAAME,EAAUC,EAAAA,OAAOC,KAAKL,GACtBM,EAAgBP,EAAAA,QAAQE,EAAgBE,GAC9CD,EAAkBE,EAAMA,OAACC,KAAKC,GAAeC,SAAS,UACtD,MAAOC,GAKP,MAJA5D,EAAOG,MAAM,oCAAqCyD,GAClD5D,EAAOG,MAAM,+BAAgCqC,KAAKG,MAAMI,SACxD/C,EAAOG,MAAM,4BAA6BiD,GAC1CpD,EAAOG,MAAM,gCAAiCkD,GACxCO,EAGV,OAAON,EAST,OAAAO,CAAQH,GACN,IAAII,EAAkBJ,EACtB,GAAIlB,KAAKC,QACP,IACEzC,EAAOG,MAAM,sCAAuCqC,KAAKG,MAAMI,SAC/D,MAAMQ,EAAUC,EAAAA,OAAOC,KAAKC,EAAcC,WAAY,UAGtDG,EAFkBD,EAAAA,QAAQrB,KAAKG,MAAMI,QAASQ,GAElBI,WAC5B,MAAOzB,GAIP,MAHAlC,EAAOG,MAAM,mCAAoC+B,GACjDlC,EAAOG,MAAM,+BAAgCqC,KAAKG,MAAMI,SACxD/C,EAAOG,MAAM,qCAAsCuD,GAC7CxB,EAIV,OAAO4B,EAGT,UAAAC,GACE,MAAO,CACLC,QAASxB,KAAKG,MAAMI,QACpBkB,OAAQzB,KAAKG,MAAMK,UAAUD,SAIjC,QAAAY,GACE3D,EAAOG,MAAM,sBAAuBqC,KAAKuB,i0HC/HtC,MAAMG,EAAqB,2CACrBC,EAA4B,CAAC,aAI7BC,EAA6B,OAG7BC,EAA2B,IAQ3BC,EAAc,CACzBC,0BAA2B,4BAC3BC,oBAAqB,uBCTjB,SAAUC,EAAMC,GACpB,MAAMC,QAAEA,GAAYD,EAEpB1E,EAAOI,oBACL,0CAA0CuE,KAG5CD,EAAME,mBAAgBC,EACtBH,EAAMI,OAAQ,EACdJ,EAAMK,0BAA2B,CACnC,CCnBA,IAAYC,ECAAC,ECAAC,ECAAC,ECAAC,ECAAC,ELAAL,QAAAA,sBAAAA,GAAAA,EAAAA,QAAgBA,mBAAhBA,yBAcX,CAAA,IAZC,aAAA,eAEAA,EAAA,QAAA,UAEAA,EAAA,QAAA,UAGAA,EAAA,OAAA,SAEAA,EAAA,OAAA,SAEAA,EAAA,WAAA,aCbUC,QAAAA,eAAAA,GAAAA,EAAAA,QAASA,YAATA,kBA8BX,CAAA,IA5BC,SAAA,WACAA,EAAA,eAAA,iBACAA,EAAA,gBAAA,kBACAA,EAAA,WAAA,aACAA,EAAA,eAAA,iBACAA,EAAA,aAAA,eACAA,EAAA,WAAA,aACAA,EAAA,gBAAA,kBACAA,EAAA,kBAAA,oBACAA,EAAA,qBAAA,uBACAA,EAAA,gBAAA,kBACAA,EAAA,cAAA,gBACAA,EAAA,SAAA,WACAA,EAAA,YAAA,cACAA,EAAA,oBAAA,sBACAA,EAAA,OAAA,SACAA,EAAA,YAAA,MACAA,EAAA,oBAAA,sBAEAA,EAAA,iBAAA,mBACAA,EAAA,IAAA,MAEAA,EAAA,aAAA,eAEAA,EAAA,WAAA,aACAA,EAAA,kBAAA,oBACAA,EAAA,QAAA,UACAA,EAAA,UAAA,YC7BF,SAAYC,GAIVA,EAAA,aAAA,cACD,CALD,CAAYA,IAAAA,EAKX,CAAA,ICLWC,QAAAA,4BAAAA,GAAAA,EAAAA,QAAsBA,yBAAtBA,+BAQX,CAAA,IAPC,oBAAA,sBACAA,EAAA,oBAAA,sBACAA,EAAA,kBAAA,oBACAA,EAAA,qBAAA,uBACAA,EAAA,kBAAA,oBACAA,EAAA,qBAAA,uBACAA,EAAA,mBAAA,OGcI,MAAOlF,UAAoBqF,EAAAA,cAkB/B,WAAAhD,EAAYiD,mBACVA,EAAkBlC,eAClBA,EAAcsB,QACdA,EAAOhC,MACPA,EAAK6C,QACLA,IAEAC,QAxBMjD,KAAakD,eAAG,EAUhBlD,KAAAmD,KACNR,QAAAA,uBAAuBS,mBAIjBpD,KAAK9C,OAAG,EAWd8C,KAAKmC,QAAUA,EAEfnC,KAAK+C,mBAAqBA,GAEtB5C,aAAK,EAALA,EAAOC,aAAcS,IACvBrD,EAAOC,YACL,4CAA4CoD,+BAC5CV,GAGFH,KAAKkD,eAAgB,GAGvBlD,KAAKqD,QAAU,IAAIxD,EAAKnB,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EAAMwB,GAAO,CAAAjD,MAAO8F,aAAO,EAAPA,EAASM,cACrDtD,KAAK+C,mBAAmBb,MAAMqB,cAAgBvD,KAAKqD,QACnDrD,KAAKwD,YAAcxD,KAAKqD,QAAQ3C,eAEhCV,KAAK9C,OAAsC,KAA9B8F,aAAO,EAAPA,EAASS,kBAElB5C,GACFb,KAAK0D,kBAAkB7C,GAGzBb,KAAK+C,mBAAmBY,GACtBjB,EAAkBkB,aAClB5D,KAAK6D,qBAAqBC,KAAK9D,OAI5B,oBAAA6D,CAAqBE,GAG1B,MAAMC,iBAAEA,GAAqBhE,KAAK+C,mBAAmBkB,OAAO/B,MAO5D,GALA1E,EAAOC,YACL,iDAAiDuC,KAAKmC,yBAAyBnC,KAAKkD,kCAAkCc,IACtHD,GAGEC,EAIF,YAHAxG,EAAOC,YACL,8GAKJ,MAAMyG,QAAEA,GAAYH,EAChB/D,KAAKkD,eACP1F,EAAOC,YACL,iDAAiDuC,KAAKmC,4DAA4DnC,KAAKmD,oBAAoBnD,KAAKa,kBAMpJb,KAAKmE,KAAK1B,QAAAA,UAAU2B,SAAUF,EAAQG,MAElCH,EAAQG,OAAS1B,QAAsBA,uBAAC2B,mBAC1CtE,KAAKuE,UAAU,CACb5B,QAAAA,uBAAuBS,mBACvBT,QAAAA,uBAAuB6B,oBAGzBhH,EAAOC,YACL,0DACAyG,GAGEA,EAAQO,QACVzE,KAAK0D,kBAAkBQ,EAAQO,QAGjCzE,KAAK+C,mBACF2B,YAAY,CACXL,KAAM1B,QAAsBA,uBAACgC,qBAC7BF,OAAQzE,KAAKwD,cAEd5D,OAAOF,IACNlC,EAAOC,YACL,2EACAiC,EACD,IAGLM,KAAK4E,QAAQjC,QAAsBA,uBAAC6B,oBAC3BN,EAAQG,OAAS1B,QAAsBA,uBAACgC,sBAEjD3E,KAAKuE,UAAU,CACb5B,QAAAA,uBAAuBgC,qBACvBhC,QAAAA,uBAAuB6B,kBACvB7B,QAAAA,uBAAuBS,qBAGzB5F,EAAOC,YACL,8DAGEyG,EAAQO,QACVzE,KAAK0D,kBAAkBQ,EAAQO,QAGjCzE,KAAK+C,mBACF2B,YAAY,CACXL,KAAM1B,QAAsBA,uBAAC6B,oBAE9B5E,OAAOF,IACNlC,EAAOC,YACL,wEACAiC,EACD,IAELM,KAAKkD,eAAgB,EAErBlD,KAAK4E,QAAQjC,QAAsBA,uBAAC6B,mBACpCxE,KAAKmE,KAAK1B,QAASA,UAACoC,iBACXX,EAAQG,OAAS1B,QAAsBA,uBAAC6B,oBACjDhH,EAAOC,YACL,sFAGFuC,KAAKuE,UAAU,CACb5B,QAAAA,uBAAuB6B,kBACvB7B,QAAAA,uBAAuBS,qBAEzBpD,KAAKkD,eAAgB,EAErBlD,KAAK4E,QAAQjC,QAAsBA,uBAAC6B,mBACpCxE,KAAKmE,KAAK1B,QAASA,UAACoC,iBAIxB,SAAAC,CAAU3E,GACRH,KAAKiC,QACLjC,KAAKqD,QAAU,IAAIxD,EAAMM,GAG3B,KAAA8B,GACEzE,EAAOC,YACL,kCAAkCuC,KAAKmC,iCAGzCnC,KAAK4E,QAAQjC,QAAsBA,uBAACS,oBACpCpD,KAAKmE,KAAK1B,QAAAA,UAAU2B,SAAUpE,KAAKmD,MACnCnD,KAAKkD,eAAgB,EAKvB,KAAA6B,EAAMC,aACJA,EAAYC,MACZA,IAKA,MAAMjB,iBAAEA,EAAgBkB,gBAAEA,GACxBlF,KAAK+C,mBAAmBkB,OAAO/B,MAE3BiD,EAAaD,GAAmB,EAElClB,EACFxG,EAAOC,YACL,gGASJD,EAAOC,YACL,kCAAkCuC,KAAKmC,2BAA2B+C,kBAAgCF,UAAqBhF,KAAKmD,cAAc8B,sBAA0BjB,mBAAkChE,KAAKkD,iBAGxM8B,IA4CFhF,KAAKkD,eACHlD,KAAKmD,OAASR,QAAAA,uBAAuBS,oBACpCpD,KAAKmD,OAASR,QAAsBA,uBAACgC,uBACxCM,GAcHzH,EAAOC,YACL,kCAAkCuC,KAAKmC,wCAAwC8C,cAAkBjF,KAAKmD,OACtGnD,KAAKmD,MAGPnD,KAAKiC,QAELjC,KAAK4E,QAAQjC,QAAsBA,uBAACgC,sBAEpC3E,KAAK+C,mBACF2B,YAAY,CACXL,KAAM1B,QAAsBA,uBAAC2B,kBAC7BG,OAAQzE,KAAKwD,YACb4B,ER3RwB,IQ6RzBxF,OAAOF,IACNlC,EAAOC,YACL,yDACAiC,EACD,KA9BHlC,EAAOC,YACL,kCACEuC,KAAKmC,mCAELnC,KAAKkD,cAAgB,OAAS,4BAEhClD,KAAKmD,MAtDFnD,KAAKkD,gBAA2B,IAAV+B,EAiCzBzH,EAAOC,YACL,mFAjCE0H,EAEFnF,KAAK+C,mBACF2B,YAAY,CACXL,KAAM1B,QAAsBA,uBAACgC,qBAC7BF,OAAQzE,KAAKwD,YACb4B,ER1NkB,IQ4NnBxF,OAAOF,IACNlC,EAAOC,YACL,4DACAiC,EACD,KAOLM,KAAK+C,mBACF2B,YAAY,CACXL,KAAM1B,QAAsBA,uBAAC0C,sBAE9BzF,OAAOF,IACNlC,EAAOC,YACL,2DACAiC,EACD,IAELM,KAAKiC,UAoDb,OAAA2C,CAAQzB,GACNnD,KAAKmD,KAAOA,EACZnD,KAAKmE,KAAK1B,kBAAU2B,SAAUjB,GAGhC,SAAAoB,CAAUe,GACJA,EAASnG,OAAS,GAAKmG,EAASC,QAAQvF,KAAKmD,KAAKhC,YAQxD,mBAAAqE,EAAoBC,SAClBA,EAAQC,SACRA,IAKA1F,KAAKa,eAAiB6E,EACtB1F,KAAKqD,QAAU,IAAIxD,EAAM,CAAEO,WAAYqF,EAAUvI,MAAO8C,KAAK9C,QAC7D8C,KAAKkD,eAAgB,EAGvB,gBAAAyC,CAAiBzC,GACflD,KAAKkD,cAAgBA,EAGvB,gBAAA0C,GACE,OAAO5F,KAAKkD,cAGd,cAAA2C,GACE,OAAO7F,KAAKwD,YAGd,iBAAAsC,GACE,OAAO9F,KAAKa,eAGd,iBAAA6C,CAAkBqC,GAChBvI,EAAOC,YAAY,kCAAmCsI,GAEtD/F,KAAKa,eAAiBkF,EAGxB,cAAAC,CAAe9B,GACb,IAAKlE,KAAKa,eACR,MAAM,IAAIoF,MACR,4DAGJ,OAAOjG,KAAKqD,QAAQ1C,QAAQuD,EAASlE,KAAKa,gBAG5C,cAAAqF,CAAehC,GACb,IAAKlE,KAAKa,eACR,MAAM,IAAIoF,MACR,4DAIJ,OAAOjG,KAAKqD,QAAQhC,QAAQ6C,GAG9B,UAAA3C,GACE,MAAO,CACLpB,MAAYzB,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EAAAqB,KAAKqD,QAAQ9B,cAAY,CAAEwE,YAAa/F,KAAKa,iBACzDsC,KAAMnD,KAAKmD,KACXD,cAAelD,KAAK4F,oBAIxB,QAAAzE,GACE,MAAMgF,EAAM,CACVC,QAASpG,KAAKuB,aACd2B,cAAelD,KAAKkD,cACpBC,KAAMnD,KAAKmD,MAEb,OAAOlE,KAAKC,UAAUiH,IFpYdvD,QAAAA,iBAAAA,GAAAA,EAAAA,QAAWA,cAAXA,oBAsBX,CAAA,IApBC,UAAA,YACAA,EAAA,OAAA,SACAA,EAAA,MAAA,QACAA,EAAA,UAAA,YACAA,EAAA,QAAA,UACAA,EAAA,YAAA,cACAA,EAAA,YAAA,cACAA,EAAA,gBAAA,kBACAA,EAAA,MAAA,QACAA,EAAA,IAAA,MAIAA,EAAA,WAAA,aAIAA,EAAA,KAAA,OAEAA,EAAA,MAAA,QCrBUC,QAAAA,oBAAAA,GAAAA,EAAAA,QAAcA,iBAAdA,uBAgBX,CAAA,IAfC,QAAA,8BACAA,EAAA,eAAA,qCACAA,EAAA,UAAA,gCACAA,EAAA,UAAA,6BACAA,EAAA,iBAAA,oCACAA,EAAA,WAAA,4BACAA,EAAA,SAAA,0BACAA,EAAA,WAAA,4BACAA,EAAA,aAAA,mBACAA,EAAA,kBAAA,oBACAA,EAAA,gBAAA,kBACAA,EAAA,yBAAA,2BACAA,EAAA,qBAAA,uBACAA,EAAA,uBAAA,yBACAA,EAAA,sBAAA,wBEKK,MAAMwD,EAA2B,CACtCC,EACA5G,EACA6G,IACEpI,EAAAA,eAAA,OAAA,OAAA,GAAA,4BACF,MAAM8F,OAAEA,EAAM/B,MAAEA,GAAUoE,GACpBE,UAAEA,EAASxB,aAAEA,GAAiB9C,EAEpC,GAAc,qBAAVxC,EAKF,OAJAlC,EAAOE,cACL,qCAAqC8I,qBAEvCF,EAASnC,KAAK1B,QAASA,UAACgE,WAI1B,IAAKF,EAIH,YAHA/I,EAAOE,cACL,mDAAmD8I,KAKvD,MAAME,YAAEA,EAAWC,UAAEA,EAASC,SAAEA,GAAaL,EAM7C,GAJA/I,EAAOE,cACL,qCAAqC8I,iBAAyBE,eAAyBC,cAAsBC,KAG3GA,EAOF,OANApJ,EAAOE,cACL,qCAAqC8I,oBAEjCF,EAASrC,OAAO4C,WAAW,CAAEC,WAAW,IAC9CR,EAASrC,OAAOE,KAAK1B,QAASA,UAACsE,SAAU,CAAEP,mBAC3CF,EAASrC,OAAO+C,yBAIlB,GAAIL,KAAwC,UAA1B1C,EAAO/B,MAAME,qBAAa,IAAA6E,OAAA,EAAAA,EAAEvB,UAAU,CAClCY,EAASY,iBACjBxD,kBAAkBiD,WAC9BQ,EAAAb,EAASpE,MAAMkF,4BAAazB,kBAAiB,GAC7C1B,EAAO/B,MAAMI,OAAQ,EACrB2B,EAAO/B,MAAMmF,YAAa,EAC1BpD,EAAOE,KAAK1B,QAASA,UAAC6E,YAEtB,MAAMvE,mBAAEA,EAAkBwE,eAAEA,GAAmBtD,EAAO/B,MAEhDE,iCACD6B,EAAO/B,MAAME,gBAChBoE,oBAAWvC,EAAO/B,MAAMsE,yBAAa,GACrCgB,WAAYC,KAAKC,MAAQ9F,EACzB6D,SAAU1C,aAAA,EAAAA,EAAoBxB,aAAapB,MAAMqB,QACjDkE,SAAUiB,IAGZL,EACG5B,YAAY,CACXL,KAAM1B,QAAsBA,uBAAC6B,oBAE9B5E,OAAOwB,IACY,IAGC,QAAvBuG,EAAArB,EAASpE,MAAM0F,cAAQ,IAAAD,GAAAA,EAAAxD,KAAKvB,QAAAA,YAAYiF,KAAM,CAC5CC,GAAItB,EACJuB,WAAY/C,EAAe,OAAS,SACpC7C,QAAS,uBACT+B,QAAS,WAGLqD,aAAA,EAAAA,EAAgBS,qBAAqB5F,GAC3C6B,EAAO+C,yBACP/C,EAAOgE,oBAAoBzF,QAAgBA,iBAAC0F,QAG1CxB,IACFJ,EAASnC,KAAK1B,QAASA,UAAC0F,6BACxBC,EAAA9B,EAASpE,MAAMkF,4BAAazB,kBAAiB,GAC7C1B,EAAO/B,MAAMI,OAAQ,EACrB2B,EAAO/B,MAAMmF,YAAa,EAC1BpD,EAAOE,KAAK1B,QAASA,UAAC6E,YAEtBtJ,EAEIU,OAAAC,OAAAD,OAAAC,OAAA,CAAAmJ,GAAItB,QAAAA,EAAa,GACjB6B,MAAOrD,EACHnC,uBAAeyF,UACfzF,uBAAe0F,kBAChBjC,EAASrC,OAAO/B,MAAMsG,gBACzB,CAAAC,WAAYnC,EAASrC,OAAO/B,MAAMuG,WAClCC,UAAWpC,EAASpE,MAAMyG,6BAC1BC,iBAAkBC,EAAYC,QAC9BC,cAA+C,QAAhCC,EAAA1C,EAASrC,OAAO/B,MAAM+G,kBAAU,IAAAD,OAAA,EAAAA,EAAEF,UAEnD5G,EAAMgH,wBACNtJ,OAAOwB,IACoC,IAGjD,IClHa+H,EAAQC,GACZ,IAAIC,SAASC,IAClBC,WAAWD,EAASF,EAAG,IAIdI,EAAa,CAIEC,EAAAC,KAAAC,IAH1BxL,EAAAA,eAAA,EAAA,CAAAsL,EAAAC,KAAAC,QAAA,GAAA,UAAAC,EACAC,EACAC,EAAW,KAEX,IAAIvD,EACJ,MAAMwD,EAAYtC,KAAKC,MAEvB,IAAIsC,GAAc,EAElB,MAAQA,GAAa,CAMnB,GAJAA,EADiBvC,KAAKC,MAAQqC,EVZD,IUgB7BxD,EAASsD,EAAID,QACcvH,IAAvBkE,EAAO0D,YACT,OAAO1D,QAEH4C,EAAKW,GAEb,MAAM,IAAI7D,MAAM,OAAO2D,cACzB,ICfaM,EAAyB5D,GAA2BnI,iBAAA,OAAA,OAAA,GAAA,YAC/D,MAAM+D,MAAEA,GAAUoE,GACZsB,OAAEA,EAAMpB,UAAEA,EAASrE,QAAEA,EAAO6C,aAAEA,EAAYmF,eAAEA,GAAmBjI,EAErE,GAAIiI,EAKF,OAJA3M,EAAOE,cACL,gFACA4I,IAEK,EAGT,IAAKsB,EAKH,OAJApK,EAAOE,cACL,2DACA4I,IAEK,EAGT,IAAKE,EAEH,OAAO,EAGT,MAAM4D,UAAEA,GAAcxC,EACtB1F,EAAMiI,gBAAiB,EACvBjI,EAAMmI,qBAAuB,EAE7B7M,EAAOE,cACL,gDAAgD0M,qDAChD9D,GAGF,IACE,KX7CoC,EW6C7BpE,EAAMmI,sBAAiD,CAW5D,GAVA7M,EAAOE,cACL,8CACEwE,EAAMmI,qBAAuB,SAE/B/D,SAII6C,EAAK,KAEPvB,EAAOwC,UAWT,OAVA5M,EAAOE,cACL,0DAGFkK,EAAOzD,KAAKvB,QAAWA,YAACiF,KAAM,CAC5BC,GAAItB,EACJuB,WAAY/C,EAAe,OAAS,SACpC7C,QAAS,oBACT+B,QAAS,MAEJ,EAGThC,EAAMoI,SAAU,EAChB1C,EAAO2C,UAEPjE,EAASnC,KAAK1B,QAASA,UAAC+H,kBAExB,IAsBE,SArBM,IAAInB,SAAc,CAACC,EAASmB,KAChC7C,EAAOzD,KACL1B,QAASA,UAACiI,aACV,CACElE,YACArE,QAAS,GAAGA,iBACZ4F,WAAY/C,EAAe,OAAS,WAEtC,CAAOtF,EAAsB6G,IAA8BpI,EAAAA,eAAA,OAAA,OAAA,GAAA,YACzD,UACQkI,EAAyBC,EAAU5G,EAAO6G,GAChD+C,IACA,MAAOqB,GACPF,EAAOE,QAGZ,UAIGxB,EAAK,KACPvB,EAAOwC,UAMT,OALA5M,EAAOE,cACL,sCACEwE,EAAMmI,qBAAuB,MAG1B,EAET,MAAO3K,GACPlC,EAAOE,cACL,qCACEwE,EAAMmI,qBAAuB,KAE/B3K,GAIJwC,EAAMmI,sBAAwB,EXlHI,EWmH9BnI,EAAMmI,6BACFlB,EAAK,MAOf,OAHA3L,EAAOE,cACL,yCAEK,EACC,QACRwE,EAAMiI,gBAAiB,EACvBjI,EAAMmI,qBAAuB,EAEjC,IC3HsB,SAAAO,EACpBtE,EACApC,4DAEA,MAAM2G,EAA6C,QAA1B5D,EAAAX,EAASpE,MAAMkF,mBAAW,IAAAH,OAAA,EAAAA,EAAEjB,eACnD/G,KAAKC,UAAUgF,IAEX4G,EAAgB,CACpBhD,GAAIxB,EAASpE,MAAMsE,UACnBrE,QAASmE,EAASpE,MAAMC,QACxB4F,WAAYzB,EAASpE,MAAM8C,aAAe,OAAS,SACnDd,QAAS2G,EACTE,UAAWzE,EAASpE,MAAM8I,aACtB/L,KAAKC,UAAUgF,QACf7B,GAYN,OATA7E,EAAOE,cACL,oDAAoD4I,EAASpE,MAAMC,UACnE2I,GAGE5G,EAAQG,OAASzB,QAAWA,YAAC6D,YAC/BH,EAASpE,MAAM+I,kBAAmB,GAG7B,IAAI5B,SAAQ,CAACC,EAASmB,WACN,QAArBxD,EAAAX,EAASpE,MAAM0F,cAAM,IAAAX,GAAAA,EAAE9C,KACrB1B,QAASA,UAACyI,QACVJ,GACA,CAACpL,EAAqBN,WAChBM,IACFlC,EAAOE,cACL,kDAAkDgC,KAEpD+K,EAAO/K,IAGTlC,EAAOE,cAAc,mCAAoC0B,GACzDkK,EAAyB,QAAjBrC,EAAA7H,aAAQ,EAARA,EAAU+L,eAAO,IAAAlE,GAAAA,EAAU,GAEtC,MAEJ,CCnDD,IAAKmE,GAAL,SAAKA,GACHA,EAAA,UAAA,WACAA,EAAA,YAAA,YACD,CAHD,CAAKA,IAAAA,EAGJ,CAAA,ICAM,MAAMC,EAAgB,CAC3B,sBACA,oBACA,sBACA,gBACA,4BACA,6BACA,uBACA,uBACA,uBACA,uBACA,kBACAC,KAAKhM,GAAWA,EAAOiM,gBAeH,SAAAC,EACpBlF,EACApC,gEAEA,IAAKoC,EAASpE,MAAMsE,UAKlB,MAJAhJ,EAAOE,cACL,4DAGI,IAAIuI,MAAM,0BAYlB,GATAzI,EAAOE,cACL,gDACE4I,EAASpE,MAAMC,4BACgC,QAA5B8E,EAAAX,EAASpE,MAAMkF,mBAAa,IAAAH,OAAA,EAAAA,EAAArB,qBACjD1B,GAG2C,QAAfiD,EAAAjD,aAAO,EAAPA,EAASG,YAAM,IAAA8C,OAAA,EAAAA,EAAAsE,WAAW,iBAItD,OC/CY,SACdnF,EACApC,SAEA1G,EAAOE,cACL,iDAAiD4I,EAASpE,MAAMC,UAChE+B,GAGqB,QAAvB+C,EAAAX,EAASpE,MAAM0F,cAAQ,IAAAX,GAAAA,EAAA9C,KAAK1B,QAAAA,UAAUyI,QAAS,CAC7CpD,GAAIxB,EAASpE,MAAMsE,UACnBrE,QAASmE,EAASpE,MAAMC,QACxB4F,WAAYzB,EAASpE,MAAM8C,aAAe,OAAS,SACnDd,WAEJ,CD+BIwH,CAAmBpF,EAAUpC,IACtB,GEhDK,SACdoC,EACApC,SAEA,KAC6B,QAA1B+C,EAAAX,EAASpE,MAAMkF,mBAAW,IAAAH,OAAA,EAAAA,EAAErB,sBAC5BU,EAASrC,OAAO/B,MAAM8B,iBAWvB,MATAxG,EAAOE,cACL,kDAAkD4I,EAASpE,MAAMC,mCACjE+B,GAOI,IAAI+B,MAAM,yBAEpB,CFgCE0F,CAAoBrF,EAAUpC,GGnDhB,SACdoC,EACApC,SAEA,MAAM5E,EAA4B,QAAnB2H,EAAA/C,aAAA,EAAAA,EAAS5E,cAAU,IAAA2H,EAAAA,EAAA,GAC5B2C,EAAQ1F,aAAA,EAAAA,EAAS4D,GACnBxB,EAASpE,MAAM8C,cAAgB4E,IACjCtD,EAASpE,MAAM0J,iBAAiBhC,GAAS,CACvC9B,GAAI8B,EACJiC,UAAWpE,KAAKC,MAChBpI,UAEFgH,EAASnC,KAAK1B,QAAAA,UAAUqJ,WAAYxF,EAASpE,MAAM0J,iBAAiBhC,IAExE,CHuCEmC,CAAezF,EAAUpC,GAEzB,MAAM8H,QAAapB,EAAsBtE,EAAUpC,GA+BnD,OA7BIoC,EAASrC,OAAO/B,MAAM+J,WAGtB3F,EAASrC,OAAO/B,MAAM8C,cACtBd,EAAQ5E,QACR+L,EAAca,SAAShI,EAAQ5E,OAAOiM,gBAEtCvN,EACE,CACE8J,GAAmC,QAA/BqE,EAAA7F,EAASrC,OAAO/B,MAAMsE,iBAAS,IAAA2F,EAAAA,EAAI,GACvC9D,MAAOxF,QAAcA,eAACuJ,gBACtBxN,OAAQ,CACNU,OAAQ4E,EAAQ5E,OAChB2B,KAAM,WAGVqF,EAASrC,OAAO/B,MAAMgH,wBACtBtJ,OAAOwB,IACwD,IDlEjD,SACpBkF,EACApC,4DAEA,MAAM0F,EAAQ1F,aAAA,EAAAA,EAAS4D,GACjBxI,EAA4B,QAAnB2H,EAAA/C,aAAA,EAAAA,EAAS5E,cAAU,IAAA2H,EAAAA,EAAA,GAElC,GAAIX,EAASpE,MAAM8C,cAAgB4E,EACjC,IACE,MAAMyC,EAAkB7C,EACtBI,EACAtD,EAASpE,MAAM0J,iBACf,KACAU,MAAM/F,IAAM,CAAQlC,KAAM+G,EAAYmB,UAAWhG,aAI7CiG,EAA4B,KAAYrO,EAAAA,UAAA6B,UAAA,OAAA,GAAA,YAE5C,MAAMyM,OHHoB,CAAAxF,GAM7B9I,EAAAA,eAAA,EAAA,CAAA8I,QAAA,GAAA,WANoC2C,MACvCA,EAAKtD,SACLA,IAKA,KACEA,EAASpE,MAAMwK,YAAc9C,QACAvH,IAA7BiE,EAASpE,MAAMwK,iBAETvD,EAAK,KAEb,OAAO7C,EAASpE,MAAMwK,SACxB,IGXgCC,CAAmB,CAAErG,WAAUsD,UACjDrD,QAAeiD,EACnBiD,EACAnG,EAASpE,MAAM0J,iBACf,KAGF,MAAO,CAAEvH,KAAM+G,EAAYwB,YAAarG,aATR,GAY5BsG,QAAexD,QAAQyD,KAAK,CAChCT,EACAG,IAGF,GAAIK,EAAOxI,OAAS+G,EAAYmB,UAAW,CACzC,MAAMQ,EAAWF,EAAOtG,OAExB/I,EAAOE,cACL,yCAAyCwG,EAAQ4D,MAAMxI,OAAYyN,EAAS9C,kBAC5E8C,EAASxG,YAEN,IAAIsG,EAAOxI,OAAS+G,EAAYwB,YAqBrC,MAAM,IAAI3G,MAAM,kCAAkC2D,KArBA,CAClD,MAOMoD,EACDtO,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EAAA2H,EAASpE,MAAM0J,iBAAiBhC,IACnC,CAAAlK,MAAO,IAAIuG,MAlEa,0BAoE1BK,EAASnC,KAAK1B,kBAAUqJ,WAAYkB,GAGpC,MAAMC,EAAe,CACnBrM,oCAAWoM,GAAS,CAAEE,QAAS,QAC/BC,KAAM,qBAER7G,EAASnC,KAAK1B,QAASA,UAACyI,QAAS,CAAEhH,QAAS+I,MAI9C,MAAO7L,GAKP,MAAMA,KAGX,CCCCgM,CAAiB9G,EAAUpC,GAAStE,OAAOwB,IACsB,IAG1D4K,IACR,CIlFD,MAAMqB,EAA0B,CAC9B,CACEhF,MAAO5F,QAASA,UAAC6K,kBACjBC,QCPY,SACdjH,EACAE,GAEA,OAAcgH,GAAerP,YAAA6B,UAAA,OAAA,GAAA,sCAC3B,MAAMgE,EACiD,QAArDmD,EAAmC,QAAnCF,EAAAX,EAASrC,OAAO/B,MAAME,qBAAa,IAAA6E,OAAA,EAAAA,EAAEjD,wBAAgB,IAAAmD,GAAAA,EAqBvD,GAnBA3J,EAAOE,cACL,qDACE4I,EAASpE,MAAMC,iCACSqE,uBAA+BxC,aACvDsC,EAASpE,MAAMoI,0BAEfhE,EAASpE,MAAMuL,+BAC2B,QAA1BtB,EAAA7F,EAASpE,MAAMkF,mBAAW,IAAA+E,OAAA,EAAAA,EAAEvG,mCAC5CU,EAASpE,MAAM8C,gBAKnBsB,EAASnC,KAAK1B,QAASA,UAAC6K,kBAAmB,CACzCtI,aAAcsB,EAASpE,MAAM8C,aAC7B9B,cAA2C,UAA5BoD,EAASpE,MAAMkF,mBAAa,IAAAO,OAAA,EAAAA,EAAA/B,mBAC3CzD,QAASmE,EAASpE,MAAMC,UAGtBmE,EAASpE,MAAMoI,QACZhE,EAASpE,MAAM8C,eAElBxH,EAAOE,cACL,qDACE4I,EAASpE,MAAMC,yDACuBmE,EAASpE,MAAMkF,kCAAaxB,gDAG5C,QAA1BoD,EAAA1C,EAASpE,MAAMkF,mBAAW,IAAA4B,GAAAA,EAAEjE,MAAM,CAChCC,uBAAcsB,EAASpE,MAAM8C,gCAIjCsB,EAASpE,MAAMoI,SAAU,OACpB,GAAIhE,EAASpE,MAAMuL,cACxBjQ,EAAOE,cACL,2GAEG,IAAK4I,EAASpE,MAAM8C,aAAc,CAEvC,MAAMC,GAASjB,EACfxG,EAAOE,cACL,qDACE4I,EAASpE,MAAMC,kDACsD,QAA5BuL,EAAApH,EAASpE,MAAMkF,mBAAa,IAAAsG,OAAA,EAAAA,EAAA9H,+BAA+BX,+BAGxGzH,EAAOE,cACL,qDACE4I,EAASpE,MAAMC,kDACsD,QAA5BwL,EAAArH,EAASpE,MAAMkF,mBAAa,IAAAuG,OAAA,EAAAA,EAAA/H,+BAA+BX,+BAK9E,QAA1B2I,EAAAtH,EAASpE,MAAMkF,mBAAW,IAAAwG,GAAAA,EAAE7I,MAAM,CAChCC,uBAAcsB,EAASpE,MAAM8C,6BAC7BC,UAIJqB,EAASpE,MAAM2L,kBAAmB,EAClCvH,EAASpE,MAAMuL,eAAgB,CACjC,GACF,GD/DE,CACEpF,MAAO5F,QAASA,UAACqL,gBACjBP,QEZY,SACdjH,EACAE,GAEA,OAAQsB,IACNtK,EAAOE,cACL,mDAAmD4I,EAASpE,MAAMC,+BAA+BqE,KACjGsB,GAEFxB,EAASnC,KAAK1B,kBAAUqL,gBAAiBhG,EAAG,CAEhD,GFGE,CACEO,MAAO5F,QAASA,UAACsL,qBACjBR,QGdY,SACdjH,EACAE,GAEA,MAAO,WACLF,EAASpE,MAAM2L,kBAAmB,EAClCrQ,EAAOE,cACL,yDAAyD4I,EAASpE,MAAMC,oCAAoCqE,MAG1GF,EAASrC,OAAO/B,MAAM8B,iBACxBxG,EAAOE,cACL,yDAAyD4I,EAASpE,MAAMC,oCAAoCqE,kEAK5GF,EAASpE,MAAM8C,eAAiBsB,EAASpE,MAAMuL,gBAErB,QAA5BxG,EAAAX,EAASpE,MAAMkF,mBAAa,IAAAH,GAAAA,EAAAhF,SAG9BqE,EAASnC,KAAK1B,kBAAUsL,qBAAsBvH,GAAU,CAE5D,GHRE,CAAE6B,MAAO5F,QAASA,UAACuL,OAAQT,QIxBb,SACdjH,EACAE,GAEA,OAAcyH,GAAuD9P,YAAA6B,UAAA,OAAA,GAAA,sBACnExC,EAAOE,cACL,6EAA6E8I,KAC7EyH,GAGF,MAAMvH,YAAEA,EAAWC,UAAEA,GAAcsH,EAE/B3H,EAASpE,MAAM8C,cAAgBsB,EAASrC,OAAO/B,MAAME,eACnD6L,EAAOtH,YAAcL,EAASrC,OAAO/B,MAAME,cAAcsD,WAC3DlI,EAAOE,cAAc,sBAAsBiJ,KAC3CL,EAASrC,OAAO/B,MAAME,cAAcsD,SAAWiB,EAC/CL,EAASY,iBAAiBxD,kBAAkBuK,EAAOtH,mBACnDM,EAAAX,EAASpE,MAAMkF,4BAAazB,kBAAiB,SACvCW,EAASrC,OAAOS,YAAY,CAChCL,KAAM1B,QAAsBA,uBAAC6B,0BAGzB8B,EAASrC,OAAOS,YAAY,CAChCL,KAAMzB,QAAWA,YAACiF,aAGsB,UAApCvB,EAASrC,OAAO/B,MAAMqF,sBAAc,IAAAJ,OAAA,EAAAA,EAAEa,qBAC1C1B,EAASrC,OAAO/B,MAAME,iBAKR,IAAhBsE,GACCJ,EAASrC,OAAO/B,MAAME,cAAc4B,mBAErCxG,EAAOE,cAAc,6BAA6BgJ,KAClDJ,EAASrC,OAAO/B,MAAME,cAAc4B,iBAAmB0C,EAEvDJ,EAASrC,OAAO/B,MAAM8B,kBAAmB,EACzCsC,EAASrC,OAAOE,KAAK1B,QAAAA,UAAU0F,qBAG/B7B,EAASrC,OAAO/B,MAAMmF,YAAa,EACnCf,EAASrC,OAAO/B,MAAMI,OAAQ,EAC9BgE,EAASrC,OAAOE,KAAK1B,QAAAA,UAAU6E,kBAEW,UAApChB,EAASrC,OAAO/B,MAAMqF,sBAAc,IAAA4E,OAAA,EAAAA,EAAEnE,qBAC1C1B,EAASrC,OAAO/B,MAAME,iBAGhBkE,EAASpE,MAAM8C,cACrBiJ,EAAOvH,cACTJ,EAASrC,OAAO/B,MAAM8B,kBAAmB,EACzCsC,EAASrC,OAAOE,KAAK1B,QAAAA,UAAU0F,qBAGrC,GACF,GJhCE,CAAEE,MAAO5F,QAASA,UAACyI,QAASqC,QKZd,SAAcjH,EAAyBE,GACrD,OAAQ0H,4CAKN,MAAMC,MAAEA,EAAKjK,QAAEA,EAAOxE,MAAEA,GAAUwO,EAC5BlK,EAA6D,QAA1CiD,EAAAX,EAASrC,OAAO/B,MAAM8B,wBAAoB,IAAAiD,GAAAA,EASnE,GAPAzJ,EAAOE,cACL,qDAAqDsG,cACnDsC,EAASpE,MAAMC,wBACAqE,mBAAqD,QAA1BW,EAAAb,EAASpE,MAAMkF,mBAAW,IAAAD,OAAA,EAAAA,EAAEvB,qBACxEsI,GAGExO,EAIF,MAHAlC,EAAOE,cAAc,mDACqB4I,EAASpE,MAAMC,+BAA+BzC,KAElF,IAAIuG,MAAMvG,GAGlB,MAAM0O,EAAwC,iBAAZlK,EAElC,IACGkK,IACDlK,aAAO,EAAPA,EAASG,QAAS1B,QAAsBA,uBAAC0C,oBACzC,CACA,GAAIrB,EAKF,OAYF,OATAxG,EAAOE,cACL,2CAA2C4I,EAASpE,MAAMC,+DAA+DmE,EAASpE,MAAM8C,eACxId,QAGwB,QAA1BiI,EAAA7F,EAASpE,MAAMkF,mBAAW,IAAA+E,GAAAA,EAAEpH,MAAM,CAChCC,uBAAcsB,EAASpE,MAAM8C,6BAC7BC,OAAO,KAKX,IAAKmJ,cAAsBlK,aAAA,EAAAA,EAASG,2BAAMoH,WAAW,kBAAkB,CACrE,GAAIzH,EAKF,OAYF,OATAxG,EAAOE,cACL,2CAA2C4I,EAASpE,MAAMC,0CAC1D+B,QAGFoC,EAASnC,KAAKzB,EAAkBkB,aAAc,CAC5CM,UACA/B,QAASmE,EAASpE,MAAMC,UAK5B,GAAIiM,KAAmD,UAA5B9H,EAASpE,MAAMkF,mBAAa,IAAA4B,OAAA,EAAAA,EAAApD,oBAAoB,CAGzE,IAAIyI,GAAa,EACjB,IACE7Q,EAAOE,cACL,2CAA2C4I,EAASpE,MAAMC,2DAE5DmM,EAAAhI,EAASpE,MAAMkF,4BAAalB,eAAehC,GAC3CmK,GAAa,EACb,MAAOjN,GAEP5D,EAAOE,cACL,2CAA2C4I,EAASpE,MAAMC,8BAC1Df,GAIJ,IAAIiN,EAiCF,OA1BI/H,EAASpE,MAAM8C,aACS,QAA1B2I,EAAArH,EAASpE,MAAMkF,mBAAW,IAAAuG,GAAAA,EAAE5I,MAAM,CAChCC,uBAAcsB,EAASpE,MAAM8C,+BAI/BsB,EACG5B,YAAY,CACXL,KAAM1B,QAAsBA,uBAAC0C,sBAE9BzF,OAAOwB,IAIL,SAKP5D,EAAOE,cACL,6DAC8B,QAA5B6Q,EAAAjI,EAASpE,MAAMkF,mBAAa,IAAAmH,OAAA,EAAAA,EAAAhN,aAAa4B,OAEf,QAA5BqL,EAAAlI,EAASpE,MAAMkF,mBAAa,IAAAoH,OAAA,EAAAA,EAAAjN,aAC5B2C,GA9BF1G,EAAOE,cACL,iEAEFgQ,EAAApH,EAASpE,MAAMkF,4BAAazB,kBAAiB,QA+B1C,IAAKyI,IAAsBlK,aAAA,EAAAA,EAASG,MAOzC,YADAiC,EAASnC,KAAK1B,kBAAUyI,QAAShH,GAInC,IAAKkK,EAMH,YADA9H,EAASnC,KAAK1B,kBAAUyI,QAAShH,GAInC,MAAMuK,EACwB,QAA5BC,EAAApI,EAASpE,MAAMkF,mBAAa,IAAAsH,OAAA,EAAAA,EAAAxI,eAAehC,GACvCyK,EAAkB1P,KAAK2P,MAAMH,QAAAA,EAAoB,MA0BvD,GAvBIN,IAASA,aAAA,EAAAA,EAAOhP,QAAS,IAC3B3B,EAAOE,cACL,2CAA2C4I,EAASpE,MAAMC,+BAA+BgM,eAAmB3H,KAGvF,QAAvBqI,EAAAvI,EAASpE,MAAM0F,cAAQ,IAAAiH,GAAAA,EAAA1K,KAAK1B,QAAAA,UAAUqM,YAAa,CACjDX,QACA3H,YACAuB,WAAYzB,EAASpE,MAAM8C,aAAe,OAAS,YAUrDsB,EAASpE,MAAMuL,eANbkB,aAAA,EAAAA,EAAiBtK,QAASzB,QAAAA,YAAYmM,MAWtCzI,EAASpE,MAAM8C,cAAgB2J,EAAgB/N,KAAM,CAEvD,MAAMoO,EAAaL,EAAgB/N,KAS7BqO,EAAmB3I,EAASpE,MAAM0J,iBAAiBoD,EAAWlH,IAEpE,GAAImH,EAAkB,CACpB,MAAMhF,EAAcxC,KAAKC,MAAQuH,EAAiBpD,UAClDrO,EAAOE,cACL,2CAA2C4I,EAASpE,MAAMC,gDAAgD6M,EAAWlH,aAAamH,EAAiB3P,uBAAuB2K,IAC1K0E,GAKArI,EAASrC,OAAO/B,MAAM+J,WACtBZ,EAAca,SAAS+C,EAAiB3P,OAAOiM,gBAE/CvN,+BAEI8J,aAAIxB,EAASrC,OAAO/B,MAAMsE,yBAAa,GACvC6B,MAAOxF,QAAAA,eAAeqM,qBAGtBzG,WAAYnC,EAASrC,OAAO/B,MAAMuG,WAClCG,iBAAkBC,EAAYC,SAC3BxC,EAASrC,OAAO/B,MAAMsG,gBAAc,CACvCO,wBAAezC,EAASrC,OAAO/B,MAAM+G,iCAAYH,QACjDlK,OAAQ,CACNU,OAAQ2P,EAAiB3P,OACzB2B,KAAM,YAGVqF,EAASrC,OAAO/B,MAAMgH,wBACtBtJ,OAAOwB,IACoC,IAG/C,MAAM4L,EAAStO,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EACVsQ,GAAgB,CACnB1I,OAAQyI,EAAWzI,OACnB7G,MAAOsP,EAAWtP,MACd,CACEyP,aAAMC,EAAAJ,EAAWtP,4BAAOyP,KACxBjL,gBAASmL,EAAAL,EAAWtP,4BAAOwE,cAE7B7B,EACJ4H,gBAEF3D,EAASpE,MAAM0J,iBAAiBoD,EAAWlH,IAAMkF,EACjD1G,EAASnC,KAAK1B,kBAAUqJ,WAAYkB,IAIxC1G,EAASnC,KAAK1B,QAASA,UAACyI,QAAS,CAAEhH,QAASyK,GAAkB,CAElE,GL5NE,CAAEtG,MAAO5F,QAASA,UAACsE,SAAUwG,QMhBf,SACdjH,EACAE,GAEA,OAAcgH,GAAerP,YAAA6B,UAAA,OAAA,GAAA,kBAEtBsG,EAASpE,MAAM8C,eAAgBsB,EAASrC,OAAO/B,MAAMI,OAO1D9E,EAAOE,cACL,oDAAoD4I,EAASpE,MAAMC,qBAAqBqE,kBAA0BF,EAASpE,MAAM8C,sBAAsBsB,EAASrC,OAAO/B,MAAMI,QAC7KgE,EAASrC,OAAO/B,MAAMsG,gBAIxBxK,EAEIU,OAAAC,OAAAD,OAAAC,OAAA,CAAAmJ,GAAItB,EACJ6B,MAAOxF,QAAcA,eAACkE,UACnBT,EAASrC,OAAO/B,MAAMsG,gBACzB,CAAAC,WAAYnC,EAASrC,OAAO/B,MAAMuG,WAClCC,UAAWpC,EAASpE,MAAMyG,6BAC1BC,iBAAkBC,EAAYC,QAC9BC,cAAiD,QAAlC9B,EAAAX,EAASrC,OAAO/B,MAAM+G,kBAAY,IAAAhC,OAAA,EAAAA,EAAA6B,UAEnDxC,EAASrC,OAAO/B,MAAMgH,wBACtBtJ,OAAOF,IAIN,UAIG4G,EAASrC,OAAO4C,WAAW,CAAEC,WAAW,IAC9CR,EAASrC,OAAOE,KAAK1B,QAASA,UAACsE,SAAU,CAAEP,cAC3CF,EAASrC,OAAOgE,oBAAoBzF,QAAAA,iBAAiB8M,eAjCnD9R,EAAOE,cACL,8DAA8D8I,kBAA0BF,EAASpE,MAAM8C,sBAAsBsB,EAASrC,OAAO/B,MAAMI,QAiCzJ,GACF,GNzBE,CACE+F,MAAO,0BACPkF,QOvBY,SACdjH,EACAE,GAEA,OAAQ+I,IACN/R,EAAOE,cACL,yDAAyD4I,EAASpE,MAAMC,uCAAuCqE,KAC/G+I,GAGFjJ,EAASnC,KAAK1B,kBAAU+M,gBAAiBD,EAAY,CAEzD,IPeME,EAA8B,CAClC,CACEpH,MAAO5F,QAASA,UAAC2B,SACjBmJ,QQ9BE,SAAwBjH,GAC5B,OAAQ+B,IACN7K,EAAOE,cACL,iDACA2K,GAGF/B,EAASnC,KAAK1B,kBAAU2B,SAAUiE,EAAM,CAE5C,GRuBE,CACEA,MAAO5F,QAASA,UAACoC,eACjB0I,QSjCE,SAA8BjH,GAClC,MAAO,eACL9I,EAAOE,cACL,0EAAsG,UAA5B4I,EAASpE,MAAMkF,mBAAa,IAAAH,OAAA,EAAAA,EAAArB,sBAIxG,MAAMxD,cAAEA,GAAkBkE,EAASrC,OAAO/B,MAE1C,GAAIE,EAAe,CACjB,MAAMsN,EAAapJ,EAASY,iBAAiB3F,aAAapB,MAC1DiC,EAAcqD,SAAWiK,EAAWlO,QACpCY,EAAcsD,SAAWgK,EAAW3J,YACpCO,EAASrC,OAAO/B,MAAME,cAAgBA,EACF,QAApC+E,EAAAb,EAASrC,OAAO/B,MAAMqF,sBAAc,IAAAJ,GAAAA,EAChCa,qBAAqB5F,GACtBxC,OAAOF,IACiD,IAK7D4G,EAASnC,KAAK1B,QAASA,UAACoC,eAAgB,CACtC3B,cAA2C,UAA5BoD,EAASpE,MAAMkF,mBAAa,IAAA+E,OAAA,EAAAA,EAAAvG,mBAC3CZ,aAAcsB,EAASpE,MAAM8C,eAE/B,MAAM2K,EAA+B,CACnCvJ,QAASE,EAAS/E,cAEpB+E,EAASnC,KAAK1B,kBAAUmN,eAAgBD,EAAc,CAE1D,ITegB,SAAAE,EACdvJ,EACAE,GAEAhJ,EAAOE,cACL,mDAAmD4I,EAASpE,MAAMC,gDAAgDqE,QAGpH,MAAMoB,OAAEA,GAAWtB,EAASpE,OACtBkF,YAAEA,GAAgBd,EAASpE,MAG7B0F,GAAUtB,EAASpE,MAAM8C,eACvBsB,EAASpE,MAAMhF,QAGjB0K,SAAAA,EAAQkI,GAAGnM,GAAG,SAAUjE,IACtBlC,EAAOE,cACL,mDAAmD4I,EAASpE,MAAMC,6BAClEzC,EACD,IAIHkI,SAAAA,EAAQkI,GAAGnM,GAAG,aAAcoM,IAC1BvS,EAAOE,cACL,mDAAmD4I,EAASpE,MAAMC,iCAClE4N,GAGF7F,EAAgB5D,GAAU1G,OAAOwI,IAAD,GAE9B,IAGJR,SAAAA,EAAQkI,GAAGnM,GAAG,mBAAoBjE,IAChClC,EAAOE,cACL,mDAAmD4I,EAASpE,MAAMC,uCAClEzC,EACD,IAIHkI,SAAAA,EAAQkI,GAAGnM,GAAG,oBAAoB,KAChCnG,EAAOE,cACL,mDAAmD4I,EAASpE,MAAMC,wCACnE,KAaLyF,SAAAA,EAAQjE,GAAG,cAAeqM,IACxBxS,EAAOE,cACL,+FAA+FsS,wBU3GjG,SAA2B1J,GAC/B,OAAQ0J,IACNxS,EAAOE,cACL,wEAAwE4I,EAASpE,MAAM+I,mBACvF+E,GAGG1J,EAASpE,MAAM+I,mBAClB3E,EAASnC,KAAK1B,QAASA,UAACwN,qBACxB/F,EAAgB5D,GAAU1G,OAAOwB,IAI9B,KAIT,CV6Fa8O,CAAiB5J,EAAjB4J,CAA2BF,OAItC3C,EAAwB8C,SAAQ,EAAG9H,QAAOkF,cAExC3F,SAAAA,EAAQjE,GADc,GAAG0E,KAAS7B,IACR+G,EAAQjH,EAAUE,GAAW,IAGzDiJ,EAA4BU,SAAQ,EAAG9H,QAAOkF,cAC5CnG,SAAAA,EAAazD,GAAG0E,EAAOkF,EAAQjH,GAAU,IAG3CA,EAASpE,MAAM2N,uBAAwB,CACzC,CW7DM,MAAOnS,UAAsBoF,EAAAA,cAuBjC,WAAAhD,CAAYsQ,GACVnN,QAvBKjD,KAAAkC,MAA4B,CACjC2L,kBAAkB,EAKlBJ,eAAe,EACfxC,kBAAkB,EAClByB,eAAWrK,EACXuJ,iBAAkB,CAAE,EACpBZ,cAAc,EACd9B,uBAAwB,GACxBmH,oBAAoB,EACpBC,yBAAqBjO,EACrB8H,gBAAgB,EAChBE,qBAAsB,GAUtBrK,KAAKoQ,QAAUA,EACf,MAAMG,UACJA,EAAS5H,6BACTA,EAA4BO,uBAC5BA,EAAsB/G,QACtBA,EAAO8B,OACPA,EAAMjB,QACNA,GACEoN,EAEJpQ,KAAKkC,MAAMoI,QAAUiG,EACrBvQ,KAAKkC,MAAMC,QAAUA,EACrBnC,KAAKkC,MAAM8C,aAAef,EAAO/B,MAAM8C,aACvChF,KAAKkC,MAAMyG,6BAA+BA,EAC1C3I,KAAKkC,MAAMhF,OAAkC,KAA1B8F,eAAAA,EAASwN,cAC5BxQ,KAAKiE,OAASA,GAEgB,KAA1BjB,aAAO,EAAPA,EAASwN,eACXtT,EAAKC,QAAC+C,OAAO,uBAGfF,KAAKkC,MAAMgH,uBAAyBA,EACpClJ,KAAKkC,MAAM8I,aACThL,KAAKkC,MAAMgH,yBAA2BxH,IACf,KAAvBsB,aAAO,EAAPA,EAAS+H,WAEXvN,EAAOE,cACL,iDAAiDsC,KAAKkC,MAAMgH,0BAG9DlJ,KAAKyQ,aAGP,UAAAA,SACE,MAAM5P,eAAEA,EAAcV,MAAEA,EAAK6C,QAAEA,GAAYhD,KAAKoQ,QAC1CM,EAAyD,CAC7DC,aAAa,EACbC,WAAYjP,EACZkP,iBAAiB,GAGbC,EAAM9Q,KAAKkC,MAAMgH,uBACvB1L,EAAOE,cAAc,gDAAgDoT,KAErE9Q,KAAKkC,MAAM0F,OAASkI,EAAAA,GAAGgB,EAAKJ,GCtI1B,SAAmCpK,GACvC,GAAsB,oBAAXyK,QAA8C,oBAAbC,WAI5CxT,EAAOE,cACL,wDAAwDsT,SAASC,aACjE3K,IAIGA,EAASpE,MAAMmO,oBAAoB,CACtC,MAAMa,EAAe,KACnB1T,EAAOE,cAAc,8CACrBwM,EAAgB5D,GAAU1G,OAAOwB,IAI9B,GACD,EAGJ2P,OAAOI,iBAAiB,QAASD,GAGjC5K,EAASpE,MAAMmO,oBAAqB,EAGpC/J,EAASpE,MAAMoO,oBAAsB,KACnCS,OAAOK,oBAAoB,QAASF,GACpC5K,EAASpE,MAAMmO,oBAAqB,CAAK,EAG/C,CDwGIgB,CAAyBrR,MAEzB,MAAMsR,EAA6C,CACjDvO,mBAAoB/C,KACpBa,iBACA0Q,eAAe,EACfpP,kBAASnC,KAAKkC,MAAMC,uBAAW,GAC/BhC,QACA6C,WAGFhD,KAAKkC,MAAMkF,YAAc,IAAI3J,EAAY6T,GAG3C,SAAAxM,GACE,OAAiB9E,KE1JnBxC,EAAOE,cAAc,qDAEO,QAA5BuJ,EFwJmBjH,KExJVkC,MAAMkF,mBAAa,IAAAH,GAAAA,EAAAnC,aAHxB,MF8JE,aAAA0M,sDACJ,OGhJE,SAA8BlL,gEASlC,GARA9I,EAAOE,cACL,4CAA4C4I,EAASpE,MAAMC,WAGxDmE,EAASpE,MAAM0F,QAClBtB,EAASmK,aAGc,QAArBxJ,EAAAX,EAASpE,MAAM0F,cAAM,IAAAX,OAAA,EAAAA,EAAEmD,UAEzB,MAAM,IAAInE,MAAM,4BAGK,QAAvBkB,EAAAb,EAASpE,MAAM0F,cAAQ,IAAAT,GAAAA,EAAAoD,UACvBjE,EAASpE,MAAM+I,kBAAmB,EAClC3E,EAASpE,MAAM8C,cAAe,EAC9B,MAAMwB,EAAYiL,EAAAA,KAClBnL,EAASpE,MAAMsE,UAAYA,EAC3BqJ,EAAsBvJ,EAAUE,SAC1B,IAAI6C,SAAc,CAACC,EAASmB,WACT,QAAvBxD,EAAAX,EAASpE,MAAM0F,cAAQ,IAAAX,GAAAA,EAAA9C,KACrB1B,QAAAA,UAAUiI,aACV,CACElE,YACArE,QAAS,GAAGmE,EAASpE,MAAMC,uBAC3B4F,WAAY,SAEd,CAAOrI,EAAsB6G,IAA8BpI,EAAAA,UAAA6B,UAAA,OAAA,GAAA,YACzD,UACQqG,EAAyBC,EAAU5G,EAAO6G,GAChD+C,IACA,MAAOqB,GACPF,EAAOE,QAGZ,IAEH,MAAMvE,EAAoC,QAA1B+F,EAAA7F,EAASpE,MAAMkF,mBAAW,IAAA+E,OAAA,EAAAA,EAAE5K,aAC5C,MAAO,CACLiF,YACAkL,QAAQtL,aAAO,EAAPA,EAASjG,MAAMsB,SAAU,GACjCkQ,SAASvL,aAAO,EAAPA,EAASjG,MAAMqB,UAAW,MAEtC,CHoGUgQ,CAAcxR,QACtB,CAED,gBAAA4R,EAAiBpL,UACfA,EAASqL,gBACTA,GAAkB,EAAKxK,WACvBA,IAEA,gBI3JkCJ,wDAACmJ,QACrCA,EAAO9J,SACPA,IAKA,MAAME,UAAEA,EAASa,WAAEA,EAAUwK,gBAAEA,GAAoBzB,GAC7ClO,MAAEA,EAAK+B,OAAEA,GAAWqC,GACpBtB,aAAEA,GAAe,EAAK4C,OAAEA,EAAMR,YAAEA,GAAgBlF,GAChDE,cAAEA,GAAkB6B,EAAO/B,MAEjC,GAAI0F,aAAM,EAANA,EAAQwC,UAIV,MAAM,IAAInE,MAAM,4BAGlB,GAAIjB,IAAgB5C,aAAA,EAAAA,EAAe4B,kBAAkB,CACnD,MAAMyB,SAAEA,EAAQC,SAAEA,GAAatD,EAC3BqD,GAAYC,IACd0B,SAAAA,EAAa5B,oBAAoB,CAAEC,WAAUC,cAqBjD,OAfAhH,OAAOC,OAAOuD,EAAO,CACnB+I,kBAAkB,EAClB4G,kBACA7M,eACAwB,cAGFoB,SAAAA,EAAQ2C,UACRsF,EAAsBvJ,EAAUE,IAE3BxB,GAAgBqC,IACnBD,SAAAA,EAAazB,kBAAiB,GAC9BjH,OAAOC,OAAOsF,EAAO/B,MAAO,CAAEI,OAAO,EAAM+E,YAAY,KAGlD,IAAIgC,SAASC,UAClB,MAAM9I,EAAqC,QAAzByG,EAAAG,aAAW,EAAXA,EAAa7F,oBAAY,IAAA0F,OAAA,EAAAA,EAAE9G,MAAMsB,OAGnDmG,SAAAA,EAAQzD,KACN1B,QAASA,UAACiI,aACV,CACElE,YACArE,QAAS,GAAGD,EAAMC,2BAClB4F,WAAY/C,EAAe,OAAS,SACpCxE,UARkB6G,IAAerC,EAAexE,OAAY6B,IAU9D,CAAO3C,EAAsB6G,IAA8BpI,EAAAA,UAAA6B,UAAA,OAAA,GAAA,kBACnDqG,EAAyBC,EAAU5G,EAAO6G,GAChD+C,QAEH,MAEJ,CJ8FUsI,CAAiB,CACtBxB,QAAS,CACP5J,YACAqL,kBACAxK,cAEFf,SAAUtG,OAId,UAAAuB,GACE,OAAQvB,KAAKkC,MAAMkF,YAA4B7F,aAGjD,QAAAuQ,GKlLI,IAAmBxL,IACA,QAAvBW,GADuBX,ELmLLtG,MKlLTkC,MAAM0F,cAAQ,IAAAX,GAAAA,EAAA9C,KAAK1B,QAAAA,UAAUyI,QAAS,CAC7CpD,GAAIxB,EAASpE,MAAMsE,UACnBrE,QAASmE,EAASpE,MAAMC,QACxB+B,QAAS,CACPG,KAAM1B,QAAsBA,uBAACoP,oBAC7BtN,OAAQ6B,EAAS/E,aAAapB,MAAM4F,eLgLxC,cAAAmB,GACE,OAAOlH,KAAKkC,MAAMkF,YAGd,WAAA1C,CAAYR,sDAChB,OAAOsH,EAAkBxL,KAAMkE,KAChC,CAED,IAAA8N,GACE,OM9LE,SAAqB1L,8DACzB9I,EAAOE,cACL,mCAAmC4I,EAASpE,MAAMC,sBAChDmE,EAASpE,MAAM8C,8BAC2B,QAA1BiC,EAAAX,EAASpE,MAAMkF,mBAAW,IAAAH,OAAA,EAAAA,EAAErB,sBAGzB,QAAvBuB,EAAAb,EAASpE,MAAM0F,cAAQ,IAAAT,GAAAA,EAAAhD,KAAKvB,QAAAA,YAAYiF,KAAM,CAC5CC,GAAIxB,EAASpE,MAAMsE,UACnBrE,QAAS,OACT4F,WAAYzB,EAASrC,OAAO/B,MAAM8C,aAAe,OAAS,SAC1Dd,QAAS,OAEZ,CNiLU8N,CAAKhS,MAGd,KAAAiS,GACE,OOpME,SAAsB3L,8DAC1B9I,EAAOE,cACL,oCAAoC4I,EAASpE,MAAMC,WAGrDmE,EAASpE,MAAM+I,kBAAmB,GACF,QAA5BhE,EAAAX,EAASpE,MAAMkF,mBAAa,IAAAH,OAAA,EAAAA,EAAArB,4BACxBU,EAAS5B,YAAY,CAAEL,KAAMzB,QAAWA,YAACmM,SAE1B,QAAvB5H,EAAAb,EAASpE,MAAM0F,cAAQ,IAAAT,GAAAA,EAAAN,eACxB,CP0LUoL,CAAMjS,MAGf,WAAAkS,SACE,OAAwB,QAAjBjL,EAAAjH,KAAKkC,MAAM0F,cAAM,IAAAX,OAAA,EAAAA,EAAEmD,UAG5B,MAAA+H,GACE,OQpME,SAAuB7L,sDAC3B,MAAMpE,MAAEA,EAAK+B,OAAEA,GAAWqC,GACpBsB,OAAEA,EAAMpB,UAAEA,EAASrE,QAAEA,EAAOiF,YAAEA,EAAWpC,aAAEA,GAAiB9C,GAC1D8C,aAAcoN,GAAuBnO,EAAO/B,MAUpD,GARA1E,EAAOE,cACL,uCAAuC8I,aAAqBrE,eAC1DyF,aAAM,EAANA,EAAQwC,8BACWlI,EAAM+I,4BACzB/I,EAAMoI,yBACUlD,aAAW,EAAXA,EAAaxB,uBAG5BY,EAEH,MADAhJ,EAAOE,cAAc,sDACf,IAAIuI,MAAM,6BAGd2B,aAAM,EAANA,EAAQwC,YACV5M,EAAOE,cAAc,gDACrBkK,EAAOzD,KAAKvB,QAAWA,YAACiF,KAAM,CAC5BC,GAAItB,EACJuB,WAAYqK,EAAqB,OAAS,SAC1CjQ,QAAS,oBACT+B,QAAS,KAGND,EAAOoO,wBAA0BjL,eAAAA,EAAaxB,sBAE7CZ,QACIsB,EAAS5B,YAAY,CAAEL,KAAMzB,QAAWA,YAAC0P,QAE/ClL,SAAAA,EAAarC,MAAM,CAAEC,cAAc,OAIvC4C,SAAAA,EAAQ2C,UAER/M,EAAOE,cACL,sEAAmEkK,aAAM,EAANA,EAAQwC,aAG7ExC,SAAAA,EAAQzD,KACN1B,QAASA,UAACiI,aACV,CACElE,YACArE,QAAS,GAAGA,WACZ4F,WAAYqK,EAAqB,OAAS,WAE5C,CAAO1S,EAAsB6G,IAA8BpI,EAAAA,UAAA6B,UAAA,OAAA,GAAA,YACzD,UACQqG,EAAyBC,EAAU5G,EAAO6G,GAChD,MAAOoE,WAOfzI,EAAM+I,kBAAmB,EACzB/I,EAAMoI,SAAU,IACjB,CRuIU6H,CAAOnS,MAGhB,mBAAAuS,GACE,OAAOvS,KAAKkC,MAAM0J,iBAGpB,UAAA/E,CAAWuJ,GACT,OSjNY,SACd9J,EACA8J,iBAEA5S,EAAOE,cACL,yCAAyC4I,EAASpE,MAAMC,UACxDiO,IAGEA,aAAO,EAAPA,EAAStJ,qBACXK,KAAAb,EAASpE,OAAMoO,4CACfhK,EAASpE,MAAMsE,UAAY4J,EAAQ5J,UACZ,QAAvB2F,EAAA7F,EAASpE,MAAM0F,cAAQ,IAAAuE,GAAAA,EAAAqG,qBACK,QAA5B7K,EAAArB,EAASpE,MAAMkF,mBAAa,IAAAO,GAAAA,EAAA1F,QAC5BqE,EAASrC,OAAO/B,MAAMI,OAAQ,EAC9BgE,EAASpE,MAAM0F,YAASvF,EAExBiE,EAASpE,MAAM0J,iBAAmB,CAAE,GAGtCtF,EAASpE,MAAM+I,kBAAmB,EACX,QAAvB7C,EAAA9B,EAASpE,MAAM0F,cAAQ,IAAAQ,GAAAA,EAAAvB,YACzB,CT2LWA,CAAW7G,KAAMoQ,IU/N5B,IAAYqC,ECAAC,ECmBN,SAAUC,EAAsBrM,GACpC,MAAO,IAAWnI,EAAAA,UAAA6B,UAAA,OAAA,GAAA,sBAChB,MAAMkC,MAAEA,GAAUoE,EAElB,GAAIpE,EAAMmF,WAER,YAI2B,KAAWlJ,YAAA6B,UAAA,OAAA,GAAA,YACtC,MAAQkC,EAAM+G,kBACNE,EAAK,IAEf,IACMyJ,GAIN,MAAMC,EAAe,MAAMC,eAAgC,QAAlB7L,EAAA/E,EAAM+G,kBAAY,IAAAhC,OAAA,EAAAA,EAAA6B,UAAW,IAOtE,GALAtL,EAAOI,oBACL,4EAA4F,UAAhBsE,EAAM+G,kBAAU,IAAA9B,OAAA,EAAAA,EAAE2B,wBAAwB+J,KAInG,IAAjBA,EAEF,OAGF,MAAME,EACJ7Q,EAAM8Q,eAAiBP,QAAYA,aAACQ,WACpC/Q,EAAM8Q,eAAiBP,QAAAA,aAAaS,aACpChR,EAAM8Q,eAAiBP,QAAAA,aAAaU,sBAEtC3V,EAAOI,oBACL,6EAA6EsE,EAAM8Q,uBAAuBD,aAA4B7Q,EAAMsE,2BAA6C,UAAlBtE,EAAM+G,kBAAY,IAAAkD,OAAA,EAAAA,EAAArD,WAGvLiK,IAEF7Q,EAAMmF,YAAa,EACnBf,EAASnC,KAAK1B,QAASA,UAAC6E,YAE5B,GACF,CCnDM,SAAU8L,EAA0B9M,GACxC,OAAQwB,IACN,MAAM5F,MAAEA,GAAUoE,EAClB9I,EAAOI,oBACL,8DAA8DsE,EAAMC,0CAA0C2F,KAGhHxB,EAASnC,KAAK1B,kBAAUqL,gBAAiBhG,EAAG,CAEhD,CCFgB,SAAAuL,EACd/M,EACAqC,GAEA,MAAO,iBACL,MAAMzG,MAAEA,GAAUoE,EAQlB,GANA9I,EAAOI,oBACL,uFACEsE,EAAMsE,mCACUW,UAAAF,EAAA/E,EAAMa,yCAAoBxB,mCAAc2B,iBAGxDhB,EAAM+J,UAAW,CACnB,MAAMqH,EAAepR,EAAM8C,aACvBnC,uBAAe0Q,QACf1Q,QAAAA,eAAe2Q,eACnBxV,EAEIU,OAAAC,OAAAD,OAAAC,OAAA,CAAAmJ,GAAmB,QAAfqE,EAAAjK,EAAMsE,iBAAS,IAAA2F,EAAAA,EAAI,GACvB9D,MAAOnG,EAAMuR,aAAe5Q,uBAAe6Q,UAAYJ,GACpDpR,EAAMsG,iBACTE,UAAWC,EACXF,WAAYvG,EAAMuG,WAClBM,sBAAepB,EAAAzF,EAAM+G,iCAAYH,QACjCF,iBAAkBC,EAAYC,UAEhC5G,EAAMgH,wBACNtJ,OAAOwB,IACoC,IAI/Cc,EAAM2L,kBAAmB,EACzB3L,EAAMyR,oBAAqB,EAC3BrN,EAASnC,KAAK1B,QAASA,UAAC6K,kBAAkB,CAE9C,CCxCM,SAAUsG,EAA+BtN,GAC7C,OAAQE,IACN,MAAMtE,MAAEA,GAAUoE,EAElB9I,EAAOI,oBACL,mEAAmEsE,EAAMC,+CAA+CqE,KAIrHtE,EAAM8B,mBACT9B,EAAM2L,kBAAmB,EACzB3L,EAAMI,OAAQ,EACdJ,EAAMmF,YAAa,GAIrBf,EAASnC,KAAK1B,QAAAA,UAAUsL,qBAAsB7L,EAAMsE,WACpDF,EAAS2B,oBAAoBzF,QAAgBA,iBAAC8M,aAAa,CAE/D,CClBM,SAAUuE,EAA0BvN,GACxC,OAAQiJ,UACN,MAAMrN,MAAEA,GAAUoE,EASlB,GAPA9I,EAAOI,oBACL,8DAA8DsE,EAAMC,4CAA4CoN,WAAqBrN,EAAMI,qBAAqBJ,EAAMK,4BAGxK+D,EAAS2B,oBAAoBzF,QAAgBA,iBAACsR,SAE9CxN,EAASnC,KAAK1B,kBAAU+M,gBAAiBD,GACrCrN,EAAMK,yBAA0B,CAClC/E,EAAOI,oBACL,6FAA6FsE,EAAMK,mCACnGL,EAAM6R,oBAGR,MAAMC,GAAoC,QAA1B/M,EAAA/E,EAAM6R,0BAAoB,IAAA9M,OAAA,EAAAA,EAAA+M,UAAW,IAC/CC,EAAY1K,YAAW,KAC3B/L,EAAOI,oBACL,iEAAiEoW,6BACjE9R,EAAM6R,oBAIR7R,EAAMK,0BAA2B,EAC5BL,EAAMI,OACTgE,EAAS2B,oBAAoBzF,QAAgBA,iBAAC0R,SAEhDC,aAAaF,EAAU,GACtBD,IAGT,CClBgB,SAAAI,GACd9N,EACAqC,GAEA,OAAQzE,wBAKN,MAAMhC,MAAEA,GAAUoE,EAOlB,GALA9I,EAAOI,oBACL,6DAA6DsE,EAAMC,iDAAiDD,EAAMsE,YAC1HtC,GAGwC,QAAtCiD,EAA0B,QAA1BF,EAAA/E,EAAMa,0BAAoB,IAAAkE,OAAA,EAAAA,EAAA1F,oBAAY,IAAA4F,OAAA,EAAAA,EAAEjE,cAAe,CAEzD,MAAMd,EACD1D,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EAAAuD,EAAME,gBACToE,kBAAW2F,EAAAjK,EAAMsE,yBAAa,GAC9BgB,YACuB,QAArBG,EAAAzF,EAAME,qBAAe,IAAAuF,OAAA,EAAAA,EAAAH,aAAc5F,EACrC6D,SAAUvD,EAAMa,mBAAmBxB,aAAapB,MAAMqB,QACtDkE,SAAUxD,EAAMa,mBAAmBxB,aAAapB,MAAM4F,cAEpC,QAApBqC,EAAAlG,EAAMqF,sBAAc,IAAAa,GAAAA,EAChBJ,qBAAqB5F,GACtBxC,OAAOF,IACiD,IAE3D4G,EAAS2B,oBAAoBzF,QAAgBA,iBAAC0F,SC/CpC,SACd5B,EACA+N,uBAEA,MAAMnS,MAAEA,GAAUoE,EAElB9I,EAAOI,oBACL,sDAAsDsE,EAAMsE,YAC5D6N,GAGF,MAAMC,EACD5V,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EAAAuD,EAAME,gBACToE,UAA0B,QAAfS,EAAA/E,EAAMsE,iBAAS,IAAAS,EAAAA,EAAI,GAC9BO,WAA2C,QAA/B2E,EAAmB,QAAnBhF,EAAAjF,EAAME,qBAAa,IAAA+E,OAAA,EAAAA,EAAEK,kBAAU,IAAA2E,EAAAA,EAAI,EAC/CnI,iBAAkB9B,EAAM8B,iBACxByB,SAC6C,kBAA3CkC,EAAAzF,EAAMa,yCAAoBb,MAAMkF,mBAAW,IAAAgB,OAAA,EAAAA,EAAE7G,aAAapB,MAAMqB,QAClEkE,SAC6C,QAA3C4I,EAA0B,QAA1BtF,EAAA9G,EAAMa,0BAAoB,IAAAiG,OAAA,EAAAA,EAAA9G,MAAMkF,mBAAW,IAAAkH,OAAA,EAAAA,EAAE/M,aAAapB,MACvD4F,YACLwO,WAAYF,EAAeG,YAET,QAApB9G,EAAAxL,EAAMqF,sBAAc,IAAAmG,GAAAA,EAAE1F,qBAAqBsM,EAC7C,CD0BIG,CAAkBnO,EAAU,IAAImB,MAE5BvF,EAAM+J,WAAa/J,EAAMsE,WAC3BxI,EAAaU,OAAAC,OAAAD,OAAAC,OAAA,CAETmJ,GAAI5F,EAAMsE,UACV6B,MAAOnE,EAAQc,aACXnC,uBAAeyF,UACfzF,QAAcA,eAAC0F,kBAChBrG,EAAMsG,gBACT,CAAAC,WAAYvG,EAAMuG,WAClBC,UAAWC,EACXC,iBAAkBC,EAAYC,QAC9BC,sBAAeC,EAAA9G,EAAM+G,iCAAYH,UAEnC5G,EAAMgH,wBACNtJ,OAAOwB,IACoC,IAI/Cc,EAAM8C,aAAed,EAAQc,aAExBd,EAAQc,eAIa,QAAxBsJ,EAAApM,EAAMa,0BAAkB,IAAAuL,GAAAA,EAAE5J,YAAY,CACpCL,KAAMzB,QAAWA,YAAC0P,QAEpBpQ,EAAMI,OAAQ,EACdJ,EAAMwS,QAAS,GAIbxQ,EAAQc,eAAiB9C,EAAMyR,qBAET,QAAxBjG,EAAAxL,EAAMa,0BAAkB,IAAA2K,GAAAA,EAAEhJ,YAAY,CACpCL,KAAMzB,QAAWA,YAAC+R,gBAClBnM,eAAgBtG,EAAMsG,eACtBoM,WAAY1S,EAAMsG,iBAEpBtG,EAAMyR,oBAAqB,GAGjC,CExGM,SAAUkB,GAAmBvO,GACjC,OAAQwO,IACN,IAAI5Q,EAAU4Q,EAEVA,EAAS5Q,UACXA,EAAUA,EAAQA,SCiBR,SACdA,EACAoC,GAEA,MAAMpE,MAAEA,GAAUoE,EAWlB,GATA9I,EAAOI,oBACL,gEACEsE,EAAMC,sCACuB+B,IAC/BA,GAGFoC,EAASpE,MAAMI,OAAQ,EAElBJ,EAAM8C,cAAgBd,EAAQG,OAASzB,QAAWA,YAAC+R,gBAGjD,GAAIzS,EAAM8C,cAAgBd,EAAQG,OAASzB,QAAAA,YAAYmS,aC5BhD,SACdzO,EACApC,GAEA,MAAMhC,MAAEA,GAAUoE,EAElBpE,EAAM+G,WAAa/E,EAAQ+E,WAC3B/G,EAAMwS,QAAS,CAiBjB,CDKIM,CAAwB1O,EAAUpC,OAD7B,CAGA,GAAIhC,EAAM8C,cAAgBd,EAAQG,OAASzB,QAAAA,YAAYqS,aE/C1C,SACpB3O,EACApC,gEAEA,MAAMhC,MAAEA,GAAUoE,EAElB,GAAIpE,EAAM8C,aAAc,CAEtB,MAAMpE,EAAOsD,EAAQtD,MAAQ,CAAE,EAE/B,GACkB,iBAATA,GACP,aAAcA,GACd,YAAaA,GACb,cAAeA,EAEf,IAEE,MAAMwB,cAAEA,GAAkBkE,EAASpE,MAMnC,GALA1E,EAAOI,oBACL,6BACAqB,KAAKC,UAAUkD,EAAe,KAAM,IAGlCA,EAAe,CACjB,MAAM8S,EAAWtU,EAAKsU,SAChBC,EAAUvU,EAAKuU,QACfxO,EAAY/F,EAAK+F,UACvB,IACIoC,EADAqM,GAA4B,EAE5B,qBAAsBxU,IACxBwU,EAA4BC,QAAQzU,EAAK0U,kBACzChP,EAASpE,MAAMkT,0BACbA,GAGA,kBAAmBxU,IACrBmI,EAAgBnI,EAAKmI,qBAGY,QAA7B9B,EAAAX,EAASpE,MAAMqF,sBAAc,IAAAN,OAAA,EAAAA,EAAEe,qBAAoBtJ,OAAAC,OAAAD,OAAAC,OAAA,GACpDyD,GACH,CAAAsD,SAAUiB,EACVoC,gBACAqM,4BACApR,kBAAkB,WAGe,QAA7BmD,EAAAb,EAASpE,MAAMqF,sBAAc,IAAAJ,OAAA,EAAAA,EAAEoO,gBAAgBL,SAClB,QAA7B/I,EAAA7F,EAASpE,MAAMqF,sBAAc,IAAA4E,OAAA,EAAAA,EAAEqJ,eAAeL,GAGtD7O,EAASnC,KAAK1B,QAASA,UAACwS,YAAa,CACnCC,SAAUtU,EAAKsU,SACfC,QAASvU,EAAKuU,UAEhB,MAAOzV,QAUd,EFjBG+V,CAAwBnP,EAAUpC,GAAStE,OAAOF,IAChDlC,EAAOI,oBACL,8DAA8D8B,IAC/D,SAEE,GAAIwE,EAAQG,OAASzB,QAAWA,YAAC6D,WGrCpC,SAAuCH,sDAC3C,MAAMpE,MAAEA,GAAUoE,EAGdpE,EAAM8C,qBACF6B,GAAW,CACfuJ,QAAS,CAAEtJ,WAAW,EAAMpC,aAAa,GACzC4B,aAEFA,EAASnC,KAAK1B,QAASA,UAACgE,cAE3B,EH2BGiP,CAAuBpP,GAAU1G,OAAOF,IACtClC,EAAOI,oBACL,8DAA8D8B,IAC/D,SAEE,GAAIwE,EAAQG,OAASzB,QAAWA,YAACmM,OInDpC,SAA6BzI,GACjC,MAAMpE,MAAEA,GAAUoE,EAElBpE,EAAMwS,QAAS,EACfpO,EAAS2B,oBAAoBzF,QAAgBA,iBAACmT,OAChD,CJ+CIC,CAAmBtP,QACd,GAAIpC,EAAQG,OAASzB,QAAAA,YAAY0P,OAASpQ,EAAM8C,cKlDnD,SAA6BsB,GACjC,MAAMpE,MAAEA,GAAUoE,EAElBA,EAAS2B,oBAAoBzF,QAAgBA,iBAAC0F,QAI9C,MAAMoC,EAAUpI,EAAMwS,OAEtBxS,EAAMwS,QAAS,EAEfpO,EAASnC,KAAK1B,QAASA,UAACoT,cAAe,CACrC7Q,aAAc9C,EAAM8C,aACpBiE,WAAY/G,EAAM+G,aAGhBqB,IACFpI,EAAMmF,YAAa,EAEnBf,EAASnC,KAAK1B,QAASA,UAAC6E,YAE5B,CL8BIwO,CAAmBxP,OACd,IAAIpC,EAAQG,OAASzB,QAAAA,YAAYmT,KAAO7T,EAAM8C,aAEnD,YMtDY,SACdsB,EACApC,SAEA,MAAMhC,MAAEA,GAAUoE,EAGlBA,EAASnC,KAAK1B,QAAAA,UAAUsT,IAAK7R,EAAQ8R,WAGwB,IAAzD,MAAMlD,eAA8B,UAAhB5Q,EAAM+G,kBAAU,IAAAhC,OAAA,EAAAA,EAAE6B,UAAW,KAKnDxC,EAASnC,KAAK1B,QAASA,UAACwT,aAAc,CACpC3W,OAAQwC,EAAYE,oBACpBpD,OAAQ,IAGd,CNiCIsX,CAAiB5P,EAAUpC,GAElBA,EAAQG,OAASzB,QAAAA,YAAY0E,YAAcpF,EAAM8C,cO1DxD,SAAkCsB,GACtC,MAAMpE,MAAEA,GAAUoE,EAElBpE,EAAMmF,YAAa,EACnBf,EAASnC,KAAK1B,QAASA,UAAC6E,WAC1B,CPsDI6O,CAAwB7P,GAI1BA,EAASnC,KAAK1B,kBAAUyI,QAAShH,QQ3DnB,SACdoC,EACApC,SAEA,MAAMhC,MAAEA,GAAUoE,EAGM,QAAxBW,EAAA/E,EAAMa,0BAAkB,IAAAkE,GAAAA,EAAEvC,YAAY,CACpCL,KAAMzB,QAAWA,YAACmS,YAClB9L,WAAY/G,EAAM+G,aAEpB/G,EAAMsG,eAAiBtE,EAAQsE,gBAAkBtE,EAAQ0Q,WACzDtO,EAASnC,KAAK1B,QAASA,UAACoT,cAAe,CACrC7Q,aAAc9C,EAAM8C,aACpBwD,eAAgBtG,EAAMsG,iBAExBtG,EAAMwS,QAAS,CACjB,CRYI0B,CAA4B9P,EAAUpC,EA+B1C,CD9DImS,CAA4BnS,EAASoC,EAAS,CAElD,CURM,SAAUgQ,GAA2BhQ,GACzC,MAAO,KACL,MAAMpE,MAAEA,GAAUoE,EAElB9I,EAAOI,oBACL,+HAGFsE,EAAMI,OAAQ,EACdJ,EAAMmF,YAAa,EACnBpF,EAAMC,GACNoE,EAASU,uBAAuB,CAAE7E,QAAS,oBAAqB,CAEpE,CCdM,SAAUoU,GAA8BjQ,GAC5C,MAAO,KACL,MAAMpE,MAAEA,GAAUoE,EAElB9I,EAAOI,oBACL,sGAEFsE,EAAMI,OAAQ,CAAK,CAEvB,CCbM,SAAUkU,GAA2BlQ,GACzC,MAAO,IAAWnI,EAAAA,UAAA6B,UAAA,OAAA,GAAA,8BAChB,MAAMkC,MAAEA,GAAUoE,EAElB9I,EAAOI,oBACL,+DAA+DsE,EAAMC,WAGvEmE,EAASpE,MAAMI,OAAQ,EACvBgE,EAASpE,MAAM2L,kBAAmB,EAClCvH,EAASpE,MAAMmF,YAAa,EAC5Bf,EAASpE,MAAM8B,kBAAmB,EACD,QAAjCiD,EAAAX,EAASpE,MAAMa,0BAAkB,IAAAkE,GAAAA,EAAEC,iBAAiBvB,kBAAiB,GAErEW,EAASnC,KAAK1B,QAASA,UAACoC,eAAgB,CACtC3B,eAAe,EACf8B,cAAc,IAIhBsB,EAASnC,KAAK1B,QAASA,UAAC6E,YACxBhB,EAASnC,KAAK1B,QAASA,UAACoT,eACxBvP,EAASnC,KAAK1B,QAASA,UAAC0F,qBACxB,IAEEjG,EAAME,cAAa1D,OAAAC,OAAAD,OAAAC,OAAA,GACduD,EAAME,eAAa,CACtBqD,SAAoC,QAA1B0B,EAAAjF,EAAMa,0BAAoB,IAAAoE,OAAA,EAAAA,EAAAD,iBAAiB3F,aAAapB,MAC/DqB,QACHkE,SAAkC,QAAxByG,EAAAjK,EAAMa,0BAAkB,IAAAoJ,OAAA,EAAAA,EAC9BjF,iBACDpB,oBACHU,UAA0B,QAAfmB,EAAAzF,EAAMsE,iBAAS,IAAAmB,EAAAA,EAAI,GAC9BH,WACiC,QAA/BwB,EAAmB,QAAnBZ,EAAAlG,EAAME,qBAAa,IAAAgG,OAAA,EAAAA,EAAEZ,kBAAU,IAAAwB,EAAAA,EAAIpH,EACrCoC,kBAAkB,UAGM,QAApBsK,EAAApM,EAAMqF,sBAAc,IAAA+G,OAAA,EAAAA,EAAEtG,qBAAqB9F,EAAME,eACvD,MAAO1C,IAGX,GACF,UCEgB+W,IAAkB9N,6BAChCA,EAA4B9H,eAC5BA,EAAc0P,UACdA,EAASpQ,MACTA,EAAK+I,uBACLA,EAAyBxH,EAAkB4E,SAC3CA,8BASA,MAAMpE,MAAEA,GAAUoE,EAOlB,GALA9I,EAAOI,oBACL,8BACAqB,KAAKC,UAAUgD,EAAO,KAAM,IAGtByG,IACD+N,QAA4BA,6BAACC,OAchC,MAAM,IAAI1Q,MAAM,kCAbhB/D,EAAMa,mBAAqB,IAAIrF,EAAc,CAC3CiL,+BACA9H,iBACA0P,YACAK,WAAY1O,EAAM0O,WAClB1H,yBACA/G,QAASD,EAAMC,QACfhC,QACA6C,QAASd,EAAMc,QACfiB,OAAQqC,IAOd,IAAIwK,EAA2B,oBAAbE,UAA4BA,SAAS4F,KAAQ,GAC3DC,EAA6B,oBAAb7F,UAA4BA,SAAS6F,OAAU,IAE3C,UAApB3U,EAAM4U,oBAAc,IAAA7P,OAAA,EAAAA,EAAA6J,OACtBA,EAAM5O,EAAM4U,aAAahG,MAGH,UAApB5O,EAAM4U,oBAAc,IAAA3P,OAAA,EAAAA,EAAAgG,QACtB0J,EAAQ3U,EAAM4U,aAAa3J,MAG7B,MAAM4J,EAC+C,QAAnD/N,EAA4B,UAAV,QAAlBmD,EAAAjK,EAAM4U,oBAAY,IAAA3K,OAAA,EAAAA,EAAEgB,YAAQ,IAAAxF,EAAAA,UAAAS,EAAAlG,EAAM4U,mCAAchG,WAAG,IAAA9H,EAAAA,EAAI,MACnDgO,EACc,oBAAXjG,aAAqD,IAApBA,OAAOkG,oBAC3ClG,OAAOkG,SAASC,wBAChBH,EAEAvO,EAAiC,CACrCsI,MACA+F,QACAM,eAAQzJ,EAAAxL,EAAM4U,mCAAcK,OAC5BH,SACAI,MAAwB,QAAlBzJ,EAAAzL,EAAM4U,oBAAY,IAAAnJ,OAAA,EAAAA,EAAE0J,WAA6B,UAAlBnV,EAAM4U,oBAAY,IAAAlJ,OAAA,EAAAA,EAAE0J,YACzDC,SAAUrV,EAAM8Q,aAChBwE,WAAY3O,EAAYC,QACxB2O,kBAAWlJ,EAAArM,EAAM4U,mCAAcW,WAEjCvV,EAAMsG,eAAiBA,EAEvB,MAAMkP,EAEF,CAEF,CAACjV,kBAAU6E,YAAaqL,EAAsBrM,GAC9C,CAAC7D,kBAAUyI,SAAU2J,GAAmBvO,GACxC,CAAC7D,kBAAU0F,qBAAsBqO,GAA2BlQ,GAC5D,CAAC7D,QAASA,UAAC6K,mBAAoB+F,EAC7B/M,EACAqC,GAEF,CAAClG,QAASA,UAACoC,gBAAiBuP,GAC1B9N,EACAqC,GAEF,CAAClG,kBAAUwN,qBAAsBsG,GAA8BjQ,GAC/D,CAAC7D,kBAAU+H,kBAAmB8L,GAA2BhQ,GACzD,CAAC7D,kBAAUsL,sBAAuB6F,EAA+BtN,GACjE,CAAC7D,QAAAA,UAAU2B,UAAW,OAItB,CAAC3B,kBAAUqL,iBAAkBsF,EAA0B9M,GACvD,CAAC7D,kBAAU+M,iBAAkBqE,EAA0BvN,GACvD,CAAC7D,QAASA,UAACqJ,YAAcjC,IAGvBvD,EAASnC,KAAK1B,kBAAUqJ,WAAYjC,EAAI,GAI5C,IAAK,MAAO8N,EAAWpK,KAAY7O,OAAOK,QAAQ2Y,GAChD,IACExV,EAAMa,mBAAmBY,GAAGgU,EAAWpK,GACvC,MAAO7N,IAIb,UC7IsBmH,GAAUI,wDAACmJ,QAC/BA,EAAO9J,SACPA,IAKA,MAAMpE,MAAEA,GAAUoE,EAOlB,OALA9I,EAAOI,oBACL,+CAA+CsE,EAAMsE,YACrD4J,GAGK,IAAI/G,SAAiB,CAACC,EAASmB,sBAChC2F,aAAO,EAAPA,EAAStJ,YACPR,EAASpE,MAAMI,OACjBtE,EACE,CACE8J,aAAIxB,EAASpE,MAAMsE,yBAAa,GAChC6B,MAAOxF,QAAcA,eAAC+U,YAExBtR,EAASpE,MAAMgH,wBACftJ,OAAOwB,IACwD,IAInEc,EAAMI,OAAQ,EACdJ,EAAMwS,QAAS,EAGK,QAApBvN,EAAAjF,EAAMqF,sBAAc,IAAAJ,GAAAA,EAAEL,UAA6B,QAAnBqF,EAAAjK,EAAMsE,iBAAa,IAAA2F,EAAAA,EAAA,IACnD7F,EAASpE,MAAM2V,YAAa,EACxBzH,EAAQ1L,aAGkB,UAA1BxC,EAAMa,0BAAoB,IAAA4E,OAAA,EAAAA,EAAApG,aAAa2B,gBACvCoD,EAASpE,MAAMa,oBAEf6H,EAAsBtE,EAASpE,MAAMa,mBAAoB,CACvDsB,KAAMzB,QAAWA,YAAC6D,YAEjB6F,MAAK,KAIJhD,GAAQ,EAAK,IAEd1J,OAAOF,IACN+K,EAAO/K,EAAM,IAInB4J,GAAQ,GAGVpH,EAAMmF,YAAa,EACnBnF,EAAM8B,kBAAmB,EACzB9B,EAAMsE,UAAYiL,OAClBrB,EAAQ5J,UAAYtE,EAAMsE,UAC1BtE,EAAME,mBAAgBC,EACtBH,EAAMK,0BAA2B,EACT,QAAxB6F,EAAAlG,EAAMa,0BAAkB,IAAAqF,GAAAA,EAAEvB,WAAWuJ,GACrC9J,EAAS2B,oBAAoBzF,QAAgBA,iBAACoV,cAEtB,QAAxB5O,EAAA9G,EAAMa,0BAAkB,IAAAiG,GAAAA,EAAEnC,WAAWuJ,GACrC9J,EAAS2B,oBAAoBzF,QAAgBA,iBAAC8M,cAC9ChG,GAAQ,SAGb,CCxFWoN,QAAAA,kCAAAA,GAAAA,QAA4BA,+BAA5BA,qCAEX,CAAA,IADC,OAAA,SxBDUjE,QAAAA,kBAAAA,GAAAA,EAAAA,QAAYA,eAAZA,qBAWX,CAAA,IATC,WAAA,SAEAA,EAAA,sBAAA,iBAEAA,EAAA,WAAA,cAEAA,EAAA,UAAA,aAEAA,EAAA,YAAA,eCVUC,QAAAA,qBAAAA,GAAAA,EAAAA,QAAeA,kBAAfA,wBAGX,CAAA,IAFC,MAAA,QACAA,EAAA,KAAA,qHwBqGI,cAAmC5P,EAAAA,cAiCvC,WAAAhD,CAAYsQ,GACVnN,QA/BKjD,KAAAkC,MAAkC,CAEvCI,OAAO,EAEP+E,YAAY,EACZrC,cAAc,EACd6S,YAAY,EACZ3S,gBAAiB,EACjBwP,QAAQ,EACRU,2BAA2B,EAC3BpC,aAAc,kBACd/G,WAAW,EACXwH,cAAc,EACdE,oBAAoB,EACpBzK,uBAAwBxH,EACxBS,QAAS,GACT2V,SAAS,EAETjK,kBAAkB,EAClBkK,gBAAiBnW,EAEjBW,0BAA0B,EAC1BrF,OAAO,EAKP8a,kBAAmBxV,QAAgBA,iBAAC8M,cAMpCtP,KAAKiY,SAAW7H,EAEhB,MAAM4C,aACJA,EAAYrK,6BACZA,EAA4B9H,eAC5BA,EAAc0P,UACdA,EAAStH,WACTA,EAAU6N,aACVA,EAAY5R,gBACZA,EAAe0L,WACfA,EAAUzO,QACVA,EAAO6B,iBACPA,EAAgB7D,MAChBA,EAAK8L,UACLA,GAAY,EAAKiM,QACjBA,EAAOzP,WACPA,EAAUS,uBACVA,EAAyBxH,EAAkBsB,QAC3CA,EAAO2N,YACPA,EAAc,CACZqD,QAASnS,IAETuO,EAEJpQ,KAAKkC,MAAMrB,eAAiBA,EAC5Bb,KAAKkC,MAAM4U,aAAeA,EAC1B9W,KAAKkC,MAAM+G,WAAaA,EACxBjJ,KAAKkC,MAAM0O,WAAaA,EACxB5Q,KAAKkC,MAAM8Q,aAAeA,EAC1BhT,KAAKkC,MAAM+J,UAAYA,EACvBjM,KAAKkC,MAAMgD,gBAAkBA,QAAAA,EAAmB,EAChDlF,KAAKkC,MAAM8C,cAAgBnE,EAC3Bb,KAAKkC,MAAM8B,iBAAmBA,EAC9BhE,KAAKkC,MAAMgH,uBAAyBA,EACpClJ,KAAKkC,MAAMC,QAAUA,EACrBnC,KAAKkC,MAAM2V,YAAa,EACxB7X,KAAKkC,MAAMuG,WAAaA,EAExBzI,KAAKmY,gBAAgB,IAErBnY,KAAKiI,oBAAoBzF,QAAgBA,iBAAC8M,eACtC4I,aAAO,EAAPA,EAASE,YACXpY,KAAKkC,MAAM6V,gBAAkBnW,GAE/B5B,KAAKkC,MAAMmW,eAAiBH,EAC5BlY,KAAKkC,MAAM6R,mBAAqBpD,EAChC3Q,KAAKkC,MAAMhF,OAAiC,KAAzB8F,eAAAA,EAASsV,cAGC,KAAzBtV,aAAO,EAAPA,EAASsV,cACXpb,EAAKC,QAAC+C,OAAO,8BAGe,KAA1B8C,aAAO,EAAPA,EAASwN,eACXtT,EAAKC,QAAC+C,OAAO,wBAGa,KAAxB8C,aAAO,EAAPA,EAASM,aACXpG,EAAKC,QAAC+C,OAAO,gBAGmB,KAA9B8C,aAAO,EAAPA,EAASS,mBACXvG,EAAKC,QAAC+C,OAAO,qBAGfF,KAAKkC,MAAMc,QAAUA,GAEjBkV,aAAO,EAAPA,EAAS3Q,kBACXvH,KAAKkC,MAAMqF,eAAiB2Q,EAAQ3Q,gBAGtC/J,EAAOI,oBACL,wDAAwDsH,sBAAoClB,kBAAiChE,KAAKkC,MAAM8C,6CAA6C2D,oBAA+C9H,eAA4B0P,KAG7PvQ,KAAKkC,MAAM8C,cACdyR,GAAkB,CAChB9N,+BACA9H,iBACA0P,YACApQ,QACA+I,yBACA5C,SAAUtG,OAIdA,KAAKgH,uBAAuB,CAAE7E,QAAS,gBAM5B,mBAAAoW,4DACX,GAAIvY,KAAKkC,MAAMqF,eAAgB,CAE7B,MAAMnF,QACEpC,KAAKkC,MAAMqF,eAAeiR,0BAA0B,IACxDpW,IACFpC,KAAKkC,MAAME,cAAgBA,EAC3BpC,KAAKkC,MAAMsE,UAAYpE,EAAcoE,UACrCxG,KAAKkC,MAAMkT,0BAC8B,QAAvCnO,EAAA7E,EAAcgT,iCAAyB,IAAAnO,GAAAA,EAErC7E,EAAc4B,mBAChBhE,KAAKkC,MAAMmF,YAAa,EACxBrH,KAAKkC,MAAMI,OAAQ,EACnBtC,KAAKiI,oBAAoBzF,QAAgBA,iBAAC0F,cACpClI,KAAK4R,iBAAiB,CAC1BpL,UAAWpE,EAAcoE,cAMjCiQ,GAAkB,CAChB9N,6BAA8B+N,QAA4BA,6BAACC,OAC3D9V,eAAgBb,KAAKkC,MAAMrB,eAC3B0P,UAAWvQ,KAAKiY,SAAS1H,UACzBpQ,MAAOH,KAAKiY,SAAS9X,MACrB+I,uBAAwBlJ,KAAKkC,MAAMgH,uBACnC5C,SAAUtG,SAEb,CAKK,wBAAAyY,sDAEJ,aC9PE,SAAyCnS,4DAC7C,MAAMpE,MAAEA,GAAUoE,EAElB,IAAKpE,EAAMqF,eAIT,YAHA/J,EAAOI,oBACL,uFAKJ,MAAMwE,QAAsBF,EAAMqF,eAAeiR,0BAC/C,CAAA,GAQF,GANAhb,EAAOI,oBACL,iEAAiEsE,EAAMK,yCACvEH,GAGwC,QAAxB6E,EAAA/E,EAAMa,0BAAkB,IAAAkE,OAAA,EAAAA,EAAEiL,cAM1C,OAJA1U,EAAOI,oBACL,qFAGKwE,EAGT,GAAIA,EAAe,CAGjB,GAFqBA,EAAcoF,WAAaC,KAAKC,MAOnD,OAJAxF,EAAME,cAAgBA,EACtBF,EAAMK,0BAA2B,EACjCL,EAAMsE,UAAYpE,aAAA,EAAAA,EAAeoE,UACjCtE,EAAMuR,cAAe,EACdrR,EAGT5E,EAAOI,oBACL,4DAGJsE,EAAMK,0BAA2B,IAElC,CDiN+BkW,CAAyBzY,QAEtD,CAEK,wBAAA0Y,sDACJ,OEpQE,SACJxW,sEAEA,IAAKA,EAAMa,mBACT,MAAM,IAAIkD,MAAM,uCAGlB,GAAI/D,EAAMI,MACR,MAAM,IAAI2D,MAAM,6BAGlB,GAAI/D,EAAMsE,YAAuC,QAA1BS,EAAA/E,EAAMa,0BAAoB,IAAAkE,OAAA,EAAAA,EAAAiL,eAS/C,OARAhQ,EAAME,cAAa1D,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EACduD,EAAME,eACT,CAAAoE,UAAWtE,EAAMsE,UACjBgB,WAAYC,KAAKC,MAAQxF,EAAM6V,kBAGX,QAAtB5Q,EAAAjF,EAAMqF,sBAAgB,IAAAJ,GAAAA,EAAAa,qBAAqB9F,EAAME,eAE1C,CACLoE,UAAWtE,EAAMsE,UACjBmL,QAA+C,QAAtChK,EAA0B,QAA1BwE,EAAAjK,EAAMa,0BAAoB,IAAAoJ,OAAA,EAAAA,EAAA5K,oBAAY,IAAAoG,OAAA,EAAAA,EAAExH,MAAMqB,QACvDkQ,OAA8C,QAAtC1I,EAA0B,QAA1BZ,EAAAlG,EAAMa,0BAAoB,IAAAqF,OAAA,EAAAA,EAAA7G,oBAAY,IAAAyH,OAAA,EAAAA,EAAE7I,MAAMsB,QAI1DjE,EAAOI,oBAAoB,8CAC3B,MAAM+a,QAAgBzW,EAAMa,mBAAmByO,gBAE/ChU,EAAOI,oBACL,6DACA+a,GAGF,MAAMvW,EAAa1D,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EACduD,EAAME,eAAa,CACtBoE,UAAWmS,EAAQnS,UACnBf,SAAUkT,EAAQhH,QAClBnK,WAAYC,KAAKC,MAAQxF,EAAM6V,kBAKjC,OAHA7V,EAAMsE,UAAYmS,EAAQnS,UAC1BtE,EAAME,cAAgBA,EAEf,CACLoE,UAAWtE,EAAMsE,UACjBkL,OAAQiH,EAAQjH,OAChBC,QAASgH,EAAQhH,WAEpB,CFmNU+G,CAAyB1Y,KAAKkC,SACtC,CAED,KAAAD,GACE,OAAOA,EAAMjC,KAAKkC,OAGpB,gBAAA0P,EAAiBpL,UACfA,EAASqL,gBACTA,EAAexK,WACfA,IAMA,gBGjRkCJ,GAAC,OAAA9I,YAAA6B,KAAA4Y,eAAA,GAAA,WAAApS,UACrCA,EAASqL,gBACTA,EAAexK,WACfA,EAAUnF,MACVA,cAOA,IAAK2W,EAAAA,SAASrS,GAIZ,MAHAhJ,EAAOI,oBACL,qDAAqDsE,EAAMC,qCAAqCqE,KAE5F,IAAIP,MAAM,mBAAmBO,KAOrC,GAJAhJ,EAAOI,oBACL,qDAAqDsE,EAAMC,qBAAqBqE,qBAA6BqL,aAG3G1K,EAAAjF,EAAMa,yCAAoBmP,cAK5B,YAHA1U,EAAOI,oBACL,qDAAqDsE,EAAMC,qDAK/DD,EAAMsE,UAAYA,QACY,QAAxB2F,EAAAjK,EAAMa,0BAAkB,IAAAoJ,OAAA,EAAAA,EAAEyF,iBAAiB,CAC/CpL,YACAa,aACAwK,oBAEF,MAAMyC,EACD5V,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EAAAuD,EAAME,eAAa,CACtBoE,YACAgB,WAAYC,KAAKC,MAAQxF,EAAM6V,kBAEjC7V,EAAME,cAAgBkS,EACF,QAApB3M,EAAAzF,EAAMqF,sBAAc,IAAAI,GAAAA,EAAEK,qBAAqBsM,KAC5C,CHsOU1C,CAAiB,CACtBpL,YACAa,aACAwK,kBACA3P,MAAOlC,KAAKkC,QAIhB,WAAAwC,CAAYR,GACV,OI9QkB,SACpBoC,EACApC,8DAEA,MAAMhC,MAAEA,GAAUoE,EAElB9I,EAAOI,oBACL,gDAAgDsE,EAAMC,kBACpDD,EAAMwS,gBACExS,EAAMI,0BACdJ,EAAM8B,+BAEN9B,EAAMmF,qBAC6B,UAA1BnF,EAAMa,0BAAoB,IAAAkE,OAAA,EAAAA,EAAAiL,kCACnChQ,EAAM2L,2BACG3L,EAAM8V,oBACjB9T,GAIChC,EAAM8B,kBACL9B,EAAMI,QACqB,UAA1BJ,EAAMa,0BAAoB,IAAAoE,OAAA,EAAAA,EAAA+K,gBAC1BhQ,EAAM2L,mBAETrQ,EAAOI,oBACL,gDAAgDsE,EAAMC,gEAGlD,IAAIkH,SAAeC,IACvBhD,EAASwS,KAAKrW,kBAAUoT,cAAevM,EAAQ,IAGjD9L,EAAOI,oBACL,gDAAgDsE,EAAMC,2DAI1D,IACE,MAAMgJ,QCtDY,SACpB7E,EACApC,sDAEA,OAAO,IAAImF,SAASC,UAClB,MAAMpH,MAAEA,GAAUoE,EAElB9I,EAAOI,oBACL,wDAAwDsE,EAAMC,iBAAiBD,EAAMI,oBAAoBJ,EAAMmF,qBAAqBnD,EAAQ5E,WAGzI4C,EAAM8C,cAAgB9C,EAAMmF,YAAcnF,EAAM8B,iBAC3B,QAAxBiD,EAAA/E,EAAMa,0BAAkB,IAAAkE,GAAAA,EACpBvC,YAAYR,GACboI,MAAMN,IACL1C,EAAQ0C,EAAK,IAEdpM,OAAOF,IAKN4J,GAAQ,EAAM,IAGlBhD,EAASwS,KAAKrW,kBAAU6E,YAAY,WAClC9J,EAAOI,oBACL,wDAAwDsE,EAAMC,+DAIxC,QAAxB8E,EAAA/E,EAAMa,0BAAkB,IAAAkE,GAAAA,EACpBvC,YAAYR,GACboI,MAAMN,IACL1C,EAAQ0C,EAAK,IAEdpM,OAAOF,IAKN4J,GAAQ,EAAM,GACd,SAIX,CDQyByP,CAAoBzS,EAAUpC,GACpD,OAAOiH,EACP,MAAO/J,GAKP,MAAMA,KAET,CJ8NUsD,CAAY1E,KAAMkE,GAGrB,WAAA8U,sDACJ,OM5RE,SAA4B9W,4DAChC,MAAM+W,QAAkC,QAAtBhS,EAAA/E,EAAMqF,sBAAgB,IAAAN,OAAA,EAAAA,EAAAuR,4BAExChb,EAAOI,oBAAoB,2CAA4Cqb,KACxE,CNwRUD,CAAYhZ,KAAKkC,SACzB,CAED,mBAAAgX,GACE,OAAOlZ,KAAKkC,MAAMkT,0BAGpB,gBAAA+D,GACE,OAAOnZ,KAAKkC,MAAME,cAQpB,OAAAgX,GACE,OAAOpZ,KAAKkC,MAAMI,MAQpB,WAAA4P,SACE,OAAoC,QAA7BjL,EAAAjH,KAAKkC,MAAMa,0BAAkB,IAAAkE,OAAA,EAAAA,EAAEiL,cAGxC,YAAAmH,GACE,OAAOrZ,KAAKkC,MAAMmF,WAGpB,QAAAiS,GACE,OAAOtZ,KAAKkC,MAAMwS,OAGpB,qBAAA6E,GACE,OAAOvZ,KAAKkC,MAAMa,mBAGd,IAAAiP,4DACJxU,EAAOI,oBACL,yCAAyCoC,KAAKkC,MAAMsE,mBAGjB,QAA/BS,EAAAjH,KAAKkC,MAAMa,0BAAoB,IAAAkE,OAAA,EAAAA,EAAA+K,SACtC,CAED,UAAAwH,GACEhc,EAAOI,oBAAoB,wBAAwBoC,KAAKkC,MAAMsE,aAC9DhJ,EAAOE,cAAc,wBAAwBsC,KAAKkC,MAAMsE,aACxDhJ,EAAOG,MAAM,wBAAwBqC,KAAKkC,MAAMsE,aAChDhJ,EAAOC,YAAY,wBAAwBuC,KAAKkC,MAAMsE,aAGxD,QAAAsL,SACEtU,EAAOI,oBACL,6CAA6CoC,KAAKkC,MAAMsE,aAG3B,QAA/BS,EAAAjH,KAAKkC,MAAMa,0BAAoB,IAAAkE,GAAAA,EAAA6K,WAGjC,mBAAA7J,CAAoBwR,GACdzZ,KAAKkC,MAAM8V,oBAAsByB,IAGrCzZ,KAAKkC,MAAM8V,kBAAoByB,EAC/BzZ,KAAKmE,KAAK1B,kBAAUiX,kBAAmBD,GACvCzZ,KAAKgH,uBAAuB,CAAE7E,QAAS,yBAGzC,sBAAA6E,CAAuB2S,EAA0B,IAE/C3Z,KAAKmE,KAAK1B,QAASA,UAACmN,eAAgB5P,KAAK4Z,oBAG3C,mBAAAC,GACE,OAAO7Z,KAAKkC,MAAM8V,kBAGpB,gBAAA4B,GACE,MAAO,CACLpR,eAAgBxI,KAAKkC,MAAMsG,eAC3BpC,QAASpG,KAAKuB,aACdkY,iBAAkBzZ,KAAKkC,MAAM8V,kBAC7B5V,cAAepC,KAAKkC,MAAME,cAC1BoE,UAAWxG,KAAKkC,MAAMsE,WAI1B,UAAAjF,SACE,OAAoC,QAA7B0F,EAAAjH,KAAKkC,MAAMa,0BAAkB,IAAAkE,OAAA,EAAAA,EAAE1F,aAGxC,SAAAuD,SACiC,QAA/BmC,EAAAjH,KAAKkC,MAAMa,0BAAoB,IAAAkE,GAAAA,EAAAnC,YAGjC,iBAAApB,CAAkB7C,SAChB,MAAMuG,EAA2C,QAA7BH,EAAAjH,KAAKkC,MAAMa,0BAAkB,IAAAkE,OAAA,EAAAA,EAAEC,iBACnD,IAAKE,EACH,MAAM,IAAInB,MAAM,mCAGdmB,EAAYtB,sBAAwBjF,GACtCuG,EAAY1D,kBAAkB7C,GAI5B,KAAAoR,4DACJzU,EAAOI,oBACL,0CAA0CoC,KAAKkC,MAAMsE,mBAGlB,QAA/BS,EAAAjH,KAAKkC,MAAMa,0BAAoB,IAAAkE,OAAA,EAAAA,EAAAgL,QACrCjS,KAAKiI,oBAAoBzF,QAAgBA,iBAACmT,UAC3C,CAED,UAAAmE,GACE,OAAOjR,EAAYC,QAGrB,mBAAAuJ,SACE,OAAkC,QAA3BpL,EAAAjH,KAAKkC,MAAM8B,wBAAgB,IAAAiD,GAAAA,EAG9B,MAAAkL,sDACJ,OOnaE,SAAuB7L,4DAC3B,MAAMpE,MAAEA,GAAUoE,EAElB9I,EAAOI,oBACL,2CAA2CsE,EAAMsE,mBAGnB,UAA1BtE,EAAMa,0BAAoB,IAAAkE,OAAA,EAAAA,EAAAkL,SAChC7L,EAAS2B,oBAAoBzF,QAAgBA,iBAAC0F,UAC/C,CP0ZUiK,CAAOnS,QACf,CAED,OAAAW,CAAQC,aACN,MAAMwG,EAA2C,QAA7BH,EAAAjH,KAAKkC,MAAMa,0BAAkB,IAAAkE,OAAA,EAAAA,EAAEC,iBAC7CrG,EAAiBuG,eAAAA,EAAatB,oBACpC,IAAKjF,EACH,MAAM,IAAIoF,MAAM,6BAElB,iBAAsC,QAA/BkB,EAAAnH,KAAKkC,MAAMa,0BAAoB,IAAAoE,OAAA,EAAAA,EAAAjF,MAAMqB,oCAAe5C,QACzDC,EACAC,GAIJ,OAAAQ,CAAQT,aACN,KAAkC,QAA7BqG,EAAAjH,KAAKkC,MAAMa,0BAAkB,IAAAkE,OAAA,EAAAA,EAAE/E,MAAMqB,eACxC,MAAM,IAAI0C,MAAM,qCAElB,OAA2D,UAAvB,QAA7BkB,EAAAnH,KAAKkC,MAAMa,0BAAkB,IAAAoE,OAAA,EAAAA,EAAEjF,MAAMqB,qBAAe,IAAA4I,OAAA,EAAAA,EAAA9K,QAAQT,GAGrE,YAAAmZ,GACE,OAAO/Z,KAAKkC,MAAMsE,UAGpB,mBAAA+L,SACE,OAAoC,QAA7BtL,EAAAjH,KAAKkC,MAAMa,0BAAkB,IAAAkE,OAAA,EAAAA,EAAEsL,sBAGxC,MAAA9H,EAAOjE,UAAEA,IACP,gBQ5b+BS,wDAACT,UAClCA,EAAStE,MACTA,cAKA,IAAK2W,EAAAA,SAASrS,GAIZ,MAHAhJ,EAAOI,oBACL,qDAAqDsE,EAAMC,qCAAqCqE,KAE5F,IAAIP,MAAM,mBAAmBO,KAGrC,GAAItE,EAAM8C,aAKR,YAHAxH,EAAOI,oBACL,2CAA2CsE,EAAMC,wBAAwBD,EAAM8C,0BAA0BwB,KAK7G,MAAMoB,OAAEA,GAA0C,QAA/BuE,EAAwB,QAAxBhF,EAAAjF,EAAMa,0BAAkB,IAAAoE,OAAA,EAAAA,EAAEjF,aAAK,IAAAiK,EAAAA,EAAI,CAAE,GAEnDvE,aAAA,EAAAA,EAAQwC,aACX5M,EAAOI,oBACL,2CAA2CsE,EAAMC,oCAEnDyF,SAAAA,EAAQ2C,WAIVvM,EAAaU,OAAAC,OAAAD,OAAAC,OAAA,CAETmJ,GAAItB,EACJ6B,MAAOxF,QAAcA,eAACkE,UACnB7E,EAAMsG,gBACT,CAAAC,WAAYvG,EAAMuG,WAClBG,iBAAkBC,EAAYC,QAC9BC,cAAiC,UAAlB7G,EAAM+G,kBAAY,IAAAtB,OAAA,EAAAA,EAAAmB,UAEnC5G,EAAMgH,wBACNtJ,OAAOF,IAC+D,UAIlE,IAAI2J,SAAiB,CAACC,EAASmB,KACnC7C,SAAAA,EAAQzD,KACN1B,QAASA,UAACsE,SACV,CACEP,cAEF,CAAC9G,EAAgBN,KACf5B,EAAOI,oBACL,2CAA2CsE,EAAMC,kBAAkByF,eAAAA,EAAQE,KAC3E,CAAEpI,QAAON,aAGPM,EACF+K,EAAO/K,GAEP4J,EAAQlK,KAGb,MAEJ,CRyXU4a,CAAc,CACnBxT,YACAtE,MAAOlC,KAAKkC,QAIV,UAAA2E,CAAWuJ,sDACf,OAAOvJ,GAAW,CAChBuJ,UACA9J,SAAUtG,SAEb"}