/**
 * These properties are optional and should only be used during development for debugging purposes.
 */
export interface ECIESProps {
    debug?: boolean;
    privateKey?: string;
}
/**
 * Class that exposes methods to generate and compute
 * Elliptic Curve Integrated Encryption Scheme (ECIES) for key exchange and symmetric encryption/decryption
 *
 * It also exposes encryption/decryption methods that are used
 * by the communication layer to encrypt/decrypt in/out data
 * The encryption/decryption is made using a symmetric key generated from the ECIES key exchange
 */
export declare class ECIES {
    private ecies;
    private enabled;
    constructor(props?: ECIESProps);
    /**
     * Creates new ECIES instance
     *
     * @returns - Generates ECIES instance
     */
    generateECIES(): void;
    /**
     * Returns ECIES instance public key
     *
     * @returns - public key in base64 format
     */
    getPublicKey(): string;
    /**
     * Encrypts a data message using the public key of the side to encrypt data for
     *
     * @param {string} data - data string to be encrypted
     * @param {string} otherPublicKey - public key of the side to encrypt data for
     * @returns - encrypted string in base64
     */
    encrypt(data: string, otherPublicKey: string): string;
    /**
     * Decrypts a data message using the instance private key
     *
     * @param {string} encryptedData - base64 data string to be decrypted
     * @returns - decrypted data || error message
     */
    decrypt(encryptedData: string): string;
    getKeyInfo(): {
        private: string;
        public: string;
    };
    toString(): void;
}
//# sourceMappingURL=ECIES.d.ts.map