{"version": 3, "file": "KeyExchange.d.ts", "sourceRoot": "", "sources": ["../../../../src/KeyExchange.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AAC9C,OAAO,EAAS,UAAU,EAAE,MAAM,SAAS,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,yBAAyB,EAAE,MAAM,mCAAmC,CAAC;AAG9E,OAAO,EAAE,sBAAsB,EAAE,MAAM,gCAAgC,CAAC;AACxE,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAC1C,OAAO,EAAE,gCAAgC,EAAE,MAAM,wBAAwB,CAAC;AAI1E,MAAM,WAAW,gBAAgB;IAC/B,kBAAkB,EAAE,aAAa,CAAC;IAClC,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,aAAa,EAAE,OAAO,CAAC;IACvB,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,CAAC,EAAE,gCAAgC,CAAC;IAC3C,KAAK,CAAC,EAAE,UAAU,CAAC;CACpB;AAED,qBAAa,WAAY,SAAQ,aAAa;IAC5C,OAAO,CAAC,aAAa,CAAS;IAE9B,OAAO,CAAC,OAAO,CAAQ;IAEvB,OAAO,CAAC,cAAc,CAAC,CAAS;IAEhC,OAAO,CAAC,kBAAkB,CAAgB;IAE1C,OAAO,CAAC,WAAW,CAAS;IAE5B,OAAO,CAAC,IAAI,CACgC;IAE5C,OAAO,CAAC,OAAO,CAAS;IAExB,OAAO,CAAC,KAAK,CAAS;gBAEV,EACV,kBAAkB,EAClB,cAAc,EACd,OAAO,EACP,KAAK,EACL,OAAO,GACR,EAAE,gBAAgB;IAgCZ,oBAAoB,CAAC,cAAc,EAAE;QAC1C,OAAO,EAAE,yBAAyB,CAAC;KACpC;IAoGD,SAAS,CAAC,KAAK,CAAC,EAAE,UAAU;IAK5B,KAAK,IAAI,IAAI;IAYb,KAAK,CAAC,EACJ,YAAY,EACZ,KAAK,GACN,EAAE;QACD,YAAY,EAAE,OAAO,CAAC;QACtB,KAAK,CAAC,EAAE,OAAO,CAAC;KACjB,GAAG,IAAI;IAyGR,OAAO,CAAC,IAAI,EAAE,sBAAsB,GAAG,IAAI;IAK3C,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,IAAI;IASnC,mBAAmB,CAAC,EAClB,QAAQ,EACR,QAAQ,GACT,EAAE;QACD,QAAQ,EAAE,MAAM,CAAC;QACjB,QAAQ,EAAE,MAAM,CAAC;KAClB;IAMD,gBAAgB,CAAC,aAAa,EAAE,OAAO;IAIvC,gBAAgB;IAIhB,cAAc;IAId,iBAAiB;IAIjB,iBAAiB,CAAC,WAAW,EAAE,MAAM;IAMrC,cAAc,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM;IASvC,cAAc,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM;IAUvC,UAAU,IAAI,OAAO;IAQrB,QAAQ;CAQT"}