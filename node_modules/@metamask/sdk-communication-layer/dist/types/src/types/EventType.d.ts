export declare enum EventType {
    KEY_INFO = "key_info",
    SERVICE_STATUS = "service_status",
    PROVIDER_UPDATE = "provider_update",
    RPC_UPDATE = "rpc_update",
    KEYS_EXCHANGED = "keys_exchanged",
    JOIN_CHANNEL = "join_channel",
    PUBLIC_KEY = "public_key",
    CHANNEL_CREATED = "channel_created",
    CLIENTS_CONNECTED = "clients_connected",
    CLIENTS_DISCONNECTED = "clients_disconnected",
    CLIENTS_WAITING = "clients_waiting",
    CLIENTS_READY = "clients_ready",
    REJECTED = "rejected",
    WALLET_INIT = "wallet_init",
    CHANNEL_PERSISTENCE = "channel_persistence",
    CONFIG = "config",
    MESSAGE_ACK = "ack",
    SOCKET_DISCONNECTED = "socket_disconnected",
    SOCKET_RECONNECT = "socket_reconnect",
    OTP = "otp",
    SDK_RPC_CALL = "sdk_rpc_call",
    AUTHORIZED = "authorized",
    CONNECTION_STATUS = "connection_status",
    MESSAGE = "message",
    TERMINATE = "terminate"
}
//# sourceMappingURL=EventType.d.ts.map