export declare enum KeyExchangeMessageType {
    KEY_<PERSON><PERSON><PERSON><PERSON><PERSON>_START = "key_handshake_start",
    KEY_HANDSHAKE_CHECK = "key_handshake_check",
    KEY_HANDSHAKE_SYN = "key_handshake_SYN",
    K<PERSON>Y_<PERSON><PERSON><PERSON><PERSON>KE_SYNACK = "key_handshake_SYNACK",
    KEY_HANDSHAKE_ACK = "key_handshake_ACK",
    KEY_HANDSHAKE_WALLET = "key_handshake_wallet",
    KEY_HANDSHAKE_NONE = "none"
}
//# sourceMappingURL=KeyExchangeMessageType.d.ts.map