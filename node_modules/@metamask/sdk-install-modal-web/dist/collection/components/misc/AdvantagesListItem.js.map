{"version": 3, "file": "AdvantagesListItem.js", "sourceRoot": "", "sources": ["../../../../src/components/misc/AdvantagesListItem.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,CAAC,EAAE,MAAM,eAAe,CAAC;AAElC,MAAM,kBAAkB,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,EAA+B,EAAE,EAAE,CAAC,CAC1E,WAAK,KAAK,EAAC,eAAe,EAAC,KAAK,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,KAAK,EAAC;IACrE,WAAK,KAAK,EAAC,WAAW;QACpB,EAAC,IAAI,OAAG,CACJ;IACN,WAAK,KAAK,EAAC,YAAY;QACrB,YAAM,KAAK,EAAE,EAAE,UAAU,EAAE,GAAG,EAAG,KAAK,EAAE,OAAO,EAAE,IAAG,IAAI,CAAQ,CAC5D,CACF,CACP,CAAC;AAEF,eAAe,kBAAkB,CAAC", "sourcesContent": ["import { h } from '@stencil/core';\n\nconst AdvantagesListItem = ({ Icon, text }: { Icon: any; text: string }) => (\n  <div class='flexContainer' style={{ padding: '6', flexDirection: 'row'}}>\n    <div class='flexItem1'>\n      <Icon />\n    </div>\n    <div class='flexItem11'>\n      <span style={{ lineHeight: '2',  color: 'black' }}>{text}</span>\n    </div>\n  </div>\n);\n\nexport default AdvantagesListItem;\n"]}