import { h } from "@stencil/core";
export const MetamaskExtensionImage = () => (h("svg", { width: "400", height: "300", viewBox: "0 0 467 300", fill: "none", xmlns: "http://www.w3.org/2000/svg" }, h("path", { d: "M312.387 280.629C312.549 280.583 312.247 280.735 312.387 280.629L339.678 260.425C340.088 260.118 340.132 259.156 340.07 258.647C340.008 258.138 339.573 258.257 339.106 258.084L311.712 247.455C311.292 247.301 311.308 247.402 310.939 247.673C310.569 247.944 310.356 248.21 310.356 248.672L310.459 279.504C310.461 280.025 311.163 280.619 311.614 280.847C311.913 280.996 312.072 280.718 312.387 280.629ZM336.204 259.736L312.979 276.292L313.439 251.139L336.204 259.736Z", fill: "url(#paint0_linear_1356_14057)" }), h("path", { d: "M324.639 260.342C324.358 259.547 323.485 258.91 322.707 259.215C319.6 260.432 318.17 262.313 317.287 264.066C316.482 265.665 316.197 267.482 314.188 268.269C312.178 269.055 310.763 268.663 309.162 268.007C307.406 267.287 305.504 266.182 302.397 267.399C299.289 268.616 297.859 270.497 296.976 272.25C296.171 273.849 295.697 274.886 293.685 275.673C291.675 276.459 290.261 276.067 288.659 275.411C286.903 274.691 284.999 273.587 281.894 274.803C278.789 276.019 277.549 278.681 276.666 280.434C275.861 282.034 275.386 283.07 273.374 283.857C272.596 284.161 271.931 285.055 272.211 285.849C272.492 286.644 273.365 287.281 274.143 286.977C277.25 285.76 278.68 283.879 279.563 282.125C280.368 280.526 280.65 278.71 282.662 277.923C284.672 277.136 286.087 277.528 287.688 278.184C289.444 278.904 291.348 280.009 294.453 278.793C297.561 277.576 298.991 275.695 299.874 273.941C300.679 272.342 301.153 271.306 303.165 270.519C305.175 269.732 306.589 270.124 308.191 270.78C309.947 271.5 311.849 272.605 314.956 271.388C318.063 270.172 319.301 267.511 320.184 265.757C320.989 264.158 321.466 263.121 323.476 262.335C324.254 262.032 324.919 261.137 324.639 260.342Z", fill: "url(#paint1_linear_1356_14057)" }), h("path", { d: "M389.034 111.124C388.968 112.406 387.342 113.113 386.324 113.549C384.297 114.419 382.089 114.493 379.942 114.5C376.607 114.509 373.215 114.163 369.89 113.976C366.712 113.798 363.156 113.311 360.03 114.232C357.705 114.919 355.667 116.762 353.452 117.739C351.129 118.765 348.957 119.198 346.489 119.687C335.569 121.85 323.587 120.825 312.656 119.977C311.92 119.92 312.539 118.997 313.237 118.981C313.207 118.815 313.001 118.394 313.045 118.201C313.086 118.029 312.781 117.586 312.853 117.421C312.67 117.198 313.316 117.427 313.626 117.204C315.912 115.557 318.721 114.79 321.363 115.041C324.677 115.355 327.293 116.04 330.641 115.781C334.392 115.491 338.125 114.787 341.857 114.312C345.142 113.892 349.09 113.941 352.106 112.28C352.916 111.834 353.137 111.262 353.269 110.287C353.331 109.828 353.672 108.969 353.658 108.511C353.629 107.529 354.191 107.886 355.013 107.298C356.857 105.981 358.274 105.516 360.428 105.784C362.841 106.083 365.264 107.273 367.578 107.953C370.761 108.888 374.328 108.646 377.631 108.477C380.517 108.329 383.141 107.901 385.944 108.653C387.013 108.941 389.104 109.745 389.034 111.124Z", fill: "url(#paint2_linear_1356_14057)" }), h("path", { d: "M278.118 85.4414C280.313 83.8186 283.302 83.9 285.853 83.2728C290.501 82.127 295.644 78.4476 300.549 79.1524C301.299 79.2595 300.904 81.0355 300.159 80.9295C300.131 80.9256 300.187 80.9333 300.159 80.9295C300.089 81.0185 299.516 81.1051 299.385 81.1463C294.999 82.515 292.895 87.5083 289.126 89.8598C287.799 90.688 286.771 90.8351 285.258 90.9441C283.515 91.0691 280.964 90.5775 279.269 90.1219C277.15 89.5532 275.965 87.034 278.118 85.4414Z", fill: "url(#paint3_linear_1356_14057)" }), h("path", { d: "M122.567 166.093C113.597 172.832 104.535 179.836 99.517 190.092C98.5342 192.099 97.519 195.019 97.9533 197.204C98.9699 202.316 105.76 203.049 110.691 201.96C113.468 201.347 116.325 199.231 115.921 196.319C115.576 193.81 113.158 192.986 112.072 190.73C110.779 188.04 112.35 184.97 114.218 182.62C128.134 165.099 150.074 157.394 171.093 151.607C175.385 150.425 180.897 147.69 180.581 143.099C180.372 140.072 177.118 137.683 174.221 137.383C171.324 137.082 168.481 138.773 165.715 139.776C158.416 142.419 151.17 143.799 143.489 143.526C136.865 143.29 130.976 143.316 129.562 150.781C128.138 158.276 129.53 160.863 122.567 166.093Z", fill: "url(#paint4_linear_1356_14057)" }), h("path", { d: "M79.1056 153.531C60.3932 164.378 43.7478 178.354 32.8214 197.415C30.3313 201.76 27.9216 206.554 27.1821 211.512C26.4448 216.47 27.7591 222.525 30.8133 226.337C34.908 231.446 41.6938 232.915 48.1811 233.125C50.7248 233.208 53.0035 233.066 55.3316 231.947C57.6602 230.83 59.7548 228.359 59.5978 225.742C59.3303 221.315 54.5491 219.665 50.9271 217.34C42.6566 212.037 40.4958 199.493 44.2381 190.032C47.9803 180.57 55.8228 173.173 63.2316 166.338C67.229 162.651 71.5194 158.372 76.777 157.522C79.9167 157.015 83.4706 157.589 86.6295 157.252C93.9004 156.479 99.64 151.404 105.588 146.912C115.048 139.765 126.045 134.283 137.501 132.093C141.913 131.248 146.293 130.493 149.682 127.831C151.37 126.507 150.803 123.567 148.726 123.93C138.542 125.713 128.652 131.135 119.142 134.758C105.627 139.914 91.7133 146.223 79.1056 153.531Z", fill: "url(#paint5_linear_1356_14057)" }), h("path", { d: "M96.3677 117.321C96.0315 116.64 95.2914 116.612 94.6293 116.975C91.9858 118.417 91.5452 120.509 90.9466 122.178C90.4004 123.702 89.3647 124.67 87.6542 125.604C85.9437 126.538 85.0983 126.316 83.5956 125.91C81.9481 125.465 79.8603 125.424 77.2168 126.866C74.5734 128.309 74.1348 130.401 73.5341 132.07C72.9879 133.593 71.9522 134.562 70.2417 135.496C68.5313 136.43 67.6859 136.208 66.1832 135.801C64.5357 135.357 62.2569 134.538 59.6128 135.978C56.9694 137.42 56.5308 139.512 55.9301 141.181C55.3839 142.705 54.5398 144.454 52.8293 145.388C52.1667 145.748 52.1006 146.485 52.439 147.165C52.7752 147.845 53.5153 147.874 54.1773 147.511C56.8208 146.069 57.2594 143.977 57.86 142.307C58.4062 140.784 59.4419 139.815 61.1524 138.881C62.8629 137.948 63.7083 138.17 65.211 138.576C66.8585 139.021 68.9458 139.059 71.5898 137.619C74.2333 136.177 74.6718 134.085 75.2725 132.416C75.8187 130.892 76.8544 129.924 78.5649 128.99C80.2754 128.056 81.1207 128.278 82.6235 128.684C84.2709 129.129 86.5503 129.95 89.1938 128.507C91.8373 127.065 92.2779 124.973 92.8765 123.304C93.4227 121.78 94.2663 120.03 95.9773 119.098C96.6378 118.738 96.7039 118.001 96.3677 117.321Z", fill: "#FBC49D" }), h("path", { d: "M391.827 164.699C392.04 164.639 392.39 164.573 392.6 164.481C393.87 163.921 395.363 162.429 395.893 161.051C396.421 159.673 396.237 158.321 395.712 156.931C394.629 154.061 391.777 152.62 389.155 153.775C386.531 154.931 384.962 158.455 386.042 161.325C387.038 163.963 389.369 165.392 391.827 164.699ZM394.166 157.367C394.529 158.332 394.52 159.751 394.156 160.706C393.792 161.662 393.1 162.534 392.218 162.921C390.398 163.723 388.338 162.878 387.589 160.889C387.226 159.925 387.234 158.506 387.599 157.55C387.963 156.594 388.654 155.723 389.536 155.336C391.356 154.534 393.416 155.376 394.166 157.367Z", fill: "#86E29B" }), h("path", { d: "M62.7198 108.691C64.4912 108.191 66.6152 106.609 67.5596 104.824C68.4273 103.187 68.465 101.455 67.9609 99.7066C67.4547 97.9586 66.0792 96.5141 64.4963 95.6786C62.9135 94.843 61.2077 94.8578 59.4756 95.4255C57.7435 95.9933 56.2768 97.4364 55.409 99.074C53.6185 102.456 55.2032 106.494 58.4723 108.22C60.0142 109.035 61.1315 109.138 62.7198 108.691ZM60.6304 96.7682C61.819 96.4332 63.0232 96.8871 64.1048 97.4571C65.2463 98.0603 66.0495 98.8796 66.4145 100.142C66.7791 101.403 66.4488 103.299 65.8224 104.48C64.5299 106.919 61.2212 107.686 58.8638 106.441C56.5063 105.197 55.8538 101.858 57.1462 99.4185C57.7726 98.2375 58.6059 97.3957 59.8573 96.9862C59.9214 96.9635 60.5631 96.7872 60.6304 96.7682Z", fill: "#FFB0EB" }), h("path", { d: "M77.0217 242.979C75.9542 243.28 75.1664 242.151 74.9029 241.074C74.6394 239.996 75 239.378 76.0676 239.077C77.1351 238.776 78.5053 238.907 78.7688 239.984C79.0323 241.061 78.0893 242.678 77.0217 242.979Z", fill: "url(#paint6_linear_1356_14057)" }), h("path", { d: "M380.506 184.577C379.439 184.878 378.651 183.749 378.387 182.672C378.124 181.594 378.484 180.976 379.552 180.675C380.619 180.374 381.99 180.505 382.253 181.582C382.517 182.659 381.574 184.276 380.506 184.577Z", fill: "url(#paint7_linear_1356_14057)" }), h("path", { d: "M348.82 246.969C347.752 247.27 346.382 247.14 346.119 246.063C345.855 244.985 346.798 243.369 347.866 243.068C348.933 242.767 349.721 243.896 349.985 244.973C350.248 246.05 349.888 246.669 348.82 246.969Z", fill: "url(#paint8_linear_1356_14057)" }), h("path", { d: "M140.419 282.412C139.351 282.713 138.754 282.365 138.491 281.288C138.227 280.21 138.397 278.812 139.464 278.511C140.532 278.21 142.093 279.121 142.357 280.198C142.62 281.275 141.486 282.111 140.419 282.412Z", fill: "url(#paint9_linear_1356_14057)" }), h("path", { d: "M121.462 101.679C120.395 101.979 119.798 101.631 119.534 100.554C119.271 99.4764 119.44 98.0779 120.508 97.777C121.575 97.4761 123.136 98.3868 123.4 99.4642C123.663 100.542 122.53 101.378 121.462 101.679Z", fill: "url(#paint10_linear_1356_14057)" }), h("path", { d: "M370.749 159.005C369.682 159.306 368.311 159.175 368.048 158.098C367.784 157.021 368.727 155.404 369.795 155.103C370.863 154.802 371.65 155.931 371.914 157.008C372.177 158.086 371.817 158.704 370.749 159.005Z", fill: "url(#paint11_linear_1356_14057)" }), h("path", { d: "M384.211 245.345C383.143 245.646 381.773 245.515 381.51 244.438C381.246 243.361 382.189 241.744 383.257 241.443C384.324 241.142 385.112 242.271 385.376 243.348C385.639 244.426 385.278 245.044 384.211 245.345Z", fill: "url(#paint12_linear_1356_14057)" }), h("path", { d: "M364.771 41.3873C363.063 41.8687 361.337 40.8616 360.915 39.1378C360.493 37.414 361.536 35.6263 363.244 35.1449C364.953 34.6634 366.679 35.6706 367.101 37.3944C367.522 39.1182 366.479 40.9059 364.771 41.3873Z", fill: "url(#paint13_linear_1356_14057)" }), h("path", { d: "M404.311 77.905C404.602 77.8231 404.875 77.9236 405.084 77.6871C405.437 77.2908 404.814 76.6385 404.703 76.1265L401.277 58.7418C401.149 58.163 400.687 57.5371 400.123 57.3991C399.558 57.2611 398.969 57.3812 398.576 57.835L385.784 73.1179C385.395 73.564 385.199 74.3453 385.393 74.8964C385.585 75.4481 385.978 76.1646 386.548 76.2391L403.538 78.123C403.744 78.1479 404.114 77.9607 404.311 77.905ZM398.948 62.7348L401.811 74.4394L389.841 72.8085L398.948 62.7348Z", fill: "#FFB0EB" }), h("path", { d: "M97.5393 295.311C97.7108 295.263 97.4284 295.461 97.5393 295.311L108.785 280.464C108.975 280.211 108.718 279.972 108.594 279.684C108.47 279.396 108.124 279.917 107.821 279.902L88.5106 278.672C88.2101 278.659 87.895 278.62 87.7374 278.89C87.5798 279.159 87.7755 279.402 87.9283 279.67L96.5753 294.749C96.7102 294.99 97.2696 295.297 97.5393 295.311C97.6283 295.319 97.4556 295.335 97.5393 295.311ZM106.465 281.118L96.9667 292.97L90.6295 280.577L106.465 281.118Z", fill: "#86E29B" }), h("path", { d: "M393.699 108.848C389.518 104.446 414.629 114.366 413.945 120.659C413.259 126.952 401.355 129.102 403.894 123.491C406.042 118.747 400.376 115.876 393.699 108.848Z", fill: "#FBC49D" }), h("path", { d: "M139.414 115.959C139.434 115.953 139.392 115.965 139.414 115.959C141.963 115.2 143.436 111.976 142.717 109.189C142.37 107.839 141.526 107.199 140.408 106.504C139.289 105.808 138.171 105.449 136.933 105.815C135.698 106.182 134.85 107.033 134.222 108.247C133.595 109.461 133.282 111.234 133.63 112.585C133.978 113.935 134.821 114.574 135.94 115.27C137.04 115.952 138.199 116.301 139.414 115.959ZM137.315 107.375C138.222 107.12 139.005 106.994 139.825 107.502C140.661 108.02 140.912 108.618 141.171 109.625C141.708 111.704 140.935 113.834 139.032 114.398C138.11 114.675 137.356 114.791 136.522 114.272C135.686 113.753 135.436 113.155 135.176 112.149C134.917 111.142 134.718 109.714 135.186 108.81C135.654 107.905 136.394 107.649 137.315 107.375C137.329 107.371 137.299 107.38 137.315 107.375Z", fill: "#75C4FD" }), h("path", { d: "M308.981 78.7519C310.052 78.4043 311.158 78.7096 312.265 78.6613C313.655 78.6006 314.936 78.517 316.323 78.3531C319.497 77.9795 322.798 77.4906 325.984 77.3012C329.309 77.1025 332.514 76.8551 335.836 77.0295C338.809 77.1842 342.163 77.3405 345.105 77.7558C352.129 78.7468 359.331 79.0578 366.347 80.1158C372.252 81.008 377.93 82.1319 383.722 83.5644C385.925 84.0054 388.267 85.0754 389.126 85.3792C389.421 85.4833 389.37 85.8347 389.317 86.1595C389.306 86.2271 389.353 86.1146 389.317 86.1595C388.269 87.414 386.564 86.8124 385.259 86.4677C384.406 86.241 381.488 85.9494 381.011 85.9957C380.14 86.0815 379.277 86.014 379.656 87.2114C380.122 88.6824 382.391 89.7375 383.704 90.2419C385.883 91.0803 388.771 91.2561 390.845 92.4015C392.968 92.821 395.425 93.5002 397.022 93.9986C402.83 95.8103 407.806 98.8678 413.033 102.002C415.503 103.482 417.829 104.764 419.973 106.72C421.647 108.248 423.771 109.882 424.595 112.091C425.113 113.482 424.451 115.471 422.848 115.085C420.353 114.488 419.638 111.665 418.227 109.714C415.003 105.251 410.067 102.067 405.118 100.06C402.64 99.0556 399.803 98.729 397.204 98.1176C394.411 97.4613 391.597 97.0442 388.707 97.1736C385.646 97.3097 382.846 97.5534 379.819 98.0078C377.027 98.4264 374.252 98.4441 371.514 97.8441C368.766 97.2421 366.276 96.7553 363.6 95.9021C361.662 95.2848 359.013 94.5583 357.232 93.5247C355.699 92.6339 355.631 91.2381 357.241 90.186C359.87 88.4656 362.778 89.8021 365.546 90.3497C365.561 90.3527 365.532 90.3468 365.546 90.3497C367.858 90.804 369.649 90.098 371.923 89.3884C372.489 89.2128 373.255 89.0482 373.47 88.953C374.383 88.5432 375.895 87.8227 375.407 86.7394C374.868 85.5457 372.659 84.8846 371.55 84.4891C369.74 83.8428 368.053 82.9215 366.147 82.6743C365.654 82.6095 365.109 83.1606 364.6 83.1097C363.36 83.1121 361.769 83.348 360.543 83.418C357.43 83.5916 354.583 83.6228 351.464 83.4719C348.714 83.3377 346.432 82.8542 343.741 82.3102C340.773 81.7101 337.52 81.5879 334.471 81.5839C331.393 81.5811 328.6 81.7214 325.583 82.4181C322.758 83.0702 319.757 83.6198 316.887 84.0326C314.698 84.3481 312.467 84.6054 310.319 84.2137C309.079 83.9871 306.994 83.3394 306.461 81.9635C305.846 80.3659 307.736 79.1556 308.981 78.7519Z", fill: "url(#paint14_linear_1356_14057)" }), h("path", { d: "M242.269 242.95C242.295 242.947 242.244 242.955 242.269 242.95C244.934 242.606 247.53 241.858 250.2 241.574C253.133 241.264 256.006 240.56 258.906 239.982C263.26 239.117 268.026 238.52 272.254 237.097C274.429 236.365 276.067 235.298 278.251 234.592C280.361 233.909 283.058 233.12 285.216 232.652C289.317 231.764 293.049 231.66 297.016 230.198C301.109 228.69 305.088 226.673 308.819 224.409C312.645 222.086 316.282 219.769 320.622 218.62C322.381 218.154 323.919 218.219 325.65 218.886C327.016 219.413 327.739 220.337 327.196 221.79C326.557 223.504 324.659 224.7 323.131 225.423C320.854 226.5 319.53 227.184 317.134 227.927C314.619 228.707 311.888 229.24 309.395 230.083C308.226 230.947 307.17 232.522 306.105 233.501C303.996 235.441 301.666 237.03 298.945 237.996C292.934 240.129 286.354 238.602 280.183 239.056C279.661 239.095 279.162 239.442 278.635 239.487C275.592 240.135 272.2 240.63 269.156 241.294C264.546 242.299 260.456 243.409 255.808 244.179C251.971 244.814 243.815 245.854 243.815 245.854L242.074 245.505C240.575 245.281 240.751 243.152 242.269 242.95Z", fill: "url(#paint15_linear_1356_14057)" }), h("path", { d: "M432.985 171.626C438.212 165.465 445.109 153.64 442.906 144.649C442.162 137.999 433.671 132.912 424.963 135.518C420.16 136.957 416.435 142.146 419.724 144.498C420.656 145.164 422.187 145.591 423.391 145.969C434.204 149.375 434.925 162.538 425.069 169.683C421.087 172.569 416.347 174.292 411.914 176.719C400.151 183.162 399.887 196.847 387.7 202.712C382.326 205.298 375.761 206.838 370.297 209.275C359.98 213.88 352.555 221.733 342.241 226.341C337.225 228.584 330.266 231.632 323.483 234.12C321.475 234.855 321.999 237.058 324.056 236.46C324.098 236.449 324.017 236.474 324.056 236.46C329.079 234.971 334.437 233.161 339.331 231.33C352.278 226.48 365.28 220.971 377.43 214.775C401.78 202.367 416.881 190.609 432.985 171.626Z", fill: "url(#paint16_linear_1356_14057)" }), h("path", { d: "M343.644 241.104C348.002 234.144 357.126 232.455 364.343 231.134C368.176 230.431 372.406 229.878 376.137 228.662C380.691 227.175 384.558 224.095 388.327 221.077C389.452 220.177 391.204 218.466 392.393 217.436C393.185 216.748 393.644 216.026 394.33 215.225C396 213.275 399.599 209.812 401.687 208.16C403.263 206.914 407.779 204.758 408.259 207.986C408.611 210.359 406.379 213.087 404.961 214.747C402.224 217.953 398.998 221.079 395.475 223.243C391.749 225.531 387.701 227.439 383.674 229.051C381.672 229.853 379.519 230.073 377.486 230.785C376.001 231.307 374.486 232.236 373.037 232.866C371.034 233.738 368.99 234.28 367.041 235.38C364.436 236.849 361.782 238.783 359.108 240.105C356.331 241.477 353.823 242.691 350.791 243.27C348.6 243.687 345.501 243.641 343.836 241.884C343.526 241.554 343.389 241.51 343.644 241.104Z", fill: "url(#paint17_linear_1356_14057)" }), h("path", { d: "M190.163 273.219C222.905 268.849 232.64 262.943 213.962 259.057C195.285 255.172 187.5 262.08 188.434 266.2C189.366 270.32 180.381 271.326 169.286 272.39C158.193 273.454 157.141 276.354 190.163 273.219Z", fill: "url(#paint18_linear_1356_14057)" }), h("path", { d: "M295.952 95.4242L241.395 135.226L251.54 111.77L295.952 95.4242Z", fill: "#E17726", stroke: "#E17726", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M170.111 95.4242L224.181 135.598L214.522 111.77L170.111 95.4242Z", fill: "#E27625", stroke: "#E27625", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M276.309 187.712L261.793 209.576L292.876 218.014L301.78 188.189L276.309 187.712Z", fill: "#E27625", stroke: "#E27625", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M164.336 188.189L173.186 218.014L204.215 209.576L189.753 187.712L164.336 188.189Z", fill: "#E27625", stroke: "#E27625", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M202.542 150.776L193.908 163.618L224.667 164.998L223.642 132.36L202.542 150.776Z", fill: "#E27625", stroke: "#E27625", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M263.52 150.775L242.097 131.989L241.395 164.998L272.154 163.618L263.52 150.775Z", fill: "#E27625", stroke: "#E27625", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M204.215 209.576L222.832 200.714L206.805 188.402L204.215 209.576Z", fill: "#E27625", stroke: "#E27625", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M243.23 200.714L261.793 209.576L259.257 188.402L243.23 200.714Z", fill: "#E27625", stroke: "#E27625", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M261.793 209.576L243.23 200.714L244.741 212.601L244.579 217.643L261.793 209.576Z", fill: "#D5BFB2", stroke: "#D5BFB2", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M204.215 209.576L221.483 217.643L221.375 212.601L222.832 200.714L204.215 209.576Z", fill: "#D5BFB2", stroke: "#D5BFB2", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M221.807 180.547L206.373 176.09L217.274 171.154L221.807 180.547Z", fill: "#233447", stroke: "#233447", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M244.255 180.547L248.788 171.154L259.742 176.09L244.255 180.547Z", fill: "#233447", stroke: "#233447", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M204.215 209.576L206.913 187.712L189.753 188.189L204.215 209.576Z", fill: "#CC6228", stroke: "#CC6228", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M259.149 187.712L261.793 209.576L276.309 188.189L259.149 187.712Z", fill: "#CC6228", stroke: "#CC6228", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M272.154 163.618L241.395 164.998L244.255 180.547L248.788 171.154L259.742 176.09L272.154 163.618Z", fill: "#CC6228", stroke: "#CC6228", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M206.373 176.09L217.274 171.154L221.807 180.547L224.667 164.998L193.908 163.618L206.373 176.09Z", fill: "#CC6228", stroke: "#CC6228", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M193.908 163.618L206.805 188.402L206.373 176.09L193.908 163.618Z", fill: "#E27525", stroke: "#E27525", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M259.743 176.09L259.257 188.402L272.154 163.618L259.743 176.09Z", fill: "#E27525", stroke: "#E27525", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M224.667 164.998L221.807 180.548L225.422 198.909L226.232 174.71L224.667 164.998Z", fill: "#E27525", stroke: "#E27525", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M241.395 164.998L239.884 174.657L240.64 198.909L244.255 180.548L241.395 164.998Z", fill: "#E27525", stroke: "#E27525", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M244.255 180.547L240.64 198.909L243.23 200.714L259.257 188.402L259.743 176.09L244.255 180.547Z", fill: "#F5841F", stroke: "#F5841F", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M206.373 176.09L206.805 188.402L222.832 200.714L225.422 198.909L221.807 180.547L206.373 176.09Z", fill: "#F5841F", stroke: "#F5841F", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M244.579 217.643L244.741 212.601L243.338 211.434H222.724L221.375 212.601L221.483 217.643L204.215 209.576L210.259 214.459L222.508 222.791H243.5L255.803 214.459L261.793 209.576L244.579 217.643Z", fill: "#C0AC9D", stroke: "#C0AC9D", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M243.23 200.714L240.64 198.909H225.422L222.832 200.714L221.375 212.601L222.724 211.434H243.338L244.741 212.601L243.23 200.714Z", fill: "#161616", stroke: "#161616", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M298.272 137.827L302.859 115.856L295.952 95.4242L243.23 133.899L263.52 150.775L292.174 159.001L298.488 151.731L295.736 149.767L300.107 145.84L296.761 143.293L301.132 140.002L298.272 137.827Z", fill: "#763E1A", stroke: "#763E1A", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M163.203 115.856L167.844 137.827L164.876 140.002L169.301 143.293L165.955 145.84L170.326 149.767L167.574 151.731L173.888 159.001L202.542 150.775L222.832 133.899L170.11 95.4242L163.203 115.856Z", fill: "#763E1A", stroke: "#763E1A", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M292.175 159.001L263.52 150.775L272.154 163.618L259.257 188.402L276.309 188.189H301.78L292.175 159.001Z", fill: "#F5841F", stroke: "#F5841F", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M202.542 150.775L173.888 159.001L164.336 188.189H189.753L206.805 188.402L193.908 163.618L202.542 150.775Z", fill: "#F5841F", stroke: "#F5841F", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("path", { d: "M241.395 164.998L243.23 133.9L251.54 111.77H214.522L222.832 133.9L224.667 164.998L225.368 174.763L225.422 198.909H240.64L240.694 174.763L241.395 164.998Z", fill: "#F5841F", stroke: "#F5841F", "stroke-width": "0.94513", "stroke-linecap": "round", "stroke-linejoin": "round" }), h("defs", null, h("linearGradient", { id: "paint0_linear_1356_14057", x1: "335.991", y1: "250.487", x2: "303.873", y2: "266.801", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#FFE466" }), h("stop", { offset: "1", "stop-color": "#FFAFEA" })), h("linearGradient", { id: "paint1_linear_1356_14057", x1: "276.993", y1: "303.722", x2: "205.254", y2: "401.574", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#75C3FC" }), h("stop", { offset: "0.0929", "stop-color": "#81C2F6" }), h("stop", { offset: "1", "stop-color": "#F0B8BD" })), h("linearGradient", { id: "paint2_linear_1356_14057", x1: "271.074", y1: "119.924", x2: "553.077", y2: "104.53", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#FFE466" }), h("stop", { offset: "1", "stop-color": "#FFAFEA" })), h("linearGradient", { id: "paint3_linear_1356_14057", x1: "264.209", y1: "91.0943", x2: "357.834", y2: "72.8792", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#FFE466" }), h("stop", { offset: "1", "stop-color": "#FFAFEA" })), h("linearGradient", { id: "paint4_linear_1356_14057", x1: "212.46", y1: "121.997", x2: "92.6119", y2: "183.406", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#FFE466" }), h("stop", { offset: "1", "stop-color": "#FFAFEA" })), h("linearGradient", { id: "paint5_linear_1356_14057", x1: "23.0498", y1: "204.411", x2: "161.86", y2: "163.003", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#75C3FC" }), h("stop", { offset: "0.0929", "stop-color": "#81C2F6" }), h("stop", { offset: "1", "stop-color": "#F0B8BD" })), h("linearGradient", { id: "paint6_linear_1356_14057", x1: "78.8647", y1: "240.375", x2: "74.9655", y2: "241.328", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#75C3FC" }), h("stop", { offset: "1", "stop-color": "#75C3FC" })), h("linearGradient", { id: "paint7_linear_1356_14057", x1: "382.349", y1: "181.971", x2: "378.45", y2: "182.925", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#75C3FC" }), h("stop", { offset: "1", "stop-color": "#75C3FC" })), h("linearGradient", { id: "paint8_linear_1356_14057", x1: "349.889", y1: "244.583", x2: "345.99", y2: "245.537", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#75C3FC" }), h("stop", { offset: "1", "stop-color": "#75C3FC" })), h("linearGradient", { id: "paint9_linear_1356_14057", x1: "142.262", y1: "279.808", x2: "138.362", y2: "280.762", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#75C3FC" }), h("stop", { offset: "1", "stop-color": "#75C3FC" })), h("linearGradient", { id: "paint10_linear_1356_14057", x1: "123.305", y1: "99.0746", x2: "119.406", y2: "100.028", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#75C3FC" }), h("stop", { offset: "1", "stop-color": "#75C3FC" })), h("linearGradient", { id: "paint11_linear_1356_14057", x1: "371.818", y1: "156.617", x2: "367.919", y2: "157.571", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#75C3FC" }), h("stop", { offset: "1", "stop-color": "#75C3FC" })), h("linearGradient", { id: "paint12_linear_1356_14057", x1: "385.281", y1: "242.958", x2: "381.382", y2: "243.911", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#75C3FC" }), h("stop", { offset: "1", "stop-color": "#75C3FC" })), h("linearGradient", { id: "paint13_linear_1356_14057", x1: "367.125", y1: "37.5052", x2: "360.843", y2: "38.8076", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#75C3FC" }), h("stop", { offset: "1", "stop-color": "#75C3FC" })), h("linearGradient", { id: "paint14_linear_1356_14057", x1: "300.182", y1: "91.321", x2: "479.464", y2: "104.041", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#FFE466" }), h("stop", { offset: "1", "stop-color": "#FFAFEA" })), h("linearGradient", { id: "paint15_linear_1356_14057", x1: "363.434", y1: "201.232", x2: "102.977", y2: "302.269", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#FFE466" }), h("stop", { offset: "1", "stop-color": "#FFAFEA" })), h("linearGradient", { id: "paint16_linear_1356_14057", x1: "447.962", y1: "165.159", x2: "313.049", y2: "197.95", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#75C3FC" }), h("stop", { offset: "0.0929", "stop-color": "#81C2F6" }), h("stop", { offset: "1", "stop-color": "#F0B8BD" })), h("linearGradient", { id: "paint17_linear_1356_14057", x1: "410.211", y1: "215.859", x2: "341.378", y2: "232.788", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#75C3FC" }), h("stop", { offset: "0.0929", "stop-color": "#81C2F6" }), h("stop", { offset: "1", "stop-color": "#F0B8BD" })), h("linearGradient", { id: "paint18_linear_1356_14057", x1: "222.282", y1: "258.986", x2: "162.257", y2: "273.774", gradientUnits: "userSpaceOnUse" }, h("stop", { "stop-color": "#FFE466" }), h("stop", { offset: "1", "stop-color": "#FFAFEA" })))));
//# sourceMappingURL=MetamaskExtensionImage.js.map
