import { h } from "@stencil/core";
export const MetamaskExtensionImage = () => (h("svg", { width: "400", height: "300", viewBox: "0 0 400 300", fill: "none", xmlns: "http://www.w3.org/2000/svg" }, h("rect", { width: "400", height: "300", fill: "white" }), h("path", { d: "M300.116 242.46L250.485 227.681L213.057 250.055L186.944 250.045L149.493 227.681L99.8843 242.46L84.7939 191.518L99.8843 134.979L84.7939 87.1777L99.8843 27.9336L177.402 74.2466H222.598L300.116 27.9336L315.206 87.1777L300.116 134.979L315.206 191.518L300.116 242.46Z", fill: "#FF5C16" }), h("path", { d: "M99.8955 27.9336L177.414 74.2792L174.331 106.085L99.8955 27.9336Z", fill: "#FF5C16" }), h("path", { d: "M149.505 191.539L183.612 217.521L149.505 227.681V191.539Z", fill: "#FF5C16" }), h("path", { d: "M180.886 148.585L174.33 106.107L132.369 134.991L132.348 134.98V135.001L132.477 164.732L149.493 148.585H149.504H180.886Z", fill: "#FF5C16" }), h("path", { d: "M300.116 27.9336L222.598 74.2792L225.67 106.085L300.116 27.9336Z", fill: "#FF5C16" }), h("path", { d: "M250.508 191.539L216.4 217.521L250.508 227.681V191.539Z", fill: "#FF5C16" }), h("path", { d: "M267.652 135.001H267.663H267.652V134.98L267.641 134.991L225.68 106.107L219.125 148.585H250.507L267.533 164.732L267.652 135.001Z", fill: "#FF5C16" }), h("path", { d: "M149.493 227.681L99.8843 242.46L84.7939 191.54H149.493V227.681Z", fill: "#E34807" }), h("path", { d: "M180.875 148.574L190.351 209.98L177.219 175.838L132.456 164.732L149.483 148.574H180.864H180.875Z", fill: "#E34807" }), h("path", { d: "M250.507 227.681L300.116 242.46L315.206 191.54H250.507V227.681Z", fill: "#E34807" }), h("path", { d: "M219.126 148.574L209.649 209.98L222.782 175.838L267.545 164.732L250.507 148.574H219.126Z", fill: "#E34807" }), h("path", { d: "M84.7939 191.517L99.8843 134.979H132.337L132.456 164.721L177.219 175.826L190.351 209.969L183.601 217.488L149.493 191.506H84.7939V191.517Z", fill: "#FF8D5D" }), h("path", { d: "M315.206 191.517L300.116 134.979H267.664L267.545 164.721L222.782 175.826L209.649 209.969L216.4 217.488L250.507 191.506H315.206V191.517Z", fill: "#FF8D5D" }), h("path", { d: "M222.598 74.2466H200H177.402L174.33 106.053L190.351 209.936H209.649L225.681 106.053L222.598 74.2466Z", fill: "#FF8D5D" }), h("path", { d: "M99.8843 27.9336L84.7939 87.1777L99.8843 134.979H132.337L174.319 106.085L99.8843 27.9336Z", fill: "#661800" }), h("path", { d: "M171.496 160.906H156.795L148.79 168.752L177.229 175.804L171.496 160.896V160.906Z", fill: "#661800" }), h("path", { d: "M300.116 27.9336L315.206 87.1777L300.116 134.979H267.663L225.681 106.085L300.116 27.9336Z", fill: "#661800" }), h("path", { d: "M228.525 160.906H243.248L251.253 168.763L222.781 175.826L228.525 160.896V160.906Z", fill: "#661800" }), h("path", { d: "M213.046 229.789L216.399 217.51L209.649 209.991H190.34L183.59 217.51L186.943 229.789", fill: "#661800" }), h("path", { d: "M213.047 229.789V250.066H186.944V229.789H213.047Z", fill: "#C0C4CD" }), h("path", { d: "M149.504 227.66L186.965 250.056V229.779L183.611 217.5L149.504 227.66Z", fill: "#E7EBF6" }), h("path", { d: "M250.506 227.66L213.045 250.056V229.779L216.398 217.5L250.506 227.66Z", fill: "#E7EBF6" })));
//# sourceMappingURL=MetamaskExtensionImage.js.map
