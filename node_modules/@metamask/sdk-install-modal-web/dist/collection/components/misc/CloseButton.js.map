{"version": 3, "file": "CloseButton.js", "sourceRoot": "", "sources": ["../../../../src/components/misc/CloseButton.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,CAAC,EAAE,MAAM,eAAe,CAAC;AAElC,MAAM,WAAW,GAAG,GAAG,EAAE,CAAC,CACxB,WACE,KAAK,EAAC,IAAI,EACV,MAAM,EAAC,IAAI,EACX,OAAO,EAAC,WAAW,EACnB,IAAI,EAAC,MAAM,EACX,KAAK,EAAC,4BAA4B;IAElC,YAAM,KAAK,EAAC,IAAI,EAAC,MAAM,EAAC,IAAI,EAAC,IAAI,EAAC,OAAO,GAAG;IAC5C,yBACY,SAAS,eACT,SAAS,EACnB,CAAC,EAAC,ufAAuf,EACzf,IAAI,EAAC,SAAS,GACd,CACE,CACP,CAAC;AAEF,eAAe,WAAW,CAAC", "sourcesContent": ["import { h } from \"@stencil/core\";\n\nconst CloseButton = () => (\n  <svg\n    width=\"14\"\n    height=\"14\"\n    viewBox=\"0 0 16 16\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <rect width=\"16\" height=\"16\" fill=\"white\" />\n    <path\n      fill-rule=\"evenodd\"\n      clip-rule=\"evenodd\"\n      d=\"M2.40554 2.40554C2.94627 1.86482 3.82296 1.86482 4.36369 2.40554L8 6.04186L11.6363 2.40554C12.177 1.86482 13.0537 1.86482 13.5945 2.40554C14.1352 2.94627 14.1352 3.82296 13.5945 4.36369L9.95814 8L13.5945 11.6363C14.1352 12.177 14.1352 13.0537 13.5945 13.5945C13.0537 14.1352 12.177 14.1352 11.6363 13.5945L8 9.95814L4.36369 13.5945C3.82296 14.1352 2.94627 14.1352 2.40554 13.5945C1.86482 13.0537 1.86482 12.177 2.40554 11.6363L6.04186 8L2.40554 4.36369C1.86482 3.82296 1.86482 2.94627 2.40554 2.40554Z\"\n      fill=\"#BBC0C5\"\n    />\n  </svg>\n);\n\nexport default CloseButton;\n"]}