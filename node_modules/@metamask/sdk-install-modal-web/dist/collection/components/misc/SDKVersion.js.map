{"version": 3, "file": "SDKVersion.js", "sourceRoot": "", "sources": ["../../../../src/components/misc/SDKVersion.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,CAAC,EAAE,MAAM,eAAe,CAAC;AAElC,MAAM,CAAC,OAAO,UAAU,UAAU,CAAC,EAAC,OAAO,EAAqB;IAC9D,OAAO,CACL,WAAK,KAAK,EAAE,EAAC,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAC;;QAAe,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,EAAE,CAAA,CAAC,CAAA,SAAS,CAAO,CAC3H,CAAA;AACH,CAAC", "sourcesContent": ["import { h } from \"@stencil/core\";\n\nexport default function SDKVersion({version}: {version?: string}) {\n  return (\n    <div style={{textAlign: 'center', color: '#BBC0C5', fontSize: '12'}}>SDK Version {version ? `v${version}`:`unknown`}</div>\n  )\n}\n"]}