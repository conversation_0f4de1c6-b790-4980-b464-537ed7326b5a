{"version": 3, "file": "Logo.js", "sourceRoot": "", "sources": ["../../../../src/components/misc/Logo.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,CAAC,EAAE,MAAM,eAAe,CAAC;AAElC,MAAM,IAAI,GAAG,GAAG,EAAE,CAAC,CACjB,WACE,KAAK,EAAC,KAAK,EACX,MAAM,EAAC,IAAI,EACX,OAAO,EAAC,YAAY,EACpB,IAAI,EAAC,MAAM,EACX,KAAK,EAAC,4BAA4B;IAElC,YACE,CAAC,EAAC,m9BAAm9B,EACr9B,IAAI,EAAC,SAAS,GACd;IACF,YACE,CAAC,EAAC,k0BAAk0B,EACp0B,IAAI,EAAC,SAAS,GACd;IACF,YACE,CAAC,EAAC,myBAAmyB,EACryB,IAAI,EAAC,SAAS,GACd;IACF,YACE,CAAC,EAAC,iYAAiY,EACnY,IAAI,EAAC,SAAS,GACd;IACF,YACE,CAAC,EAAC,+rBAA+rB,EACjsB,IAAI,EAAC,SAAS,GACd;IACF,YACE,CAAC,EAAC,isBAAisB,EACnsB,IAAI,EAAC,SAAS,GACd;IACF,YACE,CAAC,EAAC,6tBAA6tB,EAC/tB,IAAI,EAAC,SAAS,GACd;IACF,YACE,CAAC,EAAC,2pBAA2pB,EAC7pB,IAAI,EAAC,SAAS,GACd;IACF,YACE,CAAC,EAAC,qDAAqD,EACvD,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,uDAAuD,EACzD,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,kFAAkF,EACpF,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,mFAAmF,EACrF,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,kFAAkF,EACpF,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,kFAAkF,EACpF,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,kEAAkE,EACpE,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,mEAAmE,EACrE,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,mFAAmF,EACrF,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,mFAAmF,EACrF,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,mEAAmE,EACrE,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,mEAAmE,EACrE,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,mEAAmE,EACrE,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,mEAAmE,EACrE,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,iGAAiG,EACnG,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,kGAAkG,EACpG,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,mEAAmE,EACrE,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,mEAAmE,EACrE,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,iFAAiF,EACnF,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,kFAAkF,EACpF,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,mGAAmG,EACrG,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,kGAAkG,EACpG,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,kMAAkM,EACpM,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,gIAAgI,EAClI,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,yLAAyL,EAC3L,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,+KAA+K,EACjL,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,0GAA0G,EAC5G,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,2GAA2G,EAC7G,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB;IACF,YACE,CAAC,EAAC,kKAAkK,EACpK,IAAI,EAAC,SAAS,EACd,MAAM,EAAC,SAAS,kBACH,KAAK,oBACH,OAAO,qBACN,OAAO,GACvB,CACE,CACP,CAAC;AAEF,eAAe,IAAI,CAAC", "sourcesContent": ["import { h } from \"@stencil/core\";\n\nconst Logo = () => (\n  <svg\n    width=\"273\"\n    height=\"51\"\n    viewBox=\"0 0 273 51\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <path\n      d=\"M240.882 25.9263C239.472 24.997 237.916 24.3361 236.443 23.5101C235.489 22.9731 234.473 22.4982 233.643 21.8167C232.233 20.6602 232.524 18.3885 233.996 17.3973C236.112 15.993 239.617 16.7777 239.99 19.6483C239.99 19.7102 240.052 19.7515 240.114 19.7515H243.308C243.391 19.7515 243.454 19.6896 243.433 19.607C243.267 17.6244 242.5 15.9723 241.089 14.9191C239.741 13.9072 238.206 13.3702 236.568 13.3702C228.127 13.3702 227.359 22.271 231.901 25.0796C232.42 25.41 236.879 27.6404 238.455 28.611C240.032 29.5816 240.529 31.3576 239.845 32.7619C239.223 34.0423 237.605 34.9303 235.987 34.8271C234.224 34.7238 232.855 33.7738 232.378 32.2869C232.295 32.0185 232.254 31.5022 232.254 31.275C232.254 31.2131 232.192 31.1511 232.129 31.1511H228.666C228.604 31.1511 228.541 31.2131 228.541 31.275C228.541 33.7738 229.164 35.1575 230.864 36.4172C232.461 37.615 234.203 38.1106 236.008 38.1106C240.737 38.1106 243.184 35.4466 243.682 32.6793C244.117 29.974 243.308 27.5371 240.882 25.9263Z\"\n      fill=\"#24292E\"\n    />\n    <path\n      d=\"M90.4943 13.8246H88.9595H87.2795C87.2173 13.8246 87.1758 13.8659 87.1551 13.9072L84.3137 23.2416C84.2722 23.3655 84.1063 23.3655 84.0648 23.2416L81.2234 13.9072C81.2026 13.8452 81.1612 13.8246 81.0989 13.8246H79.419H77.8842H75.8102C75.748 13.8246 75.6857 13.8865 75.6857 13.9485V37.7802C75.6857 37.8422 75.748 37.9041 75.8102 37.9041H79.2738C79.336 37.9041 79.3982 37.8422 79.3982 37.7802V19.6689C79.3982 19.5244 79.6056 19.5037 79.6471 19.6276L82.5093 29.024L82.7167 29.6849C82.7374 29.7468 82.7789 29.7675 82.8411 29.7675H85.4959C85.5581 29.7675 85.5996 29.7262 85.6203 29.6849L85.8277 29.024L88.6899 19.6276C88.7313 19.4831 88.9387 19.5244 88.9387 19.6689V37.7802C88.9387 37.8422 89.001 37.9041 89.0632 37.9041H92.5268C92.589 37.9041 92.6513 37.8422 92.6513 37.7802V13.9485C92.6513 13.8865 92.589 13.8246 92.5268 13.8246H90.4943Z\"\n      fill=\"#24292E\"\n    />\n    <path\n      d=\"M187.849 13.8246C187.787 13.8246 187.745 13.8659 187.725 13.9072L184.883 23.2416C184.842 23.3655 184.676 23.3655 184.634 23.2416L181.793 13.9072C181.772 13.8452 181.731 13.8246 181.668 13.8246H176.4C176.338 13.8246 176.276 13.8865 176.276 13.9485V37.7802C176.276 37.8422 176.338 37.9041 176.4 37.9041H179.864C179.926 37.9041 179.988 37.8422 179.988 37.7802V19.6689C179.988 19.5244 180.196 19.5037 180.237 19.6276L183.099 29.024L183.307 29.6849C183.328 29.7468 183.369 29.7675 183.431 29.7675H186.086C186.148 29.7675 186.19 29.7262 186.211 29.6849L186.418 29.024L189.28 19.6276C189.322 19.4831 189.529 19.5244 189.529 19.6689V37.7802C189.529 37.8422 189.591 37.9041 189.653 37.9041H193.117C193.179 37.9041 193.241 37.8422 193.241 37.7802V13.9485C193.241 13.8865 193.179 13.8246 193.117 13.8246H187.849Z\"\n      fill=\"#24292E\"\n    />\n    <path\n      d=\"M143.174 13.8246H136.724H133.261H126.81C126.748 13.8246 126.686 13.8865 126.686 13.9485V16.9223C126.686 16.9843 126.748 17.0462 126.81 17.0462H133.136V37.7802C133.136 37.8422 133.198 37.9041 133.261 37.9041H136.724C136.786 37.9041 136.849 37.8422 136.849 37.7802V17.0462H143.174C143.237 17.0462 143.299 16.9843 143.299 16.9223V13.9485C143.299 13.8865 143.257 13.8246 143.174 13.8246Z\"\n      fill=\"#24292E\"\n    />\n    <path\n      d=\"M163.604 37.9041H166.756C166.839 37.9041 166.901 37.8215 166.881 37.7389L160.368 13.8245C160.347 13.7626 160.306 13.7419 160.244 13.7419H159.041H156.925H155.722C155.66 13.7419 155.619 13.7832 155.598 13.8245L149.085 37.7389C149.065 37.8215 149.127 37.9041 149.21 37.9041H152.362C152.425 37.9041 152.466 37.8628 152.487 37.8215L154.374 30.862C154.395 30.8 154.436 30.7794 154.499 30.7794H161.467C161.53 30.7794 161.571 30.8207 161.592 30.862L163.479 37.8215C163.5 37.8628 163.562 37.9041 163.604 37.9041ZM155.328 27.3719L157.859 18.0581C157.9 17.9342 158.066 17.9342 158.107 18.0581L160.638 27.3719C160.659 27.4545 160.596 27.5371 160.513 27.5371H155.453C155.37 27.5371 155.308 27.4545 155.328 27.3719Z\"\n      fill=\"#24292E\"\n    />\n    <path\n      d=\"M217.362 37.9041H220.515C220.598 37.9041 220.66 37.8215 220.639 37.7389L214.127 13.8245C214.106 13.7626 214.065 13.7419 214.002 13.7419H212.8H210.684H209.481C209.419 13.7419 209.377 13.7832 209.357 13.8245L202.844 37.7389C202.823 37.8215 202.886 37.9041 202.969 37.9041H206.121C206.183 37.9041 206.225 37.8628 206.246 37.8215L208.133 30.862C208.154 30.8 208.195 30.7794 208.257 30.7794H215.226C215.288 30.7794 215.33 30.8207 215.351 30.862L217.238 37.8215C217.259 37.8628 217.3 37.9041 217.362 37.9041ZM209.087 27.3719L211.617 18.0581C211.659 17.9342 211.825 17.9342 211.866 18.0581L214.397 27.3719C214.417 27.4545 214.355 27.5371 214.272 27.5371H209.211C209.129 27.5371 209.066 27.4545 209.087 27.3719Z\"\n      fill=\"#24292E\"\n    />\n    <path\n      d=\"M106.713 34.3727V26.9795C106.713 26.9176 106.775 26.8556 106.837 26.8556H116.067C116.129 26.8556 116.191 26.7936 116.191 26.7317V23.7579C116.191 23.6959 116.129 23.634 116.067 23.634H106.837C106.775 23.634 106.713 23.572 106.713 23.5101V17.1907C106.713 17.1288 106.775 17.0668 106.837 17.0668H117.332C117.394 17.0668 117.457 17.0049 117.457 16.9429V13.9691C117.457 13.9072 117.394 13.8452 117.332 13.8452H106.713H103.125C103.063 13.8452 103.001 13.9072 103.001 13.9691V17.0668V23.6546V26.8763V34.5173V37.7802C103.001 37.8422 103.063 37.9041 103.125 37.9041H106.713H117.768C117.83 37.9041 117.892 37.8422 117.892 37.7802V34.6412C117.892 34.5792 117.83 34.5173 117.768 34.5173H106.817C106.755 34.4966 106.713 34.4553 106.713 34.3727Z\"\n      fill=\"#24292E\"\n    />\n    <path\n      d=\"M272.532 37.6976L260.544 25.3687C260.502 25.3274 260.502 25.2448 260.544 25.2035L271.329 14.0517C271.412 13.9691 271.349 13.8452 271.246 13.8452H266.828C266.787 13.8452 266.766 13.8659 266.745 13.8865L257.599 23.3449C257.516 23.4275 257.391 23.3655 257.391 23.2623V13.9691C257.391 13.9072 257.329 13.8452 257.267 13.8452H253.803C253.741 13.8452 253.679 13.9072 253.679 13.9691V37.8009C253.679 37.8628 253.741 37.9248 253.803 37.9248H257.267C257.329 37.9248 257.391 37.8628 257.391 37.8009V27.3099C257.391 27.2067 257.536 27.1447 257.599 27.2273L267.969 37.8835C267.989 37.9041 268.031 37.9248 268.052 37.9248H272.469C272.552 37.9041 272.615 37.7596 272.532 37.6976Z\"\n      fill=\"#24292E\"\n    />\n    <path\n      d=\"M52.021 1L31.0526 16.4886L34.9517 7.36063L52.021 1Z\"\n      fill=\"#E17726\"\n      stroke=\"#E17726\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M3.65491 1L24.4366 16.6331L20.7241 7.36063L3.65491 1Z\"\n      fill=\"#E27625\"\n      stroke=\"#E27625\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M44.4716 36.9127L38.8925 45.4211L50.8389 48.7047L54.261 37.0986L44.4716 36.9127Z\"\n      fill=\"#E27625\"\n      stroke=\"#E27625\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M1.43555 37.0986L4.83695 48.7047L16.7626 45.4211L11.2042 36.9127L1.43555 37.0986Z\"\n      fill=\"#E27625\"\n      stroke=\"#E27625\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M16.1197 22.5395L12.8013 27.5371L24.6232 28.074L24.2292 15.3734L16.1197 22.5395Z\"\n      fill=\"#E27625\"\n      stroke=\"#E27625\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M39.5561 22.5394L31.3222 15.2288L31.0526 28.0739L42.8746 27.537L39.5561 22.5394Z\"\n      fill=\"#E27625\"\n      stroke=\"#E27625\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M16.7626 45.4212L23.918 41.9724L17.7582 37.1813L16.7626 45.4212Z\"\n      fill=\"#E27625\"\n      stroke=\"#E27625\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M31.7578 41.9724L38.8925 45.4212L37.9177 37.1813L31.7578 41.9724Z\"\n      fill=\"#E27625\"\n      stroke=\"#E27625\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M38.8923 45.4212L31.7577 41.9724L32.3384 46.5983L32.2762 48.5602L38.8923 45.4212Z\"\n      fill=\"#D5BFB2\"\n      stroke=\"#D5BFB2\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M16.7625 45.4212L23.3994 48.5602L23.3579 46.5983L23.9179 41.9724L16.7625 45.4212Z\"\n      fill=\"#D5BFB2\"\n      stroke=\"#D5BFB2\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M23.5239 34.1249L17.5922 32.3902L21.7818 30.4696L23.5239 34.1249Z\"\n      fill=\"#233447\"\n      stroke=\"#233447\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M32.1517 34.1249L33.8939 30.4696L38.1042 32.3902L32.1517 34.1249Z\"\n      fill=\"#233447\"\n      stroke=\"#233447\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M16.7626 45.4212L17.7996 36.9128L11.2042 37.0987L16.7626 45.4212Z\"\n      fill=\"#CC6228\"\n      stroke=\"#CC6228\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M37.8761 36.9128L38.8924 45.4212L44.4715 37.0987L37.8761 36.9128Z\"\n      fill=\"#CC6228\"\n      stroke=\"#CC6228\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M42.8745 27.5371L31.0526 28.074L32.1518 34.1249L33.894 30.4696L38.1042 32.3902L42.8745 27.5371Z\"\n      fill=\"#CC6228\"\n      stroke=\"#CC6228\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M17.5922 32.3902L21.7817 30.4696L23.5239 34.1249L24.6232 28.074L12.8012 27.5371L17.5922 32.3902Z\"\n      fill=\"#CC6228\"\n      stroke=\"#CC6228\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M12.8013 27.5371L17.7582 37.1813L17.5923 32.3902L12.8013 27.5371Z\"\n      fill=\"#E27525\"\n      stroke=\"#E27525\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M38.1044 32.3902L37.9177 37.1813L42.8746 27.5371L38.1044 32.3902Z\"\n      fill=\"#E27525\"\n      stroke=\"#E27525\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M24.6232 28.0741L23.524 34.125L24.9136 41.2703L25.2247 31.8533L24.6232 28.0741Z\"\n      fill=\"#E27525\"\n      stroke=\"#E27525\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M31.0526 28.0741L30.4719 31.8327L30.7623 41.2703L32.1519 34.125L31.0526 28.0741Z\"\n      fill=\"#E27525\"\n      stroke=\"#E27525\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M32.1519 34.1249L30.7623 41.2702L31.7578 41.9724L37.9177 37.1813L38.1043 32.3901L32.1519 34.1249Z\"\n      fill=\"#F5841F\"\n      stroke=\"#F5841F\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M17.5922 32.3901L17.7581 37.1813L23.918 41.9724L24.9135 41.2702L23.5239 34.1249L17.5922 32.3901Z\"\n      fill=\"#F5841F\"\n      stroke=\"#F5841F\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M32.2763 48.5602L32.3385 46.5983L31.7993 46.1439H23.8765L23.358 46.5983L23.3995 48.5602L16.7626 45.4211L19.0855 47.3211L23.7935 50.5633H31.8615L36.5903 47.3211L38.8924 45.4211L32.2763 48.5602Z\"\n      fill=\"#C0AC9D\"\n      stroke=\"#C0AC9D\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M31.7578 41.9724L30.7622 41.2703H24.9135L23.918 41.9724L23.358 46.5983L23.8765 46.144H31.7993L32.3385 46.5983L31.7578 41.9724Z\"\n      fill=\"#24292E\"\n      stroke=\"#24292E\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M52.9128 17.5005L54.6757 8.95079L52.021 1L31.7578 15.9723L39.5561 22.5394L50.5692 25.7404L52.9958 22.9111L51.938 22.147L53.618 20.6188L52.3321 19.6276L54.0121 18.3472L52.9128 17.5005Z\"\n      fill=\"#763E1A\"\n      stroke=\"#763E1A\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M1 8.95079L2.78366 17.5005L1.64295 18.3472L3.34365 19.6276L2.05775 20.6188L3.73771 22.147L2.67996 22.9111L5.10657 25.7404L16.1196 22.5394L23.918 15.9723L3.65475 1L1 8.95079Z\"\n      fill=\"#763E1A\"\n      stroke=\"#763E1A\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M50.5692 25.7404L39.5561 22.5394L42.8746 27.5371L37.9177 37.1813L44.4716 37.0987H54.261L50.5692 25.7404Z\"\n      fill=\"#F5841F\"\n      stroke=\"#F5841F\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M16.1196 22.5394L5.10651 25.7404L1.43549 37.0987H11.2041L17.7581 37.1813L12.8011 27.5371L16.1196 22.5394Z\"\n      fill=\"#F5841F\"\n      stroke=\"#F5841F\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n    <path\n      d=\"M31.0526 28.0741L31.7578 15.9724L34.9518 7.36072H20.7239L23.9179 15.9724L24.6231 28.0741L24.8927 31.8739L24.9135 41.2703H30.7622L30.783 31.8739L31.0526 28.0741Z\"\n      fill=\"#F5841F\"\n      stroke=\"#F5841F\"\n      stroke-width=\"0.5\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    />\n  </svg>\n);\n\nexport default Logo;\n"]}