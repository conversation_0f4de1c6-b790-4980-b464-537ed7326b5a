{"version": 3, "file": "simple-i18n.js", "sourceRoot": "", "sources": ["../../../../src/components/misc/simple-i18n.ts"], "names": [], "mappings": "AASA,MAAM,mBAAmB,GAAoB;IAC3C,SAAS,EAAE,SAAS;IACpB,QAAQ,EAAE,QAAQ;IAClB,sBAAsB,EAAE,qBAAqB;IAC7C,iBAAiB,EAAE,+BAA+B;IAClD,wBAAwB,EAAE,iCAAiC;IAC3D,eAAe,EAAE;QACf,kBAAkB,EAAE,+EAA+E;QACnG,uBAAuB,EAAE,8HAA8H;QACvJ,8BAA8B,EAAE,kGAAkG;QAClI,6BAA6B,EAAE,4BAA4B;KAC5D;IACD,eAAe,EAAE;QACf,4BAA4B,EAAE,qFAAqF;QACnH,yBAAyB,EAAE,sDAAsD;QACjF,0BAA0B,EAAE,oGAAoG;QAChI,YAAY,EAAE,YAAY;KAC3B;IACD,cAAc,EAAE;QACd,0BAA0B,EAAE,oHAAoH;KACjJ;IACD,iBAAiB,EAAE;QACjB,gBAAgB,EAAE,8BAA8B;QAChD,YAAY,EAAE,YAAY;QAC1B,gBAAgB,EAAE,gBAAgB;KACnC;CACO,CAAC;AAEX,MAAM,OAAO,UAAU;IAKrB,YAAY,MAA6B;;QAJjC,iBAAY,GAAoB,mBAAmB,CAAC;QACpD,qBAAgB,GAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAIxE,IAAI,CAAC,OAAO,GAAG,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,mCAAI,qFAAqF,CAAC;IAC1H,CAAC;IAEO,kBAAkB;QACxB,mDAAmD;QACnD,MAAM,gBAAgB,GAAG,SAAS,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAErE,qDAAqD;QACrD,MAAM,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC9C,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CACpC,CAAC;QAEF,sCAAsC;QACtC,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;QAED,iDAAiD;QACjD,MAAM,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAC;QACvC,MAAM,SAAS,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9C,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,MAA+B;QACxC,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9C,MAAM,MAAM,GAAG,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC;QACjD,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,MAAc;QACnC,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEzC,IAAI,WAAW,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACvE,IAAI,CAAC,YAAY,GAAG,mBAAmB,CAAC;YACxC,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,IAAI,WAAW,OAAO,CAAC;YAClD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC;YAElC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAAE,MAAM,IAAI,KAAK,CAAC,uBAAuB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YAC5E,IAAI,CAAC,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,IAAI,CAAC,oBAAoB,WAAW,yCAAyC,EAAE,KAAK,CAAC,CAAC;YAC9F,IAAI,CAAC,YAAY,GAAG,mBAAmB,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,CAAC,CAAC,GAAW;QACX,OAAO,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC;IAClE,CAAC;IAEO,oBAAoB,CAAC,GAAW,EAAE,IAAqB;QAC7D,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,OAAO,GAA6B,IAAI,CAAC;QAE7C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,OAAO,OAAO,KAAK,QAAQ;gBAAE,OAAO,EAAE,CAAC;YAC3C,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;QAED,OAAO,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IACpD,CAAC;CACF", "sourcesContent": ["interface I18nInstance {\n  t: (key: string) => string;\n  init: (config: { fallbackLng: string }) => Promise<void>;\n}\n\ninterface TranslationDict {\n  [key: string]: string | TranslationDict;\n}\n\nconst defaultTranslations: TranslationDict = {\n  \"DESKTOP\": \"Desktop\",\n  \"MO<PERSON><PERSON>\": \"Mobile\",\n  \"META_MASK_MOBILE_APP\": \"MetaMask mobile app\",\n  \"SCAN_TO_CONNECT\": \"Scan to connect and sign with\",\n  \"CONNECT_WITH_EXTENSION\": \"Connect With MetaMask Extension\",\n  \"INSTALL_MODAL\": {\n    \"TRUSTED_BY_USERS\": \"Trusted by over 30 million users to buy, store, send and swap crypto securely\",\n    \"LEADING_CRYPTO_WALLET\": \"The leading crypto wallet & gateway to blockchain apps built on Ethereum Mainnet, Polygon, Optimism, and many other networks\",\n    \"CONTROL_DIGITAL_INTERACTIONS\": \"Puts you in control of your digital interactions by making power of cryptography more accessible\",\n    \"INSTALL_META_MASK_EXTENSION\": \"Install MetaMask Extension\"\n  },\n  \"PENDING_MODAL\": {\n    \"OPEN_META_MASK_SELECT_CODE\": \"Please open the MetaMask wallet app and select the code on the screen OR disconnect\",\n    \"OPEN_META_MASK_CONTINUE\": \"Open the MetaMask app to continue with your session.\",\n    \"NUMBER_AFTER_OPEN_NOTICE\": \"If a number doesn't appear after opening MetaMask, please click disconnect and re-scan the QRCode.\",\n    \"DISCONNECT\": \"Disconnect\"\n  },\n  \"SELECT_MODAL\": {\n    \"CRYPTO_TAKE_CONTROL_TEXT\": \"Take control of your crypto and explore the blockchain with the wallet trusted by over 30 million people worldwide\"\n  },\n  \"META_MASK_MODAL\": {\n    \"ADDRESS_COPIED\": \"Address copied to clipboard!\",\n    \"DISCONNECT\": \"Disconnect\",\n    \"ACTIVE_NETWORK\": \"Active Network\"\n  }\n} as const;\n\nexport class SimpleI18n implements I18nInstance {\n  private translations: TranslationDict = defaultTranslations;\n  private supportedLocales: string[] = ['es', 'fr', 'he', 'it', 'pt', 'tr'];\n  private baseUrl: string;\n\n  constructor(config?: { baseUrl?: string }) {\n    this.baseUrl = config?.baseUrl ?? 'https://raw.githubusercontent.com/MetaMask/metamask-sdk/refs/heads/gh-pages/locales';\n  }\n\n  private getBrowserLanguage(): string {\n    // Get all browser languages in order of preference\n    const browserLanguages = navigator.languages || [navigator.language];\n\n    // Check if English is one of the preferred languages\n    const hasEnglish = browserLanguages.some(lang =>\n      lang.toLowerCase().startsWith('en')\n    );\n\n    // If user understands English, use it\n    if (hasEnglish) {\n      return 'en';\n    }\n\n    // Otherwise, check for other supported languages\n    const primaryLang = navigator.language;\n    const shortLang = primaryLang.toLowerCase().split('-')[0];\n    if (this.supportedLocales.includes(shortLang)) {\n      return shortLang;\n    }\n\n    return 'en';\n  }\n\n  async init(config: { fallbackLng: string }): Promise<void> {\n    const browserLang = this.getBrowserLanguage();\n    const locale = browserLang || config.fallbackLng;\n    await this.loadTranslations(locale);\n  }\n\n  async loadTranslations(locale: string): Promise<void> {\n    const shortLocale = locale.split('-')[0];\n\n    if (shortLocale === 'en' || !this.supportedLocales.includes(shortLocale)) {\n        this.translations = defaultTranslations;\n        return;\n    }\n\n    try {\n        const url = `${this.baseUrl}/${shortLocale}.json`;\n        const response = await fetch(url);\n\n        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);\n        this.translations = await response.json();\n    } catch (error) {\n        console.warn(`❌ Failed to load ${shortLocale} translations, falling back to English:`, error);\n        this.translations = defaultTranslations;\n    }\n  }\n\n  t(key: string): string {\n    return this.getNestedTranslation(key, this.translations) || key;\n  }\n\n  private getNestedTranslation(key: string, dict: TranslationDict): string {\n    const parts = key.split('.');\n    let current: TranslationDict | string = dict;\n\n    for (const part of parts) {\n      if (typeof current !== 'object') return '';\n      current = current[part];\n    }\n\n    return typeof current === 'string' ? current : '';\n  }\n}\n"]}