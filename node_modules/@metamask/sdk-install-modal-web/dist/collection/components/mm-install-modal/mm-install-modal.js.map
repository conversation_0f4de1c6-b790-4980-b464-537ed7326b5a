{"version": 3, "file": "mm-install-modal.js", "sourceRoot": "", "sources": ["../../../../src/components/mm-install-modal/mm-install-modal.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAgB,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAC/F,OAAO,EAAE,aAAa,EAAE,MAAM,kCAAkC,CAAC;AACjE,OAAO,kBAAkB,MAAM,4BAA4B,CAAC;AAC5D,OAAO,UAAU,MAAM,oBAAoB,CAAC;AAC5C,OAAO,SAAS,MAAM,mBAAmB,CAAC;AAC1C,OAAO,QAAQ,MAAM,kBAAkB,CAAC;AACxC,OAAO,WAAW,MAAM,qBAAqB,CAAC;AAC9C,OAAO,UAAU,MAAM,oBAAoB,CAAC;AAC5C,OAAO,WAAW,MAAM,qBAAqB,CAAC;AAC9C,OAAO,IAAI,MAAM,cAAc,CAAC;AAChC,OAAO,QAAQ,MAAM,eAAe,CAAC;AACrC,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAOzD,MAAM,OAAO,YAAY;IA0BvB;;;;mBARuB,CAAC;4BAES,IAAI;kCAIU,KAAK;QAGlD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAExC,IAAI,CAAC,YAAY,GAAG,IAAI,UAAU,EAAE,CAAC;KACtC;IAED,gBAAgB;QACd,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACvB,KAAK,EAAE,cAAc,CAAC,gBAAgB;YACtC,MAAM,EAAE;gBACN,kBAAkB,EAAE,KAAK;gBACzB,GAAG,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ;aAC3C;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB;QACrB,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YAC3B,WAAW,EAAE,IAAI;SAClB,CAAC,CAAC;QACH,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACjC,CAAC;IAGD,mBAAmB,CAAC,QAAiB;QACnC,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;IACH,CAAC;IAED,OAAO,CAAC,eAAe,GAAG,KAAK;QAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,eAAe,EAAE,CAAC,CAAC;IACvC,CAAC;IAED,+BAA+B;QAC7B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACvB,KAAK,EAAE,cAAc,CAAC,wBAAwB;YAC9C,MAAM,EAAE;gBACN,WAAW,EAAE,mBAAmB;gBAChC,GAAG,EAAE,SAAS;aACf;SACF,CAAC,CAAC;QACH,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAED,MAAM,CAAC,MAAc,EAAE,eAAwB,KAAK;QAClD,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;gBACvB,KAAK,EAAE,cAAc,CAAC,wBAAwB;gBAC9C,MAAM,EAAE;oBACN,MAAM,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,mBAAmB;iBACnE;aACF,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;QAClB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC,CAAC,qBAAqB;QACpC,CAAC;QAED,MAAM,CAAC,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACpD,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;QAE7E,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE;YAC5C,GAAG,EAAE,QAAQ;YACb,KAAK,EAAE,CAAC;SACT,CAAC,CAAC;QAEH,OAAO,CACL,EAAC,aAAa,IAAC,SAAS,EAAC,eAAe;YACtC,WAAK,KAAK,EAAC,UAAU,EAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAQ;YAC/D,WAAK,KAAK,EAAC,OAAO;gBAChB,WAAK,KAAK,EAAC,sBAAsB;oBAC/B,WAAK,KAAK,EAAC,OAAO;wBAChB,YAAM,KAAK,EAAC,aAAa,EAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;4BACzD,EAAC,WAAW,OAAG,CACV,CACH,CACF;gBACN,WAAK,KAAK,EAAC,eAAe;oBACxB,EAAC,IAAI,OAAG,CACJ;gBACN;oBACE,WAAK,KAAK,EAAC,cAAc;wBACvB,WAAK,KAAK,EAAC,eAAe;4BACxB,WACE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,EACnC,KAAK,EAAE,gBAAgB,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAA,CAAC,CAAC,EAAE,EAAE,IAE1D,CAAC,CAAC,SAAS,CAAC,CACT;4BACN,WACE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,EACnC,KAAK,EAAE,gBAAgB,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAA,CAAC,CAAC,EAAE,EAAE,IAE1D,CAAC,CAAC,QAAQ,CAAC,CACR,CACF,CACF;oBACN,WAAK,KAAK,EAAE,EAAE,OAAO,EAAE,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,EAAE;wBAC1D,WAAK,KAAK,EAAC,eAAe;4BACxB,WACE,KAAK,EAAC,UAAU,EAChB,KAAK,EAAE;oCACL,SAAS,EAAE,QAAQ;oCACnB,SAAS,EAAE,GAAG;iCACf;gCAGC,UAAU,IAAI,CACZ,WAAK,EAAE,EAAC,eAAe,EAAC,KAAK,EAAC,QAAQ,EAAC,SAAS,EAAE,UAAU,GAAI,CACjE;gCAEH,WAAK,KAAK,EAAC,mBAAmB;oCAC3B,CAAC,CAAC,iBAAiB,CAAC;;oCAAE,aAAM;oCAC7B,YAAM,KAAK,EAAC,MAAM;wCAChB,aAAI,CAAC,CAAC,sBAAsB,CAAC,CAAK,CAC7B,CACH,CACF,CACF,CACF;oBACN,WAAK,KAAK,EAAE,EAAE,OAAO,EAAE,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,EAAE;wBAC1D,WAAK,KAAK,EAAC,MAAM;4BACf,EAAC,kBAAkB,IACjB,IAAI,EAAE,SAAS,EACf,IAAI,EAAE,CAAC,CAAC,gCAAgC,CAAC,GACzC,CACE;wBACN,WAAK,KAAK,EAAC,MAAM;4BACf,EAAC,kBAAkB,IACjB,IAAI,EAAE,UAAU,EAChB,IAAI,EAAE,CAAC,CAAC,qCAAqC,CAAC,GAC9C,CACE;wBACN,WAAK,KAAK,EAAC,MAAM;4BACf,EAAC,kBAAkB,IACjB,IAAI,EAAE,QAAQ,EACd,IAAI,EAAE,CAAC,CAAC,4CAA4C,CAAC,GACrD,CACE;wBAEN,cACE,KAAK,EAAC,QAAQ,EACd,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,+BAA+B,EAAE;4BAErD,EAAC,WAAW,OAAG;4BACf,YAAM,KAAK,EAAC,sBAAsB,IAC/B,CAAC,CAAC,2CAA2C,CAAC,CAC1C,CACA,CACL,CACF;gBACN,EAAC,UAAU,IAAC,OAAO,EAAE,IAAI,CAAC,UAAU,GAAI,CACpC,CACM,CACf,CAAA;IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Prop, h, Event, EventEmitter, State, Watch, Element } from '@stencil/core';\nimport { WidgetWrapper } from '../widget-wrapper/widget-wrapper';\nimport AdvantagesListItem from '../misc/AdvantagesListItem';\nimport WalletIcon from '../misc/WalletIcon';\nimport HeartIcon from '../misc/HeartIcon';\nimport LockIcon from '../misc/LockIcon';\nimport InstallIcon from '../misc/InstallIcon';\nimport SDKVersion from '../misc/SDKVersion';\nimport CloseButton from '../misc/CloseButton';\nimport Logo from '../misc/Logo';\nimport encodeQR from '@paulmillr/qr';\nimport { SimpleI18n } from '../misc/simple-i18n';\nimport { TrackingEvents } from '../misc/tracking-events';\n\n@Component({\n  tag: 'mm-install-modal',\n  styleUrl: '../style.css',\n  shadow: true,\n})\nexport class InstallModal {\n  /**\n   * The QR code link\n   */\n  @Prop() link: string;\n\n  @Prop() sdkVersion?: string;\n\n  @Prop() preferDesktop: boolean;\n\n  private i18nInstance: SimpleI18n;\n\n  @Event() close: EventEmitter<{ shouldTerminate?: boolean }>;\n\n  @Event() startDesktopOnboarding: EventEmitter;\n\n  @Event() trackAnalytics: EventEmitter<{ event: TrackingEvents, params?: Record<string, unknown> }>;\n\n  @State() tab: number = 1;\n\n  @State() isDefaultTab: boolean = true;\n\n  @Element() el: HTMLElement;\n\n  @State() private translationsLoaded: boolean = false;\n\n  constructor() {\n    this.onClose = this.onClose.bind(this);\n    this.onStartDesktopOnboardingHandler = this.onStartDesktopOnboardingHandler.bind(this);\n    this.setTab = this.setTab.bind(this);\n    this.render = this.render.bind(this);\n    this.setTab(this.preferDesktop ? 1 : 2);\n\n    this.i18nInstance = new SimpleI18n();\n  }\n\n  componentDidLoad() {\n    this.trackAnalytics.emit({\n      event: TrackingEvents.SDK_MODAL_VIEWED,\n      params: {\n        extensionInstalled: false,\n        tab: this.tab === 1 ? 'desktop' : 'mobile',\n      },\n    });\n  }\n\n  async connectedCallback() {\n    await this.i18nInstance.init({\n      fallbackLng: 'en'\n    });\n    this.translationsLoaded = true;\n  }\n\n  @Watch('preferDesktop')\n  updatePreferDesktop(newValue: boolean) {\n    if (newValue) {\n      this.setTab(1);\n    } else {\n      this.setTab(2);\n    }\n  }\n\n  onClose(shouldTerminate = false) {\n    this.close.emit({ shouldTerminate });\n  }\n\n  onStartDesktopOnboardingHandler() {\n    this.trackAnalytics.emit({\n      event: TrackingEvents.SDK_MODAL_BUTTON_CLICKED,\n      params: {\n        button_type: 'install_extension',\n        tab: 'desktop',\n      },\n    });\n    this.startDesktopOnboarding.emit();\n  }\n\n  setTab(newTab: number, isUserAction: boolean = false) {\n    if (isUserAction) {\n      this.trackAnalytics.emit({\n        event: TrackingEvents.SDK_MODAL_TOGGLE_CHANGED,\n        params: {\n          toggle: this.tab === 1 ? 'desktop_to_mobile' : 'mobile_to_desktop',\n        },\n      });\n    }\n    \n    this.tab = newTab;\n    this.isDefaultTab = false;\n  }\n\n  render() {\n    if (!this.translationsLoaded) {\n      return null; // or a loading state\n    }\n\n    const t = (key: string) => this.i18nInstance.t(key);\n    const currentTab = this.isDefaultTab ? this.preferDesktop ? 1 : 2 : this.tab;\n\n    const svgElement = encodeQR(this.link, \"svg\", {\n      ecc: \"medium\",\n      scale: 2\n    });\n\n    return (\n      <WidgetWrapper className=\"install-model\">\n        <div class='backdrop' onClick={() => this.onClose(true)}></div>\n        <div class='modal'>\n          <div class='closeButtonContainer'>\n            <div class='right'>\n              <span class='closeButton' onClick={() => this.onClose(true)}>\n                <CloseButton />\n              </span>\n            </div>\n          </div>\n          <div class='logoContainer'>\n            <Logo />\n          </div>\n          <div>\n            <div class='tabcontainer'>\n              <div class='flexContainer'>\n                <div\n                  onClick={() => this.setTab(1, true)}\n                  class={`tab flexItem ${currentTab === 1 ? 'tabactive': ''}`}\n                >\n                  {t('DESKTOP')}\n                </div>\n                <div\n                  onClick={() => this.setTab(2, true)}\n                  class={`tab flexItem ${currentTab === 2 ? 'tabactive': ''}`}\n                >\n                  {t('MOBILE')}\n                </div>\n              </div>\n            </div>\n            <div style={{ display: currentTab === 1 ? 'none' : 'block' }}>\n              <div class='flexContainer'>\n                <div\n                  class='flexItem'\n                  style={{\n                    textAlign: 'center',\n                    marginTop: '4',\n                  }}\n                >\n                  {\n                    svgElement && (\n                      <div id=\"sdk-mm-qrcode\" class='center' innerHTML={svgElement} />\n                    )\n                  }\n                  <div class='connectMobileText'>\n                    {t('SCAN_TO_CONNECT')} <br />\n                    <span class='blue'>\n                      <b>{t('META_MASK_MOBILE_APP')}</b>\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div style={{ display: currentTab === 2 ? 'none' : 'block' }}>\n              <div class='item'>\n                <AdvantagesListItem\n                  Icon={HeartIcon}\n                  text={t('INSTALL_MODAL.TRUSTED_BY_USERS')}\n                />\n              </div>\n              <div class='item'>\n                <AdvantagesListItem\n                  Icon={WalletIcon}\n                  text={t('INSTALL_MODAL.LEADING_CRYPTO_WALLET')}\n                />\n              </div>\n              <div class='item'>\n                <AdvantagesListItem\n                  Icon={LockIcon}\n                  text={t('INSTALL_MODAL.CONTROL_DIGITAL_INTERACTIONS')}\n                />\n              </div>\n\n              <button\n                class='button'\n                onClick={() => this.onStartDesktopOnboardingHandler()}\n              >\n                <InstallIcon />\n                <span class='installExtensionText'>\n                  {t('INSTALL_MODAL.INSTALL_META_MASK_EXTENSION')}\n                </span>\n              </button>\n            </div>\n          </div>\n          <SDKVersion version={this.sdkVersion} />\n        </div>\n    </WidgetWrapper>\n    )\n  }\n}\n"]}