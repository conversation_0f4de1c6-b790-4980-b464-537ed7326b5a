{"version": 3, "file": "mm-select-modal.js", "sourceRoot": "", "sources": ["../../../../src/components/mm-select-modal/mm-select-modal.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAgB,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAC/F,OAAO,EAAE,aAAa,EAAE,MAAM,kCAAkC,CAAC;AACjE,OAAO,UAAU,MAAM,oBAAoB,CAAC;AAC5C,OAAO,WAAW,MAAM,qBAAqB,CAAC;AAC9C,OAAO,IAAI,MAAM,cAAc,CAAC;AAChC,OAAO,WAAW,MAAM,qBAAqB,CAAC;AAC9C,OAAO,EAAE,sBAAsB,EAAE,MAAM,gCAAgC,CAAC;AACxE,OAAO,QAAQ,MAAM,eAAe,CAAC;AACrC,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAOjD,MAAM,OAAO,WAAW;IAwBtB;;;;mBARuB,CAAC;4BAES,IAAI;kCAIU,KAAK;QAGlD,IAAI,CAAC,YAAY,GAAG,IAAI,UAAU,EAAE,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACzC;IAED,KAAK,CAAC,iBAAiB;QACrB,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YAC3B,WAAW,EAAE,IAAI;SAClB,CAAC,CAAC;QACH,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACjC,CAAC;IAED,OAAO,CAAC,eAAe,GAAG,KAAK;QAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,eAAe,EAAE,CAAC,CAAC;IACvC,CAAC;IAED,2BAA2B;QACzB,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAED,MAAM,CAAC,GAAW;QAChB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED,oBAAoB;QAClB,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAGD,mBAAmB,CAAC,QAAiB;QACnC,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;IACH,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,CAAC,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAEpD,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAA;QAElC,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAA;QAE5E,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE;YAC5C,GAAG,EAAE,QAAQ;YACb,KAAK,EAAE,CAAC;SACT,CAAC,CAAA;QAEF,OAAO,CACL,EAAC,aAAa,IAAC,SAAS,EAAC,cAAc;YACrC,WAAK,KAAK,EAAC,UAAU,EAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAQ;YAC/D,WAAK,KAAK,EAAC,OAAO;gBAChB,WAAK,KAAK,EAAC,sBAAsB;oBAC/B,WAAK,KAAK,EAAC,OAAO;wBAChB,YAAM,KAAK,EAAC,aAAa,EAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;4BACzD,EAAC,WAAW,OAAG,CACV,CACH,CACF;gBACN,WAAK,KAAK,EAAC,eAAe;oBACxB,EAAC,IAAI,OAAG,CACJ;gBACN;oBACE,WAAK,KAAK,EAAC,cAAc;wBACvB,WAAK,KAAK,EAAC,eAAe;4BACxB,WACE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAC7B,KAAK,EAAE,gBAAgB,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,IAE3D,CAAC,CAAC,SAAS,CAAC,CACT;4BACN,WACE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAC7B,KAAK,EAAE,gBAAgB,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,IAE3D,CAAC,CAAC,QAAQ,CAAC,CACR,CACF,CACF;oBACN,WAAK,KAAK,EAAE,EAAE,OAAO,EAAE,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,EAAE;wBAC1D,WAAK,KAAK,EAAC,eAAe;4BACxB,WACE,KAAK,EAAC,UAAU,EAChB,KAAK,EAAE;oCACL,SAAS,EAAE,QAAQ;oCACnB,SAAS,EAAE,GAAG;iCACf;gCAED,WAAK,KAAK,EAAC,QAAQ,EAAC,EAAE,EAAC,eAAe,EAAC,SAAS,EAAE,UAAU,GAAI;gCAChE,WAAK,KAAK,EAAC,mBAAmB;oCAC3B,CAAC,CAAC,iBAAiB,CAAC;oCACrB,aAAM;oCACN,YAAM,KAAK,EAAC,MAAM;wCAChB,aAAI,CAAC,CAAC,sBAAsB,CAAC,CAAK,CAC7B,CACH,CACF,CACF,CACF;oBACN,WAAK,KAAK,EAAE,EAAE,OAAO,EAAE,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,EAAE;wBAC1D,WACE,KAAK,EAAE;gCACL,OAAO,EAAE,MAAM;gCACf,cAAc,EAAE,QAAQ;gCACxB,MAAM,EAAE,KAAK;gCACb,SAAS,EAAE,KAAK;6BACjB;4BAED,EAAC,sBAAsB,OAAG,CACtB;wBACN,WAAK,KAAK,EAAC,gBAAgB,IACxB,CAAC,CAAC,uCAAuC,CAAC,CACvC;wBAEN,cAAQ,KAAK,EAAC,QAAQ,EAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,2BAA2B,EAAE;4BACtE,EAAC,WAAW,OAAG;4BACf,YAAM,KAAK,EAAC,sBAAsB,IAC/B,CAAC,CAAC,wBAAwB,CAAC,CACvB,CACA,CACL,CACF;gBACN,EAAC,UAAU,IAAC,OAAO,EAAE,UAAU,GAAI,CAC/B,CACQ,CACjB,CAAA;IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Prop, h, Event, EventEmitter, State, Element, Watch } from '@stencil/core';\nimport { WidgetWrapper } from '../widget-wrapper/widget-wrapper';\nimport SDKVersion from '../misc/SDKVersion';\nimport CloseButton from '../misc/CloseButton';\nimport Logo from '../misc/Logo';\nimport ConnectIcon from '../misc/ConnectIcon';\nimport { MetamaskExtensionImage } from '../misc/MetamaskExtensionImage';\nimport encodeQR from '@paulmillr/qr';\nimport { SimpleI18n } from '../misc/simple-i18n';\n\n@Component({\n  tag: 'mm-select-modal',\n  styleUrl: '../style.css',\n  shadow: true,\n})\nexport class SelectModal {\n  /**\n   * The QR code link\n   */\n  @Prop() link: string;\n\n  @Prop() sdkVersion?: string;\n\n  @Prop() preferDesktop: boolean;\n\n  private i18nInstance: SimpleI18n;\n\n  @Event() close: EventEmitter<{ shouldTerminate?: boolean }>;\n\n  @Event() connectWithExtension: EventEmitter;\n\n  @State() tab: number = 1;\n\n  @State() isDefaultTab: boolean = true;\n\n  @Element() el: HTMLElement;\n\n  @State() private translationsLoaded: boolean = false;\n\n  constructor() {\n    this.i18nInstance = new SimpleI18n();\n    this.setTab(this.preferDesktop ? 1 : 2);\n  }\n\n  async connectedCallback() {\n    await this.i18nInstance.init({\n      fallbackLng: 'en'\n    });\n    this.translationsLoaded = true;\n  }\n\n  onClose(shouldTerminate = false) {\n    this.close.emit({ shouldTerminate });\n  }\n\n  connectWithExtensionHandler() {\n    this.connectWithExtension.emit();\n  }\n\n  setTab(tab: number) {\n    this.tab = tab;\n    this.isDefaultTab = false;\n  }\n\n  disconnectedCallback() {\n    this.onClose();\n  }\n\n  @Watch('preferDesktop')\n  updatePreferDesktop(newValue: boolean) {\n    if (newValue) {\n      this.setTab(1);\n    } else {\n      this.setTab(2);\n    }\n  }\n\n  render() {\n    if (!this.translationsLoaded) {\n      return null;\n    }\n\n    const t = (key: string) => this.i18nInstance.t(key);\n\n    const sdkVersion = this.sdkVersion\n\n    const currentTab = this.isDefaultTab ? this.preferDesktop ? 1 : 2 : this.tab\n\n    const svgElement = encodeQR(this.link, \"svg\", {\n      ecc: \"medium\",\n      scale: 2\n    })\n\n    return (\n      <WidgetWrapper className=\"select-modal\">\n        <div class='backdrop' onClick={() => this.onClose(true)}></div>\n        <div class='modal'>\n          <div class='closeButtonContainer'>\n            <div class='right'>\n              <span class='closeButton' onClick={() => this.onClose(true)}>\n                <CloseButton />\n              </span>\n            </div>\n          </div>\n          <div class='logoContainer'>\n            <Logo />\n          </div>\n          <div>\n            <div class='tabcontainer'>\n              <div class='flexContainer'>\n                <div\n                  onClick={() => this.setTab(1)}\n                  class={`tab flexItem ${currentTab === 1 ? 'tabactive' : ''}`}\n                >\n                  {t('DESKTOP')}\n                </div>\n                <div\n                  onClick={() => this.setTab(2)}\n                  class={`tab flexItem ${currentTab === 2 ? 'tabactive' : ''}`}\n                >\n                  {t('MOBILE')}\n                </div>\n              </div>\n            </div>\n            <div style={{ display: currentTab === 1 ? 'none' : 'block' }}>\n              <div class='flexContainer'>\n                <div\n                  class='flexItem'\n                  style={{\n                    textAlign: 'center',\n                    marginTop: '4',\n                  }}\n                >\n                  <div class='center' id=\"sdk-mm-qrcode\" innerHTML={svgElement} />\n                  <div class='connectMobileText'>\n                    {t('SCAN_TO_CONNECT')}\n                    <br />\n                    <span class='blue'>\n                      <b>{t('META_MASK_MOBILE_APP')}</b>\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div style={{ display: currentTab === 2 ? 'none' : 'block' }}>\n              <div\n                style={{\n                  display: 'flex',\n                  justifyContent: 'center',\n                  height: '300',\n                  marginTop: '-20',\n                }}\n              >\n                <MetamaskExtensionImage />\n              </div>\n              <div class='extensionLabel'>\n                {t('SELECT_MODAL.CRYPTO_TAKE_CONTROL_TEXT')}\n              </div>\n\n              <button class='button' onClick={() => this.connectWithExtensionHandler()}>\n                <ConnectIcon />\n                <span class='installExtensionText'>\n                  {t('CONNECT_WITH_EXTENSION')}\n                </span>\n              </button>\n            </div>\n          </div>\n          <SDKVersion version={sdkVersion} />\n        </div>\n      </WidgetWrapper>\n    )\n  }\n}\n"]}