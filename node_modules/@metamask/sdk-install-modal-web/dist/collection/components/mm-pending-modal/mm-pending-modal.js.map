{"version": 3, "file": "mm-pending-modal.js", "sourceRoot": "", "sources": ["../../../../src/components/mm-pending-modal/mm-pending-modal.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAgB,KAAK,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AACxF,OAAO,EAAE,aAAa,EAAE,MAAM,kCAAkC,CAAC;AACjE,OAAO,UAAU,MAAM,oBAAoB,CAAC;AAC5C,OAAO,WAAW,MAAM,qBAAqB,CAAC;AAC9C,OAAO,IAAI,MAAM,cAAc,CAAC;AAChC,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAOjD,MAAM,OAAO,YAAY;IAsBvB;;;;kCAF+C,KAAK;QAGlD,IAAI,CAAC,YAAY,GAAG,IAAI,UAAU,EAAE,CAAC;KACtC;IAED,KAAK,CAAC,iBAAiB;QACrB,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YAC3B,WAAW,EAAE,IAAI;SAClB,CAAC,CAAC;QACH,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACjC,CAAC;IAED,OAAO;QACL,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;IACpB,CAAC;IAED,YAAY;QACV,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAED,uBAAuB,CAAC,QAAgB;QACtC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACrB,QAAQ;SACX,CAAC,CAAA;IACJ,CAAC;IAED,oBAAoB;QAClB,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAED,MAAM;;QACJ,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,UAAU,GAAG,MAAA,IAAI,CAAC,UAAU,mCAAI,IAAI,CAAC;QAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAA;QAClC,MAAM,CAAC,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAEpD,OAAO,CACH,EAAC,aAAa,IAAC,SAAS,EAAC,eAAe;YACxC,WAAK,KAAK,EAAC,UAAU,EAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,GAAQ;YAC3D,WAAK,KAAK,EAAC,OAAO;gBAChB,WAAK,KAAK,EAAC,sBAAsB;oBAC/B,WAAK,KAAK,EAAC,OAAO;wBAChB,YAAM,KAAK,EAAC,aAAa,EAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE;4BACrD,EAAC,WAAW,OAAG,CACV,CACH,CACF;gBACN,WAAK,KAAK,EAAC,eAAe;oBACxB,EAAC,IAAI,OAAG,CACJ;gBACN;oBACE,WACE,KAAK,EAAC,eAAe,EACrB,KAAK,EAAE;4BACL,aAAa,EAAE,QAAQ;4BACvB,KAAK,EAAE,OAAO;yBACf;wBAED,WACE,KAAK,EAAC,UAAU,EAChB,KAAK,EAAE;gCACL,SAAS,EAAE,QAAQ;gCACnB,SAAS,EAAE,MAAM;gCACjB,YAAY,EAAE,MAAM;gCACpB,QAAQ,EAAE,MAAM;6BACjB,IAEA,UAAU;4BACT,CAAC,CAAC,CAAC,CAAC,0CAA0C,CAAC;4BAC/C,CAAC,CAAC,CAAC,CAAC,uCAAuC,CAAC,CAC1C;wBACN,WACE,EAAE,EAAC,kBAAkB,EACrB,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,IACtF,IAAI,CAAC,OAAO,CAAO;wBACpB,UAAU,IAAI,CACb,WAAK,KAAK,EAAC,QAAQ;;4BACd,CAAC,CAAC,wCAAwC,CAAC,CAC1C,CACP,CACG;oBACN,WAAK,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;wBAC/B,cACE,KAAK,EAAC,aAAa,EACnB,KAAK,EAAE;gCACL,SAAS,EAAE,KAAK;gCAChB,KAAK,EAAE,SAAS;gCAChB,WAAW,EAAE,SAAS;gCACtB,WAAW,EAAE,KAAK;gCAClB,WAAW,EAAE,OAAO;gCACpB,eAAe,EAAE,OAAO;6BACzB,EACD,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,IAEjC,CAAC,CAAC,0BAA0B,CAAC,CACvB,CACL,CACF;gBACN,EAAC,UAAU,IAAC,OAAO,EAAE,UAAU,GAAI,CAC/B,CACQ,CACjB,CAAA;IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CACF", "sourcesContent": ["import { Component, Prop, h, Event, EventEmitter, State, Element } from '@stencil/core';\nimport { WidgetWrapper } from '../widget-wrapper/widget-wrapper';\nimport SDKVersion from '../misc/SDKVersion';\nimport CloseButton from '../misc/CloseButton';\nimport Logo from '../misc/Logo';\nimport { SimpleI18n } from '../misc/simple-i18n';\n\n@Component({\n  tag: 'mm-pending-modal',\n  styleUrl: '../style.css',\n  shadow: true,\n})\nexport class PendingModal {\n  /**\n   * The QR code link\n   */\n  @Prop() displayOTP?: boolean;\n\n  @Prop() sdkVersion?: string;\n\n  private i18nInstance: SimpleI18n;\n\n  @Prop() otpCode?: string;\n\n  @Event() close: EventEmitter;\n\n  @Event() disconnect: EventEmitter;\n\n  @Event() updateOTPValue: EventEmitter<{ otpValue: string }>;\n\n  @Element() el: HTMLElement;\n\n  @State() private translationsLoaded: boolean = false;\n\n  constructor() {\n    this.i18nInstance = new SimpleI18n();\n  }\n\n  async connectedCallback() {\n    await this.i18nInstance.init({\n      fallbackLng: 'en'\n    });\n    this.translationsLoaded = true;\n  }\n\n  onClose() {\n    this.close.emit();\n  }\n\n  onDisconnect() {\n    this.disconnect.emit();\n  }\n\n  onUpdateOTPValueHandler(otpValue: string) {\n    this.updateOTPValue.emit({\n        otpValue,\n    })\n  }\n\n  disconnectedCallback() {\n    this.onClose();\n  }\n\n  render() {\n    if (!this.translationsLoaded) {\n      return null;\n    }\n\n    const displayOTP = this.displayOTP ?? true;\n    const sdkVersion = this.sdkVersion\n    const t = (key: string) => this.i18nInstance.t(key);\n\n    return (\n        <WidgetWrapper className=\"pending-modal\">\n        <div class='backdrop' onClick={() => this.onClose()}></div>\n        <div class='modal'>\n          <div class='closeButtonContainer'>\n            <div class='right'>\n              <span class='closeButton' onClick={() => this.onClose()}>\n                <CloseButton />\n              </span>\n            </div>\n          </div>\n          <div class='logoContainer'>\n            <Logo />\n          </div>\n          <div>\n            <div\n              class='flexContainer'\n              style={{\n                flexDirection: 'column',\n                color: 'black',\n              }}\n            >\n              <div\n                class='flexItem'\n                style={{\n                  textAlign: 'center',\n                  marginTop: '30px',\n                  marginBottom: '30px',\n                  fontSize: '16px',\n                }}\n              >\n                {displayOTP\n                  ? t('PENDING_MODAL.OPEN_META_MASK_SELECT_CODE')\n                  : t('PENDING_MODAL.OPEN_META_MASK_CONTINUE')}\n              </div>\n              <div\n                id=\"sdk-mm-otp-value\"\n                style={{ padding: '10px', fontSize: '32px', display: this.otpCode ? 'block' : 'none' }}\n              >{this.otpCode}</div>\n              {displayOTP && (\n                <div class='notice'>\n                  * {t('PENDING_MODAL.NUMBER_AFTER_OPEN_NOTICE')}\n                </div>\n              )}\n            </div>\n            <div style={{ marginTop: '20px' }}>\n              <button\n                class='button blue'\n                style={{\n                  marginTop: '5px',\n                  color: '#0376C9',\n                  borderColor: '#0376C9',\n                  borderWidth: '1px',\n                  borderStyle: 'solid',\n                  backgroundColor: 'white',\n                }}\n                onClick={() => this.onDisconnect()}\n              >\n                {t('PENDING_MODAL.DISCONNECT')}\n              </button>\n            </div>\n          </div>\n          <SDKVersion version={sdkVersion} />\n        </div>\n      </WidgetWrapper>\n    )\n  }\n}\n"]}