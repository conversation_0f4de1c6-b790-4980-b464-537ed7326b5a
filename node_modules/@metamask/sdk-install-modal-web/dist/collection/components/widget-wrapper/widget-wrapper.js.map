{"version": 3, "file": "widget-wrapper.js", "sourceRoot": "", "sources": ["../../../../src/components/widget-wrapper/widget-wrapper.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,CAAC,EAAa,MAAM,eAAe,CAAC;AAC7C,OAAO,mBAAmB,CAAC;AAE3B,MAAM,kBAAkB,GAAG;IACzB,UAAU,EAAE,oBAAoB;CACjC,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,EAC5B,SAAS,GAGV,EAAE,QAAmB,EAAE,EAAE;IACxB,OAAO,CACL,WAAK,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,SAAS,IAC7C,QAAQ,CACL,CACP,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import { h, ChildNode } from '@stencil/core';\nimport './resetStyles.css';\n\nconst widgetWrapperStyle = {\n  fontFamily: 'Roboto, sans-serif',\n};\n\nexport const WidgetWrapper = ({\n  className,\n}: {\n  className: string;\n}, children: ChildNode) => {\n  return (\n    <div style={widgetWrapperStyle} class={className}>\n      {children}\n    </div>\n  );\n};"]}