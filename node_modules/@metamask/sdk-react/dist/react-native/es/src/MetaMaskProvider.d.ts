import { MetaMaskSD<PERSON>, MetaMask<PERSON>KOptions, RPCMethod<PERSON>ache, SDKProvider, ServiceStatus } from '@metamask/sdk';
import { EthereumRpcError } from 'eth-rpc-errors';
import React from 'react';
export interface EventHandlerProps {
    setConnecting: React.Dispatch<React.SetStateAction<boolean>>;
    setConnected: React.Dispatch<React.SetStateAction<boolean>>;
    setChainId: React.Dispatch<React.SetStateAction<string | undefined>>;
    setError: React.Dispatch<React.SetStateAction<EthereumRpcError<unknown> | undefined>>;
    setAccount: React.Dispatch<React.SetStateAction<string | undefined>>;
    setStatus: React.Dispatch<React.SetStateAction<ServiceStatus | undefined>>;
    setTrigger: React.Dispatch<React.SetStateAction<number>>;
    setRPCHistory: React.Dispatch<React.SetStateAction<RPCMethodCache>>;
    debug?: boolean;
    synced?: boolean;
    chainId?: string;
    activeProvider?: SDKProvider;
    sdk?: MetaMaskSDK;
}
export interface SDKState {
    sdk?: MetaMaskSDK;
    ready: boolean;
    connected: boolean;
    connecting: boolean;
    extensionActive: boolean;
    readOnlyCalls: boolean;
    provider?: SDKProvider;
    channelId?: string;
    error?: EthereumRpcError<unknown>;
    chainId?: string;
    balance?: string;
    balanceProcessing?: boolean;
    account?: string;
    status?: ServiceStatus;
    rpcHistory?: RPCMethodCache;
    syncing?: boolean;
}
export declare const SDKContext: React.Context<SDKState>;
export declare const MetaMaskProvider: ({ children, sdkOptions, debug, }: {
    children: React.ReactNode;
    sdkOptions: MetaMaskSDKOptions;
    debug?: boolean | undefined;
}) => React.JSX.Element;
export default MetaMaskProvider;
//# sourceMappingURL=MetaMaskProvider.d.ts.map