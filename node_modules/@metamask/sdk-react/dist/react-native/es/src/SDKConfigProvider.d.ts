import React from 'react';
export interface SDKConfigContextProps {
    socketServer: string;
    useDeeplink?: boolean;
    infuraAPIKey?: string;
    extensionOnly?: boolean;
    checkInstallationImmediately: boolean;
    debug: boolean;
    logs: {
        sdk: boolean;
        provider: boolean;
        commLayer: boolean;
    };
    lang: string;
}
export declare const SDKConfigContext: React.Context<{
    setAppContext: (_: Partial<SDKConfigContextProps>) => void;
    reset: () => void;
    socketServer: string;
    useDeeplink?: boolean | undefined;
    infuraAPIKey?: string | undefined;
    extensionOnly?: boolean | undefined;
    checkInstallationImmediately: boolean;
    debug: boolean;
    logs: {
        sdk: boolean;
        provider: boolean;
        commLayer: boolean;
    };
    lang: string;
}>;
export interface SDKConfigProviderProps {
    initialSocketServer?: string;
    initialInfuraKey?: string;
    children: React.ReactNode;
    debug?: boolean;
}
export declare const SDKConfigProvider: ({ initialSocketServer, initialInfuraKey, children, }: SDKConfigProviderProps) => React.JSX.Element;
//# sourceMappingURL=SDKConfigProvider.d.ts.map