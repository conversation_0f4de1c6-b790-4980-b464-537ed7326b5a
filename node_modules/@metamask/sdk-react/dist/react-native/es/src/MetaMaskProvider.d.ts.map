{"version": 3, "file": "MetaMaskProvider.d.ts", "sourceRoot": "", "sources": ["../../src/MetaMaskProvider.tsx"], "names": [], "mappings": "AACA,OAAO,EAGL,WAAW,EACX,kBAAkB,EAAE,cAAc,EAAmB,WAAW,EAChE,aAAa,EACd,MAAM,eAAe,CAAC;AAEvB,OAAO,EAAE,gBAAgB,EAAE,MAAM,gBAAgB,CAAC;AAClD,OAAO,KAMN,MAAM,OAAO,CAAC;AAYf,MAAM,WAAW,iBAAiB;IAChC,aAAa,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;IAC7D,YAAY,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;IAC5D,UAAU,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC;IACrE,QAAQ,EAAE,KAAK,CAAC,QAAQ,CACtB,KAAK,CAAC,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC,CAC5D,CAAC;IACF,UAAU,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC;IACrE,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC;IAC3E,UAAU,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;IACzD,aAAa,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC;IACpE,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,cAAc,CAAC,EAAE,WAAW,CAAC;IAC7B,GAAG,CAAC,EAAE,WAAW,CAAC;CACnB;AAED,MAAM,WAAW,QAAQ;IACvB,GAAG,CAAC,EAAE,WAAW,CAAC;IAClB,KAAK,EAAE,OAAO,CAAC;IACf,SAAS,EAAE,OAAO,CAAC;IACnB,UAAU,EAAE,OAAO,CAAC;IACpB,eAAe,EAAE,OAAO,CAAC;IAEzB,aAAa,EAAE,OAAO,CAAC;IACvB,QAAQ,CAAC,EAAE,WAAW,CAAC;IACvB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,KAAK,CAAC,EAAE,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAClC,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,EAAE,aAAa,CAAC;IACvB,UAAU,CAAC,EAAE,cAAc,CAAC;IAC5B,OAAO,CAAC,EAAE,OAAO,CAAC;CACnB;AASD,eAAO,MAAM,UAAU,yBAA2B,CAAC;AAuRnD,eAAO,MAAM,gBAAgB;cAKjB,MAAM,SAAS;gBACb,kBAAkB;;uBAoB/B,CAAC;AAEF,eAAe,gBAAgB,CAAC"}