export declare const useSDK: () => import("./MetaMaskProvider").SDKState;
export declare const useSDKConfig: () => {
    setAppContext: (_: Partial<import("./SDKConfigProvider").SDKConfigContextProps>) => void;
    reset: () => void;
    socketServer: string;
    useDeeplink?: boolean | undefined;
    infuraAPIKey?: string | undefined;
    extensionOnly?: boolean | undefined;
    checkInstallationImmediately: boolean;
    debug: boolean;
    logs: {
        sdk: boolean;
        provider: boolean;
        commLayer: boolean;
    };
    lang: string;
};
//# sourceMappingURL=MetaMaskHooks.d.ts.map