{"version": 3, "file": "interface.d.ts", "sourceRoot": "", "sources": ["../../../../src/link/interface.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAA;AAC1F,OAAO,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAA;AAC9D,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAA;AAE7D,YAAY,EAAE,eAAe,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,CAAA;AACnE,MAAM,MAAM,OAAO,GAAG,CAAC,GAAG,CAAC,CAAA;AAE3B,MAAM,MAAM,MAAM,GAAG,IAAI,CAAA;AACzB,MAAM,MAAM,OAAO,GAAG,IAAI,CAAA;AAE1B;;;;;;;GAOG;AACH,MAAM,WAAW,IAAI,CACnB,IAAI,SAAS,OAAO,GAAG,OAAO,EAC9B,MAAM,SAAS,MAAM,GAAG,MAAM,EAC9B,GAAG,SAAS,MAAM,GAAG,MAAM,EAC3B,CAAC,SAAS,OAAO,GAAG,CAAC,CACnB,SAAQ,OAAO,CAAC,IAAI,CAAC;IACvB,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAA;IACnB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAA;IACrB,QAAQ,CAAC,SAAS,EAAE,eAAe,CAAC,GAAG,CAAC,CAAA;IAExC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAA;IAC3B,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAA;IAC3B,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;IAEpD,MAAM,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAA;IAEjE,QAAQ,CAAC,MAAM,SAAS,MAAM,EAAE,IAAI,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,CAAA;IACpH,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAA;IAElC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAA;CACnC;AAED,MAAM,WAAW,QAAQ,CAAC,CAAC,SAAS,WAAW,GAAG,WAAW;IAC3D,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAA;CACjB;AAED,MAAM,WAAW,UAAU,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,CAAE,SAAQ,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;CAC3F;AAED,MAAM,MAAM,WAAW,GACnB,UAAU,CAAC,OAAO,CAAC,GACnB,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;AAE1C,MAAM,MAAM,QAAQ,CAAC,CAAC,EAAE,MAAM,SAAS,MAAM,GAAG,MAAM,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;AAExF,YAAY,EAAE,QAAQ,EAAE,CAAA"}