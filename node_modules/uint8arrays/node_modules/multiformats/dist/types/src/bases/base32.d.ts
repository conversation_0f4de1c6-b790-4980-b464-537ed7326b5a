export const base32: import("./base.js").Codec<"base32", "b">;
export const base32upper: import("./base.js").Codec<"base32upper", "B">;
export const base32pad: import("./base.js").Codec<"base32pad", "c">;
export const base32padupper: import("./base.js").Codec<"base32padupper", "C">;
export const base32hex: import("./base.js").Codec<"base32hex", "v">;
export const base32hexupper: import("./base.js").Codec<"base32hexupper", "V">;
export const base32hexpad: import("./base.js").Codec<"base32hexpad", "t">;
export const base32hexpadupper: import("./base.js").Codec<"base32hexpadupper", "T">;
export const base32z: import("./base.js").Codec<"base32z", "h">;
//# sourceMappingURL=base32.d.ts.map