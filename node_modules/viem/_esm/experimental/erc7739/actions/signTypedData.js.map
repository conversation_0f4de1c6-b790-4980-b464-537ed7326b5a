{"version": 3, "file": "signTypedData.js", "sourceRoot": "", "sources": ["../../../../experimental/erc7739/actions/signTypedData.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,YAAY,EAAE,MAAM,yCAAyC,CAAA;AACtE,OAAO,EAEL,eAAe,GAChB,MAAM,4CAA4C,CAAA;AACnD,OAAO,EAAE,aAAa,IAAI,cAAc,EAAE,MAAM,0CAA0C,CAAA;AAG1F,OAAO,EAAE,oBAAoB,EAAE,MAAM,4BAA4B,CAAA;AAOjE,OAAO,EAAE,SAAS,EAAE,MAAM,6BAA6B,CAAA;AAEvD,OAAO,EAAE,sBAAsB,EAAE,MAAM,oCAAoC,CAAA;AAmC3E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkGG;AACH,MAAM,CAAC,KAAK,UAAU,aAAa,CAOjC,MAAyC,EACzC,UAKC;IAED,MAAM,EACJ,OAAO,EAAE,QAAQ,GAAG,MAAM,CAAC,OAAO,EAClC,MAAM,EACN,OAAO,EACP,WAAW,EACX,OAAO,EACP,WAAW,EACX,KAAK,EACL,QAAQ,GACT,GAAG,UAAgD,CAAA;IAEpD,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,oBAAoB,CAAC;YAC7B,QAAQ,EAAE,qCAAqC;SAChD,CAAC,CAAA;IACJ,MAAM,OAAO,GAAG,YAAY,CAAC,QAAS,CAAC,CAAA;IAEvC,kCAAkC;IAClC,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM,CAAC,KAAK,IAAI,EAAE;QACnD,IAAI,UAAU,CAAC,cAAc;YAC3B,OAAO;gBACL,MAAM,EAAE,UAAU,CAAC,cAAc;aAClC,CAAA;QACH,OAAO,SAAS,CACd,MAAM,EACN,eAAe,EACf,iBAAiB,CAClB,CAAC;YACA,OAAO,EAAE,QAAS;YAClB,OAAO;YACP,WAAW;SACZ,CAAC,CAAA;IACJ,CAAC,CAAC,EAAE,CAAA;IAEJ,gCAAgC;IAChC,MAAM,SAAS,GAAG,MAAM,SAAS,CAC/B,MAAM,EACN,cAAc,EACd,eAAe,CAChB,CAAC;QACA,OAAO;QACP,MAAM;QACN,KAAK,EAAE;YACL,GAAG,KAAK;YACR,aAAa,EAAE;gBACb,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE;gBACvC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE;gBAChC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE;gBACnC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;gBACpC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;gBAC9C,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE;aAClC;SACF;QACD,WAAW,EAAE,eAAe;QAC5B,OAAO,EAAE;YACP,QAAQ,EAAE,OAAc;YACxB,GAAI,cAAsB;SAC3B;KACF,CAAC,CAAA;IAEF,OAAO,sBAAsB,CAAC;QAC5B,MAAM;QACN,OAAO;QACP,WAAW;QACX,SAAS;QACT,KAAK;KACN,CAAC,CAAA;AACJ,CAAC"}