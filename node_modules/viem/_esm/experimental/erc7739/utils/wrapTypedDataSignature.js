import { serializeSignature } from '../../../accounts/index.js';
import { encodePacked } from '../../../utils/abi/encodePacked.js';
import { isHex } from '../../../utils/data/isHex.js';
import { size } from '../../../utils/data/size.js';
import { bytesToHex, stringToHex } from '../../../utils/encoding/toHex.js';
import { encodeType, hashStruct, } from '../../../utils/signature/hashTypedData.js';
import { getTypesForEIP712Domain } from '../../../utils/typedData.js';
/**
 * Wraps a typed data signature for ERC-7739.
 *
 * @example
 * ```ts
 * const signature = wrapTypedDataSignature({
 *   domain: {
 *     name: 'Ether Mail',
 *     version: '1',
 *     chainId: 1,
 *     verifyingContract: '******************************************',
 *   },
 *   types: {
 *     Person: [
 *       { name: 'name', type: 'string' },
 *       { name: 'wallet', type: 'address' },
 *     ],
 *     Mail: [
 *       { name: 'from', type: 'Person' },
 *       { name: 'to', type: 'Person' },
 *       { name: 'contents', type: 'string' },
 *     ],
 *   },
 *   primaryType: 'Mail',
 *   message: {
 *     from: {
 *       name: 'Cow',
 *       wallet: '******************************************',
 *     },
 *     to: {
 *       name: 'Bob',
 *       wallet: '******************************************',
 *     },
 *     contents: 'Hello, Bob!',
 *   },
 *   signature: '0x...',
 * })
 * ```
 */
export function wrapTypedDataSignature(parameters) {
    const { domain, message, primaryType, signature, types } = parameters;
    const signatureHex = (() => {
        if (isHex(signature))
            return signature;
        if (typeof signature === 'object' && 'r' in signature && 's' in signature)
            return serializeSignature(signature);
        return bytesToHex(signature);
    })();
    // Compute dependencies for wrapped signature.
    const hashedDomain = hashStruct({
        data: domain ?? {},
        types: {
            EIP712Domain: getTypesForEIP712Domain({ domain }),
        },
        primaryType: 'EIP712Domain',
    });
    const hashedContents = hashStruct({
        data: message,
        types: types,
        primaryType,
    });
    const encodedType = encodeType({
        primaryType,
        types: types,
    });
    // Construct wrapped signature.
    return encodePacked(['bytes', 'bytes32', 'bytes32', 'bytes', 'uint16'], [
        signatureHex,
        hashedDomain,
        hashedContents,
        stringToHex(encodedType),
        size(stringToHex(encodedType)),
    ]);
}
//# sourceMappingURL=wrapTypedDataSignature.js.map