import { hashTypedData as hashTypedData_, } from '../../../utils/signature/hashTypedData.js';
/**
 * Generates a signable hash for ERC-7739 typed data.
 *
 * @example
 * ```ts
 * const hash = hashTypedData({
 *   domain: {
 *     name: 'Ether Mail',
 *     version: '1',
 *     chainId: 1,
 *     verifyingContract: '******************************************',
 *   },
 *   types: {
 *     Person: [
 *       { name: 'name', type: 'string' },
 *       { name: 'wallet', type: 'address' },
 *     ],
 *     Mail: [
 *       { name: 'from', type: 'Person' },
 *       { name: 'to', type: 'Person' },
 *       { name: 'contents', type: 'string' },
 *     ],
 *   },
 *   primaryType: 'Mail',
 *   message: {
 *     from: {
 *       name: 'Cow',
 *       wallet: '******************************************',
 *     },
 *     to: {
 *       name: '<PERSON>',
 *       wallet: '******************************************',
 *     },
 *     contents: 'Hello, <PERSON>!',
 *   },
 *   verifierDomain: {
 *     name: 'Smart Account',
 *     version: '1',
 *     verifyingContract: '******************************************',
 *     chainId: 1,
 *   },
 * })
 * ```
 */
export function hashTypedData(parameters) {
    const { domain, message, primaryType, types, verifierDomain } = parameters;
    return hashTypedData_({
        domain: domain,
        types: {
            ...types,
            TypedDataSign: [
                { name: 'contents', type: primaryType },
                { name: 'name', type: 'string' },
                { name: 'version', type: 'string' },
                { name: 'chainId', type: 'uint256' },
                { name: 'verifyingContract', type: 'address' },
                { name: 'salt', type: 'bytes32' },
            ],
        },
        primaryType: 'TypedDataSign',
        message: {
            contents: message,
            ...verifierDomain,
        },
    });
}
//# sourceMappingURL=hashTypedData.js.map