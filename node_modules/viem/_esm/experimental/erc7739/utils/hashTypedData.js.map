{"version": 3, "file": "hashTypedData.js", "sourceRoot": "", "sources": ["../../../../experimental/erc7739/utils/hashTypedData.ts"], "names": [], "mappings": "AAKA,OAAO,EAEL,aAAa,IAAI,cAAc,GAChC,MAAM,2CAA2C,CAAA;AAkBlD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2CG;AACH,MAAM,UAAU,aAAa,CAI3B,UAA2D;IAE3D,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,UAAU,CAAA;IAE1E,OAAO,cAAc,CAAC;QACpB,MAAM,EAAE,MAAa;QACrB,KAAK,EAAE;YACL,GAAG,KAAK;YACR,aAAa,EAAE;gBACb,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE;gBACvC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE;gBAChC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE;gBACnC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;gBACpC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;gBAC9C,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE;aAClC;SACF;QACD,WAAW,EAAE,eAAe;QAC5B,OAAO,EAAE;YACP,QAAQ,EAAE,OAAc;YACxB,GAAI,cAAsB;SAC3B;KACF,CAAC,CAAA;AACJ,CAAC"}