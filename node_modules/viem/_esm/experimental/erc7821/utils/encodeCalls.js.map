{"version": 3, "file": "encodeCalls.js", "sourceRoot": "", "sources": ["../../../../experimental/erc7821/utils/encodeCalls.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,aAAa,MAAM,kBAAkB,CAAA;AAKjD,OAAO,EAEL,kBAAkB,GACnB,MAAM,0CAA0C,CAAA;AAQjD,MAAM,UAAU,WAAW,CACzB,MAAiC,EACjC,MAAwB;IAExB,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QACjC,MAAM,IAAI,GAAG,KAAa,CAAA;QAC1B,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC;YAC/D,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;YACvB,MAAM,EAAE,IAAI,CAAC,EAAE;SAChB,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,OAAO,aAAa,CAAC,MAAM,CACzB,aAAa,CAAC,IAAI,CAAC;QACjB,4DAA4D;QAC5D,cAAc;QACd,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;KACpC,CAAC,EACF,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAQ,CAC5C,CAAA;AACH,CAAC"}