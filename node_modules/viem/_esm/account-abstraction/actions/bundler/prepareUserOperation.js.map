{"version": 3, "file": "prepareUserOperation.js", "sourceRoot": "", "sources": ["../../../../account-abstraction/actions/bundler/prepareUserOperation.ts"], "names": [], "mappings": "AACA,OAAO,EAEL,YAAY,GACb,MAAM,yCAAyC,CAAA;AAChD,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAA;AAChE,OAAO,EAEL,kBAAkB,GACnB,MAAM,+CAA+C,CAAA;AACtD,OAAO,EAAE,UAAU,IAAI,WAAW,EAAE,MAAM,uCAAuC,CAAA;AAGjF,OAAO,EAAE,oBAAoB,EAAE,MAAM,4BAA4B,CAAA;AAajE,OAAO,EAEL,kBAAkB,GACnB,MAAM,0CAA0C,CAAA;AACjD,OAAO,EAAwB,MAAM,EAAE,MAAM,+BAA+B,CAAA;AAC5E,OAAO,EAAE,SAAS,EAAE,MAAM,6BAA6B,CAAA;AAgBvD,OAAO,EAEL,gBAAgB,IAAI,iBAAiB,GACtC,MAAM,kCAAkC,CAAA;AACzC,OAAO,EAEL,oBAAoB,IAAI,qBAAqB,GAC9C,MAAM,sCAAsC,CAAA;AAC7C,OAAO,EAEL,wBAAwB,GACzB,MAAM,+BAA+B,CAAA;AAEtC,MAAM,iBAAiB,GAAG;IACxB,SAAS;IACT,MAAM;IACN,KAAK;IACL,WAAW;IACX,OAAO;IACP,WAAW;IACX,eAAe;CACP,CAAA;AAuMV;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB,CAUxC,MAAqD,EACrD,WAKC;IAID,MAAM,UAAU,GAAG,WAA6C,CAAA;IAChE,MAAM,EACJ,OAAO,EAAE,QAAQ,GAAG,MAAM,CAAC,OAAO,EAClC,UAAU,EAAE,UAAU,GAAG,iBAAiB,EAC1C,aAAa,GACd,GAAG,UAAU,CAAA;IAEd,gFAAgF;IAChF,qCAAqC;IACrC,gFAAgF;IAEhF,IAAI,CAAC,QAAQ;QAAE,MAAM,IAAI,oBAAoB,EAAE,CAAA;IAC/C,MAAM,OAAO,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAA;IAEtC,gFAAgF;IAChF,gCAAgC;IAChC,gFAAgF;IAEhF,MAAM,aAAa,GAAG,MAAkC,CAAA;IAExD,gFAAgF;IAChF,gCAAgC;IAChC,gFAAgF;IAEhF,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,IAAI,aAAa,EAAE,SAAS,CAAA;IAClE,MAAM,gBAAgB,GAAG,OAAO,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAA;IAC9E,MAAM,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,GAAG,CAAC,GAAG,EAAE;QACvD,sFAAsF;QACtF,IAAI,SAAS,KAAK,IAAI;YACpB,OAAO;gBACL,oBAAoB,EAAE,CAAC,UAAe,EAAE,EAAE,CACxC,SAAS,CACP,aAAa,EACb,qBAAqB,EACrB,sBAAsB,CACvB,CAAC,UAAU,CAAC;gBACf,gBAAgB,EAAE,CAAC,UAAe,EAAE,EAAE,CACpC,SAAS,CACP,aAAa,EACb,iBAAiB,EACjB,kBAAkB,CACnB,CAAC,UAAU,CAAC;aAChB,CAAA;QAEH,6FAA6F;QAC7F,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;YAClC,MAAM,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,GAAG,SAAS,CAAA;YAC5D,OAAO;gBACL,oBAAoB,EAAE,CAAC,gBAAgB,IAAI,oBAAoB;oBAC7D,CAAC,CAAC,oBAAoB;oBACtB,CAAC,CAAC,gBAAgB,CAAgC;gBACpD,gBAAgB,EACd,gBAAgB,IAAI,oBAAoB;oBACtC,CAAC,CAAC,gBAAgB;oBAClB,CAAC,CAAC,SAAS;aAChB,CAAA;QACH,CAAC;QAED,0BAA0B;QAC1B,OAAO;YACL,oBAAoB,EAAE,SAAS;YAC/B,gBAAgB,EAAE,SAAS;SAC5B,CAAA;IACH,CAAC,CAAC,EAAE,CAAA;IACJ,MAAM,gBAAgB,GAAG,UAAU,CAAC,gBAAgB;QAClD,CAAC,CAAC,UAAU,CAAC,gBAAgB;QAC7B,CAAC,CAAC,aAAa,EAAE,gBAAgB,CAAA;IAEnC,gFAAgF;IAChF,qCAAqC;IACrC,gFAAgF;IAEhF,IAAI,OAAO,GAAG;QACZ,GAAG,UAAU;QACb,SAAS,EAAE,gBAAgB;QAC3B,MAAM,EAAE,OAAO,CAAC,OAAO;KACO,CAAA;IAEhC,gFAAgF;IAChF,uEAAuE;IACvE,gFAAgF;IAEhF,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,aAAa,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QACxE,CAAC,KAAK,IAAI,EAAE;YACV,IAAI,UAAU,CAAC,KAAK;gBAClB,OAAO,OAAO,CAAC,WAAW,CACxB,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;oBAC7B,MAAM,IAAI,GAAG,KAAa,CAAA;oBAC1B,IAAI,IAAI,CAAC,GAAG;wBACV,OAAO;4BACL,IAAI,EAAE,kBAAkB,CAAC,IAAI,CAAC;4BAC9B,EAAE,EAAE,IAAI,CAAC,EAAE;4BACX,KAAK,EAAE,IAAI,CAAC,KAAK;yBACV,CAAA;oBACX,OAAO,IAAY,CAAA;gBACrB,CAAC,CAAC,CACH,CAAA;YACH,OAAO,UAAU,CAAC,QAAQ,CAAA;QAC5B,CAAC,CAAC,EAAE;QACJ,CAAC,KAAK,IAAI,EAAE;YACV,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAAE,OAAO,SAAS,CAAA;YACrD,IAAI,UAAU,CAAC,QAAQ;gBAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAA;YACjE,IAAI,UAAU,CAAC,OAAO,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;gBACjD,OAAO;oBACL,OAAO,EAAE,UAAU,CAAC,OAAO;oBAC3B,WAAW,EAAE,UAAU,CAAC,WAAW;iBACpC,CAAA;YACH,CAAC;YAED,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,MAAM,OAAO,CAAC,cAAc,EAAE,CAAA;YAE/D,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,KAAK,KAAK;gBACtC,OAAO;oBACL,QAAQ,EACN,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;iBACtE,CAAA;YACH,OAAO;gBACL,OAAO;gBACP,WAAW;aACZ,CAAA;QACH,CAAC,CAAC,EAAE;QACJ,CAAC,KAAK,IAAI,EAAE;YACV,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAAE,OAAO,SAAS,CAAA;YAElD,0DAA0D;YAC1D,IACE,OAAO,UAAU,CAAC,YAAY,KAAK,QAAQ;gBAC3C,OAAO,UAAU,CAAC,oBAAoB,KAAK,QAAQ;gBAEnD,OAAO,OAAO,CAAA;YAEhB,iEAAiE;YACjE,IAAI,aAAa,EAAE,aAAa,EAAE,kBAAkB,EAAE,CAAC;gBACrD,MAAM,IAAI,GAAG,MAAM,aAAa,CAAC,aAAa,CAAC,kBAAkB,CAAC;oBAChE,OAAO;oBACP,aAAa;oBACb,aAAa,EAAE,OAAwB;iBACxC,CAAC,CAAA;gBACF,OAAO;oBACL,GAAG,OAAO;oBACV,GAAG,IAAI;iBACR,CAAA;YACH,CAAC;YAED,2EAA2E;YAC3E,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,aAAa,CAAC,MAAM,IAAI,MAAM,CAAA;gBAC9C,MAAM,IAAI,GAAG,MAAM,SAAS,CAC1B,OAAO,EACP,kBAAkB,EAClB,oBAAoB,CACrB,CAAC;oBACA,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,IAAI,EAAE,SAAS;iBAChB,CAAC,CAAA;gBACF,OAAO;oBACL,YAAY,EACV,OAAO,UAAU,CAAC,YAAY,KAAK,QAAQ;wBACzC,CAAC,CAAC,UAAU,CAAC,YAAY;wBACzB,CAAC,CAAC,MAAM;wBACJ,qGAAqG;wBACrG,EAAE,GAAG,IAAI,CAAC,YAAY,CACvB;oBACP,oBAAoB,EAClB,OAAO,UAAU,CAAC,oBAAoB,KAAK,QAAQ;wBACjD,CAAC,CAAC,UAAU,CAAC,oBAAoB;wBACjC,CAAC,CAAC,MAAM;wBACJ,qGAAqG;wBACrG,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAC/B;iBACR,CAAA;YACH,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO,SAAS,CAAA;YAClB,CAAC;QACH,CAAC,CAAC,EAAE;QACJ,CAAC,KAAK,IAAI,EAAE;YACV,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAAE,OAAO,SAAS,CAAA;YACnD,IAAI,OAAO,UAAU,CAAC,KAAK,KAAK,QAAQ;gBAAE,OAAO,UAAU,CAAC,KAAK,CAAA;YACjE,OAAO,OAAO,CAAC,QAAQ,EAAE,CAAA;QAC3B,CAAC,CAAC,EAAE;QACJ,CAAC,KAAK,IAAI,EAAE;YACV,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC;gBAAE,OAAO,SAAS,CAAA;YAC3D,IAAI,OAAO,UAAU,CAAC,aAAa,KAAK,QAAQ;gBAC9C,OAAO,UAAU,CAAC,aAAa,CAAA;YACjC,IAAI,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC;gBAC3D,MAAM,aAAa,GAAG,MAAM,oBAAoB,CAC9C,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,aAAa,CACtB,CAAA;gBACD,OAAO;oBACL,GAAG,aAAa;oBAChB,CAAC,EAAE,oEAAoE;oBACvE,CAAC,EAAE,oEAAoE;oBACvE,OAAO,EAAE,CAAC;iBACmB,CAAA;YACjC,CAAC;YACD,OAAO,SAAS,CAAA;QAClB,CAAC,CAAC,EAAE;KACL,CAAC,CAAA;IAEF,gFAAgF;IAChF,+DAA+D;IAC/D,gFAAgF;IAEhF,IAAI,OAAO,QAAQ,KAAK,WAAW;QAAE,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAA;IAChE,IAAI,OAAO,OAAO,KAAK,WAAW;QAChC,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE,GAAI,OAAe,EAAE,CAAA;IAC/C,IAAI,OAAO,IAAI,KAAK,WAAW;QAAE,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE,GAAI,IAAY,EAAE,CAAA;IAC3E,IAAI,OAAO,KAAK,KAAK,WAAW;QAAE,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA;IACvD,IAAI,OAAO,aAAa,KAAK,WAAW;QACtC,OAAO,CAAC,aAAa,GAAG,aAAa,CAAA;IAEvC,gFAAgF;IAChF,qDAAqD;IACrD,gFAAgF;IAEhF,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;QACrC,IAAI,OAAO,UAAU,CAAC,SAAS,KAAK,WAAW;YAC7C,OAAO,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAA;;YAExC,OAAO,CAAC,SAAS,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAChD,OAAwB,CACzB,CAAA;IACL,CAAC;IAED,gFAAgF;IAChF,2DAA2D;IAC3D,gFAAgF;IAEhF,8DAA8D;IAC9D,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ;QAC3D,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAA;IAEzB,gFAAgF;IAChF,gFAAgF;IAChF,gFAAgF;IAEhF,IAAI,OAA2B,CAAA;IAC/B,KAAK,UAAU,UAAU;QACvB,IAAI,OAAO;YAAE,OAAO,OAAO,CAAA;QAC3B,IAAI,MAAM,CAAC,KAAK;YAAE,OAAO,MAAM,CAAC,KAAK,CAAC,EAAE,CAAA;QACxC,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,MAAM,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC,CAAA;QACvE,OAAO,GAAG,QAAQ,CAAA;QAClB,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,gGAAgG;IAChG,yEAAyE;IACzE,IAAI,oBAAoB,GAAG,KAAK,CAAA;IAChC,IACE,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC;QAChC,oBAAoB;QACpB,CAAC,gBAAgB;QACjB,CAAC,UAAU,CAAC,gBAAgB,EAC5B,CAAC;QACD,MAAM,EACJ,OAAO,GAAG,KAAK,EACf,OAAO,EACP,GAAG,aAAa,EACjB,GAAG,MAAM,oBAAoB,CAAC;YAC7B,OAAO,EAAE,MAAM,UAAU,EAAE;YAC3B,iBAAiB,EAAE,OAAO,CAAC,UAAU,CAAC,OAAO;YAC7C,OAAO,EAAE,gBAAgB;YACzB,GAAI,OAAyB;SAC9B,CAAC,CAAA;QACF,oBAAoB,GAAG,OAAO,CAAA;QAC9B,OAAO,GAAG;YACR,GAAG,OAAO;YACV,GAAG,aAAa;SACc,CAAA;IAClC,CAAC;IAED,gFAAgF;IAChF,mEAAmE;IACnE,gFAAgF;IAEhF,sEAAsE;IACtE,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,gBAAgB;QACnE,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAA;IAEjC,gFAAgF;IAChF,mDAAmD;IACnD,gFAAgF;IAEhF,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QAC/B,sFAAsF;QACtF,qDAAqD;QACrD,IAAI,OAAO,CAAC,aAAa,EAAE,WAAW,EAAE,CAAC;YACvC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,aAAa,CAAC,WAAW,CACjD,OAAwB,CACzB,CAAA;YACD,OAAO,GAAG;gBACR,GAAG,OAAO;gBACV,GAAG,GAAG;aACwB,CAAA;QAClC,CAAC;QAED,wFAAwF;QACxF,8BAA8B;QAC9B,IACE,OAAO,OAAO,CAAC,YAAY,KAAK,WAAW;YAC3C,OAAO,OAAO,CAAC,kBAAkB,KAAK,WAAW;YACjD,OAAO,OAAO,CAAC,oBAAoB,KAAK,WAAW;YACnD,CAAC,OAAO,CAAC,SAAS;gBAChB,OAAO,OAAO,CAAC,uBAAuB,KAAK,WAAW,CAAC;YACzD,CAAC,OAAO,CAAC,SAAS;gBAChB,OAAO,OAAO,CAAC,6BAA6B,KAAK,WAAW,CAAC,EAC/D,CAAC;YACD,MAAM,GAAG,GAAG,MAAM,SAAS,CACzB,aAAa,EACb,wBAAwB,EACxB,0BAA0B,CAC3B,CAAC;gBACA,OAAO;gBACP,iFAAiF;gBACjF,kDAAkD;gBAClD,YAAY,EAAE,EAAE;gBAChB,kBAAkB,EAAE,EAAE;gBACtB,oBAAoB,EAAE,EAAE;gBACxB,aAAa;gBACb,GAAG,CAAC,OAAO,CAAC,SAAS;oBACnB,CAAC,CAAC;wBACE,uBAAuB,EAAE,EAAE;wBAC3B,6BAA6B,EAAE,EAAE;qBAClC;oBACH,CAAC,CAAC,EAAE,CAAC;gBACP,GAAG,OAAO;aAC2B,CAAC,CAAA;YACxC,OAAO,GAAG;gBACR,GAAG,OAAO;gBACV,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,GAAG,CAAC,YAAY;gBACtD,kBAAkB,EAChB,OAAO,CAAC,kBAAkB,IAAI,GAAG,CAAC,kBAAkB;gBACtD,oBAAoB,EAClB,OAAO,CAAC,oBAAoB,IAAI,GAAG,CAAC,oBAAoB;gBAC1D,uBAAuB,EACrB,OAAO,CAAC,uBAAuB,IAAI,GAAG,CAAC,uBAAuB;gBAChE,6BAA6B,EAC3B,OAAO,CAAC,6BAA6B;oBACrC,GAAG,CAAC,6BAA6B;aACL,CAAA;QAClC,CAAC;IACH,CAAC;IAED,gFAAgF;IAChF,4FAA4F;IAC5F,gFAAgF;IAEhF,gGAAgG;IAChG,iEAAiE;IACjE,IACE,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC;QAChC,gBAAgB;QAChB,CAAC,gBAAgB;QACjB,CAAC,UAAU,CAAC,gBAAgB;QAC5B,CAAC,oBAAoB,EACrB,CAAC;QACD,sGAAsG;QACtG,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC;YACvC,OAAO,EAAE,MAAM,UAAU,EAAE;YAC3B,iBAAiB,EAAE,OAAO,CAAC,UAAU,CAAC,OAAO;YAC7C,OAAO,EAAE,gBAAgB;YACzB,GAAI,OAAyB;SAC9B,CAAC,CAAA;QACF,OAAO,GAAG;YACR,GAAG,OAAO;YACV,GAAG,SAAS;SACkB,CAAA;IAClC,CAAC;IAED,gFAAgF;IAChF,gFAAgF;IAChF,gFAAgF;IAEhF,OAAO,OAAO,CAAC,KAAK,CAAA;IACpB,OAAO,OAAO,CAAC,UAAU,CAAA;IACzB,OAAO,OAAO,CAAC,gBAAgB,CAAA;IAC/B,IAAI,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ;QAAE,OAAO,OAAO,CAAC,SAAS,CAAA;IAEnE,gFAAgF;IAEhF,OAAO,OAKN,CAAA;AACH,CAAC"}