{"version": 3, "file": "getUserOperationError.js", "sourceRoot": "", "sources": ["../../../../account-abstraction/utils/errors/getUserOperationError.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAA;AACnD,OAAO,EACL,8BAA8B,EAC9B,6BAA6B,EAC7B,6BAA6B,GAC9B,MAAM,6BAA6B,CAAA;AAIpC,OAAO,EAAE,iBAAiB,EAAE,MAAM,yCAAyC,CAAA;AAE3E,OAAO,EAAE,sBAAsB,EAAE,MAAM,yBAAyB,CAAA;AAChE,OAAO,EACL,2BAA2B,GAE5B,MAAM,+BAA+B,CAAA;AAEtC,OAAO,EAEL,eAAe,GAChB,MAAM,sBAAsB,CAAA;AAgB7B,MAAM,UAAU,qBAAqB,CACnC,GAAQ,EACR,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAmC;IAE7D,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE;QAClB,MAAM,KAAK,GAAG,eAAe,CAC3B,GAAsB,EACtB,IAAiC,CAClC,CAAA;QACD,IAAI,KAAK,IAAI,KAAK,YAAY,sBAAsB,EAAE,CAAC;YACrD,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,CAAA;YACvC,MAAM,aAAa,GAAG,KAAK,EAAE,MAAM,CACjC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CACL,CAAA;YACpB,IAAI,UAAU,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC;gBACxC,OAAO,gBAAgB,CAAC,EAAE,KAAK,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC,CAAA;QACjE,CAAC;QACD,OAAO,KAAK,CAAA;IACd,CAAC,CAAC,EAAE,CAAA;IACJ,OAAO,IAAI,2BAA2B,CAAC,KAAK,EAAE;QAC5C,QAAQ;QACR,GAAG,IAAI;KACR,CAAyC,CAAA;AAC5C,CAAC;AAED,iFAAiF;AAEjF,SAAS,aAAa,CAAC,KAAgB;IACrC,IAAI,UAA2B,CAAA;IAC/B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;QACf,MAAM,KAAK,GAAG,CAAQ,CAAA;QACtB,IACE,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ;YAC9B,OAAO,KAAK,CAAC,IAAI,EAAE,UAAU,KAAK,QAAQ;YAC1C,CAAC,CAAC,CAAC,KAAK,YAAY,SAAS,CAAC,IAAI,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ,CAAC,EACpE,CAAC;YACD,MAAM,KAAK,GAAG,CACZ,KAAK,CAAC,IAAI,EAAE,UAAU;gBACtB,KAAK,CAAC,IAAI;gBACV,KAAK,CAAC,OAAO,CACd,CAAC,KAAK,EAAE,CAAC,kBAAkB,CAAC,CAAA;YAC7B,IAAI,KAAK,EAAE,CAAC;gBACV,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;gBACrB,OAAO,IAAI,CAAA;YACb,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAA;IACd,CAAC,CAAC,CAAA;IACF,OAAO,UAAU,CAAA;AACnB,CAAC;AAED,SAAS,gBAAgB,CAAC,UAGzB;IACC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,UAAU,CAAA;IAExC,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG,EAAE;QAC5C,MAAM,aAAa,GAAG,KAAK,EAAE,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAC3C,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CACC,CAAA;QAEpB,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,aAAa,CAAC,CAAC,CAAC,CAAA;QAEvD,MAAM,mBAAmB,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;YACxD,IAAI,CAAC;gBACH,OAAO,OAAO,CACZ,iBAAiB,CAAC;oBAChB,GAAG,EAAE,IAAI,CAAC,GAAG;oBACb,IAAI,EAAE,UAAU;iBACjB,CAAC,CACH,CAAA;YACH,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO,KAAK,CAAA;YACd,CAAC;QACH,CAAC,CAAC,CAAA;QACF,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,mBAAmB,CAAC,CAAC,CAAC,CAAA;QAEnE,OAAO;YACL,GAAG,EAAE,EAAE;YACP,YAAY,EAAE,aAAa,CAAC,MAAM,CAChC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,EAC9D,EAAE,CACH;YACD,IAAI,EAAE,SAAS;YACf,EAAE,EAAE,SAAS;SACd,CAAA;IACH,CAAC,CAAC,EAKD,CAAA;IAED,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE;QAClB,IAAI,UAAU,KAAK,IAAI;YACrB,OAAO,IAAI,6BAA6B,CAAC,EAAE,YAAY,EAAE,CAAC,CAAA;QAC5D,OAAO,IAAI,6BAA6B,CAAC;YACvC,GAAG;YACH,IAAI,EAAE,UAAU;YAChB,YAAY;SACb,CAAC,CAAA;IACJ,CAAC,CAAC,EAAE,CAAA;IACJ,OAAO,IAAI,8BAA8B,CAAC,KAAkB,EAAE;QAC5D,GAAG;QACH,IAAI;QACJ,eAAe,EAAE,EAAE;QACnB,YAAY;KACb,CAA+B,CAAA;AAClC,CAAC"}