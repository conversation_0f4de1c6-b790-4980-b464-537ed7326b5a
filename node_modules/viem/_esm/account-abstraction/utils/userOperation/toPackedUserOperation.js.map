{"version": 3, "file": "toPackedUserOperation.js", "sourceRoot": "", "sources": ["../../../../account-abstraction/utils/userOperation/toPackedUserOperation.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,+BAA+B,CAAA;AACtD,OAAO,EAAE,GAAG,EAAE,MAAM,4BAA4B,CAAA;AAChD,OAAO,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAA;AAKrD,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAA;AAE9C,MAAM,UAAU,qBAAqB,CACnC,aAA4B;IAE5B,MAAM,EACJ,YAAY,EACZ,QAAQ,EACR,oBAAoB,EACpB,YAAY,EACZ,SAAS,EACT,aAAa,EACb,uBAAuB,EACvB,6BAA6B,EAC7B,MAAM,EACN,SAAS,GAAG,IAAI,EAChB,oBAAoB,GACrB,GAAG,aAAa,CAAA;IAEjB,MAAM,gBAAgB,GAAG,MAAM,CAAC;QAC9B,GAAG,CAAC,WAAW,CAAC,oBAAoB,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;QAC1D,GAAG,CAAC,WAAW,CAAC,YAAY,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;KACnD,CAAC,CAAA;IACF,MAAM,QAAQ,GAAG,WAAW,CAAC,aAAa,CAAC,CAAA;IAC3C,MAAM,OAAO,GAAG,MAAM,CAAC;QACrB,GAAG,CAAC,WAAW,CAAC,oBAAoB,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;QAC1D,GAAG,CAAC,WAAW,CAAC,YAAY,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;KACnD,CAAC,CAAA;IACF,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,IAAI,EAAE,CAAA;IACvC,MAAM,gBAAgB,GAAG,SAAS;QAChC,CAAC,CAAC,MAAM,CAAC;YACL,SAAS;YACT,GAAG,CAAC,WAAW,CAAC,6BAA6B,IAAI,EAAE,CAAC,EAAE;gBACpD,IAAI,EAAE,EAAE;aACT,CAAC;YACF,GAAG,CAAC,WAAW,CAAC,uBAAuB,IAAI,EAAE,CAAC,EAAE;gBAC9C,IAAI,EAAE,EAAE;aACT,CAAC;YACF,aAAa,IAAI,IAAI;SACtB,CAAC;QACJ,CAAC,CAAC,IAAI,CAAA;IACR,MAAM,kBAAkB,GAAG,aAAa,CAAC,kBAAkB,IAAI,EAAE,CAAA;IAEjE,OAAO;QACL,gBAAgB;QAChB,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,KAAK;QACL,gBAAgB;QAChB,kBAAkB;QAClB,MAAM;QACN,SAAS;KACV,CAAA;AACH,CAAC"}