{"version": 3, "file": "getUserOperationHash.js", "sourceRoot": "", "sources": ["../../../../account-abstraction/utils/userOperation/getUserOperationHash.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,mBAAmB,EAAE,MAAM,2CAA2C,CAAA;AAC/E,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAA;AAC5D,OAAO,EAAE,aAAa,EAAE,MAAM,2CAA2C,CAAA;AAGzE,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAA;AAC9C,OAAO,EAAE,yBAAyB,EAAE,MAAM,gCAAgC,CAAA;AAC1E,OAAO,EAAE,qBAAqB,EAAE,MAAM,4BAA4B,CAAA;AAalE,MAAM,UAAU,oBAAoB,CAGlC,UAA6D;IAE7D,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,GAAG,UAAU,CAAA;IACpE,MAAM,aAAa,GAAG,UAAU,CAAC,aAA8B,CAAA;IAC/D,MAAM,EACJ,aAAa,EACb,QAAQ,GAAG,IAAI,EACf,YAAY,EACZ,YAAY,EACZ,oBAAoB,EACpB,KAAK,EACL,gBAAgB,GAAG,IAAI,EACvB,kBAAkB,EAClB,MAAM,EACN,oBAAoB,GACrB,GAAG,aAAa,CAAA;IAEjB,IAAI,iBAAiB,KAAK,KAAK;QAC7B,OAAO,aAAa,CAClB,yBAAyB,CAAC;YACxB,OAAO;YACP,iBAAiB;YACjB,aAAa;SACd,CAAC,CACH,CAAA;IAEH,MAAM,YAAY,GAAG,CAAC,GAAG,EAAE;QACzB,IAAI,iBAAiB,KAAK,KAAK,EAAE,CAAC;YAChC,MAAM,OAAO,GAAG,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAQ,CAAA;YAC3D,MAAM,WAAW,GAAG,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAoB,CAAA;YACxE,MAAM,QAAQ,GAAG,WAAW,CAAC;gBAC3B,aAAa;gBACb,OAAO;gBACP,WAAW;aACZ,CAAC,CAAA;YACF,OAAO,mBAAmB,CACxB;gBACE,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;aACpB,EACD;gBACE,MAAM;gBACN,KAAK;gBACL,SAAS,CAAC,QAAQ,CAAC;gBACnB,SAAS,CAAC,QAAQ,CAAC;gBACnB,YAAY;gBACZ,oBAAoB;gBACpB,kBAAkB;gBAClB,YAAY;gBACZ,oBAAoB;gBACpB,SAAS,CAAC,gBAAgB,CAAC;aAC5B,CACF,CAAA;QACH,CAAC;QAED,IAAI,iBAAiB,KAAK,KAAK,EAAE,CAAC;YAChC,MAAM,YAAY,GAAG,qBAAqB,CAAC,aAAa,CAAC,CAAA;YACzD,OAAO,mBAAmB,CACxB;gBACE,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;aACpB,EACD;gBACE,YAAY,CAAC,MAAM;gBACnB,YAAY,CAAC,KAAK;gBAClB,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC;gBAChC,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC;gBAChC,YAAY,CAAC,gBAAgB;gBAC7B,YAAY,CAAC,kBAAkB;gBAC/B,YAAY,CAAC,OAAO;gBACpB,SAAS,CAAC,YAAY,CAAC,gBAAgB,CAAC;aACzC,CACF,CAAA;QACH,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,sBAAsB,iBAAiB,kBAAkB,CAAC,CAAA;IAC5E,CAAC,CAAC,EAAE,CAAA;IAEJ,OAAO,SAAS,CACd,mBAAmB,CACjB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAC/D,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,iBAAiB,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAC9D,CACF,CAAA;AACH,CAAC"}