{"version": 3, "file": "toSimple7702SmartAccount.js", "sourceRoot": "", "sources": ["../../../../account-abstraction/accounts/implementations/toSimple7702SmartAccount.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,mBAAmB,EAAE,MAAM,+BAA+B,CAAA;AACnE,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAA;AAGnD,OAAO,EAAE,kBAAkB,EAAE,MAAM,0CAA0C,CAAA;AAC7E,OAAO,EAAE,kBAAkB,EAAE,MAAM,0CAA0C,CAAA;AAC7E,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAA;AACzD,OAAO,EAAE,yBAAyB,EAAE,MAAM,wDAAwD,CAAA;AAClG,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAA;AAqBrD;;;;;;;;;;;;;;GAcG;AACH,MAAM,CAAC,KAAK,UAAU,wBAAwB,CAC5C,UAA8C;IAE9C,MAAM,EACJ,MAAM,EACN,cAAc,GAAG,4CAA4C,EAC7D,QAAQ,EACR,KAAK,GACN,GAAG,UAAU,CAAA;IAEd,MAAM,UAAU,GAAG;QACjB,GAAG,EAAE,eAAe;QACpB,OAAO,EAAE,mBAAmB;QAC5B,OAAO,EAAE,KAAK;KACN,CAAA;IAEV,OAAO,cAAc,CAAC;QACpB,aAAa,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,cAAc,EAAE;QAC1D,GAAG;QACH,MAAM;QACN,MAAM,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,+DAA+D;QACvF,UAAU;QACV,QAAQ;QAER,KAAK,CAAC,WAAW,CAAC,IAAI;YACpB,MAAM,MAAM,GAAG,kBAAkB,CAAC;gBAChC,GAAG;gBACH,IAAI;aACL,CAAC,CAAA;YAEF,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS;gBACnC,OAAO;oBACL,EAAE,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;iBACpE,CAAA;YACH,IAAI,MAAM,CAAC,YAAY,KAAK,cAAc;gBACxC,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;oBAClC,EAAE,EAAE,GAAG,CAAC,MAAM;oBACd,KAAK,EAAE,GAAG,CAAC,KAAK;oBAChB,IAAI,EAAE,GAAG,CAAC,IAAI;iBACf,CAAC,CAAC,CAAA;YACL,MAAM,IAAI,SAAS,CAAC,+BAA+B,MAAM,CAAC,YAAY,GAAG,CAAC,CAAA;QAC5E,CAAC;QAED,KAAK,CAAC,WAAW,CAAC,KAAK;YACrB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;gBACpB,OAAO,kBAAkB,CAAC;oBACxB,GAAG;oBACH,YAAY,EAAE,SAAS;oBACvB,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC;iBACjE,CAAC,CAAA;YACJ,OAAO,kBAAkB,CAAC;gBACxB,GAAG;gBACH,YAAY,EAAE,cAAc;gBAC5B,IAAI,EAAE;oBACJ,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;wBACnB,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI;wBACvB,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;qBACxB,CAAC,CAAC;iBACJ;aACF,CAAC,CAAA;QACJ,CAAC;QAED,KAAK,CAAC,UAAU;YACd,OAAO,KAAK,CAAC,OAAO,CAAA;QACtB,CAAC;QAED,KAAK,CAAC,cAAc;YAClB,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,CAAA;QACjD,CAAC;QAED,KAAK,CAAC,gBAAgB;YACpB,OAAO,sIAAsI,CAAA;QAC/I,CAAC;QAED,KAAK,CAAC,WAAW,CAAC,UAAU;YAC1B,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,CAAA;YAC9B,OAAO,MAAM,KAAK,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,CAAC,CAAA;QAC7C,CAAC;QAED,KAAK,CAAC,aAAa,CAAC,UAAU;YAC5B,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,GAC3C,UAAoD,CAAA;YACtD,OAAO,MAAM,KAAK,CAAC,aAAa,CAAC;gBAC/B,MAAM;gBACN,OAAO;gBACP,WAAW;gBACX,KAAK;aACN,CAAC,CAAA;QACJ,CAAC;QAED,KAAK,CAAC,iBAAiB,CAAC,UAAU;YAChC,MAAM,EAAE,OAAO,GAAG,MAAM,CAAC,KAAM,CAAC,EAAE,EAAE,GAAG,aAAa,EAAE,GAAG,UAAU,CAAA;YAEnE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;YACvC,MAAM,SAAS,GAAG,yBAAyB,CAAC;gBAC1C,OAAO;gBACP,iBAAiB,EAAE,UAAU,CAAC,OAAO;gBACrC,aAAa,EAAE;oBACb,GAAG,aAAa;oBAChB,MAAM,EAAE,OAAO;iBAChB;aACF,CAAC,CAAA;YACF,OAAO,MAAM,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA;QAC7C,CAAC;KACF,CAAC,CAAA;AACJ,CAAC;AAED,6FAA6F;AAC7F,YAAY;AAEZ,MAAM,GAAG,GAAG;IACV,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,uBAAuB,EAAE,IAAI,EAAE,OAAO,EAAE;IAC5D;QACE,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;QACtE,IAAI,EAAE,6BAA6B;QACnC,IAAI,EAAE,OAAO;KACd;IACD;QACE,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;QACjE,IAAI,EAAE,wBAAwB;QAC9B,IAAI,EAAE,OAAO;KACd;IACD;QACE,MAAM,EAAE;YACN,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE;YAC3D,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;SACxD;QACD,IAAI,EAAE,cAAc;QACpB,IAAI,EAAE,OAAO;KACd;IACD,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE;IAChD;QACE,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE;YACP,EAAE,YAAY,EAAE,sBAAsB,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;SACpE;QACD,eAAe,EAAE,MAAM;QACvB,IAAI,EAAE,UAAU;KACjB;IACD;QACE,MAAM,EAAE;YACN,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;YAC5D,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE;YAC3D,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE;SACvD;QACD,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,EAAE;QACX,eAAe,EAAE,YAAY;QAC7B,IAAI,EAAE,UAAU;KACjB;IACD;QACE,MAAM,EAAE;YACN;gBACE,UAAU,EAAE;oBACV,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;oBAC5D,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE;oBAC3D,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE;iBACvD;gBACD,YAAY,EAAE,2BAA2B;gBACzC,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,SAAS;aAChB;SACF;QACD,IAAI,EAAE,cAAc;QACpB,OAAO,EAAE,EAAE;QACX,eAAe,EAAE,YAAY;QAC7B,IAAI,EAAE,UAAU;KACjB;IACD;QACE,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,UAAU;QAChB,OAAO,EAAE,CAAC,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;QACjE,eAAe,EAAE,MAAM;QACvB,IAAI,EAAE,UAAU;KACjB;IACD;QACE,MAAM,EAAE;YACN,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE;YAC1D,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE;SAC5D;QACD,IAAI,EAAE,kBAAkB;QACxB,OAAO,EAAE,CAAC,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;QACzE,eAAe,EAAE,MAAM;QACvB,IAAI,EAAE,UAAU;KACjB;IACD;QACE,MAAM,EAAE;YACN,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;YACtD,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;YACtD,EAAE,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE;YAC1D,EAAE,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE;YAC1D,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;SACnD;QACD,IAAI,EAAE,wBAAwB;QAC9B,OAAO,EAAE,CAAC,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;QAC/D,eAAe,EAAE,YAAY;QAC7B,IAAI,EAAE,UAAU;KACjB;IACD;QACE,MAAM,EAAE;YACN,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;YACtD,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;YACtD,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;YACtD,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;YACtD,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;SACnD;QACD,IAAI,EAAE,mBAAmB;QACzB,OAAO,EAAE,CAAC,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;QAC/D,eAAe,EAAE,YAAY;QAC7B,IAAI,EAAE,UAAU;KACjB;IACD;QACE,MAAM,EAAE;YACN,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;YACtD,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;YACtD,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;YACtD,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;SACnD;QACD,IAAI,EAAE,kBAAkB;QACxB,OAAO,EAAE,CAAC,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;QAC/D,eAAe,EAAE,YAAY;QAC7B,IAAI,EAAE,UAAU;KACjB;IACD;QACE,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;QAChE,IAAI,EAAE,mBAAmB;QACzB,OAAO,EAAE,CAAC,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;QAC3D,eAAe,EAAE,MAAM;QACvB,IAAI,EAAE,UAAU;KACjB;IACD;QACE,MAAM,EAAE;YACN;gBACE,UAAU,EAAE;oBACV,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;oBAC5D,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE;oBAC3D,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE;oBAC1D,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE;oBAC1D;wBACE,YAAY,EAAE,SAAS;wBACvB,IAAI,EAAE,kBAAkB;wBACxB,IAAI,EAAE,SAAS;qBAChB;oBACD;wBACE,YAAY,EAAE,SAAS;wBACvB,IAAI,EAAE,oBAAoB;wBAC1B,IAAI,EAAE,SAAS;qBAChB;oBACD,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;oBAC7D,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,OAAO,EAAE;oBAClE,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE;iBAC5D;gBACD,YAAY,EAAE,4BAA4B;gBAC1C,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,OAAO;aACd;YACD,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE;YAChE,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE;SAC1E;QACD,IAAI,EAAE,gBAAgB;QACtB,OAAO,EAAE;YACP,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;SACrE;QACD,eAAe,EAAE,YAAY;QAC7B,IAAI,EAAE,UAAU;KACjB;IACD,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;CACvC,CAAA"}