{"version": 3, "file": "eip5792.d.ts", "sourceRoot": "", "sources": ["../../../../experimental/eip5792/decorators/eip5792.ts"], "names": [], "mappings": "AAEA,OAAO,EACL,KAAK,wBAAwB,EAC7B,KAAK,wBAAwB,EAE9B,MAAM,2CAA2C,CAAA;AAClD,OAAO,EACL,KAAK,yBAAyB,EAC9B,KAAK,yBAAyB,EAE/B,MAAM,4CAA4C,CAAA;AACnD,OAAO,EACL,KAAK,mBAAmB,EACxB,KAAK,mBAAmB,EAEzB,MAAM,sCAAsC,CAAA;AAC7C,OAAO,EACL,KAAK,yBAAyB,EAC9B,KAAK,yBAAyB,EAE/B,MAAM,4CAA4C,CAAA;AACnD,OAAO,EACL,KAAK,4BAA4B,EACjC,KAAK,4BAA4B,EAElC,MAAM,+CAA+C,CAAA;AACtD,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,kCAAkC,CAAA;AAC9D,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,gDAAgD,CAAA;AAC/E,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,2BAA2B,CAAA;AACxD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,yBAAyB,CAAA;AACpD,OAAO,EACL,KAAK,wBAAwB,EAC7B,KAAK,wBAAwB,EAE9B,MAAM,8BAA8B,CAAA;AAErC,MAAM,MAAM,cAAc,CACxB,KAAK,SAAS,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,EACnD,OAAO,SAAS,OAAO,GAAG,SAAS,GAAG,OAAO,GAAG,SAAS,IACvD;IACF;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,cAAc,EAAE,CACd,UAAU,EAAE,wBAAwB,KACjC,OAAO,CAAC,wBAAwB,CAAC,CAAA;IACtC;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,eAAe,EAAE,CACf,UAAU,CAAC,EAAE,yBAAyB,KACnC,OAAO,CAAC,yBAAyB,CAAC,CAAA;IACvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACH,SAAS,EAAE,CACT,KAAK,CAAC,KAAK,SAAS,SAAS,OAAO,EAAE,EACtC,aAAa,SAAS,KAAK,GAAG,SAAS,GAAG,SAAS,EAEnD,UAAU,EAAE,mBAAmB,CAAC,KAAK,EAAE,OAAO,EAAE,aAAa,EAAE,KAAK,CAAC,KAClE,OAAO,CAAC,mBAAmB,CAAC,CAAA;IACjC;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,eAAe,EAAE,CACf,UAAU,EAAE,yBAAyB,KAClC,OAAO,CAAC,yBAAyB,CAAC,CAAA;IACvC;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,kBAAkB,EAAE,CAClB,UAAU,EAAE,4BAA4B,KACrC,OAAO,CAAC,4BAA4B,CAAC,CAAA;IAC1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2CG;IACH,cAAc,EAAE,CACd,KAAK,CAAC,SAAS,SAAS,SAAS,OAAO,EAAE,EAC1C,aAAa,SAAS,KAAK,GAAG,SAAS,GAAG,SAAS,EAEnD,UAAU,EAAE,wBAAwB,CAClC,SAAS,EACT,KAAK,EACL,OAAO,EACP,aAAa,CACd,KACE,OAAO,CAAC,wBAAwB,CAAC,CAAA;CACvC,CAAA;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,wBAAgB,cAAc,KAE1B,SAAS,SAAS,SAAS,EAC3B,KAAK,SAAS,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,EACnD,OAAO,SAAS,OAAO,GAAG,SAAS,GAAG,OAAO,GAAG,SAAS,EAEzD,QAAQ,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,KACxC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAYlC"}