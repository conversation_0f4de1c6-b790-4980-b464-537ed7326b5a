{"version": 3, "file": "signTypedData.d.ts", "sourceRoot": "", "sources": ["../../../../experimental/erc7739/actions/signTypedData.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,MAAM,SAAS,CAAA;AAElE,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAA;AAEzD,OAAO,EACL,KAAK,yBAAyB,EAE/B,MAAM,4CAA4C,CAAA;AAEnD,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,kCAAkC,CAAA;AAC9D,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,gDAAgD,CAAA;AAE/E,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAA;AACzD,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,2BAA2B,CAAA;AACpE,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,yBAAyB,CAAA;AACpD,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,wBAAwB,CAAA;AACjD,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,6BAA6B,CAAA;AACtE,OAAO,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAA;AAEhE,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,aAAa,CAAA;AAGvD,MAAM,MAAM,uBAAuB,CACjC,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,EACjE,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,GAAG,MAAM,SAAS,EACtE,OAAO,SAAS,OAAO,GAAG,SAAS,GAAG,SAAS,EAC/C,eAAe,SAAS,OAAO,GAAG,SAAS,GAAG,SAAS,EACvD,QAAQ,SAAS,OAAO,GAAG,SAAS,GAAG,OAAO,GAAG,SAAS,EAE1D,YAAY,GAAG,SAAS,SAAS,SAAS,GAAG,MAAM,SAAS,GAAG,MAAM,IACnE,mBAAmB,CAAC,SAAS,EAAE,WAAW,EAAE,YAAY,CAAC,GAC3D,IAAI,CAAC,yBAAyB,EAAE,SAAS,GAAG,aAAa,CAAC,GAC1D,mBAAmB,CAAC,OAAO,EAAE,eAAe,CAAC,GAC7C,KAAK,CACD;IACE,cAAc,EAAE,UAAU,CACxB,eAAe,EACf,SAAS,GAAG,MAAM,GAAG,mBAAmB,GAAG,MAAM,GAAG,SAAS,CAC9D,CAAA;IACD,QAAQ,CAAC,EAAE,SAAS,CAAA;CACrB,GACD,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG;IAChC,cAAc,CAAC,EACX,UAAU,CACR,eAAe,EACf,SAAS,GAAG,MAAM,GAAG,mBAAmB,GAAG,MAAM,GAAG,SAAS,CAC9D,GACD,SAAS,CAAA;CACd,CAAC,CACL,CAAA;AAEH,MAAM,MAAM,uBAAuB,GAAG,GAAG,CAAA;AAEzC,MAAM,MAAM,sBAAsB,GAAG,SAAS,CAAA;AAE9C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkGG;AACH,wBAAsB,aAAa,CACjC,KAAK,CAAC,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC3D,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,EACpD,KAAK,SAAS,KAAK,GAAG,SAAS,EAC/B,OAAO,SAAS,OAAO,GAAG,SAAS,EACnC,eAAe,SAAS,OAAO,GAAG,SAAS,GAAG,SAAS,EAEvD,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,EACzC,UAAU,EAAE,uBAAuB,CACjC,SAAS,EACT,WAAW,EACX,OAAO,EACP,eAAe,CAChB,GACA,OAAO,CAAC,uBAAuB,CAAC,CAoElC"}