{"version": 3, "file": "signMessage.d.ts", "sourceRoot": "", "sources": ["../../../../experimental/erc7739/actions/signMessage.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,SAAS,CAAA;AACvD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAA;AAEzD,OAAO,EACL,KAAK,yBAAyB,EAE/B,MAAM,4CAA4C,CAAA;AAEnD,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,kCAAkC,CAAA;AAC9D,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,gDAAgD,CAAA;AAE/E,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAA;AACzD,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,2BAA2B,CAAA;AACpE,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,yBAAyB,CAAA;AACpD,OAAO,KAAK,EAAE,GAAG,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAA;AAClE,OAAO,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAA;AAGhE,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,aAAa,CAAA;AAEvD,MAAM,MAAM,qBAAqB,CAC/B,OAAO,SAAS,OAAO,GAAG,SAAS,GAAG,OAAO,GAAG,SAAS,EACzD,eAAe,SAAS,OAAO,GAAG,SAAS,GAAG,OAAO,GAAG,SAAS,EACjE,QAAQ,SAAS,OAAO,GAAG,SAAS,GAAG,OAAO,GAAG,SAAS,IACxD,IAAI,CAAC,yBAAyB,EAAE,SAAS,GAAG,aAAa,CAAC,GAC5D,mBAAmB,CAAC,OAAO,EAAE,eAAe,CAAC,GAAG;IAC9C,OAAO,EAAE,eAAe,CAAA;CACzB,GAAG,KAAK,CACL;IACE,cAAc,EAAE,UAAU,CACxB,eAAe,EACf,SAAS,GAAG,MAAM,GAAG,mBAAmB,GAAG,SAAS,CACrD,CAAA;IACD,QAAQ,CAAC,EAAE,SAAS,CAAA;CACrB,GACD,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG;IAChC,cAAc,CAAC,EACX,UAAU,CACR,eAAe,EACf,SAAS,GAAG,MAAM,GAAG,mBAAmB,GAAG,SAAS,CACrD,GACD,SAAS,CAAA;CACd,CAAC,CACL,CAAA;AAEH,MAAM,MAAM,qBAAqB,GAAG,GAAG,CAAA;AAEvC,MAAM,MAAM,oBAAoB,GAAG,SAAS,CAAA;AAE5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+CG;AACH,wBAAsB,WAAW,CAC/B,KAAK,SAAS,KAAK,GAAG,SAAS,EAC/B,OAAO,SAAS,OAAO,GAAG,SAAS,EACnC,eAAe,SAAS,OAAO,GAAG,SAAS,GAAG,SAAS,EAEvD,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,EACzC,UAAU,EAAE,qBAAqB,CAAC,OAAO,EAAE,eAAe,CAAC,GAC1D,OAAO,CAAC,qBAAqB,CAAC,CA4ChC"}