{"version": 3, "file": "toCoinbaseSmartAccount.d.ts", "sourceRoot": "", "sources": ["../../../../account-abstraction/accounts/implementations/toCoinbaseSmartAccount.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAa,MAAM,SAAS,CAAA;AAEjD,OAAO,KAAK,KAAK,YAAY,MAAM,iBAAiB,CAAA;AAEpD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAA;AAI9D,OAAO,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,wBAAwB,CAAA;AACvD,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,6BAA6B,CAAA;AACtE,OAAO,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAA;AAWtE,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAA;AAIzD,OAAO,KAAK,EACV,YAAY,EACZ,0BAA0B,EAC1B,eAAe,EAChB,MAAM,aAAa,CAAA;AAEpB,MAAM,MAAM,gCAAgC,GAAG;IAC7C,OAAO,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IAC7B,MAAM,EAAE,kCAAkC,CAAC,QAAQ,CAAC,CAAA;IACpD,UAAU,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC/B,MAAM,EAAE,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC,YAAY,GAAG,eAAe,CAAC,CAAC,EAAE,CAAA;IACpE,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;CAC3B,CAAA;AAED,MAAM,MAAM,gCAAgC,GAAG,QAAQ,CACrD,YAAY,CAAC,kCAAkC,CAAC,CACjD,CAAA;AAED,MAAM,MAAM,kCAAkC,GAAG,MAAM,CACrD,0BAA0B,CACxB,OAAO,eAAe,EACtB,KAAK,EACL;IAAE,GAAG,EAAE,OAAO,GAAG,CAAC;IAAC,OAAO,EAAE;QAAE,GAAG,EAAE,OAAO,UAAU,CAAC;QAAC,OAAO,EAAE,OAAO,CAAA;KAAE,CAAA;CAAE,CAC3E,EACD;IACE,WAAW,EAAE,WAAW,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAC,CAAA;IACnE,IAAI,EAAE,WAAW,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC,CAAA;CACtD,CACF,CAAA;AAED;;;;;;;;;;;;;;;GAeG;AACH,wBAAsB,sBAAsB,CAC1C,UAAU,EAAE,gCAAgC,GAC3C,OAAO,CAAC,gCAAgC,CAAC,CAwM3C;AAMD,gBAAgB;AAChB,wBAAsB,aAAa,CAAC,EAClC,SAAS,EACT,KAAK,GACN,EAAE;IACD,SAAS,EAAE,mBAAmB,CAAA;IAC9B,KAAK,EAAE,KAAK,CAAC,YAAY,GAAG,eAAe,CAAC,CAAA;CAC7C,0BAMA;AAED,gBAAgB;AAChB,wBAAsB,IAAI,CAAC,EACzB,IAAI,EACJ,KAAK,GACN,EAAE;IAAE,IAAI,EAAE,IAAI,CAAC;IAAC,KAAK,EAAE,KAAK,CAAC,YAAY,GAAG,eAAe,CAAC,CAAA;CAAE,0BAY9D;AAED,gBAAgB;AAChB,wBAAgB,qBAAqB,CAAC,EACpC,OAAO,EACP,OAAO,EACP,IAAI,GACL,EAAE;IAAE,OAAO,EAAE,OAAO,CAAC;IAAC,OAAO,EAAE,MAAM,CAAC;IAAC,IAAI,EAAE,IAAI,CAAA;CAAE;;;;;;;;;;;;;;;;;EAqBnD;AAED,gBAAgB;AAChB,wBAAgB,mBAAmB,CAAC,EAClC,QAAQ,EACR,SAAS,GACV,EAAE;IACD,QAAQ,EAAE,YAAY,CAAC,YAAY,CAAA;IACnC,SAAS,EAAE,GAAG,CAAA;CACf,iBAoCA;AAED,gBAAgB;AAChB,wBAAgB,aAAa,CAAC,UAAU,EAAE;IACxC,UAAU,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC/B,SAAS,EAAE,GAAG,CAAA;CACf,iBAiCA;AAMD,QAAA,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8XC,CAAA;AAEV,QAAA,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8CN,CAAA"}