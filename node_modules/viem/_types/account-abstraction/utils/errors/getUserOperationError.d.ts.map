{"version": 3, "file": "getUserOperationError.d.ts", "sourceRoot": "", "sources": ["../../../../account-abstraction/utils/errors/getUserOperationError.ts"], "names": [], "mappings": "AAOA,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAA;AAMzD,OAAO,EAEL,KAAK,+BAA+B,EACrC,MAAM,+BAA+B,CAAA;AACtC,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAA;AAMjE,KAAK,sBAAsB,GAAG,SAAS,CAAA;AAEvC,MAAM,MAAM,+BAA+B,GAAG,aAAa,GAAG;IAC5D,KAAK,CAAC,EAAE,SAAS,OAAO,EAAE,GAAG,SAAS,CAAA;IACtC,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;CAC9B,CAAA;AAED,MAAM,MAAM,+BAA+B,CAAC,KAAK,GAAG,SAAS,IAAI,IAAI,CACnE,+BAA+B,EAC/B,OAAO,CACR,GAAG;IAAE,KAAK,EAAE,KAAK,GAAG,sBAAsB,CAAA;CAAE,CAAA;AAE7C,MAAM,MAAM,8BAA8B,GAAG,SAAS,CAAA;AAEtD,wBAAgB,qBAAqB,CAAC,GAAG,SAAS,SAAS,CAAC,MAAM,CAAC,EACjE,GAAG,EAAE,GAAG,EACR,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAE,EAAE,+BAA+B,GAC5D,+BAA+B,CAAC,GAAG,CAAC,CAoBtC"}