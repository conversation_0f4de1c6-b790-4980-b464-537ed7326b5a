import type { BaseError } from '../../../errors/base.js';
import type { ExactPartial } from '../../../types/utils.js';
import { type AccountNotDeployedErrorType, type ExecutionRevertedErrorType, type FailedToSendToBeneficiaryErrorType, type GasValuesOverflowErrorType, type HandleOpsOutOfGasErrorType, type InitCodeFailedErrorType, type InitCodeMustCreateSenderErrorType, type InitCodeMustReturnSenderErrorType, type InsufficientPrefundErrorType, type InternalCallOnlyErrorType, type InvalidAccountNonceErrorType, type InvalidAggregatorErrorType, type InvalidBeneficiaryErrorType, type InvalidFieldsErrorType, type InvalidPaymasterAndDataErrorType, type PaymasterDepositTooLowErrorType, type PaymasterFunctionRevertedErrorType, type PaymasterNotDeployedErrorType, type PaymasterPostOpFunctionRevertedErrorType, type PaymasterRateLimitErrorType, type PaymasterStakeTooLowErrorType, type SenderAlreadyConstructedErrorType, type SignatureCheckFailedErrorType, type SmartAccountFunctionRevertedErrorType, type UnknownBundlerErrorType, type UnsupportedSignatureAggregatorErrorType, type UserOperationExpiredErrorType, type UserOperationOutOfTimeRangeErrorType, type UserOperationPaymasterExpiredErrorType, type UserOperationPaymasterSignatureErrorType, type UserOperationRejectedByEntryPointErrorType, type UserOperationRejectedByOpCodeErrorType, type UserOperationRejectedByPaymasterErrorType, type UserOperationSignatureErrorType, type VerificationGasLimitExceededErrorType, type VerificationGasLimitTooLowErrorType } from '../../errors/bundler.js';
import type { UserOperation } from '../../types/userOperation.js';
export type GetBundlerErrorParameters = ExactPartial<UserOperation>;
export type GetBundlerErrorReturnType = AccountNotDeployedErrorType | ExecutionRevertedErrorType | FailedToSendToBeneficiaryErrorType | GasValuesOverflowErrorType | HandleOpsOutOfGasErrorType | InitCodeFailedErrorType | InitCodeMustCreateSenderErrorType | InitCodeMustReturnSenderErrorType | InsufficientPrefundErrorType | InternalCallOnlyErrorType | InvalidAccountNonceErrorType | InvalidAggregatorErrorType | InvalidBeneficiaryErrorType | InvalidFieldsErrorType | InvalidPaymasterAndDataErrorType | PaymasterDepositTooLowErrorType | PaymasterFunctionRevertedErrorType | PaymasterNotDeployedErrorType | PaymasterPostOpFunctionRevertedErrorType | PaymasterRateLimitErrorType | PaymasterStakeTooLowErrorType | SignatureCheckFailedErrorType | SenderAlreadyConstructedErrorType | SmartAccountFunctionRevertedErrorType | UnsupportedSignatureAggregatorErrorType | UserOperationOutOfTimeRangeErrorType | UserOperationRejectedByEntryPointErrorType | UserOperationRejectedByOpCodeErrorType | UserOperationRejectedByPaymasterErrorType | UnknownBundlerErrorType | UserOperationExpiredErrorType | UserOperationPaymasterExpiredErrorType | UserOperationPaymasterSignatureErrorType | UserOperationSignatureErrorType | VerificationGasLimitExceededErrorType | VerificationGasLimitTooLowErrorType;
export declare function getBundlerError(err: BaseError, args: GetBundlerErrorParameters): GetBundlerErrorReturnType;
//# sourceMappingURL=getBundlerError.d.ts.map