import type { ErrorType } from '../../../errors/utils.js';
import type { RpcUserOperationReceipt } from '../../types/rpc.js';
import type { UserOperationReceipt } from '../../types/userOperation.js';
export type FormatUserOperationReceiptErrorType = ErrorType;
export declare function formatUserOperationReceipt(parameters: RpcUserOperationReceipt): UserOperationReceipt;
//# sourceMappingURL=userOperationReceipt.d.ts.map