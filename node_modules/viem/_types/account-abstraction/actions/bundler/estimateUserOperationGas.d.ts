import type { <PERSON><PERSON>, Narrow } from 'abitype';
import { type ParseAccountErrorType } from '../../../accounts/utils/parseAccount.js';
import type { Client } from '../../../clients/createClient.js';
import type { Transport } from '../../../clients/transports/createTransport.js';
import type { ErrorType } from '../../../errors/utils.js';
import type { Calls } from '../../../types/calls.js';
import type { Chain } from '../../../types/chain.js';
import type { Hex } from '../../../types/misc.js';
import type { StateOverride } from '../../../types/stateOverride.js';
import type { Assign, MaybeRequired, OneOf, Prettify } from '../../../types/utils.js';
import type { RequestErrorType } from '../../../utils/buildRequest.js';
import type { SmartAccount } from '../../accounts/types.js';
import type { PaymasterActions } from '../../clients/decorators/paymaster.js';
import type { DeriveSmartAccount, GetSmartAccountParameter } from '../../types/account.js';
import type { DeriveEntryPointVersion, EntryPointVersion } from '../../types/entryPointVersion.js';
import type { EstimateUserOperationGasReturnType as EstimateUserOperationGasReturnType_, UserOperation, UserOperationRequest } from '../../types/userOperation.js';
import { type FormatUserOperationGasErrorType } from '../../utils/formatters/userOperationGas.js';
import { type FormatUserOperationRequestErrorType } from '../../utils/formatters/userOperationRequest.js';
import { type PrepareUserOperationErrorType } from './prepareUserOperation.js';
export type EstimateUserOperationGasParameters<account extends SmartAccount | undefined = SmartAccount | undefined, accountOverride extends SmartAccount | undefined = SmartAccount | undefined, calls extends readonly unknown[] = readonly unknown[], _derivedAccount extends SmartAccount | undefined = DeriveSmartAccount<account, accountOverride>, _derivedVersion extends EntryPointVersion = DeriveEntryPointVersion<_derivedAccount>> = GetSmartAccountParameter<account, accountOverride, false> & (UserOperation | Assign<UserOperationRequest<_derivedVersion>, OneOf<{
    calls: Calls<Narrow<calls>>;
} | {
    callData: Hex;
}> & {
    paymaster?: Address | true | {
        /** Retrieves paymaster-related User Operation properties to be used for sending the User Operation. */
        getPaymasterData?: PaymasterActions['getPaymasterData'] | undefined;
        /** Retrieves paymaster-related User Operation properties to be used for gas estimation. */
        getPaymasterStubData?: PaymasterActions['getPaymasterStubData'] | undefined;
    } | undefined;
    /** Paymaster context to pass to `getPaymasterData` and `getPaymasterStubData` calls. */
    paymasterContext?: unknown | undefined;
}>) & MaybeRequired<{
    entryPointAddress?: Address;
}, _derivedAccount extends undefined ? true : false> & {
    /** State overrides for the User Operation call. */
    stateOverride?: StateOverride | undefined;
};
export type EstimateUserOperationGasReturnType<account extends SmartAccount | undefined = SmartAccount | undefined, accountOverride extends SmartAccount | undefined = SmartAccount | undefined, _derivedAccount extends SmartAccount | undefined = DeriveSmartAccount<account, accountOverride>, _derivedVersion extends EntryPointVersion = DeriveEntryPointVersion<_derivedAccount>> = Prettify<EstimateUserOperationGasReturnType_<_derivedVersion>>;
export type EstimateUserOperationGasErrorType = ParseAccountErrorType | PrepareUserOperationErrorType | FormatUserOperationRequestErrorType | FormatUserOperationGasErrorType | RequestErrorType | ErrorType;
/**
 * Returns an estimate of gas values necessary to execute the User Operation.
 *
 * - Docs: https://viem.sh/actions/bundler/estimateUserOperationGas
 *
 * @param client - Client to use
 * @param parameters - {@link EstimateUserOperationGasParameters}
 * @returns The gas estimate (in wei). {@link EstimateUserOperationGasReturnType}
 *
 * @example
 * import { createBundlerClient, http, parseEther } from 'viem'
 * import { toSmartAccount } from 'viem/accounts'
 * import { mainnet } from 'viem/chains'
 * import { estimateUserOperationGas } from 'viem/actions'
 *
 * const account = await toSmartAccount({ ... })
 *
 * const bundlerClient = createBundlerClient({
 *   chain: mainnet,
 *   transport: http(),
 * })
 *
 * const values = await estimateUserOperationGas(bundlerClient, {
 *   account,
 *   calls: [{ to: '0x...', value: parseEther('1') }],
 * })
 */
export declare function estimateUserOperationGas<const calls extends readonly unknown[], account extends SmartAccount | undefined, accountOverride extends SmartAccount | undefined = undefined>(client: Client<Transport, Chain | undefined, account>, parameters: EstimateUserOperationGasParameters<account, accountOverride, calls>): Promise<EstimateUserOperationGasReturnType<account, accountOverride>>;
//# sourceMappingURL=estimateUserOperationGas.d.ts.map