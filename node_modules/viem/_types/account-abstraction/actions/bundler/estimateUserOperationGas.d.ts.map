{"version": 3, "file": "estimateUserOperationGas.d.ts", "sourceRoot": "", "sources": ["../../../../account-abstraction/actions/bundler/estimateUserOperationGas.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,SAAS,CAAA;AAC9C,OAAO,EACL,KAAK,qBAAqB,EAE3B,MAAM,yCAAyC,CAAA;AAChD,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,kCAAkC,CAAA;AAC9D,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,gDAAgD,CAAA;AAG/E,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAA;AACzD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,yBAAyB,CAAA;AACpD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,yBAAyB,CAAA;AACpD,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,wBAAwB,CAAA;AACjD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,iCAAiC,CAAA;AACpE,OAAO,KAAK,EACV,MAAM,EACN,aAAa,EACb,KAAK,EACL,QAAQ,EACT,MAAM,yBAAyB,CAAA;AAChC,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAA;AAGtE,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAA;AAC3D,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,uCAAuC,CAAA;AAC7E,OAAO,KAAK,EACV,kBAAkB,EAClB,wBAAwB,EACzB,MAAM,wBAAwB,CAAA;AAC/B,OAAO,KAAK,EACV,uBAAuB,EACvB,iBAAiB,EAClB,MAAM,kCAAkC,CAAA;AACzC,OAAO,KAAK,EACV,kCAAkC,IAAI,mCAAmC,EACzE,aAAa,EACb,oBAAoB,EACrB,MAAM,8BAA8B,CAAA;AAErC,OAAO,EACL,KAAK,+BAA+B,EAErC,MAAM,4CAA4C,CAAA;AACnD,OAAO,EACL,KAAK,mCAAmC,EAEzC,MAAM,gDAAgD,CAAA;AACvD,OAAO,EACL,KAAK,6BAA6B,EAGnC,MAAM,2BAA2B,CAAA;AAElC,MAAM,MAAM,kCAAkC,CAC5C,OAAO,SAAS,YAAY,GAAG,SAAS,GAAG,YAAY,GAAG,SAAS,EACnE,eAAe,SAAS,YAAY,GAAG,SAAS,GAAG,YAAY,GAAG,SAAS,EAC3E,KAAK,SAAS,SAAS,OAAO,EAAE,GAAG,SAAS,OAAO,EAAE,EAErD,eAAe,SAAS,YAAY,GAAG,SAAS,GAAG,kBAAkB,CACnE,OAAO,EACP,eAAe,CAChB,EACD,eAAe,SACb,iBAAiB,GAAG,uBAAuB,CAAC,eAAe,CAAC,IAC5D,wBAAwB,CAAC,OAAO,EAAE,eAAe,EAAE,KAAK,CAAC,GAC3D,CACI,aAAa,GACb,MAAM,CAEJ,oBAAoB,CAAC,eAAe,CAAC,EACrC,KAAK,CAAC;IAAE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;CAAE,GAAG;IAAE,QAAQ,EAAE,GAAG,CAAA;CAAE,CAAC,GAAG;IAC3D,SAAS,CAAC,EACN,OAAO,GACP,IAAI,GACJ;QACE,uGAAuG;QACvG,gBAAgB,CAAC,EACb,gBAAgB,CAAC,kBAAkB,CAAC,GACpC,SAAS,CAAA;QACb,2FAA2F;QAC3F,oBAAoB,CAAC,EACjB,gBAAgB,CAAC,sBAAsB,CAAC,GACxC,SAAS,CAAA;KACd,GACD,SAAS,CAAA;IACb,wFAAwF;IACxF,gBAAgB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CACvC,CACF,CACJ,GAED,aAAa,CACX;IAAE,iBAAiB,CAAC,EAAE,OAAO,CAAA;CAAE,EAC/B,eAAe,SAAS,SAAS,GAAG,IAAI,GAAG,KAAK,CACjD,GAAG;IACF,mDAAmD;IACnD,aAAa,CAAC,EAAE,aAAa,GAAG,SAAS,CAAA;CAC1C,CAAA;AAEH,MAAM,MAAM,kCAAkC,CAC5C,OAAO,SAAS,YAAY,GAAG,SAAS,GAAG,YAAY,GAAG,SAAS,EACnE,eAAe,SAAS,YAAY,GAAG,SAAS,GAAG,YAAY,GAAG,SAAS,EAE3E,eAAe,SAAS,YAAY,GAAG,SAAS,GAAG,kBAAkB,CACnE,OAAO,EACP,eAAe,CAChB,EACD,eAAe,SACb,iBAAiB,GAAG,uBAAuB,CAAC,eAAe,CAAC,IAC5D,QAAQ,CAAC,mCAAmC,CAAC,eAAe,CAAC,CAAC,CAAA;AAElE,MAAM,MAAM,iCAAiC,GACzC,qBAAqB,GACrB,6BAA6B,GAC7B,mCAAmC,GACnC,+BAA+B,GAC/B,gBAAgB,GAChB,SAAS,CAAA;AAEb;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,wBAAsB,wBAAwB,CAC5C,KAAK,CAAC,KAAK,SAAS,SAAS,OAAO,EAAE,EACtC,OAAO,SAAS,YAAY,GAAG,SAAS,EACxC,eAAe,SAAS,YAAY,GAAG,SAAS,GAAG,SAAS,EAE5D,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK,GAAG,SAAS,EAAE,OAAO,CAAC,EACrD,UAAU,EAAE,kCAAkC,CAC5C,OAAO,EACP,eAAe,EACf,KAAK,CACN,GACA,OAAO,CAAC,kCAAkC,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC,CAkDvE"}