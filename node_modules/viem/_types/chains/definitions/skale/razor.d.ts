export declare const skaleRazor: {
    blockExplorers: {
        readonly default: {
            readonly name: "SKALE Explorer";
            readonly url: "https://turbulent-unique-scheat.explorer.mainnet.skalenodes.com";
        };
    };
    contracts: {};
    ensTlds?: readonly string[] | undefined;
    id: 278611351;
    name: "SKALE | Razor Network";
    nativeCurrency: {
        readonly name: "sFUEL";
        readonly symbol: "sFUEL";
        readonly decimals: 18;
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://mainnet.skalenodes.com/v1/turbulent-unique-scheat"];
            readonly webSocket: readonly ["wss://mainnet.skalenodes.com/v1/ws/turbulent-unique-scheat"];
        };
    };
    sourceId?: number | undefined | undefined;
    testnet?: boolean | undefined | undefined;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../../index.js").ChainSerializers<undefined, import("../../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=razor.d.ts.map