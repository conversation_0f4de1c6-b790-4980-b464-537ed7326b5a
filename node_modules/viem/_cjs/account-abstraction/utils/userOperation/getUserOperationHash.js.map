{"version": 3, "file": "getUserOperationHash.js", "sourceRoot": "", "sources": ["../../../../account-abstraction/utils/userOperation/getUserOperationHash.ts"], "names": [], "mappings": ";;AAuBA,oDAqGC;AAzHD,sFAA+E;AAC/E,mEAA4D;AAC5D,gFAAyE;AAGzE,qDAA8C;AAC9C,iFAA0E;AAC1E,yEAAkE;AAalE,SAAgB,oBAAoB,CAGlC,UAA6D;IAE7D,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,GAAG,UAAU,CAAA;IACpE,MAAM,aAAa,GAAG,UAAU,CAAC,aAA8B,CAAA;IAC/D,MAAM,EACJ,aAAa,EACb,QAAQ,GAAG,IAAI,EACf,YAAY,EACZ,YAAY,EACZ,oBAAoB,EACpB,KAAK,EACL,gBAAgB,GAAG,IAAI,EACvB,kBAAkB,EAClB,MAAM,EACN,oBAAoB,GACrB,GAAG,aAAa,CAAA;IAEjB,IAAI,iBAAiB,KAAK,KAAK;QAC7B,OAAO,IAAA,gCAAa,EAClB,IAAA,wDAAyB,EAAC;YACxB,OAAO;YACP,iBAAiB;YACjB,aAAa;SACd,CAAC,CACH,CAAA;IAEH,MAAM,YAAY,GAAG,CAAC,GAAG,EAAE;QACzB,IAAI,iBAAiB,KAAK,KAAK,EAAE,CAAC;YAChC,MAAM,OAAO,GAAG,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAQ,CAAA;YAC3D,MAAM,WAAW,GAAG,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAoB,CAAA;YACxE,MAAM,QAAQ,GAAG,IAAA,4BAAW,EAAC;gBAC3B,aAAa;gBACb,OAAO;gBACP,WAAW;aACZ,CAAC,CAAA;YACF,OAAO,IAAA,4CAAmB,EACxB;gBACE,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;aACpB,EACD;gBACE,MAAM;gBACN,KAAK;gBACL,IAAA,wBAAS,EAAC,QAAQ,CAAC;gBACnB,IAAA,wBAAS,EAAC,QAAQ,CAAC;gBACnB,YAAY;gBACZ,oBAAoB;gBACpB,kBAAkB;gBAClB,YAAY;gBACZ,oBAAoB;gBACpB,IAAA,wBAAS,EAAC,gBAAgB,CAAC;aAC5B,CACF,CAAA;QACH,CAAC;QAED,IAAI,iBAAiB,KAAK,KAAK,EAAE,CAAC;YAChC,MAAM,YAAY,GAAG,IAAA,gDAAqB,EAAC,aAAa,CAAC,CAAA;YACzD,OAAO,IAAA,4CAAmB,EACxB;gBACE,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;aACpB,EACD;gBACE,YAAY,CAAC,MAAM;gBACnB,YAAY,CAAC,KAAK;gBAClB,IAAA,wBAAS,EAAC,YAAY,CAAC,QAAQ,CAAC;gBAChC,IAAA,wBAAS,EAAC,YAAY,CAAC,QAAQ,CAAC;gBAChC,YAAY,CAAC,gBAAgB;gBAC7B,YAAY,CAAC,kBAAkB;gBAC/B,YAAY,CAAC,OAAO;gBACpB,IAAA,wBAAS,EAAC,YAAY,CAAC,gBAAgB,CAAC;aACzC,CACF,CAAA;QACH,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,sBAAsB,iBAAiB,kBAAkB,CAAC,CAAA;IAC5E,CAAC,CAAC,EAAE,CAAA;IAEJ,OAAO,IAAA,wBAAS,EACd,IAAA,4CAAmB,EACjB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAC/D,CAAC,IAAA,wBAAS,EAAC,YAAY,CAAC,EAAE,iBAAiB,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAC9D,CACF,CAAA;AACH,CAAC"}