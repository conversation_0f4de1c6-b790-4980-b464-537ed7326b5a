{"version": 3, "file": "toPackedUserOperation.js", "sourceRoot": "", "sources": ["../../../../account-abstraction/utils/userOperation/toPackedUserOperation.ts"], "names": [], "mappings": ";;AASA,sDAoDC;AA7DD,6DAAsD;AACtD,uDAAgD;AAChD,sDAAqD;AAKrD,qDAA8C;AAE9C,SAAgB,qBAAqB,CACnC,aAA4B;IAE5B,MAAM,EACJ,YAAY,EACZ,QAAQ,EACR,oBAAoB,EACpB,YAAY,EACZ,SAAS,EACT,aAAa,EACb,uBAAuB,EACvB,6BAA6B,EAC7B,MAAM,EACN,SAAS,GAAG,IAAI,EAChB,oBAAoB,GACrB,GAAG,aAAa,CAAA;IAEjB,MAAM,gBAAgB,GAAG,IAAA,kBAAM,EAAC;QAC9B,IAAA,YAAG,EAAC,IAAA,sBAAW,EAAC,oBAAoB,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;QAC1D,IAAA,YAAG,EAAC,IAAA,sBAAW,EAAC,YAAY,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;KACnD,CAAC,CAAA;IACF,MAAM,QAAQ,GAAG,IAAA,4BAAW,EAAC,aAAa,CAAC,CAAA;IAC3C,MAAM,OAAO,GAAG,IAAA,kBAAM,EAAC;QACrB,IAAA,YAAG,EAAC,IAAA,sBAAW,EAAC,oBAAoB,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;QAC1D,IAAA,YAAG,EAAC,IAAA,sBAAW,EAAC,YAAY,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;KACnD,CAAC,CAAA;IACF,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,IAAI,EAAE,CAAA;IACvC,MAAM,gBAAgB,GAAG,SAAS;QAChC,CAAC,CAAC,IAAA,kBAAM,EAAC;YACL,SAAS;YACT,IAAA,YAAG,EAAC,IAAA,sBAAW,EAAC,6BAA6B,IAAI,EAAE,CAAC,EAAE;gBACpD,IAAI,EAAE,EAAE;aACT,CAAC;YACF,IAAA,YAAG,EAAC,IAAA,sBAAW,EAAC,uBAAuB,IAAI,EAAE,CAAC,EAAE;gBAC9C,IAAI,EAAE,EAAE;aACT,CAAC;YACF,aAAa,IAAI,IAAI;SACtB,CAAC;QACJ,CAAC,CAAC,IAAI,CAAA;IACR,MAAM,kBAAkB,GAAG,aAAa,CAAC,kBAAkB,IAAI,EAAE,CAAA;IAEjE,OAAO;QACL,gBAAgB;QAChB,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,KAAK;QACL,gBAAgB;QAChB,kBAAkB;QAClB,MAAM;QACN,SAAS;KACV,CAAA;AACH,CAAC"}