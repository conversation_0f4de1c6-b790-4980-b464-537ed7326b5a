{"version": 3, "file": "connect.js", "sourceRoot": "", "sources": ["../../../../experimental/erc7846/actions/connect.ts"], "names": [], "mappings": ";;AAgDA,0BA6CC;AA5FD,qFAGoD;AAQpD,+DAA8D;AAoCvD,KAAK,UAAU,OAAO,CAC3B,MAAgC,EAChC,aAAgC,EAAE;IAElC,MAAM,YAAY,GAAG,yBAAyB,CAAC,UAAU,CAAC,YAAY,CAAC,CAAA;IAEvE,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,IAAI,EAAE;QACjC,IAAI,CAAC;YACH,OAAO,MAAM,MAAM,CAAC,OAAO,CACzB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE,EACtE,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,CAChC,CAAA;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,KAAK,GAAG,CAAc,CAAA;YAI5B,IACE,CAAC,UAAU,CAAC,YAAY;gBACxB,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAsB;oBACpC,KAAK,CAAC,IAAI,KAAK,uBAAuB;oBACtC,KAAK,CAAC,IAAI,KAAK,wBAAwB;oBACvC,KAAK,CAAC,IAAI,KAAK,4BAA4B;oBAC3C,KAAK,CAAC,IAAI,KAAK,gCAAgC,CAAC,EAClD,CAAC;gBACD,MAAM,SAAS,GAAG,MAAM,IAAA,sCAAgB,EAAC,MAAM,CAAC,CAAA;gBAChD,OAAO;oBACL,QAAQ,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;wBACpC,OAAO;wBACP,YAAY,EAAE,EAAE;qBACjB,CAAC,CAAC;iBACJ,CAAA;YACH,CAAC;YAED,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC,CAAC,EAAE,CAAA;IAEJ,OAAO;QACL,GAAG,QAAQ;QACX,QAAQ,EAAE,CAAC,QAAQ,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YACpD,GAAG,OAAO;YACV,YAAY,EAAE,0BAA0B,CAAC,OAAO,CAAC,YAAY,CAAC;SAC/D,CAAC,CAAC;KACJ,CAAA;AACH,CAAC;AAED,SAAS,yBAAyB,CAChC,YAAmE;IAEnE,MAAM,EACJ,sBAAsB,EACtB,uBAAuB,EAAE,cAAc,EACvC,2BAA2B,EAC3B,GAAG,IAAI,EACR,GAAG,YAAY,IAAI,EAAE,CAAA;IAEtB,MAAM,aAAa,GAAG,sBAAsB;QAC1C,CAAC,CAAC;YACE,GAAG,sBAAsB;YACzB,OAAO,EAAE;gBACP,GAAG,sBAAsB,CAAC,OAAO;gBACjC,GAAG,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO;oBACxC,CAAC,CAAC;wBACE,OAAO,EAAE,IAAA,sBAAW,EAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC;qBAC7D;oBACH,CAAC,CAAC,EAAE,CAAC;aACR;SACF;QACH,CAAC,CAAC,SAAS,CAAA;IAEb,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,GACpD,2BAA2B,IAAI,EAAE,CAAA;IACnC,MAAM,kBAAkB,GAAG,2BAA2B;QACpD,CAAC,CAAC;YACE,GAAG,2BAA2B;YAC9B,OAAO,EAAE,IAAA,sBAAW,EAAC,OAAQ,CAAC;YAC9B,GAAG,CAAC,cAAc;gBAChB,CAAC,CAAC;oBACE,cAAc,EAAE,cAAc,CAAC,WAAW,EAAE;iBAC7C;gBACH,CAAC,CAAC,EAAE,CAAC;YACP,GAAG,CAAC,QAAQ;gBACV,CAAC,CAAC;oBACE,QAAQ,EAAE,QAAQ,CAAC,WAAW,EAAE;iBACjC;gBACH,CAAC,CAAC,EAAE,CAAC;YACP,GAAG,CAAC,SAAS;gBACX,CAAC,CAAC;oBACE,SAAS,EAAE,SAAS,CAAC,WAAW,EAAE;iBACnC;gBACH,CAAC,CAAC,EAAE,CAAC;SACR;QACH,CAAC,CAAC,SAAS,CAAA;IAEb,OAAO;QACL,GAAG,IAAI;QACP,GAAG,CAAC,aAAa;YACf,CAAC,CAAC;gBACE,aAAa;aACd;YACH,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,cAAc;YAChB,CAAC,CAAC;gBACE,cAAc;aACf;YACH,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,kBAAkB;YACpB,CAAC,CAAC;gBACE,kBAAkB;aACnB;YACH,CAAC,CAAC,EAAE,CAAC;KACR,CAAA;AACH,CAAC;AAED,SAAS,0BAA0B,CACjC,YAAsE;IAEtE,OAAO,MAAM,CAAC,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,MAAM,CAC9C,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QAC7B,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE;YACd,IAAI,GAAG,KAAK,oBAAoB;gBAAE,OAAO,6BAA6B,CAAA;YACtE,IAAI,GAAG,KAAK,aAAa;gBAAE,OAAO,sBAAsB,CAAA;YACxD,OAAO,GAAG,CAAA;QACZ,CAAC,CAAC,EAAE,CAAA;QACJ,YAAY,CAAC,CAAC,CAAC,GAAG,KAAK,CAAA;QACvB,OAAO,YAAY,CAAA;IACrB,CAAC,EACD,EAA6B,CAC9B,CAAA;AACH,CAAC"}