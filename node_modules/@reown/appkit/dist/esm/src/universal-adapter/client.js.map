{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../../../src/universal-adapter/client.ts"], "names": [], "mappings": "AACA,OAAO,IAAI,MAAM,MAAM,CAAA;AACvB,OAAO,EAAE,KAAK,EAAE,MAAM,MAAM,CAAA;AAE5B,OAAO,EAAuB,aAAa,EAAE,MAAM,sBAAsB,CAAA;AACzE,OAAO,EACL,iBAAiB,EACjB,eAAe,EACf,aAAa,IAAI,iBAAiB,EAClC,cAAc,EACf,MAAM,2BAA2B,CAAA;AAElC,OAAO,EAAE,gBAAgB,EAAE,MAAM,sCAAsC,CAAA;AACvE,OAAO,EAAE,sBAAsB,EAAE,MAAM,yCAAyC,CAAA;AAChF,OAAO,EAAE,eAAe,EAAE,MAAM,2BAA2B,CAAA;AAE3D,MAAM,OAAO,gBAAiB,SAAQ,gBAAgB;IACpC,oBAAoB,CAAC,iBAAoC;QACvE,IAAI,CAAC,YAAY,CACf,IAAI,sBAAsB,CAAC;YACzB,QAAQ,EAAE,iBAAiB;YAC3B,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;YACpC,SAAS,EAAE,IAAI,CAAC,SAA2B;SAC5C,CAAC,CACH,CAAA;IACH,CAAC;IAEM,KAAK,CAAC,OAAO,CAClB,MAAsC;QAEtC,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,EAAE,EAAE,gBAAgB;YACpB,IAAI,EAAE,gBAAyB;YAC/B,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;YAC/B,QAAQ,EAAE,IAAI,CAAC,QAA6B;YAC5C,OAAO,EAAE,EAAE;SACZ,CAAC,CAAA;IACJ,CAAC;IAEM,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAA;YAClD,MAAM,SAAS,CAAC,UAAU,EAAE,CAAA;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAA;QAC5D,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,EACvB,SAAS,EAGV;QACC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAA6B,CAAA;QACnD,MAAM,SAAS,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,SAAS,CAAC,EAAE,QAAQ;YACrE,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE;YACd,MAAM,CAAC,EAAE,AAAD,EAAG,OAAO,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAExC,OAAO,OAAO,CAAA;QAChB,CAAC,CAAC;aACD,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,KAAK,CAAC,IAAI,EAAE,CAAa,CAAA;QAEvF,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,QAAQ,EAAE,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAChC,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAC7F;SACF,CAAC,CAAA;IACJ,CAAC;IAEQ,KAAK,CAAC,cAAc;QAC3B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC1B,CAAC;IAEM,KAAK,CAAC,UAAU,CACrB,MAAyC;QAEzC,MAAM,kBAAkB,GACtB,MAAM,CAAC,WAAW;YAClB,iBAAiB,CAAC,wBAAwB,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,CAAA;QACzF,IAAI,CAAC,kBAAkB,IAAI,MAAM,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC;YACvD,OAAO;gBACL,OAAO,EAAE,MAAM;gBACf,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,MAAM,IAAI,EAAE;aACxD,CAAA;QACH,CAAC;QAED,IACE,iBAAiB,CAAC,KAAK,CAAC,cAAc;YACtC,MAAM,CAAC,OAAO,KAAK,eAAe,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,EAC9D,CAAC;YACD,OAAO;gBACL,OAAO,EAAE,iBAAiB,CAAC,KAAK,CAAC,OAAO,IAAI,MAAM;gBAClD,MAAM,EAAE,iBAAiB,CAAC,KAAK,CAAC,aAAa,IAAI,EAAE;aACpD,CAAA;QACH,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,iBAAiB,CAAC,iBAAiB,EAAE,CAAA;QAC5D,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAC3B,CAAC,CAAC,EAAE,CACF,CAAC,CAAC,OAAO,KAAK,GAAG,MAAM,CAAC,WAAW,EAAE,cAAc,IAAI,MAAM,CAAC,OAAO,EAAE;YACvE,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,MAAM,CACzD,CAAA;QAED,OAAO;YACL,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,IAAI,MAAM;YAC5C,MAAM,EAAE,OAAO,EAAE,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,MAAM,IAAI,EAAE;SAC3E,CAAA;IACH,CAAC;IAEe,KAAK,CAAC,WAAW,CAC/B,MAA0C;QAE1C,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA;QAC7C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAA;QACzE,CAAC;QAED,IAAI,SAAS,GAAG,EAAE,CAAA;QAElB,IAAI,eAAe,CAAC,KAAK,CAAC,iBAAiB,EAAE,cAAc,KAAK,aAAa,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAC3F,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,OAAO,CACrC;gBACE,MAAM,EAAE,oBAAoB;gBAC5B,MAAM,EAAE;oBACN,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBACvD,MAAM,EAAE,OAAO;iBAChB;aACF,EACD,eAAe,CAAC,KAAK,CAAC,iBAAiB,EAAE,aAAa,CACvD,CAAA;YAED,SAAS,GAAI,QAAkC,CAAC,SAAS,CAAA;QAC3D,CAAC;aAAM,CAAC;YACN,SAAS,GAAG,MAAM,QAAQ,CAAC,OAAO,CAChC;gBACE,MAAM,EAAE,eAAe;gBACvB,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;aAC3B,EACD,eAAe,CAAC,KAAK,CAAC,iBAAiB,EAAE,aAAa,CACvD,CAAA;QACH,CAAC;QAED,OAAO,EAAE,SAAS,EAAE,CAAA;IACtB,CAAC;IAED,6EAA6E;IAC7E;;;;OAIG;IACa,KAAK,CAAC,WAAW;QAC/B,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;SACf,CAAC,CAAA;IACJ,CAAC;IAEM,KAAK,CAAC,eAAe;QAC1B,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,IAAI,EAAE,EAAE;SACT,CAAC,CAAA;IACJ,CAAC;IAEe,eAAe,CAC7B,OAA+C;QAE/C,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC5B,CAAC;IACM,KAAK,CAAC,aAAa;QACxB,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,IAAI,EAAE,EAAE;SACT,CAAC,CAAA;IACJ,CAAC;IAEM,UAAU;QACf,OAAO,EAAE,CAAA;IACX,CAAC;IAEM,WAAW;QAChB,OAAO,GAAG,CAAA;IACZ,CAAC;IAEM,KAAK,CAAC,eAAe;QAC1B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC5B,CAAC;IAEM,KAAK,CAAC,gBAAgB;QAC3B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC5B,CAAC;IAEM,KAAK,CAAC,iBAAiB;QAC5B,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;IAC9B,CAAC;IAEM,KAAK,CAAC,cAAc;QACzB,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,EAAE,EAAE,gBAAgB;YACpB,IAAI,EAAE,gBAAyB;YAC/B,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE,IAAI,CAAC,QAA6B;YAC5C,OAAO,EAAE,EAAE;SACZ,CAAC,CAAA;IACJ,CAAC;IAED,4DAA4D;IAC5C,KAAK,CAAC,aAAa,CAAC,MAA4C;QAC9E,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,CAAA;QAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAA;QAElD,IAAI,WAAW,CAAC,cAAc,KAAK,aAAa,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;YAC3D,IAAI,CAAC;gBACH,MAAM,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC;oBAChC,MAAM,EAAE,4BAA4B;oBACpC,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;iBAC7C,CAAC,CAAA;gBACF,8DAA8D;YAChE,CAAC;YAAC,OAAO,WAAgB,EAAE,CAAC;gBAC1B,IACE,WAAW,CAAC,IAAI,KAAK,eAAe,CAAC,gCAAgC;oBACrE,WAAW,CAAC,IAAI,KAAK,eAAe,CAAC,sBAAsB;oBAC3D,WAAW,CAAC,IAAI,KAAK,eAAe,CAAC,kBAAkB;oBACvD,WAAW,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI;wBACpC,eAAe,CAAC,gCAAgC,EAClD,CAAC;oBACD,IAAI,CAAC;wBACH,MAAM,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC;4BAChC,MAAM,EAAE,yBAAyB;4BACjC,MAAM,EAAE;gCACN;oCACE,OAAO,EAAE,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC;oCAC9B,OAAO,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,cAAc,CAAC,EAAE,IAAI,CAAC;oCACrD,SAAS,EAAE,WAAW,CAAC,IAAI;oCAC3B,cAAc,EAAE,WAAW,CAAC,cAAc;oCAC1C,iBAAiB,EAAE,CAAC,WAAW,CAAC,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC;iCAC7D;6BACF;yBACF,CAAC,CAAA;oBACJ,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAA;oBAC3C,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QACD,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC,aAAa,CAAC,CAAA;IAC/D,CAAC;IAEM,wBAAwB;QAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAA;QAExE,MAAM,QAAQ,GAAG,SAAS,EAAE,QAA6B,CAAA;QAEzD,OAAO,QAAQ,CAAA;IACjB,CAAC;CACF"}