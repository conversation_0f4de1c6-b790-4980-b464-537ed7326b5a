{"version": 3, "file": "WalletConnectConnector.js", "sourceRoot": "", "sources": ["../../../../src/connectors/WalletConnectConnector.ts"], "names": [], "mappings": "AACA,OAAO,iBAAiB,MAAM,mCAAmC,CAAA;AAEjE,OAAO,EAAyC,aAAa,EAAE,MAAM,sBAAsB,CAAA;AAC3F,OAAO,EAAE,eAAe,EAAE,iBAAiB,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAA;AACxF,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AAGjD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAA;AAEjD,MAAM,OAAO,sBAAsB;IAcjC,YAAY,EAAE,QAAQ,EAAE,SAAS,EAA6C;QAX9D,OAAE,GAAG,aAAa,CAAC,YAAY,CAAC,cAAc,CAAA;QAC9C,SAAI,GAAG,WAAW,CAAC,iBAAiB,CAClD,aAAa,CAAC,YAAY,CAAC,cAAc,CAChC,CAAA;QACK,SAAI,GAAG,gBAAgB,CAAA;QACvB,YAAO,GAAG,WAAW,CAAC,iBAAiB,CAAC,aAAa,CAAC,YAAY,CAAC,cAAc,CAAC,CAAA;QAI1F,oBAAe,GAAG,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;QAG7E,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAA;QAC1C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,KAAK,GAAG,SAAS,CAAA;IACxB,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,eAAe,EAAE,CAAA;IAC/B,CAAC;IAED,KAAK,CAAC,oBAAoB;QACxB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAA;QAEjD,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAA;YAC3C,MAAM,+BAA+B,GACnC,iBAAiB,CAAC,KAAK,CAAC,+BAA+B,CAAA;YACzD,MAAM,UAAU,GAAG,aAAa,CAAC,gBAAgB,CAC/C,YAAY,EACZ,+BAA+B,CAChC,CAAA;YACD,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,kBAAkB,EAAE,UAAU,EAAE,CAAC,CAAA;QACjE,CAAC;QAED,OAAO;YACL,QAAQ,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC9D,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAA8B;SACtD,CAAA;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAA;IAClC,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;QAEhE,OAAO,QAAQ,CAAC,6BAA6B,CAAC;YAC5C,iBAAiB,EAAE,IAAI,CAAC,QAAQ;YAChC,MAAM;YACN,OAAO,EAAE,gBAAgB;SAC1B,CAAC,CAAA;IACJ,CAAC;CACF;AAeD,MAAM,gBAAgB,GAAG;IACvB,cAAc;IACd,qBAAqB;IACrB,wBAAwB;IACxB,UAAU;IACV,qBAAqB;IACrB,mBAAmB;IACnB,sBAAsB;IACtB,sBAAsB;IACtB,qBAAqB;IACrB,eAAe;IACf,4BAA4B;IAC5B,yBAAyB;IACzB,uBAAuB;IACvB,2BAA2B;IAC3B,2BAA2B;IAC3B,mBAAmB;IACnB,mBAAmB;IACnB,WAAW;IACX,uBAAuB;IACvB,kBAAkB;IAClB,wBAAwB;IACxB,WAAW;IACX,yBAAyB;IACzB,0BAA0B;IAC1B,UAAU;IACV,kBAAkB;CACnB,CAAA"}