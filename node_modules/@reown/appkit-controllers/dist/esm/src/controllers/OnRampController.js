import { proxy, subscribe as sub } from 'valtio/vanilla';
import { subscribeKey as subKey } from 'valtio/vanilla/utils';
import { ConstantsUtil } from '@reown/appkit-common';
import { MELD_PUBLIC_KEY, ONRAMP_PROVIDERS } from '../utils/ConstantsUtil.js';
import { withErrorBoundary } from '../utils/withErrorBoundary.js';
import { AccountController } from './AccountController.js';
import { ApiController } from './ApiController.js';
import { BlockchainApiController } from './BlockchainApiController.js';
import { ChainController } from './ChainController.js';
import { OptionsController } from './OptionsController.js';
export const USDC_CURRENCY_DEFAULT = {
    id: '2b92315d-eab7-5bef-84fa-089a131333f5',
    name: 'USD Coin',
    symbol: 'USDC',
    networks: [
        {
            name: 'ethereum-mainnet',
            display_name: 'Ethereum',
            chain_id: '1',
            contract_address: '******************************************'
        },
        {
            name: 'polygon-mainnet',
            display_name: 'Polygon',
            chain_id: '137',
            contract_address: '******************************************'
        }
    ]
};
export const USD_CURRENCY_DEFAULT = {
    id: 'USD',
    payment_method_limits: [
        {
            id: 'card',
            min: '10.00',
            max: '7500.00'
        },
        {
            id: 'ach_bank_account',
            min: '10.00',
            max: '25000.00'
        }
    ]
};
const defaultState = {
    providers: ONRAMP_PROVIDERS,
    selectedProvider: null,
    error: null,
    purchaseCurrency: USDC_CURRENCY_DEFAULT,
    paymentCurrency: USD_CURRENCY_DEFAULT,
    purchaseCurrencies: [USDC_CURRENCY_DEFAULT],
    paymentCurrencies: [],
    quotesLoading: false
};
// -- State --------------------------------------------- //
const state = proxy(defaultState);
// -- Controller ---------------------------------------- //
const controller = {
    state,
    subscribe(callback) {
        return sub(state, () => callback(state));
    },
    subscribeKey(key, callback) {
        return subKey(state, key, callback);
    },
    setSelectedProvider(provider) {
        if (provider && provider.name === 'meld') {
            const currency = ChainController.state.activeChain === ConstantsUtil.CHAIN.SOLANA ? 'SOL' : 'USDC';
            const address = AccountController.state.address ?? '';
            const url = new URL(provider.url);
            url.searchParams.append('publicKey', MELD_PUBLIC_KEY);
            url.searchParams.append('destinationCurrencyCode', currency);
            url.searchParams.append('walletAddress', address);
            url.searchParams.append('externalCustomerId', OptionsController.state.projectId);
            state.selectedProvider = { ...provider, url: url.toString() };
        }
        else {
            state.selectedProvider = provider;
        }
    },
    setOnrampProviders(providers) {
        if (Array.isArray(providers) && providers.every(item => typeof item === 'string')) {
            const validOnramp = providers;
            const newProviders = ONRAMP_PROVIDERS.filter(provider => validOnramp.includes(provider.name));
            state.providers = newProviders;
        }
        else {
            state.providers = [];
        }
    },
    setPurchaseCurrency(currency) {
        state.purchaseCurrency = currency;
    },
    setPaymentCurrency(currency) {
        state.paymentCurrency = currency;
    },
    setPurchaseAmount(amount) {
        OnRampController.state.purchaseAmount = amount;
    },
    setPaymentAmount(amount) {
        OnRampController.state.paymentAmount = amount;
    },
    async getAvailableCurrencies() {
        const options = await BlockchainApiController.getOnrampOptions();
        state.purchaseCurrencies = options.purchaseCurrencies;
        state.paymentCurrencies = options.paymentCurrencies;
        state.paymentCurrency = options.paymentCurrencies[0] || USD_CURRENCY_DEFAULT;
        state.purchaseCurrency = options.purchaseCurrencies[0] || USDC_CURRENCY_DEFAULT;
        await ApiController.fetchCurrencyImages(options.paymentCurrencies.map(currency => currency.id));
        await ApiController.fetchTokenImages(options.purchaseCurrencies.map(currency => currency.symbol));
    },
    async getQuote() {
        state.quotesLoading = true;
        try {
            const quote = await BlockchainApiController.getOnrampQuote({
                purchaseCurrency: state.purchaseCurrency,
                paymentCurrency: state.paymentCurrency,
                amount: state.paymentAmount?.toString() || '0',
                network: state.purchaseCurrency?.symbol
            });
            state.quotesLoading = false;
            state.purchaseAmount = Number(quote?.purchaseAmount.amount);
            return quote;
        }
        catch (error) {
            state.error = error.message;
            state.quotesLoading = false;
            return null;
        }
        finally {
            state.quotesLoading = false;
        }
    },
    resetState() {
        state.selectedProvider = null;
        state.error = null;
        state.purchaseCurrency = USDC_CURRENCY_DEFAULT;
        state.paymentCurrency = USD_CURRENCY_DEFAULT;
        state.purchaseCurrencies = [USDC_CURRENCY_DEFAULT];
        state.paymentCurrencies = [];
        state.paymentAmount = undefined;
        state.purchaseAmount = undefined;
        state.quotesLoading = false;
    }
};
// Export the controller wrapped with our error boundary
export const OnRampController = withErrorBoundary(controller);
//# sourceMappingURL=OnRampController.js.map