{"version": 3, "file": "EmitterUtil.js", "sourceRoot": "", "sources": ["../../../../src/utils/EmitterUtil.ts"], "names": [], "mappings": "AAKA,MAAM,OAAO,OAAO;IAIX,EAAE,CAAsB,SAAY,EAAE,QAAqC;QAChF,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3C,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,GAAG,EAAE,CAAC,CAAA;QAClD,CAAC;QACD,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAA;IACtD,CAAC;IAEM,GAAG,CAAsB,SAAY,EAAE,QAAqC;QACjF,MAAM,SAAS,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QACvD,IAAI,SAAS,EAAE,CAAC;YACd,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;QAC5B,CAAC;IACH,CAAC;IAEM,IAAI,CAAsB,SAAY,EAAE,IAAmB;QAChE,MAAM,SAAS,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QACvD,IAAI,SAAS,EAAE,CAAC;YACd,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;QAC/C,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,SAAoB;QAC/B,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;IAC1C,CAAC;IAEM,QAAQ;QACb,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,CAAA;IAChC,CAAC;;AA7Bc,sBAAc,GAAG,IAAI,GAAG,EAAmC,CAAA"}