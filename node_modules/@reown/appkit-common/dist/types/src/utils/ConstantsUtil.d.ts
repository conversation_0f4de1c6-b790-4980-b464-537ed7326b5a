import type { ChainNamespace } from './TypeUtil.js';
export declare const ConstantsUtil: {
    readonly WC_NAME_SUFFIX: ".reown.id";
    readonly WC_NAME_SUFFIX_LEGACY: ".wcn.id";
    readonly BLOCKCHAIN_API_RPC_URL: "https://rpc.walletconnect.org";
    readonly PULSE_API_URL: "https://pulse.walletconnect.org";
    readonly W3M_API_URL: "https://api.web3modal.org";
    readonly CONNECTOR_ID: {
        readonly WALLET_CONNECT: "walletConnect";
        readonly INJECTED: "injected";
        readonly WALLET_STANDARD: "announced";
        readonly COINBASE: "coinbaseWallet";
        readonly COINBASE_SDK: "coinbaseWalletSDK";
        readonly SAFE: "safe";
        readonly LEDGER: "ledger";
        readonly OKX: "okx";
        readonly EIP6963: "eip6963";
        readonly AUTH: "ID_AUTH";
    };
    readonly CONNECTOR_NAMES: {
        readonly AUTH: "Auth";
    };
    readonly AUTH_CONNECTOR_SUPPORTED_CHAINS: ChainNamespace[];
    readonly LIMITS: {
        readonly PENDING_TRANSACTIONS: 99;
    };
    readonly CHAIN: {
        readonly EVM: "eip155";
        readonly SOLANA: "solana";
        readonly POLKADOT: "polkadot";
        readonly BITCOIN: "bip122";
    };
    readonly CHAIN_NAME_MAP: {
        readonly eip155: "EVM Networks";
        readonly solana: "Solana";
        readonly polkadot: "Polkadot";
        readonly bip122: "Bitcoin";
        readonly cosmos: "Cosmos";
    };
    readonly ADAPTER_TYPES: {
        readonly BITCOIN: "bitcoin";
        readonly SOLANA: "solana";
        readonly WAGMI: "wagmi";
        readonly ETHERS: "ethers";
        readonly ETHERS5: "ethers5";
    };
    readonly USDT_CONTRACT_ADDRESSES: readonly ["******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************"];
    readonly HTTP_STATUS_CODES: {
        readonly SERVICE_UNAVAILABLE: 503;
        readonly FORBIDDEN: 403;
    };
    readonly UNSUPPORTED_NETWORK_NAME: "Unknown Network";
    readonly SECURE_SITE_SDK_ORIGIN: string;
};
