import type { ChainNamespace } from './TypeUtil.js';
export type NamespacedConnectorKey = `@appkit/${ChainNamespace}:connected_connector_id`;
export type SafeLocalStorageItems = {
    '@appkit/wallet_id': string;
    '@appkit/wallet_name': string;
    '@appkit/solana_wallet': string;
    '@appkit/solana_caip_chain': string;
    '@appkit/active_caip_network_id': string;
    '@appkit/connected_social': string;
    '@appkit-wallet/SOCIAL_USERNAME': string;
    '@appkit/recent_wallets': string;
    '@appkit/active_namespace': string;
    '@appkit/connected_namespaces': string;
    '@appkit/connection_status': string;
    '@appkit/siwx-auth-token': string;
    '@appkit/siwx-nonce-token': string;
    '@appkit/social_provider': string;
    '@appkit/native_balance_cache': string;
    '@appkit/portfolio_cache': string;
    '@appkit/ens_cache': string;
    '@appkit/identity_cache': string;
    '@appkit/preferred_account_types': string;
    '@appkit/connections': string;
    WALLETCONNECT_DEEPLINK_CHOICE: string;
};
export declare const SafeLocalStorageKeys: {
    readonly WALLET_ID: "@appkit/wallet_id";
    readonly WALLET_NAME: "@appkit/wallet_name";
    readonly SOLANA_WALLET: "@appkit/solana_wallet";
    readonly SOLANA_CAIP_CHAIN: "@appkit/solana_caip_chain";
    readonly ACTIVE_CAIP_NETWORK_ID: "@appkit/active_caip_network_id";
    readonly CONNECTED_SOCIAL: "@appkit/connected_social";
    readonly CONNECTED_SOCIAL_USERNAME: "@appkit-wallet/SOCIAL_USERNAME";
    readonly RECENT_WALLETS: "@appkit/recent_wallets";
    readonly DEEPLINK_CHOICE: "WALLETCONNECT_DEEPLINK_CHOICE";
    readonly ACTIVE_NAMESPACE: "@appkit/active_namespace";
    readonly CONNECTED_NAMESPACES: "@appkit/connected_namespaces";
    readonly CONNECTION_STATUS: "@appkit/connection_status";
    readonly SIWX_AUTH_TOKEN: "@appkit/siwx-auth-token";
    readonly SIWX_NONCE_TOKEN: "@appkit/siwx-nonce-token";
    readonly TELEGRAM_SOCIAL_PROVIDER: "@appkit/social_provider";
    readonly NATIVE_BALANCE_CACHE: "@appkit/native_balance_cache";
    readonly PORTFOLIO_CACHE: "@appkit/portfolio_cache";
    readonly ENS_CACHE: "@appkit/ens_cache";
    readonly IDENTITY_CACHE: "@appkit/identity_cache";
    readonly PREFERRED_ACCOUNT_TYPES: "@appkit/preferred_account_types";
    readonly CONNECTIONS: "@appkit/connections";
};
export type SafeLocalStorageKey = keyof SafeLocalStorageItems | NamespacedConnectorKey;
export declare function getSafeConnectorIdKey(namespace?: ChainNamespace): NamespacedConnectorKey;
export declare const SafeLocalStorage: {
    setItem(key: SafeLocalStorageKey, value?: string): void;
    getItem(key: SafeLocalStorageKey): string | undefined;
    removeItem(key: SafeLocalStorageKey): void;
    clear(): void;
};
export declare function isSafe(): boolean;
