/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8d697b304b401681-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/ba015fad6dcf6784-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/569ce4b8f30dc480-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Fallback';src: local("Arial");ascent-override: 95.94%;descent-override: 28.16%;line-gap-override: 0.00%;size-adjust: 104.76%
}.__className_5cfdac {font-family: 'Geist', 'Geist Fallback';font-style: normal
}.__variable_5cfdac {--font-geist-sans: 'Geist', 'Geist Fallback'
}

/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/9610d9e46709d722-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/747892c23ea88013-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/93f479601ee12b01-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Mono Fallback';src: local("Arial");ascent-override: 74.67%;descent-override: 21.92%;line-gap-override: 0.00%;size-adjust: 134.59%
}.__className_9a8899 {font-family: 'Geist Mono', 'Geist Mono Fallback';font-style: normal
}.__variable_9a8899 {--font-geist-mono: 'Geist Mono', 'Geist Mono Fallback'
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
  :root {

    --background: 0 0% 100%;

    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;

    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;

    --popover-foreground: 0 0% 3.9%;

    --primary: 0 0% 9%;

    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;

    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;

    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;

    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;

    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;

    --input: 0 0% 89.8%;

    --ring: 0 0% 3.9%;

    --chart-1: 12 76% 61%;

    --chart-2: 173 58% 39%;

    --chart-3: 197 37% 24%;

    --chart-4: 43 74% 66%;

    --chart-5: 27 87% 67%;

    --radius: 0.5rem
  }
  .dark {

    --background: 0 0% 3.9%;

    --foreground: 0 0% 98%;

    --card: 0 0% 3.9%;

    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;

    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;

    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;

    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;

    --muted-foreground: 0 0% 63.9%;

    --accent: 0 0% 14.9%;

    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;

    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;

    --input: 0 0% 14.9%;

    --ring: 0 0% 83.1%;

    --chart-1: 220 70% 50%;

    --chart-2: 160 60% 45%;

    --chart-3: 30 80% 55%;

    --chart-4: 280 65% 60%;

    --chart-5: 340 75% 55%
  }
  * {
  border-color: hsl(var(--border));
}
  body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}
.container {
  width: 100%;
}
@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.visible {
  visibility: visible;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  inset: 0px;
}
.-right-2 {
  right: -0.5rem;
}
.-top-1 {
  top: -0.25rem;
}
.-top-2 {
  top: -0.5rem;
}
.left-0 {
  left: 0px;
}
.left-1\/2 {
  left: 50%;
}
.left-2 {
  left: 0.5rem;
}
.left-\[50\%\] {
  left: 50%;
}
.right-1 {
  right: 0.25rem;
}
.right-2 {
  right: 0.5rem;
}
.right-4 {
  right: 1rem;
}
.right-5 {
  right: 1.25rem;
}
.top-0 {
  top: 0px;
}
.top-1\/2 {
  top: 50%;
}
.top-2 {
  top: 0.5rem;
}
.top-4 {
  top: 1rem;
}
.top-5 {
  top: 1.25rem;
}
.top-\[50\%\] {
  top: 50%;
}
.isolate {
  isolation: isolate;
}
.-z-10 {
  z-index: -10;
}
.z-10 {
  z-index: 10;
}
.z-30 {
  z-index: 30;
}
.z-50 {
  z-index: 50;
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
.m-auto {
  margin: auto;
}
.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-0\.5 {
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-14 {
  margin-bottom: 3.5rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-auto {
  margin-right: auto;
}
.mt-10 {
  margin-top: 2.5rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-24 {
  margin-top: 6rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mt-8 {
  margin-top: 2rem;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.aspect-square {
  aspect-ratio: 1 / 1;
}
.aspect-video {
  aspect-ratio: 16 / 9;
}
.size-16 {
  width: 4rem;
  height: 4rem;
}
.size-4 {
  width: 1rem;
  height: 1rem;
}
.size-6 {
  width: 1.5rem;
  height: 1.5rem;
}
.size-\[800px\] {
  width: 800px;
  height: 800px;
}
.size-full {
  width: 100%;
  height: 100%;
}
.h-1\.5 {
  height: 0.375rem;
}
.h-10 {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-2\.5 {
  height: 0.625rem;
}
.h-24 {
  height: 6rem;
}
.h-3 {
  height: 0.75rem;
}
.h-3\.5 {
  height: 0.875rem;
}
.h-4 {
  height: 1rem;
}
.h-5 {
  height: 1.25rem;
}
.h-56 {
  height: 14rem;
}
.h-6 {
  height: 1.5rem;
}
.h-64 {
  height: 16rem;
}
.h-8 {
  height: 2rem;
}
.h-9 {
  height: 2.25rem;
}
.h-\[1\.2rem\] {
  height: 1.2rem;
}
.h-\[600px\] {
  height: 600px;
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.h-screen {
  height: 100vh;
}
.max-h-\[200px\] {
  max-height: 200px;
}
.max-h-\[524px\] {
  max-height: 524px;
}
.max-h-\[620px\] {
  max-height: 620px;
}
.max-h-\[var\(--radix-dropdown-menu-content-available-height\)\] {
  max-height: var(--radix-dropdown-menu-content-available-height);
}
.min-h-\[120px\] {
  min-height: 120px;
}
.min-h-\[200px\] {
  min-height: 200px;
}
.min-h-\[50rem\] {
  min-height: 50rem;
}
.min-h-\[60px\] {
  min-height: 60px;
}
.w-0 {
  width: 0px;
}
.w-1 {
  width: 0.25rem;
}
.w-10 {
  width: 2.5rem;
}
.w-12 {
  width: 3rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\.5 {
  width: 0.625rem;
}
.w-24 {
  width: 6rem;
}
.w-28 {
  width: 7rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\.5 {
  width: 0.875rem;
}
.w-4 {
  width: 1rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-72 {
  width: 18rem;
}
.w-8 {
  width: 2rem;
}
.w-9 {
  width: 2.25rem;
}
.w-\[1\.2rem\] {
  width: 1.2rem;
}
.w-\[60\%\] {
  width: 60%;
}
.w-auto {
  width: auto;
}
.w-full {
  width: 100%;
}
.min-w-\[8rem\] {
  min-width: 8rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-5xl {
  max-width: 64rem;
}
.max-w-full {
  max-width: 100%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-screen-2xl {
  max-width: 1536px;
}
.max-w-screen-xl {
  max-width: 1280px;
}
.max-w-xl {
  max-width: 36rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.grow {
  flex-grow: 1;
}
.basis-0 {
  flex-basis: 0px;
}
.origin-\[--radix-dropdown-menu-content-transform-origin\] {
  transform-origin: var(--radix-dropdown-menu-content-transform-origin);
}
.origin-\[--radix-popover-content-transform-origin\] {
  transform-origin: var(--radix-popover-content-transform-origin);
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[-50\%\] {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-0 {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-default {
  cursor: default;
}
.cursor-move {
  cursor: move;
}
.cursor-pointer {
  cursor: pointer;
}
.touch-none {
  touch-action: none;
}
.select-none {
  user-select: none;
}
.resize-none {
  resize: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-\[30\%_70\%\] {
  grid-template-columns: 30% 70%;
}
.flex-col {
  flex-direction: column;
}
.flex-col-reverse {
  flex-direction: column-reverse;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.items-stretch {
  align-items: stretch;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\.5 {
  gap: 0.375rem;
}
.gap-10 {
  gap: 2.5rem;
}
.gap-12 {
  gap: 3rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-5 {
  gap: 1.25rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-7 {
  gap: 1.75rem;
}
.gap-8 {
  gap: 2rem;
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.text-balance {
  text-wrap: balance;
}
.break-all {
  word-break: break-all;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-\[2px\] {
  border-radius: 2px;
}
.rounded-\[inherit\] {
  border-radius: inherit;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: var(--radius);
}
.rounded-md {
  border-radius: calc(var(--radius) - 2px);
}
.rounded-sm {
  border-radius: calc(var(--radius) - 4px);
}
.rounded-xl {
  border-radius: 0.75rem;
}
.border {
  border-width: 1px;
}
.border-2 {
  border-width: 2px;
}
.border-4 {
  border-width: 4px;
}
.border-\[1\.5px\] {
  border-width: 1.5px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-l {
  border-left-width: 1px;
}
.border-l-2 {
  border-left-width: 2px;
}
.border-t {
  border-top-width: 1px;
}
.border-t-2 {
  border-top-width: 2px;
}
.border-dashed {
  border-style: dashed;
}
.border-\[--color-border\] {
  border-color: var(--color-border);
}
.border-border {
  border-color: hsl(var(--border));
}
.border-border\/50 {
  border-color: hsl(var(--border) / 0.5);
}
.border-destructive {
  border-color: hsl(var(--destructive));
}
.border-input {
  border-color: hsl(var(--input));
}
.border-primary {
  border-color: hsl(var(--primary));
}
.border-primary\/20 {
  border-color: hsl(var(--primary) / 0.2);
}
.border-primary\/50 {
  border-color: hsl(var(--primary) / 0.5);
}
.border-transparent {
  border-color: transparent;
}
.border-l-transparent {
  border-left-color: transparent;
}
.border-t-transparent {
  border-top-color: transparent;
}
.bg-\[--color-bg\] {
  background-color: var(--color-bg);
}
.bg-accent\/20 {
  background-color: hsl(var(--accent) / 0.2);
}
.bg-background {
  background-color: hsl(var(--background));
}
.bg-background\/80 {
  background-color: hsl(var(--background) / 0.8);
}
.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}
.bg-border {
  background-color: hsl(var(--border));
}
.bg-card {
  background-color: hsl(var(--card));
}
.bg-destructive {
  background-color: hsl(var(--destructive));
}
.bg-muted {
  background-color: hsl(var(--muted));
}
.bg-popover {
  background-color: hsl(var(--popover));
}
.bg-primary {
  background-color: hsl(var(--primary));
}
.bg-primary\/10 {
  background-color: hsl(var(--primary) / 0.1);
}
.bg-primary\/20 {
  background-color: hsl(var(--primary) / 0.2);
}
.bg-secondary {
  background-color: hsl(var(--secondary));
}
.bg-transparent {
  background-color: transparent;
}
.bg-\[linear-gradient\(to_right\2c hsl\(var\(--muted-foreground\)\)_1px\2c transparent_1px\)\2c linear-gradient\(to_bottom\2c hsl\(var\(--muted-foreground\)\)_1px\2c transparent_1px\)\] {
  background-image: linear-gradient(to right,hsl(var(--muted-foreground)) 1px,transparent 1px),linear-gradient(to bottom,hsl(var(--muted-foreground)) 1px,transparent 1px);
}
.bg-\[radial-gradient\(var\(--muted-foreground\)_1px\2c transparent_1px\)\] {
  background-image: radial-gradient(var(--muted-foreground) 1px,transparent 1px);
}
.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.from-blue-500 {
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.to-purple-500 {
  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);
}
.bg-\[size\:80px_80px\] {
  background-size: 80px 80px;
}
.fill-current {
  fill: currentColor;
}
.object-contain {
  object-fit: contain;
}
.object-cover {
  object-fit: cover;
}
.p-0 {
  padding: 0px;
}
.p-1 {
  padding: 0.25rem;
}
.p-10 {
  padding: 2.5rem;
}
.p-16 {
  padding: 4rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-7 {
  padding: 1.75rem;
}
.p-8 {
  padding: 2rem;
}
.p-\[1px\] {
  padding: 1px;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-32 {
  padding-top: 8rem;
  padding-bottom: 8rem;
}
.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pb-12 {
  padding-bottom: 3rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-8 {
  padding-left: 2rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-6 {
  padding-right: 1.5rem;
}
.pt-0 {
  padding-top: 0px;
}
.pt-20 {
  padding-top: 5rem;
}
.pt-3 {
  padding-top: 0.75rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-8 {
  padding-top: 2rem;
}
.text-center {
  text-align: center;
}
.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-5xl {
  font-size: 3rem;
  line-height: 1;
}
.text-\[0\.8rem\] {
  font-size: 0.8rem;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.tabular-nums {
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}
.leading-none {
  line-height: 1;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-widest {
  letter-spacing: 0.1em;
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-card-foreground {
  color: hsl(var(--card-foreground));
}
.text-destructive {
  color: hsl(var(--destructive));
}
.text-destructive-foreground {
  color: hsl(var(--destructive-foreground));
}
.text-foreground {
  color: hsl(var(--foreground));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}
.text-muted-foreground\/70 {
  color: hsl(var(--muted-foreground) / 0.7);
}
.text-popover-foreground {
  color: hsl(var(--popover-foreground));
}
.text-primary {
  color: hsl(var(--primary));
}
.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}
.text-primary\/50 {
  color: hsl(var(--primary) / 0.5);
}
.text-secondary-foreground {
  color: hsl(var(--secondary-foreground));
}
.underline {
  text-decoration-line: underline;
}
.underline-offset-4 {
  text-underline-offset: 4px;
}
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.accent-primary {
  accent-color: hsl(var(--primary));
}
.opacity-15 {
  opacity: 0.15;
}
.opacity-60 {
  opacity: 0.6;
}
.opacity-70 {
  opacity: 0.7;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline {
  outline-style: solid;
}
.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
@keyframes enter {

  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit {

  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
.duration-200 {
  animation-duration: 200ms;
}
.duration-500 {
  animation-duration: 500ms;
}
.ease-out {
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.\[background-size\:10px_10px\] {
  background-size: 10px 10px;
}
.\[mask-image\:linear-gradient\(to_bottom_right\2c \#000\2c transparent\2c transparent\)\] {
  mask-image: linear-gradient(to bottom right,#000,transparent,transparent);
}
.\[mask-image\:linear-gradient\(to_top\2c transparent\2c transparent\2c white\2c white\2c white\2c transparent\2c transparent\)\] {
  mask-image: linear-gradient(to top,transparent,transparent,white,white,white,transparent,transparent);
}



.file\:border-0::file-selector-button {
  border-width: 0px;
}



.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}



.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}



.file\:font-medium::file-selector-button {
  font-weight: 500;
}



.file\:text-foreground::file-selector-button {
  color: hsl(var(--foreground));
}



.placeholder\:text-muted-foreground::placeholder {
  color: hsl(var(--muted-foreground));
}



.hover\:-translate-y-1:hover {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}



.hover\:border-primary:hover {
  border-color: hsl(var(--primary));
}



.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}



.hover\:bg-destructive\/80:hover {
  background-color: hsl(var(--destructive) / 0.8);
}



.hover\:bg-destructive\/90:hover {
  background-color: hsl(var(--destructive) / 0.9);
}



.hover\:bg-muted\/25:hover {
  background-color: hsl(var(--muted) / 0.25);
}



.hover\:bg-muted\/40:hover {
  background-color: hsl(var(--muted) / 0.4);
}



.hover\:bg-primary\/80:hover {
  background-color: hsl(var(--primary) / 0.8);
}



.hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / 0.9);
}



.hover\:bg-secondary\/80:hover {
  background-color: hsl(var(--secondary) / 0.8);
}



.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}



.hover\:text-blue-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}



.hover\:text-primary:hover {
  color: hsl(var(--primary));
}



.hover\:underline:hover {
  text-decoration-line: underline;
}



.hover\:opacity-100:hover {
  opacity: 1;
}



.focus\:bg-accent:focus {
  background-color: hsl(var(--accent));
}



.focus\:text-accent-foreground:focus {
  color: hsl(var(--accent-foreground));
}



.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}



.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}



.focus\:ring-ring:focus {
  --tw-ring-color: hsl(var(--ring));
}



.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}



.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}



.focus-visible\:ring-1:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}



.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}



.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: hsl(var(--ring));
}



.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}



.focus-visible\:ring-offset-background:focus-visible {
  --tw-ring-offset-color: hsl(var(--background));
}



.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}



.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}



.disabled\:opacity-50:disabled {
  opacity: 0.5;
}



.group:hover .group-hover\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}



.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}



.group:hover .group-hover\:text-primary\/70 {
  color: hsl(var(--primary) / 0.7);
}



.group.toaster .group-\[\.toaster\]\:border-border {
  border-color: hsl(var(--border));
}



.group.toast .group-\[\.toast\]\:bg-muted {
  background-color: hsl(var(--muted));
}



.group.toast .group-\[\.toast\]\:bg-primary {
  background-color: hsl(var(--primary));
}



.group.toaster .group-\[\.toaster\]\:bg-background {
  background-color: hsl(var(--background));
}



.group.toast .group-\[\.toast\]\:text-muted-foreground {
  color: hsl(var(--muted-foreground));
}



.group.toast .group-\[\.toast\]\:text-primary-foreground {
  color: hsl(var(--primary-foreground));
}



.group.toaster .group-\[\.toaster\]\:text-foreground {
  color: hsl(var(--foreground));
}



.group.toaster .group-\[\.toaster\]\:shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}



.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}



.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: 0.7;
}



.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}



.data-\[state\=checked\]\:translate-x-4[data-state="checked"] {
  --tw-translate-x: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}



.data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"] {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}



.data-\[state\=active\]\:bg-background[data-state="active"] {
  background-color: hsl(var(--background));
}



.data-\[state\=checked\]\:bg-primary[data-state="checked"] {
  background-color: hsl(var(--primary));
}



.data-\[state\=open\]\:bg-accent[data-state="open"] {
  background-color: hsl(var(--accent));
}



.data-\[state\=unchecked\]\:bg-input[data-state="unchecked"] {
  background-color: hsl(var(--input));
}



.data-\[state\=active\]\:text-foreground[data-state="active"] {
  color: hsl(var(--foreground));
}



.data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
  color: hsl(var(--muted-foreground));
}



.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: 0.5;
}



.data-\[state\=active\]\:shadow[data-state="active"] {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}



.data-\[state\=open\]\:animate-in[data-state="open"] {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}



.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}



.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}



.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}



.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}



.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}



.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
  --tw-enter-translate-y: -0.5rem;
}



.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
  --tw-enter-translate-x: 0.5rem;
}



.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
  --tw-enter-translate-x: -0.5rem;
}



.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
  --tw-enter-translate-y: 0.5rem;
}



.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"] {
  --tw-exit-translate-x: -50%;
}



.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"] {
  --tw-exit-translate-y: -48%;
}



.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"] {
  --tw-enter-translate-x: -50%;
}



.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"] {
  --tw-enter-translate-y: -48%;
}



.dark\:-rotate-90:is(.dark *) {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}



.dark\:rotate-0:is(.dark *) {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}



.dark\:scale-0:is(.dark *) {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}



.dark\:scale-100:is(.dark *) {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}



@media (min-width: 640px) {

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:rounded-lg {
    border-radius: var(--radius);
  }

  .sm\:p-2 {
    padding: 0.5rem;
  }

  .sm\:p-6 {
    padding: 1.5rem;
  }

  .sm\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .sm\:pt-20 {
    padding-top: 5rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:leading-10 {
    line-height: 2.5rem;
  }
}



@media (min-width: 768px) {

  .md\:block {
    display: block;
  }

  .md\:size-20 {
    width: 5rem;
    height: 5rem;
  }

  .md\:size-\[1300px\] {
    width: 1300px;
    height: 1300px;
  }

  .md\:h-8 {
    height: 2rem;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-\[1fr_1\.5fr\] {
    grid-template-columns: 1fr 1.5fr;
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:p-16 {
    padding: 4rem;
  }

  .md\:p-32 {
    padding: 8rem;
  }

  .md\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .md\:pt-16 {
    padding-top: 4rem;
  }

  .md\:text-left {
    text-align: left;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}



@media (min-width: 1024px) {

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:mb-0 {
    margin-bottom: 0px;
  }

  .lg\:min-h-0 {
    min-height: 0px;
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:flex-col {
    flex-direction: column;
  }

  .lg\:justify-start {
    justify-content: flex-start;
  }

  .lg\:border-l {
    border-left-width: 1px;
  }

  .lg\:border-t-0 {
    border-top-width: 0px;
  }

  .lg\:pb-16 {
    padding-bottom: 4rem;
  }
}



@media (min-width: 1280px) {

  .xl\:block {
    display: block;
  }

  .xl\:gap-16 {
    gap: 4rem;
  }

  .xl\:border-l {
    border-left-width: 1px;
  }

  .xl\:border-r {
    border-right-width: 1px;
  }

  .xl\:px-28 {
    padding-left: 7rem;
    padding-right: 7rem;
  }

  .xl\:pl-8 {
    padding-left: 2rem;
  }
}



.\[\&\>svg\]\:size-4>svg {
  width: 1rem;
  height: 1rem;
}



.\[\&\>svg\]\:h-2\.5>svg {
  height: 0.625rem;
}



.\[\&\>svg\]\:h-3>svg {
  height: 0.75rem;
}



.\[\&\>svg\]\:w-2\.5>svg {
  width: 0.625rem;
}



.\[\&\>svg\]\:w-3>svg {
  width: 0.75rem;
}



.\[\&\>svg\]\:shrink-0>svg {
  flex-shrink: 0;
}



.\[\&\>svg\]\:text-muted-foreground>svg {
  color: hsl(var(--muted-foreground));
}



.\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .recharts-cartesian-axis-tick text {
  fill: hsl(var(--muted-foreground));
}



.\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke='#ccc'] {
  stroke: hsl(var(--border) / 0.5);
}



.\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-curve.recharts-tooltip-cursor {
  stroke: hsl(var(--border));
}



.\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-dot[stroke='#fff'] {
  stroke: transparent;
}



.\[\&_\.recharts-layer\]\:outline-none .recharts-layer {
  outline: 2px solid transparent;
  outline-offset: 2px;
}



.\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-polar-grid [stroke='#ccc'] {
  stroke: hsl(var(--border));
}



.\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radial-bar-background-sector {
  fill: hsl(var(--muted));
}



.\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted .recharts-rectangle.recharts-tooltip-cursor {
  fill: hsl(var(--muted));
}



.\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-reference-line [stroke='#ccc'] {
  stroke: hsl(var(--border));
}



.\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-sector[stroke='#fff'] {
  stroke: transparent;
}



.\[\&_\.recharts-sector\]\:outline-none .recharts-sector {
  outline: 2px solid transparent;
  outline-offset: 2px;
}



.\[\&_\.recharts-surface\]\:outline-none .recharts-surface {
  outline: 2px solid transparent;
  outline-offset: 2px;
}



.\[\&_svg\]\:pointer-events-none svg {
  pointer-events: none;
}



.\[\&_svg\]\:size-4 svg {
  width: 1rem;
  height: 1rem;
}



.\[\&_svg\]\:shrink-0 svg {
  flex-shrink: 0;
}

/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@rainbow-me/rainbowkit/dist/index.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/* vanilla-extract-css-ns:src/css/reset.css.ts.vanilla.css?source=Lmlla2JjYzAgewogIGJvcmRlcjogMDsKICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogIGZvbnQtc2l6ZTogMTAwJTsKICBsaW5lLWhlaWdodDogbm9ybWFsOwogIG1hcmdpbjogMDsKICBwYWRkaW5nOiAwOwogIHRleHQtYWxpZ246IGxlZnQ7CiAgdmVydGljYWwtYWxpZ246IGJhc2VsaW5lOwogIC13ZWJraXQtdGFwLWhpZ2hsaWdodC1jb2xvcjogdHJhbnNwYXJlbnQ7Cn0KLmlla2JjYzEgewogIGxpc3Qtc3R5bGU6IG5vbmU7Cn0KLmlla2JjYzIgewogIHF1b3Rlczogbm9uZTsKfQouaWVrYmNjMjpiZWZvcmUsIC5pZWtiY2MyOmFmdGVyIHsKICBjb250ZW50OiAnJzsKfQouaWVrYmNjMyB7CiAgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsKICBib3JkZXItc3BhY2luZzogMDsKfQouaWVrYmNjNCB7CiAgYXBwZWFyYW5jZTogbm9uZTsKfQouaWVrYmNjNSB7CiAgb3V0bGluZTogbm9uZTsKfQouaWVrYmNjNTo6cGxhY2Vob2xkZXIgewogIG9wYWNpdHk6IDE7Cn0KLmlla2JjYzYgewogIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50OwogIGNvbG9yOiBpbmhlcml0Owp9Ci5pZWtiY2M3OmRpc2FibGVkIHsKICBvcGFjaXR5OiAxOwp9Ci5pZWtiY2M3OjotbXMtZXhwYW5kIHsKICBkaXNwbGF5OiBub25lOwp9Ci5pZWtiY2M4OjotbXMtY2xlYXIgewogIGRpc3BsYXk6IG5vbmU7Cn0KLmlla2JjYzg6Oi13ZWJraXQtc2VhcmNoLWNhbmNlbC1idXR0b24gewogIC13ZWJraXQtYXBwZWFyYW5jZTogbm9uZTsKfQouaWVrYmNjOSB7CiAgYmFja2dyb3VuZDogbm9uZTsKICBjdXJzb3I6IHBvaW50ZXI7CiAgdGV4dC1hbGlnbjogbGVmdDsKfQouaWVrYmNjYSB7CiAgY29sb3I6IGluaGVyaXQ7CiAgdGV4dC1kZWNvcmF0aW9uOiBub25lOwp9 */
[data-rk] .iekbcc0 {
  border: 0;
  box-sizing: border-box;
  font-size: 100%;
  line-height: normal;
  margin: 0;
  padding: 0;
  text-align: left;
  vertical-align: baseline;
  -webkit-tap-highlight-color: transparent;
}
[data-rk] .iekbcc1 {
  list-style: none;
}
[data-rk] .iekbcc2 {
  quotes: none;
}
[data-rk] .iekbcc2:before,
[data-rk] .iekbcc2:after {
  content: "";
}
[data-rk] .iekbcc3 {
  border-collapse: collapse;
  border-spacing: 0;
}
[data-rk] .iekbcc4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
[data-rk] .iekbcc5 {
  outline: none;
}
[data-rk] .iekbcc5::-moz-placeholder {
  opacity: 1;
}
[data-rk] .iekbcc5::placeholder {
  opacity: 1;
}
[data-rk] .iekbcc6 {
  background-color: transparent;
  color: inherit;
}
[data-rk] .iekbcc7:disabled {
  opacity: 1;
}
[data-rk] .iekbcc7::-ms-expand {
  display: none;
}
[data-rk] .iekbcc8::-ms-clear {
  display: none;
}
[data-rk] .iekbcc8::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
[data-rk] .iekbcc9 {
  background: none;
  cursor: pointer;
  text-align: left;
}
[data-rk] .iekbcca {
  color: inherit;
  text-decoration: none;
}

/* vanilla-extract-css-ns:src/css/sprinkles.css.ts.vanilla.css?source=#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 */
[data-rk] .ju367v0 {
  align-items: flex-start;
}
[data-rk] .ju367v2 {
  align-items: flex-end;
}
[data-rk] .ju367v4 {
  align-items: center;
}
[data-rk] .ju367v6 {
  display: none;
}
[data-rk] .ju367v8 {
  display: block;
}
[data-rk] .ju367va {
  display: flex;
}
[data-rk] .ju367vc {
  display: inline;
}
[data-rk] .ju367ve {
  align-self: flex-start;
}
[data-rk] .ju367vf {
  align-self: flex-end;
}
[data-rk] .ju367vg {
  align-self: center;
}
[data-rk] .ju367vh {
  background-size: cover;
}
[data-rk] .ju367vi {
  border-radius: 1px;
}
[data-rk] .ju367vj {
  border-radius: 6px;
}
[data-rk] .ju367vk {
  border-radius: 10px;
}
[data-rk] .ju367vl {
  border-radius: 13px;
}
[data-rk] .ju367vm {
  border-radius: var(--rk-radii-actionButton);
}
[data-rk] .ju367vn {
  border-radius: var(--rk-radii-connectButton);
}
[data-rk] .ju367vo {
  border-radius: var(--rk-radii-menuButton);
}
[data-rk] .ju367vp {
  border-radius: var(--rk-radii-modal);
}
[data-rk] .ju367vq {
  border-radius: var(--rk-radii-modalMobile);
}
[data-rk] .ju367vr {
  border-radius: 25%;
}
[data-rk] .ju367vs {
  border-radius: 9999px;
}
[data-rk] .ju367vt {
  border-style: solid;
}
[data-rk] .ju367vu {
  border-width: 0px;
}
[data-rk] .ju367vv {
  border-width: 1px;
}
[data-rk] .ju367vw {
  border-width: 2px;
}
[data-rk] .ju367vx {
  border-width: 4px;
}
[data-rk] .ju367vy {
  cursor: pointer;
}
[data-rk] .ju367vz {
  cursor: none;
}
[data-rk] .ju367v10 {
  pointer-events: none;
}
[data-rk] .ju367v11 {
  pointer-events: all;
}
[data-rk] .ju367v12 {
  min-height: 8px;
}
[data-rk] .ju367v13 {
  min-height: 44px;
}
[data-rk] .ju367v14 {
  flex-direction: row;
}
[data-rk] .ju367v15 {
  flex-direction: column;
}
[data-rk] .ju367v16 {
  font-family: var(--rk-fonts-body);
}
[data-rk] .ju367v17 {
  font-size: 12px;
  line-height: 18px;
}
[data-rk] .ju367v18 {
  font-size: 13px;
  line-height: 18px;
}
[data-rk] .ju367v19 {
  font-size: 14px;
  line-height: 18px;
}
[data-rk] .ju367v1a {
  font-size: 16px;
  line-height: 20px;
}
[data-rk] .ju367v1b {
  font-size: 18px;
  line-height: 24px;
}
[data-rk] .ju367v1c {
  font-size: 20px;
  line-height: 24px;
}
[data-rk] .ju367v1d {
  font-size: 23px;
  line-height: 29px;
}
[data-rk] .ju367v1e {
  font-weight: 400;
}
[data-rk] .ju367v1f {
  font-weight: 500;
}
[data-rk] .ju367v1g {
  font-weight: 600;
}
[data-rk] .ju367v1h {
  font-weight: 700;
}
[data-rk] .ju367v1i {
  font-weight: 800;
}
[data-rk] .ju367v1j {
  gap: 0;
}
[data-rk] .ju367v1k {
  gap: 1px;
}
[data-rk] .ju367v1l {
  gap: 2px;
}
[data-rk] .ju367v1m {
  gap: 3px;
}
[data-rk] .ju367v1n {
  gap: 4px;
}
[data-rk] .ju367v1o {
  gap: 5px;
}
[data-rk] .ju367v1p {
  gap: 6px;
}
[data-rk] .ju367v1q {
  gap: 8px;
}
[data-rk] .ju367v1r {
  gap: 10px;
}
[data-rk] .ju367v1s {
  gap: 12px;
}
[data-rk] .ju367v1t {
  gap: 14px;
}
[data-rk] .ju367v1u {
  gap: 16px;
}
[data-rk] .ju367v1v {
  gap: 18px;
}
[data-rk] .ju367v1w {
  gap: 20px;
}
[data-rk] .ju367v1x {
  gap: 24px;
}
[data-rk] .ju367v1y {
  gap: 28px;
}
[data-rk] .ju367v1z {
  gap: 32px;
}
[data-rk] .ju367v20 {
  gap: 36px;
}
[data-rk] .ju367v21 {
  gap: 44px;
}
[data-rk] .ju367v22 {
  gap: 64px;
}
[data-rk] .ju367v23 {
  gap: -1px;
}
[data-rk] .ju367v24 {
  height: 1px;
}
[data-rk] .ju367v25 {
  height: 2px;
}
[data-rk] .ju367v26 {
  height: 4px;
}
[data-rk] .ju367v27 {
  height: 8px;
}
[data-rk] .ju367v28 {
  height: 12px;
}
[data-rk] .ju367v29 {
  height: 20px;
}
[data-rk] .ju367v2a {
  height: 24px;
}
[data-rk] .ju367v2b {
  height: 28px;
}
[data-rk] .ju367v2c {
  height: 30px;
}
[data-rk] .ju367v2d {
  height: 32px;
}
[data-rk] .ju367v2e {
  height: 34px;
}
[data-rk] .ju367v2f {
  height: 36px;
}
[data-rk] .ju367v2g {
  height: 40px;
}
[data-rk] .ju367v2h {
  height: 44px;
}
[data-rk] .ju367v2i {
  height: 48px;
}
[data-rk] .ju367v2j {
  height: 54px;
}
[data-rk] .ju367v2k {
  height: 60px;
}
[data-rk] .ju367v2l {
  height: 200px;
}
[data-rk] .ju367v2m {
  height: 100%;
}
[data-rk] .ju367v2n {
  height: -moz-max-content;
  height: max-content;
}
[data-rk] .ju367v2o {
  justify-content: flex-start;
}
[data-rk] .ju367v2p {
  justify-content: flex-end;
}
[data-rk] .ju367v2q {
  justify-content: center;
}
[data-rk] .ju367v2r {
  justify-content: space-between;
}
[data-rk] .ju367v2s {
  justify-content: space-around;
}
[data-rk] .ju367v2t {
  text-align: left;
}
[data-rk] .ju367v2u {
  text-align: center;
}
[data-rk] .ju367v2v {
  text-align: inherit;
}
[data-rk] .ju367v2w {
  margin-bottom: 0;
}
[data-rk] .ju367v2x {
  margin-bottom: 1px;
}
[data-rk] .ju367v2y {
  margin-bottom: 2px;
}
[data-rk] .ju367v2z {
  margin-bottom: 3px;
}
[data-rk] .ju367v30 {
  margin-bottom: 4px;
}
[data-rk] .ju367v31 {
  margin-bottom: 5px;
}
[data-rk] .ju367v32 {
  margin-bottom: 6px;
}
[data-rk] .ju367v33 {
  margin-bottom: 8px;
}
[data-rk] .ju367v34 {
  margin-bottom: 10px;
}
[data-rk] .ju367v35 {
  margin-bottom: 12px;
}
[data-rk] .ju367v36 {
  margin-bottom: 14px;
}
[data-rk] .ju367v37 {
  margin-bottom: 16px;
}
[data-rk] .ju367v38 {
  margin-bottom: 18px;
}
[data-rk] .ju367v39 {
  margin-bottom: 20px;
}
[data-rk] .ju367v3a {
  margin-bottom: 24px;
}
[data-rk] .ju367v3b {
  margin-bottom: 28px;
}
[data-rk] .ju367v3c {
  margin-bottom: 32px;
}
[data-rk] .ju367v3d {
  margin-bottom: 36px;
}
[data-rk] .ju367v3e {
  margin-bottom: 44px;
}
[data-rk] .ju367v3f {
  margin-bottom: 64px;
}
[data-rk] .ju367v3g {
  margin-bottom: -1px;
}
[data-rk] .ju367v3h {
  margin-left: 0;
}
[data-rk] .ju367v3i {
  margin-left: 1px;
}
[data-rk] .ju367v3j {
  margin-left: 2px;
}
[data-rk] .ju367v3k {
  margin-left: 3px;
}
[data-rk] .ju367v3l {
  margin-left: 4px;
}
[data-rk] .ju367v3m {
  margin-left: 5px;
}
[data-rk] .ju367v3n {
  margin-left: 6px;
}
[data-rk] .ju367v3o {
  margin-left: 8px;
}
[data-rk] .ju367v3p {
  margin-left: 10px;
}
[data-rk] .ju367v3q {
  margin-left: 12px;
}
[data-rk] .ju367v3r {
  margin-left: 14px;
}
[data-rk] .ju367v3s {
  margin-left: 16px;
}
[data-rk] .ju367v3t {
  margin-left: 18px;
}
[data-rk] .ju367v3u {
  margin-left: 20px;
}
[data-rk] .ju367v3v {
  margin-left: 24px;
}
[data-rk] .ju367v3w {
  margin-left: 28px;
}
[data-rk] .ju367v3x {
  margin-left: 32px;
}
[data-rk] .ju367v3y {
  margin-left: 36px;
}
[data-rk] .ju367v3z {
  margin-left: 44px;
}
[data-rk] .ju367v40 {
  margin-left: 64px;
}
[data-rk] .ju367v41 {
  margin-left: -1px;
}
[data-rk] .ju367v42 {
  margin-right: 0;
}
[data-rk] .ju367v43 {
  margin-right: 1px;
}
[data-rk] .ju367v44 {
  margin-right: 2px;
}
[data-rk] .ju367v45 {
  margin-right: 3px;
}
[data-rk] .ju367v46 {
  margin-right: 4px;
}
[data-rk] .ju367v47 {
  margin-right: 5px;
}
[data-rk] .ju367v48 {
  margin-right: 6px;
}
[data-rk] .ju367v49 {
  margin-right: 8px;
}
[data-rk] .ju367v4a {
  margin-right: 10px;
}
[data-rk] .ju367v4b {
  margin-right: 12px;
}
[data-rk] .ju367v4c {
  margin-right: 14px;
}
[data-rk] .ju367v4d {
  margin-right: 16px;
}
[data-rk] .ju367v4e {
  margin-right: 18px;
}
[data-rk] .ju367v4f {
  margin-right: 20px;
}
[data-rk] .ju367v4g {
  margin-right: 24px;
}
[data-rk] .ju367v4h {
  margin-right: 28px;
}
[data-rk] .ju367v4i {
  margin-right: 32px;
}
[data-rk] .ju367v4j {
  margin-right: 36px;
}
[data-rk] .ju367v4k {
  margin-right: 44px;
}
[data-rk] .ju367v4l {
  margin-right: 64px;
}
[data-rk] .ju367v4m {
  margin-right: -1px;
}
[data-rk] .ju367v4n {
  margin-top: 0;
}
[data-rk] .ju367v4o {
  margin-top: 1px;
}
[data-rk] .ju367v4p {
  margin-top: 2px;
}
[data-rk] .ju367v4q {
  margin-top: 3px;
}
[data-rk] .ju367v4r {
  margin-top: 4px;
}
[data-rk] .ju367v4s {
  margin-top: 5px;
}
[data-rk] .ju367v4t {
  margin-top: 6px;
}
[data-rk] .ju367v4u {
  margin-top: 8px;
}
[data-rk] .ju367v4v {
  margin-top: 10px;
}
[data-rk] .ju367v4w {
  margin-top: 12px;
}
[data-rk] .ju367v4x {
  margin-top: 14px;
}
[data-rk] .ju367v4y {
  margin-top: 16px;
}
[data-rk] .ju367v4z {
  margin-top: 18px;
}
[data-rk] .ju367v50 {
  margin-top: 20px;
}
[data-rk] .ju367v51 {
  margin-top: 24px;
}
[data-rk] .ju367v52 {
  margin-top: 28px;
}
[data-rk] .ju367v53 {
  margin-top: 32px;
}
[data-rk] .ju367v54 {
  margin-top: 36px;
}
[data-rk] .ju367v55 {
  margin-top: 44px;
}
[data-rk] .ju367v56 {
  margin-top: 64px;
}
[data-rk] .ju367v57 {
  margin-top: -1px;
}
[data-rk] .ju367v58 {
  max-width: 1px;
}
[data-rk] .ju367v59 {
  max-width: 2px;
}
[data-rk] .ju367v5a {
  max-width: 4px;
}
[data-rk] .ju367v5b {
  max-width: 8px;
}
[data-rk] .ju367v5c {
  max-width: 12px;
}
[data-rk] .ju367v5d {
  max-width: 20px;
}
[data-rk] .ju367v5e {
  max-width: 24px;
}
[data-rk] .ju367v5f {
  max-width: 28px;
}
[data-rk] .ju367v5g {
  max-width: 30px;
}
[data-rk] .ju367v5h {
  max-width: 32px;
}
[data-rk] .ju367v5i {
  max-width: 34px;
}
[data-rk] .ju367v5j {
  max-width: 36px;
}
[data-rk] .ju367v5k {
  max-width: 40px;
}
[data-rk] .ju367v5l {
  max-width: 44px;
}
[data-rk] .ju367v5m {
  max-width: 48px;
}
[data-rk] .ju367v5n {
  max-width: 54px;
}
[data-rk] .ju367v5o {
  max-width: 60px;
}
[data-rk] .ju367v5p {
  max-width: 200px;
}
[data-rk] .ju367v5q {
  max-width: 100%;
}
[data-rk] .ju367v5r {
  max-width: -moz-max-content;
  max-width: max-content;
}
[data-rk] .ju367v5s {
  min-width: 1px;
}
[data-rk] .ju367v5t {
  min-width: 2px;
}
[data-rk] .ju367v5u {
  min-width: 4px;
}
[data-rk] .ju367v5v {
  min-width: 8px;
}
[data-rk] .ju367v5w {
  min-width: 12px;
}
[data-rk] .ju367v5x {
  min-width: 20px;
}
[data-rk] .ju367v5y {
  min-width: 24px;
}
[data-rk] .ju367v5z {
  min-width: 28px;
}
[data-rk] .ju367v60 {
  min-width: 30px;
}
[data-rk] .ju367v61 {
  min-width: 32px;
}
[data-rk] .ju367v62 {
  min-width: 34px;
}
[data-rk] .ju367v63 {
  min-width: 36px;
}
[data-rk] .ju367v64 {
  min-width: 40px;
}
[data-rk] .ju367v65 {
  min-width: 44px;
}
[data-rk] .ju367v66 {
  min-width: 48px;
}
[data-rk] .ju367v67 {
  min-width: 54px;
}
[data-rk] .ju367v68 {
  min-width: 60px;
}
[data-rk] .ju367v69 {
  min-width: 200px;
}
[data-rk] .ju367v6a {
  min-width: 100%;
}
[data-rk] .ju367v6b {
  min-width: -moz-max-content;
  min-width: max-content;
}
[data-rk] .ju367v6c {
  overflow: hidden;
}
[data-rk] .ju367v6d {
  padding-bottom: 0;
}
[data-rk] .ju367v6e {
  padding-bottom: 1px;
}
[data-rk] .ju367v6f {
  padding-bottom: 2px;
}
[data-rk] .ju367v6g {
  padding-bottom: 3px;
}
[data-rk] .ju367v6h {
  padding-bottom: 4px;
}
[data-rk] .ju367v6i {
  padding-bottom: 5px;
}
[data-rk] .ju367v6j {
  padding-bottom: 6px;
}
[data-rk] .ju367v6k {
  padding-bottom: 8px;
}
[data-rk] .ju367v6l {
  padding-bottom: 10px;
}
[data-rk] .ju367v6m {
  padding-bottom: 12px;
}
[data-rk] .ju367v6n {
  padding-bottom: 14px;
}
[data-rk] .ju367v6o {
  padding-bottom: 16px;
}
[data-rk] .ju367v6p {
  padding-bottom: 18px;
}
[data-rk] .ju367v6q {
  padding-bottom: 20px;
}
[data-rk] .ju367v6r {
  padding-bottom: 24px;
}
[data-rk] .ju367v6s {
  padding-bottom: 28px;
}
[data-rk] .ju367v6t {
  padding-bottom: 32px;
}
[data-rk] .ju367v6u {
  padding-bottom: 36px;
}
[data-rk] .ju367v6v {
  padding-bottom: 44px;
}
[data-rk] .ju367v6w {
  padding-bottom: 64px;
}
[data-rk] .ju367v6x {
  padding-bottom: -1px;
}
[data-rk] .ju367v6y {
  padding-left: 0;
}
[data-rk] .ju367v6z {
  padding-left: 1px;
}
[data-rk] .ju367v70 {
  padding-left: 2px;
}
[data-rk] .ju367v71 {
  padding-left: 3px;
}
[data-rk] .ju367v72 {
  padding-left: 4px;
}
[data-rk] .ju367v73 {
  padding-left: 5px;
}
[data-rk] .ju367v74 {
  padding-left: 6px;
}
[data-rk] .ju367v75 {
  padding-left: 8px;
}
[data-rk] .ju367v76 {
  padding-left: 10px;
}
[data-rk] .ju367v77 {
  padding-left: 12px;
}
[data-rk] .ju367v78 {
  padding-left: 14px;
}
[data-rk] .ju367v79 {
  padding-left: 16px;
}
[data-rk] .ju367v7a {
  padding-left: 18px;
}
[data-rk] .ju367v7b {
  padding-left: 20px;
}
[data-rk] .ju367v7c {
  padding-left: 24px;
}
[data-rk] .ju367v7d {
  padding-left: 28px;
}
[data-rk] .ju367v7e {
  padding-left: 32px;
}
[data-rk] .ju367v7f {
  padding-left: 36px;
}
[data-rk] .ju367v7g {
  padding-left: 44px;
}
[data-rk] .ju367v7h {
  padding-left: 64px;
}
[data-rk] .ju367v7i {
  padding-left: -1px;
}
[data-rk] .ju367v7j {
  padding-right: 0;
}
[data-rk] .ju367v7k {
  padding-right: 1px;
}
[data-rk] .ju367v7l {
  padding-right: 2px;
}
[data-rk] .ju367v7m {
  padding-right: 3px;
}
[data-rk] .ju367v7n {
  padding-right: 4px;
}
[data-rk] .ju367v7o {
  padding-right: 5px;
}
[data-rk] .ju367v7p {
  padding-right: 6px;
}
[data-rk] .ju367v7q {
  padding-right: 8px;
}
[data-rk] .ju367v7r {
  padding-right: 10px;
}
[data-rk] .ju367v7s {
  padding-right: 12px;
}
[data-rk] .ju367v7t {
  padding-right: 14px;
}
[data-rk] .ju367v7u {
  padding-right: 16px;
}
[data-rk] .ju367v7v {
  padding-right: 18px;
}
[data-rk] .ju367v7w {
  padding-right: 20px;
}
[data-rk] .ju367v7x {
  padding-right: 24px;
}
[data-rk] .ju367v7y {
  padding-right: 28px;
}
[data-rk] .ju367v7z {
  padding-right: 32px;
}
[data-rk] .ju367v80 {
  padding-right: 36px;
}
[data-rk] .ju367v81 {
  padding-right: 44px;
}
[data-rk] .ju367v82 {
  padding-right: 64px;
}
[data-rk] .ju367v83 {
  padding-right: -1px;
}
[data-rk] .ju367v84 {
  padding-top: 0;
}
[data-rk] .ju367v85 {
  padding-top: 1px;
}
[data-rk] .ju367v86 {
  padding-top: 2px;
}
[data-rk] .ju367v87 {
  padding-top: 3px;
}
[data-rk] .ju367v88 {
  padding-top: 4px;
}
[data-rk] .ju367v89 {
  padding-top: 5px;
}
[data-rk] .ju367v8a {
  padding-top: 6px;
}
[data-rk] .ju367v8b {
  padding-top: 8px;
}
[data-rk] .ju367v8c {
  padding-top: 10px;
}
[data-rk] .ju367v8d {
  padding-top: 12px;
}
[data-rk] .ju367v8e {
  padding-top: 14px;
}
[data-rk] .ju367v8f {
  padding-top: 16px;
}
[data-rk] .ju367v8g {
  padding-top: 18px;
}
[data-rk] .ju367v8h {
  padding-top: 20px;
}
[data-rk] .ju367v8i {
  padding-top: 24px;
}
[data-rk] .ju367v8j {
  padding-top: 28px;
}
[data-rk] .ju367v8k {
  padding-top: 32px;
}
[data-rk] .ju367v8l {
  padding-top: 36px;
}
[data-rk] .ju367v8m {
  padding-top: 44px;
}
[data-rk] .ju367v8n {
  padding-top: 64px;
}
[data-rk] .ju367v8o {
  padding-top: -1px;
}
[data-rk] .ju367v8p {
  position: absolute;
}
[data-rk] .ju367v8q {
  position: fixed;
}
[data-rk] .ju367v8r {
  position: relative;
}
[data-rk] .ju367v8s {
  -webkit-user-select: none;
}
[data-rk] .ju367v8t {
  right: 0;
}
[data-rk] .ju367v8u {
  transition: 0.125s ease;
}
[data-rk] .ju367v8v {
  transition: transform 0.125s ease;
}
[data-rk] .ju367v8w {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
[data-rk] .ju367v8x {
  width: 1px;
}
[data-rk] .ju367v8y {
  width: 2px;
}
[data-rk] .ju367v8z {
  width: 4px;
}
[data-rk] .ju367v90 {
  width: 8px;
}
[data-rk] .ju367v91 {
  width: 12px;
}
[data-rk] .ju367v92 {
  width: 20px;
}
[data-rk] .ju367v93 {
  width: 24px;
}
[data-rk] .ju367v94 {
  width: 28px;
}
[data-rk] .ju367v95 {
  width: 30px;
}
[data-rk] .ju367v96 {
  width: 32px;
}
[data-rk] .ju367v97 {
  width: 34px;
}
[data-rk] .ju367v98 {
  width: 36px;
}
[data-rk] .ju367v99 {
  width: 40px;
}
[data-rk] .ju367v9a {
  width: 44px;
}
[data-rk] .ju367v9b {
  width: 48px;
}
[data-rk] .ju367v9c {
  width: 54px;
}
[data-rk] .ju367v9d {
  width: 60px;
}
[data-rk] .ju367v9e {
  width: 200px;
}
[data-rk] .ju367v9f {
  width: 100%;
}
[data-rk] .ju367v9g {
  width: -moz-max-content;
  width: max-content;
}
[data-rk] .ju367v9h {
  -webkit-backdrop-filter: var(--rk-blurs-modalOverlay);
  backdrop-filter: var(--rk-blurs-modalOverlay);
}
[data-rk] .ju367v9i {
  background: var(--rk-colors-accentColor);
}
[data-rk] .ju367v9j:hover {
  background: var(--rk-colors-accentColor);
}
[data-rk] .ju367v9k:active {
  background: var(--rk-colors-accentColor);
}
[data-rk] .ju367v9l {
  background: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367v9m:hover {
  background: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367v9n:active {
  background: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367v9o {
  background: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367v9p:hover {
  background: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367v9q:active {
  background: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367v9r {
  background: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367v9s:hover {
  background: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367v9t:active {
  background: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367v9u {
  background: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367v9v:hover {
  background: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367v9w:active {
  background: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367v9x {
  background: var(--rk-colors-closeButton);
}
[data-rk] .ju367v9y:hover {
  background: var(--rk-colors-closeButton);
}
[data-rk] .ju367v9z:active {
  background: var(--rk-colors-closeButton);
}
[data-rk] .ju367va0 {
  background: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367va1:hover {
  background: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367va2:active {
  background: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367va3 {
  background: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367va4:hover {
  background: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367va5:active {
  background: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367va6 {
  background: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367va7:hover {
  background: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367va8:active {
  background: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367va9 {
  background: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vaa:hover {
  background: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vab:active {
  background: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vac {
  background: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vad:hover {
  background: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vae:active {
  background: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vaf {
  background: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vag:hover {
  background: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vah:active {
  background: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vai {
  background: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vaj:hover {
  background: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vak:active {
  background: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367val {
  background: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vam:hover {
  background: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367van:active {
  background: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vao {
  background: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vap:hover {
  background: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vaq:active {
  background: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367var {
  background: var(--rk-colors-error);
}
[data-rk] .ju367vas:hover {
  background: var(--rk-colors-error);
}
[data-rk] .ju367vat:active {
  background: var(--rk-colors-error);
}
[data-rk] .ju367vau {
  background: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vav:hover {
  background: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vaw:active {
  background: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vax {
  background: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vay:hover {
  background: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vaz:active {
  background: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vb0 {
  background: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vb1:hover {
  background: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vb2:active {
  background: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vb3 {
  background: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vb4:hover {
  background: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vb5:active {
  background: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vb6 {
  background: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vb7:hover {
  background: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vb8:active {
  background: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vb9 {
  background: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vba:hover {
  background: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vbb:active {
  background: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vbc {
  background: var(--rk-colors-modalText);
}
[data-rk] .ju367vbd:hover {
  background: var(--rk-colors-modalText);
}
[data-rk] .ju367vbe:active {
  background: var(--rk-colors-modalText);
}
[data-rk] .ju367vbf {
  background: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vbg:hover {
  background: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vbh:active {
  background: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vbi {
  background: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vbj:hover {
  background: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vbk:active {
  background: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vbl {
  background: var(--rk-colors-profileAction);
}
[data-rk] .ju367vbm:hover {
  background: var(--rk-colors-profileAction);
}
[data-rk] .ju367vbn:active {
  background: var(--rk-colors-profileAction);
}
[data-rk] .ju367vbo {
  background: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vbp:hover {
  background: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vbq:active {
  background: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vbr {
  background: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vbs:hover {
  background: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vbt:active {
  background: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vbu {
  background: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vbv:hover {
  background: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vbw:active {
  background: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vbx {
  background: var(--rk-colors-standby);
}
[data-rk] .ju367vby:hover {
  background: var(--rk-colors-standby);
}
[data-rk] .ju367vbz:active {
  background: var(--rk-colors-standby);
}
[data-rk] .ju367vc0 {
  border-color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vc1:hover {
  border-color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vc2:active {
  border-color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vc3 {
  border-color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vc4:hover {
  border-color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vc5:active {
  border-color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vc6 {
  border-color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vc7:hover {
  border-color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vc8:active {
  border-color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vc9 {
  border-color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vca:hover {
  border-color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vcb:active {
  border-color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vcc {
  border-color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vcd:hover {
  border-color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vce:active {
  border-color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vcf {
  border-color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vcg:hover {
  border-color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vch:active {
  border-color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vci {
  border-color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vcj:hover {
  border-color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vck:active {
  border-color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vcl {
  border-color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vcm:hover {
  border-color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vcn:active {
  border-color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vco {
  border-color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vcp:hover {
  border-color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vcq:active {
  border-color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vcr {
  border-color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vcs:hover {
  border-color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vct:active {
  border-color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vcu {
  border-color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vcv:hover {
  border-color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vcw:active {
  border-color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vcx {
  border-color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vcy:hover {
  border-color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vcz:active {
  border-color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vd0 {
  border-color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vd1:hover {
  border-color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vd2:active {
  border-color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vd3 {
  border-color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vd4:hover {
  border-color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vd5:active {
  border-color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vd6 {
  border-color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vd7:hover {
  border-color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vd8:active {
  border-color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vd9 {
  border-color: var(--rk-colors-error);
}
[data-rk] .ju367vda:hover {
  border-color: var(--rk-colors-error);
}
[data-rk] .ju367vdb:active {
  border-color: var(--rk-colors-error);
}
[data-rk] .ju367vdc {
  border-color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vdd:hover {
  border-color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vde:active {
  border-color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vdf {
  border-color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vdg:hover {
  border-color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vdh:active {
  border-color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vdi {
  border-color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vdj:hover {
  border-color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vdk:active {
  border-color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vdl {
  border-color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vdm:hover {
  border-color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vdn:active {
  border-color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vdo {
  border-color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vdp:hover {
  border-color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vdq:active {
  border-color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vdr {
  border-color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vds:hover {
  border-color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vdt:active {
  border-color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vdu {
  border-color: var(--rk-colors-modalText);
}
[data-rk] .ju367vdv:hover {
  border-color: var(--rk-colors-modalText);
}
[data-rk] .ju367vdw:active {
  border-color: var(--rk-colors-modalText);
}
[data-rk] .ju367vdx {
  border-color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vdy:hover {
  border-color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vdz:active {
  border-color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367ve0 {
  border-color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367ve1:hover {
  border-color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367ve2:active {
  border-color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367ve3 {
  border-color: var(--rk-colors-profileAction);
}
[data-rk] .ju367ve4:hover {
  border-color: var(--rk-colors-profileAction);
}
[data-rk] .ju367ve5:active {
  border-color: var(--rk-colors-profileAction);
}
[data-rk] .ju367ve6 {
  border-color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367ve7:hover {
  border-color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367ve8:active {
  border-color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367ve9 {
  border-color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vea:hover {
  border-color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367veb:active {
  border-color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vec {
  border-color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367ved:hover {
  border-color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vee:active {
  border-color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vef {
  border-color: var(--rk-colors-standby);
}
[data-rk] .ju367veg:hover {
  border-color: var(--rk-colors-standby);
}
[data-rk] .ju367veh:active {
  border-color: var(--rk-colors-standby);
}
[data-rk] .ju367vei {
  box-shadow: var(--rk-shadows-connectButton);
}
[data-rk] .ju367vej:hover {
  box-shadow: var(--rk-shadows-connectButton);
}
[data-rk] .ju367vek:active {
  box-shadow: var(--rk-shadows-connectButton);
}
[data-rk] .ju367vel {
  box-shadow: var(--rk-shadows-dialog);
}
[data-rk] .ju367vem:hover {
  box-shadow: var(--rk-shadows-dialog);
}
[data-rk] .ju367ven:active {
  box-shadow: var(--rk-shadows-dialog);
}
[data-rk] .ju367veo {
  box-shadow: var(--rk-shadows-profileDetailsAction);
}
[data-rk] .ju367vep:hover {
  box-shadow: var(--rk-shadows-profileDetailsAction);
}
[data-rk] .ju367veq:active {
  box-shadow: var(--rk-shadows-profileDetailsAction);
}
[data-rk] .ju367ver {
  box-shadow: var(--rk-shadows-selectedOption);
}
[data-rk] .ju367ves:hover {
  box-shadow: var(--rk-shadows-selectedOption);
}
[data-rk] .ju367vet:active {
  box-shadow: var(--rk-shadows-selectedOption);
}
[data-rk] .ju367veu {
  box-shadow: var(--rk-shadows-selectedWallet);
}
[data-rk] .ju367vev:hover {
  box-shadow: var(--rk-shadows-selectedWallet);
}
[data-rk] .ju367vew:active {
  box-shadow: var(--rk-shadows-selectedWallet);
}
[data-rk] .ju367vex {
  box-shadow: var(--rk-shadows-walletLogo);
}
[data-rk] .ju367vey:hover {
  box-shadow: var(--rk-shadows-walletLogo);
}
[data-rk] .ju367vez:active {
  box-shadow: var(--rk-shadows-walletLogo);
}
[data-rk] .ju367vf0 {
  color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vf1:hover {
  color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vf2:active {
  color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vf3 {
  color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vf4:hover {
  color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vf5:active {
  color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vf6 {
  color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vf7:hover {
  color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vf8:active {
  color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vf9 {
  color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vfa:hover {
  color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vfb:active {
  color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vfc {
  color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vfd:hover {
  color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vfe:active {
  color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vff {
  color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vfg:hover {
  color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vfh:active {
  color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vfi {
  color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vfj:hover {
  color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vfk:active {
  color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vfl {
  color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vfm:hover {
  color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vfn:active {
  color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vfo {
  color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vfp:hover {
  color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vfq:active {
  color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vfr {
  color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vfs:hover {
  color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vft:active {
  color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vfu {
  color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vfv:hover {
  color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vfw:active {
  color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vfx {
  color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vfy:hover {
  color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vfz:active {
  color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vg0 {
  color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vg1:hover {
  color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vg2:active {
  color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vg3 {
  color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vg4:hover {
  color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vg5:active {
  color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vg6 {
  color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vg7:hover {
  color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vg8:active {
  color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vg9 {
  color: var(--rk-colors-error);
}
[data-rk] .ju367vga:hover {
  color: var(--rk-colors-error);
}
[data-rk] .ju367vgb:active {
  color: var(--rk-colors-error);
}
[data-rk] .ju367vgc {
  color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vgd:hover {
  color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vge:active {
  color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vgf {
  color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vgg:hover {
  color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vgh:active {
  color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vgi {
  color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vgj:hover {
  color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vgk:active {
  color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vgl {
  color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vgm:hover {
  color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vgn:active {
  color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vgo {
  color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vgp:hover {
  color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vgq:active {
  color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vgr {
  color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vgs:hover {
  color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vgt:active {
  color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vgu {
  color: var(--rk-colors-modalText);
}
[data-rk] .ju367vgv:hover {
  color: var(--rk-colors-modalText);
}
[data-rk] .ju367vgw:active {
  color: var(--rk-colors-modalText);
}
[data-rk] .ju367vgx {
  color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vgy:hover {
  color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vgz:active {
  color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vh0 {
  color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vh1:hover {
  color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vh2:active {
  color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vh3 {
  color: var(--rk-colors-profileAction);
}
[data-rk] .ju367vh4:hover {
  color: var(--rk-colors-profileAction);
}
[data-rk] .ju367vh5:active {
  color: var(--rk-colors-profileAction);
}
[data-rk] .ju367vh6 {
  color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vh7:hover {
  color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vh8:active {
  color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vh9 {
  color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vha:hover {
  color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vhb:active {
  color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vhc {
  color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vhd:hover {
  color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vhe:active {
  color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vhf {
  color: var(--rk-colors-standby);
}
[data-rk] .ju367vhg:hover {
  color: var(--rk-colors-standby);
}
[data-rk] .ju367vhh:active {
  color: var(--rk-colors-standby);
}
@media screen and (min-width: 768px) {
  [data-rk] .ju367v1 {
    align-items: flex-start;
  }
  [data-rk] .ju367v3 {
    align-items: flex-end;
  }
  [data-rk] .ju367v5 {
    align-items: center;
  }
  [data-rk] .ju367v7 {
    display: none;
  }
  [data-rk] .ju367v9 {
    display: block;
  }
  [data-rk] .ju367vb {
    display: flex;
  }
  [data-rk] .ju367vd {
    display: inline;
  }
}

/* vanilla-extract-css-ns:src/css/touchableStyles.css.ts.vanilla.css?source=Ll8xMmNibzhpMywuXzEyY2JvOGkzOjphZnRlciB7CiAgLS1fMTJjYm84aTA6IDE7CiAgLS1fMTJjYm84aTE6IDE7Cn0KLl8xMmNibzhpMzpob3ZlciB7CiAgdHJhbnNmb3JtOiBzY2FsZSh2YXIoLS1fMTJjYm84aTApKTsKfQouXzEyY2JvOGkzOmFjdGl2ZSB7CiAgdHJhbnNmb3JtOiBzY2FsZSh2YXIoLS1fMTJjYm84aTEpKTsKfQouXzEyY2JvOGkzOmFjdGl2ZTo6YWZ0ZXIgewogIGNvbnRlbnQ6ICIiOwogIGJvdHRvbTogLTFweDsKICBkaXNwbGF5OiBibG9jazsKICBsZWZ0OiAtMXB4OwogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICByaWdodDogLTFweDsKICB0b3A6IC0xcHg7CiAgdHJhbnNmb3JtOiBzY2FsZShjYWxjKCgxIC8gdmFyKC0tXzEyY2JvOGkxKSkgKiB2YXIoLS1fMTJjYm84aTApKSk7Cn0KLl8xMmNibzhpNCwuXzEyY2JvOGk0OjphZnRlciB7CiAgLS1fMTJjYm84aTA6IDEuMDI1Owp9Ci5fMTJjYm84aTUsLl8xMmNibzhpNTo6YWZ0ZXIgewogIC0tXzEyY2JvOGkwOiAxLjE7Cn0KLl8xMmNibzhpNiwuXzEyY2JvOGk2OjphZnRlciB7CiAgLS1fMTJjYm84aTE6IDAuOTU7Cn0KLl8xMmNibzhpNywuXzEyY2JvOGk3OjphZnRlciB7CiAgLS1fMTJjYm84aTE6IDAuOTsKfQ== */
[data-rk] ._12cbo8i3,
[data-rk] ._12cbo8i3::after {
  --_12cbo8i0: 1;
  --_12cbo8i1: 1;
}
[data-rk] ._12cbo8i3:hover {
  transform: scale(var(--_12cbo8i0));
}
[data-rk] ._12cbo8i3:active {
  transform: scale(var(--_12cbo8i1));
}
[data-rk] ._12cbo8i3:active::after {
  content: "";
  bottom: -1px;
  display: block;
  left: -1px;
  position: absolute;
  right: -1px;
  top: -1px;
  transform: scale(calc((1 / var(--_12cbo8i1)) * var(--_12cbo8i0)));
}
[data-rk] ._12cbo8i4,
[data-rk] ._12cbo8i4::after {
  --_12cbo8i0: 1.025;
}
[data-rk] ._12cbo8i5,
[data-rk] ._12cbo8i5::after {
  --_12cbo8i0: 1.1;
}
[data-rk] ._12cbo8i6,
[data-rk] ._12cbo8i6::after {
  --_12cbo8i1: 0.95;
}
[data-rk] ._12cbo8i7,
[data-rk] ._12cbo8i7::after {
  --_12cbo8i1: 0.9;
}

/* vanilla-extract-css-ns:src/components/Icons/Icons.css.ts.vanilla.css?source=QGtleWZyYW1lcyBfMWx1dWxlNDEgewogIDAlIHsKICAgIHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOwogIH0KICAxMDAlIHsKICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7CiAgfQp9Ci5fMWx1dWxlNDIgewogIGFuaW1hdGlvbjogXzFsdXVsZTQxIDNzIGluZmluaXRlIGxpbmVhcjsKfQouXzFsdXVsZTQzIHsKICBiYWNrZ3JvdW5kOiBjb25pYy1ncmFkaWVudChmcm9tIDE4MGRlZyBhdCA1MCUgNTAlLCByZ2JhKDcyLCAxNDYsIDI1NCwgMCkgMGRlZywgY3VycmVudENvbG9yIDI4Mi4wNGRlZywgcmdiYSg3MiwgMTQ2LCAyNTQsIDApIDMxOS44NmRlZywgcmdiYSg3MiwgMTQ2LCAyNTQsIDApIDM2MGRlZyk7CiAgaGVpZ2h0OiAyMXB4OwogIHdpZHRoOiAyMXB4Owp9 */
@keyframes _1luule41 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
[data-rk] ._1luule42 {
  animation: _1luule41 3s infinite linear;
}
[data-rk] ._1luule43 {
  background:
    conic-gradient(
      from 180deg at 50% 50%,
      rgba(72, 146, 254, 0) 0deg,
      currentColor 282.04deg,
      rgba(72, 146, 254, 0) 319.86deg,
      rgba(72, 146, 254, 0) 360deg);
  height: 21px;
  width: 21px;
}

/* vanilla-extract-css-ns:src/components/Dialog/Dialog.css.ts.vanilla.css?source=QGtleWZyYW1lcyBfOXBtNGtpMCB7CiAgMCUgewogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDEwMCUpOwogIH0KICAxMDAlIHsKICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsKICB9Cn0KQGtleWZyYW1lcyBfOXBtNGtpMSB7CiAgMCUgewogICAgb3BhY2l0eTogMDsKICB9CiAgMTAwJSB7CiAgICBvcGFjaXR5OiAxOwogIH0KfQouXzlwbTRraTMgewogIGFuaW1hdGlvbjogXzlwbTRraTEgMTUwbXMgZWFzZTsKICBib3R0b206IC0yMDBweDsKICBsZWZ0OiAtMjAwcHg7CiAgcGFkZGluZzogMjAwcHg7CiAgcmlnaHQ6IC0yMDBweDsKICB0b3A6IC0yMDBweDsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVooMCk7CiAgei1pbmRleDogMjE0NzQ4MzY0NjsKfQouXzlwbTRraTUgewogIGFuaW1hdGlvbjogXzlwbTRraTAgMzUwbXMgY3ViaWMtYmV6aWVyKC4xNSwxLjE1LDAuNiwxLjAwKSwgXzlwbTRraTEgMTUwbXMgZWFzZTsKICBtYXgtd2lkdGg6IDEwMHZ3Owp9 */
@keyframes _9pm4ki0 {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes _9pm4ki1 {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
[data-rk] ._9pm4ki3 {
  animation: _9pm4ki1 150ms ease;
  bottom: -200px;
  left: -200px;
  padding: 200px;
  right: -200px;
  top: -200px;
  transform: translateZ(0);
  z-index: 2147483646;
}
[data-rk] ._9pm4ki5 {
  animation: _9pm4ki0 350ms cubic-bezier(.15, 1.15, 0.6, 1.00), _9pm4ki1 150ms ease;
  max-width: 100vw;
}

/* vanilla-extract-css-ns:src/components/Dialog/DialogContent.css.ts.vanilla.css?source=Ll8xY2tqcG9rMSB7CiAgYm94LXNpemluZzogY29udGVudC1ib3g7CiAgbWF4LXdpZHRoOiAxMDB2dzsKICB3aWR0aDogMzYwcHg7Cn0KLl8xY2tqcG9rMiB7CiAgd2lkdGg6IDEwMHZ3Owp9Ci5fMWNranBvazMgewogIG1pbi13aWR0aDogNzIwcHg7CiAgd2lkdGg6IDcyMHB4Owp9Ci5fMWNranBvazQgewogIG1pbi13aWR0aDogMzY4cHg7CiAgd2lkdGg6IDM2OHB4Owp9Ci5fMWNranBvazYgewogIGJvcmRlci13aWR0aDogMHB4OwogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgd2lkdGg6IDEwMHZ3Owp9CkBtZWRpYSBzY3JlZW4gYW5kIChtaW4td2lkdGg6IDc2OHB4KSB7CiAgLl8xY2tqcG9rMSB7CiAgICB3aWR0aDogMzYwcHg7CiAgfQogIC5fMWNranBvazIgewogICAgd2lkdGg6IDQ4MHB4OwogIH0KICAuXzFja2pwb2s0IHsKICAgIG1pbi13aWR0aDogMzY4cHg7CiAgICB3aWR0aDogMzY4cHg7CiAgfQp9CkBtZWRpYSBzY3JlZW4gYW5kIChtYXgtd2lkdGg6IDc2N3B4KSB7CiAgLl8xY2tqcG9rNyB7CiAgICBib3JkZXItYm90dG9tLWxlZnQtcmFkaXVzOiAwOwogICAgYm9yZGVyLWJvdHRvbS1yaWdodC1yYWRpdXM6IDA7CiAgICBtYXJnaW4tdG9wOiAtMjAwcHg7CiAgICBwYWRkaW5nLWJvdHRvbTogMjAwcHg7CiAgICB0b3A6IDIwMHB4OwogIH0KfQ== */
[data-rk] ._1ckjpok1 {
  box-sizing: content-box;
  max-width: 100vw;
  width: 360px;
}
[data-rk] ._1ckjpok2 {
  width: 100vw;
}
[data-rk] ._1ckjpok3 {
  min-width: 720px;
  width: 720px;
}
[data-rk] ._1ckjpok4 {
  min-width: 368px;
  width: 368px;
}
[data-rk] ._1ckjpok6 {
  border-width: 0px;
  box-sizing: border-box;
  width: 100vw;
}
@media screen and (min-width: 768px) {
  [data-rk] ._1ckjpok1 {
    width: 360px;
  }
  [data-rk] ._1ckjpok2 {
    width: 480px;
  }
  [data-rk] ._1ckjpok4 {
    min-width: 368px;
    width: 368px;
  }
}
@media screen and (max-width: 767px) {
  [data-rk] ._1ckjpok7 {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    margin-top: -200px;
    padding-bottom: 200px;
    top: 200px;
  }
}

/* vanilla-extract-css-ns:src/components/MenuButton/MenuButton.css.ts.vanilla.css?source=LnY5aG9yYjA6aG92ZXIgewogIGJhY2tncm91bmQ6IHVuc2V0Owp9 */
[data-rk] .v9horb0:hover {
  background: unset;
}

/* vanilla-extract-css-ns:src/components/ChainModal/ChainModal.css.ts.vanilla.css?source=Ll8xOGRxdzl4MCB7CiAgbWF4LWhlaWdodDogNDU2cHg7CiAgb3ZlcmZsb3cteTogYXV0bzsKICBvdmVyZmxvdy14OiBoaWRkZW47Cn0KLl8xOGRxdzl4MSB7CiAgbWF4LWhlaWdodDogNDU2cHg7CiAgb3ZlcmZsb3cteTogYXV0bzsKICBvdmVyZmxvdy14OiBoaWRkZW47CiAgc2Nyb2xsYmFyLXdpZHRoOiBub25lOwp9Ci5fMThkcXc5eDE6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICBkaXNwbGF5OiBub25lOwp9 */
[data-rk] ._18dqw9x0 {
  max-height: 456px;
  overflow-y: auto;
  overflow-x: hidden;
}
[data-rk] ._18dqw9x1 {
  max-height: 456px;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
}
[data-rk] ._18dqw9x1::-webkit-scrollbar {
  display: none;
}

/* vanilla-extract-css-ns:src/components/ModalSelection/ModalSelection.css.ts.vanilla.css?source=Lmc1a2wwbDAgewogIGJvcmRlci1jb2xvcjogdHJhbnNwYXJlbnQ7Cn0= */
[data-rk] .g5kl0l0 {
  border-color: transparent;
}

/* vanilla-extract-css-ns:src/components/ConnectOptions/DesktopOptions.css.ts.vanilla.css?source=Ll8xdnd0MGNnMCB7CiAgYmFja2dyb3VuZDogd2hpdGU7Cn0KLl8xdnd0MGNnMiB7CiAgbWF4LWhlaWdodDogNDU0cHg7CiAgb3ZlcmZsb3cteTogYXV0bzsKfQouXzF2d3QwY2czIHsKICBtaW4td2lkdGg6IDI4N3B4Owp9Ci5fMXZ3dDBjZzQgewogIG1pbi13aWR0aDogMTAwJTsKfQ== */
[data-rk] ._1vwt0cg0 {
  background: white;
}
[data-rk] ._1vwt0cg2 {
  max-height: 454px;
  overflow-y: auto;
}
[data-rk] ._1vwt0cg3 {
  min-width: 287px;
}
[data-rk] ._1vwt0cg4 {
  min-width: 100%;
}

/* vanilla-extract-css-ns:src/components/ConnectOptions/MobileOptions.css.ts.vanilla.css?source=QGtleWZyYW1lcyBfMWFtMTQ0MTEgewogIDAlIHsKICAgIHN0cm9rZS1kYXNob2Zmc2V0OiAwOwogIH0KICAxMDAlIHsKICAgIHN0cm9rZS1kYXNob2Zmc2V0OiAtMjgzOwogIH0KfQouXzFhbTE0NDEwIHsKICBvdmVyZmxvdzogYXV0bzsKICBzY3JvbGxiYXItd2lkdGg6IG5vbmU7CiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVaKDApOwp9Ci5fMWFtMTQ0MTA6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICBkaXNwbGF5OiBub25lOwp9Ci5fMWFtMTQ0MTIgewogIGFuaW1hdGlvbjogXzFhbTE0NDExIDFzIGxpbmVhciBpbmZpbml0ZTsKICBzdHJva2UtZGFzaGFycmF5OiA5OCAxOTY7CiAgZmlsbDogbm9uZTsKICBzdHJva2UtbGluZWNhcDogcm91bmQ7CiAgc3Ryb2tlLXdpZHRoOiA0Owp9Ci5fMWFtMTQ0MTMgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKfQ== */
@keyframes _1am14411 {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -283;
  }
}
[data-rk] ._1am14410 {
  overflow: auto;
  scrollbar-width: none;
  transform: translateZ(0);
}
[data-rk] ._1am14410::-webkit-scrollbar {
  display: none;
}
[data-rk] ._1am14412 {
  animation: _1am14411 1s linear infinite;
  stroke-dasharray: 98 196;
  fill: none;
  stroke-linecap: round;
  stroke-width: 4;
}
[data-rk] ._1am14413 {
  position: absolute;
}

/* vanilla-extract-css-ns:src/components/WalletButton/WalletButton.css.ts.vanilla.css?source=Ll8xeTJsbmZpMCB7CiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgxNiwgMjEsIDMxLCAwLjA2KTsKfQouXzF5MmxuZmkxIHsKICBtYXgtd2lkdGg6IGZpdC1jb250ZW50Owp9 */
[data-rk] ._1y2lnfi0 {
  border: 1px solid rgba(16, 21, 31, 0.06);
}
[data-rk] ._1y2lnfi1 {
  max-width: -moz-fit-content;
  max-width: fit-content;
}

