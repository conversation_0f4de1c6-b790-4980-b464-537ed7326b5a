"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_facebook_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/facebook.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/facebook.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   facebookSvg: () => (/* binding */ facebookSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst facebookSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 40 40\">\n  <g clip-path=\"url(#a)\">\n    <g clip-path=\"url(#b)\">\n      <circle cx=\"20\" cy=\"19.89\" r=\"20\" fill=\"#1877F2\" />\n      <g clip-path=\"url(#c)\">\n        <path\n          fill=\"#fff\"\n          d=\"M26 12.38h-2.89c-.92 0-1.61.38-1.61 1.34v1.66H26l-.36 4.5H21.5v12H17v-12h-3v-4.5h3V12.5c0-3.03 1.6-4.62 5.2-4.62H26v4.5Z\"\n        />\n      </g>\n    </g>\n    <path\n      fill=\"#1877F2\"\n      d=\"M40 20a20 20 0 1 0-23.13 19.76V25.78H11.8V20h5.07v-4.4c0-5.02 3-7.79 7.56-7.79 2.19 0 4.48.4 4.48.4v4.91h-2.53c-2.48 0-3.25 1.55-3.25 3.13V20h5.54l-.88 5.78h-4.66v13.98A20 20 0 0 0 40 20Z\"\n    />\n    <path\n      fill=\"#fff\"\n      d=\"m27.79 25.78.88-5.78h-5.55v-3.75c0-1.58.78-3.13 3.26-3.13h2.53V8.2s-2.3-.39-4.48-.39c-4.57 0-7.55 2.77-7.55 7.78V20H11.8v5.78h5.07v13.98a20.15 20.15 0 0 0 6.25 0V25.78h4.67Z\"\n    />\n  </g>\n  <defs>\n    <clipPath id=\"a\"><rect width=\"40\" height=\"40\" fill=\"#fff\" rx=\"20\" /></clipPath>\n    <clipPath id=\"b\"><path fill=\"#fff\" d=\"M0 0h40v40H0z\" /></clipPath>\n    <clipPath id=\"c\"><path fill=\"#fff\" d=\"M8 7.89h24v24H8z\" /></clipPath>\n  </defs>\n</svg>`;\n//# sourceMappingURL=facebook.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/facebook.js\n"));

/***/ })

}]);