"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_rainbow-me_rainbowkit_dist_unichain-C5BWO2ZY_js"],{

/***/ "(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/unichain-C5BWO2ZY.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@rainbow-me/rainbowkit/dist/unichain-C5BWO2ZY.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ unichain_default)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ default auto */ // src/components/RainbowKitProvider/chainIcons/unichain.svg\nvar unichain_default = \"data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22none%22%20viewBox%3D%220%200%2028%2028%22%3E%3Cg%20transform%3D%22translate(0%2C0)%20scale(0.7)%22%3E%3Cg%20clip-path%3D%22url(%23a)%22%3E%3Cpath%20fill%3D%22url(%23b)%22%20d%3D%22M0%200h40v40H0z%22%2F%3E%3Cpath%20fill%3D%22%23fff%22%20d%3D%22M36%2019.696c-8.672%200-15.696-7.03-15.696-15.696h-.608v15.696H4v.608c8.673%200%2015.696%207.03%2015.696%2015.696h.608V20.304H36v-.608Z%22%2F%3E%3C%2Fg%3E%3Cdefs%3E%3CradialGradient%20id%3D%22b%22%20cx%3D%220%22%20cy%3D%220%22%20r%3D%221%22%20gradientTransform%3D%22matrix(0%2020%20-20%200%2020%2020)%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20stop-color%3D%22%23FC74FE%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23F50DB4%22%2F%3E%3C%2FradialGradient%3E%3CclipPath%20id%3D%22a%22%3E%3Cpath%20fill%3D%22%23fff%22%20d%3D%22M0%200h40v40H0z%22%2F%3E%3C%2FclipPath%3E%3C%2Fdefs%3E%3C%2Fg%3E%3C%2Fsvg%3E\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmFpbmJvdy1tZS9yYWluYm93a2l0L2Rpc3QvdW5pY2hhaW4tQzVCV08yWlkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs2REFFQSw0REFBNEQ7QUFDNUQsSUFBSUEsbUJBQW1CO0FBR3JCIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0ByYWluYm93LW1lL3JhaW5ib3draXQvZGlzdC91bmljaGFpbi1DNUJXTzJaWS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuLy8gc3JjL2NvbXBvbmVudHMvUmFpbmJvd0tpdFByb3ZpZGVyL2NoYWluSWNvbnMvdW5pY2hhaW4uc3ZnXG52YXIgdW5pY2hhaW5fZGVmYXVsdCA9IFwiZGF0YTppbWFnZS9zdmcreG1sLCUzQ3N2ZyUyMHhtbG5zJTNEJTIyaHR0cCUzQSUyRiUyRnd3dy53My5vcmclMkYyMDAwJTJGc3ZnJTIyJTIwZmlsbCUzRCUyMm5vbmUlMjIlMjB2aWV3Qm94JTNEJTIyMCUyMDAlMjAyOCUyMDI4JTIyJTNFJTNDZyUyMHRyYW5zZm9ybSUzRCUyMnRyYW5zbGF0ZSgwJTJDMCklMjBzY2FsZSgwLjcpJTIyJTNFJTNDZyUyMGNsaXAtcGF0aCUzRCUyMnVybCglMjNhKSUyMiUzRSUzQ3BhdGglMjBmaWxsJTNEJTIydXJsKCUyM2IpJTIyJTIwZCUzRCUyMk0wJTIwMGg0MHY0MEgweiUyMiUyRiUzRSUzQ3BhdGglMjBmaWxsJTNEJTIyJTIzZmZmJTIyJTIwZCUzRCUyMk0zNiUyMDE5LjY5NmMtOC42NzIlMjAwLTE1LjY5Ni03LjAzLTE1LjY5Ni0xNS42OTZoLS42MDh2MTUuNjk2SDR2LjYwOGM4LjY3MyUyMDAlMjAxNS42OTYlMjA3LjAzJTIwMTUuNjk2JTIwMTUuNjk2aC42MDhWMjAuMzA0SDM2di0uNjA4WiUyMiUyRiUzRSUzQyUyRmclM0UlM0NkZWZzJTNFJTNDcmFkaWFsR3JhZGllbnQlMjBpZCUzRCUyMmIlMjIlMjBjeCUzRCUyMjAlMjIlMjBjeSUzRCUyMjAlMjIlMjByJTNEJTIyMSUyMiUyMGdyYWRpZW50VHJhbnNmb3JtJTNEJTIybWF0cml4KDAlMjAyMCUyMC0yMCUyMDAlMjAyMCUyMDIwKSUyMiUyMGdyYWRpZW50VW5pdHMlM0QlMjJ1c2VyU3BhY2VPblVzZSUyMiUzRSUzQ3N0b3AlMjBzdG9wLWNvbG9yJTNEJTIyJTIzRkM3NEZFJTIyJTJGJTNFJTNDc3RvcCUyMG9mZnNldCUzRCUyMjElMjIlMjBzdG9wLWNvbG9yJTNEJTIyJTIzRjUwREI0JTIyJTJGJTNFJTNDJTJGcmFkaWFsR3JhZGllbnQlM0UlM0NjbGlwUGF0aCUyMGlkJTNEJTIyYSUyMiUzRSUzQ3BhdGglMjBmaWxsJTNEJTIyJTIzZmZmJTIyJTIwZCUzRCUyMk0wJTIwMGg0MHY0MEgweiUyMiUyRiUzRSUzQyUyRmNsaXBQYXRoJTNFJTNDJTJGZGVmcyUzRSUzQyUyRmclM0UlM0MlMkZzdmclM0VcIjtcbmV4cG9ydCB7XG4gIHVuaWNoYWluX2RlZmF1bHQgYXMgZGVmYXVsdFxufTtcbiJdLCJuYW1lcyI6WyJ1bmljaGFpbl9kZWZhdWx0IiwiZGVmYXVsdCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/unichain-C5BWO2ZY.js\n"));

/***/ })

}]);