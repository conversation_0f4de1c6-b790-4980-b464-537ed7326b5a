"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_cursor_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cursorSvg: () => (/* binding */ cursorSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst cursorSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) ` <svg fill=\"none\" viewBox=\"0 0 13 4\">\n  <path fill=\"currentColor\" d=\"M.5 0h12L8.9 3.13a3.76 3.76 0 0 1-4.8 0L.5 0Z\" />\n</svg>`;\n//# sourceMappingURL=cursor.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL2N1cnNvci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUNuQixrQkFBa0Isd0NBQUc7QUFDNUI7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0ByZW93bi9hcHBraXQtdWkvZGlzdC9lc20vc3JjL2Fzc2V0cy9zdmcvY3Vyc29yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3QgY3Vyc29yU3ZnID0gc3ZnIGAgPHN2ZyBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMTMgNFwiPlxuICA8cGF0aCBmaWxsPVwiY3VycmVudENvbG9yXCIgZD1cIk0uNSAwaDEyTDguOSAzLjEzYTMuNzYgMy43NiAwIDAgMS00LjggMEwuNSAwWlwiIC8+XG48L3N2Zz5gO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y3Vyc29yLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor.js\n"));

/***/ })

}]);