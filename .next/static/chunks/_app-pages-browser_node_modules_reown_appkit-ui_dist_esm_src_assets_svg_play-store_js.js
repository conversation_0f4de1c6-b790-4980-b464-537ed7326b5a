"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_play-store_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/play-store.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/play-store.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   playStoreSvg: () => (/* binding */ playStoreSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst playStoreSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) ` <svg\n  width=\"36\"\n  height=\"36\"\n  fill=\"none\"\n>\n  <path\n    d=\"M0 8a8 8 0 0 1 8-8h20a8 8 0 0 1 8 8v20a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z\"\n    fill=\"#fff\"\n    fill-opacity=\".05\"\n  />\n  <path\n    d=\"m18.262 17.513-8.944 9.49v.01a2.417 2.417 0 0 0 3.56 1.452l.026-.017 10.061-5.803-4.703-5.132Z\"\n    fill=\"#EA4335\"\n  />\n  <path\n    d=\"m27.307 15.9-.008-.008-4.342-2.52-4.896 4.36 4.913 4.912 4.325-2.494a2.42 2.42 0 0 0 .008-4.25Z\"\n    fill=\"#FBBC04\"\n  />\n  <path\n    d=\"M9.318 8.997c-.05.202-.084.403-.084.622V26.39c0 .218.025.42.084.621l9.246-9.247-9.246-8.768Z\"\n    fill=\"#4285F4\"\n  />\n  <path\n    d=\"m18.33 18 4.627-4.628-10.053-5.828a2.427 2.427 0 0 0-3.586 1.444L18.329 18Z\"\n    fill=\"#34A853\"\n  />\n  <path\n    d=\"M8 .5h20A7.5 7.5 0 0 1 35.5 8v20a7.5 7.5 0 0 1-7.5 7.5H8A7.5 7.5 0 0 1 .5 28V8A7.5 7.5 0 0 1 8 .5Z\"\n    stroke=\"#fff\"\n    stroke-opacity=\".05\"\n  />\n</svg>`;\n//# sourceMappingURL=play-store.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL3BsYXktc3RvcmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEI7QUFDbkIscUJBQXFCLHdDQUFHO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy9wbGF5LXN0b3JlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3QgcGxheVN0b3JlU3ZnID0gc3ZnIGAgPHN2Z1xuICB3aWR0aD1cIjM2XCJcbiAgaGVpZ2h0PVwiMzZcIlxuICBmaWxsPVwibm9uZVwiXG4+XG4gIDxwYXRoXG4gICAgZD1cIk0wIDhhOCA4IDAgMCAxIDgtOGgyMGE4IDggMCAwIDEgOCA4djIwYTggOCAwIDAgMS04IDhIOGE4IDggMCAwIDEtOC04VjhaXCJcbiAgICBmaWxsPVwiI2ZmZlwiXG4gICAgZmlsbC1vcGFjaXR5PVwiLjA1XCJcbiAgLz5cbiAgPHBhdGhcbiAgICBkPVwibTE4LjI2MiAxNy41MTMtOC45NDQgOS40OXYuMDFhMi40MTcgMi40MTcgMCAwIDAgMy41NiAxLjQ1MmwuMDI2LS4wMTcgMTAuMDYxLTUuODAzLTQuNzAzLTUuMTMyWlwiXG4gICAgZmlsbD1cIiNFQTQzMzVcIlxuICAvPlxuICA8cGF0aFxuICAgIGQ9XCJtMjcuMzA3IDE1LjktLjAwOC0uMDA4LTQuMzQyLTIuNTItNC44OTYgNC4zNiA0LjkxMyA0LjkxMiA0LjMyNS0yLjQ5NGEyLjQyIDIuNDIgMCAwIDAgLjAwOC00LjI1WlwiXG4gICAgZmlsbD1cIiNGQkJDMDRcIlxuICAvPlxuICA8cGF0aFxuICAgIGQ9XCJNOS4zMTggOC45OTdjLS4wNS4yMDItLjA4NC40MDMtLjA4NC42MjJWMjYuMzljMCAuMjE4LjAyNS40Mi4wODQuNjIxbDkuMjQ2LTkuMjQ3LTkuMjQ2LTguNzY4WlwiXG4gICAgZmlsbD1cIiM0Mjg1RjRcIlxuICAvPlxuICA8cGF0aFxuICAgIGQ9XCJtMTguMzMgMTggNC42MjctNC42MjgtMTAuMDUzLTUuODI4YTIuNDI3IDIuNDI3IDAgMCAwLTMuNTg2IDEuNDQ0TDE4LjMyOSAxOFpcIlxuICAgIGZpbGw9XCIjMzRBODUzXCJcbiAgLz5cbiAgPHBhdGhcbiAgICBkPVwiTTggLjVoMjBBNy41IDcuNSAwIDAgMSAzNS41IDh2MjBhNy41IDcuNSAwIDAgMS03LjUgNy41SDhBNy41IDcuNSAwIDAgMSAuNSAyOFY4QTcuNSA3LjUgMCAwIDEgOCAuNVpcIlxuICAgIHN0cm9rZT1cIiNmZmZcIlxuICAgIHN0cm9rZS1vcGFjaXR5PVwiLjA1XCJcbiAgLz5cbjwvc3ZnPmA7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wbGF5LXN0b3JlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/play-store.js\n"));

/***/ })

}]);