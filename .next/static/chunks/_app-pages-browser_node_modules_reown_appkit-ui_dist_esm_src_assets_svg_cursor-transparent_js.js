"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_cursor-transparent_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor-transparent.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor-transparent.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cursorTransparentSvg: () => (/* binding */ cursorTransparentSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst cursorTransparentSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 14 6\">\n  <path style=\"fill: var(--wui-color-bg-150);\" d=\"M0 1h14L9.21 5.12a3.31 3.31 0 0 1-4.49 0L0 1Z\" />\n  <path\n    style=\"stroke: var(--wui-color-inverse-100);\"\n    stroke-opacity=\".05\"\n    d=\"M1.33 1.5h11.32L8.88 4.75l-.01.01a2.81 2.81 0 0 1-3.8 0l-.02-.01L1.33 1.5Z\"\n  />\n  <path\n    style=\"fill: var(--wui-color-bg-150);\"\n    d=\"M1.25.71h11.5L9.21 3.88a3.31 3.31 0 0 1-4.49 0L1.25.71Z\"\n  />\n</svg> `;\n//# sourceMappingURL=cursor-transparent.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL2N1cnNvci10cmFuc3BhcmVudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUNuQiw2QkFBNkIsd0NBQUc7QUFDdkMsNkNBQTZDO0FBQzdDO0FBQ0EsZ0RBQWdEO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0ByZW93bi9hcHBraXQtdWkvZGlzdC9lc20vc3JjL2Fzc2V0cy9zdmcvY3Vyc29yLXRyYW5zcGFyZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3QgY3Vyc29yVHJhbnNwYXJlbnRTdmcgPSBzdmcgYDxzdmcgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDE0IDZcIj5cbiAgPHBhdGggc3R5bGU9XCJmaWxsOiB2YXIoLS13dWktY29sb3ItYmctMTUwKTtcIiBkPVwiTTAgMWgxNEw5LjIxIDUuMTJhMy4zMSAzLjMxIDAgMCAxLTQuNDkgMEwwIDFaXCIgLz5cbiAgPHBhdGhcbiAgICBzdHlsZT1cInN0cm9rZTogdmFyKC0td3VpLWNvbG9yLWludmVyc2UtMTAwKTtcIlxuICAgIHN0cm9rZS1vcGFjaXR5PVwiLjA1XCJcbiAgICBkPVwiTTEuMzMgMS41aDExLjMyTDguODggNC43NWwtLjAxLjAxYTIuODEgMi44MSAwIDAgMS0zLjggMGwtLjAyLS4wMUwxLjMzIDEuNVpcIlxuICAvPlxuICA8cGF0aFxuICAgIHN0eWxlPVwiZmlsbDogdmFyKC0td3VpLWNvbG9yLWJnLTE1MCk7XCJcbiAgICBkPVwiTTEuMjUuNzFoMTEuNUw5LjIxIDMuODhhMy4zMSAzLjMxIDAgMCAxLTQuNDkgMEwxLjI1LjcxWlwiXG4gIC8+XG48L3N2Zz4gYDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWN1cnNvci10cmFuc3BhcmVudC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor-transparent.js\n"));

/***/ })

}]);