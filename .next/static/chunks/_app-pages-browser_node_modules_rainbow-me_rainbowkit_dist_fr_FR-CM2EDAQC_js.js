"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_rainbow-me_rainbowkit_dist_fr_FR-CM2EDAQC_js"],{

/***/ "(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/fr_FR-CM2EDAQC.js":
/*!********************************************************************!*\
  !*** ./node_modules/@rainbow-me/rainbowkit/dist/fr_FR-CM2EDAQC.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ fr_FR_default)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ default auto */ // src/locales/fr_FR.json\nvar fr_FR_default = '{\\n  \"connect_wallet\": {\\n    \"label\": \"Connecter le portefeuille\",\\n    \"wrong_network\": {\\n      \"label\": \"R\\xe9seau incorrect\"\\n    }\\n  },\\n  \"intro\": {\\n    \"title\": \"Qu\\'est-ce qu\\'un portefeuille?\",\\n    \"description\": \"Un portefeuille est utilis\\xe9 pour envoyer, recevoir, stocker et afficher des actifs num\\xe9riques. C\\'est aussi une nouvelle fa\\xe7on de se connecter, sans avoir besoin de cr\\xe9er de nouveaux comptes et mots de passe sur chaque site.\",\\n    \"digital_asset\": {\\n      \"title\": \"Un foyer pour vos actifs num\\xe9riques\",\\n      \"description\": \"Les portefeuilles sont utilis\\xe9s pour envoyer, recevoir, stocker et afficher des actifs num\\xe9riques comme Ethereum et les NFTs.\"\\n    },\\n    \"login\": {\\n      \"title\": \"Une nouvelle fa\\xe7on de se connecter\",\\n      \"description\": \"Au lieu de cr\\xe9er de nouveaux comptes et mots de passe sur chaque site Web, connectez simplement votre portefeuille.\"\\n    },\\n    \"get\": {\\n      \"label\": \"Obtenir un portefeuille\"\\n    },\\n    \"learn_more\": {\\n      \"label\": \"En savoir plus\"\\n    }\\n  },\\n  \"sign_in\": {\\n    \"label\": \"V\\xe9rifiez votre compte\",\\n    \"description\": \"Pour terminer la connexion, vous devez signer un message dans votre portefeuille pour v\\xe9rifier que vous \\xeates le propri\\xe9taire de ce compte.\",\\n    \"message\": {\\n      \"send\": \"Envoyer le message\",\\n      \"preparing\": \"Pr\\xe9paration du message...\",\\n      \"cancel\": \"Annuler\",\\n      \"preparing_error\": \"Erreur lors de la pr\\xe9paration du message, veuillez r\\xe9essayer!\"\\n    },\\n    \"signature\": {\\n      \"waiting\": \"En attente de la signature...\",\\n      \"verifying\": \"V\\xe9rification de la signature...\",\\n      \"signing_error\": \"Erreur lors de la signature du message, veuillez r\\xe9essayer!\",\\n      \"verifying_error\": \"Erreur lors de la v\\xe9rification de la signature, veuillez r\\xe9essayer!\",\\n      \"oops_error\": \"Oups, quelque chose a mal tourn\\xe9!\"\\n    }\\n  },\\n  \"connect\": {\\n    \"label\": \"Connecter\",\\n    \"title\": \"Connecter un portefeuille\",\\n    \"new_to_ethereum\": {\\n      \"description\": \"Nouveau aux portefeuilles Ethereum?\",\\n      \"learn_more\": {\\n        \"label\": \"En savoir plus\"\\n      }\\n    },\\n    \"learn_more\": {\\n      \"label\": \"En savoir plus\"\\n    },\\n    \"recent\": \"R\\xe9cents\",\\n    \"status\": {\\n      \"opening\": \"Ouverture %{wallet}...\",\\n      \"connecting\": \"Connect :)ing\",\\n      \"connect_mobile\": \"Continuer dans %{wallet}\",\\n      \"not_installed\": \"%{wallet} n\\'est pas install\\xe9\",\\n      \"not_available\": \"%{wallet} n\\'est pas disponible\",\\n      \"confirm\": \"Confirmez la connexion dans l\\'extension\",\\n      \"confirm_mobile\": \"Accepter la demande de connexion dans le portefeuille\"\\n    },\\n    \"secondary_action\": {\\n      \"get\": {\\n        \"description\": \"Vous n\\'avez pas de %{wallet}?\",\\n        \"label\": \"OBTENIR\"\\n      },\\n      \"install\": {\\n        \"label\": \"INSTALLER\"\\n      },\\n      \"retry\": {\\n        \"label\": \"R\\xc9ESSAYER\"\\n      }\\n    },\\n    \"walletconnect\": {\\n      \"description\": {\\n        \"full\": \"Vous avez besoin du modal officiel de WalletConnect ?\",\\n        \"compact\": \"Besoin du modal de WalletConnect ?\"\\n      },\\n      \"open\": {\\n        \"label\": \"OUVRIR\"\\n      }\\n    }\\n  },\\n  \"connect_scan\": {\\n    \"title\": \"Scannez avec %{wallet}\",\\n    \"fallback_title\": \"Scannez avec votre t\\xe9l\\xe9phone\"\\n  },\\n  \"connector_group\": {\\n    \"installed\": \"Install\\xe9\",\\n    \"recommended\": \"Recommand\\xe9\",\\n    \"other\": \"Autre\",\\n    \"popular\": \"Populaire\",\\n    \"more\": \"Plus\",\\n    \"others\": \"Autres\"\\n  },\\n  \"get\": {\\n    \"title\": \"Obtenez un portefeuille\",\\n    \"action\": {\\n      \"label\": \"OBTENIR\"\\n    },\\n    \"mobile\": {\\n      \"description\": \"Portefeuille mobile\"\\n    },\\n    \"extension\": {\\n      \"description\": \"Extension de navigateur\"\\n    },\\n    \"mobile_and_extension\": {\\n      \"description\": \"Portefeuille mobile et extension\"\\n    },\\n    \"mobile_and_desktop\": {\\n      \"description\": \"Portefeuille mobile et de bureau\"\\n    },\\n    \"looking_for\": {\\n      \"title\": \"Ce n\\'est pas ce que vous cherchez ?\",\\n      \"mobile\": {\\n        \"description\": \"S\\xe9lectionnez un portefeuille sur l\\'\\xe9cran principal pour commencer avec un autre fournisseur de portefeuille.\"\\n      },\\n      \"desktop\": {\\n        \"compact_description\": \"S\\xe9lectionnez un portefeuille sur l\\'\\xe9cran principal pour commencer avec un autre fournisseur de portefeuille.\",\\n        \"wide_description\": \"S\\xe9lectionnez un portefeuille sur la gauche pour commencer avec un autre fournisseur de portefeuille.\"\\n      }\\n    }\\n  },\\n  \"get_options\": {\\n    \"title\": \"Commencez avec %{wallet}\",\\n    \"short_title\": \"Obtenez %{wallet}\",\\n    \"mobile\": {\\n      \"title\": \"%{wallet} pour mobile\",\\n      \"description\": \"Utilisez le portefeuille mobile pour explorer le monde d\\'Ethereum.\",\\n      \"download\": {\\n        \"label\": \"Obtenez l\\'application\"\\n      }\\n    },\\n    \"extension\": {\\n      \"title\": \"%{wallet} pour %{browser}\",\\n      \"description\": \"Acc\\xe9dez \\xe0 votre portefeuille directement depuis votre navigateur web pr\\xe9f\\xe9r\\xe9.\",\\n      \"download\": {\\n        \"label\": \"Ajouter \\xe0 %{browser}\"\\n      }\\n    },\\n    \"desktop\": {\\n      \"title\": \"%{wallet} pour %{platform}\",\\n      \"description\": \"Acc\\xe9dez \\xe0 votre portefeuille nativement depuis votre puissant ordinateur de bureau.\",\\n      \"download\": {\\n        \"label\": \"Ajouter \\xe0 %{platform}\"\\n      }\\n    }\\n  },\\n  \"get_mobile\": {\\n    \"title\": \"Installer %{wallet}\",\\n    \"description\": \"Scannez avec votre t\\xe9l\\xe9phone pour t\\xe9l\\xe9charger sur iOS ou Android\",\\n    \"continue\": {\\n      \"label\": \"Continuer\"\\n    }\\n  },\\n  \"get_instructions\": {\\n    \"mobile\": {\\n      \"connect\": {\\n        \"label\": \"Connecter\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"En savoir plus\"\\n      }\\n    },\\n    \"extension\": {\\n      \"refresh\": {\\n        \"label\": \"Rafra\\xeechir\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"En savoir plus\"\\n      }\\n    },\\n    \"desktop\": {\\n      \"connect\": {\\n        \"label\": \"Connecter\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"En savoir plus\"\\n      }\\n    }\\n  },\\n  \"chains\": {\\n    \"title\": \"Changer de r\\xe9seau\",\\n    \"wrong_network\": \"Mauvais r\\xe9seau d\\xe9tect\\xe9, changez ou d\\xe9connectez-vous pour continuer.\",\\n    \"confirm\": \"Confirmer dans le portefeuille\",\\n    \"switching_not_supported\": \"Votre portefeuille ne supporte pas le changement de r\\xe9seau depuis %{appName}. Essayez de changer de r\\xe9seau depuis votre portefeuille.\",\\n    \"switching_not_supported_fallback\": \"Votre portefeuille ne prend pas en charge le changement de r\\xe9seau \\xe0 partir de cette application. Essayez de changer de r\\xe9seau \\xe0 partir de votre portefeuille \\xe0 la place.\",\\n    \"disconnect\": \"D\\xe9connecter\",\\n    \"connected\": \"Connect\\xe9\"\\n  },\\n  \"profile\": {\\n    \"disconnect\": {\\n      \"label\": \"D\\xe9connecter\"\\n    },\\n    \"copy_address\": {\\n      \"label\": \"Copier l\\'adresse\",\\n      \"copied\": \"Copi\\xe9 !\"\\n    },\\n    \"explorer\": {\\n      \"label\": \"Voir plus sur l\\'explorateur\"\\n    },\\n    \"transactions\": {\\n      \"description\": \"%{appName} transactions appara\\xeetront ici...\",\\n      \"description_fallback\": \"Vos transactions appara\\xeetront ici...\",\\n      \"recent\": {\\n        \"title\": \"Transactions R\\xe9centes\"\\n      },\\n      \"clear\": {\\n        \"label\": \"Tout supprimer\"\\n      }\\n    }\\n  },\\n  \"wallet_connectors\": {\\n    \"argent\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Mettez Argent sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Ouvrez l\\'application Argent\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Cr\\xe9ez un portefeuille et un nom d\\'utilisateur, ou importez un portefeuille existant.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton Scan QR\"\\n        }\\n      }\\n    },\\n    \"berasig\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension BeraSig\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler BeraSig \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus facile \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"best\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Best Wallet\",\\n          \"description\": \"Ajoutez Best Wallet \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Touchez l\\'ic\\xf4ne QR et scannez\",\\n          \"description\": \"Touchez l\\'ic\\xf4ne QR sur votre \\xe9cran d\\'accueil, scannez le code et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"bifrost\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons de mettre le portefeuille Bifrost sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\",\\n          \"title\": \"Ouvrez l\\'application Bifrost Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Cr\\xe9ez ou importez un portefeuille en utilisant votre phrase de r\\xe9cup\\xe9ration.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s votre scan, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton de scan\"\\n        }\\n      }\\n    },\\n    \"bitget\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons de placer Bitget Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\",\\n          \"title\": \"Ouvrez l\\'application Bitget Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s le scan, une incitation de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton de scan\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Bitget Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension de portefeuille Bitget\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec personne.\",\\n          \"title\": \"Cr\\xe9ez ou Importez un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"bitski\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Bitski \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Bitski\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec qui que ce soit.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"bitverse\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Bitverse Wallet\",\\n          \"description\": \"Ajoutez Bitverse Wallet \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Touchez l\\'ic\\xf4ne QR et scannez\",\\n          \"description\": \"Touchez l\\'ic\\xf4ne QR sur votre \\xe9cran d\\'accueil, scannez le code et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"bloom\": {\\n      \"desktop\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Bloom Wallet\",\\n          \"description\": \"Nous recommandons de placer Bloom Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Cr\\xe9ez ou importez un portefeuille en utilisant votre phrase de r\\xe9cup\\xe9ration.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir obtenu un portefeuille, cliquez sur Connecter pour vous connecter via Bloom. Une invite de connexion appara\\xeetra dans l\\'application pour que vous confirmiez la connexion.\",\\n          \"title\": \"Cliquez sur Connecter\"\\n        }\\n      }\\n    },\\n    \"bybit\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons de placer Bybit sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Ouvrez l\\'application Bybit\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Vous pouvez facilement sauvegarder votre portefeuille en utilisant notre fonction de sauvegarde sur votre t\\xe9l\\xe9phone.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton de scan\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Cliquez en haut \\xe0 droite de votre navigateur et \\xe9pinglez le portefeuille Bybit pour un acc\\xe8s facile.\",\\n          \"title\": \"Installez l\\'extension Bybit Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 Bybit Wallet, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"binance\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons de mettre Binance sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Ouvrez l\\'application Binance\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Vous pouvez facilement sauvegarder votre portefeuille en utilisant notre fonction de sauvegarde sur votre t\\xe9l\\xe9phone.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton WalletConnect\"\\n        }\\n      }\\n    },\\n    \"coin98\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons de placer Coin98 Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Ouvrez l\\'application Coin98 Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Vous pouvez facilement sauvegarder votre portefeuille en utilisant notre fonction de sauvegarde sur votre t\\xe9l\\xe9phone.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s que vous ayez scann\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton WalletConnect\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Cliquez en haut \\xe0 droite de votre navigateur et \\xe9pinglez Coin98 Wallet pour un acc\\xe8s facile.\",\\n          \"title\": \"Installez l\\'extension Coin98 Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 Coin98 Wallet, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"coinbase\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons de placer Coinbase Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\",\\n          \"title\": \"Ouvrez l\\'application Coinbase Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Vous pouvez facilement sauvegarder votre portefeuille en utilisant la fonction de sauvegarde cloud.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir scann\\xe9, une invite de connexion s\\'affichera pour que vous puissiez connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton de scan\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Coinbase Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Coinbase Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xfbre. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\",\\n          \"title\": \"Actualisez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"compass\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Compass Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Compass Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"core\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons de placer Core sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Ouvrez l\\'application Core\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Vous pouvez facilement sauvegarder votre portefeuille en utilisant notre fonction de sauvegarde sur votre t\\xe9l\\xe9phone.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir scann\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton WalletConnect\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Core \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Core\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\",\\n          \"title\": \"Cr\\xe9ez ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"fox\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons de mettre FoxWallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\",\\n          \"title\": \"Ouvrez l\\'application FoxWallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec personne.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir scann\\xe9, une invitation \\xe0 la connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton de scan\"\\n        }\\n      }\\n    },\\n    \"frontier\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons de placer le portefeuille Frontier sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\",\\n          \"title\": \"Ouvrez l\\'application Frontier Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec personne.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir scann\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton de scan\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Frontier Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Frontier Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec personne.\",\\n          \"title\": \"Cr\\xe9ez ou importez un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"im_token\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application imToken\",\\n          \"description\": \"Placez l\\'application imToken sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9ez ou importez un portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant .\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur l\\'ic\\xf4ne du scanner dans le coin sup\\xe9rieur droit\",\\n          \"description\": \"Choisissez Nouvelle Connexion, puis scannez le code QR et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"iopay\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons de placer ioPay sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Ouvrez l\\'application ioPay\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Vous pouvez facilement sauvegarder votre portefeuille en utilisant notre fonction de sauvegarde sur votre t\\xe9l\\xe9phone.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton WalletConnect\"\\n        }\\n      }\\n    },\\n    \"kaikas\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Kaikas \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Kaikas\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Kaikas\",\\n          \"description\": \"Placez l\\'application Kaikas sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur l\\'ic\\xf4ne du scanner dans le coin sup\\xe9rieur droit\",\\n          \"description\": \"Choisissez Nouvelle Connexion, puis scannez le code QR et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"kaia\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Kaia \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Kaia\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Kaia\",\\n          \"description\": \"Mettez l\\'application Kaia sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur l\\'ic\\xf4ne du scanner dans le coin sup\\xe9rieur droit\",\\n          \"description\": \"Choisissez Nouvelle Connexion, puis scannez le code QR et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"kraken\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Kraken Wallet\",\\n          \"description\": \"Ajoutez Kraken Wallet \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Touchez l\\'ic\\xf4ne QR et scannez\",\\n          \"description\": \"Touchez l\\'ic\\xf4ne QR sur votre \\xe9cran d\\'accueil, scannez le code et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"kresus\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Kresus Wallet\",\\n          \"description\": \"Ajoutez Kresus Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Touchez l\\'ic\\xf4ne QR et scannez\",\\n          \"description\": \"Touchez l\\'ic\\xf4ne QR sur votre \\xe9cran d\\'accueil, scannez le code et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"magicEden\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Magic Eden\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Magic Eden \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus facile \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase de r\\xe9cup\\xe9ration secr\\xe8te avec personne.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"metamask\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application MetaMask\",\\n          \"description\": \"Nous vous recommandons de mettre MetaMask sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Veillez \\xe0 sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec qui que ce soit.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de scan\",\\n          \"description\": \"Apr\\xe8s avoir scann\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l’extension de MetaMask\",\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler MetaMask \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"nestwallet\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension NestWallet\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler NestWallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"okx\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application OKX Wallet\",\\n          \"description\": \"Nous recommandons de mettre OKX Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de num\\xe9risation\",\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension de portefeuille OKX\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler le portefeuille OKX \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"omni\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Omni\",\\n          \"description\": \"Ajoutez Omni \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Touchez l\\'ic\\xf4ne QR et scannez\",\\n          \"description\": \"Appuyez sur l\\'ic\\xf4ne QR sur votre \\xe9cran d\\'accueil, scannez le code et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"1inch\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Placez 1inch Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Ouvrez l\\'application 1inch Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Cr\\xe9ez un portefeuille et un nom d\\'utilisateur, ou importez un portefeuille existant.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton Scan QR\"\\n        }\\n      }\\n    },\\n    \"token_pocket\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application TokenPocket\",\\n          \"description\": \"Nous vous recommandons de mettre TokenPocket sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9ez ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille \\xe0 l\\'aide d\\'une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec qui que ce soit.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de scan\",\\n          \"description\": \"Apr\\xe8s votre scan, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension TokenPocket\",\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler TokenPocket \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec qui que ce soit.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"trust\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Trust Wallet\",\\n          \"description\": \"Placez Trust Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Cr\\xe9er un nouveau portefeuille ou en importer un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur WalletConnect dans les param\\xe8tres\",\\n          \"description\": \"Choisissez Nouvelle Connexion, puis scannez le code QR et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Trust Wallet\",\\n          \"description\": \"Cliquez en haut \\xe0 droite de votre navigateur et \\xe9pinglez Trust Wallet pour un acc\\xe8s facile.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou importer un portefeuille\",\\n          \"description\": \"Cr\\xe9er un nouveau portefeuille ou en importer un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 Trust Wallet, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"uniswap\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Uniswap\",\\n          \"description\": \"Ajoutez Uniswap Wallet \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9ez ou importez un portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tapez sur l\\'ic\\xf4ne QR et scannez\",\\n          \"description\": \"Touchez l\\'ic\\xf4ne QR sur votre \\xe9cran d\\'accueil, scannez le code et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"zerion\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Zerion\",\\n          \"description\": \"Nous vous recommandons de mettre Zerion sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec personne.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de scan\",\\n          \"description\": \"Une fois que vous avez scann\\xe9, une invite de connexion appara\\xeetra pour que vous puissiez connecter votre portefeuille.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installer l\\'extension Zerion\",\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Zerion \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9ez ou Importez un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"rainbow\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvre l\\'application Rainbow\",\\n          \"description\": \"Nous vous recommandons de mettre Rainbow sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9ez ou Importez un portefeuille\",\\n          \"description\": \"Vous pouvez facilement sauvegarder votre portefeuille en utilisant notre fonction de sauvegarde sur votre t\\xe9l\\xe9phone.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de scan\",\\n          \"description\": \"Apr\\xe8s avoir scann\\xe9, une invite de connexion appara\\xeetra pour que vous connectiez votre portefeuille.\"\\n        }\\n      }\\n    },\\n    \"enkrypt\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Enkrypt Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Enkrypt Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quelqu\\'un.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l’extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"frame\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Frame \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez Frame & l\\'extension compl\\xe9mentaire\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille \\xe0 l\\'aide d\\'une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec personne.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"one_key\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension OneKey Wallet\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler OneKey Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec personne.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"paraswap\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application ParaSwap\",\\n          \"description\": \"Ajoutez ParaSwap Wallet \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Touchez l\\'ic\\xf4ne QR et scannez\",\\n          \"description\": \"Touchez l\\'ic\\xf4ne QR sur votre \\xe9cran d\\'accueil, scannez le code et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"phantom\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Phantom\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Phantom \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus facile \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase de r\\xe9cup\\xe9ration secr\\xe8te avec personne.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"rabby\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Rabby\",\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Rabby \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec qui que ce soit.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Actualisez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"ronin\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons de placer Ronin Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\",\\n          \"title\": \"Ouvrez l\\'application Ronin Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton de scan\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Ronin Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Ronin Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"ramper\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Ramper\",\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Ramper \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus facile \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"safeheron\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Core\",\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Safeheron \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quelqu\\'un.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"taho\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Taho\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Taho \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9ez ou Importez un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quelqu\\'un.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"wigwam\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Wigwam\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Wigwam \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"talisman\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Talisman\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Talisman \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou importer un portefeuille Ethereum\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase de r\\xe9cup\\xe9ration avec personne.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"xdefi\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension du portefeuille XDEFI\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler XDEFI Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec qui que ce soit.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"zeal\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Zeal\",\\n          \"description\": \"Ajoutez Zeal Wallet \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Touchez l\\'ic\\xf4ne QR et scannez\",\\n          \"description\": \"Touchez l\\'ic\\xf4ne QR sur votre \\xe9cran d\\'accueil, scannez le code et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Zeal\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Zeal \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"safepal\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension SafePal Wallet\",\\n          \"description\": \"Cliquez en haut \\xe0 droite de votre navigateur et \\xe9pinglez SafePal Wallet pour un acc\\xe8s facile.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 SafePal Wallet, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application SafePal Wallet\",\\n          \"description\": \"Mettez SafePal Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur WalletConnect dans les param\\xe8tres\",\\n          \"description\": \"Choisissez Nouvelle Connexion, puis scannez le code QR et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"desig\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Desig\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Desig \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus facile \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"subwallet\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension SubWallet\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler SubWallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase de r\\xe9cup\\xe9ration avec personne.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application SubWallet\",\\n          \"description\": \"Nous vous recommandons de mettre SubWallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de scan\",\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\"\\n        }\\n      }\\n    },\\n    \"clv\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension CLV Wallet\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler CLV Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application CLV Wallet\",\\n          \"description\": \"Nous vous recommandons de mettre CLV Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de scan\",\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\"\\n        }\\n      }\\n    },\\n    \"okto\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Okto\",\\n          \"description\": \"Ajoutez Okto \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s rapide\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er un portefeuille MPC\",\\n          \"description\": \"Cr\\xe9ez un compte et g\\xe9n\\xe9rez un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur WalletConnect dans les param\\xe8tres\",\\n          \"description\": \"Touchez l\\'ic\\xf4ne \\'Scan QR\\' en haut \\xe0 droite et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"ledger\": {\\n      \"desktop\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Ledger Live\",\\n          \"description\": \"Nous vous recommandons de mettre Ledger Live sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Configurez votre Ledger\",\\n          \"description\": \"Configurez un nouveau Ledger ou connectez-vous \\xe0 un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Connecter\",\\n          \"description\": \"Une fois que vous avez scann\\xe9, une invite de connexion appara\\xeetra pour que vous puissiez connecter votre portefeuille.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Ledger Live\",\\n          \"description\": \"Nous vous recommandons de mettre Ledger Live sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Configurez votre Ledger\",\\n          \"description\": \"Vous pouvez soit synchroniser avec l\\'application de bureau, soit connecter votre Ledger.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Scannez le code\",\\n          \"description\": \"Appuyez sur WalletConnect puis passez au Scanner. Une fois que vous avez scann\\xe9, une invite de connexion appara\\xeetra pour que vous puissiez connecter votre portefeuille.\"\\n        }\\n      }\\n    },\\n    \"valora\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Valora\",\\n          \"description\": \"Nous vous recommandons de mettre Valora sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou importer un portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de scan\",\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\"\\n        }\\n      }\\n    },\\n    \"gate\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Gate\",\\n          \"description\": \"Nous vous recommandons de mettre Gate sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de scan\",\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Gate\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Gate \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus facile \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase de r\\xe9cup\\xe9ration secr\\xe8te avec personne.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"xportal\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Mettez xPortal sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Ouvrez l\\'application xPortal\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Cr\\xe9ez un portefeuille ou importez-en un existant.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton Scan QR\"\\n        }\\n      }\\n    },\\n    \"mew\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons de mettre MEW Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\",\\n          \"title\": \"Ouvrez l\\'application MEW Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Vous pouvez facilement sauvegarder votre portefeuille en utilisant la fonction de sauvegarde cloud.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton de scan\"\\n        }\\n      }\\n    }\\n  },\\n  \"zilpay\": {\\n    \"qr_code\": {\\n      \"step1\": {\\n        \"title\": \"Ouvrez l\\'application ZilPay\",\\n        \"description\": \"Ajoutez ZilPay \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n      },\\n      \"step2\": {\\n        \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n        \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n      },\\n      \"step3\": {\\n        \"title\": \"Appuyez sur le bouton de scan\",\\n        \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\"\\n      }\\n    }\\n  }\\n}\\n';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/fr_FR-CM2EDAQC.js\n"));

/***/ })

}]);