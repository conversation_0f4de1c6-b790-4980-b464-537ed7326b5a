"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_rainbow-me_rainbowkit_dist_pt_BR-3JTS4PSK_js"],{

/***/ "(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/pt_BR-3JTS4PSK.js":
/*!********************************************************************!*\
  !*** ./node_modules/@rainbow-me/rainbowkit/dist/pt_BR-3JTS4PSK.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pt_BR_default)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ default auto */ // src/locales/pt_BR.json\nvar pt_BR_default = '{\\n  \"connect_wallet\": {\\n    \"label\": \"Conectar Carteira\",\\n    \"wrong_network\": {\\n      \"label\": \"Rede incorreta\"\\n    }\\n  },\\n  \"intro\": {\\n    \"title\": \"O que \\xE9 uma Carteira?\",\\n    \"description\": \"Uma carteira \\xE9 usada para enviar, receber, armazenar e exibir ativos digitais. Tamb\\xE9m \\xE9 uma nova forma de se conectar, sem precisar criar novas contas e senhas em todo site.\",\\n    \"digital_asset\": {\\n      \"title\": \"Um lar para seus ativos digitais\",\\n      \"description\": \"Carteiras s\\xE3o usadas para enviar, receber, armazenar e exibir ativos digitais como Ethereum e NFTs.\"\\n    },\\n    \"login\": {\\n      \"title\": \"Uma nova maneira de fazer login\",\\n      \"description\": \"Em vez de criar novas contas e senhas em todos os sites, basta conectar sua carteira.\"\\n    },\\n    \"get\": {\\n      \"label\": \"Obter uma Carteira\"\\n    },\\n    \"learn_more\": {\\n      \"label\": \"Saiba mais\"\\n    }\\n  },\\n  \"sign_in\": {\\n    \"label\": \"Verifique sua conta\",\\n    \"description\": \"Para concluir a conex\\xE3o, voc\\xEA deve assinar uma mensagem em sua carteira para confirmar que voc\\xEA \\xE9 o propriet\\xE1rio desta conta.\",\\n    \"message\": {\\n      \"send\": \"Enviar mensagem\",\\n      \"preparing\": \"Preparando mensagem...\",\\n      \"cancel\": \"Cancelar\",\\n      \"preparing_error\": \"Erro ao preparar a mensagem, tente novamente!\"\\n    },\\n    \"signature\": {\\n      \"waiting\": \"Aguardando assinatura...\",\\n      \"verifying\": \"Verificando assinatura...\",\\n      \"signing_error\": \"Erro ao assinar a mensagem, tente novamente!\",\\n      \"verifying_error\": \"Erro ao verificar assinatura, tente novamente!\",\\n      \"oops_error\": \"Ops, algo deu errado!\"\\n    }\\n  },\\n  \"connect\": {\\n    \"label\": \"Conectar\",\\n    \"title\": \"Conectar uma Carteira\",\\n    \"new_to_ethereum\": {\\n      \"description\": \"Novo nas carteiras Ethereum?\",\\n      \"learn_more\": {\\n        \"label\": \"Saiba mais\"\\n      }\\n    },\\n    \"learn_more\": {\\n      \"label\": \"Saiba mais\"\\n    },\\n    \"recent\": \"Recente\",\\n    \"status\": {\\n      \"opening\": \"Abrindo %{wallet}...\",\\n      \"connecting\": \"Conectando\",\\n      \"connect_mobile\": \"Continue em %{wallet}\",\\n      \"not_installed\": \"%{wallet} n\\xE3o est\\xE1 instalado\",\\n      \"not_available\": \"%{wallet} n\\xE3o est\\xE1 dispon\\xEDvel\",\\n      \"confirm\": \"Confirme a conex\\xE3o na extens\\xE3o\",\\n      \"confirm_mobile\": \"Aceite o pedido de conex\\xE3o na carteira\"\\n    },\\n    \"secondary_action\": {\\n      \"get\": {\\n        \"description\": \"N\\xE3o tem %{wallet}?\",\\n        \"label\": \"OBTER\"\\n      },\\n      \"install\": {\\n        \"label\": \"INSTALAR\"\\n      },\\n      \"retry\": {\\n        \"label\": \"TENTAR DE NOVO\"\\n      }\\n    },\\n    \"walletconnect\": {\\n      \"description\": {\\n        \"full\": \"Precisa do modal oficial do WalletConnect?\",\\n        \"compact\": \"Precisa do modal WalletConnect?\"\\n      },\\n      \"open\": {\\n        \"label\": \"ABRIR\"\\n      }\\n    }\\n  },\\n  \"connect_scan\": {\\n    \"title\": \"Digitalize com %{wallet}\",\\n    \"fallback_title\": \"Digitalize com o seu telefone\"\\n  },\\n  \"connector_group\": {\\n    \"installed\": \"Instalado\",\\n    \"recommended\": \"Recomendado\",\\n    \"other\": \"Outro\",\\n    \"popular\": \"Popular\",\\n    \"more\": \"Mais\",\\n    \"others\": \"Outros\"\\n  },\\n  \"get\": {\\n    \"title\": \"Obter uma Carteira\",\\n    \"action\": {\\n      \"label\": \"OBTER\"\\n    },\\n    \"mobile\": {\\n      \"description\": \"Carteira M\\xF3vel\"\\n    },\\n    \"extension\": {\\n      \"description\": \"Extens\\xE3o do Navegador\"\\n    },\\n    \"mobile_and_extension\": {\\n      \"description\": \"Carteira M\\xF3vel e Extens\\xE3o\"\\n    },\\n    \"mobile_and_desktop\": {\\n      \"description\": \"Carteira para Mobile e Desktop\"\\n    },\\n    \"looking_for\": {\\n      \"title\": \"N\\xE3o \\xE9 o que voc\\xEA est\\xE1 procurando?\",\\n      \"mobile\": {\\n        \"description\": \"Selecione uma carteira na tela principal para come\\xE7ar com um provedor de carteira diferente.\"\\n      },\\n      \"desktop\": {\\n        \"compact_description\": \"Selecione uma carteira na tela principal para come\\xE7ar com um provedor de carteira diferente.\",\\n        \"wide_description\": \"Selecione uma carteira \\xE0 esquerda para come\\xE7ar com um provedor de carteira diferente.\"\\n      }\\n    }\\n  },\\n  \"get_options\": {\\n    \"title\": \"Comece com %{wallet}\",\\n    \"short_title\": \"Obtenha %{wallet}\",\\n    \"mobile\": {\\n      \"title\": \"%{wallet} para M\\xF3vel\",\\n      \"description\": \"Use a carteira m\\xF3vel para explorar o mundo do Ethereum.\",\\n      \"download\": {\\n        \"label\": \"Baixe o aplicativo\"\\n      }\\n    },\\n    \"extension\": {\\n      \"title\": \"%{wallet} para %{browser}\",\\n      \"description\": \"Acesse sua carteira diretamente do seu navegador web favorito.\",\\n      \"download\": {\\n        \"label\": \"Adicionar ao %{browser}\"\\n      }\\n    },\\n    \"desktop\": {\\n      \"title\": \"%{wallet} para %{platform}\",\\n      \"description\": \"Acesse sua carteira nativamente do seu desktop poderoso.\",\\n      \"download\": {\\n        \"label\": \"Adicionar ao %{platform}\"\\n      }\\n    }\\n  },\\n  \"get_mobile\": {\\n    \"title\": \"Instale %{wallet}\",\\n    \"description\": \"Escaneie com seu celular para baixar no iOS ou Android\",\\n    \"continue\": {\\n      \"label\": \"Continuar\"\\n    }\\n  },\\n  \"get_instructions\": {\\n    \"mobile\": {\\n      \"connect\": {\\n        \"label\": \"Conectar\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"Saiba mais\"\\n      }\\n    },\\n    \"extension\": {\\n      \"refresh\": {\\n        \"label\": \"Atualizar\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"Saiba mais\"\\n      }\\n    },\\n    \"desktop\": {\\n      \"connect\": {\\n        \"label\": \"Conectar\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"Saiba mais\"\\n      }\\n    }\\n  },\\n  \"chains\": {\\n    \"title\": \"Mudar Redes\",\\n    \"wrong_network\": \"Rede errada detectada, mude ou desconecte para continuar.\",\\n    \"confirm\": \"Confirme na Carteira\",\\n    \"switching_not_supported\": \"Sua carteira n\\xE3o suporta a mudan\\xE7a de redes de %{appName}. Tente mudar de redes dentro da sua carteira.\",\\n    \"switching_not_supported_fallback\": \"Sua carteira n\\xE3o suporta a troca de redes a partir deste aplicativo. Tente trocar de rede dentro de sua carteira.\",\\n    \"disconnect\": \"Desconectar\",\\n    \"connected\": \"Conectado\"\\n  },\\n  \"profile\": {\\n    \"disconnect\": {\\n      \"label\": \"Desconectar\"\\n    },\\n    \"copy_address\": {\\n      \"label\": \"Copiar Endere\\xE7o\",\\n      \"copied\": \"Copiado!\"\\n    },\\n    \"explorer\": {\\n      \"label\": \"Veja mais no explorador\"\\n    },\\n    \"transactions\": {\\n      \"description\": \"%{appName} transa\\xE7\\xF5es aparecer\\xE3o aqui...\",\\n      \"description_fallback\": \"Suas transa\\xE7\\xF5es aparecer\\xE3o aqui...\",\\n      \"recent\": {\\n        \"title\": \"Transa\\xE7\\xF5es Recentes\"\\n      },\\n      \"clear\": {\\n        \"label\": \"Limpar Tudo\"\\n      }\\n    }\\n  },\\n  \"wallet_connectors\": {\\n    \"argent\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Coloque o Argent na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Abra o aplicativo Argent\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Crie uma carteira e nome de usu\\xE1rio, ou importe uma carteira existente.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o Scan QR\"\\n        }\\n      }\\n    },\\n    \"berasig\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o BeraSig\",\\n          \"description\": \"Recomendamos fixar BeraSig na sua barra de tarefas para facilitar o acesso \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"best\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Best Wallet\",\\n          \"description\": \"Adicione a Best Wallet \\xE0 sua tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do QR e escaneie\",\\n          \"description\": \"Toque no \\xEDcone QR na sua tela inicial, escaneie o c\\xF3digo e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"bifrost\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar a Bifrost Wallet na sua tela inicial para um acesso mais r\\xE1pido.\",\\n          \"title\": \"Abra o aplicativo Bifrost Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Crie ou importe uma carteira usando sua frase de recupera\\xE7\\xE3o.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Ap\\xF3s voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o de escanear\"\\n        }\\n      }\\n    },\\n    \"bitget\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar a Bitget Wallet na sua tela inicial para um acesso mais r\\xE1pido.\",\\n          \"title\": \"Abra o aplicativo Bitget Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o de escaneamento\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar a Bitget Wallet na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o da Carteira Bitget\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer o backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"bitski\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar o Bitski na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o Bitski\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"bitverse\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Bitverse Wallet\",\\n          \"description\": \"Adicione o Bitverse Wallet \\xE0 sua tela inicial para acessar sua carteira mais rapidamente.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do QR e escaneie\",\\n          \"description\": \"Toque no \\xEDcone QR na sua tela inicial, escaneie o c\\xF3digo e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"bloom\": {\\n      \"desktop\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Bloom Wallet\",\\n          \"description\": \"Recomendamos colocar o Bloom Wallet na sua tela inicial para acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Crie ou importe uma carteira usando sua frase de recupera\\xE7\\xE3o.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de ter uma carteira, clique em Conectar para se conectar via Bloom. Um prompt de conex\\xE3o aparecer\\xE1 no aplicativo para voc\\xEA confirmar a conex\\xE3o.\",\\n          \"title\": \"Clique em Conectar\"\\n        }\\n      }\\n    },\\n    \"bybit\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar o Bybit na sua tela inicial para acessar sua carteira mais rapidamente.\",\\n          \"title\": \"Abra o aplicativo Bybit\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Voc\\xEA pode facilmente fazer backup de sua carteira usando nosso recurso de backup em seu telefone.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o de escanear\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Clique no canto superior direito do seu navegador e fixe a Carteira Bybit para acesso f\\xE1cil.\",\\n          \"title\": \"Instale a extens\\xE3o da Carteira Bybit\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\",\\n          \"title\": \"Criar ou Importar uma carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar a Carteira Bybit, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"binance\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar o Binance na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Abra o aplicativo Binance\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Voc\\xEA pode facilmente fazer backup de sua carteira usando nosso recurso de backup em seu telefone.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o WalletConnect\"\\n        }\\n      }\\n    },\\n    \"coin98\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar a Carteira Coin98 na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Abra o aplicativo Carteira Coin98\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Voc\\xEA pode facilmente fazer backup de sua carteira usando nosso recurso de backup em seu telefone.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de escanear, uma solicita\\xE7\\xE3o de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o WalletConnect\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Clique no canto superior direito do seu navegador e fixe a Carteira Coin98 para f\\xE1cil acesso.\",\\n          \"title\": \"Instale a extens\\xE3o da Carteira Coin98\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\",\\n          \"title\": \"Criar ou Importar uma carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar a Carteira Coin98, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"coinbase\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar a Carteira Coinbase na tela inicial para um acesso mais r\\xE1pido.\",\\n          \"title\": \"Abra o aplicativo Coinbase Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Voc\\xEA pode fazer backup da sua carteira facilmente usando o recurso de backup na nuvem.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de escanear, um prompt de conex\\xE3o aparecer\\xE1 para que voc\\xEA conecte sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o de escanear\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar o Coinbase Wallet na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o Coinbase Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Uma vez que voc\\xEA configurou sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"compass\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar a Carteira Compass na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o Compass Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"core\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar o Core na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Abra o aplicativo Core\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Voc\\xEA pode facilmente salvar sua carteira usando nosso recurso de backup no seu celular.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o WalletConnect\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar o Core na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o Core\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"fox\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar o FoxWallet na tela inicial para um acesso mais r\\xE1pido.\",\\n          \"title\": \"Abra o aplicativo FoxWallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de escanear, uma solicita\\xE7\\xE3o de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o de escaneamento\"\\n        }\\n      }\\n    },\\n    \"frontier\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar o Frontier Wallet na tela inicial para um acesso mais r\\xE1pido.\",\\n          \"title\": \"Abra o aplicativo Frontier Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de escanear, aparecer\\xE1 um prompt de conex\\xE3o para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o de varredura\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar a Carteira Frontier na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o da Carteira Frontier\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"im_token\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo imToken\",\\n          \"description\": \"Coloque o aplicativo imToken na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do Scanner no canto superior direito\",\\n          \"description\": \"Escolha Nova Conex\\xE3o, em seguida, escaneie o c\\xF3digo QR e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"iopay\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar o ioPay na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Abra o aplicativo ioPay\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Voc\\xEA pode facilmente fazer backup de sua carteira usando nosso recurso de backup em seu telefone.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o WalletConnect\"\\n        }\\n      }\\n    },\\n    \"kaikas\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar o Kaikas na sua barra de tarefas para acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o Kaikas\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Kaikas\",\\n          \"description\": \"Coloque o aplicativo Kaikas na sua tela inicial para acessar sua carteira mais rapidamente.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do Scanner no canto superior direito\",\\n          \"description\": \"Escolha Nova Conex\\xE3o, em seguida, escaneie o c\\xF3digo QR e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"kaia\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar o Kaia na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o Kaia\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Kaia\",\\n          \"description\": \"Coloque o aplicativo Kaia na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do Scanner no canto superior direito\",\\n          \"description\": \"Escolha Nova Conex\\xE3o, em seguida, escaneie o c\\xF3digo QR e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"kraken\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Kraken Wallet\",\\n          \"description\": \"Adicione o Kraken Wallet \\xE0 tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do QR e escaneie\",\\n          \"description\": \"Toque no \\xEDcone QR na sua tela inicial, escaneie o c\\xF3digo e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"kresus\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Kresus Wallet\",\\n          \"description\": \"Adicione a Carteira Kresus \\xE0 sua tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do QR e escaneie\",\\n          \"description\": \"Toque no \\xEDcone QR na sua tela inicial, escaneie o c\\xF3digo e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"magicEden\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Magic Eden\",\\n          \"description\": \"Recomendamos fixar o Magic Eden na sua barra de tarefas para facilitar o acesso \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta de recupera\\xE7\\xE3o com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"metamask\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo MetaMask\",\\n          \"description\": \"Recomendamos colocar o MetaMask na tela inicial para um acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o escanear\",\\n          \"description\": \"Depois de escanear, aparecer\\xE1 um prompt de conex\\xE3o para voc\\xEA conectar sua carteira.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o MetaMask\",\\n          \"description\": \"Recomendamos fixar o MetaMask na barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize o seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"nestwallet\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o NestWallet\",\\n          \"description\": \"Recomendamos fixar o NestWallet na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"okx\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo da Carteira OKX\",\\n          \"description\": \"Recomendamos colocar a Carteira OKX na tela inicial para um acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer o backup da sua carteira utilizando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o de digitaliza\\xE7\\xE3o\",\\n          \"description\": \"Depois de escanear, aparecer\\xE1 um prompt de conex\\xE3o para voc\\xEA conectar sua carteira.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o OKX Wallet\",\\n          \"description\": \"Recomendamos fixar a OKX Wallet na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer o backup da sua carteira utilizando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize o seu navegador\",\\n          \"description\": \"Uma vez que voc\\xEA configurou sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"omni\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Omni\",\\n          \"description\": \"Adicione o Omni \\xE0 sua tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do QR e escaneie\",\\n          \"description\": \"Toque no \\xEDcone QR na tela inicial, escaneie o c\\xF3digo e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"1inch\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Coloque o 1inch Wallet na sua tela inicial para acessar sua carteira mais rapidamente.\",\\n          \"title\": \"Abra o aplicativo 1inch Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Crie uma carteira e nome de usu\\xE1rio, ou importe uma carteira existente.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o Scan QR\"\\n        }\\n      }\\n    },\\n    \"token_pocket\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo TokenPocket\",\\n          \"description\": \"Recomendamos colocar o TokenPocket na tela inicial para um acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o de digitaliza\\xE7\\xE3o\",\\n          \"description\": \"Depois de escanear, aparecer\\xE1 um prompt de conex\\xE3o para voc\\xEA conectar sua carteira.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o TokenPocket\",\\n          \"description\": \"Recomendamos fixar o TokenPocket em sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Uma vez que voc\\xEA configurou sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"trust\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Trust Wallet\",\\n          \"description\": \"Coloque o Trust Wallet na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque em WalletConnect nas Configura\\xE7\\xF5es\",\\n          \"description\": \"Escolha Nova Conex\\xE3o, depois escaneie o QR code e confirme o prompt para se conectar.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Trust Wallet\",\\n          \"description\": \"Clique no canto superior direito do seu navegador e marque Trust Wallet para f\\xE1cil acesso.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Crie ou Importe uma carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois que configurar a Trust Wallet, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"uniswap\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Uniswap\",\\n          \"description\": \"Adicione a Carteira Uniswap \\xE0 sua tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone QR e escaneie\",\\n          \"description\": \"Toque no \\xEDcone QR na sua tela inicial, escaneie o c\\xF3digo e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"zerion\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Zerion\",\\n          \"description\": \"Recomendamos colocar o Zerion na sua tela inicial para um acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o de digitaliza\\xE7\\xE3o\",\\n          \"description\": \"Depois de digitalizar, um prompt de conex\\xE3o aparecer\\xE1 para que voc\\xEA possa conectar sua carteira.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Zerion\",\\n          \"description\": \"Recomendamos fixar o Zerion na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"rainbow\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Rainbow\",\\n          \"description\": \"Recomendamos colocar o Rainbow na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Voc\\xEA pode facilmente fazer backup da sua carteira usando nosso recurso de backup no seu telefone.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o de digitalizar\",\\n          \"description\": \"Depois de escanear, uma solicita\\xE7\\xE3o de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\"\\n        }\\n      }\\n    },\\n    \"enkrypt\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar a Carteira Enkrypt na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o da Carteira Enkrypt\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize o seu navegador\"\\n        }\\n      }\\n    },\\n    \"frame\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar o Frame na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale o Frame e a extens\\xE3o complementar\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"one_key\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o OneKey Wallet\",\\n          \"description\": \"Recomendamos fixar a OneKey Wallet na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Uma vez que voc\\xEA configurou sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"paraswap\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo ParaSwap\",\\n          \"description\": \"Adicione a Carteira ParaSwap \\xE0 sua tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do QR e escaneie\",\\n          \"description\": \"Toque no \\xEDcone QR na sua tela inicial, escaneie o c\\xF3digo e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"phantom\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Phantom\",\\n          \"description\": \"Recomendamos fixar o Phantom na sua barra de tarefas para facilitar o acesso \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta de recupera\\xE7\\xE3o com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"rabby\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Rabby\",\\n          \"description\": \"Recomendamos fixar Rabby na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"ronin\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar a Carteira Ronin na tela inicial para um acesso mais r\\xE1pido.\",\\n          \"title\": \"Abra o aplicativo Carteira Ronin\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o de escanear\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos fixar a Carteira Ronin na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Instale a extens\\xE3o da Carteira Ronin\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\",\\n          \"title\": \"Atualize seu navegador\"\\n        }\\n      }\\n    },\\n    \"ramper\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Ramper\",\\n          \"description\": \"Recomendamos fixar o Ramper na sua barra de tarefas para um acesso mais f\\xE1cil \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"safeheron\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Core\",\\n          \"description\": \"Recomendamos fixar Safeheron na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer o backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"taho\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Taho\",\\n          \"description\": \"Recomendamos fixar o Taho na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer o backup da sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"wigwam\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Wigwam\",\\n          \"description\": \"Recomendamos fixar o Wigwam na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"talisman\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Talisman\",\\n          \"description\": \"Recomendamos fixar o Talisman na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Crie ou Importe uma Carteira Ethereum\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase de recupera\\xE7\\xE3o com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize o seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"xdefi\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o XDEFI Wallet\",\\n          \"description\": \"Recomendamos fixar a Carteira XDEFI na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"zeal\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Zeal\",\\n          \"description\": \"Adicione a Carteira Zeal \\xE0 sua tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no \\xEDcone do QR e escaneie\",\\n          \"description\": \"Toque no \\xEDcone QR na sua tela inicial, escaneie o c\\xF3digo e confirme o prompt para conectar.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Zeal\",\\n          \"description\": \"Recomendamos fixar o Zeal na sua barra de tarefas para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"safepal\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o da Carteira SafePal\",\\n          \"description\": \"Clique no canto superior direito do seu navegador e fixe a Carteira SafePal para f\\xE1cil acesso.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar a Carteira SafePal, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Carteira SafePal\",\\n          \"description\": \"Coloque a Carteira SafePal na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque em WalletConnect nas Configura\\xE7\\xF5es\",\\n          \"description\": \"Escolha Nova Conex\\xE3o, em seguida, escaneie o c\\xF3digo QR e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"desig\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Desig\",\\n          \"description\": \"Recomendamos fixar Desig na sua barra de tarefas para facilitar o acesso \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"subwallet\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o SubWallet\",\\n          \"description\": \"Recomendamos fixar SubWallet na sua barra de tarefas para acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase de recupera\\xE7\\xE3o com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo SubWallet\",\\n          \"description\": \"Recomendamos colocar SubWallet na tela inicial para acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o de escanear\",\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\"\\n        }\\n      }\\n    },\\n    \"clv\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o CLV Wallet\",\\n          \"description\": \"Recomendamos fixar CLV Wallet na sua barra de tarefas para acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo da carteira CLV\",\\n          \"description\": \"Recomendamos colocar a Carteira CLV na tela inicial para acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o de escanear\",\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\"\\n        }\\n      }\\n    },\\n    \"okto\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Okto\",\\n          \"description\": \"Adicione Okto \\xE0 sua tela inicial para acesso r\\xE1pido\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Crie uma carteira MPC\",\\n          \"description\": \"Crie uma conta e gere uma carteira\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque em WalletConnect nas Configura\\xE7\\xF5es\",\\n          \"description\": \"Toque no \\xEDcone Scan QR no canto superior direito e confirme o prompt para conectar.\"\\n        }\\n      }\\n    },\\n    \"ledger\": {\\n      \"desktop\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Ledger Live\",\\n          \"description\": \"Recomendamos colocar o Ledger Live na tela inicial para um acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Configure seu Ledger\",\\n          \"description\": \"Configure um novo Ledger ou conecte-se a um j\\xE1 existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Conectar\",\\n          \"description\": \"Depois de escanear, aparecer\\xE1 um prompt de conex\\xE3o para voc\\xEA conectar sua carteira.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Ledger Live\",\\n          \"description\": \"Recomendamos colocar o Ledger Live na tela inicial para um acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Configure seu Ledger\",\\n          \"description\": \"Voc\\xEA pode sincronizar com o aplicativo de desktop ou conectar seu Ledger.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Escanear o c\\xF3digo\",\\n          \"description\": \"Toque em WalletConnect e em seguida mude para Scanner. Depois de escanear, aparecer\\xE1 um prompt de conex\\xE3o para voc\\xEA conectar sua carteira.\"\\n        }\\n      }\\n    },\\n    \"valora\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Valora\",\\n          \"description\": \"Recomendamos colocar o Valora na tela inicial para um acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o de escanear\",\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\"\\n        }\\n      }\\n    },\\n    \"gate\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Abra o aplicativo Gate\",\\n          \"description\": \"Recomendamos colocar o Gate na sua tela inicial para um acesso mais r\\xE1pido.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Toque no bot\\xE3o de escanear\",\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Instale a extens\\xE3o Gate\",\\n          \"description\": \"Recomendamos fixar o Gate na sua barra de tarefas para facilitar o acesso \\xE0 sua carteira.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Criar ou Importar uma Carteira\",\\n          \"description\": \"Certifique-se de fazer backup de sua carteira usando um m\\xE9todo seguro. Nunca compartilhe sua frase secreta de recupera\\xE7\\xE3o com ningu\\xE9m.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Atualize seu navegador\",\\n          \"description\": \"Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extens\\xE3o.\"\\n        }\\n      }\\n    },\\n    \"xportal\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Coloque o xPortal na tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\",\\n          \"title\": \"Abra o aplicativo xPortal\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Crie uma nova carteira ou importe uma existente.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o Scan QR\"\\n        }\\n      }\\n    },\\n    \"mew\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Recomendamos colocar a Carteira MEW na tela inicial para acesso mais r\\xE1pido.\",\\n          \"title\": \"Abra o aplicativo da Carteira MEW\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Voc\\xEA pode fazer backup da sua carteira facilmente usando o recurso de backup na nuvem.\",\\n          \"title\": \"Criar ou Importar uma Carteira\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\",\\n          \"title\": \"Toque no bot\\xE3o de escanear\"\\n        }\\n      }\\n    }\\n  },\\n  \"zilpay\": {\\n    \"qr_code\": {\\n      \"step1\": {\\n        \"title\": \"Abra o aplicativo ZilPay\",\\n        \"description\": \"Adicione o ZilPay \\xE0 sua tela inicial para um acesso mais r\\xE1pido \\xE0 sua carteira.\"\\n      },\\n      \"step2\": {\\n        \"title\": \"Criar ou Importar uma Carteira\",\\n        \"description\": \"Crie uma nova carteira ou importe uma existente.\"\\n      },\\n      \"step3\": {\\n        \"title\": \"Toque no bot\\xE3o de escanear\",\\n        \"description\": \"Depois que voc\\xEA escanear, um prompt de conex\\xE3o aparecer\\xE1 para voc\\xEA conectar sua carteira.\"\\n      }\\n    }\\n  }\\n}\\n';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@rainbow-me/rainbowkit/dist/pt_BR-3JTS4PSK.js\n"));

/***/ })

}]);