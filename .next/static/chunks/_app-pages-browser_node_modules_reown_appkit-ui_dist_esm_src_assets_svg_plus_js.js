"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_plus_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   plusSvg: () => (/* binding */ plusSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst plusSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg\n  width=\"13\"\n  height=\"12\"\n  viewBox=\"0 0 13 12\"\n  fill=\"none\"\n>\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    clip-rule=\"evenodd\"\n    d=\"M0.794373 5.99982C0.794373 5.52643 1.17812 5.14268 1.6515 5.14268H5.643V1.15109C5.643 0.677701 6.02675 0.293946 6.50012 0.293945C6.9735 0.293946 7.35725 0.677701 7.35725 1.15109V5.14268H11.3488C11.8221 5.14268 12.2059 5.52643 12.2059 5.99982C12.2059 6.47321 11.8221 6.85696 11.3488 6.85696H7.35725V10.8486C7.35725 11.3219 6.9735 11.7057 6.50012 11.7057C6.02675 11.7057 5.643 11.3219 5.643 10.8486V6.85696H1.6515C1.17812 6.85696 0.794373 6.47321 0.794373 5.99982Z\"\n  /></svg\n>`;\n//# sourceMappingURL=plus.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL3BsdXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEI7QUFDbkIsZ0JBQWdCLHdDQUFHO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0ByZW93bi9hcHBraXQtdWkvZGlzdC9lc20vc3JjL2Fzc2V0cy9zdmcvcGx1cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdmcgfSBmcm9tICdsaXQnO1xuZXhwb3J0IGNvbnN0IHBsdXNTdmcgPSBzdmcgYDxzdmdcbiAgd2lkdGg9XCIxM1wiXG4gIGhlaWdodD1cIjEyXCJcbiAgdmlld0JveD1cIjAgMCAxMyAxMlwiXG4gIGZpbGw9XCJub25lXCJcbj5cbiAgPHBhdGhcbiAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcbiAgICBmaWxsLXJ1bGU9XCJldmVub2RkXCJcbiAgICBjbGlwLXJ1bGU9XCJldmVub2RkXCJcbiAgICBkPVwiTTAuNzk0MzczIDUuOTk5ODJDMC43OTQzNzMgNS41MjY0MyAxLjE3ODEyIDUuMTQyNjggMS42NTE1IDUuMTQyNjhINS42NDNWMS4xNTEwOUM1LjY0MyAwLjY3NzcwMSA2LjAyNjc1IDAuMjkzOTQ2IDYuNTAwMTIgMC4yOTM5NDVDNi45NzM1IDAuMjkzOTQ2IDcuMzU3MjUgMC42Nzc3MDEgNy4zNTcyNSAxLjE1MTA5VjUuMTQyNjhIMTEuMzQ4OEMxMS44MjIxIDUuMTQyNjggMTIuMjA1OSA1LjUyNjQzIDEyLjIwNTkgNS45OTk4MkMxMi4yMDU5IDYuNDczMjEgMTEuODIyMSA2Ljg1Njk2IDExLjM0ODggNi44NTY5Nkg3LjM1NzI1VjEwLjg0ODZDNy4zNTcyNSAxMS4zMjE5IDYuOTczNSAxMS43MDU3IDYuNTAwMTIgMTEuNzA1N0M2LjAyNjc1IDExLjcwNTcgNS42NDMgMTEuMzIxOSA1LjY0MyAxMC44NDg2VjYuODU2OTZIMS42NTE1QzEuMTc4MTIgNi44NTY5NiAwLjc5NDM3MyA2LjQ3MzIxIDAuNzk0MzczIDUuOTk5ODJaXCJcbiAgLz48L3N2Z1xuPmA7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wbHVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js\n"));

/***/ })

}]);