"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_viem_node_modules_noble_curves_esm_secp256k1_js"],{

/***/ "(app-pages-browser)/./node_modules/@noble/hashes/esm/hmac.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/hmac.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HMAC: () => (/* binding */ HMAC),\n/* harmony export */   hmac: () => (/* binding */ hmac)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@noble/hashes/esm/utils.js\");\n/**\n * HMAC: RFC2104 message authentication code.\n * @module\n */\n\nclass HMAC extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(hash, _key) {\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ahash)(hash);\n        const key = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(_key);\n        this.iHash = hash.create();\n        if (typeof this.iHash.update !== 'function')\n            throw new Error('Expected instance of class which extends utils.Hash');\n        this.blockLen = this.iHash.blockLen;\n        this.outputLen = this.iHash.outputLen;\n        const blockLen = this.blockLen;\n        const pad = new Uint8Array(blockLen);\n        // blockLen can be bigger than outputLen\n        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36;\n        this.iHash.update(pad);\n        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n        this.oHash = hash.create();\n        // Undo internal XOR && apply outer XOR\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36 ^ 0x5c;\n        this.oHash.update(pad);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.clean)(pad);\n    }\n    update(buf) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        this.iHash.update(buf);\n        return this;\n    }\n    digestInto(out) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(out, this.outputLen);\n        this.finished = true;\n        this.iHash.digestInto(out);\n        this.oHash.update(out);\n        this.oHash.digestInto(out);\n        this.destroy();\n    }\n    digest() {\n        const out = new Uint8Array(this.oHash.outputLen);\n        this.digestInto(out);\n        return out;\n    }\n    _cloneInto(to) {\n        // Create new instance without calling constructor since key already in state and we don't know it.\n        to || (to = Object.create(Object.getPrototypeOf(this), {}));\n        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n        to = to;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        to.blockLen = blockLen;\n        to.outputLen = outputLen;\n        to.oHash = oHash._cloneInto(to.oHash);\n        to.iHash = iHash._cloneInto(to.iHash);\n        return to;\n    }\n    clone() {\n        return this._cloneInto();\n    }\n    destroy() {\n        this.destroyed = true;\n        this.oHash.destroy();\n        this.iHash.destroy();\n    }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nconst hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key) => new HMAC(hash, key);\n//# sourceMappingURL=hmac.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@noble/hashes/esm/hmac.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/_shortw_utils.js":
/*!***************************************************************************!*\
  !*** ./node_modules/viem/node_modules/@noble/curves/esm/_shortw_utils.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCurve: () => (/* binding */ createCurve),\n/* harmony export */   getHash: () => (/* binding */ getHash)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_hmac__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/hmac */ \"(app-pages-browser)/./node_modules/@noble/hashes/esm/hmac.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/hashes/utils */ \"(app-pages-browser)/./node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./abstract/weierstrass.js */ \"(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/weierstrass.js\");\n/**\n * Utilities for short weierstrass curves, combined with noble-hashes.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n/** connects noble-curves to noble-hashes */\nfunction getHash(hash) {\n    return {\n        hash,\n        hmac: (key, ...msgs) => (0,_noble_hashes_hmac__WEBPACK_IMPORTED_MODULE_0__.hmac)(hash, key, (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.concatBytes)(...msgs)),\n        randomBytes: _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.randomBytes,\n    };\n}\nfunction createCurve(curveDef, defHash) {\n    const create = (hash) => (0,_abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_2__.weierstrass)({ ...curveDef, ...getHash(hash) });\n    return { ...create(defHash), create };\n}\n//# sourceMappingURL=_shortw_utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy92aWVtL25vZGVfbW9kdWxlcy9Abm9ibGUvY3VydmVzL2VzbS9fc2hvcnR3X3V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUMwQztBQUNxQjtBQUNQO0FBQ3hEO0FBQ087QUFDUDtBQUNBO0FBQ0EsZ0NBQWdDLHdEQUFJLFlBQVksZ0VBQVc7QUFDM0QsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDTztBQUNQLDZCQUE2QixxRUFBVyxHQUFHLCtCQUErQjtBQUMxRSxhQUFhO0FBQ2I7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy92aWVtL25vZGVfbW9kdWxlcy9Abm9ibGUvY3VydmVzL2VzbS9fc2hvcnR3X3V0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVXRpbGl0aWVzIGZvciBzaG9ydCB3ZWllcnN0cmFzcyBjdXJ2ZXMsIGNvbWJpbmVkIHdpdGggbm9ibGUtaGFzaGVzLlxuICogQG1vZHVsZVxuICovXG4vKiEgbm9ibGUtY3VydmVzIC0gTUlUIExpY2Vuc2UgKGMpIDIwMjIgUGF1bCBNaWxsZXIgKHBhdWxtaWxsci5jb20pICovXG5pbXBvcnQgeyBobWFjIH0gZnJvbSAnQG5vYmxlL2hhc2hlcy9obWFjJztcbmltcG9ydCB7IGNvbmNhdEJ5dGVzLCByYW5kb21CeXRlcyB9IGZyb20gJ0Bub2JsZS9oYXNoZXMvdXRpbHMnO1xuaW1wb3J0IHsgd2VpZXJzdHJhc3MgfSBmcm9tIFwiLi9hYnN0cmFjdC93ZWllcnN0cmFzcy5qc1wiO1xuLyoqIGNvbm5lY3RzIG5vYmxlLWN1cnZlcyB0byBub2JsZS1oYXNoZXMgKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRIYXNoKGhhc2gpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBoYXNoLFxuICAgICAgICBobWFjOiAoa2V5LCAuLi5tc2dzKSA9PiBobWFjKGhhc2gsIGtleSwgY29uY2F0Qnl0ZXMoLi4ubXNncykpLFxuICAgICAgICByYW5kb21CeXRlcyxcbiAgICB9O1xufVxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUN1cnZlKGN1cnZlRGVmLCBkZWZIYXNoKSB7XG4gICAgY29uc3QgY3JlYXRlID0gKGhhc2gpID0+IHdlaWVyc3RyYXNzKHsgLi4uY3VydmVEZWYsIC4uLmdldEhhc2goaGFzaCkgfSk7XG4gICAgcmV0dXJuIHsgLi4uY3JlYXRlKGRlZkhhc2gpLCBjcmVhdGUgfTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPV9zaG9ydHdfdXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/_shortw_utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/curve.js":
/*!****************************************************************************!*\
  !*** ./node_modules/viem/node_modules/@noble/curves/esm/abstract/curve.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pippenger: () => (/* binding */ pippenger),\n/* harmony export */   precomputeMSMUnsafe: () => (/* binding */ precomputeMSMUnsafe),\n/* harmony export */   validateBasic: () => (/* binding */ validateBasic),\n/* harmony export */   wNAF: () => (/* binding */ wNAF)\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Methods for elliptic curve multiplication by scalars.\n * Contains wNAF, pippenger\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nfunction constTimeNegate(condition, item) {\n    const neg = item.negate();\n    return condition ? neg : item;\n}\nfunction validateW(W, bits) {\n    if (!Number.isSafeInteger(W) || W <= 0 || W > bits)\n        throw new Error('invalid window size, expected [1..' + bits + '], got W=' + W);\n}\nfunction calcWOpts(W, scalarBits) {\n    validateW(W, scalarBits);\n    const windows = Math.ceil(scalarBits / W) + 1; // W=8 33. Not 32, because we skip zero\n    const windowSize = 2 ** (W - 1); // W=8 128. Not 256, because we skip zero\n    const maxNumber = 2 ** W; // W=8 256\n    const mask = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(W); // W=8 255 == mask 0b11111111\n    const shiftBy = BigInt(W); // W=8 8\n    return { windows, windowSize, mask, maxNumber, shiftBy };\n}\nfunction calcOffsets(n, window, wOpts) {\n    const { windowSize, mask, maxNumber, shiftBy } = wOpts;\n    let wbits = Number(n & mask); // extract W bits.\n    let nextN = n >> shiftBy; // shift number by W bits.\n    // What actually happens here:\n    // const highestBit = Number(mask ^ (mask >> 1n));\n    // let wbits2 = wbits - 1; // skip zero\n    // if (wbits2 & highestBit) { wbits2 ^= Number(mask); // (~);\n    // split if bits > max: +224 => 256-32\n    if (wbits > windowSize) {\n        // we skip zero, which means instead of `>= size-1`, we do `> size`\n        wbits -= maxNumber; // -32, can be maxNumber - wbits, but then we need to set isNeg here.\n        nextN += _1n; // +256 (carry)\n    }\n    const offsetStart = window * windowSize;\n    const offset = offsetStart + Math.abs(wbits) - 1; // -1 because we skip zero\n    const isZero = wbits === 0; // is current window slice a 0?\n    const isNeg = wbits < 0; // is current window slice negative?\n    const isNegF = window % 2 !== 0; // fake random statement for noise\n    const offsetF = offsetStart; // fake offset for noise\n    return { nextN, offset, isZero, isNeg, isNegF, offsetF };\n}\nfunction validateMSMPoints(points, c) {\n    if (!Array.isArray(points))\n        throw new Error('array expected');\n    points.forEach((p, i) => {\n        if (!(p instanceof c))\n            throw new Error('invalid point at index ' + i);\n    });\n}\nfunction validateMSMScalars(scalars, field) {\n    if (!Array.isArray(scalars))\n        throw new Error('array of scalars expected');\n    scalars.forEach((s, i) => {\n        if (!field.isValid(s))\n            throw new Error('invalid scalar at index ' + i);\n    });\n}\n// Since points in different groups cannot be equal (different object constructor),\n// we can have single place to store precomputes.\n// Allows to make points frozen / immutable.\nconst pointPrecomputes = new WeakMap();\nconst pointWindowSizes = new WeakMap();\nfunction getW(P) {\n    return pointWindowSizes.get(P) || 1;\n}\n/**\n * Elliptic curve multiplication of Point by scalar. Fragile.\n * Scalars should always be less than curve order: this should be checked inside of a curve itself.\n * Creates precomputation tables for fast multiplication:\n * - private scalar is split by fixed size windows of W bits\n * - every window point is collected from window's table & added to accumulator\n * - since windows are different, same point inside tables won't be accessed more than once per calc\n * - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n * - +1 window is neccessary for wNAF\n * - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n *\n * @todo Research returning 2d JS array of windows, instead of a single window.\n * This would allow windows to be in different memory locations\n */\nfunction wNAF(c, bits) {\n    return {\n        constTimeNegate,\n        hasPrecomputes(elm) {\n            return getW(elm) !== 1;\n        },\n        // non-const time multiplication ladder\n        unsafeLadder(elm, n, p = c.ZERO) {\n            let d = elm;\n            while (n > _0n) {\n                if (n & _1n)\n                    p = p.add(d);\n                d = d.double();\n                n >>= _1n;\n            }\n            return p;\n        },\n        /**\n         * Creates a wNAF precomputation window. Used for caching.\n         * Default window size is set by `utils.precompute()` and is equal to 8.\n         * Number of precomputed points depends on the curve size:\n         * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n         * - 𝑊 is the window size\n         * - 𝑛 is the bitlength of the curve order.\n         * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n         * @param elm Point instance\n         * @param W window size\n         * @returns precomputed point tables flattened to a single array\n         */\n        precomputeWindow(elm, W) {\n            const { windows, windowSize } = calcWOpts(W, bits);\n            const points = [];\n            let p = elm;\n            let base = p;\n            for (let window = 0; window < windows; window++) {\n                base = p;\n                points.push(base);\n                // i=1, bc we skip 0\n                for (let i = 1; i < windowSize; i++) {\n                    base = base.add(p);\n                    points.push(base);\n                }\n                p = base.double();\n            }\n            return points;\n        },\n        /**\n         * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @returns real and fake (for const-time) points\n         */\n        wNAF(W, precomputes, n) {\n            // Smaller version:\n            // https://github.com/paulmillr/noble-secp256k1/blob/47cb1669b6e506ad66b35fe7d76132ae97465da2/index.ts#L502-L541\n            // TODO: check the scalar is less than group order?\n            // wNAF behavior is undefined otherwise. But have to carefully remove\n            // other checks before wNAF. ORDER == bits here.\n            // Accumulators\n            let p = c.ZERO;\n            let f = c.BASE;\n            // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n            // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n            // there is negate now: it is possible that negated element from low value\n            // would be the same as high element, which will create carry into next window.\n            // It's not obvious how this can fail, but still worth investigating later.\n            const wo = calcWOpts(W, bits);\n            for (let window = 0; window < wo.windows; window++) {\n                // (n === _0n) is handled and not early-exited. isEven and offsetF are used for noise\n                const { nextN, offset, isZero, isNeg, isNegF, offsetF } = calcOffsets(n, window, wo);\n                n = nextN;\n                if (isZero) {\n                    // bits are 0: add garbage to fake point\n                    // Important part for const-time getPublicKey: add random \"noise\" point to f.\n                    f = f.add(constTimeNegate(isNegF, precomputes[offsetF]));\n                }\n                else {\n                    // bits are 1: add to result point\n                    p = p.add(constTimeNegate(isNeg, precomputes[offset]));\n                }\n            }\n            // Return both real and fake points: JIT won't eliminate f.\n            // At this point there is a way to F be infinity-point even if p is not,\n            // which makes it less const-time: around 1 bigint multiply.\n            return { p, f };\n        },\n        /**\n         * Implements ec unsafe (non const-time) multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @param acc accumulator point to add result of multiplication\n         * @returns point\n         */\n        wNAFUnsafe(W, precomputes, n, acc = c.ZERO) {\n            const wo = calcWOpts(W, bits);\n            for (let window = 0; window < wo.windows; window++) {\n                if (n === _0n)\n                    break; // Early-exit, skip 0 value\n                const { nextN, offset, isZero, isNeg } = calcOffsets(n, window, wo);\n                n = nextN;\n                if (isZero) {\n                    // Window bits are 0: skip processing.\n                    // Move to next window.\n                    continue;\n                }\n                else {\n                    const item = precomputes[offset];\n                    acc = acc.add(isNeg ? item.negate() : item); // Re-using acc allows to save adds in MSM\n                }\n            }\n            return acc;\n        },\n        getPrecomputes(W, P, transform) {\n            // Calculate precomputes on a first run, reuse them after\n            let comp = pointPrecomputes.get(P);\n            if (!comp) {\n                comp = this.precomputeWindow(P, W);\n                if (W !== 1)\n                    pointPrecomputes.set(P, transform(comp));\n            }\n            return comp;\n        },\n        wNAFCached(P, n, transform) {\n            const W = getW(P);\n            return this.wNAF(W, this.getPrecomputes(W, P, transform), n);\n        },\n        wNAFCachedUnsafe(P, n, transform, prev) {\n            const W = getW(P);\n            if (W === 1)\n                return this.unsafeLadder(P, n, prev); // For W=1 ladder is ~x2 faster\n            return this.wNAFUnsafe(W, this.getPrecomputes(W, P, transform), n, prev);\n        },\n        // We calculate precomputes for elliptic curve point multiplication\n        // using windowed method. This specifies window size and\n        // stores precomputed values. Usually only base point would be precomputed.\n        setWindowSize(P, W) {\n            validateW(W, bits);\n            pointWindowSizes.set(P, W);\n            pointPrecomputes.delete(P);\n        },\n    };\n}\n/**\n * Pippenger algorithm for multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * 30x faster vs naive addition on L=4096, 10x faster than precomputes.\n * For N=254bit, L=1, it does: 1024 ADD + 254 DBL. For L=5: 1536 ADD + 254 DBL.\n * Algorithmically constant-time (for same L), even when 1 point + scalar, or when scalar = 0.\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @param scalars array of L scalars (aka private keys / bigints)\n */\nfunction pippenger(c, fieldN, points, scalars) {\n    // If we split scalars by some window (let's say 8 bits), every chunk will only\n    // take 256 buckets even if there are 4096 scalars, also re-uses double.\n    // TODO:\n    // - https://eprint.iacr.org/2024/750.pdf\n    // - https://tches.iacr.org/index.php/TCHES/article/view/10287\n    // 0 is accepted in scalars\n    validateMSMPoints(points, c);\n    validateMSMScalars(scalars, fieldN);\n    const plength = points.length;\n    const slength = scalars.length;\n    if (plength !== slength)\n        throw new Error('arrays of points and scalars must have equal length');\n    // if (plength === 0) throw new Error('array must be of length >= 2');\n    const zero = c.ZERO;\n    const wbits = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitLen)(BigInt(plength));\n    let windowSize = 1; // bits\n    if (wbits > 12)\n        windowSize = wbits - 3;\n    else if (wbits > 4)\n        windowSize = wbits - 2;\n    else if (wbits > 0)\n        windowSize = 2;\n    const MASK = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(windowSize);\n    const buckets = new Array(Number(MASK) + 1).fill(zero); // +1 for zero array\n    const lastBits = Math.floor((fieldN.BITS - 1) / windowSize) * windowSize;\n    let sum = zero;\n    for (let i = lastBits; i >= 0; i -= windowSize) {\n        buckets.fill(zero);\n        for (let j = 0; j < slength; j++) {\n            const scalar = scalars[j];\n            const wbits = Number((scalar >> BigInt(i)) & MASK);\n            buckets[wbits] = buckets[wbits].add(points[j]);\n        }\n        let resI = zero; // not using this will do small speed-up, but will lose ct\n        // Skip first bucket, because it is zero\n        for (let j = buckets.length - 1, sumI = zero; j > 0; j--) {\n            sumI = sumI.add(buckets[j]);\n            resI = resI.add(sumI);\n        }\n        sum = sum.add(resI);\n        if (i !== 0)\n            for (let j = 0; j < windowSize; j++)\n                sum = sum.double();\n    }\n    return sum;\n}\n/**\n * Precomputed multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @returns function which multiplies points with scaars\n */\nfunction precomputeMSMUnsafe(c, fieldN, points, windowSize) {\n    /**\n     * Performance Analysis of Window-based Precomputation\n     *\n     * Base Case (256-bit scalar, 8-bit window):\n     * - Standard precomputation requires:\n     *   - 31 additions per scalar × 256 scalars = 7,936 ops\n     *   - Plus 255 summary additions = 8,191 total ops\n     *   Note: Summary additions can be optimized via accumulator\n     *\n     * Chunked Precomputation Analysis:\n     * - Using 32 chunks requires:\n     *   - 255 additions per chunk\n     *   - 256 doublings\n     *   - Total: (255 × 32) + 256 = 8,416 ops\n     *\n     * Memory Usage Comparison:\n     * Window Size | Standard Points | Chunked Points\n     * ------------|-----------------|---------------\n     *     4-bit   |     520         |      15\n     *     8-bit   |    4,224        |     255\n     *    10-bit   |   13,824        |   1,023\n     *    16-bit   |  557,056        |  65,535\n     *\n     * Key Advantages:\n     * 1. Enables larger window sizes due to reduced memory overhead\n     * 2. More efficient for smaller scalar counts:\n     *    - 16 chunks: (16 × 255) + 256 = 4,336 ops\n     *    - ~2x faster than standard 8,191 ops\n     *\n     * Limitations:\n     * - Not suitable for plain precomputes (requires 256 constant doublings)\n     * - Performance degrades with larger scalar counts:\n     *   - Optimal for ~256 scalars\n     *   - Less efficient for 4096+ scalars (Pippenger preferred)\n     */\n    validateW(windowSize, fieldN.BITS);\n    validateMSMPoints(points, c);\n    const zero = c.ZERO;\n    const tableSize = 2 ** windowSize - 1; // table size (without zero)\n    const chunks = Math.ceil(fieldN.BITS / windowSize); // chunks of item\n    const MASK = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(windowSize);\n    const tables = points.map((p) => {\n        const res = [];\n        for (let i = 0, acc = p; i < tableSize; i++) {\n            res.push(acc);\n            acc = acc.add(p);\n        }\n        return res;\n    });\n    return (scalars) => {\n        validateMSMScalars(scalars, fieldN);\n        if (scalars.length > points.length)\n            throw new Error('array of scalars must be smaller than array of points');\n        let res = zero;\n        for (let i = 0; i < chunks; i++) {\n            // No need to double if accumulator is still zero.\n            if (res !== zero)\n                for (let j = 0; j < windowSize; j++)\n                    res = res.double();\n            const shiftBy = BigInt(chunks * windowSize - (i + 1) * windowSize);\n            for (let j = 0; j < scalars.length; j++) {\n                const n = scalars[j];\n                const curr = Number((n >> shiftBy) & MASK);\n                if (!curr)\n                    continue; // skip zero scalars chunks\n                res = res.add(tables[j][curr - 1]);\n            }\n        }\n        return res;\n    };\n}\nfunction validateBasic(curve) {\n    (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.validateField)(curve.Fp);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(curve, {\n        n: 'bigint',\n        h: 'bigint',\n        Gx: 'field',\n        Gy: 'field',\n    }, {\n        nBitLength: 'isSafeInteger',\n        nByteLength: 'isSafeInteger',\n    });\n    // Set defaults\n    return Object.freeze({\n        ...(0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.nLength)(curve.n, curve.nBitLength),\n        ...curve,\n        ...{ p: curve.Fp.ORDER },\n    });\n}\n//# sourceMappingURL=curve.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/curve.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/hash-to-curve.js":
/*!************************************************************************************!*\
  !*** ./node_modules/viem/node_modules/@noble/curves/esm/abstract/hash-to-curve.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHasher: () => (/* binding */ createHasher),\n/* harmony export */   expand_message_xmd: () => (/* binding */ expand_message_xmd),\n/* harmony export */   expand_message_xof: () => (/* binding */ expand_message_xof),\n/* harmony export */   hash_to_field: () => (/* binding */ hash_to_field),\n/* harmony export */   isogenyMap: () => (/* binding */ isogenyMap)\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/utils.js\");\n\n\n// Octet Stream to Integer. \"spec\" implementation of os2ip is 2.5x slower vs bytesToNumberBE.\nconst os2ip = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE;\n// Integer to Octet Stream (numberToBytesBE)\nfunction i2osp(value, length) {\n    anum(value);\n    anum(length);\n    if (value < 0 || value >= 1 << (8 * length))\n        throw new Error('invalid I2OSP input: ' + value);\n    const res = Array.from({ length }).fill(0);\n    for (let i = length - 1; i >= 0; i--) {\n        res[i] = value & 0xff;\n        value >>>= 8;\n    }\n    return new Uint8Array(res);\n}\nfunction strxor(a, b) {\n    const arr = new Uint8Array(a.length);\n    for (let i = 0; i < a.length; i++) {\n        arr[i] = a[i] ^ b[i];\n    }\n    return arr;\n}\nfunction anum(item) {\n    if (!Number.isSafeInteger(item))\n        throw new Error('number expected');\n}\n/**\n * Produces a uniformly random byte string using a cryptographic hash function H that outputs b bits.\n * [RFC 9380 5.3.1](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.1).\n */\nfunction expand_message_xmd(msg, DST, lenInBytes, H) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(DST);\n    anum(lenInBytes);\n    // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n    if (DST.length > 255)\n        DST = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)('H2C-OVERSIZE-DST-'), DST));\n    const { outputLen: b_in_bytes, blockLen: r_in_bytes } = H;\n    const ell = Math.ceil(lenInBytes / b_in_bytes);\n    if (lenInBytes > 65535 || ell > 255)\n        throw new Error('expand_message_xmd: invalid lenInBytes');\n    const DST_prime = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(DST, i2osp(DST.length, 1));\n    const Z_pad = i2osp(0, r_in_bytes);\n    const l_i_b_str = i2osp(lenInBytes, 2); // len_in_bytes_str\n    const b = new Array(ell);\n    const b_0 = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(Z_pad, msg, l_i_b_str, i2osp(0, 1), DST_prime));\n    b[0] = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(b_0, i2osp(1, 1), DST_prime));\n    for (let i = 1; i <= ell; i++) {\n        const args = [strxor(b_0, b[i - 1]), i2osp(i + 1, 1), DST_prime];\n        b[i] = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...args));\n    }\n    const pseudo_random_bytes = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...b);\n    return pseudo_random_bytes.slice(0, lenInBytes);\n}\n/**\n * Produces a uniformly random byte string using an extendable-output function (XOF) H.\n * 1. The collision resistance of H MUST be at least k bits.\n * 2. H MUST be an XOF that has been proved indifferentiable from\n *    a random oracle under a reasonable cryptographic assumption.\n * [RFC 9380 5.3.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.2).\n */\nfunction expand_message_xof(msg, DST, lenInBytes, k, H) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(DST);\n    anum(lenInBytes);\n    // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n    // DST = H('H2C-OVERSIZE-DST-' || a_very_long_DST, Math.ceil((lenInBytes * k) / 8));\n    if (DST.length > 255) {\n        const dkLen = Math.ceil((2 * k) / 8);\n        DST = H.create({ dkLen }).update((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)('H2C-OVERSIZE-DST-')).update(DST).digest();\n    }\n    if (lenInBytes > 65535 || DST.length > 255)\n        throw new Error('expand_message_xof: invalid lenInBytes');\n    return (H.create({ dkLen: lenInBytes })\n        .update(msg)\n        .update(i2osp(lenInBytes, 2))\n        // 2. DST_prime = DST || I2OSP(len(DST), 1)\n        .update(DST)\n        .update(i2osp(DST.length, 1))\n        .digest());\n}\n/**\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F.\n * [RFC 9380 5.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.2).\n * @param msg a byte string containing the message to hash\n * @param count the number of elements of F to output\n * @param options `{DST: string, p: bigint, m: number, k: number, expand: 'xmd' | 'xof', hash: H}`, see above\n * @returns [u_0, ..., u_(count - 1)], a list of field elements.\n */\nfunction hash_to_field(msg, count, options) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(options, {\n        DST: 'stringOrUint8Array',\n        p: 'bigint',\n        m: 'isSafeInteger',\n        k: 'isSafeInteger',\n        hash: 'hash',\n    });\n    const { p, k, m, hash, expand, DST: _DST } = options;\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    anum(count);\n    const DST = typeof _DST === 'string' ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)(_DST) : _DST;\n    const log2p = p.toString(2).length;\n    const L = Math.ceil((log2p + k) / 8); // section 5.1 of ietf draft link above\n    const len_in_bytes = count * m * L;\n    let prb; // pseudo_random_bytes\n    if (expand === 'xmd') {\n        prb = expand_message_xmd(msg, DST, len_in_bytes, hash);\n    }\n    else if (expand === 'xof') {\n        prb = expand_message_xof(msg, DST, len_in_bytes, k, hash);\n    }\n    else if (expand === '_internal_pass') {\n        // for internal tests only\n        prb = msg;\n    }\n    else {\n        throw new Error('expand must be \"xmd\" or \"xof\"');\n    }\n    const u = new Array(count);\n    for (let i = 0; i < count; i++) {\n        const e = new Array(m);\n        for (let j = 0; j < m; j++) {\n            const elm_offset = L * (j + i * m);\n            const tv = prb.subarray(elm_offset, elm_offset + L);\n            e[j] = (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.mod)(os2ip(tv), p);\n        }\n        u[i] = e;\n    }\n    return u;\n}\nfunction isogenyMap(field, map) {\n    // Make same order as in spec\n    const coeff = map.map((i) => Array.from(i).reverse());\n    return (x, y) => {\n        const [xn, xd, yn, yd] = coeff.map((val) => val.reduce((acc, i) => field.add(field.mul(acc, x), i)));\n        // 6.6.3\n        // Exceptional cases of iso_map are inputs that cause the denominator of\n        // either rational function to evaluate to zero; such cases MUST return\n        // the identity point on E.\n        const [xd_inv, yd_inv] = (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.FpInvertBatch)(field, [xd, yd], true);\n        x = field.mul(xn, xd_inv); // xNum / xDen\n        y = field.mul(y, field.mul(yn, yd_inv)); // y * (yNum / yDev)\n        return { x, y };\n    };\n}\n/** Creates hash-to-curve methods from EC Point and mapToCurve function. */\nfunction createHasher(Point, mapToCurve, defaults) {\n    if (typeof mapToCurve !== 'function')\n        throw new Error('mapToCurve() must be defined');\n    function map(num) {\n        return Point.fromAffine(mapToCurve(num));\n    }\n    function clear(initial) {\n        const P = initial.clearCofactor();\n        if (P.equals(Point.ZERO))\n            return Point.ZERO; // zero will throw in assert\n        P.assertValidity();\n        return P;\n    }\n    return {\n        defaults,\n        // Encodes byte string to elliptic curve.\n        // hash_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n        hashToCurve(msg, options) {\n            const u = hash_to_field(msg, 2, { ...defaults, DST: defaults.DST, ...options });\n            const u0 = map(u[0]);\n            const u1 = map(u[1]);\n            return clear(u0.add(u1));\n        },\n        // Encodes byte string to elliptic curve.\n        // encode_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n        encodeToCurve(msg, options) {\n            const u = hash_to_field(msg, 1, { ...defaults, DST: defaults.encodeDST, ...options });\n            return clear(map(u[0]));\n        },\n        // Same as encodeToCurve, but without hash\n        mapToCurve(scalars) {\n            if (!Array.isArray(scalars))\n                throw new Error('expected array of bigints');\n            for (const i of scalars)\n                if (typeof i !== 'bigint')\n                    throw new Error('expected array of bigints');\n            return clear(map(scalars));\n        },\n    };\n}\n//# sourceMappingURL=hash-to-curve.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/hash-to-curve.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/modular.js":
/*!******************************************************************************!*\
  !*** ./node_modules/viem/node_modules/@noble/curves/esm/abstract/modular.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Field: () => (/* binding */ Field),\n/* harmony export */   FpDiv: () => (/* binding */ FpDiv),\n/* harmony export */   FpInvertBatch: () => (/* binding */ FpInvertBatch),\n/* harmony export */   FpIsSquare: () => (/* binding */ FpIsSquare),\n/* harmony export */   FpLegendre: () => (/* binding */ FpLegendre),\n/* harmony export */   FpPow: () => (/* binding */ FpPow),\n/* harmony export */   FpSqrt: () => (/* binding */ FpSqrt),\n/* harmony export */   FpSqrtEven: () => (/* binding */ FpSqrtEven),\n/* harmony export */   FpSqrtOdd: () => (/* binding */ FpSqrtOdd),\n/* harmony export */   getFieldBytesLength: () => (/* binding */ getFieldBytesLength),\n/* harmony export */   getMinHashLength: () => (/* binding */ getMinHashLength),\n/* harmony export */   hashToPrivateScalar: () => (/* binding */ hashToPrivateScalar),\n/* harmony export */   invert: () => (/* binding */ invert),\n/* harmony export */   isNegativeLE: () => (/* binding */ isNegativeLE),\n/* harmony export */   mapHashToField: () => (/* binding */ mapHashToField),\n/* harmony export */   mod: () => (/* binding */ mod),\n/* harmony export */   nLength: () => (/* binding */ nLength),\n/* harmony export */   pow: () => (/* binding */ pow),\n/* harmony export */   pow2: () => (/* binding */ pow2),\n/* harmony export */   tonelliShanks: () => (/* binding */ tonelliShanks),\n/* harmony export */   validateField: () => (/* binding */ validateField)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/hashes/utils */ \"(app-pages-browser)/./node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Utils for modular division and finite fields.\n * A finite field over 11 is integer number operations `mod 11`.\n * There is no division: it is replaced by modular multiplicative inverse.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = /* @__PURE__ */ BigInt(2), _3n = /* @__PURE__ */ BigInt(3);\n// prettier-ignore\nconst _4n = /* @__PURE__ */ BigInt(4), _5n = /* @__PURE__ */ BigInt(5), _8n = /* @__PURE__ */ BigInt(8);\n// Calculates a modulo b\nfunction mod(a, b) {\n    const result = a % b;\n    return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * TODO: remove.\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\nfunction pow(num, power, modulo) {\n    return FpPow(Field(modulo), num, power);\n}\n/** Does `x^(2^power)` mod p. `pow2(30, 4)` == `30^(2^4)` */\nfunction pow2(x, power, modulo) {\n    let res = x;\n    while (power-- > _0n) {\n        res *= res;\n        res %= modulo;\n    }\n    return res;\n}\n/**\n * Inverses number over modulo.\n * Implemented using [Euclidean GCD](https://brilliant.org/wiki/extended-euclidean-algorithm/).\n */\nfunction invert(number, modulo) {\n    if (number === _0n)\n        throw new Error('invert: expected non-zero number');\n    if (modulo <= _0n)\n        throw new Error('invert: expected positive modulus, got ' + modulo);\n    // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n    let a = mod(number, modulo);\n    let b = modulo;\n    // prettier-ignore\n    let x = _0n, y = _1n, u = _1n, v = _0n;\n    while (a !== _0n) {\n        // JIT applies optimization if those two lines follow each other\n        const q = b / a;\n        const r = b % a;\n        const m = x - u * q;\n        const n = y - v * q;\n        // prettier-ignore\n        b = a, a = r, x = u, y = v, u = m, v = n;\n    }\n    const gcd = b;\n    if (gcd !== _1n)\n        throw new Error('invert: does not exist');\n    return mod(x, modulo);\n}\n// Not all roots are possible! Example which will throw:\n// const NUM =\n// n = 72057594037927816n;\n// Fp = Field(BigInt('0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaab'));\nfunction sqrt3mod4(Fp, n) {\n    const p1div4 = (Fp.ORDER + _1n) / _4n;\n    const root = Fp.pow(n, p1div4);\n    // Throw if root^2 != n\n    if (!Fp.eql(Fp.sqr(root), n))\n        throw new Error('Cannot find square root');\n    return root;\n}\nfunction sqrt5mod8(Fp, n) {\n    const p5div8 = (Fp.ORDER - _5n) / _8n;\n    const n2 = Fp.mul(n, _2n);\n    const v = Fp.pow(n2, p5div8);\n    const nv = Fp.mul(n, v);\n    const i = Fp.mul(Fp.mul(nv, _2n), v);\n    const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n    if (!Fp.eql(Fp.sqr(root), n))\n        throw new Error('Cannot find square root');\n    return root;\n}\n// TODO: Commented-out for now. Provide test vectors.\n// Tonelli is too slow for extension fields Fp2.\n// That means we can't use sqrt (c1, c2...) even for initialization constants.\n// if (P % _16n === _9n) return sqrt9mod16;\n// // prettier-ignore\n// function sqrt9mod16<T>(Fp: IField<T>, n: T, p7div16?: bigint) {\n//   if (p7div16 === undefined) p7div16 = (Fp.ORDER + BigInt(7)) / _16n;\n//   const c1 = Fp.sqrt(Fp.neg(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n//   const c2 = Fp.sqrt(c1);             //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n//   const c3 = Fp.sqrt(Fp.neg(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n//   const c4 = p7div16;                 //  4. c4 = (q + 7) / 16        # Integer arithmetic\n//   let tv1 = Fp.pow(n, c4);            //  1. tv1 = x^c4\n//   let tv2 = Fp.mul(c1, tv1);          //  2. tv2 = c1 * tv1\n//   const tv3 = Fp.mul(c2, tv1);        //  3. tv3 = c2 * tv1\n//   let tv4 = Fp.mul(c3, tv1);          //  4. tv4 = c3 * tv1\n//   const e1 = Fp.eql(Fp.sqr(tv2), n);  //  5.  e1 = (tv2^2) == x\n//   const e2 = Fp.eql(Fp.sqr(tv3), n);  //  6.  e2 = (tv3^2) == x\n//   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n//   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n//   const e3 = Fp.eql(Fp.sqr(tv2), n);  //  9.  e3 = (tv2^2) == x\n//   return Fp.cmov(tv1, tv2, e3); // 10.  z = CMOV(tv1, tv2, e3) # Select the sqrt from tv1 and tv2\n// }\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nfunction tonelliShanks(P) {\n    // Initialization (precomputation).\n    if (P < BigInt(3))\n        throw new Error('sqrt is not defined for small field');\n    // Factor P - 1 = Q * 2^S, where Q is odd\n    let Q = P - _1n;\n    let S = 0;\n    while (Q % _2n === _0n) {\n        Q /= _2n;\n        S++;\n    }\n    // Find the first quadratic non-residue Z >= 2\n    let Z = _2n;\n    const _Fp = Field(P);\n    while (FpLegendre(_Fp, Z) === 1) {\n        // Basic primality test for P. After x iterations, chance of\n        // not finding quadratic non-residue is 2^x, so 2^1000.\n        if (Z++ > 1000)\n            throw new Error('Cannot find square root: probably non-prime P');\n    }\n    // Fast-path; usually done before Z, but we do \"primality test\".\n    if (S === 1)\n        return sqrt3mod4;\n    // Slow-path\n    // TODO: test on Fp2 and others\n    let cc = _Fp.pow(Z, Q); // c = z^Q\n    const Q1div2 = (Q + _1n) / _2n;\n    return function tonelliSlow(Fp, n) {\n        if (Fp.is0(n))\n            return n;\n        // Check if n is a quadratic residue using Legendre symbol\n        if (FpLegendre(Fp, n) !== 1)\n            throw new Error('Cannot find square root');\n        // Initialize variables for the main loop\n        let M = S;\n        let c = Fp.mul(Fp.ONE, cc); // c = z^Q, move cc from field _Fp into field Fp\n        let t = Fp.pow(n, Q); // t = n^Q, first guess at the fudge factor\n        let R = Fp.pow(n, Q1div2); // R = n^((Q+1)/2), first guess at the square root\n        // Main loop\n        // while t != 1\n        while (!Fp.eql(t, Fp.ONE)) {\n            if (Fp.is0(t))\n                return Fp.ZERO; // if t=0 return R=0\n            let i = 1;\n            // Find the smallest i >= 1 such that t^(2^i) ≡ 1 (mod P)\n            let t_tmp = Fp.sqr(t); // t^(2^1)\n            while (!Fp.eql(t_tmp, Fp.ONE)) {\n                i++;\n                t_tmp = Fp.sqr(t_tmp); // t^(2^2)...\n                if (i === M)\n                    throw new Error('Cannot find square root');\n            }\n            // Calculate the exponent for b: 2^(M - i - 1)\n            const exponent = _1n << BigInt(M - i - 1); // bigint is important\n            const b = Fp.pow(c, exponent); // b = 2^(M - i - 1)\n            // Update variables\n            M = i;\n            c = Fp.sqr(b); // c = b^2\n            t = Fp.mul(t, c); // t = (t * b^2)\n            R = Fp.mul(R, b); // R = R*b\n        }\n        return R;\n    };\n}\n/**\n * Square root for a finite field. Will try optimized versions first:\n *\n * 1. P ≡ 3 (mod 4)\n * 2. P ≡ 5 (mod 8)\n * 3. Tonelli-Shanks algorithm\n *\n * Different algorithms can give different roots, it is up to user to decide which one they want.\n * For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n */\nfunction FpSqrt(P) {\n    // P ≡ 3 (mod 4) => √n = n^((P+1)/4)\n    if (P % _4n === _3n)\n        return sqrt3mod4;\n    // P ≡ 5 (mod 8) => Atkin algorithm, page 10 of https://eprint.iacr.org/2012/685.pdf\n    if (P % _8n === _5n)\n        return sqrt5mod8;\n    // P ≡ 9 (mod 16) not implemented, see above\n    // Tonelli-Shanks algorithm\n    return tonelliShanks(P);\n}\n// Little-endian check for first LE bit (last BE bit);\nconst isNegativeLE = (num, modulo) => (mod(num, modulo) & _1n) === _1n;\n// prettier-ignore\nconst FIELD_FIELDS = [\n    'create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr',\n    'eql', 'add', 'sub', 'mul', 'pow', 'div',\n    'addN', 'subN', 'mulN', 'sqrN'\n];\nfunction validateField(field) {\n    const initial = {\n        ORDER: 'bigint',\n        MASK: 'bigint',\n        BYTES: 'isSafeInteger',\n        BITS: 'isSafeInteger',\n    };\n    const opts = FIELD_FIELDS.reduce((map, val) => {\n        map[val] = 'function';\n        return map;\n    }, initial);\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(field, opts);\n}\n// Generic field functions\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nfunction FpPow(Fp, num, power) {\n    if (power < _0n)\n        throw new Error('invalid exponent, negatives unsupported');\n    if (power === _0n)\n        return Fp.ONE;\n    if (power === _1n)\n        return num;\n    let p = Fp.ONE;\n    let d = num;\n    while (power > _0n) {\n        if (power & _1n)\n            p = Fp.mul(p, d);\n        d = Fp.sqr(d);\n        power >>= _1n;\n    }\n    return p;\n}\n/**\n * Efficiently invert an array of Field elements.\n * Exception-free. Will return `undefined` for 0 elements.\n * @param passZero map 0 to 0 (instead of undefined)\n */\nfunction FpInvertBatch(Fp, nums, passZero = false) {\n    const inverted = new Array(nums.length).fill(passZero ? Fp.ZERO : undefined);\n    // Walk from first to last, multiply them by each other MOD p\n    const multipliedAcc = nums.reduce((acc, num, i) => {\n        if (Fp.is0(num))\n            return acc;\n        inverted[i] = acc;\n        return Fp.mul(acc, num);\n    }, Fp.ONE);\n    // Invert last element\n    const invertedAcc = Fp.inv(multipliedAcc);\n    // Walk from last to first, multiply them by inverted each other MOD p\n    nums.reduceRight((acc, num, i) => {\n        if (Fp.is0(num))\n            return acc;\n        inverted[i] = Fp.mul(acc, inverted[i]);\n        return Fp.mul(acc, num);\n    }, invertedAcc);\n    return inverted;\n}\n// TODO: remove\nfunction FpDiv(Fp, lhs, rhs) {\n    return Fp.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, Fp.ORDER) : Fp.inv(rhs));\n}\n/**\n * Legendre symbol.\n * Legendre constant is used to calculate Legendre symbol (a | p)\n * which denotes the value of a^((p-1)/2) (mod p).\n *\n * * (a | p) ≡ 1    if a is a square (mod p), quadratic residue\n * * (a | p) ≡ -1   if a is not a square (mod p), quadratic non residue\n * * (a | p) ≡ 0    if a ≡ 0 (mod p)\n */\nfunction FpLegendre(Fp, n) {\n    // We can use 3rd argument as optional cache of this value\n    // but seems unneeded for now. The operation is very fast.\n    const p1mod2 = (Fp.ORDER - _1n) / _2n;\n    const powered = Fp.pow(n, p1mod2);\n    const yes = Fp.eql(powered, Fp.ONE);\n    const zero = Fp.eql(powered, Fp.ZERO);\n    const no = Fp.eql(powered, Fp.neg(Fp.ONE));\n    if (!yes && !zero && !no)\n        throw new Error('invalid Legendre symbol result');\n    return yes ? 1 : zero ? 0 : -1;\n}\n// This function returns True whenever the value x is a square in the field F.\nfunction FpIsSquare(Fp, n) {\n    const l = FpLegendre(Fp, n);\n    return l === 1;\n}\n// CURVE.n lengths\nfunction nLength(n, nBitLength) {\n    // Bit size, byte size of CURVE.n\n    if (nBitLength !== undefined)\n        (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.anumber)(nBitLength);\n    const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n    const nByteLength = Math.ceil(_nBitLength / 8);\n    return { nBitLength: _nBitLength, nByteLength };\n}\n/**\n * Initializes a finite field over prime.\n * Major performance optimizations:\n * * a) denormalized operations like mulN instead of mul\n * * b) same object shape: never add or remove keys\n * * c) Object.freeze\n * Fragile: always run a benchmark on a change.\n * Security note: operations don't check 'isValid' for all elements for performance reasons,\n * it is caller responsibility to check this.\n * This is low-level code, please make sure you know what you're doing.\n * @param ORDER prime positive bigint\n * @param bitLen how many bits the field consumes\n * @param isLE (def: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nfunction Field(ORDER, bitLen, isLE = false, redef = {}) {\n    if (ORDER <= _0n)\n        throw new Error('invalid field: expected ORDER > 0, got ' + ORDER);\n    const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, bitLen);\n    if (BYTES > 2048)\n        throw new Error('invalid field: expected ORDER of <= 2048 bytes');\n    let sqrtP; // cached sqrtP\n    const f = Object.freeze({\n        ORDER,\n        isLE,\n        BITS,\n        BYTES,\n        MASK: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(BITS),\n        ZERO: _0n,\n        ONE: _1n,\n        create: (num) => mod(num, ORDER),\n        isValid: (num) => {\n            if (typeof num !== 'bigint')\n                throw new Error('invalid field element: expected bigint, got ' + typeof num);\n            return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n        },\n        is0: (num) => num === _0n,\n        isOdd: (num) => (num & _1n) === _1n,\n        neg: (num) => mod(-num, ORDER),\n        eql: (lhs, rhs) => lhs === rhs,\n        sqr: (num) => mod(num * num, ORDER),\n        add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n        sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n        mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n        pow: (num, power) => FpPow(f, num, power),\n        div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n        // Same as above, but doesn't normalize\n        sqrN: (num) => num * num,\n        addN: (lhs, rhs) => lhs + rhs,\n        subN: (lhs, rhs) => lhs - rhs,\n        mulN: (lhs, rhs) => lhs * rhs,\n        inv: (num) => invert(num, ORDER),\n        sqrt: redef.sqrt ||\n            ((n) => {\n                if (!sqrtP)\n                    sqrtP = FpSqrt(ORDER);\n                return sqrtP(f, n);\n            }),\n        toBytes: (num) => (isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesLE)(num, BYTES) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(num, BYTES)),\n        fromBytes: (bytes) => {\n            if (bytes.length !== BYTES)\n                throw new Error('Field.fromBytes: expected ' + BYTES + ' bytes, got ' + bytes.length);\n            return isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(bytes) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(bytes);\n        },\n        // TODO: we don't need it here, move out to separate fn\n        invertBatch: (lst) => FpInvertBatch(f, lst),\n        // We can't move this out because Fp6, Fp12 implement it\n        // and it's unclear what to return in there.\n        cmov: (a, b, c) => (c ? b : a),\n    });\n    return Object.freeze(f);\n}\nfunction FpSqrtOdd(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? root : Fp.neg(root);\n}\nfunction FpSqrtEven(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use `mapKeyToField` instead\n */\nfunction hashToPrivateScalar(hash, groupOrder, isLE = false) {\n    hash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('privateHash', hash);\n    const hashLen = hash.length;\n    const minLen = nLength(groupOrder).nByteLength + 8;\n    if (minLen < 24 || hashLen < minLen || hashLen > 1024)\n        throw new Error('hashToPrivateScalar: expected ' + minLen + '-1024 bytes of input, got ' + hashLen);\n    const num = isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(hash) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(hash);\n    return mod(num, groupOrder - _1n) + _1n;\n}\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nfunction getFieldBytesLength(fieldOrder) {\n    if (typeof fieldOrder !== 'bigint')\n        throw new Error('field order must be bigint');\n    const bitLength = fieldOrder.toString(2).length;\n    return Math.ceil(bitLength / 8);\n}\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nfunction getMinHashLength(fieldOrder) {\n    const length = getFieldBytesLength(fieldOrder);\n    return length + Math.ceil(length / 2);\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nfunction mapHashToField(key, fieldOrder, isLE = false) {\n    const len = key.length;\n    const fieldLen = getFieldBytesLength(fieldOrder);\n    const minLen = getMinHashLength(fieldOrder);\n    // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n    if (len < 16 || len < minLen || len > 1024)\n        throw new Error('expected ' + minLen + '-1024 bytes of input, got ' + len);\n    const num = isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(key) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(key);\n    // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n    const reduced = mod(num, fieldOrder - _1n) + _1n;\n    return isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesLE)(reduced, fieldLen) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(reduced, fieldLen);\n}\n//# sourceMappingURL=modular.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/modular.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/utils.js":
/*!****************************************************************************!*\
  !*** ./node_modules/viem/node_modules/@noble/curves/esm/abstract/utils.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aInRange: () => (/* binding */ aInRange),\n/* harmony export */   abool: () => (/* binding */ abool),\n/* harmony export */   abytes: () => (/* binding */ abytes),\n/* harmony export */   bitGet: () => (/* binding */ bitGet),\n/* harmony export */   bitLen: () => (/* binding */ bitLen),\n/* harmony export */   bitMask: () => (/* binding */ bitMask),\n/* harmony export */   bitSet: () => (/* binding */ bitSet),\n/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),\n/* harmony export */   bytesToNumberBE: () => (/* binding */ bytesToNumberBE),\n/* harmony export */   bytesToNumberLE: () => (/* binding */ bytesToNumberLE),\n/* harmony export */   concatBytes: () => (/* binding */ concatBytes),\n/* harmony export */   createHmacDrbg: () => (/* binding */ createHmacDrbg),\n/* harmony export */   ensureBytes: () => (/* binding */ ensureBytes),\n/* harmony export */   equalBytes: () => (/* binding */ equalBytes),\n/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),\n/* harmony export */   hexToNumber: () => (/* binding */ hexToNumber),\n/* harmony export */   inRange: () => (/* binding */ inRange),\n/* harmony export */   isBytes: () => (/* binding */ isBytes),\n/* harmony export */   memoized: () => (/* binding */ memoized),\n/* harmony export */   notImplemented: () => (/* binding */ notImplemented),\n/* harmony export */   numberToBytesBE: () => (/* binding */ numberToBytesBE),\n/* harmony export */   numberToBytesLE: () => (/* binding */ numberToBytesLE),\n/* harmony export */   numberToHexUnpadded: () => (/* binding */ numberToHexUnpadded),\n/* harmony export */   numberToVarBytesBE: () => (/* binding */ numberToVarBytesBE),\n/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),\n/* harmony export */   validateObject: () => (/* binding */ validateObject)\n/* harmony export */ });\n/**\n * Hex, bytes and number utilities.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// 100 lines of code in the file are duplicated from noble-hashes (utils).\n// This is OK: `abstract` directory does not use noble-hashes.\n// User may opt-in into using different hashing library. This way, noble-hashes\n// won't be included into their bundle.\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\nfunction abytes(item) {\n    if (!isBytes(item))\n        throw new Error('Uint8Array expected');\n}\nfunction abool(title, value) {\n    if (typeof value !== 'boolean')\n        throw new Error(title + ' boolean expected, got ' + value);\n}\n// Used in weierstrass, der\nfunction numberToHexUnpadded(num) {\n    const hex = num.toString(16);\n    return hex.length & 1 ? '0' + hex : hex;\n}\nfunction hexToNumber(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    return hex === '' ? _0n : BigInt('0x' + hex); // Big Endian\n}\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\nconst hasHexBuiltin = \n// @ts-ignore\ntypeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function';\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * Convert byte array to hex string. Uses built-in function, when available.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nfunction bytesToHex(bytes) {\n    abytes(bytes);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return bytes.toHex();\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * Convert hex string to byte array. Uses built-in function, when available.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return Uint8Array.fromHex(hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n// BE: Big Endian, LE: Little Endian\nfunction bytesToNumberBE(bytes) {\n    return hexToNumber(bytesToHex(bytes));\n}\nfunction bytesToNumberLE(bytes) {\n    abytes(bytes);\n    return hexToNumber(bytesToHex(Uint8Array.from(bytes).reverse()));\n}\nfunction numberToBytesBE(n, len) {\n    return hexToBytes(n.toString(16).padStart(len * 2, '0'));\n}\nfunction numberToBytesLE(n, len) {\n    return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nfunction numberToVarBytesBE(n) {\n    return hexToBytes(numberToHexUnpadded(n));\n}\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nfunction ensureBytes(title, hex, expectedLength) {\n    let res;\n    if (typeof hex === 'string') {\n        try {\n            res = hexToBytes(hex);\n        }\n        catch (e) {\n            throw new Error(title + ' must be hex string or Uint8Array, cause: ' + e);\n        }\n    }\n    else if (isBytes(hex)) {\n        // Uint8Array.from() instead of hash.slice() because node.js Buffer\n        // is instance of Uint8Array, and its slice() creates **mutable** copy\n        res = Uint8Array.from(hex);\n    }\n    else {\n        throw new Error(title + ' must be hex string or Uint8Array');\n    }\n    const len = res.length;\n    if (typeof expectedLength === 'number' && len !== expectedLength)\n        throw new Error(title + ' of length ' + expectedLength + ' expected, got ' + len);\n    return res;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nfunction concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\n// Compares 2 u8a-s in kinda constant time\nfunction equalBytes(a, b) {\n    if (a.length !== b.length)\n        return false;\n    let diff = 0;\n    for (let i = 0; i < a.length; i++)\n        diff |= a[i] ^ b[i];\n    return diff === 0;\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n// Is positive bigint\nconst isPosBig = (n) => typeof n === 'bigint' && _0n <= n;\nfunction inRange(n, min, max) {\n    return isPosBig(n) && isPosBig(min) && isPosBig(max) && min <= n && n < max;\n}\n/**\n * Asserts min <= n < max. NOTE: It's < max and not <= max.\n * @example\n * aInRange('x', x, 1n, 256n); // would assume x is in (1n..255n)\n */\nfunction aInRange(title, n, min, max) {\n    // Why min <= n < max and not a (min < n < max) OR b (min <= n <= max)?\n    // consider P=256n, min=0n, max=P\n    // - a for min=0 would require -1:          `inRange('x', x, -1n, P)`\n    // - b would commonly require subtraction:  `inRange('x', x, 0n, P - 1n)`\n    // - our way is the cleanest:               `inRange('x', x, 0n, P)\n    if (!inRange(n, min, max))\n        throw new Error('expected valid ' + title + ': ' + min + ' <= n < ' + max + ', got ' + n);\n}\n// Bit operations\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n * TODO: merge with nLength in modular\n */\nfunction bitLen(n) {\n    let len;\n    for (len = 0; n > _0n; n >>= _1n, len += 1)\n        ;\n    return len;\n}\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nfunction bitGet(n, pos) {\n    return (n >> BigInt(pos)) & _1n;\n}\n/**\n * Sets single bit at position.\n */\nfunction bitSet(n, pos, value) {\n    return n | ((value ? _1n : _0n) << BigInt(pos));\n}\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nconst bitMask = (n) => (_1n << BigInt(n)) - _1n;\n// DRBG\nconst u8n = (len) => new Uint8Array(len); // creates Uint8Array\nconst u8fr = (arr) => Uint8Array.from(arr); // another shortcut\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nfunction createHmacDrbg(hashLen, qByteLen, hmacFn) {\n    if (typeof hashLen !== 'number' || hashLen < 2)\n        throw new Error('hashLen must be a number');\n    if (typeof qByteLen !== 'number' || qByteLen < 2)\n        throw new Error('qByteLen must be a number');\n    if (typeof hmacFn !== 'function')\n        throw new Error('hmacFn must be a function');\n    // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n    let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n    let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n    let i = 0; // Iterations counter, will throw when over 1000\n    const reset = () => {\n        v.fill(1);\n        k.fill(0);\n        i = 0;\n    };\n    const h = (...b) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n    const reseed = (seed = u8n(0)) => {\n        // HMAC-DRBG reseed() function. Steps D-G\n        k = h(u8fr([0x00]), seed); // k = hmac(k || v || 0x00 || seed)\n        v = h(); // v = hmac(k || v)\n        if (seed.length === 0)\n            return;\n        k = h(u8fr([0x01]), seed); // k = hmac(k || v || 0x01 || seed)\n        v = h(); // v = hmac(k || v)\n    };\n    const gen = () => {\n        // HMAC-DRBG generate() function\n        if (i++ >= 1000)\n            throw new Error('drbg: tried 1000 values');\n        let len = 0;\n        const out = [];\n        while (len < qByteLen) {\n            v = h();\n            const sl = v.slice();\n            out.push(sl);\n            len += v.length;\n        }\n        return concatBytes(...out);\n    };\n    const genUntil = (seed, pred) => {\n        reset();\n        reseed(seed); // Steps D-G\n        let res = undefined; // Step H: grind until k is in [1..n-1]\n        while (!(res = pred(gen())))\n            reseed();\n        reset();\n        return res;\n    };\n    return genUntil;\n}\n// Validating curves and fields\nconst validatorFns = {\n    bigint: (val) => typeof val === 'bigint',\n    function: (val) => typeof val === 'function',\n    boolean: (val) => typeof val === 'boolean',\n    string: (val) => typeof val === 'string',\n    stringOrUint8Array: (val) => typeof val === 'string' || isBytes(val),\n    isSafeInteger: (val) => Number.isSafeInteger(val),\n    array: (val) => Array.isArray(val),\n    field: (val, object) => object.Fp.isValid(val),\n    hash: (val) => typeof val === 'function' && Number.isSafeInteger(val.outputLen),\n};\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\nfunction validateObject(object, validators, optValidators = {}) {\n    const checkField = (fieldName, type, isOptional) => {\n        const checkVal = validatorFns[type];\n        if (typeof checkVal !== 'function')\n            throw new Error('invalid validator function');\n        const val = object[fieldName];\n        if (isOptional && val === undefined)\n            return;\n        if (!checkVal(val, object)) {\n            throw new Error('param ' + String(fieldName) + ' is invalid. Expected ' + type + ', got ' + val);\n        }\n    };\n    for (const [fieldName, type] of Object.entries(validators))\n        checkField(fieldName, type, false);\n    for (const [fieldName, type] of Object.entries(optValidators))\n        checkField(fieldName, type, true);\n    return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\n/**\n * throws not implemented error\n */\nconst notImplemented = () => {\n    throw new Error('not implemented');\n};\n/**\n * Memoizes (caches) computation result.\n * Uses WeakMap: the value is going auto-cleaned by GC after last reference is removed.\n */\nfunction memoized(fn) {\n    const map = new WeakMap();\n    return (arg, ...args) => {\n        const val = map.get(arg);\n        if (val !== undefined)\n            return val;\n        const computed = fn(arg, ...args);\n        map.set(arg, computed);\n        return computed;\n    };\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/weierstrass.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/viem/node_modules/@noble/curves/esm/abstract/weierstrass.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DER: () => (/* binding */ DER),\n/* harmony export */   DERErr: () => (/* binding */ DERErr),\n/* harmony export */   SWUFpSqrtRatio: () => (/* binding */ SWUFpSqrtRatio),\n/* harmony export */   mapToCurveSimpleSWU: () => (/* binding */ mapToCurveSimpleSWU),\n/* harmony export */   weierstrass: () => (/* binding */ weierstrass),\n/* harmony export */   weierstrassPoints: () => (/* binding */ weierstrassPoints)\n/* harmony export */ });\n/* harmony import */ var _curve_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curve.js */ \"(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/curve.js\");\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Short Weierstrass curve methods. The formula is: y² = x³ + ax + b.\n *\n * ### Parameters\n *\n * To initialize a weierstrass curve, one needs to pass following params:\n *\n * * a: formula param\n * * b: formula param\n * * Fp: finite field of prime characteristic P; may be complex (Fp2). Arithmetics is done in field\n * * n: order of prime subgroup a.k.a total amount of valid curve points\n * * Gx: Base point (x, y) aka generator point. Gx = x coordinate\n * * Gy: ...y coordinate\n * * h: cofactor, usually 1. h*n = curve group order (n is only subgroup order)\n * * lowS: whether to enable (default) or disable \"low-s\" non-malleable signatures\n *\n * ### Design rationale for types\n *\n * * Interaction between classes from different curves should fail:\n *   `k256.Point.BASE.add(p256.Point.BASE)`\n * * For this purpose we want to use `instanceof` operator, which is fast and works during runtime\n * * Different calls of `curve()` would return different classes -\n *   `curve(params) !== curve(params)`: if somebody decided to monkey-patch their curve,\n *   it won't affect others\n *\n * TypeScript can't infer types for classes created inside a function. Classes is one instance\n * of nominative types in TypeScript and interfaces only check for shape, so it's hard to create\n * unique type for every function call.\n *\n * We can use generic types via some param, like curve opts, but that would:\n *     1. Enable interaction between `curve(params)` and `curve(params)` (curves of same params)\n *     which is hard to debug.\n *     2. Params can be generic and we can't enforce them to be constant value:\n *     if somebody creates curve from non-constant params,\n *     it would be allowed to interact with other curves with non-constant params\n *\n * @todo https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-7.html#unique-symbol\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// prettier-ignore\n\n// prettier-ignore\n\n// prettier-ignore\n\nfunction validateSigVerOpts(opts) {\n    if (opts.lowS !== undefined)\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('lowS', opts.lowS);\n    if (opts.prehash !== undefined)\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('prehash', opts.prehash);\n}\nfunction validatePointOpts(curve) {\n    const opts = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.validateBasic)(curve);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(opts, {\n        a: 'field',\n        b: 'field',\n    }, {\n        allowInfinityPoint: 'boolean',\n        allowedPrivateKeyLengths: 'array',\n        clearCofactor: 'function',\n        fromBytes: 'function',\n        isTorsionFree: 'function',\n        toBytes: 'function',\n        wrapPrivateKey: 'boolean',\n    });\n    const { endo, Fp, a } = opts;\n    if (endo) {\n        if (!Fp.eql(a, Fp.ZERO)) {\n            throw new Error('invalid endo: CURVE.a must be 0');\n        }\n        if (typeof endo !== 'object' ||\n            typeof endo.beta !== 'bigint' ||\n            typeof endo.splitScalar !== 'function') {\n            throw new Error('invalid endo: expected \"beta\": bigint and \"splitScalar\": function');\n        }\n    }\n    return Object.freeze({ ...opts });\n}\nclass DERErr extends Error {\n    constructor(m = '') {\n        super(m);\n    }\n}\n/**\n * ASN.1 DER encoding utilities. ASN is very complex & fragile. Format:\n *\n *     [0x30 (SEQUENCE), bytelength, 0x02 (INTEGER), intLength, R, 0x02 (INTEGER), intLength, S]\n *\n * Docs: https://letsencrypt.org/docs/a-warm-welcome-to-asn1-and-der/, https://luca.ntop.org/Teaching/Appunti/asn1.html\n */\nconst DER = {\n    // asn.1 DER encoding utils\n    Err: DERErr,\n    // Basic building block is TLV (Tag-Length-Value)\n    _tlv: {\n        encode: (tag, data) => {\n            const { Err: E } = DER;\n            if (tag < 0 || tag > 256)\n                throw new E('tlv.encode: wrong tag');\n            if (data.length & 1)\n                throw new E('tlv.encode: unpadded data');\n            const dataLen = data.length / 2;\n            const len = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded)(dataLen);\n            if ((len.length / 2) & 128)\n                throw new E('tlv.encode: long form length too big');\n            // length of length with long form flag\n            const lenLen = dataLen > 127 ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded)((len.length / 2) | 128) : '';\n            const t = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded)(tag);\n            return t + lenLen + len + data;\n        },\n        // v - value, l - left bytes (unparsed)\n        decode(tag, data) {\n            const { Err: E } = DER;\n            let pos = 0;\n            if (tag < 0 || tag > 256)\n                throw new E('tlv.encode: wrong tag');\n            if (data.length < 2 || data[pos++] !== tag)\n                throw new E('tlv.decode: wrong tlv');\n            const first = data[pos++];\n            const isLong = !!(first & 128); // First bit of first length byte is flag for short/long form\n            let length = 0;\n            if (!isLong)\n                length = first;\n            else {\n                // Long form: [longFlag(1bit), lengthLength(7bit), length (BE)]\n                const lenLen = first & 127;\n                if (!lenLen)\n                    throw new E('tlv.decode(long): indefinite length not supported');\n                if (lenLen > 4)\n                    throw new E('tlv.decode(long): byte length is too big'); // this will overflow u32 in js\n                const lengthBytes = data.subarray(pos, pos + lenLen);\n                if (lengthBytes.length !== lenLen)\n                    throw new E('tlv.decode: length bytes not complete');\n                if (lengthBytes[0] === 0)\n                    throw new E('tlv.decode(long): zero leftmost byte');\n                for (const b of lengthBytes)\n                    length = (length << 8) | b;\n                pos += lenLen;\n                if (length < 128)\n                    throw new E('tlv.decode(long): not minimal encoding');\n            }\n            const v = data.subarray(pos, pos + length);\n            if (v.length !== length)\n                throw new E('tlv.decode: wrong value length');\n            return { v, l: data.subarray(pos + length) };\n        },\n    },\n    // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n    // since we always use positive integers here. It must always be empty:\n    // - add zero byte if exists\n    // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n    _int: {\n        encode(num) {\n            const { Err: E } = DER;\n            if (num < _0n)\n                throw new E('integer: negative integers are not allowed');\n            let hex = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded)(num);\n            // Pad with zero byte if negative flag is present\n            if (Number.parseInt(hex[0], 16) & 0b1000)\n                hex = '00' + hex;\n            if (hex.length & 1)\n                throw new E('unexpected DER parsing assertion: unpadded hex');\n            return hex;\n        },\n        decode(data) {\n            const { Err: E } = DER;\n            if (data[0] & 128)\n                throw new E('invalid signature integer: negative');\n            if (data[0] === 0x00 && !(data[1] & 128))\n                throw new E('invalid signature integer: unnecessary leading zero');\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(data);\n        },\n    },\n    toSig(hex) {\n        // parse DER signature\n        const { Err: E, _int: int, _tlv: tlv } = DER;\n        const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('signature', hex);\n        const { v: seqBytes, l: seqLeftBytes } = tlv.decode(0x30, data);\n        if (seqLeftBytes.length)\n            throw new E('invalid signature: left bytes after parsing');\n        const { v: rBytes, l: rLeftBytes } = tlv.decode(0x02, seqBytes);\n        const { v: sBytes, l: sLeftBytes } = tlv.decode(0x02, rLeftBytes);\n        if (sLeftBytes.length)\n            throw new E('invalid signature: left bytes after parsing');\n        return { r: int.decode(rBytes), s: int.decode(sBytes) };\n    },\n    hexFromSig(sig) {\n        const { _tlv: tlv, _int: int } = DER;\n        const rs = tlv.encode(0x02, int.encode(sig.r));\n        const ss = tlv.encode(0x02, int.encode(sig.s));\n        const seq = rs + ss;\n        return tlv.encode(0x30, seq);\n    },\n};\nfunction numToSizedHex(num, size) {\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex)((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(num, size));\n}\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3), _4n = BigInt(4);\nfunction weierstrassPoints(opts) {\n    const CURVE = validatePointOpts(opts);\n    const { Fp } = CURVE; // All curves has same field / group length as for now, but they can differ\n    const Fn = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.Field)(CURVE.n, CURVE.nBitLength);\n    const toBytes = CURVE.toBytes ||\n        ((_c, point, _isCompressed) => {\n            const a = point.toAffine();\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(Uint8Array.from([0x04]), Fp.toBytes(a.x), Fp.toBytes(a.y));\n        });\n    const fromBytes = CURVE.fromBytes ||\n        ((bytes) => {\n            // const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // if (head !== 0x04) throw new Error('Only non-compressed encoding is supported');\n            const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n            const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n            return { x, y };\n        });\n    /**\n     * y² = x³ + ax + b: Short weierstrass curve formula. Takes x, returns y².\n     * @returns y²\n     */\n    function weierstrassEquation(x) {\n        const { a, b } = CURVE;\n        const x2 = Fp.sqr(x); // x * x\n        const x3 = Fp.mul(x2, x); // x² * x\n        return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x³ + a * x + b\n    }\n    function isValidXY(x, y) {\n        const left = Fp.sqr(y); // y²\n        const right = weierstrassEquation(x); // x³ + ax + b\n        return Fp.eql(left, right);\n    }\n    // Validate whether the passed curve params are valid.\n    // Test 1: equation y² = x³ + ax + b should work for generator point.\n    if (!isValidXY(CURVE.Gx, CURVE.Gy))\n        throw new Error('bad curve params: generator point');\n    // Test 2: discriminant Δ part should be non-zero: 4a³ + 27b² != 0.\n    // Guarantees curve is genus-1, smooth (non-singular).\n    const _4a3 = Fp.mul(Fp.pow(CURVE.a, _3n), _4n);\n    const _27b2 = Fp.mul(Fp.sqr(CURVE.b), BigInt(27));\n    if (Fp.is0(Fp.add(_4a3, _27b2)))\n        throw new Error('bad curve params: a or b');\n    // Valid group elements reside in range 1..n-1\n    function isWithinCurveOrder(num) {\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.inRange)(num, _1n, CURVE.n);\n    }\n    // Validates if priv key is valid and converts it to bigint.\n    // Supports options allowedPrivateKeyLengths and wrapPrivateKey.\n    function normPrivateKeyToScalar(key) {\n        const { allowedPrivateKeyLengths: lengths, nByteLength, wrapPrivateKey, n: N } = CURVE;\n        if (lengths && typeof key !== 'bigint') {\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes)(key))\n                key = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex)(key);\n            // Normalize to hex string, pad. E.g. P521 would norm 130-132 char hex to 132-char bytes\n            if (typeof key !== 'string' || !lengths.includes(key.length))\n                throw new Error('invalid private key');\n            key = key.padStart(nByteLength * 2, '0');\n        }\n        let num;\n        try {\n            num =\n                typeof key === 'bigint'\n                    ? key\n                    : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('private key', key, nByteLength));\n        }\n        catch (error) {\n            throw new Error('invalid private key, expected hex or ' + nByteLength + ' bytes, got ' + typeof key);\n        }\n        if (wrapPrivateKey)\n            num = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mod)(num, N); // disabled by default, enabled for BLS\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('private key', num, _1n, N); // num in range [1..N-1]\n        return num;\n    }\n    function aprjpoint(other) {\n        if (!(other instanceof Point))\n            throw new Error('ProjectivePoint expected');\n    }\n    // Memoized toAffine / validity check. They are heavy. Points are immutable.\n    // Converts Projective point to affine (x, y) coordinates.\n    // Can accept precomputed Z^-1 - for example, from invertBatch.\n    // (X, Y, Z) ∋ (x=X/Z, y=Y/Z)\n    const toAffineMemo = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memoized)((p, iz) => {\n        const { px: x, py: y, pz: z } = p;\n        // Fast-path for normalized points\n        if (Fp.eql(z, Fp.ONE))\n            return { x, y };\n        const is0 = p.is0();\n        // If invZ was 0, we return zero point. However we still want to execute\n        // all operations, so we replace invZ with a random number, 1.\n        if (iz == null)\n            iz = is0 ? Fp.ONE : Fp.inv(z);\n        const ax = Fp.mul(x, iz);\n        const ay = Fp.mul(y, iz);\n        const zz = Fp.mul(z, iz);\n        if (is0)\n            return { x: Fp.ZERO, y: Fp.ZERO };\n        if (!Fp.eql(zz, Fp.ONE))\n            throw new Error('invZ was invalid');\n        return { x: ax, y: ay };\n    });\n    // NOTE: on exception this will crash 'cached' and no value will be set.\n    // Otherwise true will be return\n    const assertValidMemo = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memoized)((p) => {\n        if (p.is0()) {\n            // (0, 1, 0) aka ZERO is invalid in most contexts.\n            // In BLS, ZERO can be serialized, so we allow it.\n            // (0, 0, 0) is invalid representation of ZERO.\n            if (CURVE.allowInfinityPoint && !Fp.is0(p.py))\n                return;\n            throw new Error('bad point: ZERO');\n        }\n        // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n        const { x, y } = p.toAffine();\n        // Check if x, y are valid field elements\n        if (!Fp.isValid(x) || !Fp.isValid(y))\n            throw new Error('bad point: x or y not FE');\n        if (!isValidXY(x, y))\n            throw new Error('bad point: equation left != right');\n        if (!p.isTorsionFree())\n            throw new Error('bad point: not in prime-order subgroup');\n        return true;\n    });\n    /**\n     * Projective Point works in 3d / projective (homogeneous) coordinates: (X, Y, Z) ∋ (x=X/Z, y=Y/Z)\n     * Default Point works in 2d / affine coordinates: (x, y)\n     * We're doing calculations in projective, because its operations don't require costly inversion.\n     */\n    class Point {\n        constructor(px, py, pz) {\n            if (px == null || !Fp.isValid(px))\n                throw new Error('x required');\n            if (py == null || !Fp.isValid(py) || Fp.is0(py))\n                throw new Error('y required');\n            if (pz == null || !Fp.isValid(pz))\n                throw new Error('z required');\n            this.px = px;\n            this.py = py;\n            this.pz = pz;\n            Object.freeze(this);\n        }\n        // Does not validate if the point is on-curve.\n        // Use fromHex instead, or call assertValidity() later.\n        static fromAffine(p) {\n            const { x, y } = p || {};\n            if (!p || !Fp.isValid(x) || !Fp.isValid(y))\n                throw new Error('invalid affine point');\n            if (p instanceof Point)\n                throw new Error('projective point not allowed');\n            const is0 = (i) => Fp.eql(i, Fp.ZERO);\n            // fromAffine(x:0, y:0) would produce (x:0, y:0, z:1), but we need (x:0, y:1, z:0)\n            if (is0(x) && is0(y))\n                return Point.ZERO;\n            return new Point(x, y, Fp.ONE);\n        }\n        get x() {\n            return this.toAffine().x;\n        }\n        get y() {\n            return this.toAffine().y;\n        }\n        /**\n         * Takes a bunch of Projective Points but executes only one\n         * inversion on all of them. Inversion is very slow operation,\n         * so this improves performance massively.\n         * Optimization: converts a list of projective points to a list of identical points with Z=1.\n         */\n        static normalizeZ(points) {\n            const toInv = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.FpInvertBatch)(Fp, points.map((p) => p.pz));\n            return points.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n        }\n        /**\n         * Converts hash string or Uint8Array to Point.\n         * @param hex short/long ECDSA hex\n         */\n        static fromHex(hex) {\n            const P = Point.fromAffine(fromBytes((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('pointHex', hex)));\n            P.assertValidity();\n            return P;\n        }\n        // Multiplies generator point by privateKey.\n        static fromPrivateKey(privateKey) {\n            return Point.BASE.multiply(normPrivateKeyToScalar(privateKey));\n        }\n        // Multiscalar Multiplication\n        static msm(points, scalars) {\n            return (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.pippenger)(Point, Fn, points, scalars);\n        }\n        // \"Private method\", don't use it directly\n        _setWindowSize(windowSize) {\n            wnaf.setWindowSize(this, windowSize);\n        }\n        // A point on curve is valid if it conforms to equation.\n        assertValidity() {\n            assertValidMemo(this);\n        }\n        hasEvenY() {\n            const { y } = this.toAffine();\n            if (Fp.isOdd)\n                return !Fp.isOdd(y);\n            throw new Error(\"Field doesn't support isOdd\");\n        }\n        /**\n         * Compare one point to another.\n         */\n        equals(other) {\n            aprjpoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n            const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n            return U1 && U2;\n        }\n        /**\n         * Flips point to one corresponding to (x, -y) in Affine coordinates.\n         */\n        negate() {\n            return new Point(this.px, Fp.neg(this.py), this.pz);\n        }\n        // Renes-Costello-Batina exception-free doubling formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 3\n        // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n        double() {\n            const { a, b } = CURVE;\n            const b3 = Fp.mul(b, _3n);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            let t0 = Fp.mul(X1, X1); // step 1\n            let t1 = Fp.mul(Y1, Y1);\n            let t2 = Fp.mul(Z1, Z1);\n            let t3 = Fp.mul(X1, Y1);\n            t3 = Fp.add(t3, t3); // step 5\n            Z3 = Fp.mul(X1, Z1);\n            Z3 = Fp.add(Z3, Z3);\n            X3 = Fp.mul(a, Z3);\n            Y3 = Fp.mul(b3, t2);\n            Y3 = Fp.add(X3, Y3); // step 10\n            X3 = Fp.sub(t1, Y3);\n            Y3 = Fp.add(t1, Y3);\n            Y3 = Fp.mul(X3, Y3);\n            X3 = Fp.mul(t3, X3);\n            Z3 = Fp.mul(b3, Z3); // step 15\n            t2 = Fp.mul(a, t2);\n            t3 = Fp.sub(t0, t2);\n            t3 = Fp.mul(a, t3);\n            t3 = Fp.add(t3, Z3);\n            Z3 = Fp.add(t0, t0); // step 20\n            t0 = Fp.add(Z3, t0);\n            t0 = Fp.add(t0, t2);\n            t0 = Fp.mul(t0, t3);\n            Y3 = Fp.add(Y3, t0);\n            t2 = Fp.mul(Y1, Z1); // step 25\n            t2 = Fp.add(t2, t2);\n            t0 = Fp.mul(t2, t3);\n            X3 = Fp.sub(X3, t0);\n            Z3 = Fp.mul(t2, t1);\n            Z3 = Fp.add(Z3, Z3); // step 30\n            Z3 = Fp.add(Z3, Z3);\n            return new Point(X3, Y3, Z3);\n        }\n        // Renes-Costello-Batina exception-free addition formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 1\n        // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n        add(other) {\n            aprjpoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            const a = CURVE.a;\n            const b3 = Fp.mul(CURVE.b, _3n);\n            let t0 = Fp.mul(X1, X2); // step 1\n            let t1 = Fp.mul(Y1, Y2);\n            let t2 = Fp.mul(Z1, Z2);\n            let t3 = Fp.add(X1, Y1);\n            let t4 = Fp.add(X2, Y2); // step 5\n            t3 = Fp.mul(t3, t4);\n            t4 = Fp.add(t0, t1);\n            t3 = Fp.sub(t3, t4);\n            t4 = Fp.add(X1, Z1);\n            let t5 = Fp.add(X2, Z2); // step 10\n            t4 = Fp.mul(t4, t5);\n            t5 = Fp.add(t0, t2);\n            t4 = Fp.sub(t4, t5);\n            t5 = Fp.add(Y1, Z1);\n            X3 = Fp.add(Y2, Z2); // step 15\n            t5 = Fp.mul(t5, X3);\n            X3 = Fp.add(t1, t2);\n            t5 = Fp.sub(t5, X3);\n            Z3 = Fp.mul(a, t4);\n            X3 = Fp.mul(b3, t2); // step 20\n            Z3 = Fp.add(X3, Z3);\n            X3 = Fp.sub(t1, Z3);\n            Z3 = Fp.add(t1, Z3);\n            Y3 = Fp.mul(X3, Z3);\n            t1 = Fp.add(t0, t0); // step 25\n            t1 = Fp.add(t1, t0);\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.mul(b3, t4);\n            t1 = Fp.add(t1, t2);\n            t2 = Fp.sub(t0, t2); // step 30\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.add(t4, t2);\n            t0 = Fp.mul(t1, t4);\n            Y3 = Fp.add(Y3, t0);\n            t0 = Fp.mul(t5, t4); // step 35\n            X3 = Fp.mul(t3, X3);\n            X3 = Fp.sub(X3, t0);\n            t0 = Fp.mul(t3, t1);\n            Z3 = Fp.mul(t5, Z3);\n            Z3 = Fp.add(Z3, t0); // step 40\n            return new Point(X3, Y3, Z3);\n        }\n        subtract(other) {\n            return this.add(other.negate());\n        }\n        is0() {\n            return this.equals(Point.ZERO);\n        }\n        wNAF(n) {\n            return wnaf.wNAFCached(this, n, Point.normalizeZ);\n        }\n        /**\n         * Non-constant-time multiplication. Uses double-and-add algorithm.\n         * It's faster, but should only be used when you don't care about\n         * an exposed private key e.g. sig verification, which works over *public* keys.\n         */\n        multiplyUnsafe(sc) {\n            const { endo, n: N } = CURVE;\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('scalar', sc, _0n, N);\n            const I = Point.ZERO;\n            if (sc === _0n)\n                return I;\n            if (this.is0() || sc === _1n)\n                return this;\n            // Case a: no endomorphism. Case b: has precomputes.\n            if (!endo || wnaf.hasPrecomputes(this))\n                return wnaf.wNAFCachedUnsafe(this, sc, Point.normalizeZ);\n            // Case c: endomorphism\n            /** See docs for {@link EndomorphismOpts} */\n            let { k1neg, k1, k2neg, k2 } = endo.splitScalar(sc);\n            let k1p = I;\n            let k2p = I;\n            let d = this;\n            while (k1 > _0n || k2 > _0n) {\n                if (k1 & _1n)\n                    k1p = k1p.add(d);\n                if (k2 & _1n)\n                    k2p = k2p.add(d);\n                d = d.double();\n                k1 >>= _1n;\n                k2 >>= _1n;\n            }\n            if (k1neg)\n                k1p = k1p.negate();\n            if (k2neg)\n                k2p = k2p.negate();\n            k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n            return k1p.add(k2p);\n        }\n        /**\n         * Constant time multiplication.\n         * Uses wNAF method. Windowed method may be 10% faster,\n         * but takes 2x longer to generate and consumes 2x memory.\n         * Uses precomputes when available.\n         * Uses endomorphism for Koblitz curves.\n         * @param scalar by which the point would be multiplied\n         * @returns New point\n         */\n        multiply(scalar) {\n            const { endo, n: N } = CURVE;\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('scalar', scalar, _1n, N);\n            let point, fake; // Fake point is used to const-time mult\n            /** See docs for {@link EndomorphismOpts} */\n            if (endo) {\n                const { k1neg, k1, k2neg, k2 } = endo.splitScalar(scalar);\n                let { p: k1p, f: f1p } = this.wNAF(k1);\n                let { p: k2p, f: f2p } = this.wNAF(k2);\n                k1p = wnaf.constTimeNegate(k1neg, k1p);\n                k2p = wnaf.constTimeNegate(k2neg, k2p);\n                k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n                point = k1p.add(k2p);\n                fake = f1p.add(f2p);\n            }\n            else {\n                const { p, f } = this.wNAF(scalar);\n                point = p;\n                fake = f;\n            }\n            // Normalize `z` for both points, but return only real one\n            return Point.normalizeZ([point, fake])[0];\n        }\n        /**\n         * Efficiently calculate `aP + bQ`. Unsafe, can expose private key, if used incorrectly.\n         * Not using Strauss-Shamir trick: precomputation tables are faster.\n         * The trick could be useful if both P and Q are not G (not in our case).\n         * @returns non-zero affine point\n         */\n        multiplyAndAddUnsafe(Q, a, b) {\n            const G = Point.BASE; // No Strauss-Shamir trick: we have 10% faster G precomputes\n            const mul = (P, a // Select faster multiply() method\n            ) => (a === _0n || a === _1n || !P.equals(G) ? P.multiplyUnsafe(a) : P.multiply(a));\n            const sum = mul(this, a).add(mul(Q, b));\n            return sum.is0() ? undefined : sum;\n        }\n        // Converts Projective point to affine (x, y) coordinates.\n        // Can accept precomputed Z^-1 - for example, from invertBatch.\n        // (x, y, z) ∋ (x=x/z, y=y/z)\n        toAffine(iz) {\n            return toAffineMemo(this, iz);\n        }\n        isTorsionFree() {\n            const { h: cofactor, isTorsionFree } = CURVE;\n            if (cofactor === _1n)\n                return true; // No subgroups, always torsion-free\n            if (isTorsionFree)\n                return isTorsionFree(Point, this);\n            throw new Error('isTorsionFree() has not been declared for the elliptic curve');\n        }\n        clearCofactor() {\n            const { h: cofactor, clearCofactor } = CURVE;\n            if (cofactor === _1n)\n                return this; // Fast-path\n            if (clearCofactor)\n                return clearCofactor(Point, this);\n            return this.multiplyUnsafe(CURVE.h);\n        }\n        toRawBytes(isCompressed = true) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            this.assertValidity();\n            return toBytes(Point, this, isCompressed);\n        }\n        toHex(isCompressed = true) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex)(this.toRawBytes(isCompressed));\n        }\n    }\n    // base / generator point\n    Point.BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n    // zero / infinity / identity point\n    Point.ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO); // 0, 1, 0\n    const { endo, nBitLength } = CURVE;\n    const wnaf = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.wNAF)(Point, endo ? Math.ceil(nBitLength / 2) : nBitLength);\n    return {\n        CURVE,\n        ProjectivePoint: Point,\n        normPrivateKeyToScalar,\n        weierstrassEquation,\n        isWithinCurveOrder,\n    };\n}\nfunction validateOpts(curve) {\n    const opts = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.validateBasic)(curve);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(opts, {\n        hash: 'hash',\n        hmac: 'function',\n        randomBytes: 'function',\n    }, {\n        bits2int: 'function',\n        bits2int_modN: 'function',\n        lowS: 'boolean',\n    });\n    return Object.freeze({ lowS: true, ...opts });\n}\n/**\n * Creates short weierstrass curve and ECDSA signature methods for it.\n * @example\n * import { Field } from '@noble/curves/abstract/modular';\n * // Before that, define BigInt-s: a, b, p, n, Gx, Gy\n * const curve = weierstrass({ a, b, Fp: Field(p), n, Gx, Gy, h: 1n })\n */\nfunction weierstrass(curveDef) {\n    const CURVE = validateOpts(curveDef);\n    const { Fp, n: CURVE_ORDER, nByteLength, nBitLength } = CURVE;\n    const compressedLen = Fp.BYTES + 1; // e.g. 33 for 32\n    const uncompressedLen = 2 * Fp.BYTES + 1; // e.g. 65 for 32\n    function modN(a) {\n        return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mod)(a, CURVE_ORDER);\n    }\n    function invN(a) {\n        return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.invert)(a, CURVE_ORDER);\n    }\n    const { ProjectivePoint: Point, normPrivateKeyToScalar, weierstrassEquation, isWithinCurveOrder, } = weierstrassPoints({\n        ...CURVE,\n        toBytes(_c, point, isCompressed) {\n            const a = point.toAffine();\n            const x = Fp.toBytes(a.x);\n            const cat = _utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes;\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            if (isCompressed) {\n                return cat(Uint8Array.from([point.hasEvenY() ? 0x02 : 0x03]), x);\n            }\n            else {\n                return cat(Uint8Array.from([0x04]), x, Fp.toBytes(a.y));\n            }\n        },\n        fromBytes(bytes) {\n            const len = bytes.length;\n            const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // this.assertValidity() is done inside of fromHex\n            if (len === compressedLen && (head === 0x02 || head === 0x03)) {\n                const x = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(tail);\n                if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.inRange)(x, _1n, Fp.ORDER))\n                    throw new Error('Point is not on curve');\n                const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n                let y;\n                try {\n                    y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n                }\n                catch (sqrtError) {\n                    const suffix = sqrtError instanceof Error ? ': ' + sqrtError.message : '';\n                    throw new Error('Point is not on curve' + suffix);\n                }\n                const isYOdd = (y & _1n) === _1n;\n                // ECDSA\n                const isHeadOdd = (head & 1) === 1;\n                if (isHeadOdd !== isYOdd)\n                    y = Fp.neg(y);\n                return { x, y };\n            }\n            else if (len === uncompressedLen && head === 0x04) {\n                const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n                const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n                return { x, y };\n            }\n            else {\n                const cl = compressedLen;\n                const ul = uncompressedLen;\n                throw new Error('invalid Point, expected length of ' + cl + ', or uncompressed ' + ul + ', got ' + len);\n            }\n        },\n    });\n    function isBiggerThanHalfOrder(number) {\n        const HALF = CURVE_ORDER >> _1n;\n        return number > HALF;\n    }\n    function normalizeS(s) {\n        return isBiggerThanHalfOrder(s) ? modN(-s) : s;\n    }\n    // slice bytes num\n    const slcNum = (b, from, to) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(b.slice(from, to));\n    /**\n     * ECDSA signature with its (r, s) properties. Supports DER & compact representations.\n     */\n    class Signature {\n        constructor(r, s, recovery) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('r', r, _1n, CURVE_ORDER); // r in [1..N]\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('s', s, _1n, CURVE_ORDER); // s in [1..N]\n            this.r = r;\n            this.s = s;\n            if (recovery != null)\n                this.recovery = recovery;\n            Object.freeze(this);\n        }\n        // pair (bytes of r, bytes of s)\n        static fromCompact(hex) {\n            const l = nByteLength;\n            hex = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('compactSignature', hex, l * 2);\n            return new Signature(slcNum(hex, 0, l), slcNum(hex, l, 2 * l));\n        }\n        // DER encoded ECDSA signature\n        // https://bitcoin.stackexchange.com/questions/57644/what-are-the-parts-of-a-bitcoin-transaction-input-script\n        static fromDER(hex) {\n            const { r, s } = DER.toSig((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('DER', hex));\n            return new Signature(r, s);\n        }\n        /**\n         * @todo remove\n         * @deprecated\n         */\n        assertValidity() { }\n        addRecoveryBit(recovery) {\n            return new Signature(this.r, this.s, recovery);\n        }\n        recoverPublicKey(msgHash) {\n            const { r, s, recovery: rec } = this;\n            const h = bits2int_modN((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash)); // Truncate hash\n            if (rec == null || ![0, 1, 2, 3].includes(rec))\n                throw new Error('recovery id invalid');\n            const radj = rec === 2 || rec === 3 ? r + CURVE.n : r;\n            if (radj >= Fp.ORDER)\n                throw new Error('recovery id 2 or 3 invalid');\n            const prefix = (rec & 1) === 0 ? '02' : '03';\n            const R = Point.fromHex(prefix + numToSizedHex(radj, Fp.BYTES));\n            const ir = invN(radj); // r^-1\n            const u1 = modN(-h * ir); // -hr^-1\n            const u2 = modN(s * ir); // sr^-1\n            const Q = Point.BASE.multiplyAndAddUnsafe(R, u1, u2); // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1)\n            if (!Q)\n                throw new Error('point at infinify'); // unsafe is fine: no priv data leaked\n            Q.assertValidity();\n            return Q;\n        }\n        // Signatures should be low-s, to prevent malleability.\n        hasHighS() {\n            return isBiggerThanHalfOrder(this.s);\n        }\n        normalizeS() {\n            return this.hasHighS() ? new Signature(this.r, modN(-this.s), this.recovery) : this;\n        }\n        // DER-encoded\n        toDERRawBytes() {\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.hexToBytes)(this.toDERHex());\n        }\n        toDERHex() {\n            return DER.hexFromSig(this);\n        }\n        // padded bytes of r, then padded bytes of s\n        toCompactRawBytes() {\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.hexToBytes)(this.toCompactHex());\n        }\n        toCompactHex() {\n            const l = nByteLength;\n            return numToSizedHex(this.r, l) + numToSizedHex(this.s, l);\n        }\n    }\n    const utils = {\n        isValidPrivateKey(privateKey) {\n            try {\n                normPrivateKeyToScalar(privateKey);\n                return true;\n            }\n            catch (error) {\n                return false;\n            }\n        },\n        normPrivateKeyToScalar: normPrivateKeyToScalar,\n        /**\n         * Produces cryptographically secure private key from random of size\n         * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n         */\n        randomPrivateKey: () => {\n            const length = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.getMinHashLength)(CURVE.n);\n            return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mapHashToField)(CURVE.randomBytes(length), CURVE.n);\n        },\n        /**\n         * Creates precompute table for an arbitrary EC point. Makes point \"cached\".\n         * Allows to massively speed-up `point.multiply(scalar)`.\n         * @returns cached point\n         * @example\n         * const fast = utils.precompute(8, ProjectivePoint.fromHex(someonesPubKey));\n         * fast.multiply(privKey); // much faster ECDH now\n         */\n        precompute(windowSize = 8, point = Point.BASE) {\n            point._setWindowSize(windowSize);\n            point.multiply(BigInt(3)); // 3 is arbitrary, just need any number here\n            return point;\n        },\n    };\n    /**\n     * Computes public key for a private key. Checks for validity of the private key.\n     * @param privateKey private key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns Public key, full when isCompressed=false; short when isCompressed=true\n     */\n    function getPublicKey(privateKey, isCompressed = true) {\n        return Point.fromPrivateKey(privateKey).toRawBytes(isCompressed);\n    }\n    /**\n     * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n     */\n    function isProbPub(item) {\n        if (typeof item === 'bigint')\n            return false;\n        if (item instanceof Point)\n            return true;\n        const arr = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('key', item);\n        const len = arr.length;\n        const fpl = Fp.BYTES;\n        const compLen = fpl + 1; // e.g. 33 for 32\n        const uncompLen = 2 * fpl + 1; // e.g. 65 for 32\n        if (CURVE.allowedPrivateKeyLengths || nByteLength === compLen) {\n            return undefined;\n        }\n        else {\n            return len === compLen || len === uncompLen;\n        }\n    }\n    /**\n     * ECDH (Elliptic Curve Diffie Hellman).\n     * Computes shared public key from private key and public key.\n     * Checks: 1) private key validity 2) shared key is on-curve.\n     * Does NOT hash the result.\n     * @param privateA private key\n     * @param publicB different public key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns shared public key\n     */\n    function getSharedSecret(privateA, publicB, isCompressed = true) {\n        if (isProbPub(privateA) === true)\n            throw new Error('first arg must be private key');\n        if (isProbPub(publicB) === false)\n            throw new Error('second arg must be public key');\n        const b = Point.fromHex(publicB); // check for being on-curve\n        return b.multiply(normPrivateKeyToScalar(privateA)).toRawBytes(isCompressed);\n    }\n    // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n    // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n    // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n    // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n    const bits2int = CURVE.bits2int ||\n        function (bytes) {\n            // Our custom check \"just in case\", for protection against DoS\n            if (bytes.length > 8192)\n                throw new Error('input is too large');\n            // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n            // for some cases, since bytes.length * 8 is not actual bitLength.\n            const num = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(bytes); // check for == u8 done here\n            const delta = bytes.length * 8 - nBitLength; // truncate to nBitLength leftmost bits\n            return delta > 0 ? num >> BigInt(delta) : num;\n        };\n    const bits2int_modN = CURVE.bits2int_modN ||\n        function (bytes) {\n            return modN(bits2int(bytes)); // can't use bytesToNumberBE here\n        };\n    // NOTE: pads output with zero as per spec\n    const ORDER_MASK = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(nBitLength);\n    /**\n     * Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`.\n     */\n    function int2octets(num) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('num < 2^' + nBitLength, num, _0n, ORDER_MASK);\n        // works with order, can have different size than numToField!\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(num, nByteLength);\n    }\n    // Steps A, D of RFC6979 3.2\n    // Creates RFC6979 seed; converts msg/privKey to numbers.\n    // Used only in sign, not in verify.\n    // NOTE: we cannot assume here that msgHash has same amount of bytes as curve order,\n    // this will be invalid at least for P521. Also it can be bigger for P224 + SHA256\n    function prepSig(msgHash, privateKey, opts = defaultSigOpts) {\n        if (['recovered', 'canonical'].some((k) => k in opts))\n            throw new Error('sign() legacy options not supported');\n        const { hash, randomBytes } = CURVE;\n        let { lowS, prehash, extraEntropy: ent } = opts; // generates low-s sigs by default\n        if (lowS == null)\n            lowS = true; // RFC6979 3.2: we skip step A, because we already provide hash\n        msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash);\n        validateSigVerOpts(opts);\n        if (prehash)\n            msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('prehashed msgHash', hash(msgHash));\n        // We can't later call bits2octets, since nested bits2int is broken for curves\n        // with nBitLength % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n        // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n        const h1int = bits2int_modN(msgHash);\n        const d = normPrivateKeyToScalar(privateKey); // validate private key, convert to bigint\n        const seedArgs = [int2octets(d), int2octets(h1int)];\n        // extraEntropy. RFC6979 3.6: additional k' (optional).\n        if (ent != null && ent !== false) {\n            // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n            const e = ent === true ? randomBytes(Fp.BYTES) : ent; // generate random bytes OR pass as-is\n            seedArgs.push((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('extraEntropy', e)); // check for being bytes\n        }\n        const seed = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...seedArgs); // Step D of RFC6979 3.2\n        const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n        // Converts signature params into point w r/s, checks result for validity.\n        function k2sig(kBytes) {\n            // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n            const k = bits2int(kBytes); // Cannot use fields methods, since it is group element\n            if (!isWithinCurveOrder(k))\n                return; // Important: all mod() calls here must be done over N\n            const ik = invN(k); // k^-1 mod n\n            const q = Point.BASE.multiply(k).toAffine(); // q = Gk\n            const r = modN(q.x); // r = q.x mod n\n            if (r === _0n)\n                return;\n            // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n            // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n            // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n            const s = modN(ik * modN(m + r * d)); // Not using blinding here\n            if (s === _0n)\n                return;\n            let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n            let normS = s;\n            if (lowS && isBiggerThanHalfOrder(s)) {\n                normS = normalizeS(s); // if lowS was passed, ensure s is always\n                recovery ^= 1; // // in the bottom half of N\n            }\n            return new Signature(r, normS, recovery); // use normS, not s\n        }\n        return { seed, k2sig };\n    }\n    const defaultSigOpts = { lowS: CURVE.lowS, prehash: false };\n    const defaultVerOpts = { lowS: CURVE.lowS, prehash: false };\n    /**\n     * Signs message hash with a private key.\n     * ```\n     * sign(m, d, k) where\n     *   (x, y) = G × k\n     *   r = x mod n\n     *   s = (m + dr)/k mod n\n     * ```\n     * @param msgHash NOT message. msg needs to be hashed to `msgHash`, or use `prehash`.\n     * @param privKey private key\n     * @param opts lowS for non-malleable sigs. extraEntropy for mixing randomness into k. prehash will hash first arg.\n     * @returns signature with recovery param\n     */\n    function sign(msgHash, privKey, opts = defaultSigOpts) {\n        const { seed, k2sig } = prepSig(msgHash, privKey, opts); // Steps A, D of RFC6979 3.2.\n        const C = CURVE;\n        const drbg = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createHmacDrbg)(C.hash.outputLen, C.nByteLength, C.hmac);\n        return drbg(seed, k2sig); // Steps B, C, D, E, F, G\n    }\n    // Enable precomputes. Slows down first publicKey computation by 20ms.\n    Point.BASE._setWindowSize(8);\n    // utils.precompute(8, ProjectivePoint.BASE)\n    /**\n     * Verifies a signature against message hash and public key.\n     * Rejects lowS signatures by default: to override,\n     * specify option `{lowS: false}`. Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n     *\n     * ```\n     * verify(r, s, h, P) where\n     *   U1 = hs^-1 mod n\n     *   U2 = rs^-1 mod n\n     *   R = U1⋅G - U2⋅P\n     *   mod(R.x, n) == r\n     * ```\n     */\n    function verify(signature, msgHash, publicKey, opts = defaultVerOpts) {\n        const sg = signature;\n        msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash);\n        publicKey = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('publicKey', publicKey);\n        const { lowS, prehash, format } = opts;\n        // Verify opts, deduce signature format\n        validateSigVerOpts(opts);\n        if ('strict' in opts)\n            throw new Error('options.strict was renamed to lowS');\n        if (format !== undefined && format !== 'compact' && format !== 'der')\n            throw new Error('format must be compact or der');\n        const isHex = typeof sg === 'string' || (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes)(sg);\n        const isObj = !isHex &&\n            !format &&\n            typeof sg === 'object' &&\n            sg !== null &&\n            typeof sg.r === 'bigint' &&\n            typeof sg.s === 'bigint';\n        if (!isHex && !isObj)\n            throw new Error('invalid signature, expected Uint8Array, hex string or Signature instance');\n        let _sig = undefined;\n        let P;\n        try {\n            if (isObj)\n                _sig = new Signature(sg.r, sg.s);\n            if (isHex) {\n                // Signature can be represented in 2 ways: compact (2*nByteLength) & DER (variable-length).\n                // Since DER can also be 2*nByteLength bytes, we check for it first.\n                try {\n                    if (format !== 'compact')\n                        _sig = Signature.fromDER(sg);\n                }\n                catch (derError) {\n                    if (!(derError instanceof DER.Err))\n                        throw derError;\n                }\n                if (!_sig && format !== 'der')\n                    _sig = Signature.fromCompact(sg);\n            }\n            P = Point.fromHex(publicKey);\n        }\n        catch (error) {\n            return false;\n        }\n        if (!_sig)\n            return false;\n        if (lowS && _sig.hasHighS())\n            return false;\n        if (prehash)\n            msgHash = CURVE.hash(msgHash);\n        const { r, s } = _sig;\n        const h = bits2int_modN(msgHash); // Cannot use fields methods, since it is group element\n        const is = invN(s); // s^-1\n        const u1 = modN(h * is); // u1 = hs^-1 mod n\n        const u2 = modN(r * is); // u2 = rs^-1 mod n\n        const R = Point.BASE.multiplyAndAddUnsafe(P, u1, u2)?.toAffine(); // R = u1⋅G + u2⋅P\n        if (!R)\n            return false;\n        const v = modN(R.x);\n        return v === r;\n    }\n    return {\n        CURVE,\n        getPublicKey,\n        getSharedSecret,\n        sign,\n        verify,\n        ProjectivePoint: Point,\n        Signature,\n        utils,\n    };\n}\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nfunction SWUFpSqrtRatio(Fp, Z) {\n    // Generic implementation\n    const q = Fp.ORDER;\n    let l = _0n;\n    for (let o = q - _1n; o % _2n === _0n; o /= _2n)\n        l += _1n;\n    const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n    // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n    // 2n ** c1 == 2n << (c1-1)\n    const _2n_pow_c1_1 = _2n << (c1 - _1n - _1n);\n    const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n    const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n    const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n    const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n    const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n    const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n    const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n    let sqrtRatio = (u, v) => {\n        let tv1 = c6; // 1. tv1 = c6\n        let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n        let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n        tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n        let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n        tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n        tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n        tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n        tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n        let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n        tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n        let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n        tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n        tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n        tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n        tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n        // 17. for i in (c1, c1 - 1, ..., 2):\n        for (let i = c1; i > _1n; i--) {\n            let tv5 = i - _2n; // 18.    tv5 = i - 2\n            tv5 = _2n << (tv5 - _1n); // 19.    tv5 = 2^tv5\n            let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n            const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n            tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n            tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n            tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n            tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n            tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n        }\n        return { isValid: isQR, value: tv3 };\n    };\n    if (Fp.ORDER % _4n === _3n) {\n        // sqrt_ratio_3mod4(u, v)\n        const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n        const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n        sqrtRatio = (u, v) => {\n            let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n            const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n            tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n            let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n            y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n            const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n            const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n            const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n            let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n            return { isValid: isQR, value: y }; // 11. return (isQR, y) isQR ? y : y*c2\n        };\n    }\n    // No curves uses that\n    // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n    return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nfunction mapToCurveSimpleSWU(Fp, opts) {\n    (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.validateField)(Fp);\n    if (!Fp.isValid(opts.A) || !Fp.isValid(opts.B) || !Fp.isValid(opts.Z))\n        throw new Error('mapToCurveSimpleSWU: invalid opts');\n    const sqrtRatio = SWUFpSqrtRatio(Fp, opts.Z);\n    if (!Fp.isOdd)\n        throw new Error('Fp.isOdd is not implemented!');\n    // Input: u, an element of F.\n    // Output: (x, y), a point on E.\n    return (u) => {\n        // prettier-ignore\n        let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n        tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n        tv1 = Fp.mul(tv1, opts.Z); // 2.  tv1 = Z * tv1\n        tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n        tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n        tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n        tv3 = Fp.mul(tv3, opts.B); // 6.  tv3 = B * tv3\n        tv4 = Fp.cmov(opts.Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n        tv4 = Fp.mul(tv4, opts.A); // 8.  tv4 = A * tv4\n        tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n        tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n        tv5 = Fp.mul(tv6, opts.A); // 11. tv5 = A * tv6\n        tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n        tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n        tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n        tv5 = Fp.mul(tv6, opts.B); // 15. tv5 = B * tv6\n        tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n        x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n        const { isValid, value } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n        y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n        y = Fp.mul(y, value); // 20.   y = y * y1\n        x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n        y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n        const e1 = Fp.isOdd(u) === Fp.isOdd(y); // 23.  e1 = sgn0(u) == sgn0(y)\n        y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n        const tv4_inv = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.FpInvertBatch)(Fp, [tv4], true)[0];\n        x = Fp.mul(x, tv4_inv); // 25.   x = x / tv4\n        return { x, y };\n    };\n}\n//# sourceMappingURL=weierstrass.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/weierstrass.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/secp256k1.js":
/*!***********************************************************************!*\
  !*** ./node_modules/viem/node_modules/@noble/curves/esm/secp256k1.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeToCurve: () => (/* binding */ encodeToCurve),\n/* harmony export */   hashToCurve: () => (/* binding */ hashToCurve),\n/* harmony export */   schnorr: () => (/* binding */ schnorr),\n/* harmony export */   secp256k1: () => (/* binding */ secp256k1),\n/* harmony export */   secp256k1_hasher: () => (/* binding */ secp256k1_hasher)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_sha2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @noble/hashes/sha2 */ \"(app-pages-browser)/./node_modules/@noble/hashes/esm/sha2.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @noble/hashes/utils */ \"(app-pages-browser)/./node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _shortw_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_shortw_utils.js */ \"(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/_shortw_utils.js\");\n/* harmony import */ var _abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./abstract/hash-to-curve.js */ \"(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/hash-to-curve.js\");\n/* harmony import */ var _abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abstract/modular.js */ \"(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./abstract/utils.js */ \"(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/utils.js\");\n/* harmony import */ var _abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./abstract/weierstrass.js */ \"(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/abstract/weierstrass.js\");\n/**\n * NIST secp256k1. See [pdf](https://www.secg.org/sec2-v2.pdf).\n *\n * Seems to be rigid (not backdoored)\n * [as per discussion](https://bitcointalk.org/index.php?topic=289795.msg3183975#msg3183975).\n *\n * secp256k1 belongs to Koblitz curves: it has efficiently computable endomorphism.\n * Endomorphism uses 2x less RAM, speeds up precomputation by 2x and ECDH / key recovery by 20%.\n * For precomputed wNAF it trades off 1/2 init time & 1/3 ram for 20% perf hit.\n * [See explanation](https://gist.github.com/paulmillr/eb670806793e84df628a7c434a873066).\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n\n\n\n\nconst secp256k1P = BigInt('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f');\nconst secp256k1N = BigInt('0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141');\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst divNearest = (a, b) => (a + b / _2n) / b;\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */\nfunction sqrtMod(y) {\n    const P = secp256k1P;\n    // prettier-ignore\n    const _3n = BigInt(3), _6n = BigInt(6), _11n = BigInt(11), _22n = BigInt(22);\n    // prettier-ignore\n    const _23n = BigInt(23), _44n = BigInt(44), _88n = BigInt(88);\n    const b2 = (y * y * y) % P; // x^3, 11\n    const b3 = (b2 * b2 * y) % P; // x^7\n    const b6 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b3, _3n, P) * b3) % P;\n    const b9 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b6, _3n, P) * b3) % P;\n    const b11 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b9, _2n, P) * b2) % P;\n    const b22 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b11, _11n, P) * b11) % P;\n    const b44 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b22, _22n, P) * b22) % P;\n    const b88 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b44, _44n, P) * b44) % P;\n    const b176 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b88, _88n, P) * b88) % P;\n    const b220 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b176, _44n, P) * b44) % P;\n    const b223 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b220, _3n, P) * b3) % P;\n    const t1 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b223, _23n, P) * b22) % P;\n    const t2 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(t1, _6n, P) * b2) % P;\n    const root = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(t2, _2n, P);\n    if (!Fpk1.eql(Fpk1.sqr(root), y))\n        throw new Error('Cannot find square root');\n    return root;\n}\nconst Fpk1 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.Field)(secp256k1P, undefined, undefined, { sqrt: sqrtMod });\n/**\n * secp256k1 curve, ECDSA and ECDH methods.\n *\n * Field: `2n**256n - 2n**32n - 2n**9n - 2n**8n - 2n**7n - 2n**6n - 2n**4n - 1n`\n *\n * @example\n * ```js\n * import { secp256k1 } from '@noble/curves/secp256k1';\n * const priv = secp256k1.utils.randomPrivateKey();\n * const pub = secp256k1.getPublicKey(priv);\n * const msg = new Uint8Array(32).fill(1); // message hash (not message) in ecdsa\n * const sig = secp256k1.sign(msg, priv); // `{prehash: true}` option is available\n * const isValid = secp256k1.verify(sig, msg, pub) === true;\n * ```\n */\nconst secp256k1 = (0,_shortw_utils_js__WEBPACK_IMPORTED_MODULE_1__.createCurve)({\n    a: _0n,\n    b: BigInt(7),\n    Fp: Fpk1,\n    n: secp256k1N,\n    Gx: BigInt('55066263022277343669578718895168534326250603453777594175500187360389116729240'),\n    Gy: BigInt('32670510020758816978083085130507043184471273380659243275938904335757337482424'),\n    h: BigInt(1),\n    lowS: true, // Allow only low-S signatures by default in sign() and verify()\n    endo: {\n        // Endomorphism, see above\n        beta: BigInt('0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee'),\n        splitScalar: (k) => {\n            const n = secp256k1N;\n            const a1 = BigInt('0x3086d221a7d46bcde86c90e49284eb15');\n            const b1 = -_1n * BigInt('0xe4437ed6010e88286f547fa90abfe4c3');\n            const a2 = BigInt('0x114ca50f7a8e2f3f657c1108d9d44cfd8');\n            const b2 = a1;\n            const POW_2_128 = BigInt('0x100000000000000000000000000000000'); // (2n**128n).toString(16)\n            const c1 = divNearest(b2 * k, n);\n            const c2 = divNearest(-b1 * k, n);\n            let k1 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(k - c1 * a1 - c2 * a2, n);\n            let k2 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(-c1 * b1 - c2 * b2, n);\n            const k1neg = k1 > POW_2_128;\n            const k2neg = k2 > POW_2_128;\n            if (k1neg)\n                k1 = n - k1;\n            if (k2neg)\n                k2 = n - k2;\n            if (k1 > POW_2_128 || k2 > POW_2_128) {\n                throw new Error('splitScalar: Endomorphism failed, k=' + k);\n            }\n            return { k1neg, k1, k2neg, k2 };\n        },\n    },\n}, _noble_hashes_sha2__WEBPACK_IMPORTED_MODULE_2__.sha256);\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */\nconst TAGGED_HASH_PREFIXES = {};\nfunction taggedHash(tag, ...messages) {\n    let tagP = TAGGED_HASH_PREFIXES[tag];\n    if (tagP === undefined) {\n        const tagH = (0,_noble_hashes_sha2__WEBPACK_IMPORTED_MODULE_2__.sha256)(Uint8Array.from(tag, (c) => c.charCodeAt(0)));\n        tagP = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.concatBytes)(tagH, tagH);\n        TAGGED_HASH_PREFIXES[tag] = tagP;\n    }\n    return (0,_noble_hashes_sha2__WEBPACK_IMPORTED_MODULE_2__.sha256)((0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.concatBytes)(tagP, ...messages));\n}\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = (point) => point.toRawBytes(true).slice(1);\nconst numTo32b = (n) => (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.numberToBytesBE)(n, 32);\nconst modP = (x) => (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(x, secp256k1P);\nconst modN = (x) => (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(x, secp256k1N);\nconst Point = /* @__PURE__ */ (() => secp256k1.ProjectivePoint)();\nconst GmulAdd = (Q, a, b) => Point.BASE.multiplyAndAddUnsafe(Q, a, b);\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv) {\n    let d_ = secp256k1.utils.normPrivateKeyToScalar(priv); // same method executed in fromPrivateKey\n    let p = Point.fromPrivateKey(d_); // P = d'⋅G; 0 < d' < n check is done inside\n    const scalar = p.hasEvenY() ? d_ : modN(-d_);\n    return { scalar: scalar, bytes: pointToBytes(p) };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */\nfunction lift_x(x) {\n    (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.aInRange)('x', x, _1n, secp256k1P); // Fail if x ≥ p.\n    const xx = modP(x * x);\n    const c = modP(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n    let y = sqrtMod(c); // Let y = c^(p+1)/4 mod p.\n    if (y % _2n !== _0n)\n        y = modP(-y); // Return the unique point P such that x(P) = x and\n    const p = new Point(x, y, _1n); // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n    p.assertValidity();\n    return p;\n}\nconst num = _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE;\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */\nfunction challenge(...args) {\n    return modN(num(taggedHash('BIP0340/challenge', ...args)));\n}\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */\nfunction schnorrGetPublicKey(privateKey) {\n    return schnorrGetExtPubKey(privateKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */\nfunction schnorrSign(message, privateKey, auxRand = (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_4__.randomBytes)(32)) {\n    const m = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('message', message);\n    const { bytes: px, scalar: d } = schnorrGetExtPubKey(privateKey); // checks for isWithinCurveOrder\n    const a = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('auxRand', auxRand, 32); // Auxiliary random data a: a 32-byte array\n    const t = numTo32b(d ^ num(taggedHash('BIP0340/aux', a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n    const rand = taggedHash('BIP0340/nonce', t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n    const k_ = modN(num(rand)); // Let k' = int(rand) mod n\n    if (k_ === _0n)\n        throw new Error('sign failed: k is zero'); // Fail if k' = 0.\n    const { bytes: rx, scalar: k } = schnorrGetExtPubKey(k_); // Let R = k'⋅G.\n    const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n    const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n    sig.set(rx, 0);\n    sig.set(numTo32b(modN(k + e * d)), 32);\n    // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n    if (!schnorrVerify(sig, m, px))\n        throw new Error('sign: Invalid signature produced');\n    return sig;\n}\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */\nfunction schnorrVerify(signature, message, publicKey) {\n    const sig = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('signature', signature, 64);\n    const m = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('message', message);\n    const pub = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('publicKey', publicKey, 32);\n    try {\n        const P = lift_x(num(pub)); // P = lift_x(int(pk)); fail if that fails\n        const r = num(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n        if (!(0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.inRange)(r, _1n, secp256k1P))\n            return false;\n        const s = num(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n        if (!(0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.inRange)(s, _1n, secp256k1N))\n            return false;\n        const e = challenge(numTo32b(r), pointToBytes(P), m); // int(challenge(bytes(r)||bytes(P)||m))%n\n        const R = GmulAdd(P, s, modN(-e)); // R = s⋅G - e⋅P\n        if (!R || !R.hasEvenY() || R.toAffine().x !== r)\n            return false; // -eP == (n-e)P\n        return true; // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n    }\n    catch (error) {\n        return false;\n    }\n}\n/**\n * Schnorr signatures over secp256k1.\n * https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n * @example\n * ```js\n * import { schnorr } from '@noble/curves/secp256k1';\n * const priv = schnorr.utils.randomPrivateKey();\n * const pub = schnorr.getPublicKey(priv);\n * const msg = new TextEncoder().encode('hello');\n * const sig = schnorr.sign(msg, priv);\n * const isValid = schnorr.verify(sig, msg, pub);\n * ```\n */\nconst schnorr = /* @__PURE__ */ (() => ({\n    getPublicKey: schnorrGetPublicKey,\n    sign: schnorrSign,\n    verify: schnorrVerify,\n    utils: {\n        randomPrivateKey: secp256k1.utils.randomPrivateKey,\n        lift_x,\n        pointToBytes,\n        numberToBytesBE: _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.numberToBytesBE,\n        bytesToNumberBE: _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE,\n        taggedHash,\n        mod: _abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod,\n    },\n}))();\nconst isoMap = /* @__PURE__ */ (() => (0,_abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__.isogenyMap)(Fpk1, [\n    // xNum\n    [\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7',\n        '0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581',\n        '0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262',\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c',\n    ],\n    // xDen\n    [\n        '0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b',\n        '0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n    // yNum\n    [\n        '0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c',\n        '0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3',\n        '0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931',\n        '0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84',\n    ],\n    // yDen\n    [\n        '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b',\n        '0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573',\n        '0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n].map((i) => i.map((j) => BigInt(j)))))();\nconst mapSWU = /* @__PURE__ */ (() => (0,_abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_6__.mapToCurveSimpleSWU)(Fpk1, {\n    A: BigInt('0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533'),\n    B: BigInt('1771'),\n    Z: Fpk1.create(BigInt('-11')),\n}))();\n/** Hashing / encoding to secp256k1 points / field. RFC 9380 methods. */\nconst secp256k1_hasher = /* @__PURE__ */ (() => (0,_abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__.createHasher)(secp256k1.ProjectivePoint, (scalars) => {\n    const { x, y } = mapSWU(Fpk1.create(scalars[0]));\n    return isoMap(x, y);\n}, {\n    DST: 'secp256k1_XMD:SHA-256_SSWU_RO_',\n    encodeDST: 'secp256k1_XMD:SHA-256_SSWU_NU_',\n    p: Fpk1.ORDER,\n    m: 1,\n    k: 128,\n    expand: 'xmd',\n    hash: _noble_hashes_sha2__WEBPACK_IMPORTED_MODULE_2__.sha256,\n}))();\nconst hashToCurve = /* @__PURE__ */ (() => secp256k1_hasher.hashToCurve)();\nconst encodeToCurve = /* @__PURE__ */ (() => secp256k1_hasher.encodeToCurve)();\n//# sourceMappingURL=secp256k1.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy92aWVtL25vZGVfbW9kdWxlcy9Abm9ibGUvY3VydmVzL2VzbS9zZWNwMjU2azEuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDNEM7QUFDTTtBQUNEO0FBQ3NCO0FBQ2Q7QUFDNEQ7QUFDckQ7QUFDaEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0M7QUFDaEMsa0NBQWtDO0FBQ2xDLGdCQUFnQiwwREFBSTtBQUNwQixnQkFBZ0IsMERBQUk7QUFDcEIsaUJBQWlCLDBEQUFJO0FBQ3JCLGlCQUFpQiwwREFBSTtBQUNyQixpQkFBaUIsMERBQUk7QUFDckIsaUJBQWlCLDBEQUFJO0FBQ3JCLGtCQUFrQiwwREFBSTtBQUN0QixrQkFBa0IsMERBQUk7QUFDdEIsa0JBQWtCLDBEQUFJO0FBQ3RCLGdCQUFnQiwwREFBSTtBQUNwQixnQkFBZ0IsMERBQUk7QUFDcEIsaUJBQWlCLDBEQUFJO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSwyREFBSyxxQ0FBcUMsZUFBZTtBQUN0RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksWUFBWTtBQUN4QjtBQUNBO0FBQ0EsMkNBQTJDO0FBQzNDLDBDQUEwQyxLQUFLLGNBQWM7QUFDN0Q7QUFDQTtBQUNBO0FBQ08sa0JBQWtCLDZEQUFXO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2RUFBNkU7QUFDN0U7QUFDQTtBQUNBLHFCQUFxQix5REFBRztBQUN4QixxQkFBcUIseURBQUc7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLFNBQVM7QUFDVCxLQUFLO0FBQ0wsQ0FBQyxFQUFFLHNEQUFNO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsMERBQU07QUFDM0IsZUFBZSwrREFBVztBQUMxQjtBQUNBO0FBQ0EsV0FBVywwREFBTSxDQUFDLCtEQUFXO0FBQzdCO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixtRUFBZTtBQUN2QyxvQkFBb0IseURBQUc7QUFDdkIsb0JBQW9CLHlEQUFHO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkRBQTJEO0FBQzNELHNDQUFzQyxhQUFhO0FBQ25EO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksNERBQVEsMkJBQTJCO0FBQ3ZDO0FBQ0Esd0NBQXdDO0FBQ3hDLHdCQUF3QjtBQUN4QjtBQUNBLHNCQUFzQjtBQUN0QixvQ0FBb0M7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsWUFBWSwrREFBZTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRDtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0RBQW9ELGdFQUFXO0FBQy9ELGNBQWMsK0RBQVc7QUFDekIsWUFBWSx1QkFBdUIsbUNBQW1DO0FBQ3RFLGNBQWMsK0RBQVcsMEJBQTBCO0FBQ25ELCtEQUErRDtBQUMvRCx3REFBd0Q7QUFDeEQsZ0NBQWdDO0FBQ2hDO0FBQ0EsbURBQW1EO0FBQ25ELFlBQVksdUJBQXVCLDJCQUEyQjtBQUM5RCxvQ0FBb0M7QUFDcEMsb0NBQW9DO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiwrREFBVztBQUMzQixjQUFjLCtEQUFXO0FBQ3pCLGdCQUFnQiwrREFBVztBQUMzQjtBQUNBLG9DQUFvQyx3QkFBd0I7QUFDNUQsNENBQTRDLDJCQUEyQjtBQUN2RSxhQUFhLDJEQUFPO0FBQ3BCO0FBQ0EsNkNBQTZDLDRCQUE0QjtBQUN6RSxhQUFhLDJEQUFPO0FBQ3BCO0FBQ0EsOERBQThEO0FBQzlELDJDQUEyQztBQUMzQztBQUNBLDBCQUEwQjtBQUMxQixxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLFVBQVU7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCO0FBQ3ZCLHVCQUF1QjtBQUN2QjtBQUNBLFdBQVc7QUFDWCxLQUFLO0FBQ0wsQ0FBQztBQUNELHNDQUFzQyxzRUFBVTtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyw2RUFBbUI7QUFDekQ7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ08sZ0RBQWdELHdFQUFZO0FBQ25FLFlBQVksT0FBTztBQUNuQjtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLHNEQUFNO0FBQ2hCLENBQUM7QUFDTTtBQUNBO0FBQ1AiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvdmllbS9ub2RlX21vZHVsZXMvQG5vYmxlL2N1cnZlcy9lc20vc2VjcDI1NmsxLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTklTVCBzZWNwMjU2azEuIFNlZSBbcGRmXShodHRwczovL3d3dy5zZWNnLm9yZy9zZWMyLXYyLnBkZikuXG4gKlxuICogU2VlbXMgdG8gYmUgcmlnaWQgKG5vdCBiYWNrZG9vcmVkKVxuICogW2FzIHBlciBkaXNjdXNzaW9uXShodHRwczovL2JpdGNvaW50YWxrLm9yZy9pbmRleC5waHA/dG9waWM9Mjg5Nzk1Lm1zZzMxODM5NzUjbXNnMzE4Mzk3NSkuXG4gKlxuICogc2VjcDI1NmsxIGJlbG9uZ3MgdG8gS29ibGl0eiBjdXJ2ZXM6IGl0IGhhcyBlZmZpY2llbnRseSBjb21wdXRhYmxlIGVuZG9tb3JwaGlzbS5cbiAqIEVuZG9tb3JwaGlzbSB1c2VzIDJ4IGxlc3MgUkFNLCBzcGVlZHMgdXAgcHJlY29tcHV0YXRpb24gYnkgMnggYW5kIEVDREggLyBrZXkgcmVjb3ZlcnkgYnkgMjAlLlxuICogRm9yIHByZWNvbXB1dGVkIHdOQUYgaXQgdHJhZGVzIG9mZiAxLzIgaW5pdCB0aW1lICYgMS8zIHJhbSBmb3IgMjAlIHBlcmYgaGl0LlxuICogW1NlZSBleHBsYW5hdGlvbl0oaHR0cHM6Ly9naXN0LmdpdGh1Yi5jb20vcGF1bG1pbGxyL2ViNjcwODA2NzkzZTg0ZGY2MjhhN2M0MzRhODczMDY2KS5cbiAqIEBtb2R1bGVcbiAqL1xuLyohIG5vYmxlLWN1cnZlcyAtIE1JVCBMaWNlbnNlIChjKSAyMDIyIFBhdWwgTWlsbGVyIChwYXVsbWlsbHIuY29tKSAqL1xuaW1wb3J0IHsgc2hhMjU2IH0gZnJvbSAnQG5vYmxlL2hhc2hlcy9zaGEyJztcbmltcG9ydCB7IHJhbmRvbUJ5dGVzIH0gZnJvbSAnQG5vYmxlL2hhc2hlcy91dGlscyc7XG5pbXBvcnQgeyBjcmVhdGVDdXJ2ZSB9IGZyb20gXCIuL19zaG9ydHdfdXRpbHMuanNcIjtcbmltcG9ydCB7IGNyZWF0ZUhhc2hlciwgaXNvZ2VueU1hcCB9IGZyb20gXCIuL2Fic3RyYWN0L2hhc2gtdG8tY3VydmUuanNcIjtcbmltcG9ydCB7IEZpZWxkLCBtb2QsIHBvdzIgfSBmcm9tIFwiLi9hYnN0cmFjdC9tb2R1bGFyLmpzXCI7XG5pbXBvcnQgeyBhSW5SYW5nZSwgYnl0ZXNUb051bWJlckJFLCBjb25jYXRCeXRlcywgZW5zdXJlQnl0ZXMsIGluUmFuZ2UsIG51bWJlclRvQnl0ZXNCRSwgfSBmcm9tIFwiLi9hYnN0cmFjdC91dGlscy5qc1wiO1xuaW1wb3J0IHsgbWFwVG9DdXJ2ZVNpbXBsZVNXVSB9IGZyb20gXCIuL2Fic3RyYWN0L3dlaWVyc3RyYXNzLmpzXCI7XG5jb25zdCBzZWNwMjU2azFQID0gQmlnSW50KCcweGZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZlZmZmZmZjMmYnKTtcbmNvbnN0IHNlY3AyNTZrMU4gPSBCaWdJbnQoJzB4ZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmViYWFlZGNlNmFmNDhhMDNiYmZkMjVlOGNkMDM2NDE0MScpO1xuY29uc3QgXzBuID0gQmlnSW50KDApO1xuY29uc3QgXzFuID0gQmlnSW50KDEpO1xuY29uc3QgXzJuID0gQmlnSW50KDIpO1xuY29uc3QgZGl2TmVhcmVzdCA9IChhLCBiKSA9PiAoYSArIGIgLyBfMm4pIC8gYjtcbi8qKlxuICog4oiabiA9IG5eKChwKzEpLzQpIGZvciBmaWVsZHMgcCA9IDMgbW9kIDQuIFdlIHVud3JhcCB0aGUgbG9vcCBhbmQgbXVsdGlwbHkgYml0LWJ5LWJpdC5cbiAqIChQKzFuLzRuKS50b1N0cmluZygyKSB3b3VsZCBwcm9kdWNlIGJpdHMgWzIyM3ggMSwgMCwgMjJ4IDEsIDR4IDAsIDExLCAwMF1cbiAqL1xuZnVuY3Rpb24gc3FydE1vZCh5KSB7XG4gICAgY29uc3QgUCA9IHNlY3AyNTZrMVA7XG4gICAgLy8gcHJldHRpZXItaWdub3JlXG4gICAgY29uc3QgXzNuID0gQmlnSW50KDMpLCBfNm4gPSBCaWdJbnQoNiksIF8xMW4gPSBCaWdJbnQoMTEpLCBfMjJuID0gQmlnSW50KDIyKTtcbiAgICAvLyBwcmV0dGllci1pZ25vcmVcbiAgICBjb25zdCBfMjNuID0gQmlnSW50KDIzKSwgXzQ0biA9IEJpZ0ludCg0NCksIF84OG4gPSBCaWdJbnQoODgpO1xuICAgIGNvbnN0IGIyID0gKHkgKiB5ICogeSkgJSBQOyAvLyB4XjMsIDExXG4gICAgY29uc3QgYjMgPSAoYjIgKiBiMiAqIHkpICUgUDsgLy8geF43XG4gICAgY29uc3QgYjYgPSAocG93MihiMywgXzNuLCBQKSAqIGIzKSAlIFA7XG4gICAgY29uc3QgYjkgPSAocG93MihiNiwgXzNuLCBQKSAqIGIzKSAlIFA7XG4gICAgY29uc3QgYjExID0gKHBvdzIoYjksIF8ybiwgUCkgKiBiMikgJSBQO1xuICAgIGNvbnN0IGIyMiA9IChwb3cyKGIxMSwgXzExbiwgUCkgKiBiMTEpICUgUDtcbiAgICBjb25zdCBiNDQgPSAocG93MihiMjIsIF8yMm4sIFApICogYjIyKSAlIFA7XG4gICAgY29uc3QgYjg4ID0gKHBvdzIoYjQ0LCBfNDRuLCBQKSAqIGI0NCkgJSBQO1xuICAgIGNvbnN0IGIxNzYgPSAocG93MihiODgsIF84OG4sIFApICogYjg4KSAlIFA7XG4gICAgY29uc3QgYjIyMCA9IChwb3cyKGIxNzYsIF80NG4sIFApICogYjQ0KSAlIFA7XG4gICAgY29uc3QgYjIyMyA9IChwb3cyKGIyMjAsIF8zbiwgUCkgKiBiMykgJSBQO1xuICAgIGNvbnN0IHQxID0gKHBvdzIoYjIyMywgXzIzbiwgUCkgKiBiMjIpICUgUDtcbiAgICBjb25zdCB0MiA9IChwb3cyKHQxLCBfNm4sIFApICogYjIpICUgUDtcbiAgICBjb25zdCByb290ID0gcG93Mih0MiwgXzJuLCBQKTtcbiAgICBpZiAoIUZwazEuZXFsKEZwazEuc3FyKHJvb3QpLCB5KSlcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdDYW5ub3QgZmluZCBzcXVhcmUgcm9vdCcpO1xuICAgIHJldHVybiByb290O1xufVxuY29uc3QgRnBrMSA9IEZpZWxkKHNlY3AyNTZrMVAsIHVuZGVmaW5lZCwgdW5kZWZpbmVkLCB7IHNxcnQ6IHNxcnRNb2QgfSk7XG4vKipcbiAqIHNlY3AyNTZrMSBjdXJ2ZSwgRUNEU0EgYW5kIEVDREggbWV0aG9kcy5cbiAqXG4gKiBGaWVsZDogYDJuKioyNTZuIC0gMm4qKjMybiAtIDJuKio5biAtIDJuKio4biAtIDJuKio3biAtIDJuKio2biAtIDJuKio0biAtIDFuYFxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGBqc1xuICogaW1wb3J0IHsgc2VjcDI1NmsxIH0gZnJvbSAnQG5vYmxlL2N1cnZlcy9zZWNwMjU2azEnO1xuICogY29uc3QgcHJpdiA9IHNlY3AyNTZrMS51dGlscy5yYW5kb21Qcml2YXRlS2V5KCk7XG4gKiBjb25zdCBwdWIgPSBzZWNwMjU2azEuZ2V0UHVibGljS2V5KHByaXYpO1xuICogY29uc3QgbXNnID0gbmV3IFVpbnQ4QXJyYXkoMzIpLmZpbGwoMSk7IC8vIG1lc3NhZ2UgaGFzaCAobm90IG1lc3NhZ2UpIGluIGVjZHNhXG4gKiBjb25zdCBzaWcgPSBzZWNwMjU2azEuc2lnbihtc2csIHByaXYpOyAvLyBge3ByZWhhc2g6IHRydWV9YCBvcHRpb24gaXMgYXZhaWxhYmxlXG4gKiBjb25zdCBpc1ZhbGlkID0gc2VjcDI1NmsxLnZlcmlmeShzaWcsIG1zZywgcHViKSA9PT0gdHJ1ZTtcbiAqIGBgYFxuICovXG5leHBvcnQgY29uc3Qgc2VjcDI1NmsxID0gY3JlYXRlQ3VydmUoe1xuICAgIGE6IF8wbixcbiAgICBiOiBCaWdJbnQoNyksXG4gICAgRnA6IEZwazEsXG4gICAgbjogc2VjcDI1NmsxTixcbiAgICBHeDogQmlnSW50KCc1NTA2NjI2MzAyMjI3NzM0MzY2OTU3ODcxODg5NTE2ODUzNDMyNjI1MDYwMzQ1Mzc3NzU5NDE3NTUwMDE4NzM2MDM4OTExNjcyOTI0MCcpLFxuICAgIEd5OiBCaWdJbnQoJzMyNjcwNTEwMDIwNzU4ODE2OTc4MDgzMDg1MTMwNTA3MDQzMTg0NDcxMjczMzgwNjU5MjQzMjc1OTM4OTA0MzM1NzU3MzM3NDgyNDI0JyksXG4gICAgaDogQmlnSW50KDEpLFxuICAgIGxvd1M6IHRydWUsIC8vIEFsbG93IG9ubHkgbG93LVMgc2lnbmF0dXJlcyBieSBkZWZhdWx0IGluIHNpZ24oKSBhbmQgdmVyaWZ5KClcbiAgICBlbmRvOiB7XG4gICAgICAgIC8vIEVuZG9tb3JwaGlzbSwgc2VlIGFib3ZlXG4gICAgICAgIGJldGE6IEJpZ0ludCgnMHg3YWU5NmEyYjY1N2MwNzEwNmU2NDQ3OWVhYzM0MzRlOTljZjA0OTc1MTJmNTg5OTVjMTM5NmMyODcxOTUwMWVlJyksXG4gICAgICAgIHNwbGl0U2NhbGFyOiAoaykgPT4ge1xuICAgICAgICAgICAgY29uc3QgbiA9IHNlY3AyNTZrMU47XG4gICAgICAgICAgICBjb25zdCBhMSA9IEJpZ0ludCgnMHgzMDg2ZDIyMWE3ZDQ2YmNkZTg2YzkwZTQ5Mjg0ZWIxNScpO1xuICAgICAgICAgICAgY29uc3QgYjEgPSAtXzFuICogQmlnSW50KCcweGU0NDM3ZWQ2MDEwZTg4Mjg2ZjU0N2ZhOTBhYmZlNGMzJyk7XG4gICAgICAgICAgICBjb25zdCBhMiA9IEJpZ0ludCgnMHgxMTRjYTUwZjdhOGUyZjNmNjU3YzExMDhkOWQ0NGNmZDgnKTtcbiAgICAgICAgICAgIGNvbnN0IGIyID0gYTE7XG4gICAgICAgICAgICBjb25zdCBQT1dfMl8xMjggPSBCaWdJbnQoJzB4MTAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwJyk7IC8vICgybioqMTI4bikudG9TdHJpbmcoMTYpXG4gICAgICAgICAgICBjb25zdCBjMSA9IGRpdk5lYXJlc3QoYjIgKiBrLCBuKTtcbiAgICAgICAgICAgIGNvbnN0IGMyID0gZGl2TmVhcmVzdCgtYjEgKiBrLCBuKTtcbiAgICAgICAgICAgIGxldCBrMSA9IG1vZChrIC0gYzEgKiBhMSAtIGMyICogYTIsIG4pO1xuICAgICAgICAgICAgbGV0IGsyID0gbW9kKC1jMSAqIGIxIC0gYzIgKiBiMiwgbik7XG4gICAgICAgICAgICBjb25zdCBrMW5lZyA9IGsxID4gUE9XXzJfMTI4O1xuICAgICAgICAgICAgY29uc3QgazJuZWcgPSBrMiA+IFBPV18yXzEyODtcbiAgICAgICAgICAgIGlmIChrMW5lZylcbiAgICAgICAgICAgICAgICBrMSA9IG4gLSBrMTtcbiAgICAgICAgICAgIGlmIChrMm5lZylcbiAgICAgICAgICAgICAgICBrMiA9IG4gLSBrMjtcbiAgICAgICAgICAgIGlmIChrMSA+IFBPV18yXzEyOCB8fCBrMiA+IFBPV18yXzEyOCkge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignc3BsaXRTY2FsYXI6IEVuZG9tb3JwaGlzbSBmYWlsZWQsIGs9JyArIGspO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHsgazFuZWcsIGsxLCBrMm5lZywgazIgfTtcbiAgICAgICAgfSxcbiAgICB9LFxufSwgc2hhMjU2KTtcbi8vIFNjaG5vcnIgc2lnbmF0dXJlcyBhcmUgc3VwZXJpb3IgdG8gRUNEU0EgZnJvbSBhYm92ZS4gQmVsb3cgaXMgU2Nobm9yci1zcGVjaWZpYyBCSVAwMzQwIGNvZGUuXG4vLyBodHRwczovL2dpdGh1Yi5jb20vYml0Y29pbi9iaXBzL2Jsb2IvbWFzdGVyL2JpcC0wMzQwLm1lZGlhd2lraVxuLyoqIEFuIG9iamVjdCBtYXBwaW5nIHRhZ3MgdG8gdGhlaXIgdGFnZ2VkIGhhc2ggcHJlZml4IG9mIFtTSEEyNTYodGFnKSB8IFNIQTI1Nih0YWcpXSAqL1xuY29uc3QgVEFHR0VEX0hBU0hfUFJFRklYRVMgPSB7fTtcbmZ1bmN0aW9uIHRhZ2dlZEhhc2godGFnLCAuLi5tZXNzYWdlcykge1xuICAgIGxldCB0YWdQID0gVEFHR0VEX0hBU0hfUFJFRklYRVNbdGFnXTtcbiAgICBpZiAodGFnUCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIGNvbnN0IHRhZ0ggPSBzaGEyNTYoVWludDhBcnJheS5mcm9tKHRhZywgKGMpID0+IGMuY2hhckNvZGVBdCgwKSkpO1xuICAgICAgICB0YWdQID0gY29uY2F0Qnl0ZXModGFnSCwgdGFnSCk7XG4gICAgICAgIFRBR0dFRF9IQVNIX1BSRUZJWEVTW3RhZ10gPSB0YWdQO1xuICAgIH1cbiAgICByZXR1cm4gc2hhMjU2KGNvbmNhdEJ5dGVzKHRhZ1AsIC4uLm1lc3NhZ2VzKSk7XG59XG4vLyBFQ0RTQSBjb21wYWN0IHBvaW50cyBhcmUgMzMtYnl0ZS4gU2Nobm9yciBpcyAzMjogd2Ugc3RyaXAgZmlyc3QgYnl0ZSAweDAyIG9yIDB4MDNcbmNvbnN0IHBvaW50VG9CeXRlcyA9IChwb2ludCkgPT4gcG9pbnQudG9SYXdCeXRlcyh0cnVlKS5zbGljZSgxKTtcbmNvbnN0IG51bVRvMzJiID0gKG4pID0+IG51bWJlclRvQnl0ZXNCRShuLCAzMik7XG5jb25zdCBtb2RQID0gKHgpID0+IG1vZCh4LCBzZWNwMjU2azFQKTtcbmNvbnN0IG1vZE4gPSAoeCkgPT4gbW9kKHgsIHNlY3AyNTZrMU4pO1xuY29uc3QgUG9pbnQgPSAvKiBAX19QVVJFX18gKi8gKCgpID0+IHNlY3AyNTZrMS5Qcm9qZWN0aXZlUG9pbnQpKCk7XG5jb25zdCBHbXVsQWRkID0gKFEsIGEsIGIpID0+IFBvaW50LkJBU0UubXVsdGlwbHlBbmRBZGRVbnNhZmUoUSwgYSwgYik7XG4vLyBDYWxjdWxhdGUgcG9pbnQsIHNjYWxhciBhbmQgYnl0ZXNcbmZ1bmN0aW9uIHNjaG5vcnJHZXRFeHRQdWJLZXkocHJpdikge1xuICAgIGxldCBkXyA9IHNlY3AyNTZrMS51dGlscy5ub3JtUHJpdmF0ZUtleVRvU2NhbGFyKHByaXYpOyAvLyBzYW1lIG1ldGhvZCBleGVjdXRlZCBpbiBmcm9tUHJpdmF0ZUtleVxuICAgIGxldCBwID0gUG9pbnQuZnJvbVByaXZhdGVLZXkoZF8pOyAvLyBQID0gZCfii4VHOyAwIDwgZCcgPCBuIGNoZWNrIGlzIGRvbmUgaW5zaWRlXG4gICAgY29uc3Qgc2NhbGFyID0gcC5oYXNFdmVuWSgpID8gZF8gOiBtb2ROKC1kXyk7XG4gICAgcmV0dXJuIHsgc2NhbGFyOiBzY2FsYXIsIGJ5dGVzOiBwb2ludFRvQnl0ZXMocCkgfTtcbn1cbi8qKlxuICogbGlmdF94IGZyb20gQklQMzQwLiBDb252ZXJ0IDMyLWJ5dGUgeCBjb29yZGluYXRlIHRvIGVsbGlwdGljIGN1cnZlIHBvaW50LlxuICogQHJldHVybnMgdmFsaWQgcG9pbnQgY2hlY2tlZCBmb3IgYmVpbmcgb24tY3VydmVcbiAqL1xuZnVuY3Rpb24gbGlmdF94KHgpIHtcbiAgICBhSW5SYW5nZSgneCcsIHgsIF8xbiwgc2VjcDI1NmsxUCk7IC8vIEZhaWwgaWYgeCDiiaUgcC5cbiAgICBjb25zdCB4eCA9IG1vZFAoeCAqIHgpO1xuICAgIGNvbnN0IGMgPSBtb2RQKHh4ICogeCArIEJpZ0ludCg3KSk7IC8vIExldCBjID0geMKzICsgNyBtb2QgcC5cbiAgICBsZXQgeSA9IHNxcnRNb2QoYyk7IC8vIExldCB5ID0gY14ocCsxKS80IG1vZCBwLlxuICAgIGlmICh5ICUgXzJuICE9PSBfMG4pXG4gICAgICAgIHkgPSBtb2RQKC15KTsgLy8gUmV0dXJuIHRoZSB1bmlxdWUgcG9pbnQgUCBzdWNoIHRoYXQgeChQKSA9IHggYW5kXG4gICAgY29uc3QgcCA9IG5ldyBQb2ludCh4LCB5LCBfMW4pOyAvLyB5KFApID0geSBpZiB5IG1vZCAyID0gMCBvciB5KFApID0gcC15IG90aGVyd2lzZS5cbiAgICBwLmFzc2VydFZhbGlkaXR5KCk7XG4gICAgcmV0dXJuIHA7XG59XG5jb25zdCBudW0gPSBieXRlc1RvTnVtYmVyQkU7XG4vKipcbiAqIENyZWF0ZSB0YWdnZWQgaGFzaCwgY29udmVydCBpdCB0byBiaWdpbnQsIHJlZHVjZSBtb2R1bG8tbi5cbiAqL1xuZnVuY3Rpb24gY2hhbGxlbmdlKC4uLmFyZ3MpIHtcbiAgICByZXR1cm4gbW9kTihudW0odGFnZ2VkSGFzaCgnQklQMDM0MC9jaGFsbGVuZ2UnLCAuLi5hcmdzKSkpO1xufVxuLyoqXG4gKiBTY2hub3JyIHB1YmxpYyBrZXkgaXMganVzdCBgeGAgY29vcmRpbmF0ZSBvZiBQb2ludCBhcyBwZXIgQklQMzQwLlxuICovXG5mdW5jdGlvbiBzY2hub3JyR2V0UHVibGljS2V5KHByaXZhdGVLZXkpIHtcbiAgICByZXR1cm4gc2Nobm9yckdldEV4dFB1YktleShwcml2YXRlS2V5KS5ieXRlczsgLy8gZCc9aW50KHNrKS4gRmFpbCBpZiBkJz0wIG9yIGQn4omlbi4gUmV0IGJ5dGVzKGQn4ouFRylcbn1cbi8qKlxuICogQ3JlYXRlcyBTY2hub3JyIHNpZ25hdHVyZSBhcyBwZXIgQklQMzQwLiBWZXJpZmllcyBpdHNlbGYgYmVmb3JlIHJldHVybmluZyBhbnl0aGluZy5cbiAqIGF1eFJhbmQgaXMgb3B0aW9uYWwgYW5kIGlzIG5vdCB0aGUgc29sZSBzb3VyY2Ugb2YgayBnZW5lcmF0aW9uOiBiYWQgQ1NQUk5HIHdvbid0IGJlIGRhbmdlcm91cy5cbiAqL1xuZnVuY3Rpb24gc2Nobm9yclNpZ24obWVzc2FnZSwgcHJpdmF0ZUtleSwgYXV4UmFuZCA9IHJhbmRvbUJ5dGVzKDMyKSkge1xuICAgIGNvbnN0IG0gPSBlbnN1cmVCeXRlcygnbWVzc2FnZScsIG1lc3NhZ2UpO1xuICAgIGNvbnN0IHsgYnl0ZXM6IHB4LCBzY2FsYXI6IGQgfSA9IHNjaG5vcnJHZXRFeHRQdWJLZXkocHJpdmF0ZUtleSk7IC8vIGNoZWNrcyBmb3IgaXNXaXRoaW5DdXJ2ZU9yZGVyXG4gICAgY29uc3QgYSA9IGVuc3VyZUJ5dGVzKCdhdXhSYW5kJywgYXV4UmFuZCwgMzIpOyAvLyBBdXhpbGlhcnkgcmFuZG9tIGRhdGEgYTogYSAzMi1ieXRlIGFycmF5XG4gICAgY29uc3QgdCA9IG51bVRvMzJiKGQgXiBudW0odGFnZ2VkSGFzaCgnQklQMDM0MC9hdXgnLCBhKSkpOyAvLyBMZXQgdCBiZSB0aGUgYnl0ZS13aXNlIHhvciBvZiBieXRlcyhkKSBhbmQgaGFzaC9hdXgoYSlcbiAgICBjb25zdCByYW5kID0gdGFnZ2VkSGFzaCgnQklQMDM0MC9ub25jZScsIHQsIHB4LCBtKTsgLy8gTGV0IHJhbmQgPSBoYXNoL25vbmNlKHQgfHwgYnl0ZXMoUCkgfHwgbSlcbiAgICBjb25zdCBrXyA9IG1vZE4obnVtKHJhbmQpKTsgLy8gTGV0IGsnID0gaW50KHJhbmQpIG1vZCBuXG4gICAgaWYgKGtfID09PSBfMG4pXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignc2lnbiBmYWlsZWQ6IGsgaXMgemVybycpOyAvLyBGYWlsIGlmIGsnID0gMC5cbiAgICBjb25zdCB7IGJ5dGVzOiByeCwgc2NhbGFyOiBrIH0gPSBzY2hub3JyR2V0RXh0UHViS2V5KGtfKTsgLy8gTGV0IFIgPSBrJ+KLhUcuXG4gICAgY29uc3QgZSA9IGNoYWxsZW5nZShyeCwgcHgsIG0pOyAvLyBMZXQgZSA9IGludChoYXNoL2NoYWxsZW5nZShieXRlcyhSKSB8fCBieXRlcyhQKSB8fCBtKSkgbW9kIG4uXG4gICAgY29uc3Qgc2lnID0gbmV3IFVpbnQ4QXJyYXkoNjQpOyAvLyBMZXQgc2lnID0gYnl0ZXMoUikgfHwgYnl0ZXMoKGsgKyBlZCkgbW9kIG4pLlxuICAgIHNpZy5zZXQocngsIDApO1xuICAgIHNpZy5zZXQobnVtVG8zMmIobW9kTihrICsgZSAqIGQpKSwgMzIpO1xuICAgIC8vIElmIFZlcmlmeShieXRlcyhQKSwgbSwgc2lnKSAoc2VlIGJlbG93KSByZXR1cm5zIGZhaWx1cmUsIGFib3J0XG4gICAgaWYgKCFzY2hub3JyVmVyaWZ5KHNpZywgbSwgcHgpKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ3NpZ246IEludmFsaWQgc2lnbmF0dXJlIHByb2R1Y2VkJyk7XG4gICAgcmV0dXJuIHNpZztcbn1cbi8qKlxuICogVmVyaWZpZXMgU2Nobm9yciBzaWduYXR1cmUuXG4gKiBXaWxsIHN3YWxsb3cgZXJyb3JzICYgcmV0dXJuIGZhbHNlIGV4Y2VwdCBmb3IgaW5pdGlhbCB0eXBlIHZhbGlkYXRpb24gb2YgYXJndW1lbnRzLlxuICovXG5mdW5jdGlvbiBzY2hub3JyVmVyaWZ5KHNpZ25hdHVyZSwgbWVzc2FnZSwgcHVibGljS2V5KSB7XG4gICAgY29uc3Qgc2lnID0gZW5zdXJlQnl0ZXMoJ3NpZ25hdHVyZScsIHNpZ25hdHVyZSwgNjQpO1xuICAgIGNvbnN0IG0gPSBlbnN1cmVCeXRlcygnbWVzc2FnZScsIG1lc3NhZ2UpO1xuICAgIGNvbnN0IHB1YiA9IGVuc3VyZUJ5dGVzKCdwdWJsaWNLZXknLCBwdWJsaWNLZXksIDMyKTtcbiAgICB0cnkge1xuICAgICAgICBjb25zdCBQID0gbGlmdF94KG51bShwdWIpKTsgLy8gUCA9IGxpZnRfeChpbnQocGspKTsgZmFpbCBpZiB0aGF0IGZhaWxzXG4gICAgICAgIGNvbnN0IHIgPSBudW0oc2lnLnN1YmFycmF5KDAsIDMyKSk7IC8vIExldCByID0gaW50KHNpZ1swOjMyXSk7IGZhaWwgaWYgciDiiaUgcC5cbiAgICAgICAgaWYgKCFpblJhbmdlKHIsIF8xbiwgc2VjcDI1NmsxUCkpXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIGNvbnN0IHMgPSBudW0oc2lnLnN1YmFycmF5KDMyLCA2NCkpOyAvLyBMZXQgcyA9IGludChzaWdbMzI6NjRdKTsgZmFpbCBpZiBzIOKJpSBuLlxuICAgICAgICBpZiAoIWluUmFuZ2UocywgXzFuLCBzZWNwMjU2azFOKSlcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgY29uc3QgZSA9IGNoYWxsZW5nZShudW1UbzMyYihyKSwgcG9pbnRUb0J5dGVzKFApLCBtKTsgLy8gaW50KGNoYWxsZW5nZShieXRlcyhyKXx8Ynl0ZXMoUCl8fG0pKSVuXG4gICAgICAgIGNvbnN0IFIgPSBHbXVsQWRkKFAsIHMsIG1vZE4oLWUpKTsgLy8gUiA9IHPii4VHIC0gZeKLhVBcbiAgICAgICAgaWYgKCFSIHx8ICFSLmhhc0V2ZW5ZKCkgfHwgUi50b0FmZmluZSgpLnggIT09IHIpXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7IC8vIC1lUCA9PSAobi1lKVBcbiAgICAgICAgcmV0dXJuIHRydWU7IC8vIEZhaWwgaWYgaXNfaW5maW5pdGUoUikgLyBub3QgaGFzX2V2ZW5feShSKSAvIHgoUikg4omgIHIuXG4gICAgfVxuICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxufVxuLyoqXG4gKiBTY2hub3JyIHNpZ25hdHVyZXMgb3ZlciBzZWNwMjU2azEuXG4gKiBodHRwczovL2dpdGh1Yi5jb20vYml0Y29pbi9iaXBzL2Jsb2IvbWFzdGVyL2JpcC0wMzQwLm1lZGlhd2lraVxuICogQGV4YW1wbGVcbiAqIGBgYGpzXG4gKiBpbXBvcnQgeyBzY2hub3JyIH0gZnJvbSAnQG5vYmxlL2N1cnZlcy9zZWNwMjU2azEnO1xuICogY29uc3QgcHJpdiA9IHNjaG5vcnIudXRpbHMucmFuZG9tUHJpdmF0ZUtleSgpO1xuICogY29uc3QgcHViID0gc2Nobm9yci5nZXRQdWJsaWNLZXkocHJpdik7XG4gKiBjb25zdCBtc2cgPSBuZXcgVGV4dEVuY29kZXIoKS5lbmNvZGUoJ2hlbGxvJyk7XG4gKiBjb25zdCBzaWcgPSBzY2hub3JyLnNpZ24obXNnLCBwcml2KTtcbiAqIGNvbnN0IGlzVmFsaWQgPSBzY2hub3JyLnZlcmlmeShzaWcsIG1zZywgcHViKTtcbiAqIGBgYFxuICovXG5leHBvcnQgY29uc3Qgc2Nobm9yciA9IC8qIEBfX1BVUkVfXyAqLyAoKCkgPT4gKHtcbiAgICBnZXRQdWJsaWNLZXk6IHNjaG5vcnJHZXRQdWJsaWNLZXksXG4gICAgc2lnbjogc2Nobm9yclNpZ24sXG4gICAgdmVyaWZ5OiBzY2hub3JyVmVyaWZ5LFxuICAgIHV0aWxzOiB7XG4gICAgICAgIHJhbmRvbVByaXZhdGVLZXk6IHNlY3AyNTZrMS51dGlscy5yYW5kb21Qcml2YXRlS2V5LFxuICAgICAgICBsaWZ0X3gsXG4gICAgICAgIHBvaW50VG9CeXRlcyxcbiAgICAgICAgbnVtYmVyVG9CeXRlc0JFLFxuICAgICAgICBieXRlc1RvTnVtYmVyQkUsXG4gICAgICAgIHRhZ2dlZEhhc2gsXG4gICAgICAgIG1vZCxcbiAgICB9LFxufSkpKCk7XG5jb25zdCBpc29NYXAgPSAvKiBAX19QVVJFX18gKi8gKCgpID0+IGlzb2dlbnlNYXAoRnBrMSwgW1xuICAgIC8vIHhOdW1cbiAgICBbXG4gICAgICAgICcweDhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhlMzhkYWFhYWE4YzcnLFxuICAgICAgICAnMHg3ZDNkNGM4MGJjMzIxZDViOWYzMTVjZWE3ZmQ0NGM1ZDU5NWQyZmMwYmY2M2I5MmRmZmYxMDQ0ZjE3YzY1ODEnLFxuICAgICAgICAnMHg1MzRjMzI4ZDIzZjIzNGU2ZTJhNDEzZGVjYTI1Y2FlY2U0NTA2MTQ0MDM3YzQwMzE0ZWNiZDBiNTNkOWRkMjYyJyxcbiAgICAgICAgJzB4OGUzOGUzOGUzOGUzOGUzOGUzOGUzOGUzOGUzOGUzOGUzOGUzOGUzOGUzOGUzOGUzOGUzOGUzOGRhYWFhYTg4YycsXG4gICAgXSxcbiAgICAvLyB4RGVuXG4gICAgW1xuICAgICAgICAnMHhkMzU3NzExOTNkOTQ5MThhOWNhMzRjY2JiN2I2NDBkZDg2Y2Q0MDk1NDJmODQ4N2Q5ZmU2Yjc0NTc4MWViNDliJyxcbiAgICAgICAgJzB4ZWRhZGM2ZjY0MzgzZGMxZGY3YzRiMmQ1MWI1NDIyNTQwNmQzNmI2NDFmNWU0MWJiYzUyYTU2NjEyYThjNmQxNCcsXG4gICAgICAgICcweDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDEnLCAvLyBMQVNUIDFcbiAgICBdLFxuICAgIC8vIHlOdW1cbiAgICBbXG4gICAgICAgICcweDRiZGExMmY2ODRiZGExMmY2ODRiZGExMmY2ODRiZGExMmY2ODRiZGExMmY2ODRiZGExMmY2ODRiOGUzOGUyM2MnLFxuICAgICAgICAnMHhjNzVlMGMzMmQ1Y2I3YzBmYTlkMGE1NGIxMmEwYTZkNTY0N2FiMDQ2ZDY4NmRhNmZkZmZjOTBmYzIwMWQ3MWEzJyxcbiAgICAgICAgJzB4MjlhNjE5NDY5MWY5MWE3MzcxNTIwOWVmNjUxMmU1NzY3MjI4MzBhMjAxYmUyMDE4YTc2NWU4NWE5ZWNlZTkzMScsXG4gICAgICAgICcweDJmNjg0YmRhMTJmNjg0YmRhMTJmNjg0YmRhMTJmNjg0YmRhMTJmNjg0YmRhMTJmNjg0YmRhMTJmMzhlMzhkODQnLFxuICAgIF0sXG4gICAgLy8geURlblxuICAgIFtcbiAgICAgICAgJzB4ZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmVmZmZmZjkzYicsXG4gICAgICAgICcweDdhMDY1MzRiYjhiZGI0OWZkNWU5ZTY2MzI3MjJjMjk4OTQ2N2MxYmZjOGU4ZDk3OGRmYjQyNWQyNjg1YzI1NzMnLFxuICAgICAgICAnMHg2NDg0YWE3MTY1NDVjYTJjZjNhNzBjM2ZhOGZlMzM3ZTBhM2QyMTE2MmYwZDYyOTlhN2JmODE5MmJmZDJhNzZmJyxcbiAgICAgICAgJzB4MDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMScsIC8vIExBU1QgMVxuICAgIF0sXG5dLm1hcCgoaSkgPT4gaS5tYXAoKGopID0+IEJpZ0ludChqKSkpKSkoKTtcbmNvbnN0IG1hcFNXVSA9IC8qIEBfX1BVUkVfXyAqLyAoKCkgPT4gbWFwVG9DdXJ2ZVNpbXBsZVNXVShGcGsxLCB7XG4gICAgQTogQmlnSW50KCcweDNmODczMWFiZGQ2NjFhZGNhMDhhNTU1OGYwZjVkMjcyZTk1M2QzNjNjYjZmMGU1ZDQwNTQ0N2MwMWE0NDQ1MzMnKSxcbiAgICBCOiBCaWdJbnQoJzE3NzEnKSxcbiAgICBaOiBGcGsxLmNyZWF0ZShCaWdJbnQoJy0xMScpKSxcbn0pKSgpO1xuLyoqIEhhc2hpbmcgLyBlbmNvZGluZyB0byBzZWNwMjU2azEgcG9pbnRzIC8gZmllbGQuIFJGQyA5MzgwIG1ldGhvZHMuICovXG5leHBvcnQgY29uc3Qgc2VjcDI1NmsxX2hhc2hlciA9IC8qIEBfX1BVUkVfXyAqLyAoKCkgPT4gY3JlYXRlSGFzaGVyKHNlY3AyNTZrMS5Qcm9qZWN0aXZlUG9pbnQsIChzY2FsYXJzKSA9PiB7XG4gICAgY29uc3QgeyB4LCB5IH0gPSBtYXBTV1UoRnBrMS5jcmVhdGUoc2NhbGFyc1swXSkpO1xuICAgIHJldHVybiBpc29NYXAoeCwgeSk7XG59LCB7XG4gICAgRFNUOiAnc2VjcDI1NmsxX1hNRDpTSEEtMjU2X1NTV1VfUk9fJyxcbiAgICBlbmNvZGVEU1Q6ICdzZWNwMjU2azFfWE1EOlNIQS0yNTZfU1NXVV9OVV8nLFxuICAgIHA6IEZwazEuT1JERVIsXG4gICAgbTogMSxcbiAgICBrOiAxMjgsXG4gICAgZXhwYW5kOiAneG1kJyxcbiAgICBoYXNoOiBzaGEyNTYsXG59KSkoKTtcbmV4cG9ydCBjb25zdCBoYXNoVG9DdXJ2ZSA9IC8qIEBfX1BVUkVfXyAqLyAoKCkgPT4gc2VjcDI1NmsxX2hhc2hlci5oYXNoVG9DdXJ2ZSkoKTtcbmV4cG9ydCBjb25zdCBlbmNvZGVUb0N1cnZlID0gLyogQF9fUFVSRV9fICovICgoKSA9PiBzZWNwMjU2azFfaGFzaGVyLmVuY29kZVRvQ3VydmUpKCk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zZWNwMjU2azEuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/node_modules/@noble/curves/esm/secp256k1.js\n"));

/***/ })

}]);