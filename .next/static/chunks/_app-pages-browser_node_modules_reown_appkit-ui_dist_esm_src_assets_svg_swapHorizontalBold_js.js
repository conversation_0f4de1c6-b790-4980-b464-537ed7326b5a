"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalBold_js"],{

/***/ "(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalBold.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalBold.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   swapHorizontalBoldSvg: () => (/* binding */ swapHorizontalBoldSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/./node_modules/lit/index.js\");\n\nconst swapHorizontalBoldSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg width=\"10\" height=\"10\" viewBox=\"0 0 10 10\">\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    d=\"M3.77986 0.566631C4.0589 0.845577 4.0589 1.29784 3.77986 1.57678L3.08261 2.2738H6.34184C6.73647 2.2738 7.05637 2.5936 7.05637 2.98808C7.05637 3.38257 6.73647 3.70237 6.34184 3.70237H3.08261L3.77986 4.39938C4.0589 4.67833 4.0589 5.13059 3.77986 5.40954C3.50082 5.68848 3.04841 5.68848 2.76937 5.40954L0.852346 3.49316C0.573306 3.21421 0.573306 2.76195 0.852346 2.48301L2.76937 0.566631C3.04841 0.287685 3.50082 0.287685 3.77986 0.566631ZM6.22 4.59102C6.49904 4.31208 6.95145 4.31208 7.23049 4.59102L9.14751 6.5074C9.42655 6.78634 9.42655 7.23861 9.14751 7.51755L7.23049 9.43393C6.95145 9.71287 6.49904 9.71287 6.22 9.43393C5.94096 9.15498 5.94096 8.70272 6.22 8.42377L6.91725 7.72676L3.65802 7.72676C3.26339 7.72676 2.94349 7.40696 2.94349 7.01247C2.94349 6.61798 3.26339 6.29819 3.65802 6.29819L6.91725 6.29819L6.22 5.60117C5.94096 5.32223 5.94096 4.86997 6.22 4.59102Z\"\n    clip-rule=\"evenodd\"\n  />\n</svg>`;\n//# sourceMappingURL=swapHorizontalBold.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalBold.js\n"));

/***/ })

}]);