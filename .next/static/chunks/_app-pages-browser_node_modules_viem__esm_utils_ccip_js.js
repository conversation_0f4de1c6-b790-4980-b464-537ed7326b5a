"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_viem__esm_utils_ccip_js"],{

/***/ "(app-pages-browser)/./node_modules/viem/_esm/errors/ccip.js":
/*!***********************************************!*\
  !*** ./node_modules/viem/_esm/errors/ccip.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OffchainLookupError: () => (/* binding */ OffchainLookupError),\n/* harmony export */   OffchainLookupResponseMalformedError: () => (/* binding */ OffchainLookupResponseMalformedError),\n/* harmony export */   OffchainLookupSenderMismatchError: () => (/* binding */ OffchainLookupSenderMismatchError)\n/* harmony export */ });\n/* harmony import */ var _utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/stringify.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/stringify.js\");\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/base.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/utils.js\");\n\n\n\nclass OffchainLookupError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ callbackSelector, cause, data, extraData, sender, urls, }) {\n        super(cause.shortMessage ||\n            'An error occurred while fetching for an offchain result.', {\n            cause,\n            metaMessages: [\n                ...(cause.metaMessages || []),\n                cause.metaMessages?.length ? '' : [],\n                'Offchain Gateway Call:',\n                urls && [\n                    '  Gateway URL(s):',\n                    ...urls.map((url) => `    ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`),\n                ],\n                `  Sender: ${sender}`,\n                `  Data: ${data}`,\n                `  Callback selector: ${callbackSelector}`,\n                `  Extra data: ${extraData}`,\n            ].flat(),\n            name: 'OffchainLookupError',\n        });\n    }\n}\nclass OffchainLookupResponseMalformedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ result, url }) {\n        super('Offchain gateway response is malformed. Response data must be a hex value.', {\n            metaMessages: [\n                `Gateway URL: ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`,\n                `Response: ${(0,_utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__.stringify)(result)}`,\n            ],\n            name: 'OffchainLookupResponseMalformedError',\n        });\n    }\n}\nclass OffchainLookupSenderMismatchError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ sender, to }) {\n        super('Reverted sender address does not match target contract address (`to`).', {\n            metaMessages: [\n                `Contract address: ${to}`,\n                `OffchainLookup sender address: ${sender}`,\n            ],\n            name: 'OffchainLookupSenderMismatchError',\n        });\n    }\n}\n//# sourceMappingURL=ccip.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/errors/ccip.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/viem/_esm/utils/ccip.js":
/*!**********************************************!*\
  !*** ./node_modules/viem/_esm/utils/ccip.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ccipRequest: () => (/* binding */ ccipRequest),\n/* harmony export */   offchainLookup: () => (/* binding */ offchainLookup),\n/* harmony export */   offchainLookupAbiItem: () => (/* binding */ offchainLookupAbiItem),\n/* harmony export */   offchainLookupSignature: () => (/* binding */ offchainLookupSignature)\n/* harmony export */ });\n/* harmony import */ var _actions_public_call_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../actions/public/call.js */ \"(app-pages-browser)/./node_modules/viem/_esm/actions/public/call.js\");\n/* harmony import */ var _errors_ccip_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../errors/ccip.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/ccip.js\");\n/* harmony import */ var _errors_request_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../errors/request.js */ \"(app-pages-browser)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var _abi_decodeErrorResult_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abi/decodeErrorResult.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/decodeErrorResult.js\");\n/* harmony import */ var _abi_encodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./abi/encodeAbiParameters.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/abi/encodeAbiParameters.js\");\n/* harmony import */ var _address_isAddressEqual_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./address/isAddressEqual.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/address/isAddressEqual.js\");\n/* harmony import */ var _data_concat_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./data/concat.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/data/concat.js\");\n/* harmony import */ var _data_isHex_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./data/isHex.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/data/isHex.js\");\n/* harmony import */ var _ens_localBatchGatewayRequest_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ens/localBatchGatewayRequest.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/ens/localBatchGatewayRequest.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./stringify.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/stringify.js\");\n\n\n\n\n\n\n\n\n\n\nconst offchainLookupSignature = '0x556f1830';\nconst offchainLookupAbiItem = {\n    name: 'OffchainLookup',\n    type: 'error',\n    inputs: [\n        {\n            name: 'sender',\n            type: 'address',\n        },\n        {\n            name: 'urls',\n            type: 'string[]',\n        },\n        {\n            name: 'callData',\n            type: 'bytes',\n        },\n        {\n            name: 'callbackFunction',\n            type: 'bytes4',\n        },\n        {\n            name: 'extraData',\n            type: 'bytes',\n        },\n    ],\n};\nasync function offchainLookup(client, { blockNumber, blockTag, data, to, }) {\n    const { args } = (0,_abi_decodeErrorResult_js__WEBPACK_IMPORTED_MODULE_0__.decodeErrorResult)({\n        data,\n        abi: [offchainLookupAbiItem],\n    });\n    const [sender, urls, callData, callbackSelector, extraData] = args;\n    const { ccipRead } = client;\n    const ccipRequest_ = ccipRead && typeof ccipRead?.request === 'function'\n        ? ccipRead.request\n        : ccipRequest;\n    try {\n        if (!(0,_address_isAddressEqual_js__WEBPACK_IMPORTED_MODULE_1__.isAddressEqual)(to, sender))\n            throw new _errors_ccip_js__WEBPACK_IMPORTED_MODULE_2__.OffchainLookupSenderMismatchError({ sender, to });\n        const result = urls.includes(_ens_localBatchGatewayRequest_js__WEBPACK_IMPORTED_MODULE_3__.localBatchGatewayUrl)\n            ? await (0,_ens_localBatchGatewayRequest_js__WEBPACK_IMPORTED_MODULE_3__.localBatchGatewayRequest)({\n                data: callData,\n                ccipRequest: ccipRequest_,\n            })\n            : await ccipRequest_({ data: callData, sender, urls });\n        const { data: data_ } = await (0,_actions_public_call_js__WEBPACK_IMPORTED_MODULE_4__.call)(client, {\n            blockNumber,\n            blockTag,\n            data: (0,_data_concat_js__WEBPACK_IMPORTED_MODULE_5__.concat)([\n                callbackSelector,\n                (0,_abi_encodeAbiParameters_js__WEBPACK_IMPORTED_MODULE_6__.encodeAbiParameters)([{ type: 'bytes' }, { type: 'bytes' }], [result, extraData]),\n            ]),\n            to,\n        });\n        return data_;\n    }\n    catch (err) {\n        throw new _errors_ccip_js__WEBPACK_IMPORTED_MODULE_2__.OffchainLookupError({\n            callbackSelector,\n            cause: err,\n            data,\n            extraData,\n            sender,\n            urls,\n        });\n    }\n}\nasync function ccipRequest({ data, sender, urls, }) {\n    let error = new Error('An unknown error occurred.');\n    for (let i = 0; i < urls.length; i++) {\n        const url = urls[i];\n        const method = url.includes('{data}') ? 'GET' : 'POST';\n        const body = method === 'POST' ? { data, sender } : undefined;\n        const headers = method === 'POST' ? { 'Content-Type': 'application/json' } : {};\n        try {\n            const response = await fetch(url.replace('{sender}', sender.toLowerCase()).replace('{data}', data), {\n                body: JSON.stringify(body),\n                headers,\n                method,\n            });\n            let result;\n            if (response.headers.get('Content-Type')?.startsWith('application/json')) {\n                result = (await response.json()).data;\n            }\n            else {\n                result = (await response.text());\n            }\n            if (!response.ok) {\n                error = new _errors_request_js__WEBPACK_IMPORTED_MODULE_7__.HttpRequestError({\n                    body,\n                    details: result?.error\n                        ? (0,_stringify_js__WEBPACK_IMPORTED_MODULE_8__.stringify)(result.error)\n                        : response.statusText,\n                    headers: response.headers,\n                    status: response.status,\n                    url,\n                });\n                continue;\n            }\n            if (!(0,_data_isHex_js__WEBPACK_IMPORTED_MODULE_9__.isHex)(result)) {\n                error = new _errors_ccip_js__WEBPACK_IMPORTED_MODULE_2__.OffchainLookupResponseMalformedError({\n                    result,\n                    url,\n                });\n                continue;\n            }\n            return result;\n        }\n        catch (err) {\n            error = new _errors_request_js__WEBPACK_IMPORTED_MODULE_7__.HttpRequestError({\n                body,\n                details: err.message,\n                url,\n            });\n        }\n    }\n    throw error;\n}\n//# sourceMappingURL=ccip.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/utils/ccip.js\n"));

/***/ })

}]);