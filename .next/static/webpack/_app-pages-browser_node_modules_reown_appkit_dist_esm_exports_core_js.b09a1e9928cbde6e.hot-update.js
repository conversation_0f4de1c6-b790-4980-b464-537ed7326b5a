"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_node_modules_reown_appkit_dist_esm_exports_core_js",{

/***/ "(app-pages-browser)/./node_modules/viem/_esm/chains/definitions/sepolia.js":
/*!**************************************************************!*\
  !*** ./node_modules/viem/_esm/chains/definitions/sepolia.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sepolia: () => (/* binding */ sepolia)\n/* harmony export */ });\n/* harmony import */ var _utils_chain_defineChain_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/chain/defineChain.js */ \"(app-pages-browser)/./node_modules/viem/_esm/utils/chain/defineChain.js\");\n\nconst sepolia = /*#__PURE__*/ (0,_utils_chain_defineChain_js__WEBPACK_IMPORTED_MODULE_0__.defineChain)({\n    id: 11_155_111,\n    name: 'Sepolia',\n    nativeCurrency: { name: 'Sepolia Ether', symbol: 'ETH', decimals: 18 },\n    rpcUrls: {\n        default: {\n            http: ['https://sepolia.drpc.org'],\n        },\n    },\n    blockExplorers: {\n        default: {\n            name: 'Etherscan',\n            url: 'https://sepolia.etherscan.io',\n            apiUrl: 'https://api-sepolia.etherscan.io/api',\n        },\n    },\n    contracts: {\n        multicall3: {\n            address: '******************************************',\n            blockCreated: 751532,\n        },\n        ensRegistry: { address: '******************************************' },\n        ensUniversalResolver: {\n            address: '******************************************',\n            blockCreated: 5_317_080,\n        },\n    },\n    testnet: true,\n});\n//# sourceMappingURL=sepolia.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/viem/_esm/chains/definitions/sepolia.js\n"));

/***/ })

});