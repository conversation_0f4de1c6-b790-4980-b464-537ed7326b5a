"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/thread-stream";
exports.ids = ["vendor-chunks/thread-stream"];
exports.modules = {

/***/ "(ssr)/./node_modules/thread-stream/index.js":
/*!*********************************************!*\
  !*** ./node_modules/thread-stream/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { EventEmitter } = __webpack_require__(/*! events */ \"events\")\nconst { Worker } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\nconst { join } = __webpack_require__(/*! path */ \"path\")\nconst { pathToFileURL } = __webpack_require__(/*! url */ \"url\")\nconst { wait } = __webpack_require__(/*! ./lib/wait */ \"(ssr)/./node_modules/thread-stream/lib/wait.js\")\nconst {\n  WRITE_INDEX,\n  READ_INDEX\n} = __webpack_require__(/*! ./lib/indexes */ \"(ssr)/./node_modules/thread-stream/lib/indexes.js\")\nconst buffer = __webpack_require__(/*! buffer */ \"buffer\")\nconst assert = __webpack_require__(/*! assert */ \"assert\")\n\nconst kImpl = Symbol('kImpl')\n\n// V8 limit for string size\nconst MAX_STRING = buffer.constants.MAX_STRING_LENGTH\n\nclass FakeWeakRef {\n  constructor (value) {\n    this._value = value\n  }\n\n  deref () {\n    return this._value\n  }\n}\n\nconst FinalizationRegistry = global.FinalizationRegistry || class FakeFinalizationRegistry {\n  register () {}\n  unregister () {}\n}\n\nconst WeakRef = global.WeakRef || FakeWeakRef\n\nconst registry = new FinalizationRegistry((worker) => {\n  if (worker.exited) {\n    return\n  }\n  worker.terminate()\n})\n\nfunction createWorker (stream, opts) {\n  const { filename, workerData } = opts\n\n  const bundlerOverrides = '__bundlerPathsOverrides' in globalThis ? globalThis.__bundlerPathsOverrides : {}\n  const toExecute = bundlerOverrides['thread-stream-worker'] || join(__dirname, 'lib', 'worker.js')\n\n  const worker = new Worker(toExecute, {\n    ...opts.workerOpts,\n    workerData: {\n      filename: filename.indexOf('file://') === 0\n        ? filename\n        : pathToFileURL(filename).href,\n      dataBuf: stream[kImpl].dataBuf,\n      stateBuf: stream[kImpl].stateBuf,\n      workerData\n    }\n  })\n\n  // We keep a strong reference for now,\n  // we need to start writing first\n  worker.stream = new FakeWeakRef(stream)\n\n  worker.on('message', onWorkerMessage)\n  worker.on('exit', onWorkerExit)\n  registry.register(stream, worker)\n\n  return worker\n}\n\nfunction drain (stream) {\n  assert(!stream[kImpl].sync)\n  if (stream[kImpl].needDrain) {\n    stream[kImpl].needDrain = false\n    stream.emit('drain')\n  }\n}\n\nfunction nextFlush (stream) {\n  const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n  let leftover = stream[kImpl].data.length - writeIndex\n\n  if (leftover > 0) {\n    if (stream[kImpl].buf.length === 0) {\n      stream[kImpl].flushing = false\n\n      if (stream[kImpl].ending) {\n        end(stream)\n      } else if (stream[kImpl].needDrain) {\n        process.nextTick(drain, stream)\n      }\n\n      return\n    }\n\n    let toWrite = stream[kImpl].buf.slice(0, leftover)\n    let toWriteBytes = Buffer.byteLength(toWrite)\n    if (toWriteBytes <= leftover) {\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      // process._rawDebug('writing ' + toWrite.length)\n      write(stream, toWrite, nextFlush.bind(null, stream))\n    } else {\n      // multi-byte utf-8\n      stream.flush(() => {\n        // err is already handled in flush()\n        if (stream.destroyed) {\n          return\n        }\n\n        Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n        Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n\n        // Find a toWrite length that fits the buffer\n        // it must exists as the buffer is at least 4 bytes length\n        // and the max utf-8 length for a char is 4 bytes.\n        while (toWriteBytes > stream[kImpl].data.length) {\n          leftover = leftover / 2\n          toWrite = stream[kImpl].buf.slice(0, leftover)\n          toWriteBytes = Buffer.byteLength(toWrite)\n        }\n        stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n        write(stream, toWrite, nextFlush.bind(null, stream))\n      })\n    }\n  } else if (leftover === 0) {\n    if (writeIndex === 0 && stream[kImpl].buf.length === 0) {\n      // we had a flushSync in the meanwhile\n      return\n    }\n    stream.flush(() => {\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n      nextFlush(stream)\n    })\n  } else {\n    // This should never happen\n    throw new Error('overwritten')\n  }\n}\n\nfunction onWorkerMessage (msg) {\n  const stream = this.stream.deref()\n  if (stream === undefined) {\n    this.exited = true\n    // Terminate the worker.\n    this.terminate()\n    return\n  }\n\n  switch (msg.code) {\n    case 'READY':\n      // Replace the FakeWeakRef with a\n      // proper one.\n      this.stream = new WeakRef(stream)\n\n      stream.flush(() => {\n        stream[kImpl].ready = true\n        stream.emit('ready')\n      })\n      break\n    case 'ERROR':\n      destroy(stream, msg.err)\n      break\n    default:\n      throw new Error('this should not happen: ' + msg.code)\n  }\n}\n\nfunction onWorkerExit (code) {\n  const stream = this.stream.deref()\n  if (stream === undefined) {\n    // Nothing to do, the worker already exit\n    return\n  }\n  registry.unregister(stream)\n  stream.worker.exited = true\n  stream.worker.off('exit', onWorkerExit)\n  destroy(stream, code !== 0 ? new Error('The worker thread exited') : null)\n}\n\nclass ThreadStream extends EventEmitter {\n  constructor (opts = {}) {\n    super()\n\n    if (opts.bufferSize < 4) {\n      throw new Error('bufferSize must at least fit a 4-byte utf-8 char')\n    }\n\n    this[kImpl] = {}\n    this[kImpl].stateBuf = new SharedArrayBuffer(128)\n    this[kImpl].state = new Int32Array(this[kImpl].stateBuf)\n    this[kImpl].dataBuf = new SharedArrayBuffer(opts.bufferSize || 4 * 1024 * 1024)\n    this[kImpl].data = Buffer.from(this[kImpl].dataBuf)\n    this[kImpl].sync = opts.sync || false\n    this[kImpl].ending = false\n    this[kImpl].ended = false\n    this[kImpl].needDrain = false\n    this[kImpl].destroyed = false\n    this[kImpl].flushing = false\n    this[kImpl].ready = false\n    this[kImpl].finished = false\n    this[kImpl].errored = null\n    this[kImpl].closed = false\n    this[kImpl].buf = ''\n\n    // TODO (fix): Make private?\n    this.worker = createWorker(this, opts) // TODO (fix): make private\n  }\n\n  write (data) {\n    if (this[kImpl].destroyed) {\n      throw new Error('the worker has exited')\n    }\n\n    if (this[kImpl].ending) {\n      throw new Error('the worker is ending')\n    }\n\n    if (this[kImpl].flushing && this[kImpl].buf.length + data.length >= MAX_STRING) {\n      try {\n        writeSync(this)\n        this[kImpl].flushing = true\n      } catch (err) {\n        destroy(this, err)\n        return false\n      }\n    }\n\n    this[kImpl].buf += data\n\n    if (this[kImpl].sync) {\n      try {\n        writeSync(this)\n        return true\n      } catch (err) {\n        destroy(this, err)\n        return false\n      }\n    }\n\n    if (!this[kImpl].flushing) {\n      this[kImpl].flushing = true\n      setImmediate(nextFlush, this)\n    }\n\n    this[kImpl].needDrain = this[kImpl].data.length - this[kImpl].buf.length - Atomics.load(this[kImpl].state, WRITE_INDEX) <= 0\n    return !this[kImpl].needDrain\n  }\n\n  end () {\n    if (this[kImpl].destroyed) {\n      return\n    }\n\n    this[kImpl].ending = true\n    end(this)\n  }\n\n  flush (cb) {\n    if (this[kImpl].destroyed) {\n      if (typeof cb === 'function') {\n        process.nextTick(cb, new Error('the worker has exited'))\n      }\n      return\n    }\n\n    // TODO write all .buf\n    const writeIndex = Atomics.load(this[kImpl].state, WRITE_INDEX)\n    // process._rawDebug(`(flush) readIndex (${Atomics.load(this.state, READ_INDEX)}) writeIndex (${Atomics.load(this.state, WRITE_INDEX)})`)\n    wait(this[kImpl].state, READ_INDEX, writeIndex, Infinity, (err, res) => {\n      if (err) {\n        destroy(this, err)\n        process.nextTick(cb, err)\n        return\n      }\n      if (res === 'not-equal') {\n        // TODO handle deadlock\n        this.flush(cb)\n        return\n      }\n      process.nextTick(cb)\n    })\n  }\n\n  flushSync () {\n    if (this[kImpl].destroyed) {\n      return\n    }\n\n    writeSync(this)\n    flushSync(this)\n  }\n\n  unref () {\n    this.worker.unref()\n  }\n\n  ref () {\n    this.worker.ref()\n  }\n\n  get ready () {\n    return this[kImpl].ready\n  }\n\n  get destroyed () {\n    return this[kImpl].destroyed\n  }\n\n  get closed () {\n    return this[kImpl].closed\n  }\n\n  get writable () {\n    return !this[kImpl].destroyed && !this[kImpl].ending\n  }\n\n  get writableEnded () {\n    return this[kImpl].ending\n  }\n\n  get writableFinished () {\n    return this[kImpl].finished\n  }\n\n  get writableNeedDrain () {\n    return this[kImpl].needDrain\n  }\n\n  get writableObjectMode () {\n    return false\n  }\n\n  get writableErrored () {\n    return this[kImpl].errored\n  }\n}\n\nfunction destroy (stream, err) {\n  if (stream[kImpl].destroyed) {\n    return\n  }\n  stream[kImpl].destroyed = true\n\n  if (err) {\n    stream[kImpl].errored = err\n    stream.emit('error', err)\n  }\n\n  if (!stream.worker.exited) {\n    stream.worker.terminate()\n      .catch(() => {})\n      .then(() => {\n        stream[kImpl].closed = true\n        stream.emit('close')\n      })\n  } else {\n    setImmediate(() => {\n      stream[kImpl].closed = true\n      stream.emit('close')\n    })\n  }\n}\n\nfunction write (stream, data, cb) {\n  // data is smaller than the shared buffer length\n  const current = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n  const length = Buffer.byteLength(data)\n  stream[kImpl].data.write(data, current)\n  Atomics.store(stream[kImpl].state, WRITE_INDEX, current + length)\n  Atomics.notify(stream[kImpl].state, WRITE_INDEX)\n  cb()\n  return true\n}\n\nfunction end (stream) {\n  if (stream[kImpl].ended || !stream[kImpl].ending || stream[kImpl].flushing) {\n    return\n  }\n  stream[kImpl].ended = true\n\n  try {\n    stream.flushSync()\n\n    let readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n    // process._rawDebug('writing index')\n    Atomics.store(stream[kImpl].state, WRITE_INDEX, -1)\n    // process._rawDebug(`(end) readIndex (${Atomics.load(stream.state, READ_INDEX)}) writeIndex (${Atomics.load(stream.state, WRITE_INDEX)})`)\n    Atomics.notify(stream[kImpl].state, WRITE_INDEX)\n\n    // Wait for the process to complete\n    let spins = 0\n    while (readIndex !== -1) {\n      // process._rawDebug(`read = ${read}`)\n      Atomics.wait(stream[kImpl].state, READ_INDEX, readIndex, 1000)\n      readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n      if (readIndex === -2) {\n        throw new Error('end() failed')\n      }\n\n      if (++spins === 10) {\n        throw new Error('end() took too long (10s)')\n      }\n    }\n\n    process.nextTick(() => {\n      stream[kImpl].finished = true\n      stream.emit('finish')\n    })\n  } catch (err) {\n    destroy(stream, err)\n  }\n  // process._rawDebug('end finished...')\n}\n\nfunction writeSync (stream) {\n  const cb = () => {\n    if (stream[kImpl].ending) {\n      end(stream)\n    } else if (stream[kImpl].needDrain) {\n      process.nextTick(drain, stream)\n    }\n  }\n  stream[kImpl].flushing = false\n\n  while (stream[kImpl].buf.length !== 0) {\n    const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n    let leftover = stream[kImpl].data.length - writeIndex\n    if (leftover === 0) {\n      flushSync(stream)\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n      continue\n    } else if (leftover < 0) {\n      // stream should never happen\n      throw new Error('overwritten')\n    }\n\n    let toWrite = stream[kImpl].buf.slice(0, leftover)\n    let toWriteBytes = Buffer.byteLength(toWrite)\n    if (toWriteBytes <= leftover) {\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      // process._rawDebug('writing ' + toWrite.length)\n      write(stream, toWrite, cb)\n    } else {\n      // multi-byte utf-8\n      flushSync(stream)\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n\n      // Find a toWrite length that fits the buffer\n      // it must exists as the buffer is at least 4 bytes length\n      // and the max utf-8 length for a char is 4 bytes.\n      while (toWriteBytes > stream[kImpl].buf.length) {\n        leftover = leftover / 2\n        toWrite = stream[kImpl].buf.slice(0, leftover)\n        toWriteBytes = Buffer.byteLength(toWrite)\n      }\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      write(stream, toWrite, cb)\n    }\n  }\n}\n\nfunction flushSync (stream) {\n  if (stream[kImpl].flushing) {\n    throw new Error('unable to flush while flushing')\n  }\n\n  // process._rawDebug('flushSync started')\n\n  const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n\n  let spins = 0\n\n  // TODO handle deadlock\n  while (true) {\n    const readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n    if (readIndex === -2) {\n      throw new Error('_flushSync failed')\n    }\n\n    // process._rawDebug(`(flushSync) readIndex (${readIndex}) writeIndex (${writeIndex})`)\n    if (readIndex !== writeIndex) {\n      // TODO stream timeouts for some reason.\n      Atomics.wait(stream[kImpl].state, READ_INDEX, readIndex, 1000)\n    } else {\n      break\n    }\n\n    if (++spins === 10) {\n      throw new Error('_flushSync took too long (10s)')\n    }\n  }\n  // process._rawDebug('flushSync finished')\n}\n\nmodule.exports = ThreadStream\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/thread-stream/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/thread-stream/lib/indexes.js":
/*!***************************************************!*\
  !*** ./node_modules/thread-stream/lib/indexes.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\n\nconst WRITE_INDEX = 4\nconst READ_INDEX = 8\n\nmodule.exports = {\n  WRITE_INDEX,\n  READ_INDEX\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdGhyZWFkLXN0cmVhbS9saWIvaW5kZXhlcy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3RocmVhZC1zdHJlYW0vbGliL2luZGV4ZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IFdSSVRFX0lOREVYID0gNFxuY29uc3QgUkVBRF9JTkRFWCA9IDhcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIFdSSVRFX0lOREVYLFxuICBSRUFEX0lOREVYXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/thread-stream/lib/indexes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/thread-stream/lib/wait.js":
/*!************************************************!*\
  !*** ./node_modules/thread-stream/lib/wait.js ***!
  \************************************************/
/***/ ((module) => {

eval("\n\nconst MAX_TIMEOUT = 1000\n\nfunction wait (state, index, expected, timeout, done) {\n  const max = Date.now() + timeout\n  let current = Atomics.load(state, index)\n  if (current === expected) {\n    done(null, 'ok')\n    return\n  }\n  let prior = current\n  const check = (backoff) => {\n    if (Date.now() > max) {\n      done(null, 'timed-out')\n    } else {\n      setTimeout(() => {\n        prior = current\n        current = Atomics.load(state, index)\n        if (current === prior) {\n          check(backoff >= MAX_TIMEOUT ? MAX_TIMEOUT : backoff * 2)\n        } else {\n          if (current === expected) done(null, 'ok')\n          else done(null, 'not-equal')\n        }\n      }, backoff)\n    }\n  }\n  check(1)\n}\n\n// let waitDiffCount = 0\nfunction waitDiff (state, index, expected, timeout, done) {\n  // const id = waitDiffCount++\n  // process._rawDebug(`>>> waitDiff ${id}`)\n  const max = Date.now() + timeout\n  let current = Atomics.load(state, index)\n  if (current !== expected) {\n    done(null, 'ok')\n    return\n  }\n  const check = (backoff) => {\n    // process._rawDebug(`${id} ${index} current ${current} expected ${expected}`)\n    // process._rawDebug('' + backoff)\n    if (Date.now() > max) {\n      done(null, 'timed-out')\n    } else {\n      setTimeout(() => {\n        current = Atomics.load(state, index)\n        if (current !== expected) {\n          done(null, 'ok')\n        } else {\n          check(backoff >= MAX_TIMEOUT ? MAX_TIMEOUT : backoff * 2)\n        }\n      }, backoff)\n    }\n  }\n  check(1)\n}\n\nmodule.exports = { wait, waitDiff }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/thread-stream/lib/wait.js\n");

/***/ })

};
;