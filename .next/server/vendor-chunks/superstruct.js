"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/superstruct";
exports.ids = ["vendor-chunks/superstruct"];
exports.modules = {

/***/ "(ssr)/./node_modules/superstruct/dist/index.mjs":
/*!*************************************************!*\
  !*** ./node_modules/superstruct/dist/index.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Struct: () => (/* binding */ Struct),\n/* harmony export */   StructError: () => (/* binding */ StructError),\n/* harmony export */   any: () => (/* binding */ any),\n/* harmony export */   array: () => (/* binding */ array),\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   assign: () => (/* binding */ assign),\n/* harmony export */   bigint: () => (/* binding */ bigint),\n/* harmony export */   boolean: () => (/* binding */ boolean),\n/* harmony export */   coerce: () => (/* binding */ coerce),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   date: () => (/* binding */ date),\n/* harmony export */   defaulted: () => (/* binding */ defaulted),\n/* harmony export */   define: () => (/* binding */ define),\n/* harmony export */   deprecated: () => (/* binding */ deprecated),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   empty: () => (/* binding */ empty),\n/* harmony export */   enums: () => (/* binding */ enums),\n/* harmony export */   func: () => (/* binding */ func),\n/* harmony export */   instance: () => (/* binding */ instance),\n/* harmony export */   integer: () => (/* binding */ integer),\n/* harmony export */   intersection: () => (/* binding */ intersection),\n/* harmony export */   is: () => (/* binding */ is),\n/* harmony export */   lazy: () => (/* binding */ lazy),\n/* harmony export */   literal: () => (/* binding */ literal),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   mask: () => (/* binding */ mask),\n/* harmony export */   max: () => (/* binding */ max),\n/* harmony export */   min: () => (/* binding */ min),\n/* harmony export */   never: () => (/* binding */ never),\n/* harmony export */   nonempty: () => (/* binding */ nonempty),\n/* harmony export */   nullable: () => (/* binding */ nullable),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   object: () => (/* binding */ object),\n/* harmony export */   omit: () => (/* binding */ omit),\n/* harmony export */   optional: () => (/* binding */ optional),\n/* harmony export */   partial: () => (/* binding */ partial),\n/* harmony export */   pattern: () => (/* binding */ pattern),\n/* harmony export */   pick: () => (/* binding */ pick),\n/* harmony export */   record: () => (/* binding */ record),\n/* harmony export */   refine: () => (/* binding */ refine),\n/* harmony export */   regexp: () => (/* binding */ regexp),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   size: () => (/* binding */ size),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   struct: () => (/* binding */ struct),\n/* harmony export */   trimmed: () => (/* binding */ trimmed),\n/* harmony export */   tuple: () => (/* binding */ tuple),\n/* harmony export */   type: () => (/* binding */ type),\n/* harmony export */   union: () => (/* binding */ union),\n/* harmony export */   unknown: () => (/* binding */ unknown),\n/* harmony export */   validate: () => (/* binding */ validate)\n/* harmony export */ });\n/**\n * A `StructFailure` represents a single specific failure in validation.\n */\n/**\n * `StructError` objects are thrown (or returned) when validation fails.\n *\n * Validation logic is design to exit early for maximum performance. The error\n * represents the first error encountered during validation. For more detail,\n * the `error.failures` property is a generator function that can be run to\n * continue validation and receive all the failures in the data.\n */\nclass StructError extends TypeError {\n    constructor(failure, failures) {\n        let cached;\n        const { message, explanation, ...rest } = failure;\n        const { path } = failure;\n        const msg = path.length === 0 ? message : `At path: ${path.join('.')} -- ${message}`;\n        super(explanation ?? msg);\n        if (explanation != null)\n            this.cause = msg;\n        Object.assign(this, rest);\n        this.name = this.constructor.name;\n        this.failures = () => {\n            return (cached ?? (cached = [failure, ...failures()]));\n        };\n    }\n}\n\n/**\n * Check if a value is an iterator.\n */\nfunction isIterable(x) {\n    return isObject(x) && typeof x[Symbol.iterator] === 'function';\n}\n/**\n * Check if a value is a plain object.\n */\nfunction isObject(x) {\n    return typeof x === 'object' && x != null;\n}\n/**\n * Check if a value is a plain object.\n */\nfunction isPlainObject(x) {\n    if (Object.prototype.toString.call(x) !== '[object Object]') {\n        return false;\n    }\n    const prototype = Object.getPrototypeOf(x);\n    return prototype === null || prototype === Object.prototype;\n}\n/**\n * Return a value as a printable string.\n */\nfunction print(value) {\n    if (typeof value === 'symbol') {\n        return value.toString();\n    }\n    return typeof value === 'string' ? JSON.stringify(value) : `${value}`;\n}\n/**\n * Shifts (removes and returns) the first value from the `input` iterator.\n * Like `Array.prototype.shift()` but for an `Iterator`.\n */\nfunction shiftIterator(input) {\n    const { done, value } = input.next();\n    return done ? undefined : value;\n}\n/**\n * Convert a single validation result to a failure.\n */\nfunction toFailure(result, context, struct, value) {\n    if (result === true) {\n        return;\n    }\n    else if (result === false) {\n        result = {};\n    }\n    else if (typeof result === 'string') {\n        result = { message: result };\n    }\n    const { path, branch } = context;\n    const { type } = struct;\n    const { refinement, message = `Expected a value of type \\`${type}\\`${refinement ? ` with refinement \\`${refinement}\\`` : ''}, but received: \\`${print(value)}\\``, } = result;\n    return {\n        value,\n        type,\n        refinement,\n        key: path[path.length - 1],\n        path,\n        branch,\n        ...result,\n        message,\n    };\n}\n/**\n * Convert a validation result to an iterable of failures.\n */\nfunction* toFailures(result, context, struct, value) {\n    if (!isIterable(result)) {\n        result = [result];\n    }\n    for (const r of result) {\n        const failure = toFailure(r, context, struct, value);\n        if (failure) {\n            yield failure;\n        }\n    }\n}\n/**\n * Check a value against a struct, traversing deeply into nested values, and\n * returning an iterator of failures or success.\n */\nfunction* run(value, struct, options = {}) {\n    const { path = [], branch = [value], coerce = false, mask = false } = options;\n    const ctx = { path, branch };\n    if (coerce) {\n        value = struct.coercer(value, ctx);\n        if (mask &&\n            struct.type !== 'type' &&\n            isObject(struct.schema) &&\n            isObject(value) &&\n            !Array.isArray(value)) {\n            for (const key in value) {\n                if (struct.schema[key] === undefined) {\n                    delete value[key];\n                }\n            }\n        }\n    }\n    let status = 'valid';\n    for (const failure of struct.validator(value, ctx)) {\n        failure.explanation = options.message;\n        status = 'not_valid';\n        yield [failure, undefined];\n    }\n    for (let [k, v, s] of struct.entries(value, ctx)) {\n        const ts = run(v, s, {\n            path: k === undefined ? path : [...path, k],\n            branch: k === undefined ? branch : [...branch, v],\n            coerce,\n            mask,\n            message: options.message,\n        });\n        for (const t of ts) {\n            if (t[0]) {\n                status = t[0].refinement != null ? 'not_refined' : 'not_valid';\n                yield [t[0], undefined];\n            }\n            else if (coerce) {\n                v = t[1];\n                if (k === undefined) {\n                    value = v;\n                }\n                else if (value instanceof Map) {\n                    value.set(k, v);\n                }\n                else if (value instanceof Set) {\n                    value.add(v);\n                }\n                else if (isObject(value)) {\n                    if (v !== undefined || k in value)\n                        value[k] = v;\n                }\n            }\n        }\n    }\n    if (status !== 'not_valid') {\n        for (const failure of struct.refiner(value, ctx)) {\n            failure.explanation = options.message;\n            status = 'not_refined';\n            yield [failure, undefined];\n        }\n    }\n    if (status === 'valid') {\n        yield [undefined, value];\n    }\n}\n\n/**\n * `Struct` objects encapsulate the validation logic for a specific type of\n * values. Once constructed, you use the `assert`, `is` or `validate` helpers to\n * validate unknown input data against the struct.\n */\nclass Struct {\n    constructor(props) {\n        const { type, schema, validator, refiner, coercer = (value) => value, entries = function* () { }, } = props;\n        this.type = type;\n        this.schema = schema;\n        this.entries = entries;\n        this.coercer = coercer;\n        if (validator) {\n            this.validator = (value, context) => {\n                const result = validator(value, context);\n                return toFailures(result, context, this, value);\n            };\n        }\n        else {\n            this.validator = () => [];\n        }\n        if (refiner) {\n            this.refiner = (value, context) => {\n                const result = refiner(value, context);\n                return toFailures(result, context, this, value);\n            };\n        }\n        else {\n            this.refiner = () => [];\n        }\n    }\n    /**\n     * Assert that a value passes the struct's validation, throwing if it doesn't.\n     */\n    assert(value, message) {\n        return assert(value, this, message);\n    }\n    /**\n     * Create a value with the struct's coercion logic, then validate it.\n     */\n    create(value, message) {\n        return create(value, this, message);\n    }\n    /**\n     * Check if a value passes the struct's validation.\n     */\n    is(value) {\n        return is(value, this);\n    }\n    /**\n     * Mask a value, coercing and validating it, but returning only the subset of\n     * properties defined by the struct's schema.\n     */\n    mask(value, message) {\n        return mask(value, this, message);\n    }\n    /**\n     * Validate a value with the struct's validation logic, returning a tuple\n     * representing the result.\n     *\n     * You may optionally pass `true` for the `withCoercion` argument to coerce\n     * the value before attempting to validate it. If you do, the result will\n     * contain the coerced result when successful.\n     */\n    validate(value, options = {}) {\n        return validate(value, this, options);\n    }\n}\n/**\n * Assert that a value passes a struct, throwing if it doesn't.\n */\nfunction assert(value, struct, message) {\n    const result = validate(value, struct, { message });\n    if (result[0]) {\n        throw result[0];\n    }\n}\n/**\n * Create a value with the coercion logic of struct and validate it.\n */\nfunction create(value, struct, message) {\n    const result = validate(value, struct, { coerce: true, message });\n    if (result[0]) {\n        throw result[0];\n    }\n    else {\n        return result[1];\n    }\n}\n/**\n * Mask a value, returning only the subset of properties defined by a struct.\n */\nfunction mask(value, struct, message) {\n    const result = validate(value, struct, { coerce: true, mask: true, message });\n    if (result[0]) {\n        throw result[0];\n    }\n    else {\n        return result[1];\n    }\n}\n/**\n * Check if a value passes a struct.\n */\nfunction is(value, struct) {\n    const result = validate(value, struct);\n    return !result[0];\n}\n/**\n * Validate a value against a struct, returning an error if invalid, or the\n * value (with potential coercion) if valid.\n */\nfunction validate(value, struct, options = {}) {\n    const tuples = run(value, struct, options);\n    const tuple = shiftIterator(tuples);\n    if (tuple[0]) {\n        const error = new StructError(tuple[0], function* () {\n            for (const t of tuples) {\n                if (t[0]) {\n                    yield t[0];\n                }\n            }\n        });\n        return [error, undefined];\n    }\n    else {\n        const v = tuple[1];\n        return [undefined, v];\n    }\n}\n\nfunction assign(...Structs) {\n    const isType = Structs[0].type === 'type';\n    const schemas = Structs.map((s) => s.schema);\n    const schema = Object.assign({}, ...schemas);\n    return isType ? type(schema) : object(schema);\n}\n/**\n * Define a new struct type with a custom validation function.\n */\nfunction define(name, validator) {\n    return new Struct({ type: name, schema: null, validator });\n}\n/**\n * Create a new struct based on an existing struct, but the value is allowed to\n * be `undefined`. `log` will be called if the value is not `undefined`.\n */\nfunction deprecated(struct, log) {\n    return new Struct({\n        ...struct,\n        refiner: (value, ctx) => value === undefined || struct.refiner(value, ctx),\n        validator(value, ctx) {\n            if (value === undefined) {\n                return true;\n            }\n            else {\n                log(value, ctx);\n                return struct.validator(value, ctx);\n            }\n        },\n    });\n}\n/**\n * Create a struct with dynamic validation logic.\n *\n * The callback will receive the value currently being validated, and must\n * return a struct object to validate it with. This can be useful to model\n * validation logic that changes based on its input.\n */\nfunction dynamic(fn) {\n    return new Struct({\n        type: 'dynamic',\n        schema: null,\n        *entries(value, ctx) {\n            const struct = fn(value, ctx);\n            yield* struct.entries(value, ctx);\n        },\n        validator(value, ctx) {\n            const struct = fn(value, ctx);\n            return struct.validator(value, ctx);\n        },\n        coercer(value, ctx) {\n            const struct = fn(value, ctx);\n            return struct.coercer(value, ctx);\n        },\n        refiner(value, ctx) {\n            const struct = fn(value, ctx);\n            return struct.refiner(value, ctx);\n        },\n    });\n}\n/**\n * Create a struct with lazily evaluated validation logic.\n *\n * The first time validation is run with the struct, the callback will be called\n * and must return a struct object to use. This is useful for cases where you\n * want to have self-referential structs for nested data structures to avoid a\n * circular definition problem.\n */\nfunction lazy(fn) {\n    let struct;\n    return new Struct({\n        type: 'lazy',\n        schema: null,\n        *entries(value, ctx) {\n            struct ?? (struct = fn());\n            yield* struct.entries(value, ctx);\n        },\n        validator(value, ctx) {\n            struct ?? (struct = fn());\n            return struct.validator(value, ctx);\n        },\n        coercer(value, ctx) {\n            struct ?? (struct = fn());\n            return struct.coercer(value, ctx);\n        },\n        refiner(value, ctx) {\n            struct ?? (struct = fn());\n            return struct.refiner(value, ctx);\n        },\n    });\n}\n/**\n * Create a new struct based on an existing object struct, but excluding\n * specific properties.\n *\n * Like TypeScript's `Omit` utility.\n */\nfunction omit(struct, keys) {\n    const { schema } = struct;\n    const subschema = { ...schema };\n    for (const key of keys) {\n        delete subschema[key];\n    }\n    switch (struct.type) {\n        case 'type':\n            return type(subschema);\n        default:\n            return object(subschema);\n    }\n}\n/**\n * Create a new struct based on an existing object struct, but with all of its\n * properties allowed to be `undefined`.\n *\n * Like TypeScript's `Partial` utility.\n */\nfunction partial(struct) {\n    const isStruct = struct instanceof Struct;\n    const schema = isStruct ? { ...struct.schema } : { ...struct };\n    for (const key in schema) {\n        schema[key] = optional(schema[key]);\n    }\n    if (isStruct && struct.type === 'type') {\n        return type(schema);\n    }\n    return object(schema);\n}\n/**\n * Create a new struct based on an existing object struct, but only including\n * specific properties.\n *\n * Like TypeScript's `Pick` utility.\n */\nfunction pick(struct, keys) {\n    const { schema } = struct;\n    const subschema = {};\n    for (const key of keys) {\n        subschema[key] = schema[key];\n    }\n    switch (struct.type) {\n        case 'type':\n            return type(subschema);\n        default:\n            return object(subschema);\n    }\n}\n/**\n * Define a new struct type with a custom validation function.\n *\n * @deprecated This function has been renamed to `define`.\n */\nfunction struct(name, validator) {\n    console.warn('superstruct@0.11 - The `struct` helper has been renamed to `define`.');\n    return define(name, validator);\n}\n\n/**\n * Ensure that any value passes validation.\n */\nfunction any() {\n    return define('any', () => true);\n}\nfunction array(Element) {\n    return new Struct({\n        type: 'array',\n        schema: Element,\n        *entries(value) {\n            if (Element && Array.isArray(value)) {\n                for (const [i, v] of value.entries()) {\n                    yield [i, v, Element];\n                }\n            }\n        },\n        coercer(value) {\n            return Array.isArray(value) ? value.slice() : value;\n        },\n        validator(value) {\n            return (Array.isArray(value) ||\n                `Expected an array value, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value is a bigint.\n */\nfunction bigint() {\n    return define('bigint', (value) => {\n        return typeof value === 'bigint';\n    });\n}\n/**\n * Ensure that a value is a boolean.\n */\nfunction boolean() {\n    return define('boolean', (value) => {\n        return typeof value === 'boolean';\n    });\n}\n/**\n * Ensure that a value is a valid `Date`.\n *\n * Note: this also ensures that the value is *not* an invalid `Date` object,\n * which can occur when parsing a date fails but still returns a `Date`.\n */\nfunction date() {\n    return define('date', (value) => {\n        return ((value instanceof Date && !isNaN(value.getTime())) ||\n            `Expected a valid \\`Date\\` object, but received: ${print(value)}`);\n    });\n}\nfunction enums(values) {\n    const schema = {};\n    const description = values.map((v) => print(v)).join();\n    for (const key of values) {\n        schema[key] = key;\n    }\n    return new Struct({\n        type: 'enums',\n        schema,\n        validator(value) {\n            return (values.includes(value) ||\n                `Expected one of \\`${description}\\`, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value is a function.\n */\nfunction func() {\n    return define('func', (value) => {\n        return (typeof value === 'function' ||\n            `Expected a function, but received: ${print(value)}`);\n    });\n}\n/**\n * Ensure that a value is an instance of a specific class.\n */\nfunction instance(Class) {\n    return define('instance', (value) => {\n        return (value instanceof Class ||\n            `Expected a \\`${Class.name}\\` instance, but received: ${print(value)}`);\n    });\n}\n/**\n * Ensure that a value is an integer.\n */\nfunction integer() {\n    return define('integer', (value) => {\n        return ((typeof value === 'number' && !isNaN(value) && Number.isInteger(value)) ||\n            `Expected an integer, but received: ${print(value)}`);\n    });\n}\n/**\n * Ensure that a value matches all of a set of types.\n */\nfunction intersection(Structs) {\n    return new Struct({\n        type: 'intersection',\n        schema: null,\n        *entries(value, ctx) {\n            for (const S of Structs) {\n                yield* S.entries(value, ctx);\n            }\n        },\n        *validator(value, ctx) {\n            for (const S of Structs) {\n                yield* S.validator(value, ctx);\n            }\n        },\n        *refiner(value, ctx) {\n            for (const S of Structs) {\n                yield* S.refiner(value, ctx);\n            }\n        },\n    });\n}\nfunction literal(constant) {\n    const description = print(constant);\n    const t = typeof constant;\n    return new Struct({\n        type: 'literal',\n        schema: t === 'string' || t === 'number' || t === 'boolean' ? constant : null,\n        validator(value) {\n            return (value === constant ||\n                `Expected the literal \\`${description}\\`, but received: ${print(value)}`);\n        },\n    });\n}\nfunction map(Key, Value) {\n    return new Struct({\n        type: 'map',\n        schema: null,\n        *entries(value) {\n            if (Key && Value && value instanceof Map) {\n                for (const [k, v] of value.entries()) {\n                    yield [k, k, Key];\n                    yield [k, v, Value];\n                }\n            }\n        },\n        coercer(value) {\n            return value instanceof Map ? new Map(value) : value;\n        },\n        validator(value) {\n            return (value instanceof Map ||\n                `Expected a \\`Map\\` object, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that no value ever passes validation.\n */\nfunction never() {\n    return define('never', () => false);\n}\n/**\n * Augment an existing struct to allow `null` values.\n */\nfunction nullable(struct) {\n    return new Struct({\n        ...struct,\n        validator: (value, ctx) => value === null || struct.validator(value, ctx),\n        refiner: (value, ctx) => value === null || struct.refiner(value, ctx),\n    });\n}\n/**\n * Ensure that a value is a number.\n */\nfunction number() {\n    return define('number', (value) => {\n        return ((typeof value === 'number' && !isNaN(value)) ||\n            `Expected a number, but received: ${print(value)}`);\n    });\n}\nfunction object(schema) {\n    const knowns = schema ? Object.keys(schema) : [];\n    const Never = never();\n    return new Struct({\n        type: 'object',\n        schema: schema ? schema : null,\n        *entries(value) {\n            if (schema && isObject(value)) {\n                const unknowns = new Set(Object.keys(value));\n                for (const key of knowns) {\n                    unknowns.delete(key);\n                    yield [key, value[key], schema[key]];\n                }\n                for (const key of unknowns) {\n                    yield [key, value[key], Never];\n                }\n            }\n        },\n        validator(value) {\n            return (isObject(value) || `Expected an object, but received: ${print(value)}`);\n        },\n        coercer(value) {\n            return isObject(value) ? { ...value } : value;\n        },\n    });\n}\n/**\n * Augment a struct to allow `undefined` values.\n */\nfunction optional(struct) {\n    return new Struct({\n        ...struct,\n        validator: (value, ctx) => value === undefined || struct.validator(value, ctx),\n        refiner: (value, ctx) => value === undefined || struct.refiner(value, ctx),\n    });\n}\n/**\n * Ensure that a value is an object with keys and values of specific types, but\n * without ensuring any specific shape of properties.\n *\n * Like TypeScript's `Record` utility.\n */\nfunction record(Key, Value) {\n    return new Struct({\n        type: 'record',\n        schema: null,\n        *entries(value) {\n            if (isObject(value)) {\n                for (const k in value) {\n                    const v = value[k];\n                    yield [k, k, Key];\n                    yield [k, v, Value];\n                }\n            }\n        },\n        validator(value) {\n            return (isObject(value) || `Expected an object, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value is a `RegExp`.\n *\n * Note: this does not test the value against the regular expression! For that\n * you need to use the `pattern()` refinement.\n */\nfunction regexp() {\n    return define('regexp', (value) => {\n        return value instanceof RegExp;\n    });\n}\nfunction set(Element) {\n    return new Struct({\n        type: 'set',\n        schema: null,\n        *entries(value) {\n            if (Element && value instanceof Set) {\n                for (const v of value) {\n                    yield [v, v, Element];\n                }\n            }\n        },\n        coercer(value) {\n            return value instanceof Set ? new Set(value) : value;\n        },\n        validator(value) {\n            return (value instanceof Set ||\n                `Expected a \\`Set\\` object, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value is a string.\n */\nfunction string() {\n    return define('string', (value) => {\n        return (typeof value === 'string' ||\n            `Expected a string, but received: ${print(value)}`);\n    });\n}\n/**\n * Ensure that a value is a tuple of a specific length, and that each of its\n * elements is of a specific type.\n */\nfunction tuple(Structs) {\n    const Never = never();\n    return new Struct({\n        type: 'tuple',\n        schema: null,\n        *entries(value) {\n            if (Array.isArray(value)) {\n                const length = Math.max(Structs.length, value.length);\n                for (let i = 0; i < length; i++) {\n                    yield [i, value[i], Structs[i] || Never];\n                }\n            }\n        },\n        validator(value) {\n            return (Array.isArray(value) ||\n                `Expected an array, but received: ${print(value)}`);\n        },\n    });\n}\n/**\n * Ensure that a value has a set of known properties of specific types.\n *\n * Note: Unrecognized properties are allowed and untouched. This is similar to\n * how TypeScript's structural typing works.\n */\nfunction type(schema) {\n    const keys = Object.keys(schema);\n    return new Struct({\n        type: 'type',\n        schema,\n        *entries(value) {\n            if (isObject(value)) {\n                for (const k of keys) {\n                    yield [k, value[k], schema[k]];\n                }\n            }\n        },\n        validator(value) {\n            return (isObject(value) || `Expected an object, but received: ${print(value)}`);\n        },\n        coercer(value) {\n            return isObject(value) ? { ...value } : value;\n        },\n    });\n}\n/**\n * Ensure that a value matches one of a set of types.\n */\nfunction union(Structs) {\n    const description = Structs.map((s) => s.type).join(' | ');\n    return new Struct({\n        type: 'union',\n        schema: null,\n        coercer(value) {\n            for (const S of Structs) {\n                const [error, coerced] = S.validate(value, { coerce: true });\n                if (!error) {\n                    return coerced;\n                }\n            }\n            return value;\n        },\n        validator(value, ctx) {\n            const failures = [];\n            for (const S of Structs) {\n                const [...tuples] = run(value, S, ctx);\n                const [first] = tuples;\n                if (!first[0]) {\n                    return [];\n                }\n                else {\n                    for (const [failure] of tuples) {\n                        if (failure) {\n                            failures.push(failure);\n                        }\n                    }\n                }\n            }\n            return [\n                `Expected the value to satisfy a union of \\`${description}\\`, but received: ${print(value)}`,\n                ...failures,\n            ];\n        },\n    });\n}\n/**\n * Ensure that any value passes validation, without widening its type to `any`.\n */\nfunction unknown() {\n    return define('unknown', () => true);\n}\n\n/**\n * Augment a `Struct` to add an additional coercion step to its input.\n *\n * This allows you to transform input data before validating it, to increase the\n * likelihood that it passes validation—for example for default values, parsing\n * different formats, etc.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\nfunction coerce(struct, condition, coercer) {\n    return new Struct({\n        ...struct,\n        coercer: (value, ctx) => {\n            return is(value, condition)\n                ? struct.coercer(coercer(value, ctx), ctx)\n                : struct.coercer(value, ctx);\n        },\n    });\n}\n/**\n * Augment a struct to replace `undefined` values with a default.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\nfunction defaulted(struct, fallback, options = {}) {\n    return coerce(struct, unknown(), (x) => {\n        const f = typeof fallback === 'function' ? fallback() : fallback;\n        if (x === undefined) {\n            return f;\n        }\n        if (!options.strict && isPlainObject(x) && isPlainObject(f)) {\n            const ret = { ...x };\n            let changed = false;\n            for (const key in f) {\n                if (ret[key] === undefined) {\n                    ret[key] = f[key];\n                    changed = true;\n                }\n            }\n            if (changed) {\n                return ret;\n            }\n        }\n        return x;\n    });\n}\n/**\n * Augment a struct to trim string inputs.\n *\n * Note: You must use `create(value, Struct)` on the value to have the coercion\n * take effect! Using simply `assert()` or `is()` will not use coercion.\n */\nfunction trimmed(struct) {\n    return coerce(struct, string(), (x) => x.trim());\n}\n\n/**\n * Ensure that a string, array, map, or set is empty.\n */\nfunction empty(struct) {\n    return refine(struct, 'empty', (value) => {\n        const size = getSize(value);\n        return (size === 0 ||\n            `Expected an empty ${struct.type} but received one with a size of \\`${size}\\``);\n    });\n}\nfunction getSize(value) {\n    if (value instanceof Map || value instanceof Set) {\n        return value.size;\n    }\n    else {\n        return value.length;\n    }\n}\n/**\n * Ensure that a number or date is below a threshold.\n */\nfunction max(struct, threshold, options = {}) {\n    const { exclusive } = options;\n    return refine(struct, 'max', (value) => {\n        return exclusive\n            ? value < threshold\n            : value <= threshold ||\n                `Expected a ${struct.type} less than ${exclusive ? '' : 'or equal to '}${threshold} but received \\`${value}\\``;\n    });\n}\n/**\n * Ensure that a number or date is above a threshold.\n */\nfunction min(struct, threshold, options = {}) {\n    const { exclusive } = options;\n    return refine(struct, 'min', (value) => {\n        return exclusive\n            ? value > threshold\n            : value >= threshold ||\n                `Expected a ${struct.type} greater than ${exclusive ? '' : 'or equal to '}${threshold} but received \\`${value}\\``;\n    });\n}\n/**\n * Ensure that a string, array, map or set is not empty.\n */\nfunction nonempty(struct) {\n    return refine(struct, 'nonempty', (value) => {\n        const size = getSize(value);\n        return (size > 0 || `Expected a nonempty ${struct.type} but received an empty one`);\n    });\n}\n/**\n * Ensure that a string matches a regular expression.\n */\nfunction pattern(struct, regexp) {\n    return refine(struct, 'pattern', (value) => {\n        return (regexp.test(value) ||\n            `Expected a ${struct.type} matching \\`/${regexp.source}/\\` but received \"${value}\"`);\n    });\n}\n/**\n * Ensure that a string, array, number, date, map, or set has a size (or length, or time) between `min` and `max`.\n */\nfunction size(struct, min, max = min) {\n    const expected = `Expected a ${struct.type}`;\n    const of = min === max ? `of \\`${min}\\`` : `between \\`${min}\\` and \\`${max}\\``;\n    return refine(struct, 'size', (value) => {\n        if (typeof value === 'number' || value instanceof Date) {\n            return ((min <= value && value <= max) ||\n                `${expected} ${of} but received \\`${value}\\``);\n        }\n        else if (value instanceof Map || value instanceof Set) {\n            const { size } = value;\n            return ((min <= size && size <= max) ||\n                `${expected} with a size ${of} but received one with a size of \\`${size}\\``);\n        }\n        else {\n            const { length } = value;\n            return ((min <= length && length <= max) ||\n                `${expected} with a length ${of} but received one with a length of \\`${length}\\``);\n        }\n    });\n}\n/**\n * Augment a `Struct` to add an additional refinement to the validation.\n *\n * The refiner function is guaranteed to receive a value of the struct's type,\n * because the struct's existing validation will already have passed. This\n * allows you to layer additional validation on top of existing structs.\n */\nfunction refine(struct, name, refiner) {\n    return new Struct({\n        ...struct,\n        *refiner(value, ctx) {\n            yield* struct.refiner(value, ctx);\n            const result = refiner(value, ctx);\n            const failures = toFailures(result, ctx, struct, value);\n            for (const failure of failures) {\n                yield { ...failure, refinement: name };\n            }\n        },\n    });\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3VwZXJzdHJ1Y3QvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixnQ0FBZ0M7QUFDaEQsZ0JBQWdCLE9BQU87QUFDdkIsOERBQThELGdCQUFnQixLQUFLLFFBQVE7QUFDM0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtFQUFrRSxNQUFNO0FBQ3hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksY0FBYztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBLFlBQVksZUFBZTtBQUMzQixZQUFZLE9BQU87QUFDbkIsWUFBWSxvREFBb0QsS0FBSyxJQUFJLG1DQUFtQyxXQUFXLFNBQVMsb0JBQW9CLGFBQWEsT0FBTztBQUN4SztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDO0FBQ3pDLFlBQVksNERBQTREO0FBQ3hFLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0Isd0ZBQXdGLElBQUk7QUFDNUc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QyxTQUFTO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsdUJBQXVCO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsbUNBQW1DO0FBQ2hGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QztBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixxQ0FBcUM7QUFDN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxTQUFTO0FBQ3JCLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsbUJBQW1CLElBQUk7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxTQUFTO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsMERBQTBELGFBQWE7QUFDdkUsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtEQUErRCxhQUFhO0FBQzVFLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsWUFBWSxvQkFBb0IsYUFBYTtBQUNsRixTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0RBQWtELGFBQWE7QUFDL0QsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLFdBQVcsNkJBQTZCLGFBQWE7QUFDakYsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0RBQWtELGFBQWE7QUFDL0QsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLFlBQVksb0JBQW9CLGFBQWE7QUFDdkYsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsNERBQTRELGFBQWE7QUFDekUsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsYUFBYTtBQUM3RCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsNEVBQTRFLGFBQWE7QUFDekYsU0FBUztBQUNUO0FBQ0EsdUNBQXVDLFdBQVc7QUFDbEQsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSw0RUFBNEUsYUFBYTtBQUN6RixTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsNERBQTRELGFBQWE7QUFDekUsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdEQUFnRCxhQUFhO0FBQzdELEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxZQUFZO0FBQzVDO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0Esb0RBQW9ELGFBQWE7QUFDakUsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSw0RUFBNEUsYUFBYTtBQUN6RixTQUFTO0FBQ1Q7QUFDQSx1Q0FBdUMsV0FBVztBQUNsRCxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2REFBNkQsY0FBYztBQUMzRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4REFBOEQsWUFBWSxvQkFBb0IsYUFBYTtBQUMzRztBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRDtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLGFBQWEsb0NBQW9DLEtBQUs7QUFDdkYsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QztBQUM1QyxZQUFZLFlBQVk7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsYUFBYSxZQUFZLGdDQUFnQyxFQUFFLFdBQVcsaUJBQWlCLE1BQU07QUFDM0gsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDO0FBQzVDLFlBQVksWUFBWTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixhQUFhLGVBQWUsZ0NBQWdDLEVBQUUsV0FBVyxpQkFBaUIsTUFBTTtBQUM5SCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtREFBbUQsYUFBYTtBQUNoRSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsYUFBYSxjQUFjLGNBQWMsb0JBQW9CLE1BQU07QUFDN0YsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsWUFBWTtBQUMvQyxxQ0FBcUMsSUFBSSxtQkFBbUIsSUFBSSxXQUFXLElBQUk7QUFDL0U7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLFVBQVUsRUFBRSxJQUFJLGlCQUFpQixNQUFNO0FBQzFEO0FBQ0E7QUFDQSxvQkFBb0IsT0FBTztBQUMzQjtBQUNBLG1CQUFtQixVQUFVLGNBQWMsSUFBSSxvQ0FBb0MsS0FBSztBQUN4RjtBQUNBO0FBQ0Esb0JBQW9CLFNBQVM7QUFDN0I7QUFDQSxtQkFBbUIsVUFBVSxnQkFBZ0IsSUFBSSxzQ0FBc0MsT0FBTztBQUM5RjtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QjtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7O0FBRTZaO0FBQzdaIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3N1cGVyc3RydWN0L2Rpc3QvaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQSBgU3RydWN0RmFpbHVyZWAgcmVwcmVzZW50cyBhIHNpbmdsZSBzcGVjaWZpYyBmYWlsdXJlIGluIHZhbGlkYXRpb24uXG4gKi9cbi8qKlxuICogYFN0cnVjdEVycm9yYCBvYmplY3RzIGFyZSB0aHJvd24gKG9yIHJldHVybmVkKSB3aGVuIHZhbGlkYXRpb24gZmFpbHMuXG4gKlxuICogVmFsaWRhdGlvbiBsb2dpYyBpcyBkZXNpZ24gdG8gZXhpdCBlYXJseSBmb3IgbWF4aW11bSBwZXJmb3JtYW5jZS4gVGhlIGVycm9yXG4gKiByZXByZXNlbnRzIHRoZSBmaXJzdCBlcnJvciBlbmNvdW50ZXJlZCBkdXJpbmcgdmFsaWRhdGlvbi4gRm9yIG1vcmUgZGV0YWlsLFxuICogdGhlIGBlcnJvci5mYWlsdXJlc2AgcHJvcGVydHkgaXMgYSBnZW5lcmF0b3IgZnVuY3Rpb24gdGhhdCBjYW4gYmUgcnVuIHRvXG4gKiBjb250aW51ZSB2YWxpZGF0aW9uIGFuZCByZWNlaXZlIGFsbCB0aGUgZmFpbHVyZXMgaW4gdGhlIGRhdGEuXG4gKi9cbmNsYXNzIFN0cnVjdEVycm9yIGV4dGVuZHMgVHlwZUVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcihmYWlsdXJlLCBmYWlsdXJlcykge1xuICAgICAgICBsZXQgY2FjaGVkO1xuICAgICAgICBjb25zdCB7IG1lc3NhZ2UsIGV4cGxhbmF0aW9uLCAuLi5yZXN0IH0gPSBmYWlsdXJlO1xuICAgICAgICBjb25zdCB7IHBhdGggfSA9IGZhaWx1cmU7XG4gICAgICAgIGNvbnN0IG1zZyA9IHBhdGgubGVuZ3RoID09PSAwID8gbWVzc2FnZSA6IGBBdCBwYXRoOiAke3BhdGguam9pbignLicpfSAtLSAke21lc3NhZ2V9YDtcbiAgICAgICAgc3VwZXIoZXhwbGFuYXRpb24gPz8gbXNnKTtcbiAgICAgICAgaWYgKGV4cGxhbmF0aW9uICE9IG51bGwpXG4gICAgICAgICAgICB0aGlzLmNhdXNlID0gbXNnO1xuICAgICAgICBPYmplY3QuYXNzaWduKHRoaXMsIHJlc3QpO1xuICAgICAgICB0aGlzLm5hbWUgPSB0aGlzLmNvbnN0cnVjdG9yLm5hbWU7XG4gICAgICAgIHRoaXMuZmFpbHVyZXMgPSAoKSA9PiB7XG4gICAgICAgICAgICByZXR1cm4gKGNhY2hlZCA/PyAoY2FjaGVkID0gW2ZhaWx1cmUsIC4uLmZhaWx1cmVzKCldKSk7XG4gICAgICAgIH07XG4gICAgfVxufVxuXG4vKipcbiAqIENoZWNrIGlmIGEgdmFsdWUgaXMgYW4gaXRlcmF0b3IuXG4gKi9cbmZ1bmN0aW9uIGlzSXRlcmFibGUoeCkge1xuICAgIHJldHVybiBpc09iamVjdCh4KSAmJiB0eXBlb2YgeFtTeW1ib2wuaXRlcmF0b3JdID09PSAnZnVuY3Rpb24nO1xufVxuLyoqXG4gKiBDaGVjayBpZiBhIHZhbHVlIGlzIGEgcGxhaW4gb2JqZWN0LlxuICovXG5mdW5jdGlvbiBpc09iamVjdCh4KSB7XG4gICAgcmV0dXJuIHR5cGVvZiB4ID09PSAnb2JqZWN0JyAmJiB4ICE9IG51bGw7XG59XG4vKipcbiAqIENoZWNrIGlmIGEgdmFsdWUgaXMgYSBwbGFpbiBvYmplY3QuXG4gKi9cbmZ1bmN0aW9uIGlzUGxhaW5PYmplY3QoeCkge1xuICAgIGlmIChPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwoeCkgIT09ICdbb2JqZWN0IE9iamVjdF0nKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgY29uc3QgcHJvdG90eXBlID0gT2JqZWN0LmdldFByb3RvdHlwZU9mKHgpO1xuICAgIHJldHVybiBwcm90b3R5cGUgPT09IG51bGwgfHwgcHJvdG90eXBlID09PSBPYmplY3QucHJvdG90eXBlO1xufVxuLyoqXG4gKiBSZXR1cm4gYSB2YWx1ZSBhcyBhIHByaW50YWJsZSBzdHJpbmcuXG4gKi9cbmZ1bmN0aW9uIHByaW50KHZhbHVlKSB7XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ3N5bWJvbCcpIHtcbiAgICAgICAgcmV0dXJuIHZhbHVlLnRvU3RyaW5nKCk7XG4gICAgfVxuICAgIHJldHVybiB0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnID8gSlNPTi5zdHJpbmdpZnkodmFsdWUpIDogYCR7dmFsdWV9YDtcbn1cbi8qKlxuICogU2hpZnRzIChyZW1vdmVzIGFuZCByZXR1cm5zKSB0aGUgZmlyc3QgdmFsdWUgZnJvbSB0aGUgYGlucHV0YCBpdGVyYXRvci5cbiAqIExpa2UgYEFycmF5LnByb3RvdHlwZS5zaGlmdCgpYCBidXQgZm9yIGFuIGBJdGVyYXRvcmAuXG4gKi9cbmZ1bmN0aW9uIHNoaWZ0SXRlcmF0b3IoaW5wdXQpIHtcbiAgICBjb25zdCB7IGRvbmUsIHZhbHVlIH0gPSBpbnB1dC5uZXh0KCk7XG4gICAgcmV0dXJuIGRvbmUgPyB1bmRlZmluZWQgOiB2YWx1ZTtcbn1cbi8qKlxuICogQ29udmVydCBhIHNpbmdsZSB2YWxpZGF0aW9uIHJlc3VsdCB0byBhIGZhaWx1cmUuXG4gKi9cbmZ1bmN0aW9uIHRvRmFpbHVyZShyZXN1bHQsIGNvbnRleHQsIHN0cnVjdCwgdmFsdWUpIHtcbiAgICBpZiAocmVzdWx0ID09PSB0cnVlKSB7XG4gICAgICAgIHJldHVybjtcbiAgICB9XG4gICAgZWxzZSBpZiAocmVzdWx0ID09PSBmYWxzZSkge1xuICAgICAgICByZXN1bHQgPSB7fTtcbiAgICB9XG4gICAgZWxzZSBpZiAodHlwZW9mIHJlc3VsdCA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgcmVzdWx0ID0geyBtZXNzYWdlOiByZXN1bHQgfTtcbiAgICB9XG4gICAgY29uc3QgeyBwYXRoLCBicmFuY2ggfSA9IGNvbnRleHQ7XG4gICAgY29uc3QgeyB0eXBlIH0gPSBzdHJ1Y3Q7XG4gICAgY29uc3QgeyByZWZpbmVtZW50LCBtZXNzYWdlID0gYEV4cGVjdGVkIGEgdmFsdWUgb2YgdHlwZSBcXGAke3R5cGV9XFxgJHtyZWZpbmVtZW50ID8gYCB3aXRoIHJlZmluZW1lbnQgXFxgJHtyZWZpbmVtZW50fVxcYGAgOiAnJ30sIGJ1dCByZWNlaXZlZDogXFxgJHtwcmludCh2YWx1ZSl9XFxgYCwgfSA9IHJlc3VsdDtcbiAgICByZXR1cm4ge1xuICAgICAgICB2YWx1ZSxcbiAgICAgICAgdHlwZSxcbiAgICAgICAgcmVmaW5lbWVudCxcbiAgICAgICAga2V5OiBwYXRoW3BhdGgubGVuZ3RoIC0gMV0sXG4gICAgICAgIHBhdGgsXG4gICAgICAgIGJyYW5jaCxcbiAgICAgICAgLi4ucmVzdWx0LFxuICAgICAgICBtZXNzYWdlLFxuICAgIH07XG59XG4vKipcbiAqIENvbnZlcnQgYSB2YWxpZGF0aW9uIHJlc3VsdCB0byBhbiBpdGVyYWJsZSBvZiBmYWlsdXJlcy5cbiAqL1xuZnVuY3Rpb24qIHRvRmFpbHVyZXMocmVzdWx0LCBjb250ZXh0LCBzdHJ1Y3QsIHZhbHVlKSB7XG4gICAgaWYgKCFpc0l0ZXJhYmxlKHJlc3VsdCkpIHtcbiAgICAgICAgcmVzdWx0ID0gW3Jlc3VsdF07XG4gICAgfVxuICAgIGZvciAoY29uc3QgciBvZiByZXN1bHQpIHtcbiAgICAgICAgY29uc3QgZmFpbHVyZSA9IHRvRmFpbHVyZShyLCBjb250ZXh0LCBzdHJ1Y3QsIHZhbHVlKTtcbiAgICAgICAgaWYgKGZhaWx1cmUpIHtcbiAgICAgICAgICAgIHlpZWxkIGZhaWx1cmU7XG4gICAgICAgIH1cbiAgICB9XG59XG4vKipcbiAqIENoZWNrIGEgdmFsdWUgYWdhaW5zdCBhIHN0cnVjdCwgdHJhdmVyc2luZyBkZWVwbHkgaW50byBuZXN0ZWQgdmFsdWVzLCBhbmRcbiAqIHJldHVybmluZyBhbiBpdGVyYXRvciBvZiBmYWlsdXJlcyBvciBzdWNjZXNzLlxuICovXG5mdW5jdGlvbiogcnVuKHZhbHVlLCBzdHJ1Y3QsIG9wdGlvbnMgPSB7fSkge1xuICAgIGNvbnN0IHsgcGF0aCA9IFtdLCBicmFuY2ggPSBbdmFsdWVdLCBjb2VyY2UgPSBmYWxzZSwgbWFzayA9IGZhbHNlIH0gPSBvcHRpb25zO1xuICAgIGNvbnN0IGN0eCA9IHsgcGF0aCwgYnJhbmNoIH07XG4gICAgaWYgKGNvZXJjZSkge1xuICAgICAgICB2YWx1ZSA9IHN0cnVjdC5jb2VyY2VyKHZhbHVlLCBjdHgpO1xuICAgICAgICBpZiAobWFzayAmJlxuICAgICAgICAgICAgc3RydWN0LnR5cGUgIT09ICd0eXBlJyAmJlxuICAgICAgICAgICAgaXNPYmplY3Qoc3RydWN0LnNjaGVtYSkgJiZcbiAgICAgICAgICAgIGlzT2JqZWN0KHZhbHVlKSAmJlxuICAgICAgICAgICAgIUFycmF5LmlzQXJyYXkodmFsdWUpKSB7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiB2YWx1ZSkge1xuICAgICAgICAgICAgICAgIGlmIChzdHJ1Y3Quc2NoZW1hW2tleV0gPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICBkZWxldGUgdmFsdWVba2V5XTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgbGV0IHN0YXR1cyA9ICd2YWxpZCc7XG4gICAgZm9yIChjb25zdCBmYWlsdXJlIG9mIHN0cnVjdC52YWxpZGF0b3IodmFsdWUsIGN0eCkpIHtcbiAgICAgICAgZmFpbHVyZS5leHBsYW5hdGlvbiA9IG9wdGlvbnMubWVzc2FnZTtcbiAgICAgICAgc3RhdHVzID0gJ25vdF92YWxpZCc7XG4gICAgICAgIHlpZWxkIFtmYWlsdXJlLCB1bmRlZmluZWRdO1xuICAgIH1cbiAgICBmb3IgKGxldCBbaywgdiwgc10gb2Ygc3RydWN0LmVudHJpZXModmFsdWUsIGN0eCkpIHtcbiAgICAgICAgY29uc3QgdHMgPSBydW4odiwgcywge1xuICAgICAgICAgICAgcGF0aDogayA9PT0gdW5kZWZpbmVkID8gcGF0aCA6IFsuLi5wYXRoLCBrXSxcbiAgICAgICAgICAgIGJyYW5jaDogayA9PT0gdW5kZWZpbmVkID8gYnJhbmNoIDogWy4uLmJyYW5jaCwgdl0sXG4gICAgICAgICAgICBjb2VyY2UsXG4gICAgICAgICAgICBtYXNrLFxuICAgICAgICAgICAgbWVzc2FnZTogb3B0aW9ucy5tZXNzYWdlLFxuICAgICAgICB9KTtcbiAgICAgICAgZm9yIChjb25zdCB0IG9mIHRzKSB7XG4gICAgICAgICAgICBpZiAodFswXSkge1xuICAgICAgICAgICAgICAgIHN0YXR1cyA9IHRbMF0ucmVmaW5lbWVudCAhPSBudWxsID8gJ25vdF9yZWZpbmVkJyA6ICdub3RfdmFsaWQnO1xuICAgICAgICAgICAgICAgIHlpZWxkIFt0WzBdLCB1bmRlZmluZWRdO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSBpZiAoY29lcmNlKSB7XG4gICAgICAgICAgICAgICAgdiA9IHRbMV07XG4gICAgICAgICAgICAgICAgaWYgKGsgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZSA9IHY7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKHZhbHVlIGluc3RhbmNlb2YgTWFwKSB7XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlLnNldChrLCB2KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAodmFsdWUgaW5zdGFuY2VvZiBTZXQpIHtcbiAgICAgICAgICAgICAgICAgICAgdmFsdWUuYWRkKHYpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmIChpc09iamVjdCh2YWx1ZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHYgIT09IHVuZGVmaW5lZCB8fCBrIGluIHZhbHVlKVxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWVba10gPSB2O1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAoc3RhdHVzICE9PSAnbm90X3ZhbGlkJykge1xuICAgICAgICBmb3IgKGNvbnN0IGZhaWx1cmUgb2Ygc3RydWN0LnJlZmluZXIodmFsdWUsIGN0eCkpIHtcbiAgICAgICAgICAgIGZhaWx1cmUuZXhwbGFuYXRpb24gPSBvcHRpb25zLm1lc3NhZ2U7XG4gICAgICAgICAgICBzdGF0dXMgPSAnbm90X3JlZmluZWQnO1xuICAgICAgICAgICAgeWllbGQgW2ZhaWx1cmUsIHVuZGVmaW5lZF07XG4gICAgICAgIH1cbiAgICB9XG4gICAgaWYgKHN0YXR1cyA9PT0gJ3ZhbGlkJykge1xuICAgICAgICB5aWVsZCBbdW5kZWZpbmVkLCB2YWx1ZV07XG4gICAgfVxufVxuXG4vKipcbiAqIGBTdHJ1Y3RgIG9iamVjdHMgZW5jYXBzdWxhdGUgdGhlIHZhbGlkYXRpb24gbG9naWMgZm9yIGEgc3BlY2lmaWMgdHlwZSBvZlxuICogdmFsdWVzLiBPbmNlIGNvbnN0cnVjdGVkLCB5b3UgdXNlIHRoZSBgYXNzZXJ0YCwgYGlzYCBvciBgdmFsaWRhdGVgIGhlbHBlcnMgdG9cbiAqIHZhbGlkYXRlIHVua25vd24gaW5wdXQgZGF0YSBhZ2FpbnN0IHRoZSBzdHJ1Y3QuXG4gKi9cbmNsYXNzIFN0cnVjdCB7XG4gICAgY29uc3RydWN0b3IocHJvcHMpIHtcbiAgICAgICAgY29uc3QgeyB0eXBlLCBzY2hlbWEsIHZhbGlkYXRvciwgcmVmaW5lciwgY29lcmNlciA9ICh2YWx1ZSkgPT4gdmFsdWUsIGVudHJpZXMgPSBmdW5jdGlvbiogKCkgeyB9LCB9ID0gcHJvcHM7XG4gICAgICAgIHRoaXMudHlwZSA9IHR5cGU7XG4gICAgICAgIHRoaXMuc2NoZW1hID0gc2NoZW1hO1xuICAgICAgICB0aGlzLmVudHJpZXMgPSBlbnRyaWVzO1xuICAgICAgICB0aGlzLmNvZXJjZXIgPSBjb2VyY2VyO1xuICAgICAgICBpZiAodmFsaWRhdG9yKSB7XG4gICAgICAgICAgICB0aGlzLnZhbGlkYXRvciA9ICh2YWx1ZSwgY29udGV4dCkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IHZhbGlkYXRvcih2YWx1ZSwgY29udGV4dCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRvRmFpbHVyZXMocmVzdWx0LCBjb250ZXh0LCB0aGlzLCB2YWx1ZSk7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy52YWxpZGF0b3IgPSAoKSA9PiBbXTtcbiAgICAgICAgfVxuICAgICAgICBpZiAocmVmaW5lcikge1xuICAgICAgICAgICAgdGhpcy5yZWZpbmVyID0gKHZhbHVlLCBjb250ZXh0KSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gcmVmaW5lcih2YWx1ZSwgY29udGV4dCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRvRmFpbHVyZXMocmVzdWx0LCBjb250ZXh0LCB0aGlzLCB2YWx1ZSk7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5yZWZpbmVyID0gKCkgPT4gW107XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogQXNzZXJ0IHRoYXQgYSB2YWx1ZSBwYXNzZXMgdGhlIHN0cnVjdCdzIHZhbGlkYXRpb24sIHRocm93aW5nIGlmIGl0IGRvZXNuJ3QuXG4gICAgICovXG4gICAgYXNzZXJ0KHZhbHVlLCBtZXNzYWdlKSB7XG4gICAgICAgIHJldHVybiBhc3NlcnQodmFsdWUsIHRoaXMsIG1lc3NhZ2UpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDcmVhdGUgYSB2YWx1ZSB3aXRoIHRoZSBzdHJ1Y3QncyBjb2VyY2lvbiBsb2dpYywgdGhlbiB2YWxpZGF0ZSBpdC5cbiAgICAgKi9cbiAgICBjcmVhdGUodmFsdWUsIG1lc3NhZ2UpIHtcbiAgICAgICAgcmV0dXJuIGNyZWF0ZSh2YWx1ZSwgdGhpcywgbWVzc2FnZSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENoZWNrIGlmIGEgdmFsdWUgcGFzc2VzIHRoZSBzdHJ1Y3QncyB2YWxpZGF0aW9uLlxuICAgICAqL1xuICAgIGlzKHZhbHVlKSB7XG4gICAgICAgIHJldHVybiBpcyh2YWx1ZSwgdGhpcyk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIE1hc2sgYSB2YWx1ZSwgY29lcmNpbmcgYW5kIHZhbGlkYXRpbmcgaXQsIGJ1dCByZXR1cm5pbmcgb25seSB0aGUgc3Vic2V0IG9mXG4gICAgICogcHJvcGVydGllcyBkZWZpbmVkIGJ5IHRoZSBzdHJ1Y3QncyBzY2hlbWEuXG4gICAgICovXG4gICAgbWFzayh2YWx1ZSwgbWVzc2FnZSkge1xuICAgICAgICByZXR1cm4gbWFzayh2YWx1ZSwgdGhpcywgbWVzc2FnZSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFZhbGlkYXRlIGEgdmFsdWUgd2l0aCB0aGUgc3RydWN0J3MgdmFsaWRhdGlvbiBsb2dpYywgcmV0dXJuaW5nIGEgdHVwbGVcbiAgICAgKiByZXByZXNlbnRpbmcgdGhlIHJlc3VsdC5cbiAgICAgKlxuICAgICAqIFlvdSBtYXkgb3B0aW9uYWxseSBwYXNzIGB0cnVlYCBmb3IgdGhlIGB3aXRoQ29lcmNpb25gIGFyZ3VtZW50IHRvIGNvZXJjZVxuICAgICAqIHRoZSB2YWx1ZSBiZWZvcmUgYXR0ZW1wdGluZyB0byB2YWxpZGF0ZSBpdC4gSWYgeW91IGRvLCB0aGUgcmVzdWx0IHdpbGxcbiAgICAgKiBjb250YWluIHRoZSBjb2VyY2VkIHJlc3VsdCB3aGVuIHN1Y2Nlc3NmdWwuXG4gICAgICovXG4gICAgdmFsaWRhdGUodmFsdWUsIG9wdGlvbnMgPSB7fSkge1xuICAgICAgICByZXR1cm4gdmFsaWRhdGUodmFsdWUsIHRoaXMsIG9wdGlvbnMpO1xuICAgIH1cbn1cbi8qKlxuICogQXNzZXJ0IHRoYXQgYSB2YWx1ZSBwYXNzZXMgYSBzdHJ1Y3QsIHRocm93aW5nIGlmIGl0IGRvZXNuJ3QuXG4gKi9cbmZ1bmN0aW9uIGFzc2VydCh2YWx1ZSwgc3RydWN0LCBtZXNzYWdlKSB7XG4gICAgY29uc3QgcmVzdWx0ID0gdmFsaWRhdGUodmFsdWUsIHN0cnVjdCwgeyBtZXNzYWdlIH0pO1xuICAgIGlmIChyZXN1bHRbMF0pIHtcbiAgICAgICAgdGhyb3cgcmVzdWx0WzBdO1xuICAgIH1cbn1cbi8qKlxuICogQ3JlYXRlIGEgdmFsdWUgd2l0aCB0aGUgY29lcmNpb24gbG9naWMgb2Ygc3RydWN0IGFuZCB2YWxpZGF0ZSBpdC5cbiAqL1xuZnVuY3Rpb24gY3JlYXRlKHZhbHVlLCBzdHJ1Y3QsIG1lc3NhZ2UpIHtcbiAgICBjb25zdCByZXN1bHQgPSB2YWxpZGF0ZSh2YWx1ZSwgc3RydWN0LCB7IGNvZXJjZTogdHJ1ZSwgbWVzc2FnZSB9KTtcbiAgICBpZiAocmVzdWx0WzBdKSB7XG4gICAgICAgIHRocm93IHJlc3VsdFswXTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiByZXN1bHRbMV07XG4gICAgfVxufVxuLyoqXG4gKiBNYXNrIGEgdmFsdWUsIHJldHVybmluZyBvbmx5IHRoZSBzdWJzZXQgb2YgcHJvcGVydGllcyBkZWZpbmVkIGJ5IGEgc3RydWN0LlxuICovXG5mdW5jdGlvbiBtYXNrKHZhbHVlLCBzdHJ1Y3QsIG1lc3NhZ2UpIHtcbiAgICBjb25zdCByZXN1bHQgPSB2YWxpZGF0ZSh2YWx1ZSwgc3RydWN0LCB7IGNvZXJjZTogdHJ1ZSwgbWFzazogdHJ1ZSwgbWVzc2FnZSB9KTtcbiAgICBpZiAocmVzdWx0WzBdKSB7XG4gICAgICAgIHRocm93IHJlc3VsdFswXTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiByZXN1bHRbMV07XG4gICAgfVxufVxuLyoqXG4gKiBDaGVjayBpZiBhIHZhbHVlIHBhc3NlcyBhIHN0cnVjdC5cbiAqL1xuZnVuY3Rpb24gaXModmFsdWUsIHN0cnVjdCkge1xuICAgIGNvbnN0IHJlc3VsdCA9IHZhbGlkYXRlKHZhbHVlLCBzdHJ1Y3QpO1xuICAgIHJldHVybiAhcmVzdWx0WzBdO1xufVxuLyoqXG4gKiBWYWxpZGF0ZSBhIHZhbHVlIGFnYWluc3QgYSBzdHJ1Y3QsIHJldHVybmluZyBhbiBlcnJvciBpZiBpbnZhbGlkLCBvciB0aGVcbiAqIHZhbHVlICh3aXRoIHBvdGVudGlhbCBjb2VyY2lvbikgaWYgdmFsaWQuXG4gKi9cbmZ1bmN0aW9uIHZhbGlkYXRlKHZhbHVlLCBzdHJ1Y3QsIG9wdGlvbnMgPSB7fSkge1xuICAgIGNvbnN0IHR1cGxlcyA9IHJ1bih2YWx1ZSwgc3RydWN0LCBvcHRpb25zKTtcbiAgICBjb25zdCB0dXBsZSA9IHNoaWZ0SXRlcmF0b3IodHVwbGVzKTtcbiAgICBpZiAodHVwbGVbMF0pIHtcbiAgICAgICAgY29uc3QgZXJyb3IgPSBuZXcgU3RydWN0RXJyb3IodHVwbGVbMF0sIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IHQgb2YgdHVwbGVzKSB7XG4gICAgICAgICAgICAgICAgaWYgKHRbMF0pIHtcbiAgICAgICAgICAgICAgICAgICAgeWllbGQgdFswXTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm4gW2Vycm9yLCB1bmRlZmluZWRdO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgY29uc3QgdiA9IHR1cGxlWzFdO1xuICAgICAgICByZXR1cm4gW3VuZGVmaW5lZCwgdl07XG4gICAgfVxufVxuXG5mdW5jdGlvbiBhc3NpZ24oLi4uU3RydWN0cykge1xuICAgIGNvbnN0IGlzVHlwZSA9IFN0cnVjdHNbMF0udHlwZSA9PT0gJ3R5cGUnO1xuICAgIGNvbnN0IHNjaGVtYXMgPSBTdHJ1Y3RzLm1hcCgocykgPT4gcy5zY2hlbWEpO1xuICAgIGNvbnN0IHNjaGVtYSA9IE9iamVjdC5hc3NpZ24oe30sIC4uLnNjaGVtYXMpO1xuICAgIHJldHVybiBpc1R5cGUgPyB0eXBlKHNjaGVtYSkgOiBvYmplY3Qoc2NoZW1hKTtcbn1cbi8qKlxuICogRGVmaW5lIGEgbmV3IHN0cnVjdCB0eXBlIHdpdGggYSBjdXN0b20gdmFsaWRhdGlvbiBmdW5jdGlvbi5cbiAqL1xuZnVuY3Rpb24gZGVmaW5lKG5hbWUsIHZhbGlkYXRvcikge1xuICAgIHJldHVybiBuZXcgU3RydWN0KHsgdHlwZTogbmFtZSwgc2NoZW1hOiBudWxsLCB2YWxpZGF0b3IgfSk7XG59XG4vKipcbiAqIENyZWF0ZSBhIG5ldyBzdHJ1Y3QgYmFzZWQgb24gYW4gZXhpc3Rpbmcgc3RydWN0LCBidXQgdGhlIHZhbHVlIGlzIGFsbG93ZWQgdG9cbiAqIGJlIGB1bmRlZmluZWRgLiBgbG9nYCB3aWxsIGJlIGNhbGxlZCBpZiB0aGUgdmFsdWUgaXMgbm90IGB1bmRlZmluZWRgLlxuICovXG5mdW5jdGlvbiBkZXByZWNhdGVkKHN0cnVjdCwgbG9nKSB7XG4gICAgcmV0dXJuIG5ldyBTdHJ1Y3Qoe1xuICAgICAgICAuLi5zdHJ1Y3QsXG4gICAgICAgIHJlZmluZXI6ICh2YWx1ZSwgY3R4KSA9PiB2YWx1ZSA9PT0gdW5kZWZpbmVkIHx8IHN0cnVjdC5yZWZpbmVyKHZhbHVlLCBjdHgpLFxuICAgICAgICB2YWxpZGF0b3IodmFsdWUsIGN0eCkge1xuICAgICAgICAgICAgaWYgKHZhbHVlID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGxvZyh2YWx1ZSwgY3R4KTtcbiAgICAgICAgICAgICAgICByZXR1cm4gc3RydWN0LnZhbGlkYXRvcih2YWx1ZSwgY3R4KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICB9KTtcbn1cbi8qKlxuICogQ3JlYXRlIGEgc3RydWN0IHdpdGggZHluYW1pYyB2YWxpZGF0aW9uIGxvZ2ljLlxuICpcbiAqIFRoZSBjYWxsYmFjayB3aWxsIHJlY2VpdmUgdGhlIHZhbHVlIGN1cnJlbnRseSBiZWluZyB2YWxpZGF0ZWQsIGFuZCBtdXN0XG4gKiByZXR1cm4gYSBzdHJ1Y3Qgb2JqZWN0IHRvIHZhbGlkYXRlIGl0IHdpdGguIFRoaXMgY2FuIGJlIHVzZWZ1bCB0byBtb2RlbFxuICogdmFsaWRhdGlvbiBsb2dpYyB0aGF0IGNoYW5nZXMgYmFzZWQgb24gaXRzIGlucHV0LlxuICovXG5mdW5jdGlvbiBkeW5hbWljKGZuKSB7XG4gICAgcmV0dXJuIG5ldyBTdHJ1Y3Qoe1xuICAgICAgICB0eXBlOiAnZHluYW1pYycsXG4gICAgICAgIHNjaGVtYTogbnVsbCxcbiAgICAgICAgKmVudHJpZXModmFsdWUsIGN0eCkge1xuICAgICAgICAgICAgY29uc3Qgc3RydWN0ID0gZm4odmFsdWUsIGN0eCk7XG4gICAgICAgICAgICB5aWVsZCogc3RydWN0LmVudHJpZXModmFsdWUsIGN0eCk7XG4gICAgICAgIH0sXG4gICAgICAgIHZhbGlkYXRvcih2YWx1ZSwgY3R4KSB7XG4gICAgICAgICAgICBjb25zdCBzdHJ1Y3QgPSBmbih2YWx1ZSwgY3R4KTtcbiAgICAgICAgICAgIHJldHVybiBzdHJ1Y3QudmFsaWRhdG9yKHZhbHVlLCBjdHgpO1xuICAgICAgICB9LFxuICAgICAgICBjb2VyY2VyKHZhbHVlLCBjdHgpIHtcbiAgICAgICAgICAgIGNvbnN0IHN0cnVjdCA9IGZuKHZhbHVlLCBjdHgpO1xuICAgICAgICAgICAgcmV0dXJuIHN0cnVjdC5jb2VyY2VyKHZhbHVlLCBjdHgpO1xuICAgICAgICB9LFxuICAgICAgICByZWZpbmVyKHZhbHVlLCBjdHgpIHtcbiAgICAgICAgICAgIGNvbnN0IHN0cnVjdCA9IGZuKHZhbHVlLCBjdHgpO1xuICAgICAgICAgICAgcmV0dXJuIHN0cnVjdC5yZWZpbmVyKHZhbHVlLCBjdHgpO1xuICAgICAgICB9LFxuICAgIH0pO1xufVxuLyoqXG4gKiBDcmVhdGUgYSBzdHJ1Y3Qgd2l0aCBsYXppbHkgZXZhbHVhdGVkIHZhbGlkYXRpb24gbG9naWMuXG4gKlxuICogVGhlIGZpcnN0IHRpbWUgdmFsaWRhdGlvbiBpcyBydW4gd2l0aCB0aGUgc3RydWN0LCB0aGUgY2FsbGJhY2sgd2lsbCBiZSBjYWxsZWRcbiAqIGFuZCBtdXN0IHJldHVybiBhIHN0cnVjdCBvYmplY3QgdG8gdXNlLiBUaGlzIGlzIHVzZWZ1bCBmb3IgY2FzZXMgd2hlcmUgeW91XG4gKiB3YW50IHRvIGhhdmUgc2VsZi1yZWZlcmVudGlhbCBzdHJ1Y3RzIGZvciBuZXN0ZWQgZGF0YSBzdHJ1Y3R1cmVzIHRvIGF2b2lkIGFcbiAqIGNpcmN1bGFyIGRlZmluaXRpb24gcHJvYmxlbS5cbiAqL1xuZnVuY3Rpb24gbGF6eShmbikge1xuICAgIGxldCBzdHJ1Y3Q7XG4gICAgcmV0dXJuIG5ldyBTdHJ1Y3Qoe1xuICAgICAgICB0eXBlOiAnbGF6eScsXG4gICAgICAgIHNjaGVtYTogbnVsbCxcbiAgICAgICAgKmVudHJpZXModmFsdWUsIGN0eCkge1xuICAgICAgICAgICAgc3RydWN0ID8/IChzdHJ1Y3QgPSBmbigpKTtcbiAgICAgICAgICAgIHlpZWxkKiBzdHJ1Y3QuZW50cmllcyh2YWx1ZSwgY3R4KTtcbiAgICAgICAgfSxcbiAgICAgICAgdmFsaWRhdG9yKHZhbHVlLCBjdHgpIHtcbiAgICAgICAgICAgIHN0cnVjdCA/PyAoc3RydWN0ID0gZm4oKSk7XG4gICAgICAgICAgICByZXR1cm4gc3RydWN0LnZhbGlkYXRvcih2YWx1ZSwgY3R4KTtcbiAgICAgICAgfSxcbiAgICAgICAgY29lcmNlcih2YWx1ZSwgY3R4KSB7XG4gICAgICAgICAgICBzdHJ1Y3QgPz8gKHN0cnVjdCA9IGZuKCkpO1xuICAgICAgICAgICAgcmV0dXJuIHN0cnVjdC5jb2VyY2VyKHZhbHVlLCBjdHgpO1xuICAgICAgICB9LFxuICAgICAgICByZWZpbmVyKHZhbHVlLCBjdHgpIHtcbiAgICAgICAgICAgIHN0cnVjdCA/PyAoc3RydWN0ID0gZm4oKSk7XG4gICAgICAgICAgICByZXR1cm4gc3RydWN0LnJlZmluZXIodmFsdWUsIGN0eCk7XG4gICAgICAgIH0sXG4gICAgfSk7XG59XG4vKipcbiAqIENyZWF0ZSBhIG5ldyBzdHJ1Y3QgYmFzZWQgb24gYW4gZXhpc3Rpbmcgb2JqZWN0IHN0cnVjdCwgYnV0IGV4Y2x1ZGluZ1xuICogc3BlY2lmaWMgcHJvcGVydGllcy5cbiAqXG4gKiBMaWtlIFR5cGVTY3JpcHQncyBgT21pdGAgdXRpbGl0eS5cbiAqL1xuZnVuY3Rpb24gb21pdChzdHJ1Y3QsIGtleXMpIHtcbiAgICBjb25zdCB7IHNjaGVtYSB9ID0gc3RydWN0O1xuICAgIGNvbnN0IHN1YnNjaGVtYSA9IHsgLi4uc2NoZW1hIH07XG4gICAgZm9yIChjb25zdCBrZXkgb2Yga2V5cykge1xuICAgICAgICBkZWxldGUgc3Vic2NoZW1hW2tleV07XG4gICAgfVxuICAgIHN3aXRjaCAoc3RydWN0LnR5cGUpIHtcbiAgICAgICAgY2FzZSAndHlwZSc6XG4gICAgICAgICAgICByZXR1cm4gdHlwZShzdWJzY2hlbWEpO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgcmV0dXJuIG9iamVjdChzdWJzY2hlbWEpO1xuICAgIH1cbn1cbi8qKlxuICogQ3JlYXRlIGEgbmV3IHN0cnVjdCBiYXNlZCBvbiBhbiBleGlzdGluZyBvYmplY3Qgc3RydWN0LCBidXQgd2l0aCBhbGwgb2YgaXRzXG4gKiBwcm9wZXJ0aWVzIGFsbG93ZWQgdG8gYmUgYHVuZGVmaW5lZGAuXG4gKlxuICogTGlrZSBUeXBlU2NyaXB0J3MgYFBhcnRpYWxgIHV0aWxpdHkuXG4gKi9cbmZ1bmN0aW9uIHBhcnRpYWwoc3RydWN0KSB7XG4gICAgY29uc3QgaXNTdHJ1Y3QgPSBzdHJ1Y3QgaW5zdGFuY2VvZiBTdHJ1Y3Q7XG4gICAgY29uc3Qgc2NoZW1hID0gaXNTdHJ1Y3QgPyB7IC4uLnN0cnVjdC5zY2hlbWEgfSA6IHsgLi4uc3RydWN0IH07XG4gICAgZm9yIChjb25zdCBrZXkgaW4gc2NoZW1hKSB7XG4gICAgICAgIHNjaGVtYVtrZXldID0gb3B0aW9uYWwoc2NoZW1hW2tleV0pO1xuICAgIH1cbiAgICBpZiAoaXNTdHJ1Y3QgJiYgc3RydWN0LnR5cGUgPT09ICd0eXBlJykge1xuICAgICAgICByZXR1cm4gdHlwZShzY2hlbWEpO1xuICAgIH1cbiAgICByZXR1cm4gb2JqZWN0KHNjaGVtYSk7XG59XG4vKipcbiAqIENyZWF0ZSBhIG5ldyBzdHJ1Y3QgYmFzZWQgb24gYW4gZXhpc3Rpbmcgb2JqZWN0IHN0cnVjdCwgYnV0IG9ubHkgaW5jbHVkaW5nXG4gKiBzcGVjaWZpYyBwcm9wZXJ0aWVzLlxuICpcbiAqIExpa2UgVHlwZVNjcmlwdCdzIGBQaWNrYCB1dGlsaXR5LlxuICovXG5mdW5jdGlvbiBwaWNrKHN0cnVjdCwga2V5cykge1xuICAgIGNvbnN0IHsgc2NoZW1hIH0gPSBzdHJ1Y3Q7XG4gICAgY29uc3Qgc3Vic2NoZW1hID0ge307XG4gICAgZm9yIChjb25zdCBrZXkgb2Yga2V5cykge1xuICAgICAgICBzdWJzY2hlbWFba2V5XSA9IHNjaGVtYVtrZXldO1xuICAgIH1cbiAgICBzd2l0Y2ggKHN0cnVjdC50eXBlKSB7XG4gICAgICAgIGNhc2UgJ3R5cGUnOlxuICAgICAgICAgICAgcmV0dXJuIHR5cGUoc3Vic2NoZW1hKTtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHJldHVybiBvYmplY3Qoc3Vic2NoZW1hKTtcbiAgICB9XG59XG4vKipcbiAqIERlZmluZSBhIG5ldyBzdHJ1Y3QgdHlwZSB3aXRoIGEgY3VzdG9tIHZhbGlkYXRpb24gZnVuY3Rpb24uXG4gKlxuICogQGRlcHJlY2F0ZWQgVGhpcyBmdW5jdGlvbiBoYXMgYmVlbiByZW5hbWVkIHRvIGBkZWZpbmVgLlxuICovXG5mdW5jdGlvbiBzdHJ1Y3QobmFtZSwgdmFsaWRhdG9yKSB7XG4gICAgY29uc29sZS53YXJuKCdzdXBlcnN0cnVjdEAwLjExIC0gVGhlIGBzdHJ1Y3RgIGhlbHBlciBoYXMgYmVlbiByZW5hbWVkIHRvIGBkZWZpbmVgLicpO1xuICAgIHJldHVybiBkZWZpbmUobmFtZSwgdmFsaWRhdG9yKTtcbn1cblxuLyoqXG4gKiBFbnN1cmUgdGhhdCBhbnkgdmFsdWUgcGFzc2VzIHZhbGlkYXRpb24uXG4gKi9cbmZ1bmN0aW9uIGFueSgpIHtcbiAgICByZXR1cm4gZGVmaW5lKCdhbnknLCAoKSA9PiB0cnVlKTtcbn1cbmZ1bmN0aW9uIGFycmF5KEVsZW1lbnQpIHtcbiAgICByZXR1cm4gbmV3IFN0cnVjdCh7XG4gICAgICAgIHR5cGU6ICdhcnJheScsXG4gICAgICAgIHNjaGVtYTogRWxlbWVudCxcbiAgICAgICAgKmVudHJpZXModmFsdWUpIHtcbiAgICAgICAgICAgIGlmIChFbGVtZW50ICYmIEFycmF5LmlzQXJyYXkodmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCBbaSwgdl0gb2YgdmFsdWUuZW50cmllcygpKSB7XG4gICAgICAgICAgICAgICAgICAgIHlpZWxkIFtpLCB2LCBFbGVtZW50XTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIGNvZXJjZXIodmFsdWUpIHtcbiAgICAgICAgICAgIHJldHVybiBBcnJheS5pc0FycmF5KHZhbHVlKSA/IHZhbHVlLnNsaWNlKCkgOiB2YWx1ZTtcbiAgICAgICAgfSxcbiAgICAgICAgdmFsaWRhdG9yKHZhbHVlKSB7XG4gICAgICAgICAgICByZXR1cm4gKEFycmF5LmlzQXJyYXkodmFsdWUpIHx8XG4gICAgICAgICAgICAgICAgYEV4cGVjdGVkIGFuIGFycmF5IHZhbHVlLCBidXQgcmVjZWl2ZWQ6ICR7cHJpbnQodmFsdWUpfWApO1xuICAgICAgICB9LFxuICAgIH0pO1xufVxuLyoqXG4gKiBFbnN1cmUgdGhhdCBhIHZhbHVlIGlzIGEgYmlnaW50LlxuICovXG5mdW5jdGlvbiBiaWdpbnQoKSB7XG4gICAgcmV0dXJuIGRlZmluZSgnYmlnaW50JywgKHZhbHVlKSA9PiB7XG4gICAgICAgIHJldHVybiB0eXBlb2YgdmFsdWUgPT09ICdiaWdpbnQnO1xuICAgIH0pO1xufVxuLyoqXG4gKiBFbnN1cmUgdGhhdCBhIHZhbHVlIGlzIGEgYm9vbGVhbi5cbiAqL1xuZnVuY3Rpb24gYm9vbGVhbigpIHtcbiAgICByZXR1cm4gZGVmaW5lKCdib29sZWFuJywgKHZhbHVlKSA9PiB7XG4gICAgICAgIHJldHVybiB0eXBlb2YgdmFsdWUgPT09ICdib29sZWFuJztcbiAgICB9KTtcbn1cbi8qKlxuICogRW5zdXJlIHRoYXQgYSB2YWx1ZSBpcyBhIHZhbGlkIGBEYXRlYC5cbiAqXG4gKiBOb3RlOiB0aGlzIGFsc28gZW5zdXJlcyB0aGF0IHRoZSB2YWx1ZSBpcyAqbm90KiBhbiBpbnZhbGlkIGBEYXRlYCBvYmplY3QsXG4gKiB3aGljaCBjYW4gb2NjdXIgd2hlbiBwYXJzaW5nIGEgZGF0ZSBmYWlscyBidXQgc3RpbGwgcmV0dXJucyBhIGBEYXRlYC5cbiAqL1xuZnVuY3Rpb24gZGF0ZSgpIHtcbiAgICByZXR1cm4gZGVmaW5lKCdkYXRlJywgKHZhbHVlKSA9PiB7XG4gICAgICAgIHJldHVybiAoKHZhbHVlIGluc3RhbmNlb2YgRGF0ZSAmJiAhaXNOYU4odmFsdWUuZ2V0VGltZSgpKSkgfHxcbiAgICAgICAgICAgIGBFeHBlY3RlZCBhIHZhbGlkIFxcYERhdGVcXGAgb2JqZWN0LCBidXQgcmVjZWl2ZWQ6ICR7cHJpbnQodmFsdWUpfWApO1xuICAgIH0pO1xufVxuZnVuY3Rpb24gZW51bXModmFsdWVzKSB7XG4gICAgY29uc3Qgc2NoZW1hID0ge307XG4gICAgY29uc3QgZGVzY3JpcHRpb24gPSB2YWx1ZXMubWFwKCh2KSA9PiBwcmludCh2KSkuam9pbigpO1xuICAgIGZvciAoY29uc3Qga2V5IG9mIHZhbHVlcykge1xuICAgICAgICBzY2hlbWFba2V5XSA9IGtleTtcbiAgICB9XG4gICAgcmV0dXJuIG5ldyBTdHJ1Y3Qoe1xuICAgICAgICB0eXBlOiAnZW51bXMnLFxuICAgICAgICBzY2hlbWEsXG4gICAgICAgIHZhbGlkYXRvcih2YWx1ZSkge1xuICAgICAgICAgICAgcmV0dXJuICh2YWx1ZXMuaW5jbHVkZXModmFsdWUpIHx8XG4gICAgICAgICAgICAgICAgYEV4cGVjdGVkIG9uZSBvZiBcXGAke2Rlc2NyaXB0aW9ufVxcYCwgYnV0IHJlY2VpdmVkOiAke3ByaW50KHZhbHVlKX1gKTtcbiAgICAgICAgfSxcbiAgICB9KTtcbn1cbi8qKlxuICogRW5zdXJlIHRoYXQgYSB2YWx1ZSBpcyBhIGZ1bmN0aW9uLlxuICovXG5mdW5jdGlvbiBmdW5jKCkge1xuICAgIHJldHVybiBkZWZpbmUoJ2Z1bmMnLCAodmFsdWUpID0+IHtcbiAgICAgICAgcmV0dXJuICh0eXBlb2YgdmFsdWUgPT09ICdmdW5jdGlvbicgfHxcbiAgICAgICAgICAgIGBFeHBlY3RlZCBhIGZ1bmN0aW9uLCBidXQgcmVjZWl2ZWQ6ICR7cHJpbnQodmFsdWUpfWApO1xuICAgIH0pO1xufVxuLyoqXG4gKiBFbnN1cmUgdGhhdCBhIHZhbHVlIGlzIGFuIGluc3RhbmNlIG9mIGEgc3BlY2lmaWMgY2xhc3MuXG4gKi9cbmZ1bmN0aW9uIGluc3RhbmNlKENsYXNzKSB7XG4gICAgcmV0dXJuIGRlZmluZSgnaW5zdGFuY2UnLCAodmFsdWUpID0+IHtcbiAgICAgICAgcmV0dXJuICh2YWx1ZSBpbnN0YW5jZW9mIENsYXNzIHx8XG4gICAgICAgICAgICBgRXhwZWN0ZWQgYSBcXGAke0NsYXNzLm5hbWV9XFxgIGluc3RhbmNlLCBidXQgcmVjZWl2ZWQ6ICR7cHJpbnQodmFsdWUpfWApO1xuICAgIH0pO1xufVxuLyoqXG4gKiBFbnN1cmUgdGhhdCBhIHZhbHVlIGlzIGFuIGludGVnZXIuXG4gKi9cbmZ1bmN0aW9uIGludGVnZXIoKSB7XG4gICAgcmV0dXJuIGRlZmluZSgnaW50ZWdlcicsICh2YWx1ZSkgPT4ge1xuICAgICAgICByZXR1cm4gKCh0eXBlb2YgdmFsdWUgPT09ICdudW1iZXInICYmICFpc05hTih2YWx1ZSkgJiYgTnVtYmVyLmlzSW50ZWdlcih2YWx1ZSkpIHx8XG4gICAgICAgICAgICBgRXhwZWN0ZWQgYW4gaW50ZWdlciwgYnV0IHJlY2VpdmVkOiAke3ByaW50KHZhbHVlKX1gKTtcbiAgICB9KTtcbn1cbi8qKlxuICogRW5zdXJlIHRoYXQgYSB2YWx1ZSBtYXRjaGVzIGFsbCBvZiBhIHNldCBvZiB0eXBlcy5cbiAqL1xuZnVuY3Rpb24gaW50ZXJzZWN0aW9uKFN0cnVjdHMpIHtcbiAgICByZXR1cm4gbmV3IFN0cnVjdCh7XG4gICAgICAgIHR5cGU6ICdpbnRlcnNlY3Rpb24nLFxuICAgICAgICBzY2hlbWE6IG51bGwsXG4gICAgICAgICplbnRyaWVzKHZhbHVlLCBjdHgpIHtcbiAgICAgICAgICAgIGZvciAoY29uc3QgUyBvZiBTdHJ1Y3RzKSB7XG4gICAgICAgICAgICAgICAgeWllbGQqIFMuZW50cmllcyh2YWx1ZSwgY3R4KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgKnZhbGlkYXRvcih2YWx1ZSwgY3R4KSB7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IFMgb2YgU3RydWN0cykge1xuICAgICAgICAgICAgICAgIHlpZWxkKiBTLnZhbGlkYXRvcih2YWx1ZSwgY3R4KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgKnJlZmluZXIodmFsdWUsIGN0eCkge1xuICAgICAgICAgICAgZm9yIChjb25zdCBTIG9mIFN0cnVjdHMpIHtcbiAgICAgICAgICAgICAgICB5aWVsZCogUy5yZWZpbmVyKHZhbHVlLCBjdHgpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgIH0pO1xufVxuZnVuY3Rpb24gbGl0ZXJhbChjb25zdGFudCkge1xuICAgIGNvbnN0IGRlc2NyaXB0aW9uID0gcHJpbnQoY29uc3RhbnQpO1xuICAgIGNvbnN0IHQgPSB0eXBlb2YgY29uc3RhbnQ7XG4gICAgcmV0dXJuIG5ldyBTdHJ1Y3Qoe1xuICAgICAgICB0eXBlOiAnbGl0ZXJhbCcsXG4gICAgICAgIHNjaGVtYTogdCA9PT0gJ3N0cmluZycgfHwgdCA9PT0gJ251bWJlcicgfHwgdCA9PT0gJ2Jvb2xlYW4nID8gY29uc3RhbnQgOiBudWxsLFxuICAgICAgICB2YWxpZGF0b3IodmFsdWUpIHtcbiAgICAgICAgICAgIHJldHVybiAodmFsdWUgPT09IGNvbnN0YW50IHx8XG4gICAgICAgICAgICAgICAgYEV4cGVjdGVkIHRoZSBsaXRlcmFsIFxcYCR7ZGVzY3JpcHRpb259XFxgLCBidXQgcmVjZWl2ZWQ6ICR7cHJpbnQodmFsdWUpfWApO1xuICAgICAgICB9LFxuICAgIH0pO1xufVxuZnVuY3Rpb24gbWFwKEtleSwgVmFsdWUpIHtcbiAgICByZXR1cm4gbmV3IFN0cnVjdCh7XG4gICAgICAgIHR5cGU6ICdtYXAnLFxuICAgICAgICBzY2hlbWE6IG51bGwsXG4gICAgICAgICplbnRyaWVzKHZhbHVlKSB7XG4gICAgICAgICAgICBpZiAoS2V5ICYmIFZhbHVlICYmIHZhbHVlIGluc3RhbmNlb2YgTWFwKSB7XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCBbaywgdl0gb2YgdmFsdWUuZW50cmllcygpKSB7XG4gICAgICAgICAgICAgICAgICAgIHlpZWxkIFtrLCBrLCBLZXldO1xuICAgICAgICAgICAgICAgICAgICB5aWVsZCBbaywgdiwgVmFsdWVdO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgY29lcmNlcih2YWx1ZSkge1xuICAgICAgICAgICAgcmV0dXJuIHZhbHVlIGluc3RhbmNlb2YgTWFwID8gbmV3IE1hcCh2YWx1ZSkgOiB2YWx1ZTtcbiAgICAgICAgfSxcbiAgICAgICAgdmFsaWRhdG9yKHZhbHVlKSB7XG4gICAgICAgICAgICByZXR1cm4gKHZhbHVlIGluc3RhbmNlb2YgTWFwIHx8XG4gICAgICAgICAgICAgICAgYEV4cGVjdGVkIGEgXFxgTWFwXFxgIG9iamVjdCwgYnV0IHJlY2VpdmVkOiAke3ByaW50KHZhbHVlKX1gKTtcbiAgICAgICAgfSxcbiAgICB9KTtcbn1cbi8qKlxuICogRW5zdXJlIHRoYXQgbm8gdmFsdWUgZXZlciBwYXNzZXMgdmFsaWRhdGlvbi5cbiAqL1xuZnVuY3Rpb24gbmV2ZXIoKSB7XG4gICAgcmV0dXJuIGRlZmluZSgnbmV2ZXInLCAoKSA9PiBmYWxzZSk7XG59XG4vKipcbiAqIEF1Z21lbnQgYW4gZXhpc3Rpbmcgc3RydWN0IHRvIGFsbG93IGBudWxsYCB2YWx1ZXMuXG4gKi9cbmZ1bmN0aW9uIG51bGxhYmxlKHN0cnVjdCkge1xuICAgIHJldHVybiBuZXcgU3RydWN0KHtcbiAgICAgICAgLi4uc3RydWN0LFxuICAgICAgICB2YWxpZGF0b3I6ICh2YWx1ZSwgY3R4KSA9PiB2YWx1ZSA9PT0gbnVsbCB8fCBzdHJ1Y3QudmFsaWRhdG9yKHZhbHVlLCBjdHgpLFxuICAgICAgICByZWZpbmVyOiAodmFsdWUsIGN0eCkgPT4gdmFsdWUgPT09IG51bGwgfHwgc3RydWN0LnJlZmluZXIodmFsdWUsIGN0eCksXG4gICAgfSk7XG59XG4vKipcbiAqIEVuc3VyZSB0aGF0IGEgdmFsdWUgaXMgYSBudW1iZXIuXG4gKi9cbmZ1bmN0aW9uIG51bWJlcigpIHtcbiAgICByZXR1cm4gZGVmaW5lKCdudW1iZXInLCAodmFsdWUpID0+IHtcbiAgICAgICAgcmV0dXJuICgodHlwZW9mIHZhbHVlID09PSAnbnVtYmVyJyAmJiAhaXNOYU4odmFsdWUpKSB8fFxuICAgICAgICAgICAgYEV4cGVjdGVkIGEgbnVtYmVyLCBidXQgcmVjZWl2ZWQ6ICR7cHJpbnQodmFsdWUpfWApO1xuICAgIH0pO1xufVxuZnVuY3Rpb24gb2JqZWN0KHNjaGVtYSkge1xuICAgIGNvbnN0IGtub3ducyA9IHNjaGVtYSA/IE9iamVjdC5rZXlzKHNjaGVtYSkgOiBbXTtcbiAgICBjb25zdCBOZXZlciA9IG5ldmVyKCk7XG4gICAgcmV0dXJuIG5ldyBTdHJ1Y3Qoe1xuICAgICAgICB0eXBlOiAnb2JqZWN0JyxcbiAgICAgICAgc2NoZW1hOiBzY2hlbWEgPyBzY2hlbWEgOiBudWxsLFxuICAgICAgICAqZW50cmllcyh2YWx1ZSkge1xuICAgICAgICAgICAgaWYgKHNjaGVtYSAmJiBpc09iamVjdCh2YWx1ZSkpIHtcbiAgICAgICAgICAgICAgICBjb25zdCB1bmtub3ducyA9IG5ldyBTZXQoT2JqZWN0LmtleXModmFsdWUpKTtcbiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGtleSBvZiBrbm93bnMpIHtcbiAgICAgICAgICAgICAgICAgICAgdW5rbm93bnMuZGVsZXRlKGtleSk7XG4gICAgICAgICAgICAgICAgICAgIHlpZWxkIFtrZXksIHZhbHVlW2tleV0sIHNjaGVtYVtrZXldXTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCBrZXkgb2YgdW5rbm93bnMpIHtcbiAgICAgICAgICAgICAgICAgICAgeWllbGQgW2tleSwgdmFsdWVba2V5XSwgTmV2ZXJdO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgdmFsaWRhdG9yKHZhbHVlKSB7XG4gICAgICAgICAgICByZXR1cm4gKGlzT2JqZWN0KHZhbHVlKSB8fCBgRXhwZWN0ZWQgYW4gb2JqZWN0LCBidXQgcmVjZWl2ZWQ6ICR7cHJpbnQodmFsdWUpfWApO1xuICAgICAgICB9LFxuICAgICAgICBjb2VyY2VyKHZhbHVlKSB7XG4gICAgICAgICAgICByZXR1cm4gaXNPYmplY3QodmFsdWUpID8geyAuLi52YWx1ZSB9IDogdmFsdWU7XG4gICAgICAgIH0sXG4gICAgfSk7XG59XG4vKipcbiAqIEF1Z21lbnQgYSBzdHJ1Y3QgdG8gYWxsb3cgYHVuZGVmaW5lZGAgdmFsdWVzLlxuICovXG5mdW5jdGlvbiBvcHRpb25hbChzdHJ1Y3QpIHtcbiAgICByZXR1cm4gbmV3IFN0cnVjdCh7XG4gICAgICAgIC4uLnN0cnVjdCxcbiAgICAgICAgdmFsaWRhdG9yOiAodmFsdWUsIGN0eCkgPT4gdmFsdWUgPT09IHVuZGVmaW5lZCB8fCBzdHJ1Y3QudmFsaWRhdG9yKHZhbHVlLCBjdHgpLFxuICAgICAgICByZWZpbmVyOiAodmFsdWUsIGN0eCkgPT4gdmFsdWUgPT09IHVuZGVmaW5lZCB8fCBzdHJ1Y3QucmVmaW5lcih2YWx1ZSwgY3R4KSxcbiAgICB9KTtcbn1cbi8qKlxuICogRW5zdXJlIHRoYXQgYSB2YWx1ZSBpcyBhbiBvYmplY3Qgd2l0aCBrZXlzIGFuZCB2YWx1ZXMgb2Ygc3BlY2lmaWMgdHlwZXMsIGJ1dFxuICogd2l0aG91dCBlbnN1cmluZyBhbnkgc3BlY2lmaWMgc2hhcGUgb2YgcHJvcGVydGllcy5cbiAqXG4gKiBMaWtlIFR5cGVTY3JpcHQncyBgUmVjb3JkYCB1dGlsaXR5LlxuICovXG5mdW5jdGlvbiByZWNvcmQoS2V5LCBWYWx1ZSkge1xuICAgIHJldHVybiBuZXcgU3RydWN0KHtcbiAgICAgICAgdHlwZTogJ3JlY29yZCcsXG4gICAgICAgIHNjaGVtYTogbnVsbCxcbiAgICAgICAgKmVudHJpZXModmFsdWUpIHtcbiAgICAgICAgICAgIGlmIChpc09iamVjdCh2YWx1ZSkpIHtcbiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGsgaW4gdmFsdWUpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdiA9IHZhbHVlW2tdO1xuICAgICAgICAgICAgICAgICAgICB5aWVsZCBbaywgaywgS2V5XTtcbiAgICAgICAgICAgICAgICAgICAgeWllbGQgW2ssIHYsIFZhbHVlXTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIHZhbGlkYXRvcih2YWx1ZSkge1xuICAgICAgICAgICAgcmV0dXJuIChpc09iamVjdCh2YWx1ZSkgfHwgYEV4cGVjdGVkIGFuIG9iamVjdCwgYnV0IHJlY2VpdmVkOiAke3ByaW50KHZhbHVlKX1gKTtcbiAgICAgICAgfSxcbiAgICB9KTtcbn1cbi8qKlxuICogRW5zdXJlIHRoYXQgYSB2YWx1ZSBpcyBhIGBSZWdFeHBgLlxuICpcbiAqIE5vdGU6IHRoaXMgZG9lcyBub3QgdGVzdCB0aGUgdmFsdWUgYWdhaW5zdCB0aGUgcmVndWxhciBleHByZXNzaW9uISBGb3IgdGhhdFxuICogeW91IG5lZWQgdG8gdXNlIHRoZSBgcGF0dGVybigpYCByZWZpbmVtZW50LlxuICovXG5mdW5jdGlvbiByZWdleHAoKSB7XG4gICAgcmV0dXJuIGRlZmluZSgncmVnZXhwJywgKHZhbHVlKSA9PiB7XG4gICAgICAgIHJldHVybiB2YWx1ZSBpbnN0YW5jZW9mIFJlZ0V4cDtcbiAgICB9KTtcbn1cbmZ1bmN0aW9uIHNldChFbGVtZW50KSB7XG4gICAgcmV0dXJuIG5ldyBTdHJ1Y3Qoe1xuICAgICAgICB0eXBlOiAnc2V0JyxcbiAgICAgICAgc2NoZW1hOiBudWxsLFxuICAgICAgICAqZW50cmllcyh2YWx1ZSkge1xuICAgICAgICAgICAgaWYgKEVsZW1lbnQgJiYgdmFsdWUgaW5zdGFuY2VvZiBTZXQpIHtcbiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IHYgb2YgdmFsdWUpIHtcbiAgICAgICAgICAgICAgICAgICAgeWllbGQgW3YsIHYsIEVsZW1lbnRdO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgY29lcmNlcih2YWx1ZSkge1xuICAgICAgICAgICAgcmV0dXJuIHZhbHVlIGluc3RhbmNlb2YgU2V0ID8gbmV3IFNldCh2YWx1ZSkgOiB2YWx1ZTtcbiAgICAgICAgfSxcbiAgICAgICAgdmFsaWRhdG9yKHZhbHVlKSB7XG4gICAgICAgICAgICByZXR1cm4gKHZhbHVlIGluc3RhbmNlb2YgU2V0IHx8XG4gICAgICAgICAgICAgICAgYEV4cGVjdGVkIGEgXFxgU2V0XFxgIG9iamVjdCwgYnV0IHJlY2VpdmVkOiAke3ByaW50KHZhbHVlKX1gKTtcbiAgICAgICAgfSxcbiAgICB9KTtcbn1cbi8qKlxuICogRW5zdXJlIHRoYXQgYSB2YWx1ZSBpcyBhIHN0cmluZy5cbiAqL1xuZnVuY3Rpb24gc3RyaW5nKCkge1xuICAgIHJldHVybiBkZWZpbmUoJ3N0cmluZycsICh2YWx1ZSkgPT4ge1xuICAgICAgICByZXR1cm4gKHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZycgfHxcbiAgICAgICAgICAgIGBFeHBlY3RlZCBhIHN0cmluZywgYnV0IHJlY2VpdmVkOiAke3ByaW50KHZhbHVlKX1gKTtcbiAgICB9KTtcbn1cbi8qKlxuICogRW5zdXJlIHRoYXQgYSB2YWx1ZSBpcyBhIHR1cGxlIG9mIGEgc3BlY2lmaWMgbGVuZ3RoLCBhbmQgdGhhdCBlYWNoIG9mIGl0c1xuICogZWxlbWVudHMgaXMgb2YgYSBzcGVjaWZpYyB0eXBlLlxuICovXG5mdW5jdGlvbiB0dXBsZShTdHJ1Y3RzKSB7XG4gICAgY29uc3QgTmV2ZXIgPSBuZXZlcigpO1xuICAgIHJldHVybiBuZXcgU3RydWN0KHtcbiAgICAgICAgdHlwZTogJ3R1cGxlJyxcbiAgICAgICAgc2NoZW1hOiBudWxsLFxuICAgICAgICAqZW50cmllcyh2YWx1ZSkge1xuICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgbGVuZ3RoID0gTWF0aC5tYXgoU3RydWN0cy5sZW5ndGgsIHZhbHVlLmxlbmd0aCk7XG4gICAgICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgICAgICAgICB5aWVsZCBbaSwgdmFsdWVbaV0sIFN0cnVjdHNbaV0gfHwgTmV2ZXJdO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgdmFsaWRhdG9yKHZhbHVlKSB7XG4gICAgICAgICAgICByZXR1cm4gKEFycmF5LmlzQXJyYXkodmFsdWUpIHx8XG4gICAgICAgICAgICAgICAgYEV4cGVjdGVkIGFuIGFycmF5LCBidXQgcmVjZWl2ZWQ6ICR7cHJpbnQodmFsdWUpfWApO1xuICAgICAgICB9LFxuICAgIH0pO1xufVxuLyoqXG4gKiBFbnN1cmUgdGhhdCBhIHZhbHVlIGhhcyBhIHNldCBvZiBrbm93biBwcm9wZXJ0aWVzIG9mIHNwZWNpZmljIHR5cGVzLlxuICpcbiAqIE5vdGU6IFVucmVjb2duaXplZCBwcm9wZXJ0aWVzIGFyZSBhbGxvd2VkIGFuZCB1bnRvdWNoZWQuIFRoaXMgaXMgc2ltaWxhciB0b1xuICogaG93IFR5cGVTY3JpcHQncyBzdHJ1Y3R1cmFsIHR5cGluZyB3b3Jrcy5cbiAqL1xuZnVuY3Rpb24gdHlwZShzY2hlbWEpIHtcbiAgICBjb25zdCBrZXlzID0gT2JqZWN0LmtleXMoc2NoZW1hKTtcbiAgICByZXR1cm4gbmV3IFN0cnVjdCh7XG4gICAgICAgIHR5cGU6ICd0eXBlJyxcbiAgICAgICAgc2NoZW1hLFxuICAgICAgICAqZW50cmllcyh2YWx1ZSkge1xuICAgICAgICAgICAgaWYgKGlzT2JqZWN0KHZhbHVlKSkge1xuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgayBvZiBrZXlzKSB7XG4gICAgICAgICAgICAgICAgICAgIHlpZWxkIFtrLCB2YWx1ZVtrXSwgc2NoZW1hW2tdXTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIHZhbGlkYXRvcih2YWx1ZSkge1xuICAgICAgICAgICAgcmV0dXJuIChpc09iamVjdCh2YWx1ZSkgfHwgYEV4cGVjdGVkIGFuIG9iamVjdCwgYnV0IHJlY2VpdmVkOiAke3ByaW50KHZhbHVlKX1gKTtcbiAgICAgICAgfSxcbiAgICAgICAgY29lcmNlcih2YWx1ZSkge1xuICAgICAgICAgICAgcmV0dXJuIGlzT2JqZWN0KHZhbHVlKSA/IHsgLi4udmFsdWUgfSA6IHZhbHVlO1xuICAgICAgICB9LFxuICAgIH0pO1xufVxuLyoqXG4gKiBFbnN1cmUgdGhhdCBhIHZhbHVlIG1hdGNoZXMgb25lIG9mIGEgc2V0IG9mIHR5cGVzLlxuICovXG5mdW5jdGlvbiB1bmlvbihTdHJ1Y3RzKSB7XG4gICAgY29uc3QgZGVzY3JpcHRpb24gPSBTdHJ1Y3RzLm1hcCgocykgPT4gcy50eXBlKS5qb2luKCcgfCAnKTtcbiAgICByZXR1cm4gbmV3IFN0cnVjdCh7XG4gICAgICAgIHR5cGU6ICd1bmlvbicsXG4gICAgICAgIHNjaGVtYTogbnVsbCxcbiAgICAgICAgY29lcmNlcih2YWx1ZSkge1xuICAgICAgICAgICAgZm9yIChjb25zdCBTIG9mIFN0cnVjdHMpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBbZXJyb3IsIGNvZXJjZWRdID0gUy52YWxpZGF0ZSh2YWx1ZSwgeyBjb2VyY2U6IHRydWUgfSk7XG4gICAgICAgICAgICAgICAgaWYgKCFlcnJvcikge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gY29lcmNlZDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgICAgIH0sXG4gICAgICAgIHZhbGlkYXRvcih2YWx1ZSwgY3R4KSB7XG4gICAgICAgICAgICBjb25zdCBmYWlsdXJlcyA9IFtdO1xuICAgICAgICAgICAgZm9yIChjb25zdCBTIG9mIFN0cnVjdHMpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBbLi4udHVwbGVzXSA9IHJ1bih2YWx1ZSwgUywgY3R4KTtcbiAgICAgICAgICAgICAgICBjb25zdCBbZmlyc3RdID0gdHVwbGVzO1xuICAgICAgICAgICAgICAgIGlmICghZmlyc3RbMF0pIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFtdO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBbZmFpbHVyZV0gb2YgdHVwbGVzKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoZmFpbHVyZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZhaWx1cmVzLnB1c2goZmFpbHVyZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAgICAgIGBFeHBlY3RlZCB0aGUgdmFsdWUgdG8gc2F0aXNmeSBhIHVuaW9uIG9mIFxcYCR7ZGVzY3JpcHRpb259XFxgLCBidXQgcmVjZWl2ZWQ6ICR7cHJpbnQodmFsdWUpfWAsXG4gICAgICAgICAgICAgICAgLi4uZmFpbHVyZXMsXG4gICAgICAgICAgICBdO1xuICAgICAgICB9LFxuICAgIH0pO1xufVxuLyoqXG4gKiBFbnN1cmUgdGhhdCBhbnkgdmFsdWUgcGFzc2VzIHZhbGlkYXRpb24sIHdpdGhvdXQgd2lkZW5pbmcgaXRzIHR5cGUgdG8gYGFueWAuXG4gKi9cbmZ1bmN0aW9uIHVua25vd24oKSB7XG4gICAgcmV0dXJuIGRlZmluZSgndW5rbm93bicsICgpID0+IHRydWUpO1xufVxuXG4vKipcbiAqIEF1Z21lbnQgYSBgU3RydWN0YCB0byBhZGQgYW4gYWRkaXRpb25hbCBjb2VyY2lvbiBzdGVwIHRvIGl0cyBpbnB1dC5cbiAqXG4gKiBUaGlzIGFsbG93cyB5b3UgdG8gdHJhbnNmb3JtIGlucHV0IGRhdGEgYmVmb3JlIHZhbGlkYXRpbmcgaXQsIHRvIGluY3JlYXNlIHRoZVxuICogbGlrZWxpaG9vZCB0aGF0IGl0IHBhc3NlcyB2YWxpZGF0aW9u4oCUZm9yIGV4YW1wbGUgZm9yIGRlZmF1bHQgdmFsdWVzLCBwYXJzaW5nXG4gKiBkaWZmZXJlbnQgZm9ybWF0cywgZXRjLlxuICpcbiAqIE5vdGU6IFlvdSBtdXN0IHVzZSBgY3JlYXRlKHZhbHVlLCBTdHJ1Y3QpYCBvbiB0aGUgdmFsdWUgdG8gaGF2ZSB0aGUgY29lcmNpb25cbiAqIHRha2UgZWZmZWN0ISBVc2luZyBzaW1wbHkgYGFzc2VydCgpYCBvciBgaXMoKWAgd2lsbCBub3QgdXNlIGNvZXJjaW9uLlxuICovXG5mdW5jdGlvbiBjb2VyY2Uoc3RydWN0LCBjb25kaXRpb24sIGNvZXJjZXIpIHtcbiAgICByZXR1cm4gbmV3IFN0cnVjdCh7XG4gICAgICAgIC4uLnN0cnVjdCxcbiAgICAgICAgY29lcmNlcjogKHZhbHVlLCBjdHgpID0+IHtcbiAgICAgICAgICAgIHJldHVybiBpcyh2YWx1ZSwgY29uZGl0aW9uKVxuICAgICAgICAgICAgICAgID8gc3RydWN0LmNvZXJjZXIoY29lcmNlcih2YWx1ZSwgY3R4KSwgY3R4KVxuICAgICAgICAgICAgICAgIDogc3RydWN0LmNvZXJjZXIodmFsdWUsIGN0eCk7XG4gICAgICAgIH0sXG4gICAgfSk7XG59XG4vKipcbiAqIEF1Z21lbnQgYSBzdHJ1Y3QgdG8gcmVwbGFjZSBgdW5kZWZpbmVkYCB2YWx1ZXMgd2l0aCBhIGRlZmF1bHQuXG4gKlxuICogTm90ZTogWW91IG11c3QgdXNlIGBjcmVhdGUodmFsdWUsIFN0cnVjdClgIG9uIHRoZSB2YWx1ZSB0byBoYXZlIHRoZSBjb2VyY2lvblxuICogdGFrZSBlZmZlY3QhIFVzaW5nIHNpbXBseSBgYXNzZXJ0KClgIG9yIGBpcygpYCB3aWxsIG5vdCB1c2UgY29lcmNpb24uXG4gKi9cbmZ1bmN0aW9uIGRlZmF1bHRlZChzdHJ1Y3QsIGZhbGxiYWNrLCBvcHRpb25zID0ge30pIHtcbiAgICByZXR1cm4gY29lcmNlKHN0cnVjdCwgdW5rbm93bigpLCAoeCkgPT4ge1xuICAgICAgICBjb25zdCBmID0gdHlwZW9mIGZhbGxiYWNrID09PSAnZnVuY3Rpb24nID8gZmFsbGJhY2soKSA6IGZhbGxiYWNrO1xuICAgICAgICBpZiAoeCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICByZXR1cm4gZjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIW9wdGlvbnMuc3RyaWN0ICYmIGlzUGxhaW5PYmplY3QoeCkgJiYgaXNQbGFpbk9iamVjdChmKSkge1xuICAgICAgICAgICAgY29uc3QgcmV0ID0geyAuLi54IH07XG4gICAgICAgICAgICBsZXQgY2hhbmdlZCA9IGZhbHNlO1xuICAgICAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gZikge1xuICAgICAgICAgICAgICAgIGlmIChyZXRba2V5XSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldFtrZXldID0gZltrZXldO1xuICAgICAgICAgICAgICAgICAgICBjaGFuZ2VkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoY2hhbmdlZCkge1xuICAgICAgICAgICAgICAgIHJldHVybiByZXQ7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHg7XG4gICAgfSk7XG59XG4vKipcbiAqIEF1Z21lbnQgYSBzdHJ1Y3QgdG8gdHJpbSBzdHJpbmcgaW5wdXRzLlxuICpcbiAqIE5vdGU6IFlvdSBtdXN0IHVzZSBgY3JlYXRlKHZhbHVlLCBTdHJ1Y3QpYCBvbiB0aGUgdmFsdWUgdG8gaGF2ZSB0aGUgY29lcmNpb25cbiAqIHRha2UgZWZmZWN0ISBVc2luZyBzaW1wbHkgYGFzc2VydCgpYCBvciBgaXMoKWAgd2lsbCBub3QgdXNlIGNvZXJjaW9uLlxuICovXG5mdW5jdGlvbiB0cmltbWVkKHN0cnVjdCkge1xuICAgIHJldHVybiBjb2VyY2Uoc3RydWN0LCBzdHJpbmcoKSwgKHgpID0+IHgudHJpbSgpKTtcbn1cblxuLyoqXG4gKiBFbnN1cmUgdGhhdCBhIHN0cmluZywgYXJyYXksIG1hcCwgb3Igc2V0IGlzIGVtcHR5LlxuICovXG5mdW5jdGlvbiBlbXB0eShzdHJ1Y3QpIHtcbiAgICByZXR1cm4gcmVmaW5lKHN0cnVjdCwgJ2VtcHR5JywgKHZhbHVlKSA9PiB7XG4gICAgICAgIGNvbnN0IHNpemUgPSBnZXRTaXplKHZhbHVlKTtcbiAgICAgICAgcmV0dXJuIChzaXplID09PSAwIHx8XG4gICAgICAgICAgICBgRXhwZWN0ZWQgYW4gZW1wdHkgJHtzdHJ1Y3QudHlwZX0gYnV0IHJlY2VpdmVkIG9uZSB3aXRoIGEgc2l6ZSBvZiBcXGAke3NpemV9XFxgYCk7XG4gICAgfSk7XG59XG5mdW5jdGlvbiBnZXRTaXplKHZhbHVlKSB7XG4gICAgaWYgKHZhbHVlIGluc3RhbmNlb2YgTWFwIHx8IHZhbHVlIGluc3RhbmNlb2YgU2V0KSB7XG4gICAgICAgIHJldHVybiB2YWx1ZS5zaXplO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIHZhbHVlLmxlbmd0aDtcbiAgICB9XG59XG4vKipcbiAqIEVuc3VyZSB0aGF0IGEgbnVtYmVyIG9yIGRhdGUgaXMgYmVsb3cgYSB0aHJlc2hvbGQuXG4gKi9cbmZ1bmN0aW9uIG1heChzdHJ1Y3QsIHRocmVzaG9sZCwgb3B0aW9ucyA9IHt9KSB7XG4gICAgY29uc3QgeyBleGNsdXNpdmUgfSA9IG9wdGlvbnM7XG4gICAgcmV0dXJuIHJlZmluZShzdHJ1Y3QsICdtYXgnLCAodmFsdWUpID0+IHtcbiAgICAgICAgcmV0dXJuIGV4Y2x1c2l2ZVxuICAgICAgICAgICAgPyB2YWx1ZSA8IHRocmVzaG9sZFxuICAgICAgICAgICAgOiB2YWx1ZSA8PSB0aHJlc2hvbGQgfHxcbiAgICAgICAgICAgICAgICBgRXhwZWN0ZWQgYSAke3N0cnVjdC50eXBlfSBsZXNzIHRoYW4gJHtleGNsdXNpdmUgPyAnJyA6ICdvciBlcXVhbCB0byAnfSR7dGhyZXNob2xkfSBidXQgcmVjZWl2ZWQgXFxgJHt2YWx1ZX1cXGBgO1xuICAgIH0pO1xufVxuLyoqXG4gKiBFbnN1cmUgdGhhdCBhIG51bWJlciBvciBkYXRlIGlzIGFib3ZlIGEgdGhyZXNob2xkLlxuICovXG5mdW5jdGlvbiBtaW4oc3RydWN0LCB0aHJlc2hvbGQsIG9wdGlvbnMgPSB7fSkge1xuICAgIGNvbnN0IHsgZXhjbHVzaXZlIH0gPSBvcHRpb25zO1xuICAgIHJldHVybiByZWZpbmUoc3RydWN0LCAnbWluJywgKHZhbHVlKSA9PiB7XG4gICAgICAgIHJldHVybiBleGNsdXNpdmVcbiAgICAgICAgICAgID8gdmFsdWUgPiB0aHJlc2hvbGRcbiAgICAgICAgICAgIDogdmFsdWUgPj0gdGhyZXNob2xkIHx8XG4gICAgICAgICAgICAgICAgYEV4cGVjdGVkIGEgJHtzdHJ1Y3QudHlwZX0gZ3JlYXRlciB0aGFuICR7ZXhjbHVzaXZlID8gJycgOiAnb3IgZXF1YWwgdG8gJ30ke3RocmVzaG9sZH0gYnV0IHJlY2VpdmVkIFxcYCR7dmFsdWV9XFxgYDtcbiAgICB9KTtcbn1cbi8qKlxuICogRW5zdXJlIHRoYXQgYSBzdHJpbmcsIGFycmF5LCBtYXAgb3Igc2V0IGlzIG5vdCBlbXB0eS5cbiAqL1xuZnVuY3Rpb24gbm9uZW1wdHkoc3RydWN0KSB7XG4gICAgcmV0dXJuIHJlZmluZShzdHJ1Y3QsICdub25lbXB0eScsICh2YWx1ZSkgPT4ge1xuICAgICAgICBjb25zdCBzaXplID0gZ2V0U2l6ZSh2YWx1ZSk7XG4gICAgICAgIHJldHVybiAoc2l6ZSA+IDAgfHwgYEV4cGVjdGVkIGEgbm9uZW1wdHkgJHtzdHJ1Y3QudHlwZX0gYnV0IHJlY2VpdmVkIGFuIGVtcHR5IG9uZWApO1xuICAgIH0pO1xufVxuLyoqXG4gKiBFbnN1cmUgdGhhdCBhIHN0cmluZyBtYXRjaGVzIGEgcmVndWxhciBleHByZXNzaW9uLlxuICovXG5mdW5jdGlvbiBwYXR0ZXJuKHN0cnVjdCwgcmVnZXhwKSB7XG4gICAgcmV0dXJuIHJlZmluZShzdHJ1Y3QsICdwYXR0ZXJuJywgKHZhbHVlKSA9PiB7XG4gICAgICAgIHJldHVybiAocmVnZXhwLnRlc3QodmFsdWUpIHx8XG4gICAgICAgICAgICBgRXhwZWN0ZWQgYSAke3N0cnVjdC50eXBlfSBtYXRjaGluZyBcXGAvJHtyZWdleHAuc291cmNlfS9cXGAgYnV0IHJlY2VpdmVkIFwiJHt2YWx1ZX1cImApO1xuICAgIH0pO1xufVxuLyoqXG4gKiBFbnN1cmUgdGhhdCBhIHN0cmluZywgYXJyYXksIG51bWJlciwgZGF0ZSwgbWFwLCBvciBzZXQgaGFzIGEgc2l6ZSAob3IgbGVuZ3RoLCBvciB0aW1lKSBiZXR3ZWVuIGBtaW5gIGFuZCBgbWF4YC5cbiAqL1xuZnVuY3Rpb24gc2l6ZShzdHJ1Y3QsIG1pbiwgbWF4ID0gbWluKSB7XG4gICAgY29uc3QgZXhwZWN0ZWQgPSBgRXhwZWN0ZWQgYSAke3N0cnVjdC50eXBlfWA7XG4gICAgY29uc3Qgb2YgPSBtaW4gPT09IG1heCA/IGBvZiBcXGAke21pbn1cXGBgIDogYGJldHdlZW4gXFxgJHttaW59XFxgIGFuZCBcXGAke21heH1cXGBgO1xuICAgIHJldHVybiByZWZpbmUoc3RydWN0LCAnc2l6ZScsICh2YWx1ZSkgPT4ge1xuICAgICAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnbnVtYmVyJyB8fCB2YWx1ZSBpbnN0YW5jZW9mIERhdGUpIHtcbiAgICAgICAgICAgIHJldHVybiAoKG1pbiA8PSB2YWx1ZSAmJiB2YWx1ZSA8PSBtYXgpIHx8XG4gICAgICAgICAgICAgICAgYCR7ZXhwZWN0ZWR9ICR7b2Z9IGJ1dCByZWNlaXZlZCBcXGAke3ZhbHVlfVxcYGApO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHZhbHVlIGluc3RhbmNlb2YgTWFwIHx8IHZhbHVlIGluc3RhbmNlb2YgU2V0KSB7XG4gICAgICAgICAgICBjb25zdCB7IHNpemUgfSA9IHZhbHVlO1xuICAgICAgICAgICAgcmV0dXJuICgobWluIDw9IHNpemUgJiYgc2l6ZSA8PSBtYXgpIHx8XG4gICAgICAgICAgICAgICAgYCR7ZXhwZWN0ZWR9IHdpdGggYSBzaXplICR7b2Z9IGJ1dCByZWNlaXZlZCBvbmUgd2l0aCBhIHNpemUgb2YgXFxgJHtzaXplfVxcYGApO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgY29uc3QgeyBsZW5ndGggfSA9IHZhbHVlO1xuICAgICAgICAgICAgcmV0dXJuICgobWluIDw9IGxlbmd0aCAmJiBsZW5ndGggPD0gbWF4KSB8fFxuICAgICAgICAgICAgICAgIGAke2V4cGVjdGVkfSB3aXRoIGEgbGVuZ3RoICR7b2Z9IGJ1dCByZWNlaXZlZCBvbmUgd2l0aCBhIGxlbmd0aCBvZiBcXGAke2xlbmd0aH1cXGBgKTtcbiAgICAgICAgfVxuICAgIH0pO1xufVxuLyoqXG4gKiBBdWdtZW50IGEgYFN0cnVjdGAgdG8gYWRkIGFuIGFkZGl0aW9uYWwgcmVmaW5lbWVudCB0byB0aGUgdmFsaWRhdGlvbi5cbiAqXG4gKiBUaGUgcmVmaW5lciBmdW5jdGlvbiBpcyBndWFyYW50ZWVkIHRvIHJlY2VpdmUgYSB2YWx1ZSBvZiB0aGUgc3RydWN0J3MgdHlwZSxcbiAqIGJlY2F1c2UgdGhlIHN0cnVjdCdzIGV4aXN0aW5nIHZhbGlkYXRpb24gd2lsbCBhbHJlYWR5IGhhdmUgcGFzc2VkLiBUaGlzXG4gKiBhbGxvd3MgeW91IHRvIGxheWVyIGFkZGl0aW9uYWwgdmFsaWRhdGlvbiBvbiB0b3Agb2YgZXhpc3Rpbmcgc3RydWN0cy5cbiAqL1xuZnVuY3Rpb24gcmVmaW5lKHN0cnVjdCwgbmFtZSwgcmVmaW5lcikge1xuICAgIHJldHVybiBuZXcgU3RydWN0KHtcbiAgICAgICAgLi4uc3RydWN0LFxuICAgICAgICAqcmVmaW5lcih2YWx1ZSwgY3R4KSB7XG4gICAgICAgICAgICB5aWVsZCogc3RydWN0LnJlZmluZXIodmFsdWUsIGN0eCk7XG4gICAgICAgICAgICBjb25zdCByZXN1bHQgPSByZWZpbmVyKHZhbHVlLCBjdHgpO1xuICAgICAgICAgICAgY29uc3QgZmFpbHVyZXMgPSB0b0ZhaWx1cmVzKHJlc3VsdCwgY3R4LCBzdHJ1Y3QsIHZhbHVlKTtcbiAgICAgICAgICAgIGZvciAoY29uc3QgZmFpbHVyZSBvZiBmYWlsdXJlcykge1xuICAgICAgICAgICAgICAgIHlpZWxkIHsgLi4uZmFpbHVyZSwgcmVmaW5lbWVudDogbmFtZSB9O1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgIH0pO1xufVxuXG5leHBvcnQgeyBTdHJ1Y3QsIFN0cnVjdEVycm9yLCBhbnksIGFycmF5LCBhc3NlcnQsIGFzc2lnbiwgYmlnaW50LCBib29sZWFuLCBjb2VyY2UsIGNyZWF0ZSwgZGF0ZSwgZGVmYXVsdGVkLCBkZWZpbmUsIGRlcHJlY2F0ZWQsIGR5bmFtaWMsIGVtcHR5LCBlbnVtcywgZnVuYywgaW5zdGFuY2UsIGludGVnZXIsIGludGVyc2VjdGlvbiwgaXMsIGxhenksIGxpdGVyYWwsIG1hcCwgbWFzaywgbWF4LCBtaW4sIG5ldmVyLCBub25lbXB0eSwgbnVsbGFibGUsIG51bWJlciwgb2JqZWN0LCBvbWl0LCBvcHRpb25hbCwgcGFydGlhbCwgcGF0dGVybiwgcGljaywgcmVjb3JkLCByZWZpbmUsIHJlZ2V4cCwgc2V0LCBzaXplLCBzdHJpbmcsIHN0cnVjdCwgdHJpbW1lZCwgdHVwbGUsIHR5cGUsIHVuaW9uLCB1bmtub3duLCB2YWxpZGF0ZSB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/superstruct/dist/index.mjs\n");

/***/ })

};
;