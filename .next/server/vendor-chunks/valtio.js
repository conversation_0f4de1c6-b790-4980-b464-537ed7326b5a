"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/valtio";
exports.ids = ["vendor-chunks/valtio"];
exports.modules = {

/***/ "(ssr)/./node_modules/valtio/esm/vanilla.mjs":
/*!*********************************************!*\
  !*** ./node_modules/valtio/esm/vanilla.mjs ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getVersion: () => (/* binding */ getVersion),\n/* harmony export */   proxy: () => (/* binding */ proxy),\n/* harmony export */   ref: () => (/* binding */ ref),\n/* harmony export */   snapshot: () => (/* binding */ snapshot),\n/* harmony export */   subscribe: () => (/* binding */ subscribe),\n/* harmony export */   unstable_buildProxyFunction: () => (/* binding */ unstable_buildProxyFunction)\n/* harmony export */ });\n/* harmony import */ var proxy_compare__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! proxy-compare */ \"(ssr)/./node_modules/proxy-compare/dist/index.modern.js\");\n\n\nconst isObject = (x) => typeof x === \"object\" && x !== null;\nconst proxyStateMap = /* @__PURE__ */ new WeakMap();\nconst refSet = /* @__PURE__ */ new WeakSet();\nconst buildProxyFunction = (objectIs = Object.is, newProxy = (target, handler) => new Proxy(target, handler), canProxy = (x) => isObject(x) && !refSet.has(x) && (Array.isArray(x) || !(Symbol.iterator in x)) && !(x instanceof WeakMap) && !(x instanceof WeakSet) && !(x instanceof Error) && !(x instanceof Number) && !(x instanceof Date) && !(x instanceof String) && !(x instanceof RegExp) && !(x instanceof ArrayBuffer), defaultHandlePromise = (promise) => {\n  switch (promise.status) {\n    case \"fulfilled\":\n      return promise.value;\n    case \"rejected\":\n      throw promise.reason;\n    default:\n      throw promise;\n  }\n}, snapCache = /* @__PURE__ */ new WeakMap(), createSnapshot = (target, version, handlePromise = defaultHandlePromise) => {\n  const cache = snapCache.get(target);\n  if ((cache == null ? void 0 : cache[0]) === version) {\n    return cache[1];\n  }\n  const snap = Array.isArray(target) ? [] : Object.create(Object.getPrototypeOf(target));\n  (0,proxy_compare__WEBPACK_IMPORTED_MODULE_0__.markToTrack)(snap, true);\n  snapCache.set(target, [version, snap]);\n  Reflect.ownKeys(target).forEach((key) => {\n    if (Object.getOwnPropertyDescriptor(snap, key)) {\n      return;\n    }\n    const value = Reflect.get(target, key);\n    const { enumerable } = Reflect.getOwnPropertyDescriptor(\n      target,\n      key\n    );\n    const desc = {\n      value,\n      enumerable,\n      // This is intentional to avoid copying with proxy-compare.\n      // It's still non-writable, so it avoids assigning a value.\n      configurable: true\n    };\n    if (refSet.has(value)) {\n      (0,proxy_compare__WEBPACK_IMPORTED_MODULE_0__.markToTrack)(value, false);\n    } else if (value instanceof Promise) {\n      delete desc.value;\n      desc.get = () => handlePromise(value);\n    } else if (proxyStateMap.has(value)) {\n      const [target2, ensureVersion] = proxyStateMap.get(\n        value\n      );\n      desc.value = createSnapshot(\n        target2,\n        ensureVersion(),\n        handlePromise\n      );\n    }\n    Object.defineProperty(snap, key, desc);\n  });\n  return Object.preventExtensions(snap);\n}, proxyCache = /* @__PURE__ */ new WeakMap(), versionHolder = [1, 1], proxyFunction = (initialObject) => {\n  if (!isObject(initialObject)) {\n    throw new Error(\"object required\");\n  }\n  const found = proxyCache.get(initialObject);\n  if (found) {\n    return found;\n  }\n  let version = versionHolder[0];\n  const listeners = /* @__PURE__ */ new Set();\n  const notifyUpdate = (op, nextVersion = ++versionHolder[0]) => {\n    if (version !== nextVersion) {\n      version = nextVersion;\n      listeners.forEach((listener) => listener(op, nextVersion));\n    }\n  };\n  let checkVersion = versionHolder[1];\n  const ensureVersion = (nextCheckVersion = ++versionHolder[1]) => {\n    if (checkVersion !== nextCheckVersion && !listeners.size) {\n      checkVersion = nextCheckVersion;\n      propProxyStates.forEach(([propProxyState]) => {\n        const propVersion = propProxyState[1](nextCheckVersion);\n        if (propVersion > version) {\n          version = propVersion;\n        }\n      });\n    }\n    return version;\n  };\n  const createPropListener = (prop) => (op, nextVersion) => {\n    const newOp = [...op];\n    newOp[1] = [prop, ...newOp[1]];\n    notifyUpdate(newOp, nextVersion);\n  };\n  const propProxyStates = /* @__PURE__ */ new Map();\n  const addPropListener = (prop, propProxyState) => {\n    if (( false ? 0 : void 0) !== \"production\" && propProxyStates.has(prop)) {\n      throw new Error(\"prop listener already exists\");\n    }\n    if (listeners.size) {\n      const remove = propProxyState[3](createPropListener(prop));\n      propProxyStates.set(prop, [propProxyState, remove]);\n    } else {\n      propProxyStates.set(prop, [propProxyState]);\n    }\n  };\n  const removePropListener = (prop) => {\n    var _a;\n    const entry = propProxyStates.get(prop);\n    if (entry) {\n      propProxyStates.delete(prop);\n      (_a = entry[1]) == null ? void 0 : _a.call(entry);\n    }\n  };\n  const addListener = (listener) => {\n    listeners.add(listener);\n    if (listeners.size === 1) {\n      propProxyStates.forEach(([propProxyState, prevRemove], prop) => {\n        if (( false ? 0 : void 0) !== \"production\" && prevRemove) {\n          throw new Error(\"remove already exists\");\n        }\n        const remove = propProxyState[3](createPropListener(prop));\n        propProxyStates.set(prop, [propProxyState, remove]);\n      });\n    }\n    const removeListener = () => {\n      listeners.delete(listener);\n      if (listeners.size === 0) {\n        propProxyStates.forEach(([propProxyState, remove], prop) => {\n          if (remove) {\n            remove();\n            propProxyStates.set(prop, [propProxyState]);\n          }\n        });\n      }\n    };\n    return removeListener;\n  };\n  const baseObject = Array.isArray(initialObject) ? [] : Object.create(Object.getPrototypeOf(initialObject));\n  const handler = {\n    deleteProperty(target, prop) {\n      const prevValue = Reflect.get(target, prop);\n      removePropListener(prop);\n      const deleted = Reflect.deleteProperty(target, prop);\n      if (deleted) {\n        notifyUpdate([\"delete\", [prop], prevValue]);\n      }\n      return deleted;\n    },\n    set(target, prop, value, receiver) {\n      const hasPrevValue = Reflect.has(target, prop);\n      const prevValue = Reflect.get(target, prop, receiver);\n      if (hasPrevValue && (objectIs(prevValue, value) || proxyCache.has(value) && objectIs(prevValue, proxyCache.get(value)))) {\n        return true;\n      }\n      removePropListener(prop);\n      if (isObject(value)) {\n        value = (0,proxy_compare__WEBPACK_IMPORTED_MODULE_0__.getUntracked)(value) || value;\n      }\n      let nextValue = value;\n      if (value instanceof Promise) {\n        value.then((v) => {\n          value.status = \"fulfilled\";\n          value.value = v;\n          notifyUpdate([\"resolve\", [prop], v]);\n        }).catch((e) => {\n          value.status = \"rejected\";\n          value.reason = e;\n          notifyUpdate([\"reject\", [prop], e]);\n        });\n      } else {\n        if (!proxyStateMap.has(value) && canProxy(value)) {\n          nextValue = proxyFunction(value);\n        }\n        const childProxyState = !refSet.has(nextValue) && proxyStateMap.get(nextValue);\n        if (childProxyState) {\n          addPropListener(prop, childProxyState);\n        }\n      }\n      Reflect.set(target, prop, nextValue, receiver);\n      notifyUpdate([\"set\", [prop], value, prevValue]);\n      return true;\n    }\n  };\n  const proxyObject = newProxy(baseObject, handler);\n  proxyCache.set(initialObject, proxyObject);\n  const proxyState = [\n    baseObject,\n    ensureVersion,\n    createSnapshot,\n    addListener\n  ];\n  proxyStateMap.set(proxyObject, proxyState);\n  Reflect.ownKeys(initialObject).forEach((key) => {\n    const desc = Object.getOwnPropertyDescriptor(\n      initialObject,\n      key\n    );\n    if (\"value\" in desc) {\n      proxyObject[key] = initialObject[key];\n      delete desc.value;\n      delete desc.writable;\n    }\n    Object.defineProperty(baseObject, key, desc);\n  });\n  return proxyObject;\n}) => [\n  // public functions\n  proxyFunction,\n  // shared state\n  proxyStateMap,\n  refSet,\n  // internal things\n  objectIs,\n  newProxy,\n  canProxy,\n  defaultHandlePromise,\n  snapCache,\n  createSnapshot,\n  proxyCache,\n  versionHolder\n];\nconst [defaultProxyFunction] = buildProxyFunction();\nfunction proxy(initialObject = {}) {\n  return defaultProxyFunction(initialObject);\n}\nfunction getVersion(proxyObject) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  return proxyState == null ? void 0 : proxyState[1]();\n}\nfunction subscribe(proxyObject, callback, notifyInSync) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  if (( false ? 0 : void 0) !== \"production\" && !proxyState) {\n    console.warn(\"Please use proxy object\");\n  }\n  let promise;\n  const ops = [];\n  const addListener = proxyState[3];\n  let isListenerActive = false;\n  const listener = (op) => {\n    ops.push(op);\n    if (notifyInSync) {\n      callback(ops.splice(0));\n      return;\n    }\n    if (!promise) {\n      promise = Promise.resolve().then(() => {\n        promise = void 0;\n        if (isListenerActive) {\n          callback(ops.splice(0));\n        }\n      });\n    }\n  };\n  const removeListener = addListener(listener);\n  isListenerActive = true;\n  return () => {\n    isListenerActive = false;\n    removeListener();\n  };\n}\nfunction snapshot(proxyObject, handlePromise) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  if (( false ? 0 : void 0) !== \"production\" && !proxyState) {\n    console.warn(\"Please use proxy object\");\n  }\n  const [target, ensureVersion, createSnapshot] = proxyState;\n  return createSnapshot(target, ensureVersion(), handlePromise);\n}\nfunction ref(obj) {\n  refSet.add(obj);\n  return obj;\n}\nconst unstable_buildProxyFunction = buildProxyFunction;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/valtio/esm/vanilla.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/valtio/esm/vanilla/utils.mjs":
/*!***************************************************!*\
  !*** ./node_modules/valtio/esm/vanilla/utils.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addComputed: () => (/* binding */ addComputed_DEPRECATED),\n/* harmony export */   derive: () => (/* reexport safe */ derive_valtio__WEBPACK_IMPORTED_MODULE_0__.derive),\n/* harmony export */   devtools: () => (/* binding */ devtools),\n/* harmony export */   proxyMap: () => (/* binding */ proxyMap),\n/* harmony export */   proxySet: () => (/* binding */ proxySet),\n/* harmony export */   proxyWithComputed: () => (/* binding */ proxyWithComputed_DEPRECATED),\n/* harmony export */   proxyWithHistory: () => (/* binding */ proxyWithHistory_DEPRECATED),\n/* harmony export */   subscribeKey: () => (/* binding */ subscribeKey),\n/* harmony export */   underive: () => (/* reexport safe */ derive_valtio__WEBPACK_IMPORTED_MODULE_0__.underive),\n/* harmony export */   unstable_deriveSubscriptions: () => (/* reexport safe */ derive_valtio__WEBPACK_IMPORTED_MODULE_0__.unstable_deriveSubscriptions),\n/* harmony export */   watch: () => (/* binding */ watch)\n/* harmony export */ });\n/* harmony import */ var valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! valtio/vanilla */ \"(ssr)/./node_modules/valtio/esm/vanilla.mjs\");\n/* harmony import */ var derive_valtio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! derive-valtio */ \"(ssr)/./node_modules/derive-valtio/dist/index.modern.js\");\n\n\n\n\nfunction subscribeKey(proxyObject, key, callback, notifyInSync) {\n  let prevValue = proxyObject[key];\n  return (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.subscribe)(\n    proxyObject,\n    () => {\n      const nextValue = proxyObject[key];\n      if (!Object.is(prevValue, nextValue)) {\n        callback(prevValue = nextValue);\n      }\n    },\n    notifyInSync\n  );\n}\n\nlet currentCleanups;\nfunction watch(callback, options) {\n  let alive = true;\n  const cleanups = /* @__PURE__ */ new Set();\n  const subscriptions = /* @__PURE__ */ new Map();\n  const cleanup = () => {\n    if (alive) {\n      alive = false;\n      cleanups.forEach((clean) => clean());\n      cleanups.clear();\n      subscriptions.forEach((unsubscribe) => unsubscribe());\n      subscriptions.clear();\n    }\n  };\n  const revalidate = async () => {\n    if (!alive) {\n      return;\n    }\n    cleanups.forEach((clean) => clean());\n    cleanups.clear();\n    const proxiesToSubscribe = /* @__PURE__ */ new Set();\n    const parent = currentCleanups;\n    currentCleanups = cleanups;\n    try {\n      const promiseOrPossibleCleanup = callback((proxyObject) => {\n        proxiesToSubscribe.add(proxyObject);\n        if (alive && !subscriptions.has(proxyObject)) {\n          const unsubscribe = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.subscribe)(proxyObject, revalidate, options == null ? void 0 : options.sync);\n          subscriptions.set(proxyObject, unsubscribe);\n        }\n        return proxyObject;\n      });\n      const couldBeCleanup = promiseOrPossibleCleanup && promiseOrPossibleCleanup instanceof Promise ? await promiseOrPossibleCleanup : promiseOrPossibleCleanup;\n      if (couldBeCleanup) {\n        if (alive) {\n          cleanups.add(couldBeCleanup);\n        } else {\n          cleanup();\n        }\n      }\n    } finally {\n      currentCleanups = parent;\n    }\n    subscriptions.forEach((unsubscribe, proxyObject) => {\n      if (!proxiesToSubscribe.has(proxyObject)) {\n        subscriptions.delete(proxyObject);\n        unsubscribe();\n      }\n    });\n  };\n  if (currentCleanups) {\n    currentCleanups.add(cleanup);\n  }\n  revalidate();\n  return cleanup;\n}\n\nconst DEVTOOLS = Symbol();\nfunction devtools(proxyObject, options) {\n  if (typeof options === \"string\") {\n    console.warn(\n      \"string name option is deprecated, use { name }. https://github.com/pmndrs/valtio/pull/400\"\n    );\n    options = { name: options };\n  }\n  const { enabled, name = \"\", ...rest } = options || {};\n  let extension;\n  try {\n    extension = (enabled != null ? enabled : ( false ? 0 : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extension) {\n    if (( false ? 0 : void 0) !== \"production\" && enabled) {\n      console.warn(\"[Warning] Please install/enable Redux devtools extension\");\n    }\n    return;\n  }\n  let isTimeTraveling = false;\n  const devtools2 = extension.connect({ name, ...rest });\n  const unsub1 = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.subscribe)(proxyObject, (ops) => {\n    const action = ops.filter(([_, path]) => path[0] !== DEVTOOLS).map(([op, path]) => `${op}:${path.map(String).join(\".\")}`).join(\", \");\n    if (!action) {\n      return;\n    }\n    if (isTimeTraveling) {\n      isTimeTraveling = false;\n    } else {\n      const snapWithoutDevtools = Object.assign({}, (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n      delete snapWithoutDevtools[DEVTOOLS];\n      devtools2.send(\n        {\n          type: action,\n          updatedAt: (/* @__PURE__ */ new Date()).toLocaleString()\n        },\n        snapWithoutDevtools\n      );\n    }\n  });\n  const unsub2 = devtools2.subscribe((message) => {\n    var _a, _b, _c, _d, _e, _f;\n    if (message.type === \"ACTION\" && message.payload) {\n      try {\n        Object.assign(proxyObject, JSON.parse(message.payload));\n      } catch (e) {\n        console.error(\n          \"please dispatch a serializable value that JSON.parse() and proxy() support\\n\",\n          e\n        );\n      }\n    }\n    if (message.type === \"DISPATCH\" && message.state) {\n      if (((_a = message.payload) == null ? void 0 : _a.type) === \"JUMP_TO_ACTION\" || ((_b = message.payload) == null ? void 0 : _b.type) === \"JUMP_TO_STATE\") {\n        isTimeTraveling = true;\n        const state = JSON.parse(message.state);\n        Object.assign(proxyObject, state);\n      }\n      proxyObject[DEVTOOLS] = message;\n    } else if (message.type === \"DISPATCH\" && ((_c = message.payload) == null ? void 0 : _c.type) === \"COMMIT\") {\n      devtools2.init((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n    } else if (message.type === \"DISPATCH\" && ((_d = message.payload) == null ? void 0 : _d.type) === \"IMPORT_STATE\") {\n      const actions = (_e = message.payload.nextLiftedState) == null ? void 0 : _e.actionsById;\n      const computedStates = ((_f = message.payload.nextLiftedState) == null ? void 0 : _f.computedStates) || [];\n      isTimeTraveling = true;\n      computedStates.forEach(({ state }, index) => {\n        const action = actions[index] || \"No action found\";\n        Object.assign(proxyObject, state);\n        if (index === 0) {\n          devtools2.init((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n        } else {\n          devtools2.send(action, (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n        }\n      });\n    }\n  });\n  devtools2.init((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n  return () => {\n    unsub1();\n    unsub2 == null ? void 0 : unsub2();\n  };\n}\n\nfunction addComputed_DEPRECATED(proxyObject, computedFns_FAKE, targetObject = proxyObject) {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      \"addComputed is deprecated. Please consider using `derive`. Falling back to emulation with derive. https://github.com/pmndrs/valtio/pull/201\"\n    );\n  }\n  const derivedFns = {};\n  Object.keys(computedFns_FAKE).forEach((key) => {\n    derivedFns[key] = (get) => computedFns_FAKE[key](get(proxyObject));\n  });\n  return (0,derive_valtio__WEBPACK_IMPORTED_MODULE_0__.derive)(derivedFns, { proxy: targetObject });\n}\n\nfunction proxyWithComputed_DEPRECATED(initialObject, computedFns) {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      'proxyWithComputed is deprecated. Please follow \"Computed Properties\" guide in docs.'\n    );\n  }\n  Object.keys(computedFns).forEach((key) => {\n    if (Object.getOwnPropertyDescriptor(initialObject, key)) {\n      throw new Error(\"object property already defined\");\n    }\n    const computedFn = computedFns[key];\n    const { get, set } = typeof computedFn === \"function\" ? { get: computedFn } : computedFn;\n    const desc = {};\n    desc.get = () => get((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n    if (set) {\n      desc.set = (newValue) => set(proxyObject, newValue);\n    }\n    Object.defineProperty(initialObject, key, desc);\n  });\n  const proxyObject = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)(initialObject);\n  return proxyObject;\n}\n\nconst isObject = (x) => typeof x === \"object\" && x !== null;\nlet refSet;\nconst deepClone = (obj) => {\n  if (!refSet) {\n    refSet = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.unstable_buildProxyFunction)()[2];\n  }\n  if (!isObject(obj) || refSet.has(obj)) {\n    return obj;\n  }\n  const baseObject = Array.isArray(obj) ? [] : Object.create(Object.getPrototypeOf(obj));\n  Reflect.ownKeys(obj).forEach((key) => {\n    baseObject[key] = deepClone(obj[key]);\n  });\n  return baseObject;\n};\nfunction proxyWithHistory_DEPRECATED(initialValue, skipSubscribe = false) {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      'proxyWithHistory is deprecated. Please use the \"valtio-history\" package; refer to the docs'\n    );\n  }\n  const proxyObject = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)({\n    value: initialValue,\n    history: (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.ref)({\n      wip: void 0,\n      // to avoid infinite loop\n      snapshots: [],\n      index: -1\n    }),\n    clone: deepClone,\n    canUndo: () => proxyObject.history.index > 0,\n    undo: () => {\n      if (proxyObject.canUndo()) {\n        proxyObject.value = proxyObject.history.wip = proxyObject.clone(\n          proxyObject.history.snapshots[--proxyObject.history.index]\n        );\n      }\n    },\n    canRedo: () => proxyObject.history.index < proxyObject.history.snapshots.length - 1,\n    redo: () => {\n      if (proxyObject.canRedo()) {\n        proxyObject.value = proxyObject.history.wip = proxyObject.clone(\n          proxyObject.history.snapshots[++proxyObject.history.index]\n        );\n      }\n    },\n    saveHistory: () => {\n      proxyObject.history.snapshots.splice(proxyObject.history.index + 1);\n      proxyObject.history.snapshots.push((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject).value);\n      ++proxyObject.history.index;\n    },\n    subscribe: () => (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.subscribe)(proxyObject, (ops) => {\n      if (ops.every(\n        (op) => op[1][0] === \"value\" && (op[0] !== \"set\" || op[2] !== proxyObject.history.wip)\n      )) {\n        proxyObject.saveHistory();\n      }\n    })\n  });\n  proxyObject.saveHistory();\n  if (!skipSubscribe) {\n    proxyObject.subscribe();\n  }\n  return proxyObject;\n}\n\nfunction proxySet(initialValues) {\n  const set = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)({\n    data: Array.from(new Set(initialValues)),\n    has(value) {\n      return this.data.indexOf(value) !== -1;\n    },\n    add(value) {\n      let hasProxy = false;\n      if (typeof value === \"object\" && value !== null) {\n        hasProxy = this.data.indexOf((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)(value)) !== -1;\n      }\n      if (this.data.indexOf(value) === -1 && !hasProxy) {\n        this.data.push(value);\n      }\n      return this;\n    },\n    delete(value) {\n      const index = this.data.indexOf(value);\n      if (index === -1) {\n        return false;\n      }\n      this.data.splice(index, 1);\n      return true;\n    },\n    clear() {\n      this.data.splice(0);\n    },\n    get size() {\n      return this.data.length;\n    },\n    forEach(cb) {\n      this.data.forEach((value) => {\n        cb(value, value, this);\n      });\n    },\n    get [Symbol.toStringTag]() {\n      return \"Set\";\n    },\n    toJSON() {\n      return new Set(this.data);\n    },\n    [Symbol.iterator]() {\n      return this.data[Symbol.iterator]();\n    },\n    values() {\n      return this.data.values();\n    },\n    keys() {\n      return this.data.values();\n    },\n    entries() {\n      return new Set(this.data).entries();\n    }\n  });\n  Object.defineProperties(set, {\n    data: {\n      enumerable: false\n    },\n    size: {\n      enumerable: false\n    },\n    toJSON: {\n      enumerable: false\n    }\n  });\n  Object.seal(set);\n  return set;\n}\n\nfunction proxyMap(entries) {\n  const map = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)({\n    data: Array.from(entries || []),\n    has(key) {\n      return this.data.some((p) => p[0] === key);\n    },\n    set(key, value) {\n      const record = this.data.find((p) => p[0] === key);\n      if (record) {\n        record[1] = value;\n      } else {\n        this.data.push([key, value]);\n      }\n      return this;\n    },\n    get(key) {\n      var _a;\n      return (_a = this.data.find((p) => p[0] === key)) == null ? void 0 : _a[1];\n    },\n    delete(key) {\n      const index = this.data.findIndex((p) => p[0] === key);\n      if (index === -1) {\n        return false;\n      }\n      this.data.splice(index, 1);\n      return true;\n    },\n    clear() {\n      this.data.splice(0);\n    },\n    get size() {\n      return this.data.length;\n    },\n    toJSON() {\n      return new Map(this.data);\n    },\n    forEach(cb) {\n      this.data.forEach((p) => {\n        cb(p[1], p[0], this);\n      });\n    },\n    keys() {\n      return this.data.map((p) => p[0]).values();\n    },\n    values() {\n      return this.data.map((p) => p[1]).values();\n    },\n    entries() {\n      return new Map(this.data).entries();\n    },\n    get [Symbol.toStringTag]() {\n      return \"Map\";\n    },\n    [Symbol.iterator]() {\n      return this.entries();\n    }\n  });\n  Object.defineProperties(map, {\n    data: {\n      enumerable: false\n    },\n    size: {\n      enumerable: false\n    },\n    toJSON: {\n      enumerable: false\n    }\n  });\n  Object.seal(map);\n  return map;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/valtio/esm/vanilla/utils.mjs\n");

/***/ })

};
;