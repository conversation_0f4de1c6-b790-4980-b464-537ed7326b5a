"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pino";
exports.ids = ["vendor-chunks/pino"];
exports.modules = {

/***/ "(ssr)/./node_modules/pino/lib/caller.js":
/*!*****************************************!*\
  !*** ./node_modules/pino/lib/caller.js ***!
  \*****************************************/
/***/ ((module) => {

eval("\n\nfunction noOpPrepareStackTrace (_, stack) {\n  return stack\n}\n\nmodule.exports = function getCallers () {\n  const originalPrepare = Error.prepareStackTrace\n  Error.prepareStackTrace = noOpPrepareStackTrace\n  const stack = new Error().stack\n  Error.prepareStackTrace = originalPrepare\n\n  if (!Array.isArray(stack)) {\n    return undefined\n  }\n\n  const entries = stack.slice(2)\n\n  const fileNames = []\n\n  for (const entry of entries) {\n    if (!entry) {\n      continue\n    }\n\n    fileNames.push(entry.getFileName())\n  }\n\n  return fileNames\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGluby9saWIvY2FsbGVyLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvcGluby9saWIvY2FsbGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5mdW5jdGlvbiBub09wUHJlcGFyZVN0YWNrVHJhY2UgKF8sIHN0YWNrKSB7XG4gIHJldHVybiBzdGFja1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIGdldENhbGxlcnMgKCkge1xuICBjb25zdCBvcmlnaW5hbFByZXBhcmUgPSBFcnJvci5wcmVwYXJlU3RhY2tUcmFjZVxuICBFcnJvci5wcmVwYXJlU3RhY2tUcmFjZSA9IG5vT3BQcmVwYXJlU3RhY2tUcmFjZVxuICBjb25zdCBzdGFjayA9IG5ldyBFcnJvcigpLnN0YWNrXG4gIEVycm9yLnByZXBhcmVTdGFja1RyYWNlID0gb3JpZ2luYWxQcmVwYXJlXG5cbiAgaWYgKCFBcnJheS5pc0FycmF5KHN0YWNrKSkge1xuICAgIHJldHVybiB1bmRlZmluZWRcbiAgfVxuXG4gIGNvbnN0IGVudHJpZXMgPSBzdGFjay5zbGljZSgyKVxuXG4gIGNvbnN0IGZpbGVOYW1lcyA9IFtdXG5cbiAgZm9yIChjb25zdCBlbnRyeSBvZiBlbnRyaWVzKSB7XG4gICAgaWYgKCFlbnRyeSkge1xuICAgICAgY29udGludWVcbiAgICB9XG5cbiAgICBmaWxlTmFtZXMucHVzaChlbnRyeS5nZXRGaWxlTmFtZSgpKVxuICB9XG5cbiAgcmV0dXJuIGZpbGVOYW1lc1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/caller.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/deprecations.js":
/*!***********************************************!*\
  !*** ./node_modules/pino/lib/deprecations.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst warning = __webpack_require__(/*! process-warning */ \"(ssr)/./node_modules/process-warning/index.js\")()\nmodule.exports = warning\n\nconst warnName = 'PinoWarning'\n\nwarning.create(warnName, 'PINODEP008', 'prettyPrint is deprecated, look at https://github.com/pinojs/pino-pretty for alternatives.')\n\nwarning.create(warnName, 'PINODEP009', 'The use of pino.final is discouraged in Node.js v14+ and not required. It will be removed in the next major version')\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGluby9saWIvZGVwcmVjYXRpb25zLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGdCQUFnQixtQkFBTyxDQUFDLHNFQUFpQjtBQUN6Qzs7QUFFQTs7QUFFQTs7QUFFQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9waW5vL2xpYi9kZXByZWNhdGlvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IHdhcm5pbmcgPSByZXF1aXJlKCdwcm9jZXNzLXdhcm5pbmcnKSgpXG5tb2R1bGUuZXhwb3J0cyA9IHdhcm5pbmdcblxuY29uc3Qgd2Fybk5hbWUgPSAnUGlub1dhcm5pbmcnXG5cbndhcm5pbmcuY3JlYXRlKHdhcm5OYW1lLCAnUElOT0RFUDAwOCcsICdwcmV0dHlQcmludCBpcyBkZXByZWNhdGVkLCBsb29rIGF0IGh0dHBzOi8vZ2l0aHViLmNvbS9waW5vanMvcGluby1wcmV0dHkgZm9yIGFsdGVybmF0aXZlcy4nKVxuXG53YXJuaW5nLmNyZWF0ZSh3YXJuTmFtZSwgJ1BJTk9ERVAwMDknLCAnVGhlIHVzZSBvZiBwaW5vLmZpbmFsIGlzIGRpc2NvdXJhZ2VkIGluIE5vZGUuanMgdjE0KyBhbmQgbm90IHJlcXVpcmVkLiBJdCB3aWxsIGJlIHJlbW92ZWQgaW4gdGhlIG5leHQgbWFqb3IgdmVyc2lvbicpXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/deprecations.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/levels.js":
/*!*****************************************!*\
  !*** ./node_modules/pino/lib/levels.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n/* eslint no-prototype-builtins: 0 */\nconst {\n  lsCacheSym,\n  levelValSym,\n  useOnlyCustomLevelsSym,\n  streamSym,\n  formattersSym,\n  hooksSym\n} = __webpack_require__(/*! ./symbols */ \"(ssr)/./node_modules/pino/lib/symbols.js\")\nconst { noop, genLog } = __webpack_require__(/*! ./tools */ \"(ssr)/./node_modules/pino/lib/tools.js\")\n\nconst levels = {\n  trace: 10,\n  debug: 20,\n  info: 30,\n  warn: 40,\n  error: 50,\n  fatal: 60\n}\nconst levelMethods = {\n  fatal: (hook) => {\n    const logFatal = genLog(levels.fatal, hook)\n    return function (...args) {\n      const stream = this[streamSym]\n      logFatal.call(this, ...args)\n      if (typeof stream.flushSync === 'function') {\n        try {\n          stream.flushSync()\n        } catch (e) {\n          // https://github.com/pinojs/pino/pull/740#discussion_r346788313\n        }\n      }\n    }\n  },\n  error: (hook) => genLog(levels.error, hook),\n  warn: (hook) => genLog(levels.warn, hook),\n  info: (hook) => genLog(levels.info, hook),\n  debug: (hook) => genLog(levels.debug, hook),\n  trace: (hook) => genLog(levels.trace, hook)\n}\n\nconst nums = Object.keys(levels).reduce((o, k) => {\n  o[levels[k]] = k\n  return o\n}, {})\n\nconst initialLsCache = Object.keys(nums).reduce((o, k) => {\n  o[k] = '{\"level\":' + Number(k)\n  return o\n}, {})\n\nfunction genLsCache (instance) {\n  const formatter = instance[formattersSym].level\n  const { labels } = instance.levels\n  const cache = {}\n  for (const label in labels) {\n    const level = formatter(labels[label], Number(label))\n    cache[label] = JSON.stringify(level).slice(0, -1)\n  }\n  instance[lsCacheSym] = cache\n  return instance\n}\n\nfunction isStandardLevel (level, useOnlyCustomLevels) {\n  if (useOnlyCustomLevels) {\n    return false\n  }\n\n  switch (level) {\n    case 'fatal':\n    case 'error':\n    case 'warn':\n    case 'info':\n    case 'debug':\n    case 'trace':\n      return true\n    default:\n      return false\n  }\n}\n\nfunction setLevel (level) {\n  const { labels, values } = this.levels\n  if (typeof level === 'number') {\n    if (labels[level] === undefined) throw Error('unknown level value' + level)\n    level = labels[level]\n  }\n  if (values[level] === undefined) throw Error('unknown level ' + level)\n  const preLevelVal = this[levelValSym]\n  const levelVal = this[levelValSym] = values[level]\n  const useOnlyCustomLevelsVal = this[useOnlyCustomLevelsSym]\n  const hook = this[hooksSym].logMethod\n\n  for (const key in values) {\n    if (levelVal > values[key]) {\n      this[key] = noop\n      continue\n    }\n    this[key] = isStandardLevel(key, useOnlyCustomLevelsVal) ? levelMethods[key](hook) : genLog(values[key], hook)\n  }\n\n  this.emit(\n    'level-change',\n    level,\n    levelVal,\n    labels[preLevelVal],\n    preLevelVal\n  )\n}\n\nfunction getLevel (level) {\n  const { levels, levelVal } = this\n  // protection against potential loss of Pino scope from serializers (edge case with circular refs - https://github.com/pinojs/pino/issues/833)\n  return (levels && levels.labels) ? levels.labels[levelVal] : ''\n}\n\nfunction isLevelEnabled (logLevel) {\n  const { values } = this.levels\n  const logLevelVal = values[logLevel]\n  return logLevelVal !== undefined && (logLevelVal >= this[levelValSym])\n}\n\nfunction mappings (customLevels = null, useOnlyCustomLevels = false) {\n  const customNums = customLevels\n    /* eslint-disable */\n    ? Object.keys(customLevels).reduce((o, k) => {\n        o[customLevels[k]] = k\n        return o\n      }, {})\n    : null\n    /* eslint-enable */\n\n  const labels = Object.assign(\n    Object.create(Object.prototype, { Infinity: { value: 'silent' } }),\n    useOnlyCustomLevels ? null : nums,\n    customNums\n  )\n  const values = Object.assign(\n    Object.create(Object.prototype, { silent: { value: Infinity } }),\n    useOnlyCustomLevels ? null : levels,\n    customLevels\n  )\n  return { labels, values }\n}\n\nfunction assertDefaultLevelFound (defaultLevel, customLevels, useOnlyCustomLevels) {\n  if (typeof defaultLevel === 'number') {\n    const values = [].concat(\n      Object.keys(customLevels || {}).map(key => customLevels[key]),\n      useOnlyCustomLevels ? [] : Object.keys(nums).map(level => +level),\n      Infinity\n    )\n    if (!values.includes(defaultLevel)) {\n      throw Error(`default level:${defaultLevel} must be included in custom levels`)\n    }\n    return\n  }\n\n  const labels = Object.assign(\n    Object.create(Object.prototype, { silent: { value: Infinity } }),\n    useOnlyCustomLevels ? null : levels,\n    customLevels\n  )\n  if (!(defaultLevel in labels)) {\n    throw Error(`default level:${defaultLevel} must be included in custom levels`)\n  }\n}\n\nfunction assertNoLevelCollisions (levels, customLevels) {\n  const { labels, values } = levels\n  for (const k in customLevels) {\n    if (k in values) {\n      throw Error('levels cannot be overridden')\n    }\n    if (customLevels[k] in labels) {\n      throw Error('pre-existing level values cannot be used for new levels')\n    }\n  }\n}\n\nmodule.exports = {\n  initialLsCache,\n  genLsCache,\n  levelMethods,\n  getLevel,\n  setLevel,\n  isLevelEnabled,\n  mappings,\n  levels,\n  assertNoLevelCollisions,\n  assertDefaultLevelFound\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/levels.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/meta.js":
/*!***************************************!*\
  !*** ./node_modules/pino/lib/meta.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { version } = __webpack_require__(/*! ../package.json */ \"(ssr)/./node_modules/pino/package.json\")\n\nmodule.exports = { version }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGluby9saWIvbWV0YS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixRQUFRLFVBQVUsRUFBRSxtQkFBTyxDQUFDLCtEQUFpQjs7QUFFN0MsbUJBQW1CIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3Bpbm8vbGliL21ldGEuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IHsgdmVyc2lvbiB9ID0gcmVxdWlyZSgnLi4vcGFja2FnZS5qc29uJylcblxubW9kdWxlLmV4cG9ydHMgPSB7IHZlcnNpb24gfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/meta.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/multistream.js":
/*!**********************************************!*\
  !*** ./node_modules/pino/lib/multistream.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst metadata = Symbol.for('pino.metadata')\nconst { levels } = __webpack_require__(/*! ./levels */ \"(ssr)/./node_modules/pino/lib/levels.js\")\n\nconst defaultLevels = Object.create(levels)\ndefaultLevels.silent = Infinity\n\nconst DEFAULT_INFO_LEVEL = levels.info\n\nfunction multistream (streamsArray, opts) {\n  let counter = 0\n  streamsArray = streamsArray || []\n  opts = opts || { dedupe: false }\n\n  let levels = defaultLevels\n  if (opts.levels && typeof opts.levels === 'object') {\n    levels = opts.levels\n  }\n\n  const res = {\n    write,\n    add,\n    flushSync,\n    end,\n    minLevel: 0,\n    streams: [],\n    clone,\n    [metadata]: true\n  }\n\n  if (Array.isArray(streamsArray)) {\n    streamsArray.forEach(add, res)\n  } else {\n    add.call(res, streamsArray)\n  }\n\n  // clean this object up\n  // or it will stay allocated forever\n  // as it is closed on the following closures\n  streamsArray = null\n\n  return res\n\n  // we can exit early because the streams are ordered by level\n  function write (data) {\n    let dest\n    const level = this.lastLevel\n    const { streams } = this\n    let stream\n    for (let i = 0; i < streams.length; i++) {\n      dest = streams[i]\n      if (dest.level <= level) {\n        stream = dest.stream\n        if (stream[metadata]) {\n          const { lastTime, lastMsg, lastObj, lastLogger } = this\n          stream.lastLevel = level\n          stream.lastTime = lastTime\n          stream.lastMsg = lastMsg\n          stream.lastObj = lastObj\n          stream.lastLogger = lastLogger\n        }\n        if (!opts.dedupe || dest.level === level) {\n          stream.write(data)\n        }\n      } else {\n        break\n      }\n    }\n  }\n\n  function flushSync () {\n    for (const { stream } of this.streams) {\n      if (typeof stream.flushSync === 'function') {\n        stream.flushSync()\n      }\n    }\n  }\n\n  function add (dest) {\n    if (!dest) {\n      return res\n    }\n\n    // Check that dest implements either StreamEntry or DestinationStream\n    const isStream = typeof dest.write === 'function' || dest.stream\n    const stream_ = dest.write ? dest : dest.stream\n    // This is necessary to provide a meaningful error message, otherwise it throws somewhere inside write()\n    if (!isStream) {\n      throw Error('stream object needs to implement either StreamEntry or DestinationStream interface')\n    }\n\n    const { streams } = this\n\n    let level\n    if (typeof dest.levelVal === 'number') {\n      level = dest.levelVal\n    } else if (typeof dest.level === 'string') {\n      level = levels[dest.level]\n    } else if (typeof dest.level === 'number') {\n      level = dest.level\n    } else {\n      level = DEFAULT_INFO_LEVEL\n    }\n\n    const dest_ = {\n      stream: stream_,\n      level,\n      levelVal: undefined,\n      id: counter++\n    }\n\n    streams.unshift(dest_)\n    streams.sort(compareByLevel)\n\n    this.minLevel = streams[0].level\n\n    return res\n  }\n\n  function end () {\n    for (const { stream } of this.streams) {\n      if (typeof stream.flushSync === 'function') {\n        stream.flushSync()\n      }\n      stream.end()\n    }\n  }\n\n  function clone (level) {\n    const streams = new Array(this.streams.length)\n\n    for (let i = 0; i < streams.length; i++) {\n      streams[i] = {\n        level: level,\n        stream: this.streams[i].stream\n      }\n    }\n\n    return {\n      write,\n      add,\n      minLevel: level,\n      streams,\n      clone,\n      flushSync,\n      [metadata]: true\n    }\n  }\n}\n\nfunction compareByLevel (a, b) {\n  return a.level - b.level\n}\n\nmodule.exports = multistream\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGluby9saWIvbXVsdGlzdHJlYW0uanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQSxRQUFRLFNBQVMsRUFBRSxtQkFBTyxDQUFDLHlEQUFVOztBQUVyQztBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjs7QUFFbkI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxVQUFVO0FBQ3RCO0FBQ0Esb0JBQW9CLG9CQUFvQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix5Q0FBeUM7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsaUJBQWlCLFNBQVM7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxZQUFZLFVBQVU7O0FBRXRCO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxpQkFBaUIsU0FBUztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSxvQkFBb0Isb0JBQW9CO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvcGluby9saWIvbXVsdGlzdHJlYW0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IG1ldGFkYXRhID0gU3ltYm9sLmZvcigncGluby5tZXRhZGF0YScpXG5jb25zdCB7IGxldmVscyB9ID0gcmVxdWlyZSgnLi9sZXZlbHMnKVxuXG5jb25zdCBkZWZhdWx0TGV2ZWxzID0gT2JqZWN0LmNyZWF0ZShsZXZlbHMpXG5kZWZhdWx0TGV2ZWxzLnNpbGVudCA9IEluZmluaXR5XG5cbmNvbnN0IERFRkFVTFRfSU5GT19MRVZFTCA9IGxldmVscy5pbmZvXG5cbmZ1bmN0aW9uIG11bHRpc3RyZWFtIChzdHJlYW1zQXJyYXksIG9wdHMpIHtcbiAgbGV0IGNvdW50ZXIgPSAwXG4gIHN0cmVhbXNBcnJheSA9IHN0cmVhbXNBcnJheSB8fCBbXVxuICBvcHRzID0gb3B0cyB8fCB7IGRlZHVwZTogZmFsc2UgfVxuXG4gIGxldCBsZXZlbHMgPSBkZWZhdWx0TGV2ZWxzXG4gIGlmIChvcHRzLmxldmVscyAmJiB0eXBlb2Ygb3B0cy5sZXZlbHMgPT09ICdvYmplY3QnKSB7XG4gICAgbGV2ZWxzID0gb3B0cy5sZXZlbHNcbiAgfVxuXG4gIGNvbnN0IHJlcyA9IHtcbiAgICB3cml0ZSxcbiAgICBhZGQsXG4gICAgZmx1c2hTeW5jLFxuICAgIGVuZCxcbiAgICBtaW5MZXZlbDogMCxcbiAgICBzdHJlYW1zOiBbXSxcbiAgICBjbG9uZSxcbiAgICBbbWV0YWRhdGFdOiB0cnVlXG4gIH1cblxuICBpZiAoQXJyYXkuaXNBcnJheShzdHJlYW1zQXJyYXkpKSB7XG4gICAgc3RyZWFtc0FycmF5LmZvckVhY2goYWRkLCByZXMpXG4gIH0gZWxzZSB7XG4gICAgYWRkLmNhbGwocmVzLCBzdHJlYW1zQXJyYXkpXG4gIH1cblxuICAvLyBjbGVhbiB0aGlzIG9iamVjdCB1cFxuICAvLyBvciBpdCB3aWxsIHN0YXkgYWxsb2NhdGVkIGZvcmV2ZXJcbiAgLy8gYXMgaXQgaXMgY2xvc2VkIG9uIHRoZSBmb2xsb3dpbmcgY2xvc3VyZXNcbiAgc3RyZWFtc0FycmF5ID0gbnVsbFxuXG4gIHJldHVybiByZXNcblxuICAvLyB3ZSBjYW4gZXhpdCBlYXJseSBiZWNhdXNlIHRoZSBzdHJlYW1zIGFyZSBvcmRlcmVkIGJ5IGxldmVsXG4gIGZ1bmN0aW9uIHdyaXRlIChkYXRhKSB7XG4gICAgbGV0IGRlc3RcbiAgICBjb25zdCBsZXZlbCA9IHRoaXMubGFzdExldmVsXG4gICAgY29uc3QgeyBzdHJlYW1zIH0gPSB0aGlzXG4gICAgbGV0IHN0cmVhbVxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc3RyZWFtcy5sZW5ndGg7IGkrKykge1xuICAgICAgZGVzdCA9IHN0cmVhbXNbaV1cbiAgICAgIGlmIChkZXN0LmxldmVsIDw9IGxldmVsKSB7XG4gICAgICAgIHN0cmVhbSA9IGRlc3Quc3RyZWFtXG4gICAgICAgIGlmIChzdHJlYW1bbWV0YWRhdGFdKSB7XG4gICAgICAgICAgY29uc3QgeyBsYXN0VGltZSwgbGFzdE1zZywgbGFzdE9iaiwgbGFzdExvZ2dlciB9ID0gdGhpc1xuICAgICAgICAgIHN0cmVhbS5sYXN0TGV2ZWwgPSBsZXZlbFxuICAgICAgICAgIHN0cmVhbS5sYXN0VGltZSA9IGxhc3RUaW1lXG4gICAgICAgICAgc3RyZWFtLmxhc3RNc2cgPSBsYXN0TXNnXG4gICAgICAgICAgc3RyZWFtLmxhc3RPYmogPSBsYXN0T2JqXG4gICAgICAgICAgc3RyZWFtLmxhc3RMb2dnZXIgPSBsYXN0TG9nZ2VyXG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFvcHRzLmRlZHVwZSB8fCBkZXN0LmxldmVsID09PSBsZXZlbCkge1xuICAgICAgICAgIHN0cmVhbS53cml0ZShkYXRhKVxuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBicmVha1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIGZ1bmN0aW9uIGZsdXNoU3luYyAoKSB7XG4gICAgZm9yIChjb25zdCB7IHN0cmVhbSB9IG9mIHRoaXMuc3RyZWFtcykge1xuICAgICAgaWYgKHR5cGVvZiBzdHJlYW0uZmx1c2hTeW5jID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgIHN0cmVhbS5mbHVzaFN5bmMoKVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIGZ1bmN0aW9uIGFkZCAoZGVzdCkge1xuICAgIGlmICghZGVzdCkge1xuICAgICAgcmV0dXJuIHJlc1xuICAgIH1cblxuICAgIC8vIENoZWNrIHRoYXQgZGVzdCBpbXBsZW1lbnRzIGVpdGhlciBTdHJlYW1FbnRyeSBvciBEZXN0aW5hdGlvblN0cmVhbVxuICAgIGNvbnN0IGlzU3RyZWFtID0gdHlwZW9mIGRlc3Qud3JpdGUgPT09ICdmdW5jdGlvbicgfHwgZGVzdC5zdHJlYW1cbiAgICBjb25zdCBzdHJlYW1fID0gZGVzdC53cml0ZSA/IGRlc3QgOiBkZXN0LnN0cmVhbVxuICAgIC8vIFRoaXMgaXMgbmVjZXNzYXJ5IHRvIHByb3ZpZGUgYSBtZWFuaW5nZnVsIGVycm9yIG1lc3NhZ2UsIG90aGVyd2lzZSBpdCB0aHJvd3Mgc29tZXdoZXJlIGluc2lkZSB3cml0ZSgpXG4gICAgaWYgKCFpc1N0cmVhbSkge1xuICAgICAgdGhyb3cgRXJyb3IoJ3N0cmVhbSBvYmplY3QgbmVlZHMgdG8gaW1wbGVtZW50IGVpdGhlciBTdHJlYW1FbnRyeSBvciBEZXN0aW5hdGlvblN0cmVhbSBpbnRlcmZhY2UnKVxuICAgIH1cblxuICAgIGNvbnN0IHsgc3RyZWFtcyB9ID0gdGhpc1xuXG4gICAgbGV0IGxldmVsXG4gICAgaWYgKHR5cGVvZiBkZXN0LmxldmVsVmFsID09PSAnbnVtYmVyJykge1xuICAgICAgbGV2ZWwgPSBkZXN0LmxldmVsVmFsXG4gICAgfSBlbHNlIGlmICh0eXBlb2YgZGVzdC5sZXZlbCA9PT0gJ3N0cmluZycpIHtcbiAgICAgIGxldmVsID0gbGV2ZWxzW2Rlc3QubGV2ZWxdXG4gICAgfSBlbHNlIGlmICh0eXBlb2YgZGVzdC5sZXZlbCA9PT0gJ251bWJlcicpIHtcbiAgICAgIGxldmVsID0gZGVzdC5sZXZlbFxuICAgIH0gZWxzZSB7XG4gICAgICBsZXZlbCA9IERFRkFVTFRfSU5GT19MRVZFTFxuICAgIH1cblxuICAgIGNvbnN0IGRlc3RfID0ge1xuICAgICAgc3RyZWFtOiBzdHJlYW1fLFxuICAgICAgbGV2ZWwsXG4gICAgICBsZXZlbFZhbDogdW5kZWZpbmVkLFxuICAgICAgaWQ6IGNvdW50ZXIrK1xuICAgIH1cblxuICAgIHN0cmVhbXMudW5zaGlmdChkZXN0XylcbiAgICBzdHJlYW1zLnNvcnQoY29tcGFyZUJ5TGV2ZWwpXG5cbiAgICB0aGlzLm1pbkxldmVsID0gc3RyZWFtc1swXS5sZXZlbFxuXG4gICAgcmV0dXJuIHJlc1xuICB9XG5cbiAgZnVuY3Rpb24gZW5kICgpIHtcbiAgICBmb3IgKGNvbnN0IHsgc3RyZWFtIH0gb2YgdGhpcy5zdHJlYW1zKSB7XG4gICAgICBpZiAodHlwZW9mIHN0cmVhbS5mbHVzaFN5bmMgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgc3RyZWFtLmZsdXNoU3luYygpXG4gICAgICB9XG4gICAgICBzdHJlYW0uZW5kKClcbiAgICB9XG4gIH1cblxuICBmdW5jdGlvbiBjbG9uZSAobGV2ZWwpIHtcbiAgICBjb25zdCBzdHJlYW1zID0gbmV3IEFycmF5KHRoaXMuc3RyZWFtcy5sZW5ndGgpXG5cbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHN0cmVhbXMubGVuZ3RoOyBpKyspIHtcbiAgICAgIHN0cmVhbXNbaV0gPSB7XG4gICAgICAgIGxldmVsOiBsZXZlbCxcbiAgICAgICAgc3RyZWFtOiB0aGlzLnN0cmVhbXNbaV0uc3RyZWFtXG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIHdyaXRlLFxuICAgICAgYWRkLFxuICAgICAgbWluTGV2ZWw6IGxldmVsLFxuICAgICAgc3RyZWFtcyxcbiAgICAgIGNsb25lLFxuICAgICAgZmx1c2hTeW5jLFxuICAgICAgW21ldGFkYXRhXTogdHJ1ZVxuICAgIH1cbiAgfVxufVxuXG5mdW5jdGlvbiBjb21wYXJlQnlMZXZlbCAoYSwgYikge1xuICByZXR1cm4gYS5sZXZlbCAtIGIubGV2ZWxcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBtdWx0aXN0cmVhbVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/multistream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/proto.js":
/*!****************************************!*\
  !*** ./node_modules/pino/lib/proto.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/* eslint no-prototype-builtins: 0 */\n\nconst { EventEmitter } = __webpack_require__(/*! events */ \"events\")\nconst {\n  lsCacheSym,\n  levelValSym,\n  setLevelSym,\n  getLevelSym,\n  chindingsSym,\n  parsedChindingsSym,\n  mixinSym,\n  asJsonSym,\n  writeSym,\n  mixinMergeStrategySym,\n  timeSym,\n  timeSliceIndexSym,\n  streamSym,\n  serializersSym,\n  formattersSym,\n  useOnlyCustomLevelsSym,\n  needsMetadataGsym,\n  redactFmtSym,\n  stringifySym,\n  formatOptsSym,\n  stringifiersSym\n} = __webpack_require__(/*! ./symbols */ \"(ssr)/./node_modules/pino/lib/symbols.js\")\nconst {\n  getLevel,\n  setLevel,\n  isLevelEnabled,\n  mappings,\n  initialLsCache,\n  genLsCache,\n  assertNoLevelCollisions\n} = __webpack_require__(/*! ./levels */ \"(ssr)/./node_modules/pino/lib/levels.js\")\nconst {\n  asChindings,\n  asJson,\n  buildFormatters,\n  stringify\n} = __webpack_require__(/*! ./tools */ \"(ssr)/./node_modules/pino/lib/tools.js\")\nconst {\n  version\n} = __webpack_require__(/*! ./meta */ \"(ssr)/./node_modules/pino/lib/meta.js\")\nconst redaction = __webpack_require__(/*! ./redaction */ \"(ssr)/./node_modules/pino/lib/redaction.js\")\n\n// note: use of class is satirical\n// https://github.com/pinojs/pino/pull/433#pullrequestreview-127703127\nconst constructor = class Pino {}\nconst prototype = {\n  constructor,\n  child,\n  bindings,\n  setBindings,\n  flush,\n  isLevelEnabled,\n  version,\n  get level () { return this[getLevelSym]() },\n  set level (lvl) { this[setLevelSym](lvl) },\n  get levelVal () { return this[levelValSym] },\n  set levelVal (n) { throw Error('levelVal is read-only') },\n  [lsCacheSym]: initialLsCache,\n  [writeSym]: write,\n  [asJsonSym]: asJson,\n  [getLevelSym]: getLevel,\n  [setLevelSym]: setLevel\n}\n\nObject.setPrototypeOf(prototype, EventEmitter.prototype)\n\n// exporting and consuming the prototype object using factory pattern fixes scoping issues with getters when serializing\nmodule.exports = function () {\n  return Object.create(prototype)\n}\n\nconst resetChildingsFormatter = bindings => bindings\nfunction child (bindings, options) {\n  if (!bindings) {\n    throw Error('missing bindings for child Pino')\n  }\n  options = options || {} // default options to empty object\n  const serializers = this[serializersSym]\n  const formatters = this[formattersSym]\n  const instance = Object.create(this)\n\n  if (options.hasOwnProperty('serializers') === true) {\n    instance[serializersSym] = Object.create(null)\n\n    for (const k in serializers) {\n      instance[serializersSym][k] = serializers[k]\n    }\n    const parentSymbols = Object.getOwnPropertySymbols(serializers)\n    /* eslint no-var: off */\n    for (var i = 0; i < parentSymbols.length; i++) {\n      const ks = parentSymbols[i]\n      instance[serializersSym][ks] = serializers[ks]\n    }\n\n    for (const bk in options.serializers) {\n      instance[serializersSym][bk] = options.serializers[bk]\n    }\n    const bindingsSymbols = Object.getOwnPropertySymbols(options.serializers)\n    for (var bi = 0; bi < bindingsSymbols.length; bi++) {\n      const bks = bindingsSymbols[bi]\n      instance[serializersSym][bks] = options.serializers[bks]\n    }\n  } else instance[serializersSym] = serializers\n  if (options.hasOwnProperty('formatters')) {\n    const { level, bindings: chindings, log } = options.formatters\n    instance[formattersSym] = buildFormatters(\n      level || formatters.level,\n      chindings || resetChildingsFormatter,\n      log || formatters.log\n    )\n  } else {\n    instance[formattersSym] = buildFormatters(\n      formatters.level,\n      resetChildingsFormatter,\n      formatters.log\n    )\n  }\n  if (options.hasOwnProperty('customLevels') === true) {\n    assertNoLevelCollisions(this.levels, options.customLevels)\n    instance.levels = mappings(options.customLevels, instance[useOnlyCustomLevelsSym])\n    genLsCache(instance)\n  }\n\n  // redact must place before asChindings and only replace if exist\n  if ((typeof options.redact === 'object' && options.redact !== null) || Array.isArray(options.redact)) {\n    instance.redact = options.redact // replace redact directly\n    const stringifiers = redaction(instance.redact, stringify)\n    const formatOpts = { stringify: stringifiers[redactFmtSym] }\n    instance[stringifySym] = stringify\n    instance[stringifiersSym] = stringifiers\n    instance[formatOptsSym] = formatOpts\n  }\n\n  instance[chindingsSym] = asChindings(instance, bindings)\n  const childLevel = options.level || this.level\n  instance[setLevelSym](childLevel)\n\n  return instance\n}\n\nfunction bindings () {\n  const chindings = this[chindingsSym]\n  const chindingsJson = `{${chindings.substr(1)}}` // at least contains ,\"pid\":7068,\"hostname\":\"myMac\"\n  const bindingsFromJson = JSON.parse(chindingsJson)\n  delete bindingsFromJson.pid\n  delete bindingsFromJson.hostname\n  return bindingsFromJson\n}\n\nfunction setBindings (newBindings) {\n  const chindings = asChindings(this, newBindings)\n  this[chindingsSym] = chindings\n  delete this[parsedChindingsSym]\n}\n\n/**\n * Default strategy for creating `mergeObject` from arguments and the result from `mixin()`.\n * Fields from `mergeObject` have higher priority in this strategy.\n *\n * @param {Object} mergeObject The object a user has supplied to the logging function.\n * @param {Object} mixinObject The result of the `mixin` method.\n * @return {Object}\n */\nfunction defaultMixinMergeStrategy (mergeObject, mixinObject) {\n  return Object.assign(mixinObject, mergeObject)\n}\n\nfunction write (_obj, msg, num) {\n  const t = this[timeSym]()\n  const mixin = this[mixinSym]\n  const mixinMergeStrategy = this[mixinMergeStrategySym] || defaultMixinMergeStrategy\n  let obj\n\n  if (_obj === undefined || _obj === null) {\n    obj = {}\n  } else if (_obj instanceof Error) {\n    obj = { err: _obj }\n    if (msg === undefined) {\n      msg = _obj.message\n    }\n  } else {\n    obj = _obj\n    if (msg === undefined && _obj.err) {\n      msg = _obj.err.message\n    }\n  }\n\n  if (mixin) {\n    obj = mixinMergeStrategy(obj, mixin(obj, num))\n  }\n\n  const s = this[asJsonSym](obj, msg, num, t)\n\n  const stream = this[streamSym]\n  if (stream[needsMetadataGsym] === true) {\n    stream.lastLevel = num\n    stream.lastObj = obj\n    stream.lastMsg = msg\n    stream.lastTime = t.slice(this[timeSliceIndexSym])\n    stream.lastLogger = this // for child loggers\n  }\n  stream.write(s)\n}\n\nfunction noop () {}\n\nfunction flush () {\n  const stream = this[streamSym]\n  if ('flush' in stream) stream.flush(noop)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/proto.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/redaction.js":
/*!********************************************!*\
  !*** ./node_modules/pino/lib/redaction.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst fastRedact = __webpack_require__(/*! fast-redact */ \"(ssr)/./node_modules/fast-redact/index.js\")\nconst { redactFmtSym, wildcardFirstSym } = __webpack_require__(/*! ./symbols */ \"(ssr)/./node_modules/pino/lib/symbols.js\")\nconst { rx, validator } = fastRedact\n\nconst validate = validator({\n  ERR_PATHS_MUST_BE_STRINGS: () => 'pino – redacted paths must be strings',\n  ERR_INVALID_PATH: (s) => `pino – redact paths array contains an invalid path (${s})`\n})\n\nconst CENSOR = '[Redacted]'\nconst strict = false // TODO should this be configurable?\n\nfunction redaction (opts, serialize) {\n  const { paths, censor } = handle(opts)\n\n  const shape = paths.reduce((o, str) => {\n    rx.lastIndex = 0\n    const first = rx.exec(str)\n    const next = rx.exec(str)\n\n    // ns is the top-level path segment, brackets + quoting removed.\n    let ns = first[1] !== undefined\n      ? first[1].replace(/^(?:\"|'|`)(.*)(?:\"|'|`)$/, '$1')\n      : first[0]\n\n    if (ns === '*') {\n      ns = wildcardFirstSym\n    }\n\n    // top level key:\n    if (next === null) {\n      o[ns] = null\n      return o\n    }\n\n    // path with at least two segments:\n    // if ns is already redacted at the top level, ignore lower level redactions\n    if (o[ns] === null) {\n      return o\n    }\n\n    const { index } = next\n    const nextPath = `${str.substr(index, str.length - 1)}`\n\n    o[ns] = o[ns] || []\n\n    // shape is a mix of paths beginning with literal values and wildcard\n    // paths [ \"a.b.c\", \"*.b.z\" ] should reduce to a shape of\n    // { \"a\": [ \"b.c\", \"b.z\" ], *: [ \"b.z\" ] }\n    // note: \"b.z\" is in both \"a\" and * arrays because \"a\" matches the wildcard.\n    // (* entry has wildcardFirstSym as key)\n    if (ns !== wildcardFirstSym && o[ns].length === 0) {\n      // first time ns's get all '*' redactions so far\n      o[ns].push(...(o[wildcardFirstSym] || []))\n    }\n\n    if (ns === wildcardFirstSym) {\n      // new * path gets added to all previously registered literal ns's.\n      Object.keys(o).forEach(function (k) {\n        if (o[k]) {\n          o[k].push(nextPath)\n        }\n      })\n    }\n\n    o[ns].push(nextPath)\n    return o\n  }, {})\n\n  // the redactor assigned to the format symbol key\n  // provides top level redaction for instances where\n  // an object is interpolated into the msg string\n  const result = {\n    [redactFmtSym]: fastRedact({ paths, censor, serialize, strict })\n  }\n\n  const topCensor = (...args) => {\n    return typeof censor === 'function' ? serialize(censor(...args)) : serialize(censor)\n  }\n\n  return [...Object.keys(shape), ...Object.getOwnPropertySymbols(shape)].reduce((o, k) => {\n    // top level key:\n    if (shape[k] === null) {\n      o[k] = (value) => topCensor(value, [k])\n    } else {\n      const wrappedCensor = typeof censor === 'function'\n        ? (value, path) => {\n            return censor(value, [k, ...path])\n          }\n        : censor\n      o[k] = fastRedact({\n        paths: shape[k],\n        censor: wrappedCensor,\n        serialize,\n        strict\n      })\n    }\n    return o\n  }, result)\n}\n\nfunction handle (opts) {\n  if (Array.isArray(opts)) {\n    opts = { paths: opts, censor: CENSOR }\n    validate(opts)\n    return opts\n  }\n  let { paths, censor = CENSOR, remove } = opts\n  if (Array.isArray(paths) === false) { throw Error('pino – redact must contain an array of strings') }\n  if (remove === true) censor = undefined\n  validate({ paths, censor })\n\n  return { paths, censor }\n}\n\nmodule.exports = redaction\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/redaction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/symbols.js":
/*!******************************************!*\
  !*** ./node_modules/pino/lib/symbols.js ***!
  \******************************************/
/***/ ((module) => {

eval("\n\nconst setLevelSym = Symbol('pino.setLevel')\nconst getLevelSym = Symbol('pino.getLevel')\nconst levelValSym = Symbol('pino.levelVal')\nconst useLevelLabelsSym = Symbol('pino.useLevelLabels')\nconst useOnlyCustomLevelsSym = Symbol('pino.useOnlyCustomLevels')\nconst mixinSym = Symbol('pino.mixin')\n\nconst lsCacheSym = Symbol('pino.lsCache')\nconst chindingsSym = Symbol('pino.chindings')\nconst parsedChindingsSym = Symbol('pino.parsedChindings')\n\nconst asJsonSym = Symbol('pino.asJson')\nconst writeSym = Symbol('pino.write')\nconst redactFmtSym = Symbol('pino.redactFmt')\n\nconst timeSym = Symbol('pino.time')\nconst timeSliceIndexSym = Symbol('pino.timeSliceIndex')\nconst streamSym = Symbol('pino.stream')\nconst stringifySym = Symbol('pino.stringify')\nconst stringifySafeSym = Symbol('pino.stringifySafe')\nconst stringifiersSym = Symbol('pino.stringifiers')\nconst endSym = Symbol('pino.end')\nconst formatOptsSym = Symbol('pino.formatOpts')\nconst messageKeySym = Symbol('pino.messageKey')\nconst nestedKeySym = Symbol('pino.nestedKey')\nconst nestedKeyStrSym = Symbol('pino.nestedKeyStr')\nconst mixinMergeStrategySym = Symbol('pino.mixinMergeStrategy')\n\nconst wildcardFirstSym = Symbol('pino.wildcardFirst')\n\n// public symbols, no need to use the same pino\n// version for these\nconst serializersSym = Symbol.for('pino.serializers')\nconst formattersSym = Symbol.for('pino.formatters')\nconst hooksSym = Symbol.for('pino.hooks')\nconst needsMetadataGsym = Symbol.for('pino.metadata')\n\nmodule.exports = {\n  setLevelSym,\n  getLevelSym,\n  levelValSym,\n  useLevelLabelsSym,\n  mixinSym,\n  lsCacheSym,\n  chindingsSym,\n  parsedChindingsSym,\n  asJsonSym,\n  writeSym,\n  serializersSym,\n  redactFmtSym,\n  timeSym,\n  timeSliceIndexSym,\n  streamSym,\n  stringifySym,\n  stringifySafeSym,\n  stringifiersSym,\n  endSym,\n  formatOptsSym,\n  messageKeySym,\n  nestedKeySym,\n  wildcardFirstSym,\n  needsMetadataGsym,\n  useOnlyCustomLevelsSym,\n  formattersSym,\n  hooksSym,\n  nestedKeyStrSym,\n  mixinMergeStrategySym\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/symbols.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/time.js":
/*!***************************************!*\
  !*** ./node_modules/pino/lib/time.js ***!
  \***************************************/
/***/ ((module) => {

eval("\n\nconst nullTime = () => ''\n\nconst epochTime = () => `,\"time\":${Date.now()}`\n\nconst unixTime = () => `,\"time\":${Math.round(Date.now() / 1000.0)}`\n\nconst isoTime = () => `,\"time\":\"${new Date(Date.now()).toISOString()}\"` // using Date.now() for testability\n\nmodule.exports = { nullTime, epochTime, unixTime, isoTime }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGluby9saWIvdGltZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjs7QUFFQSxtQ0FBbUMsV0FBVzs7QUFFOUMsa0NBQWtDLGdDQUFnQzs7QUFFbEUsa0NBQWtDLG1DQUFtQzs7QUFFckUsbUJBQW1CIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3Bpbm8vbGliL3RpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IG51bGxUaW1lID0gKCkgPT4gJydcblxuY29uc3QgZXBvY2hUaW1lID0gKCkgPT4gYCxcInRpbWVcIjoke0RhdGUubm93KCl9YFxuXG5jb25zdCB1bml4VGltZSA9ICgpID0+IGAsXCJ0aW1lXCI6JHtNYXRoLnJvdW5kKERhdGUubm93KCkgLyAxMDAwLjApfWBcblxuY29uc3QgaXNvVGltZSA9ICgpID0+IGAsXCJ0aW1lXCI6XCIke25ldyBEYXRlKERhdGUubm93KCkpLnRvSVNPU3RyaW5nKCl9XCJgIC8vIHVzaW5nIERhdGUubm93KCkgZm9yIHRlc3RhYmlsaXR5XG5cbm1vZHVsZS5leHBvcnRzID0geyBudWxsVGltZSwgZXBvY2hUaW1lLCB1bml4VGltZSwgaXNvVGltZSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/time.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/tools.js":
/*!****************************************!*\
  !*** ./node_modules/pino/lib/tools.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/* eslint no-prototype-builtins: 0 */\n\nconst format = __webpack_require__(/*! quick-format-unescaped */ \"(ssr)/./node_modules/quick-format-unescaped/index.js\")\nconst { mapHttpRequest, mapHttpResponse } = __webpack_require__(/*! pino-std-serializers */ \"(ssr)/./node_modules/pino-std-serializers/index.js\")\nconst SonicBoom = __webpack_require__(/*! sonic-boom */ \"(ssr)/./node_modules/sonic-boom/index.js\")\nconst warning = __webpack_require__(/*! ./deprecations */ \"(ssr)/./node_modules/pino/lib/deprecations.js\")\nconst {\n  lsCacheSym,\n  chindingsSym,\n  parsedChindingsSym,\n  writeSym,\n  serializersSym,\n  formatOptsSym,\n  endSym,\n  stringifiersSym,\n  stringifySym,\n  stringifySafeSym,\n  wildcardFirstSym,\n  needsMetadataGsym,\n  redactFmtSym,\n  streamSym,\n  nestedKeySym,\n  formattersSym,\n  messageKeySym,\n  nestedKeyStrSym\n} = __webpack_require__(/*! ./symbols */ \"(ssr)/./node_modules/pino/lib/symbols.js\")\nconst { isMainThread } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\nconst transport = __webpack_require__(/*! ./transport */ \"(ssr)/./node_modules/pino/lib/transport.js\")\n\nfunction noop () {}\n\nfunction genLog (level, hook) {\n  if (!hook) return LOG\n\n  return function hookWrappedLog (...args) {\n    hook.call(this, args, LOG, level)\n  }\n\n  function LOG (o, ...n) {\n    if (typeof o === 'object') {\n      let msg = o\n      if (o !== null) {\n        if (o.method && o.headers && o.socket) {\n          o = mapHttpRequest(o)\n        } else if (typeof o.setHeader === 'function') {\n          o = mapHttpResponse(o)\n        }\n      }\n      let formatParams\n      if (msg === null && n.length === 0) {\n        formatParams = [null]\n      } else {\n        msg = n.shift()\n        formatParams = n\n      }\n      this[writeSym](o, format(msg, formatParams, this[formatOptsSym]), level)\n    } else {\n      this[writeSym](null, format(o, n, this[formatOptsSym]), level)\n    }\n  }\n}\n\n// magically escape strings for json\n// relying on their charCodeAt\n// everything below 32 needs JSON.stringify()\n// 34 and 92 happens all the time, so we\n// have a fast case for them\nfunction asString (str) {\n  let result = ''\n  let last = 0\n  let found = false\n  let point = 255\n  const l = str.length\n  if (l > 100) {\n    return JSON.stringify(str)\n  }\n  for (var i = 0; i < l && point >= 32; i++) {\n    point = str.charCodeAt(i)\n    if (point === 34 || point === 92) {\n      result += str.slice(last, i) + '\\\\'\n      last = i\n      found = true\n    }\n  }\n  if (!found) {\n    result = str\n  } else {\n    result += str.slice(last)\n  }\n  return point < 32 ? JSON.stringify(str) : '\"' + result + '\"'\n}\n\nfunction asJson (obj, msg, num, time) {\n  const stringify = this[stringifySym]\n  const stringifySafe = this[stringifySafeSym]\n  const stringifiers = this[stringifiersSym]\n  const end = this[endSym]\n  const chindings = this[chindingsSym]\n  const serializers = this[serializersSym]\n  const formatters = this[formattersSym]\n  const messageKey = this[messageKeySym]\n  let data = this[lsCacheSym][num] + time\n\n  // we need the child bindings added to the output first so instance logged\n  // objects can take precedence when JSON.parse-ing the resulting log line\n  data = data + chindings\n\n  let value\n  if (formatters.log) {\n    obj = formatters.log(obj)\n  }\n  const wildcardStringifier = stringifiers[wildcardFirstSym]\n  let propStr = ''\n  for (const key in obj) {\n    value = obj[key]\n    if (Object.prototype.hasOwnProperty.call(obj, key) && value !== undefined) {\n      value = serializers[key] ? serializers[key](value) : value\n\n      const stringifier = stringifiers[key] || wildcardStringifier\n\n      switch (typeof value) {\n        case 'undefined':\n        case 'function':\n          continue\n        case 'number':\n          /* eslint no-fallthrough: \"off\" */\n          if (Number.isFinite(value) === false) {\n            value = null\n          }\n        // this case explicitly falls through to the next one\n        case 'boolean':\n          if (stringifier) value = stringifier(value)\n          break\n        case 'string':\n          value = (stringifier || asString)(value)\n          break\n        default:\n          value = (stringifier || stringify)(value, stringifySafe)\n      }\n      if (value === undefined) continue\n      propStr += ',\"' + key + '\":' + value\n    }\n  }\n\n  let msgStr = ''\n  if (msg !== undefined) {\n    value = serializers[messageKey] ? serializers[messageKey](msg) : msg\n    const stringifier = stringifiers[messageKey] || wildcardStringifier\n\n    switch (typeof value) {\n      case 'function':\n        break\n      case 'number':\n        /* eslint no-fallthrough: \"off\" */\n        if (Number.isFinite(value) === false) {\n          value = null\n        }\n      // this case explicitly falls through to the next one\n      case 'boolean':\n        if (stringifier) value = stringifier(value)\n        msgStr = ',\"' + messageKey + '\":' + value\n        break\n      case 'string':\n        value = (stringifier || asString)(value)\n        msgStr = ',\"' + messageKey + '\":' + value\n        break\n      default:\n        value = (stringifier || stringify)(value, stringifySafe)\n        msgStr = ',\"' + messageKey + '\":' + value\n    }\n  }\n\n  if (this[nestedKeySym] && propStr) {\n    // place all the obj properties under the specified key\n    // the nested key is already formatted from the constructor\n    return data + this[nestedKeyStrSym] + propStr.slice(1) + '}' + msgStr + end\n  } else {\n    return data + propStr + msgStr + end\n  }\n}\n\nfunction asChindings (instance, bindings) {\n  let value\n  let data = instance[chindingsSym]\n  const stringify = instance[stringifySym]\n  const stringifySafe = instance[stringifySafeSym]\n  const stringifiers = instance[stringifiersSym]\n  const wildcardStringifier = stringifiers[wildcardFirstSym]\n  const serializers = instance[serializersSym]\n  const formatter = instance[formattersSym].bindings\n  bindings = formatter(bindings)\n\n  for (const key in bindings) {\n    value = bindings[key]\n    const valid = key !== 'level' &&\n      key !== 'serializers' &&\n      key !== 'formatters' &&\n      key !== 'customLevels' &&\n      bindings.hasOwnProperty(key) &&\n      value !== undefined\n    if (valid === true) {\n      value = serializers[key] ? serializers[key](value) : value\n      value = (stringifiers[key] || wildcardStringifier || stringify)(value, stringifySafe)\n      if (value === undefined) continue\n      data += ',\"' + key + '\":' + value\n    }\n  }\n  return data\n}\n\nfunction getPrettyStream (opts, prettifier, dest, instance) {\n  if (prettifier && typeof prettifier === 'function') {\n    prettifier = prettifier.bind(instance)\n    return prettifierMetaWrapper(prettifier(opts), dest, opts)\n  }\n  try {\n    const prettyFactory = Object(function webpackMissingModule() { var e = new Error(\"Cannot find module 'pino-pretty'\"); e.code = 'MODULE_NOT_FOUND'; throw e; }())\n    prettyFactory.asMetaWrapper = prettifierMetaWrapper\n    return prettifierMetaWrapper(prettyFactory(opts), dest, opts)\n  } catch (e) {\n    if (e.message.startsWith(\"Cannot find module 'pino-pretty'\")) {\n      throw Error('Missing `pino-pretty` module: `pino-pretty` must be installed separately')\n    };\n    throw e\n  }\n}\n\nfunction prettifierMetaWrapper (pretty, dest, opts) {\n  opts = Object.assign({ suppressFlushSyncWarning: false }, opts)\n  let warned = false\n  return {\n    [needsMetadataGsym]: true,\n    lastLevel: 0,\n    lastMsg: null,\n    lastObj: null,\n    lastLogger: null,\n    flushSync () {\n      if (opts.suppressFlushSyncWarning || warned) {\n        return\n      }\n      warned = true\n      setMetadataProps(dest, this)\n      dest.write(pretty(Object.assign({\n        level: 40, // warn\n        msg: 'pino.final with prettyPrint does not support flushing',\n        time: Date.now()\n      }, this.chindings())))\n    },\n    chindings () {\n      const lastLogger = this.lastLogger\n      let chindings = null\n\n      // protection against flushSync being called before logging\n      // anything\n      if (!lastLogger) {\n        return null\n      }\n\n      if (lastLogger.hasOwnProperty(parsedChindingsSym)) {\n        chindings = lastLogger[parsedChindingsSym]\n      } else {\n        chindings = JSON.parse('{' + lastLogger[chindingsSym].substr(1) + '}')\n        lastLogger[parsedChindingsSym] = chindings\n      }\n\n      return chindings\n    },\n    write (chunk) {\n      const lastLogger = this.lastLogger\n      const chindings = this.chindings()\n\n      let time = this.lastTime\n\n      /* istanbul ignore next */\n      if (typeof time === 'number') {\n        // do nothing!\n      } else if (time.match(/^\\d+/)) {\n        time = parseInt(time)\n      } else {\n        time = time.slice(1, -1)\n      }\n\n      const lastObj = this.lastObj\n      const lastMsg = this.lastMsg\n      const errorProps = null\n\n      const formatters = lastLogger[formattersSym]\n      const formattedObj = formatters.log ? formatters.log(lastObj) : lastObj\n\n      const messageKey = lastLogger[messageKeySym]\n      if (lastMsg && formattedObj && !Object.prototype.hasOwnProperty.call(formattedObj, messageKey)) {\n        formattedObj[messageKey] = lastMsg\n      }\n\n      const obj = Object.assign({\n        level: this.lastLevel,\n        time\n      }, formattedObj, errorProps)\n\n      const serializers = lastLogger[serializersSym]\n      const keys = Object.keys(serializers)\n\n      for (var i = 0; i < keys.length; i++) {\n        const key = keys[i]\n        if (obj[key] !== undefined) {\n          obj[key] = serializers[key](obj[key])\n        }\n      }\n\n      for (const key in chindings) {\n        if (!obj.hasOwnProperty(key)) {\n          obj[key] = chindings[key]\n        }\n      }\n\n      const stringifiers = lastLogger[stringifiersSym]\n      const redact = stringifiers[redactFmtSym]\n\n      const formatted = pretty(typeof redact === 'function' ? redact(obj) : obj)\n      if (formatted === undefined) return\n\n      setMetadataProps(dest, this)\n      dest.write(formatted)\n    }\n  }\n}\n\nfunction hasBeenTampered (stream) {\n  return stream.write !== stream.constructor.prototype.write\n}\n\nfunction buildSafeSonicBoom (opts) {\n  const stream = new SonicBoom(opts)\n  stream.on('error', filterBrokenPipe)\n  // if we are sync: false, we must flush on exit\n  if (!opts.sync && isMainThread) {\n    setupOnExit(stream)\n  }\n  return stream\n\n  function filterBrokenPipe (err) {\n    // TODO verify on Windows\n    if (err.code === 'EPIPE') {\n      // If we get EPIPE, we should stop logging here\n      // however we have no control to the consumer of\n      // SonicBoom, so we just overwrite the write method\n      stream.write = noop\n      stream.end = noop\n      stream.flushSync = noop\n      stream.destroy = noop\n      return\n    }\n    stream.removeListener('error', filterBrokenPipe)\n    stream.emit('error', err)\n  }\n}\n\nfunction setupOnExit (stream) {\n  /* istanbul ignore next */\n  if (global.WeakRef && global.WeakMap && global.FinalizationRegistry) {\n    // This is leak free, it does not leave event handlers\n    const onExit = __webpack_require__(/*! on-exit-leak-free */ \"(ssr)/./node_modules/on-exit-leak-free/index.js\")\n\n    onExit.register(stream, autoEnd)\n\n    stream.on('close', function () {\n      onExit.unregister(stream)\n    })\n  }\n}\n\nfunction autoEnd (stream, eventName) {\n  // This check is needed only on some platforms\n  /* istanbul ignore next */\n  if (stream.destroyed) {\n    return\n  }\n\n  if (eventName === 'beforeExit') {\n    // We still have an event loop, let's use it\n    stream.flush()\n    stream.on('drain', function () {\n      stream.end()\n    })\n  } else {\n    // We do not have an event loop, so flush synchronously\n    stream.flushSync()\n  }\n}\n\nfunction createArgsNormalizer (defaultOptions) {\n  return function normalizeArgs (instance, caller, opts = {}, stream) {\n    // support stream as a string\n    if (typeof opts === 'string') {\n      stream = buildSafeSonicBoom({ dest: opts, sync: true })\n      opts = {}\n    } else if (typeof stream === 'string') {\n      if (opts && opts.transport) {\n        throw Error('only one of option.transport or stream can be specified')\n      }\n      stream = buildSafeSonicBoom({ dest: stream, sync: true })\n    } else if (opts instanceof SonicBoom || opts.writable || opts._writableState) {\n      stream = opts\n      opts = {}\n    } else if (opts.transport) {\n      if (opts.transport instanceof SonicBoom || opts.transport.writable || opts.transport._writableState) {\n        throw Error('option.transport do not allow stream, please pass to option directly. e.g. pino(transport)')\n      }\n      if (opts.transport.targets && opts.transport.targets.length && opts.formatters && typeof opts.formatters.level === 'function') {\n        throw Error('option.transport.targets do not allow custom level formatters')\n      }\n\n      let customLevels\n      if (opts.customLevels) {\n        customLevels = opts.useOnlyCustomLevels ? opts.customLevels : Object.assign({}, opts.levels, opts.customLevels)\n      }\n      stream = transport({ caller, ...opts.transport, levels: customLevels })\n    }\n    opts = Object.assign({}, defaultOptions, opts)\n    opts.serializers = Object.assign({}, defaultOptions.serializers, opts.serializers)\n    opts.formatters = Object.assign({}, defaultOptions.formatters, opts.formatters)\n\n    if ('onTerminated' in opts) {\n      throw Error('The onTerminated option has been removed, use pino.final instead')\n    }\n    if ('changeLevelName' in opts) {\n      process.emitWarning(\n        'The changeLevelName option is deprecated and will be removed in v7. Use levelKey instead.',\n        { code: 'changeLevelName_deprecation' }\n      )\n      opts.levelKey = opts.changeLevelName\n      delete opts.changeLevelName\n    }\n    const { enabled, prettyPrint, prettifier, messageKey } = opts\n    if (enabled === false) opts.level = 'silent'\n    stream = stream || process.stdout\n    if (stream === process.stdout && stream.fd >= 0 && !hasBeenTampered(stream)) {\n      stream = buildSafeSonicBoom({ fd: stream.fd, sync: true })\n    }\n    if (prettyPrint) {\n      warning.emit('PINODEP008')\n      const prettyOpts = Object.assign({ messageKey }, prettyPrint)\n      stream = getPrettyStream(prettyOpts, prettifier, stream, instance)\n    }\n    return { opts, stream }\n  }\n}\n\nfunction final (logger, handler) {\n  const major = Number(process.versions.node.split('.')[0])\n  if (major >= 14) warning.emit('PINODEP009')\n\n  if (typeof logger === 'undefined' || typeof logger.child !== 'function') {\n    throw Error('expected a pino logger instance')\n  }\n  const hasHandler = (typeof handler !== 'undefined')\n  if (hasHandler && typeof handler !== 'function') {\n    throw Error('if supplied, the handler parameter should be a function')\n  }\n  const stream = logger[streamSym]\n  if (typeof stream.flushSync !== 'function') {\n    throw Error('final requires a stream that has a flushSync method, such as pino.destination')\n  }\n\n  const finalLogger = new Proxy(logger, {\n    get: (logger, key) => {\n      if (key in logger.levels.values) {\n        return (...args) => {\n          logger[key](...args)\n          stream.flushSync()\n        }\n      }\n      return logger[key]\n    }\n  })\n\n  if (!hasHandler) {\n    try {\n      stream.flushSync()\n    } catch {\n      // it's too late to wait for the stream to be ready\n      // because this is a final tick scenario.\n      // in practice there shouldn't be a situation where it isn't\n      // however, swallow the error just in case (and for easier testing)\n    }\n    return finalLogger\n  }\n\n  return (err = null, ...args) => {\n    try {\n      stream.flushSync()\n    } catch (e) {\n      // it's too late to wait for the stream to be ready\n      // because this is a final tick scenario.\n      // in practice there shouldn't be a situation where it isn't\n      // however, swallow the error just in case (and for easier testing)\n    }\n    return handler(err, finalLogger, ...args)\n  }\n}\n\nfunction stringify (obj, stringifySafeFn) {\n  try {\n    return JSON.stringify(obj)\n  } catch (_) {\n    try {\n      const stringify = stringifySafeFn || this[stringifySafeSym]\n      return stringify(obj)\n    } catch (_) {\n      return '\"[unable to serialize, circular reference is too complex to analyze]\"'\n    }\n  }\n}\n\nfunction buildFormatters (level, bindings, log) {\n  return {\n    level,\n    bindings,\n    log\n  }\n}\n\nfunction setMetadataProps (dest, that) {\n  if (dest[needsMetadataGsym] === true) {\n    dest.lastLevel = that.lastLevel\n    dest.lastMsg = that.lastMsg\n    dest.lastObj = that.lastObj\n    dest.lastTime = that.lastTime\n    dest.lastLogger = that.lastLogger\n  }\n}\n\n/**\n * Convert a string integer file descriptor to a proper native integer\n * file descriptor.\n *\n * @param {string} destination The file descriptor string to attempt to convert.\n *\n * @returns {Number}\n */\nfunction normalizeDestFileDescriptor (destination) {\n  const fd = Number(destination)\n  if (typeof destination === 'string' && Number.isFinite(fd)) {\n    return fd\n  }\n  return destination\n}\n\nmodule.exports = {\n  noop,\n  buildSafeSonicBoom,\n  getPrettyStream,\n  asChindings,\n  asJson,\n  genLog,\n  createArgsNormalizer,\n  final,\n  stringify,\n  buildFormatters,\n  normalizeDestFileDescriptor\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/tools.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/lib/transport.js":
/*!********************************************!*\
  !*** ./node_modules/pino/lib/transport.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { createRequire } = __webpack_require__(/*! module */ \"module\")\nconst getCallers = __webpack_require__(/*! ./caller */ \"(ssr)/./node_modules/pino/lib/caller.js\")\nconst { join, isAbsolute } = __webpack_require__(/*! path */ \"path\")\nconst sleep = __webpack_require__(/*! atomic-sleep */ \"(ssr)/./node_modules/atomic-sleep/index.js\")\n\nlet onExit\n\nif (global.WeakRef && global.WeakMap && global.FinalizationRegistry) {\n  // This require MUST be top level otherwise the transport would\n  // not work from within Jest as it hijacks require.\n  onExit = __webpack_require__(/*! on-exit-leak-free */ \"(ssr)/./node_modules/on-exit-leak-free/index.js\")\n}\n\nconst ThreadStream = __webpack_require__(/*! thread-stream */ \"(ssr)/./node_modules/thread-stream/index.js\")\n\nfunction setupOnExit (stream) {\n  /* istanbul ignore next */\n  if (onExit) {\n    // This is leak free, it does not leave event handlers\n    onExit.register(stream, autoEnd)\n\n    stream.on('close', function () {\n      onExit.unregister(stream)\n    })\n  } else {\n    const fn = autoEnd.bind(null, stream)\n    process.once('beforeExit', fn)\n    process.once('exit', fn)\n\n    stream.on('close', function () {\n      process.removeListener('beforeExit', fn)\n      process.removeListener('exit', fn)\n    })\n  }\n}\n\nfunction buildStream (filename, workerData, workerOpts) {\n  const stream = new ThreadStream({\n    filename,\n    workerData,\n    workerOpts\n  })\n\n  stream.on('ready', onReady)\n  stream.on('close', function () {\n    process.removeListener('exit', onExit)\n  })\n\n  process.on('exit', onExit)\n\n  function onReady () {\n    process.removeListener('exit', onExit)\n    stream.unref()\n\n    if (workerOpts.autoEnd !== false) {\n      setupOnExit(stream)\n    }\n  }\n\n  function onExit () {\n    if (stream.closed) {\n      return\n    }\n    stream.flushSync()\n    // Apparently there is a very sporadic race condition\n    // that in certain OS would prevent the messages to be flushed\n    // because the thread might not have been created still.\n    // Unfortunately we need to sleep(100) in this case.\n    sleep(100)\n    stream.end()\n  }\n\n  return stream\n}\n\nfunction autoEnd (stream) {\n  stream.ref()\n  stream.flushSync()\n  stream.end()\n  stream.once('close', function () {\n    stream.unref()\n  })\n}\n\nfunction transport (fullOptions) {\n  const { pipeline, targets, levels, options = {}, worker = {}, caller = getCallers() } = fullOptions\n\n  // Backwards compatibility\n  const callers = typeof caller === 'string' ? [caller] : caller\n\n  // This will be eventually modified by bundlers\n  const bundlerOverrides = '__bundlerPathsOverrides' in globalThis ? globalThis.__bundlerPathsOverrides : {}\n\n  let target = fullOptions.target\n\n  if (target && targets) {\n    throw new Error('only one of target or targets can be specified')\n  }\n\n  if (targets) {\n    target = bundlerOverrides['pino-worker'] || join(__dirname, 'worker.js')\n    options.targets = targets.map((dest) => {\n      return {\n        ...dest,\n        target: fixTarget(dest.target)\n      }\n    })\n  } else if (pipeline) {\n    target = bundlerOverrides['pino-pipeline-worker'] || join(__dirname, 'worker-pipeline.js')\n    options.targets = pipeline.map((dest) => {\n      return {\n        ...dest,\n        target: fixTarget(dest.target)\n      }\n    })\n  }\n\n  if (levels) {\n    options.levels = levels\n  }\n\n  return buildStream(fixTarget(target), options, worker)\n\n  function fixTarget (origin) {\n    origin = bundlerOverrides[origin] || origin\n\n    if (isAbsolute(origin) || origin.indexOf('file://') === 0) {\n      return origin\n    }\n\n    if (origin === 'pino/file') {\n      return join(__dirname, '..', 'file.js')\n    }\n\n    let fixTarget\n\n    for (const filePath of callers) {\n      try {\n        fixTarget = createRequire(filePath).resolve(origin)\n        break\n      } catch (err) {\n        // Silent catch\n        continue\n      }\n    }\n\n    if (!fixTarget) {\n      throw new Error(`unable to determine transport target for \"${origin}\"`)\n    }\n\n    return fixTarget\n  }\n}\n\nmodule.exports = transport\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/lib/transport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/pino.js":
/*!***********************************!*\
  !*** ./node_modules/pino/pino.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n/* eslint no-prototype-builtins: 0 */\nconst os = __webpack_require__(/*! os */ \"os\")\nconst stdSerializers = __webpack_require__(/*! pino-std-serializers */ \"(ssr)/./node_modules/pino-std-serializers/index.js\")\nconst caller = __webpack_require__(/*! ./lib/caller */ \"(ssr)/./node_modules/pino/lib/caller.js\")\nconst redaction = __webpack_require__(/*! ./lib/redaction */ \"(ssr)/./node_modules/pino/lib/redaction.js\")\nconst time = __webpack_require__(/*! ./lib/time */ \"(ssr)/./node_modules/pino/lib/time.js\")\nconst proto = __webpack_require__(/*! ./lib/proto */ \"(ssr)/./node_modules/pino/lib/proto.js\")\nconst symbols = __webpack_require__(/*! ./lib/symbols */ \"(ssr)/./node_modules/pino/lib/symbols.js\")\nconst { configure } = __webpack_require__(/*! safe-stable-stringify */ \"(ssr)/./node_modules/safe-stable-stringify/index.js\")\nconst { assertDefaultLevelFound, mappings, genLsCache, levels } = __webpack_require__(/*! ./lib/levels */ \"(ssr)/./node_modules/pino/lib/levels.js\")\nconst {\n  createArgsNormalizer,\n  asChindings,\n  final,\n  buildSafeSonicBoom,\n  buildFormatters,\n  stringify,\n  normalizeDestFileDescriptor,\n  noop\n} = __webpack_require__(/*! ./lib/tools */ \"(ssr)/./node_modules/pino/lib/tools.js\")\nconst { version } = __webpack_require__(/*! ./lib/meta */ \"(ssr)/./node_modules/pino/lib/meta.js\")\nconst {\n  chindingsSym,\n  redactFmtSym,\n  serializersSym,\n  timeSym,\n  timeSliceIndexSym,\n  streamSym,\n  stringifySym,\n  stringifySafeSym,\n  stringifiersSym,\n  setLevelSym,\n  endSym,\n  formatOptsSym,\n  messageKeySym,\n  nestedKeySym,\n  mixinSym,\n  useOnlyCustomLevelsSym,\n  formattersSym,\n  hooksSym,\n  nestedKeyStrSym,\n  mixinMergeStrategySym\n} = symbols\nconst { epochTime, nullTime } = time\nconst { pid } = process\nconst hostname = os.hostname()\nconst defaultErrorSerializer = stdSerializers.err\nconst defaultOptions = {\n  level: 'info',\n  levels,\n  messageKey: 'msg',\n  nestedKey: null,\n  enabled: true,\n  prettyPrint: false,\n  base: { pid, hostname },\n  serializers: Object.assign(Object.create(null), {\n    err: defaultErrorSerializer\n  }),\n  formatters: Object.assign(Object.create(null), {\n    bindings (bindings) {\n      return bindings\n    },\n    level (label, number) {\n      return { level: number }\n    }\n  }),\n  hooks: {\n    logMethod: undefined\n  },\n  timestamp: epochTime,\n  name: undefined,\n  redact: null,\n  customLevels: null,\n  useOnlyCustomLevels: false,\n  depthLimit: 5,\n  edgeLimit: 100\n}\n\nconst normalize = createArgsNormalizer(defaultOptions)\n\nconst serializers = Object.assign(Object.create(null), stdSerializers)\n\nfunction pino (...args) {\n  const instance = {}\n  const { opts, stream } = normalize(instance, caller(), ...args)\n  const {\n    redact,\n    crlf,\n    serializers,\n    timestamp,\n    messageKey,\n    nestedKey,\n    base,\n    name,\n    level,\n    customLevels,\n    mixin,\n    mixinMergeStrategy,\n    useOnlyCustomLevels,\n    formatters,\n    hooks,\n    depthLimit,\n    edgeLimit\n  } = opts\n\n  const stringifySafe = configure({\n    maximumDepth: depthLimit,\n    maximumBreadth: edgeLimit\n  })\n\n  const allFormatters = buildFormatters(\n    formatters.level,\n    formatters.bindings,\n    formatters.log\n  )\n\n  const stringifiers = redact ? redaction(redact, stringify) : {}\n  const stringifyFn = stringify.bind({\n    [stringifySafeSym]: stringifySafe\n  })\n  const formatOpts = redact\n    ? { stringify: stringifiers[redactFmtSym] }\n    : { stringify: stringifyFn }\n  const end = '}' + (crlf ? '\\r\\n' : '\\n')\n  const coreChindings = asChindings.bind(null, {\n    [chindingsSym]: '',\n    [serializersSym]: serializers,\n    [stringifiersSym]: stringifiers,\n    [stringifySym]: stringify,\n    [stringifySafeSym]: stringifySafe,\n    [formattersSym]: allFormatters\n  })\n\n  let chindings = ''\n  if (base !== null) {\n    if (name === undefined) {\n      chindings = coreChindings(base)\n    } else {\n      chindings = coreChindings(Object.assign({}, base, { name }))\n    }\n  }\n\n  const time = (timestamp instanceof Function)\n    ? timestamp\n    : (timestamp ? epochTime : nullTime)\n  const timeSliceIndex = time().indexOf(':') + 1\n\n  if (useOnlyCustomLevels && !customLevels) throw Error('customLevels is required if useOnlyCustomLevels is set true')\n  if (mixin && typeof mixin !== 'function') throw Error(`Unknown mixin type \"${typeof mixin}\" - expected \"function\"`)\n\n  assertDefaultLevelFound(level, customLevels, useOnlyCustomLevels)\n  const levels = mappings(customLevels, useOnlyCustomLevels)\n\n  Object.assign(instance, {\n    levels,\n    [useOnlyCustomLevelsSym]: useOnlyCustomLevels,\n    [streamSym]: stream,\n    [timeSym]: time,\n    [timeSliceIndexSym]: timeSliceIndex,\n    [stringifySym]: stringify,\n    [stringifySafeSym]: stringifySafe,\n    [stringifiersSym]: stringifiers,\n    [endSym]: end,\n    [formatOptsSym]: formatOpts,\n    [messageKeySym]: messageKey,\n    [nestedKeySym]: nestedKey,\n    // protect against injection\n    [nestedKeyStrSym]: nestedKey ? `,${JSON.stringify(nestedKey)}:{` : '',\n    [serializersSym]: serializers,\n    [mixinSym]: mixin,\n    [mixinMergeStrategySym]: mixinMergeStrategy,\n    [chindingsSym]: chindings,\n    [formattersSym]: allFormatters,\n    [hooksSym]: hooks,\n    silent: noop\n  })\n\n  Object.setPrototypeOf(instance, proto())\n\n  genLsCache(instance)\n\n  instance[setLevelSym](level)\n\n  return instance\n}\n\nmodule.exports = pino\n\nmodule.exports.destination = (dest = process.stdout.fd) => {\n  if (typeof dest === 'object') {\n    dest.dest = normalizeDestFileDescriptor(dest.dest || process.stdout.fd)\n    return buildSafeSonicBoom(dest)\n  } else {\n    return buildSafeSonicBoom({ dest: normalizeDestFileDescriptor(dest), minLength: 0, sync: true })\n  }\n}\n\nmodule.exports.transport = __webpack_require__(/*! ./lib/transport */ \"(ssr)/./node_modules/pino/lib/transport.js\")\nmodule.exports.multistream = __webpack_require__(/*! ./lib/multistream */ \"(ssr)/./node_modules/pino/lib/multistream.js\")\n\nmodule.exports.final = final\nmodule.exports.levels = mappings()\nmodule.exports.stdSerializers = serializers\nmodule.exports.stdTimeFunctions = Object.assign({}, time)\nmodule.exports.symbols = symbols\nmodule.exports.version = version\n\n// Enables default and name export with TypeScript and Babel\nmodule.exports[\"default\"] = pino\nmodule.exports.pino = pino\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pino/pino.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pino/package.json":
/*!****************************************!*\
  !*** ./node_modules/pino/package.json ***!
  \****************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"pino","version":"7.11.0","description":"super fast, all natural json logger","main":"pino.js","type":"commonjs","types":"pino.d.ts","browser":"./browser.js","files":["pino.js","file.js","pino.d.ts","bin.js","browser.js","pretty.js","usage.txt","test","docs","example.js","lib"],"scripts":{"docs":"docsify serve","browser-test":"airtap --local 8080 test/browser*test.js","lint":"eslint .","test":"npm run lint && npm run transpile && tap --ts && jest test/jest && npm run test-types","test-ci":"npm run lint && npm run transpile && tap --ts --no-check-coverage --coverage-report=lcovonly && npm run test-types","test-ci-pnpm":"pnpm run lint && npm run transpile && tap --ts --no-coverage --no-check-coverage && pnpm run test-types","test-ci-yarn-pnp":"yarn run lint && npm run transpile && tap --ts --no-check-coverage --coverage-report=lcovonly","test-types":"tsc && tsd && ts-node test/types/pino.ts","transpile":"node ./test/fixtures/ts/transpile.cjs","cov-ui":"tap --ts --coverage-report=html","bench":"node benchmarks/utils/runbench all","bench-basic":"node benchmarks/utils/runbench basic","bench-object":"node benchmarks/utils/runbench object","bench-deep-object":"node benchmarks/utils/runbench deep-object","bench-multi-arg":"node benchmarks/utils/runbench multi-arg","bench-longs-tring":"node benchmarks/utils/runbench long-string","bench-child":"node benchmarks/utils/runbench child","bench-child-child":"node benchmarks/utils/runbench child-child","bench-child-creation":"node benchmarks/utils/runbench child-creation","bench-formatters":"node benchmarks/utils/runbench formatters","update-bench-doc":"node benchmarks/utils/generate-benchmark-doc > docs/benchmarks.md"},"bin":{"pino":"./bin.js"},"precommit":"test","repository":{"type":"git","url":"git+https://github.com/pinojs/pino.git"},"keywords":["fast","logger","stream","json"],"author":"Matteo Collina <<EMAIL>>","contributors":["David Mark Clements <<EMAIL>>","James Sumners <<EMAIL>>","Thomas Watson Steen <<EMAIL>> (https://twitter.com/wa7son)"],"license":"MIT","bugs":{"url":"https://github.com/pinojs/pino/issues"},"homepage":"http://getpino.io","devDependencies":{"@types/flush-write-stream":"^1.0.0","@types/node":"^17.0.0","@types/tap":"^15.0.6","airtap":"4.0.4","benchmark":"^2.1.4","bole":"^4.0.0","bunyan":"^1.8.14","docsify-cli":"^4.4.1","eslint":"^7.17.0","eslint-config-standard":"^16.0.3","eslint-plugin-import":"^2.22.1","eslint-plugin-node":"^11.1.0","eslint-plugin-promise":"^5.1.0","execa":"^5.0.0","fastbench":"^1.0.1","flush-write-stream":"^2.0.0","import-fresh":"^3.2.1","jest":"^27.3.1","log":"^6.0.0","loglevel":"^1.6.7","pino-pretty":"^v7.6.0","pre-commit":"^1.2.2","proxyquire":"^2.1.3","pump":"^3.0.0","rimraf":"^3.0.2","semver":"^7.0.0","split2":"^4.0.0","steed":"^1.1.3","strip-ansi":"^6.0.0","tap":"^16.0.0","tape":"^5.0.0","through2":"^4.0.0","ts-node":"^10.7.0","tsd":"^0.20.0","typescript":"^4.4.4","winston":"^3.3.3"},"dependencies":{"atomic-sleep":"^1.0.0","fast-redact":"^3.0.0","on-exit-leak-free":"^0.2.0","pino-abstract-transport":"v0.5.0","pino-std-serializers":"^4.0.0","process-warning":"^1.0.0","quick-format-unescaped":"^4.0.3","real-require":"^0.1.0","safe-stable-stringify":"^2.1.0","sonic-boom":"^2.2.1","thread-stream":"^0.15.1"},"tsd":{"directory":"test/types"}}');

/***/ })

};
;