/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eventemitter2";
exports.ids = ["vendor-chunks/eventemitter2"];
exports.modules = {

/***/ "(ssr)/./node_modules/eventemitter2/lib/eventemitter2.js":
/*!*********************************************************!*\
  !*** ./node_modules/eventemitter2/lib/eventemitter2.js ***!
  \*********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("var __WEBPACK_AMD_DEFINE_RESULT__;/*!\n * EventEmitter2\n * https://github.com/hij1nx/EventEmitter2\n *\n * Copyright (c) 2013 hij1nx\n * Licensed under the MIT license.\n */\n;!function(undefined) {\n  var hasOwnProperty= Object.hasOwnProperty;\n  var isArray = Array.isArray ? Array.isArray : function _isArray(obj) {\n    return Object.prototype.toString.call(obj) === \"[object Array]\";\n  };\n  var defaultMaxListeners = 10;\n  var nextTickSupported= typeof process=='object' && typeof process.nextTick=='function';\n  var symbolsSupported= typeof Symbol==='function';\n  var reflectSupported= typeof Reflect === 'object';\n  var setImmediateSupported= typeof setImmediate === 'function';\n  var _setImmediate= setImmediateSupported ? setImmediate : setTimeout;\n  var ownKeys= symbolsSupported? (reflectSupported && typeof Reflect.ownKeys==='function'? Reflect.ownKeys : function(obj){\n    var arr= Object.getOwnPropertyNames(obj);\n    arr.push.apply(arr, Object.getOwnPropertySymbols(obj));\n    return arr;\n  }) : Object.keys;\n\n  function init() {\n    this._events = {};\n    if (this._conf) {\n      configure.call(this, this._conf);\n    }\n  }\n\n  function configure(conf) {\n    if (conf) {\n      this._conf = conf;\n\n      conf.delimiter && (this.delimiter = conf.delimiter);\n\n      if(conf.maxListeners!==undefined){\n          this._maxListeners= conf.maxListeners;\n      }\n\n      conf.wildcard && (this.wildcard = conf.wildcard);\n      conf.newListener && (this._newListener = conf.newListener);\n      conf.removeListener && (this._removeListener = conf.removeListener);\n      conf.verboseMemoryLeak && (this.verboseMemoryLeak = conf.verboseMemoryLeak);\n      conf.ignoreErrors && (this.ignoreErrors = conf.ignoreErrors);\n\n      if (this.wildcard) {\n        this.listenerTree = {};\n      }\n    }\n  }\n\n  function logPossibleMemoryLeak(count, eventName) {\n    var errorMsg = '(node) warning: possible EventEmitter memory ' +\n        'leak detected. ' + count + ' listeners added. ' +\n        'Use emitter.setMaxListeners() to increase limit.';\n\n    if(this.verboseMemoryLeak){\n      errorMsg += ' Event name: ' + eventName + '.';\n    }\n\n    if(typeof process !== 'undefined' && process.emitWarning){\n      var e = new Error(errorMsg);\n      e.name = 'MaxListenersExceededWarning';\n      e.emitter = this;\n      e.count = count;\n      process.emitWarning(e);\n    } else {\n      console.error(errorMsg);\n\n      if (console.trace){\n        console.trace();\n      }\n    }\n  }\n\n  var toArray = function (a, b, c) {\n    var n = arguments.length;\n    switch (n) {\n      case 0:\n        return [];\n      case 1:\n        return [a];\n      case 2:\n        return [a, b];\n      case 3:\n        return [a, b, c];\n      default:\n        var arr = new Array(n);\n        while (n--) {\n          arr[n] = arguments[n];\n        }\n        return arr;\n    }\n  };\n\n  function toObject(keys, values) {\n    var obj = {};\n    var key;\n    var len = keys.length;\n    var valuesCount = values ? values.length : 0;\n    for (var i = 0; i < len; i++) {\n      key = keys[i];\n      obj[key] = i < valuesCount ? values[i] : undefined;\n    }\n    return obj;\n  }\n\n  function TargetObserver(emitter, target, options) {\n    this._emitter = emitter;\n    this._target = target;\n    this._listeners = {};\n    this._listenersCount = 0;\n\n    var on, off;\n\n    if (options.on || options.off) {\n      on = options.on;\n      off = options.off;\n    }\n\n    if (target.addEventListener) {\n      on = target.addEventListener;\n      off = target.removeEventListener;\n    } else if (target.addListener) {\n      on = target.addListener;\n      off = target.removeListener;\n    } else if (target.on) {\n      on = target.on;\n      off = target.off;\n    }\n\n    if (!on && !off) {\n      throw Error('target does not implement any known event API');\n    }\n\n    if (typeof on !== 'function') {\n      throw TypeError('on method must be a function');\n    }\n\n    if (typeof off !== 'function') {\n      throw TypeError('off method must be a function');\n    }\n\n    this._on = on;\n    this._off = off;\n\n    var _observers= emitter._observers;\n    if(_observers){\n      _observers.push(this);\n    }else{\n      emitter._observers= [this];\n    }\n  }\n\n  Object.assign(TargetObserver.prototype, {\n    subscribe: function(event, localEvent, reducer){\n      var observer= this;\n      var target= this._target;\n      var emitter= this._emitter;\n      var listeners= this._listeners;\n      var handler= function(){\n        var args= toArray.apply(null, arguments);\n        var eventObj= {\n          data: args,\n          name: localEvent,\n          original: event\n        };\n        if(reducer){\n          var result= reducer.call(target, eventObj);\n          if(result!==false){\n            emitter.emit.apply(emitter, [eventObj.name].concat(args))\n          }\n          return;\n        }\n        emitter.emit.apply(emitter, [localEvent].concat(args));\n      };\n\n\n      if(listeners[event]){\n        throw Error('Event \\'' + event + '\\' is already listening');\n      }\n\n      this._listenersCount++;\n\n      if(emitter._newListener && emitter._removeListener && !observer._onNewListener){\n\n        this._onNewListener = function (_event) {\n          if (_event === localEvent && listeners[event] === null) {\n            listeners[event] = handler;\n            observer._on.call(target, event, handler);\n          }\n        };\n\n        emitter.on('newListener', this._onNewListener);\n\n        this._onRemoveListener= function(_event){\n          if(_event === localEvent && !emitter.hasListeners(_event) && listeners[event]){\n            listeners[event]= null;\n            observer._off.call(target, event, handler);\n          }\n        };\n\n        listeners[event]= null;\n\n        emitter.on('removeListener', this._onRemoveListener);\n      }else{\n        listeners[event]= handler;\n        observer._on.call(target, event, handler);\n      }\n    },\n\n    unsubscribe: function(event){\n      var observer= this;\n      var listeners= this._listeners;\n      var emitter= this._emitter;\n      var handler;\n      var events;\n      var off= this._off;\n      var target= this._target;\n      var i;\n\n      if(event && typeof event!=='string'){\n        throw TypeError('event must be a string');\n      }\n\n      function clearRefs(){\n        if(observer._onNewListener){\n          emitter.off('newListener', observer._onNewListener);\n          emitter.off('removeListener', observer._onRemoveListener);\n          observer._onNewListener= null;\n          observer._onRemoveListener= null;\n        }\n        var index= findTargetIndex.call(emitter, observer);\n        emitter._observers.splice(index, 1);\n      }\n\n      if(event){\n        handler= listeners[event];\n        if(!handler) return;\n        off.call(target, event, handler);\n        delete listeners[event];\n        if(!--this._listenersCount){\n          clearRefs();\n        }\n      }else{\n        events= ownKeys(listeners);\n        i= events.length;\n        while(i-->0){\n          event= events[i];\n          off.call(target, event, listeners[event]);\n        }\n        this._listeners= {};\n        this._listenersCount= 0;\n        clearRefs();\n      }\n    }\n  });\n\n  function resolveOptions(options, schema, reducers, allowUnknown) {\n    var computedOptions = Object.assign({}, schema);\n\n    if (!options) return computedOptions;\n\n    if (typeof options !== 'object') {\n      throw TypeError('options must be an object')\n    }\n\n    var keys = Object.keys(options);\n    var length = keys.length;\n    var option, value;\n    var reducer;\n\n    function reject(reason) {\n      throw Error('Invalid \"' + option + '\" option value' + (reason ? '. Reason: ' + reason : ''))\n    }\n\n    for (var i = 0; i < length; i++) {\n      option = keys[i];\n      if (!allowUnknown && !hasOwnProperty.call(schema, option)) {\n        throw Error('Unknown \"' + option + '\" option');\n      }\n      value = options[option];\n      if (value !== undefined) {\n        reducer = reducers[option];\n        computedOptions[option] = reducer ? reducer(value, reject) : value;\n      }\n    }\n    return computedOptions;\n  }\n\n  function constructorReducer(value, reject) {\n    if (typeof value !== 'function' || !value.hasOwnProperty('prototype')) {\n      reject('value must be a constructor');\n    }\n    return value;\n  }\n\n  function makeTypeReducer(types) {\n    var message= 'value must be type of ' + types.join('|');\n    var len= types.length;\n    var firstType= types[0];\n    var secondType= types[1];\n\n    if (len === 1) {\n      return function (v, reject) {\n        if (typeof v === firstType) {\n          return v;\n        }\n        reject(message);\n      }\n    }\n\n    if (len === 2) {\n      return function (v, reject) {\n        var kind= typeof v;\n        if (kind === firstType || kind === secondType) return v;\n        reject(message);\n      }\n    }\n\n    return function (v, reject) {\n      var kind = typeof v;\n      var i = len;\n      while (i-- > 0) {\n        if (kind === types[i]) return v;\n      }\n      reject(message);\n    }\n  }\n\n  var functionReducer= makeTypeReducer(['function']);\n\n  var objectFunctionReducer= makeTypeReducer(['object', 'function']);\n\n  function makeCancelablePromise(Promise, executor, options) {\n    var isCancelable;\n    var callbacks;\n    var timer= 0;\n    var subscriptionClosed;\n\n    var promise = new Promise(function (resolve, reject, onCancel) {\n      options= resolveOptions(options, {\n        timeout: 0,\n        overload: false\n      }, {\n        timeout: function(value, reject){\n          value*= 1;\n          if (typeof value !== 'number' || value < 0 || !Number.isFinite(value)) {\n            reject('timeout must be a positive number');\n          }\n          return value;\n        }\n      });\n\n      isCancelable = !options.overload && typeof Promise.prototype.cancel === 'function' && typeof onCancel === 'function';\n\n      function cleanup() {\n        if (callbacks) {\n          callbacks = null;\n        }\n        if (timer) {\n          clearTimeout(timer);\n          timer = 0;\n        }\n      }\n\n      var _resolve= function(value){\n        cleanup();\n        resolve(value);\n      };\n\n      var _reject= function(err){\n        cleanup();\n        reject(err);\n      };\n\n      if (isCancelable) {\n        executor(_resolve, _reject, onCancel);\n      } else {\n        callbacks = [function(reason){\n          _reject(reason || Error('canceled'));\n        }];\n        executor(_resolve, _reject, function (cb) {\n          if (subscriptionClosed) {\n            throw Error('Unable to subscribe on cancel event asynchronously')\n          }\n          if (typeof cb !== 'function') {\n            throw TypeError('onCancel callback must be a function');\n          }\n          callbacks.push(cb);\n        });\n        subscriptionClosed= true;\n      }\n\n      if (options.timeout > 0) {\n        timer= setTimeout(function(){\n          var reason= Error('timeout');\n          reason.code = 'ETIMEDOUT'\n          timer= 0;\n          promise.cancel(reason);\n          reject(reason);\n        }, options.timeout);\n      }\n    });\n\n    if (!isCancelable) {\n      promise.cancel = function (reason) {\n        if (!callbacks) {\n          return;\n        }\n        var length = callbacks.length;\n        for (var i = 1; i < length; i++) {\n          callbacks[i](reason);\n        }\n        // internal callback to reject the promise\n        callbacks[0](reason);\n        callbacks = null;\n      };\n    }\n\n    return promise;\n  }\n\n  function findTargetIndex(observer) {\n    var observers = this._observers;\n    if(!observers){\n      return -1;\n    }\n    var len = observers.length;\n    for (var i = 0; i < len; i++) {\n      if (observers[i]._target === observer) return i;\n    }\n    return -1;\n  }\n\n  // Attention, function return type now is array, always !\n  // It has zero elements if no any matches found and one or more\n  // elements (leafs) if there are matches\n  //\n  function searchListenerTree(handlers, type, tree, i, typeLength) {\n    if (!tree) {\n      return null;\n    }\n\n    if (i === 0) {\n      var kind = typeof type;\n      if (kind === 'string') {\n        var ns, n, l = 0, j = 0, delimiter = this.delimiter, dl = delimiter.length;\n        if ((n = type.indexOf(delimiter)) !== -1) {\n          ns = new Array(5);\n          do {\n            ns[l++] = type.slice(j, n);\n            j = n + dl;\n          } while ((n = type.indexOf(delimiter, j)) !== -1);\n\n          ns[l++] = type.slice(j);\n          type = ns;\n          typeLength = l;\n        } else {\n          type = [type];\n          typeLength = 1;\n        }\n      } else if (kind === 'object') {\n        typeLength = type.length;\n      } else {\n        type = [type];\n        typeLength = 1;\n      }\n    }\n\n    var listeners= null, branch, xTree, xxTree, isolatedBranch, endReached, currentType = type[i],\n        nextType = type[i + 1], branches, _listeners;\n\n    if (i === typeLength) {\n      //\n      // If at the end of the event(s) list and the tree has listeners\n      // invoke those listeners.\n      //\n\n      if(tree._listeners) {\n        if (typeof tree._listeners === 'function') {\n          handlers && handlers.push(tree._listeners);\n          listeners = [tree];\n        } else {\n          handlers && handlers.push.apply(handlers, tree._listeners);\n          listeners = [tree];\n        }\n      }\n    } else {\n\n      if (currentType === '*') {\n        //\n        // If the event emitted is '*' at this part\n        // or there is a concrete match at this patch\n        //\n        branches = ownKeys(tree);\n        n = branches.length;\n        while (n-- > 0) {\n          branch = branches[n];\n          if (branch !== '_listeners') {\n            _listeners = searchListenerTree(handlers, type, tree[branch], i + 1, typeLength);\n            if (_listeners) {\n              if (listeners) {\n                listeners.push.apply(listeners, _listeners);\n              } else {\n                listeners = _listeners;\n              }\n            }\n          }\n        }\n        return listeners;\n      } else if (currentType === '**') {\n        endReached = (i + 1 === typeLength || (i + 2 === typeLength && nextType === '*'));\n        if (endReached && tree._listeners) {\n          // The next element has a _listeners, add it to the handlers.\n          listeners = searchListenerTree(handlers, type, tree, typeLength, typeLength);\n        }\n\n        branches = ownKeys(tree);\n        n = branches.length;\n        while (n-- > 0) {\n          branch = branches[n];\n          if (branch !== '_listeners') {\n            if (branch === '*' || branch === '**') {\n              if (tree[branch]._listeners && !endReached) {\n                _listeners = searchListenerTree(handlers, type, tree[branch], typeLength, typeLength);\n                if (_listeners) {\n                  if (listeners) {\n                    listeners.push.apply(listeners, _listeners);\n                  } else {\n                    listeners = _listeners;\n                  }\n                }\n              }\n              _listeners = searchListenerTree(handlers, type, tree[branch], i, typeLength);\n            } else if (branch === nextType) {\n              _listeners = searchListenerTree(handlers, type, tree[branch], i + 2, typeLength);\n            } else {\n              // No match on this one, shift into the tree but not in the type array.\n              _listeners = searchListenerTree(handlers, type, tree[branch], i, typeLength);\n            }\n            if (_listeners) {\n              if (listeners) {\n                listeners.push.apply(listeners, _listeners);\n              } else {\n                listeners = _listeners;\n              }\n            }\n          }\n        }\n        return listeners;\n      } else if (tree[currentType]) {\n        listeners = searchListenerTree(handlers, type, tree[currentType], i + 1, typeLength);\n      }\n    }\n\n      xTree = tree['*'];\n    if (xTree) {\n      //\n      // If the listener tree will allow any match for this part,\n      // then recursively explore all branches of the tree\n      //\n      searchListenerTree(handlers, type, xTree, i + 1, typeLength);\n    }\n\n    xxTree = tree['**'];\n    if (xxTree) {\n      if (i < typeLength) {\n        if (xxTree._listeners) {\n          // If we have a listener on a '**', it will catch all, so add its handler.\n          searchListenerTree(handlers, type, xxTree, typeLength, typeLength);\n        }\n\n        // Build arrays of matching next branches and others.\n        branches= ownKeys(xxTree);\n        n= branches.length;\n        while(n-->0){\n          branch= branches[n];\n          if (branch !== '_listeners') {\n            if (branch === nextType) {\n              // We know the next element will match, so jump twice.\n              searchListenerTree(handlers, type, xxTree[branch], i + 2, typeLength);\n            } else if (branch === currentType) {\n              // Current node matches, move into the tree.\n              searchListenerTree(handlers, type, xxTree[branch], i + 1, typeLength);\n            } else {\n              isolatedBranch = {};\n              isolatedBranch[branch] = xxTree[branch];\n              searchListenerTree(handlers, type, {'**': isolatedBranch}, i + 1, typeLength);\n            }\n          }\n        }\n      } else if (xxTree._listeners) {\n        // We have reached the end and still on a '**'\n        searchListenerTree(handlers, type, xxTree, typeLength, typeLength);\n      } else if (xxTree['*'] && xxTree['*']._listeners) {\n        searchListenerTree(handlers, type, xxTree['*'], typeLength, typeLength);\n      }\n    }\n\n    return listeners;\n  }\n\n  function growListenerTree(type, listener, prepend) {\n    var len = 0, j = 0, i, delimiter = this.delimiter, dl= delimiter.length, ns;\n\n    if(typeof type==='string') {\n      if ((i = type.indexOf(delimiter)) !== -1) {\n        ns = new Array(5);\n        do {\n          ns[len++] = type.slice(j, i);\n          j = i + dl;\n        } while ((i = type.indexOf(delimiter, j)) !== -1);\n\n        ns[len++] = type.slice(j);\n      }else{\n        ns= [type];\n        len= 1;\n      }\n    }else{\n      ns= type;\n      len= type.length;\n    }\n\n    //\n    // Looks for two consecutive '**', if so, don't add the event at all.\n    //\n    if (len > 1) {\n      for (i = 0; i + 1 < len; i++) {\n        if (ns[i] === '**' && ns[i + 1] === '**') {\n          return;\n        }\n      }\n    }\n\n\n\n    var tree = this.listenerTree, name;\n\n    for (i = 0; i < len; i++) {\n      name = ns[i];\n\n      tree = tree[name] || (tree[name] = {});\n\n      if (i === len - 1) {\n        if (!tree._listeners) {\n          tree._listeners = listener;\n        } else {\n          if (typeof tree._listeners === 'function') {\n            tree._listeners = [tree._listeners];\n          }\n\n          if (prepend) {\n            tree._listeners.unshift(listener);\n          } else {\n            tree._listeners.push(listener);\n          }\n\n          if (\n              !tree._listeners.warned &&\n              this._maxListeners > 0 &&\n              tree._listeners.length > this._maxListeners\n          ) {\n            tree._listeners.warned = true;\n            logPossibleMemoryLeak.call(this, tree._listeners.length, name);\n          }\n        }\n        return true;\n      }\n    }\n\n    return true;\n  }\n\n  function collectTreeEvents(tree, events, root, asArray){\n     var branches= ownKeys(tree);\n     var i= branches.length;\n     var branch, branchName, path;\n     var hasListeners= tree['_listeners'];\n     var isArrayPath;\n\n     while(i-->0){\n         branchName= branches[i];\n\n         branch= tree[branchName];\n\n         if(branchName==='_listeners'){\n             path= root;\n         }else {\n             path = root ? root.concat(branchName) : [branchName];\n         }\n\n         isArrayPath= asArray || typeof branchName==='symbol';\n\n         hasListeners && events.push(isArrayPath? path : path.join(this.delimiter));\n\n         if(typeof branch==='object'){\n             collectTreeEvents.call(this, branch, events, path, isArrayPath);\n         }\n     }\n\n     return events;\n  }\n\n  function recursivelyGarbageCollect(root) {\n    var keys = ownKeys(root);\n    var i= keys.length;\n    var obj, key, flag;\n    while(i-->0){\n      key = keys[i];\n      obj = root[key];\n\n      if(obj){\n          flag= true;\n          if(key !== '_listeners' && !recursivelyGarbageCollect(obj)){\n             delete root[key];\n          }\n      }\n    }\n\n    return flag;\n  }\n\n  function Listener(emitter, event, listener){\n    this.emitter= emitter;\n    this.event= event;\n    this.listener= listener;\n  }\n\n  Listener.prototype.off= function(){\n    this.emitter.off(this.event, this.listener);\n    return this;\n  };\n\n  function setupListener(event, listener, options){\n      if (options === true) {\n        promisify = true;\n      } else if (options === false) {\n        async = true;\n      } else {\n        if (!options || typeof options !== 'object') {\n          throw TypeError('options should be an object or true');\n        }\n        var async = options.async;\n        var promisify = options.promisify;\n        var nextTick = options.nextTick;\n        var objectify = options.objectify;\n      }\n\n      if (async || nextTick || promisify) {\n        var _listener = listener;\n        var _origin = listener._origin || listener;\n\n        if (nextTick && !nextTickSupported) {\n          throw Error('process.nextTick is not supported');\n        }\n\n        if (promisify === undefined) {\n          promisify = listener.constructor.name === 'AsyncFunction';\n        }\n\n        listener = function () {\n          var args = arguments;\n          var context = this;\n          var event = this.event;\n\n          return promisify ? (nextTick ? Promise.resolve() : new Promise(function (resolve) {\n            _setImmediate(resolve);\n          }).then(function () {\n            context.event = event;\n            return _listener.apply(context, args)\n          })) : (nextTick ? process.nextTick : _setImmediate)(function () {\n            context.event = event;\n            _listener.apply(context, args)\n          });\n        };\n\n        listener._async = true;\n        listener._origin = _origin;\n      }\n\n    return [listener, objectify? new Listener(this, event, listener): this];\n  }\n\n  function EventEmitter(conf) {\n    this._events = {};\n    this._newListener = false;\n    this._removeListener = false;\n    this.verboseMemoryLeak = false;\n    configure.call(this, conf);\n  }\n\n  EventEmitter.EventEmitter2 = EventEmitter; // backwards compatibility for exporting EventEmitter property\n\n  EventEmitter.prototype.listenTo= function(target, events, options){\n    if(typeof target!=='object'){\n      throw TypeError('target musts be an object');\n    }\n\n    var emitter= this;\n\n    options = resolveOptions(options, {\n      on: undefined,\n      off: undefined,\n      reducers: undefined\n    }, {\n      on: functionReducer,\n      off: functionReducer,\n      reducers: objectFunctionReducer\n    });\n\n    function listen(events){\n      if(typeof events!=='object'){\n        throw TypeError('events must be an object');\n      }\n\n      var reducers= options.reducers;\n      var index= findTargetIndex.call(emitter, target);\n      var observer;\n\n      if(index===-1){\n        observer= new TargetObserver(emitter, target, options);\n      }else{\n        observer= emitter._observers[index];\n      }\n\n      var keys= ownKeys(events);\n      var len= keys.length;\n      var event;\n      var isSingleReducer= typeof reducers==='function';\n\n      for(var i=0; i<len; i++){\n        event= keys[i];\n        observer.subscribe(\n            event,\n            events[event] || event,\n            isSingleReducer ? reducers : reducers && reducers[event]\n        );\n      }\n    }\n\n    isArray(events)?\n        listen(toObject(events)) :\n        (typeof events==='string'? listen(toObject(events.split(/\\s+/))): listen(events));\n\n    return this;\n  };\n\n  EventEmitter.prototype.stopListeningTo = function (target, event) {\n    var observers = this._observers;\n\n    if(!observers){\n      return false;\n    }\n\n    var i = observers.length;\n    var observer;\n    var matched= false;\n\n    if(target && typeof target!=='object'){\n      throw TypeError('target should be an object');\n    }\n\n    while (i-- > 0) {\n      observer = observers[i];\n      if (!target || observer._target === target) {\n        observer.unsubscribe(event);\n        matched= true;\n      }\n    }\n\n    return matched;\n  };\n\n  // By default EventEmitters will print a warning if more than\n  // 10 listeners are added to it. This is a useful default which\n  // helps finding memory leaks.\n  //\n  // Obviously not all Emitters should be limited to 10. This function allows\n  // that to be increased. Set to zero for unlimited.\n\n  EventEmitter.prototype.delimiter = '.';\n\n  EventEmitter.prototype.setMaxListeners = function(n) {\n    if (n !== undefined) {\n      this._maxListeners = n;\n      if (!this._conf) this._conf = {};\n      this._conf.maxListeners = n;\n    }\n  };\n\n  EventEmitter.prototype.getMaxListeners = function() {\n    return this._maxListeners;\n  };\n\n  EventEmitter.prototype.event = '';\n\n  EventEmitter.prototype.once = function(event, fn, options) {\n    return this._once(event, fn, false, options);\n  };\n\n  EventEmitter.prototype.prependOnceListener = function(event, fn, options) {\n    return this._once(event, fn, true, options);\n  };\n\n  EventEmitter.prototype._once = function(event, fn, prepend, options) {\n    return this._many(event, 1, fn, prepend, options);\n  };\n\n  EventEmitter.prototype.many = function(event, ttl, fn, options) {\n    return this._many(event, ttl, fn, false, options);\n  };\n\n  EventEmitter.prototype.prependMany = function(event, ttl, fn, options) {\n    return this._many(event, ttl, fn, true, options);\n  };\n\n  EventEmitter.prototype._many = function(event, ttl, fn, prepend, options) {\n    var self = this;\n\n    if (typeof fn !== 'function') {\n      throw new Error('many only accepts instances of Function');\n    }\n\n    function listener() {\n      if (--ttl === 0) {\n        self.off(event, listener);\n      }\n      return fn.apply(this, arguments);\n    }\n\n    listener._origin = fn;\n\n    return this._on(event, listener, prepend, options);\n  };\n\n  EventEmitter.prototype.emit = function() {\n    if (!this._events && !this._all) {\n      return false;\n    }\n\n    this._events || init.call(this);\n\n    var type = arguments[0], ns, wildcard= this.wildcard;\n    var args,l,i,j, containsSymbol;\n\n    if (type === 'newListener' && !this._newListener) {\n      if (!this._events.newListener) {\n        return false;\n      }\n    }\n\n    if (wildcard) {\n      ns= type;\n      if(type!=='newListener' && type!=='removeListener'){\n        if (typeof type === 'object') {\n          l = type.length;\n          if (symbolsSupported) {\n            for (i = 0; i < l; i++) {\n              if (typeof type[i] === 'symbol') {\n                containsSymbol = true;\n                break;\n              }\n            }\n          }\n          if (!containsSymbol) {\n            type = type.join(this.delimiter);\n          }\n        }\n      }\n    }\n\n    var al = arguments.length;\n    var handler;\n\n    if (this._all && this._all.length) {\n      handler = this._all.slice();\n\n      for (i = 0, l = handler.length; i < l; i++) {\n        this.event = type;\n        switch (al) {\n        case 1:\n          handler[i].call(this, type);\n          break;\n        case 2:\n          handler[i].call(this, type, arguments[1]);\n          break;\n        case 3:\n          handler[i].call(this, type, arguments[1], arguments[2]);\n          break;\n        default:\n          handler[i].apply(this, arguments);\n        }\n      }\n    }\n\n    if (wildcard) {\n      handler = [];\n      searchListenerTree.call(this, handler, ns, this.listenerTree, 0, l);\n    } else {\n      handler = this._events[type];\n      if (typeof handler === 'function') {\n        this.event = type;\n        switch (al) {\n        case 1:\n          handler.call(this);\n          break;\n        case 2:\n          handler.call(this, arguments[1]);\n          break;\n        case 3:\n          handler.call(this, arguments[1], arguments[2]);\n          break;\n        default:\n          args = new Array(al - 1);\n          for (j = 1; j < al; j++) args[j - 1] = arguments[j];\n          handler.apply(this, args);\n        }\n        return true;\n      } else if (handler) {\n        // need to make copy of handlers because list can change in the middle\n        // of emit call\n        handler = handler.slice();\n      }\n    }\n\n    if (handler && handler.length) {\n      if (al > 3) {\n        args = new Array(al - 1);\n        for (j = 1; j < al; j++) args[j - 1] = arguments[j];\n      }\n      for (i = 0, l = handler.length; i < l; i++) {\n        this.event = type;\n        switch (al) {\n        case 1:\n          handler[i].call(this);\n          break;\n        case 2:\n          handler[i].call(this, arguments[1]);\n          break;\n        case 3:\n          handler[i].call(this, arguments[1], arguments[2]);\n          break;\n        default:\n          handler[i].apply(this, args);\n        }\n      }\n      return true;\n    } else if (!this.ignoreErrors && !this._all && type === 'error') {\n      if (arguments[1] instanceof Error) {\n        throw arguments[1]; // Unhandled 'error' event\n      } else {\n        throw new Error(\"Uncaught, unspecified 'error' event.\");\n      }\n    }\n\n    return !!this._all;\n  };\n\n  EventEmitter.prototype.emitAsync = function() {\n    if (!this._events && !this._all) {\n      return false;\n    }\n\n    this._events || init.call(this);\n\n    var type = arguments[0], wildcard= this.wildcard, ns, containsSymbol;\n    var args,l,i,j;\n\n    if (type === 'newListener' && !this._newListener) {\n        if (!this._events.newListener) { return Promise.resolve([false]); }\n    }\n\n    if (wildcard) {\n      ns= type;\n      if(type!=='newListener' && type!=='removeListener'){\n        if (typeof type === 'object') {\n          l = type.length;\n          if (symbolsSupported) {\n            for (i = 0; i < l; i++) {\n              if (typeof type[i] === 'symbol') {\n                containsSymbol = true;\n                break;\n              }\n            }\n          }\n          if (!containsSymbol) {\n            type = type.join(this.delimiter);\n          }\n        }\n      }\n    }\n\n    var promises= [];\n\n    var al = arguments.length;\n    var handler;\n\n    if (this._all) {\n      for (i = 0, l = this._all.length; i < l; i++) {\n        this.event = type;\n        switch (al) {\n        case 1:\n          promises.push(this._all[i].call(this, type));\n          break;\n        case 2:\n          promises.push(this._all[i].call(this, type, arguments[1]));\n          break;\n        case 3:\n          promises.push(this._all[i].call(this, type, arguments[1], arguments[2]));\n          break;\n        default:\n          promises.push(this._all[i].apply(this, arguments));\n        }\n      }\n    }\n\n    if (wildcard) {\n      handler = [];\n      searchListenerTree.call(this, handler, ns, this.listenerTree, 0);\n    } else {\n      handler = this._events[type];\n    }\n\n    if (typeof handler === 'function') {\n      this.event = type;\n      switch (al) {\n      case 1:\n        promises.push(handler.call(this));\n        break;\n      case 2:\n        promises.push(handler.call(this, arguments[1]));\n        break;\n      case 3:\n        promises.push(handler.call(this, arguments[1], arguments[2]));\n        break;\n      default:\n        args = new Array(al - 1);\n        for (j = 1; j < al; j++) args[j - 1] = arguments[j];\n        promises.push(handler.apply(this, args));\n      }\n    } else if (handler && handler.length) {\n      handler = handler.slice();\n      if (al > 3) {\n        args = new Array(al - 1);\n        for (j = 1; j < al; j++) args[j - 1] = arguments[j];\n      }\n      for (i = 0, l = handler.length; i < l; i++) {\n        this.event = type;\n        switch (al) {\n        case 1:\n          promises.push(handler[i].call(this));\n          break;\n        case 2:\n          promises.push(handler[i].call(this, arguments[1]));\n          break;\n        case 3:\n          promises.push(handler[i].call(this, arguments[1], arguments[2]));\n          break;\n        default:\n          promises.push(handler[i].apply(this, args));\n        }\n      }\n    } else if (!this.ignoreErrors && !this._all && type === 'error') {\n      if (arguments[1] instanceof Error) {\n        return Promise.reject(arguments[1]); // Unhandled 'error' event\n      } else {\n        return Promise.reject(\"Uncaught, unspecified 'error' event.\");\n      }\n    }\n\n    return Promise.all(promises);\n  };\n\n  EventEmitter.prototype.on = function(type, listener, options) {\n    return this._on(type, listener, false, options);\n  };\n\n  EventEmitter.prototype.prependListener = function(type, listener, options) {\n    return this._on(type, listener, true, options);\n  };\n\n  EventEmitter.prototype.onAny = function(fn) {\n    return this._onAny(fn, false);\n  };\n\n  EventEmitter.prototype.prependAny = function(fn) {\n    return this._onAny(fn, true);\n  };\n\n  EventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n  EventEmitter.prototype._onAny = function(fn, prepend){\n    if (typeof fn !== 'function') {\n      throw new Error('onAny only accepts instances of Function');\n    }\n\n    if (!this._all) {\n      this._all = [];\n    }\n\n    // Add the function to the event listener collection.\n    if(prepend){\n      this._all.unshift(fn);\n    }else{\n      this._all.push(fn);\n    }\n\n    return this;\n  };\n\n  EventEmitter.prototype._on = function(type, listener, prepend, options) {\n    if (typeof type === 'function') {\n      this._onAny(type, listener);\n      return this;\n    }\n\n    if (typeof listener !== 'function') {\n      throw new Error('on only accepts instances of Function');\n    }\n    this._events || init.call(this);\n\n    var returnValue= this, temp;\n\n    if (options !== undefined) {\n      temp = setupListener.call(this, type, listener, options);\n      listener = temp[0];\n      returnValue = temp[1];\n    }\n\n    // To avoid recursion in the case that type == \"newListeners\"! Before\n    // adding it to the listeners, first emit \"newListeners\".\n    if (this._newListener) {\n      this.emit('newListener', type, listener);\n    }\n\n    if (this.wildcard) {\n      growListenerTree.call(this, type, listener, prepend);\n      return returnValue;\n    }\n\n    if (!this._events[type]) {\n      // Optimize the case of one listener. Don't need the extra array object.\n      this._events[type] = listener;\n    } else {\n      if (typeof this._events[type] === 'function') {\n        // Change to array.\n        this._events[type] = [this._events[type]];\n      }\n\n      // If we've already got an array, just add\n      if(prepend){\n        this._events[type].unshift(listener);\n      }else{\n        this._events[type].push(listener);\n      }\n\n      // Check for listener leak\n      if (\n        !this._events[type].warned &&\n        this._maxListeners > 0 &&\n        this._events[type].length > this._maxListeners\n      ) {\n        this._events[type].warned = true;\n        logPossibleMemoryLeak.call(this, this._events[type].length, type);\n      }\n    }\n\n    return returnValue;\n  };\n\n  EventEmitter.prototype.off = function(type, listener) {\n    if (typeof listener !== 'function') {\n      throw new Error('removeListener only takes instances of Function');\n    }\n\n    var handlers,leafs=[];\n\n    if(this.wildcard) {\n      var ns = typeof type === 'string' ? type.split(this.delimiter) : type.slice();\n      leafs = searchListenerTree.call(this, null, ns, this.listenerTree, 0);\n      if(!leafs) return this;\n    } else {\n      // does not use listeners(), so no side effect of creating _events[type]\n      if (!this._events[type]) return this;\n      handlers = this._events[type];\n      leafs.push({_listeners:handlers});\n    }\n\n    for (var iLeaf=0; iLeaf<leafs.length; iLeaf++) {\n      var leaf = leafs[iLeaf];\n      handlers = leaf._listeners;\n      if (isArray(handlers)) {\n\n        var position = -1;\n\n        for (var i = 0, length = handlers.length; i < length; i++) {\n          if (handlers[i] === listener ||\n            (handlers[i].listener && handlers[i].listener === listener) ||\n            (handlers[i]._origin && handlers[i]._origin === listener)) {\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0) {\n          continue;\n        }\n\n        if(this.wildcard) {\n          leaf._listeners.splice(position, 1);\n        }\n        else {\n          this._events[type].splice(position, 1);\n        }\n\n        if (handlers.length === 0) {\n          if(this.wildcard) {\n            delete leaf._listeners;\n          }\n          else {\n            delete this._events[type];\n          }\n        }\n        if (this._removeListener)\n          this.emit(\"removeListener\", type, listener);\n\n        return this;\n      }\n      else if (handlers === listener ||\n        (handlers.listener && handlers.listener === listener) ||\n        (handlers._origin && handlers._origin === listener)) {\n        if(this.wildcard) {\n          delete leaf._listeners;\n        }\n        else {\n          delete this._events[type];\n        }\n        if (this._removeListener)\n          this.emit(\"removeListener\", type, listener);\n      }\n    }\n\n    this.listenerTree && recursivelyGarbageCollect(this.listenerTree);\n\n    return this;\n  };\n\n  EventEmitter.prototype.offAny = function(fn) {\n    var i = 0, l = 0, fns;\n    if (fn && this._all && this._all.length > 0) {\n      fns = this._all;\n      for(i = 0, l = fns.length; i < l; i++) {\n        if(fn === fns[i]) {\n          fns.splice(i, 1);\n          if (this._removeListener)\n            this.emit(\"removeListenerAny\", fn);\n          return this;\n        }\n      }\n    } else {\n      fns = this._all;\n      if (this._removeListener) {\n        for(i = 0, l = fns.length; i < l; i++)\n          this.emit(\"removeListenerAny\", fns[i]);\n      }\n      this._all = [];\n    }\n    return this;\n  };\n\n  EventEmitter.prototype.removeListener = EventEmitter.prototype.off;\n\n  EventEmitter.prototype.removeAllListeners = function (type) {\n    if (type === undefined) {\n      !this._events || init.call(this);\n      return this;\n    }\n\n    if (this.wildcard) {\n      var leafs = searchListenerTree.call(this, null, type, this.listenerTree, 0), leaf, i;\n      if (!leafs) return this;\n      for (i = 0; i < leafs.length; i++) {\n        leaf = leafs[i];\n        leaf._listeners = null;\n      }\n      this.listenerTree && recursivelyGarbageCollect(this.listenerTree);\n    } else if (this._events) {\n      this._events[type] = null;\n    }\n    return this;\n  };\n\n  EventEmitter.prototype.listeners = function (type) {\n    var _events = this._events;\n    var keys, listeners, allListeners;\n    var i;\n    var listenerTree;\n\n    if (type === undefined) {\n      if (this.wildcard) {\n        throw Error('event name required for wildcard emitter');\n      }\n\n      if (!_events) {\n        return [];\n      }\n\n      keys = ownKeys(_events);\n      i = keys.length;\n      allListeners = [];\n      while (i-- > 0) {\n        listeners = _events[keys[i]];\n        if (typeof listeners === 'function') {\n          allListeners.push(listeners);\n        } else {\n          allListeners.push.apply(allListeners, listeners);\n        }\n      }\n      return allListeners;\n    } else {\n      if (this.wildcard) {\n        listenerTree= this.listenerTree;\n        if(!listenerTree) return [];\n        var handlers = [];\n        var ns = typeof type === 'string' ? type.split(this.delimiter) : type.slice();\n        searchListenerTree.call(this, handlers, ns, listenerTree, 0);\n        return handlers;\n      }\n\n      if (!_events) {\n        return [];\n      }\n\n      listeners = _events[type];\n\n      if (!listeners) {\n        return [];\n      }\n      return typeof listeners === 'function' ? [listeners] : listeners;\n    }\n  };\n\n  EventEmitter.prototype.eventNames = function(nsAsArray){\n    var _events= this._events;\n    return this.wildcard? collectTreeEvents.call(this, this.listenerTree, [], null, nsAsArray) : (_events? ownKeys(_events) : []);\n  };\n\n  EventEmitter.prototype.listenerCount = function(type) {\n    return this.listeners(type).length;\n  };\n\n  EventEmitter.prototype.hasListeners = function (type) {\n    if (this.wildcard) {\n      var handlers = [];\n      var ns = typeof type === 'string' ? type.split(this.delimiter) : type.slice();\n      searchListenerTree.call(this, handlers, ns, this.listenerTree, 0);\n      return handlers.length > 0;\n    }\n\n    var _events = this._events;\n    var _all = this._all;\n\n    return !!(_all && _all.length || _events && (type === undefined ? ownKeys(_events).length : _events[type]));\n  };\n\n  EventEmitter.prototype.listenersAny = function() {\n\n    if(this._all) {\n      return this._all;\n    }\n    else {\n      return [];\n    }\n\n  };\n\n  EventEmitter.prototype.waitFor = function (event, options) {\n    var self = this;\n    var type = typeof options;\n    if (type === 'number') {\n      options = {timeout: options};\n    } else if (type === 'function') {\n      options = {filter: options};\n    }\n\n    options= resolveOptions(options, {\n      timeout: 0,\n      filter: undefined,\n      handleError: false,\n      Promise: Promise,\n      overload: false\n    }, {\n      filter: functionReducer,\n      Promise: constructorReducer\n    });\n\n    return makeCancelablePromise(options.Promise, function (resolve, reject, onCancel) {\n      function listener() {\n        var filter= options.filter;\n        if (filter && !filter.apply(self, arguments)) {\n          return;\n        }\n        self.off(event, listener);\n        if (options.handleError) {\n          var err = arguments[0];\n          err ? reject(err) : resolve(toArray.apply(null, arguments).slice(1));\n        } else {\n          resolve(toArray.apply(null, arguments));\n        }\n      }\n\n      onCancel(function(){\n        self.off(event, listener);\n      });\n\n      self._on(event, listener, false);\n    }, {\n      timeout: options.timeout,\n      overload: options.overload\n    })\n  };\n\n  function once(emitter, name, options) {\n    options= resolveOptions(options, {\n      Promise: Promise,\n      timeout: 0,\n      overload: false\n    }, {\n      Promise: constructorReducer\n    });\n\n    var _Promise= options.Promise;\n\n    return makeCancelablePromise(_Promise, function(resolve, reject, onCancel){\n      var handler;\n      if (typeof emitter.addEventListener === 'function') {\n        handler=  function () {\n          resolve(toArray.apply(null, arguments));\n        };\n\n        onCancel(function(){\n          emitter.removeEventListener(name, handler);\n        });\n\n        emitter.addEventListener(\n            name,\n            handler,\n            {once: true}\n        );\n        return;\n      }\n\n      var eventListener = function(){\n        errorListener && emitter.removeListener('error', errorListener);\n        resolve(toArray.apply(null, arguments));\n      };\n\n      var errorListener;\n\n      if (name !== 'error') {\n        errorListener = function (err){\n          emitter.removeListener(name, eventListener);\n          reject(err);\n        };\n\n        emitter.once('error', errorListener);\n      }\n\n      onCancel(function(){\n        errorListener && emitter.removeListener('error', errorListener);\n        emitter.removeListener(name, eventListener);\n      });\n\n      emitter.once(name, eventListener);\n    }, {\n      timeout: options.timeout,\n      overload: options.overload\n    });\n  }\n\n  var prototype= EventEmitter.prototype;\n\n  Object.defineProperties(EventEmitter, {\n    defaultMaxListeners: {\n      get: function () {\n        return prototype._maxListeners;\n      },\n      set: function (n) {\n        if (typeof n !== 'number' || n < 0 || Number.isNaN(n)) {\n          throw TypeError('n must be a non-negative number')\n        }\n        prototype._maxListeners = n;\n      },\n      enumerable: true\n    },\n    once: {\n      value: once,\n      writable: true,\n      configurable: true\n    }\n  });\n\n  Object.defineProperties(prototype, {\n      _maxListeners: {\n          value: defaultMaxListeners,\n          writable: true,\n          configurable: true\n      },\n      _observers: {value: null, writable: true, configurable: true}\n  });\n\n  if (true) {\n     // AMD. Register as an anonymous module.\n    !(__WEBPACK_AMD_DEFINE_RESULT__ = (function() {\n      return EventEmitter;\n    }).call(exports, __webpack_require__, exports, module),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n  } else { var _global; }\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eventemitter2/lib/eventemitter2.js\n");

/***/ })

};
;