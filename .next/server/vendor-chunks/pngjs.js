"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pngjs";
exports.ids = ["vendor-chunks/pngjs"];
exports.modules = {

/***/ "(ssr)/./node_modules/pngjs/lib/bitmapper.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/bitmapper.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nlet interlaceUtils = __webpack_require__(/*! ./interlace */ \"(ssr)/./node_modules/pngjs/lib/interlace.js\");\n\nlet pixelBppMapper = [\n  // 0 - dummy entry\n  function () {},\n\n  // 1 - L\n  // 0: 0, 1: 0, 2: 0, 3: 0xff\n  function (pxData, data, pxPos, rawPos) {\n    if (rawPos === data.length) {\n      throw new Error(\"Ran out of data\");\n    }\n\n    let pixel = data[rawPos];\n    pxData[pxPos] = pixel;\n    pxData[pxPos + 1] = pixel;\n    pxData[pxPos + 2] = pixel;\n    pxData[pxPos + 3] = 0xff;\n  },\n\n  // 2 - LA\n  // 0: 0, 1: 0, 2: 0, 3: 1\n  function (pxData, data, pxPos, rawPos) {\n    if (rawPos + 1 >= data.length) {\n      throw new Error(\"Ran out of data\");\n    }\n\n    let pixel = data[rawPos];\n    pxData[pxPos] = pixel;\n    pxData[pxPos + 1] = pixel;\n    pxData[pxPos + 2] = pixel;\n    pxData[pxPos + 3] = data[rawPos + 1];\n  },\n\n  // 3 - RGB\n  // 0: 0, 1: 1, 2: 2, 3: 0xff\n  function (pxData, data, pxPos, rawPos) {\n    if (rawPos + 2 >= data.length) {\n      throw new Error(\"Ran out of data\");\n    }\n\n    pxData[pxPos] = data[rawPos];\n    pxData[pxPos + 1] = data[rawPos + 1];\n    pxData[pxPos + 2] = data[rawPos + 2];\n    pxData[pxPos + 3] = 0xff;\n  },\n\n  // 4 - RGBA\n  // 0: 0, 1: 1, 2: 2, 3: 3\n  function (pxData, data, pxPos, rawPos) {\n    if (rawPos + 3 >= data.length) {\n      throw new Error(\"Ran out of data\");\n    }\n\n    pxData[pxPos] = data[rawPos];\n    pxData[pxPos + 1] = data[rawPos + 1];\n    pxData[pxPos + 2] = data[rawPos + 2];\n    pxData[pxPos + 3] = data[rawPos + 3];\n  },\n];\n\nlet pixelBppCustomMapper = [\n  // 0 - dummy entry\n  function () {},\n\n  // 1 - L\n  // 0: 0, 1: 0, 2: 0, 3: 0xff\n  function (pxData, pixelData, pxPos, maxBit) {\n    let pixel = pixelData[0];\n    pxData[pxPos] = pixel;\n    pxData[pxPos + 1] = pixel;\n    pxData[pxPos + 2] = pixel;\n    pxData[pxPos + 3] = maxBit;\n  },\n\n  // 2 - LA\n  // 0: 0, 1: 0, 2: 0, 3: 1\n  function (pxData, pixelData, pxPos) {\n    let pixel = pixelData[0];\n    pxData[pxPos] = pixel;\n    pxData[pxPos + 1] = pixel;\n    pxData[pxPos + 2] = pixel;\n    pxData[pxPos + 3] = pixelData[1];\n  },\n\n  // 3 - RGB\n  // 0: 0, 1: 1, 2: 2, 3: 0xff\n  function (pxData, pixelData, pxPos, maxBit) {\n    pxData[pxPos] = pixelData[0];\n    pxData[pxPos + 1] = pixelData[1];\n    pxData[pxPos + 2] = pixelData[2];\n    pxData[pxPos + 3] = maxBit;\n  },\n\n  // 4 - RGBA\n  // 0: 0, 1: 1, 2: 2, 3: 3\n  function (pxData, pixelData, pxPos) {\n    pxData[pxPos] = pixelData[0];\n    pxData[pxPos + 1] = pixelData[1];\n    pxData[pxPos + 2] = pixelData[2];\n    pxData[pxPos + 3] = pixelData[3];\n  },\n];\n\nfunction bitRetriever(data, depth) {\n  let leftOver = [];\n  let i = 0;\n\n  function split() {\n    if (i === data.length) {\n      throw new Error(\"Ran out of data\");\n    }\n    let byte = data[i];\n    i++;\n    let byte8, byte7, byte6, byte5, byte4, byte3, byte2, byte1;\n    switch (depth) {\n      default:\n        throw new Error(\"unrecognised depth\");\n      case 16:\n        byte2 = data[i];\n        i++;\n        leftOver.push((byte << 8) + byte2);\n        break;\n      case 4:\n        byte2 = byte & 0x0f;\n        byte1 = byte >> 4;\n        leftOver.push(byte1, byte2);\n        break;\n      case 2:\n        byte4 = byte & 3;\n        byte3 = (byte >> 2) & 3;\n        byte2 = (byte >> 4) & 3;\n        byte1 = (byte >> 6) & 3;\n        leftOver.push(byte1, byte2, byte3, byte4);\n        break;\n      case 1:\n        byte8 = byte & 1;\n        byte7 = (byte >> 1) & 1;\n        byte6 = (byte >> 2) & 1;\n        byte5 = (byte >> 3) & 1;\n        byte4 = (byte >> 4) & 1;\n        byte3 = (byte >> 5) & 1;\n        byte2 = (byte >> 6) & 1;\n        byte1 = (byte >> 7) & 1;\n        leftOver.push(byte1, byte2, byte3, byte4, byte5, byte6, byte7, byte8);\n        break;\n    }\n  }\n\n  return {\n    get: function (count) {\n      while (leftOver.length < count) {\n        split();\n      }\n      let returner = leftOver.slice(0, count);\n      leftOver = leftOver.slice(count);\n      return returner;\n    },\n    resetAfterLine: function () {\n      leftOver.length = 0;\n    },\n    end: function () {\n      if (i !== data.length) {\n        throw new Error(\"extra data found\");\n      }\n    },\n  };\n}\n\nfunction mapImage8Bit(image, pxData, getPxPos, bpp, data, rawPos) {\n  // eslint-disable-line max-params\n  let imageWidth = image.width;\n  let imageHeight = image.height;\n  let imagePass = image.index;\n  for (let y = 0; y < imageHeight; y++) {\n    for (let x = 0; x < imageWidth; x++) {\n      let pxPos = getPxPos(x, y, imagePass);\n      pixelBppMapper[bpp](pxData, data, pxPos, rawPos);\n      rawPos += bpp; //eslint-disable-line no-param-reassign\n    }\n  }\n  return rawPos;\n}\n\nfunction mapImageCustomBit(image, pxData, getPxPos, bpp, bits, maxBit) {\n  // eslint-disable-line max-params\n  let imageWidth = image.width;\n  let imageHeight = image.height;\n  let imagePass = image.index;\n  for (let y = 0; y < imageHeight; y++) {\n    for (let x = 0; x < imageWidth; x++) {\n      let pixelData = bits.get(bpp);\n      let pxPos = getPxPos(x, y, imagePass);\n      pixelBppCustomMapper[bpp](pxData, pixelData, pxPos, maxBit);\n    }\n    bits.resetAfterLine();\n  }\n}\n\nexports.dataToBitMap = function (data, bitmapInfo) {\n  let width = bitmapInfo.width;\n  let height = bitmapInfo.height;\n  let depth = bitmapInfo.depth;\n  let bpp = bitmapInfo.bpp;\n  let interlace = bitmapInfo.interlace;\n  let bits;\n\n  if (depth !== 8) {\n    bits = bitRetriever(data, depth);\n  }\n  let pxData;\n  if (depth <= 8) {\n    pxData = Buffer.alloc(width * height * 4);\n  } else {\n    pxData = new Uint16Array(width * height * 4);\n  }\n  let maxBit = Math.pow(2, depth) - 1;\n  let rawPos = 0;\n  let images;\n  let getPxPos;\n\n  if (interlace) {\n    images = interlaceUtils.getImagePasses(width, height);\n    getPxPos = interlaceUtils.getInterlaceIterator(width, height);\n  } else {\n    let nonInterlacedPxPos = 0;\n    getPxPos = function () {\n      let returner = nonInterlacedPxPos;\n      nonInterlacedPxPos += 4;\n      return returner;\n    };\n    images = [{ width: width, height: height }];\n  }\n\n  for (let imageIndex = 0; imageIndex < images.length; imageIndex++) {\n    if (depth === 8) {\n      rawPos = mapImage8Bit(\n        images[imageIndex],\n        pxData,\n        getPxPos,\n        bpp,\n        data,\n        rawPos\n      );\n    } else {\n      mapImageCustomBit(\n        images[imageIndex],\n        pxData,\n        getPxPos,\n        bpp,\n        bits,\n        maxBit\n      );\n    }\n  }\n  if (depth === 8) {\n    if (rawPos !== data.length) {\n      throw new Error(\"extra data found\");\n    }\n  } else {\n    bits.end();\n  }\n\n  return pxData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/bitmapper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/bitpacker.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/bitpacker.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\n\nmodule.exports = function (dataIn, width, height, options) {\n  let outHasAlpha =\n    [constants.COLORTYPE_COLOR_ALPHA, constants.COLORTYPE_ALPHA].indexOf(\n      options.colorType\n    ) !== -1;\n  if (options.colorType === options.inputColorType) {\n    let bigEndian = (function () {\n      let buffer = new ArrayBuffer(2);\n      new DataView(buffer).setInt16(0, 256, true /* littleEndian */);\n      // Int16Array uses the platform's endianness.\n      return new Int16Array(buffer)[0] !== 256;\n    })();\n    // If no need to convert to grayscale and alpha is present/absent in both, take a fast route\n    if (options.bitDepth === 8 || (options.bitDepth === 16 && bigEndian)) {\n      return dataIn;\n    }\n  }\n\n  // map to a UInt16 array if data is 16bit, fix endianness below\n  let data = options.bitDepth !== 16 ? dataIn : new Uint16Array(dataIn.buffer);\n\n  let maxValue = 255;\n  let inBpp = constants.COLORTYPE_TO_BPP_MAP[options.inputColorType];\n  if (inBpp === 4 && !options.inputHasAlpha) {\n    inBpp = 3;\n  }\n  let outBpp = constants.COLORTYPE_TO_BPP_MAP[options.colorType];\n  if (options.bitDepth === 16) {\n    maxValue = 65535;\n    outBpp *= 2;\n  }\n  let outData = Buffer.alloc(width * height * outBpp);\n\n  let inIndex = 0;\n  let outIndex = 0;\n\n  let bgColor = options.bgColor || {};\n  if (bgColor.red === undefined) {\n    bgColor.red = maxValue;\n  }\n  if (bgColor.green === undefined) {\n    bgColor.green = maxValue;\n  }\n  if (bgColor.blue === undefined) {\n    bgColor.blue = maxValue;\n  }\n\n  function getRGBA() {\n    let red;\n    let green;\n    let blue;\n    let alpha = maxValue;\n    switch (options.inputColorType) {\n      case constants.COLORTYPE_COLOR_ALPHA:\n        alpha = data[inIndex + 3];\n        red = data[inIndex];\n        green = data[inIndex + 1];\n        blue = data[inIndex + 2];\n        break;\n      case constants.COLORTYPE_COLOR:\n        red = data[inIndex];\n        green = data[inIndex + 1];\n        blue = data[inIndex + 2];\n        break;\n      case constants.COLORTYPE_ALPHA:\n        alpha = data[inIndex + 1];\n        red = data[inIndex];\n        green = red;\n        blue = red;\n        break;\n      case constants.COLORTYPE_GRAYSCALE:\n        red = data[inIndex];\n        green = red;\n        blue = red;\n        break;\n      default:\n        throw new Error(\n          \"input color type:\" +\n            options.inputColorType +\n            \" is not supported at present\"\n        );\n    }\n\n    if (options.inputHasAlpha) {\n      if (!outHasAlpha) {\n        alpha /= maxValue;\n        red = Math.min(\n          Math.max(Math.round((1 - alpha) * bgColor.red + alpha * red), 0),\n          maxValue\n        );\n        green = Math.min(\n          Math.max(Math.round((1 - alpha) * bgColor.green + alpha * green), 0),\n          maxValue\n        );\n        blue = Math.min(\n          Math.max(Math.round((1 - alpha) * bgColor.blue + alpha * blue), 0),\n          maxValue\n        );\n      }\n    }\n    return { red: red, green: green, blue: blue, alpha: alpha };\n  }\n\n  for (let y = 0; y < height; y++) {\n    for (let x = 0; x < width; x++) {\n      let rgba = getRGBA(data, inIndex);\n\n      switch (options.colorType) {\n        case constants.COLORTYPE_COLOR_ALPHA:\n        case constants.COLORTYPE_COLOR:\n          if (options.bitDepth === 8) {\n            outData[outIndex] = rgba.red;\n            outData[outIndex + 1] = rgba.green;\n            outData[outIndex + 2] = rgba.blue;\n            if (outHasAlpha) {\n              outData[outIndex + 3] = rgba.alpha;\n            }\n          } else {\n            outData.writeUInt16BE(rgba.red, outIndex);\n            outData.writeUInt16BE(rgba.green, outIndex + 2);\n            outData.writeUInt16BE(rgba.blue, outIndex + 4);\n            if (outHasAlpha) {\n              outData.writeUInt16BE(rgba.alpha, outIndex + 6);\n            }\n          }\n          break;\n        case constants.COLORTYPE_ALPHA:\n        case constants.COLORTYPE_GRAYSCALE: {\n          // Convert to grayscale and alpha\n          let grayscale = (rgba.red + rgba.green + rgba.blue) / 3;\n          if (options.bitDepth === 8) {\n            outData[outIndex] = grayscale;\n            if (outHasAlpha) {\n              outData[outIndex + 1] = rgba.alpha;\n            }\n          } else {\n            outData.writeUInt16BE(grayscale, outIndex);\n            if (outHasAlpha) {\n              outData.writeUInt16BE(rgba.alpha, outIndex + 2);\n            }\n          }\n          break;\n        }\n        default:\n          throw new Error(\"unrecognised color Type \" + options.colorType);\n      }\n\n      inIndex += inBpp;\n      outIndex += outBpp;\n    }\n  }\n\n  return outData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/bitpacker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/chunkstream.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/chunkstream.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet util = __webpack_require__(/*! util */ \"util\");\nlet Stream = __webpack_require__(/*! stream */ \"stream\");\n\nlet ChunkStream = (module.exports = function () {\n  Stream.call(this);\n\n  this._buffers = [];\n  this._buffered = 0;\n\n  this._reads = [];\n  this._paused = false;\n\n  this._encoding = \"utf8\";\n  this.writable = true;\n});\nutil.inherits(ChunkStream, Stream);\n\nChunkStream.prototype.read = function (length, callback) {\n  this._reads.push({\n    length: Math.abs(length), // if length < 0 then at most this length\n    allowLess: length < 0,\n    func: callback,\n  });\n\n  process.nextTick(\n    function () {\n      this._process();\n\n      // its paused and there is not enought data then ask for more\n      if (this._paused && this._reads && this._reads.length > 0) {\n        this._paused = false;\n\n        this.emit(\"drain\");\n      }\n    }.bind(this)\n  );\n};\n\nChunkStream.prototype.write = function (data, encoding) {\n  if (!this.writable) {\n    this.emit(\"error\", new Error(\"Stream not writable\"));\n    return false;\n  }\n\n  let dataBuffer;\n  if (Buffer.isBuffer(data)) {\n    dataBuffer = data;\n  } else {\n    dataBuffer = Buffer.from(data, encoding || this._encoding);\n  }\n\n  this._buffers.push(dataBuffer);\n  this._buffered += dataBuffer.length;\n\n  this._process();\n\n  // ok if there are no more read requests\n  if (this._reads && this._reads.length === 0) {\n    this._paused = true;\n  }\n\n  return this.writable && !this._paused;\n};\n\nChunkStream.prototype.end = function (data, encoding) {\n  if (data) {\n    this.write(data, encoding);\n  }\n\n  this.writable = false;\n\n  // already destroyed\n  if (!this._buffers) {\n    return;\n  }\n\n  // enqueue or handle end\n  if (this._buffers.length === 0) {\n    this._end();\n  } else {\n    this._buffers.push(null);\n    this._process();\n  }\n};\n\nChunkStream.prototype.destroySoon = ChunkStream.prototype.end;\n\nChunkStream.prototype._end = function () {\n  if (this._reads.length > 0) {\n    this.emit(\"error\", new Error(\"Unexpected end of input\"));\n  }\n\n  this.destroy();\n};\n\nChunkStream.prototype.destroy = function () {\n  if (!this._buffers) {\n    return;\n  }\n\n  this.writable = false;\n  this._reads = null;\n  this._buffers = null;\n\n  this.emit(\"close\");\n};\n\nChunkStream.prototype._processReadAllowingLess = function (read) {\n  // ok there is any data so that we can satisfy this request\n  this._reads.shift(); // == read\n\n  // first we need to peek into first buffer\n  let smallerBuf = this._buffers[0];\n\n  // ok there is more data than we need\n  if (smallerBuf.length > read.length) {\n    this._buffered -= read.length;\n    this._buffers[0] = smallerBuf.slice(read.length);\n\n    read.func.call(this, smallerBuf.slice(0, read.length));\n  } else {\n    // ok this is less than maximum length so use it all\n    this._buffered -= smallerBuf.length;\n    this._buffers.shift(); // == smallerBuf\n\n    read.func.call(this, smallerBuf);\n  }\n};\n\nChunkStream.prototype._processRead = function (read) {\n  this._reads.shift(); // == read\n\n  let pos = 0;\n  let count = 0;\n  let data = Buffer.alloc(read.length);\n\n  // create buffer for all data\n  while (pos < read.length) {\n    let buf = this._buffers[count++];\n    let len = Math.min(buf.length, read.length - pos);\n\n    buf.copy(data, pos, 0, len);\n    pos += len;\n\n    // last buffer wasn't used all so just slice it and leave\n    if (len !== buf.length) {\n      this._buffers[--count] = buf.slice(len);\n    }\n  }\n\n  // remove all used buffers\n  if (count > 0) {\n    this._buffers.splice(0, count);\n  }\n\n  this._buffered -= read.length;\n\n  read.func.call(this, data);\n};\n\nChunkStream.prototype._process = function () {\n  try {\n    // as long as there is any data and read requests\n    while (this._buffered > 0 && this._reads && this._reads.length > 0) {\n      let read = this._reads[0];\n\n      // read any data (but no more than length)\n      if (read.allowLess) {\n        this._processReadAllowingLess(read);\n      } else if (this._buffered >= read.length) {\n        // ok we can meet some expectations\n\n        this._processRead(read);\n      } else {\n        // not enought data to satisfy first request in queue\n        // so we need to wait for more\n        break;\n      }\n    }\n\n    if (this._buffers && !this.writable) {\n      this._end();\n    }\n  } catch (ex) {\n    this.emit(\"error\", ex);\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/chunkstream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/constants.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/constants.js ***!
  \*********************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = {\n  PNG_SIGNATURE: [0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a],\n\n  TYPE_IHDR: 0x49484452,\n  TYPE_IEND: 0x49454e44,\n  TYPE_IDAT: 0x49444154,\n  TYPE_PLTE: 0x504c5445,\n  TYPE_tRNS: 0x74524e53, // eslint-disable-line camelcase\n  TYPE_gAMA: 0x67414d41, // eslint-disable-line camelcase\n\n  // color-type bits\n  COLORTYPE_GRAYSCALE: 0,\n  COLORTYPE_PALETTE: 1,\n  COLORTYPE_COLOR: 2,\n  COLORTYPE_ALPHA: 4, // e.g. grayscale and alpha\n\n  // color-type combinations\n  COLORTYPE_PALETTE_COLOR: 3,\n  COLORTYPE_COLOR_ALPHA: 6,\n\n  COLORTYPE_TO_BPP_MAP: {\n    0: 1,\n    2: 3,\n    3: 1,\n    4: 2,\n    6: 4,\n  },\n\n  GAMMA_DIVISION: 100000,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvcG5nanMvbGliL2NvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIFBOR19TSUdOQVRVUkU6IFsweDg5LCAweDUwLCAweDRlLCAweDQ3LCAweDBkLCAweDBhLCAweDFhLCAweDBhXSxcblxuICBUWVBFX0lIRFI6IDB4NDk0ODQ0NTIsXG4gIFRZUEVfSUVORDogMHg0OTQ1NGU0NCxcbiAgVFlQRV9JREFUOiAweDQ5NDQ0MTU0LFxuICBUWVBFX1BMVEU6IDB4NTA0YzU0NDUsXG4gIFRZUEVfdFJOUzogMHg3NDUyNGU1MywgLy8gZXNsaW50LWRpc2FibGUtbGluZSBjYW1lbGNhc2VcbiAgVFlQRV9nQU1BOiAweDY3NDE0ZDQxLCAvLyBlc2xpbnQtZGlzYWJsZS1saW5lIGNhbWVsY2FzZVxuXG4gIC8vIGNvbG9yLXR5cGUgYml0c1xuICBDT0xPUlRZUEVfR1JBWVNDQUxFOiAwLFxuICBDT0xPUlRZUEVfUEFMRVRURTogMSxcbiAgQ09MT1JUWVBFX0NPTE9SOiAyLFxuICBDT0xPUlRZUEVfQUxQSEE6IDQsIC8vIGUuZy4gZ3JheXNjYWxlIGFuZCBhbHBoYVxuXG4gIC8vIGNvbG9yLXR5cGUgY29tYmluYXRpb25zXG4gIENPTE9SVFlQRV9QQUxFVFRFX0NPTE9SOiAzLFxuICBDT0xPUlRZUEVfQ09MT1JfQUxQSEE6IDYsXG5cbiAgQ09MT1JUWVBFX1RPX0JQUF9NQVA6IHtcbiAgICAwOiAxLFxuICAgIDI6IDMsXG4gICAgMzogMSxcbiAgICA0OiAyLFxuICAgIDY6IDQsXG4gIH0sXG5cbiAgR0FNTUFfRElWSVNJT046IDEwMDAwMCxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/crc.js":
/*!***************************************!*\
  !*** ./node_modules/pngjs/lib/crc.js ***!
  \***************************************/
/***/ ((module) => {

eval("\n\nlet crcTable = [];\n\n(function () {\n  for (let i = 0; i < 256; i++) {\n    let currentCrc = i;\n    for (let j = 0; j < 8; j++) {\n      if (currentCrc & 1) {\n        currentCrc = 0xedb88320 ^ (currentCrc >>> 1);\n      } else {\n        currentCrc = currentCrc >>> 1;\n      }\n    }\n    crcTable[i] = currentCrc;\n  }\n})();\n\nlet CrcCalculator = (module.exports = function () {\n  this._crc = -1;\n});\n\nCrcCalculator.prototype.write = function (data) {\n  for (let i = 0; i < data.length; i++) {\n    this._crc = crcTable[(this._crc ^ data[i]) & 0xff] ^ (this._crc >>> 8);\n  }\n  return true;\n};\n\nCrcCalculator.prototype.crc32 = function () {\n  return this._crc ^ -1;\n};\n\nCrcCalculator.crc32 = function (buf) {\n  let crc = -1;\n  for (let i = 0; i < buf.length; i++) {\n    crc = crcTable[(crc ^ buf[i]) & 0xff] ^ (crc >>> 8);\n  }\n  return crc ^ -1;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2NyYy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjs7QUFFQTtBQUNBLGtCQUFrQixTQUFTO0FBQzNCO0FBQ0Esb0JBQW9CLE9BQU87QUFDM0I7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBLGtCQUFrQixpQkFBaUI7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxrQkFBa0IsZ0JBQWdCO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3BuZ2pzL2xpYi9jcmMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmxldCBjcmNUYWJsZSA9IFtdO1xuXG4oZnVuY3Rpb24gKCkge1xuICBmb3IgKGxldCBpID0gMDsgaSA8IDI1NjsgaSsrKSB7XG4gICAgbGV0IGN1cnJlbnRDcmMgPSBpO1xuICAgIGZvciAobGV0IGogPSAwOyBqIDwgODsgaisrKSB7XG4gICAgICBpZiAoY3VycmVudENyYyAmIDEpIHtcbiAgICAgICAgY3VycmVudENyYyA9IDB4ZWRiODgzMjAgXiAoY3VycmVudENyYyA+Pj4gMSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjdXJyZW50Q3JjID0gY3VycmVudENyYyA+Pj4gMTtcbiAgICAgIH1cbiAgICB9XG4gICAgY3JjVGFibGVbaV0gPSBjdXJyZW50Q3JjO1xuICB9XG59KSgpO1xuXG5sZXQgQ3JjQ2FsY3VsYXRvciA9IChtb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uICgpIHtcbiAgdGhpcy5fY3JjID0gLTE7XG59KTtcblxuQ3JjQ2FsY3VsYXRvci5wcm90b3R5cGUud3JpdGUgPSBmdW5jdGlvbiAoZGF0YSkge1xuICBmb3IgKGxldCBpID0gMDsgaSA8IGRhdGEubGVuZ3RoOyBpKyspIHtcbiAgICB0aGlzLl9jcmMgPSBjcmNUYWJsZVsodGhpcy5fY3JjIF4gZGF0YVtpXSkgJiAweGZmXSBeICh0aGlzLl9jcmMgPj4+IDgpO1xuICB9XG4gIHJldHVybiB0cnVlO1xufTtcblxuQ3JjQ2FsY3VsYXRvci5wcm90b3R5cGUuY3JjMzIgPSBmdW5jdGlvbiAoKSB7XG4gIHJldHVybiB0aGlzLl9jcmMgXiAtMTtcbn07XG5cbkNyY0NhbGN1bGF0b3IuY3JjMzIgPSBmdW5jdGlvbiAoYnVmKSB7XG4gIGxldCBjcmMgPSAtMTtcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBidWYubGVuZ3RoOyBpKyspIHtcbiAgICBjcmMgPSBjcmNUYWJsZVsoY3JjIF4gYnVmW2ldKSAmIDB4ZmZdIF4gKGNyYyA+Pj4gOCk7XG4gIH1cbiAgcmV0dXJuIGNyYyBeIC0xO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/crc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/filter-pack.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/filter-pack.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet paethPredictor = __webpack_require__(/*! ./paeth-predictor */ \"(ssr)/./node_modules/pngjs/lib/paeth-predictor.js\");\n\nfunction filterNone(pxData, pxPos, byteWidth, rawData, rawPos) {\n  for (let x = 0; x < byteWidth; x++) {\n    rawData[rawPos + x] = pxData[pxPos + x];\n  }\n}\n\nfunction filterSumNone(pxData, pxPos, byteWidth) {\n  let sum = 0;\n  let length = pxPos + byteWidth;\n\n  for (let i = pxPos; i < length; i++) {\n    sum += Math.abs(pxData[i]);\n  }\n  return sum;\n}\n\nfunction filterSub(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let val = pxData[pxPos + x] - left;\n\n    rawData[rawPos + x] = val;\n  }\n}\n\nfunction filterSumSub(pxData, pxPos, byteWidth, bpp) {\n  let sum = 0;\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let val = pxData[pxPos + x] - left;\n\n    sum += Math.abs(val);\n  }\n\n  return sum;\n}\n\nfunction filterUp(pxData, pxPos, byteWidth, rawData, rawPos) {\n  for (let x = 0; x < byteWidth; x++) {\n    let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    let val = pxData[pxPos + x] - up;\n\n    rawData[rawPos + x] = val;\n  }\n}\n\nfunction filterSumUp(pxData, pxPos, byteWidth) {\n  let sum = 0;\n  let length = pxPos + byteWidth;\n  for (let x = pxPos; x < length; x++) {\n    let up = pxPos > 0 ? pxData[x - byteWidth] : 0;\n    let val = pxData[x] - up;\n\n    sum += Math.abs(val);\n  }\n\n  return sum;\n}\n\nfunction filterAvg(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    let val = pxData[pxPos + x] - ((left + up) >> 1);\n\n    rawData[rawPos + x] = val;\n  }\n}\n\nfunction filterSumAvg(pxData, pxPos, byteWidth, bpp) {\n  let sum = 0;\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    let val = pxData[pxPos + x] - ((left + up) >> 1);\n\n    sum += Math.abs(val);\n  }\n\n  return sum;\n}\n\nfunction filterPaeth(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    let upleft =\n      pxPos > 0 && x >= bpp ? pxData[pxPos + x - (byteWidth + bpp)] : 0;\n    let val = pxData[pxPos + x] - paethPredictor(left, up, upleft);\n\n    rawData[rawPos + x] = val;\n  }\n}\n\nfunction filterSumPaeth(pxData, pxPos, byteWidth, bpp) {\n  let sum = 0;\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    let upleft =\n      pxPos > 0 && x >= bpp ? pxData[pxPos + x - (byteWidth + bpp)] : 0;\n    let val = pxData[pxPos + x] - paethPredictor(left, up, upleft);\n\n    sum += Math.abs(val);\n  }\n\n  return sum;\n}\n\nlet filters = {\n  0: filterNone,\n  1: filterSub,\n  2: filterUp,\n  3: filterAvg,\n  4: filterPaeth,\n};\n\nlet filterSums = {\n  0: filterSumNone,\n  1: filterSumSub,\n  2: filterSumUp,\n  3: filterSumAvg,\n  4: filterSumPaeth,\n};\n\nmodule.exports = function (pxData, width, height, options, bpp) {\n  let filterTypes;\n  if (!(\"filterType\" in options) || options.filterType === -1) {\n    filterTypes = [0, 1, 2, 3, 4];\n  } else if (typeof options.filterType === \"number\") {\n    filterTypes = [options.filterType];\n  } else {\n    throw new Error(\"unrecognised filter types\");\n  }\n\n  if (options.bitDepth === 16) {\n    bpp *= 2;\n  }\n  let byteWidth = width * bpp;\n  let rawPos = 0;\n  let pxPos = 0;\n  let rawData = Buffer.alloc((byteWidth + 1) * height);\n\n  let sel = filterTypes[0];\n\n  for (let y = 0; y < height; y++) {\n    if (filterTypes.length > 1) {\n      // find best filter for this line (with lowest sum of values)\n      let min = Infinity;\n\n      for (let i = 0; i < filterTypes.length; i++) {\n        let sum = filterSums[filterTypes[i]](pxData, pxPos, byteWidth, bpp);\n        if (sum < min) {\n          sel = filterTypes[i];\n          min = sum;\n        }\n      }\n    }\n\n    rawData[rawPos] = sel;\n    rawPos++;\n    filters[sel](pxData, pxPos, byteWidth, rawData, rawPos, bpp);\n    rawPos += byteWidth;\n    pxPos += byteWidth;\n  }\n  return rawData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/filter-pack.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/filter-parse-async.js":
/*!******************************************************!*\
  !*** ./node_modules/pngjs/lib/filter-parse-async.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet util = __webpack_require__(/*! util */ \"util\");\nlet ChunkStream = __webpack_require__(/*! ./chunkstream */ \"(ssr)/./node_modules/pngjs/lib/chunkstream.js\");\nlet Filter = __webpack_require__(/*! ./filter-parse */ \"(ssr)/./node_modules/pngjs/lib/filter-parse.js\");\n\nlet FilterAsync = (module.exports = function (bitmapInfo) {\n  ChunkStream.call(this);\n\n  let buffers = [];\n  let that = this;\n  this._filter = new Filter(bitmapInfo, {\n    read: this.read.bind(this),\n    write: function (buffer) {\n      buffers.push(buffer);\n    },\n    complete: function () {\n      that.emit(\"complete\", Buffer.concat(buffers));\n    },\n  });\n\n  this._filter.start();\n});\nutil.inherits(FilterAsync, ChunkStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2ZpbHRlci1wYXJzZS1hc3luYy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixXQUFXLG1CQUFPLENBQUMsa0JBQU07QUFDekIsa0JBQWtCLG1CQUFPLENBQUMsb0VBQWU7QUFDekMsYUFBYSxtQkFBTyxDQUFDLHNFQUFnQjs7QUFFckM7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHOztBQUVIO0FBQ0EsQ0FBQztBQUNEIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3BuZ2pzL2xpYi9maWx0ZXItcGFyc2UtYXN5bmMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmxldCB1dGlsID0gcmVxdWlyZShcInV0aWxcIik7XG5sZXQgQ2h1bmtTdHJlYW0gPSByZXF1aXJlKFwiLi9jaHVua3N0cmVhbVwiKTtcbmxldCBGaWx0ZXIgPSByZXF1aXJlKFwiLi9maWx0ZXItcGFyc2VcIik7XG5cbmxldCBGaWx0ZXJBc3luYyA9IChtb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIChiaXRtYXBJbmZvKSB7XG4gIENodW5rU3RyZWFtLmNhbGwodGhpcyk7XG5cbiAgbGV0IGJ1ZmZlcnMgPSBbXTtcbiAgbGV0IHRoYXQgPSB0aGlzO1xuICB0aGlzLl9maWx0ZXIgPSBuZXcgRmlsdGVyKGJpdG1hcEluZm8sIHtcbiAgICByZWFkOiB0aGlzLnJlYWQuYmluZCh0aGlzKSxcbiAgICB3cml0ZTogZnVuY3Rpb24gKGJ1ZmZlcikge1xuICAgICAgYnVmZmVycy5wdXNoKGJ1ZmZlcik7XG4gICAgfSxcbiAgICBjb21wbGV0ZTogZnVuY3Rpb24gKCkge1xuICAgICAgdGhhdC5lbWl0KFwiY29tcGxldGVcIiwgQnVmZmVyLmNvbmNhdChidWZmZXJzKSk7XG4gICAgfSxcbiAgfSk7XG5cbiAgdGhpcy5fZmlsdGVyLnN0YXJ0KCk7XG59KTtcbnV0aWwuaW5oZXJpdHMoRmlsdGVyQXN5bmMsIENodW5rU3RyZWFtKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/filter-parse-async.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/filter-parse-sync.js":
/*!*****************************************************!*\
  !*** ./node_modules/pngjs/lib/filter-parse-sync.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nlet SyncReader = __webpack_require__(/*! ./sync-reader */ \"(ssr)/./node_modules/pngjs/lib/sync-reader.js\");\nlet Filter = __webpack_require__(/*! ./filter-parse */ \"(ssr)/./node_modules/pngjs/lib/filter-parse.js\");\n\nexports.process = function (inBuffer, bitmapInfo) {\n  let outBuffers = [];\n  let reader = new SyncReader(inBuffer);\n  let filter = new Filter(bitmapInfo, {\n    read: reader.read.bind(reader),\n    write: function (bufferPart) {\n      outBuffers.push(bufferPart);\n    },\n    complete: function () {},\n  });\n\n  filter.start();\n  reader.process();\n\n  return Buffer.concat(outBuffers);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2ZpbHRlci1wYXJzZS1zeW5jLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLGlCQUFpQixtQkFBTyxDQUFDLG9FQUFlO0FBQ3hDLGFBQWEsbUJBQU8sQ0FBQyxzRUFBZ0I7O0FBRXJDLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsNEJBQTRCO0FBQzVCLEdBQUc7O0FBRUg7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3BuZ2pzL2xpYi9maWx0ZXItcGFyc2Utc3luYy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubGV0IFN5bmNSZWFkZXIgPSByZXF1aXJlKFwiLi9zeW5jLXJlYWRlclwiKTtcbmxldCBGaWx0ZXIgPSByZXF1aXJlKFwiLi9maWx0ZXItcGFyc2VcIik7XG5cbmV4cG9ydHMucHJvY2VzcyA9IGZ1bmN0aW9uIChpbkJ1ZmZlciwgYml0bWFwSW5mbykge1xuICBsZXQgb3V0QnVmZmVycyA9IFtdO1xuICBsZXQgcmVhZGVyID0gbmV3IFN5bmNSZWFkZXIoaW5CdWZmZXIpO1xuICBsZXQgZmlsdGVyID0gbmV3IEZpbHRlcihiaXRtYXBJbmZvLCB7XG4gICAgcmVhZDogcmVhZGVyLnJlYWQuYmluZChyZWFkZXIpLFxuICAgIHdyaXRlOiBmdW5jdGlvbiAoYnVmZmVyUGFydCkge1xuICAgICAgb3V0QnVmZmVycy5wdXNoKGJ1ZmZlclBhcnQpO1xuICAgIH0sXG4gICAgY29tcGxldGU6IGZ1bmN0aW9uICgpIHt9LFxuICB9KTtcblxuICBmaWx0ZXIuc3RhcnQoKTtcbiAgcmVhZGVyLnByb2Nlc3MoKTtcblxuICByZXR1cm4gQnVmZmVyLmNvbmNhdChvdXRCdWZmZXJzKTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/filter-parse-sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/filter-parse.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/filter-parse.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet interlaceUtils = __webpack_require__(/*! ./interlace */ \"(ssr)/./node_modules/pngjs/lib/interlace.js\");\nlet paethPredictor = __webpack_require__(/*! ./paeth-predictor */ \"(ssr)/./node_modules/pngjs/lib/paeth-predictor.js\");\n\nfunction getByteWidth(width, bpp, depth) {\n  let byteWidth = width * bpp;\n  if (depth !== 8) {\n    byteWidth = Math.ceil(byteWidth / (8 / depth));\n  }\n  return byteWidth;\n}\n\nlet Filter = (module.exports = function (bitmapInfo, dependencies) {\n  let width = bitmapInfo.width;\n  let height = bitmapInfo.height;\n  let interlace = bitmapInfo.interlace;\n  let bpp = bitmapInfo.bpp;\n  let depth = bitmapInfo.depth;\n\n  this.read = dependencies.read;\n  this.write = dependencies.write;\n  this.complete = dependencies.complete;\n\n  this._imageIndex = 0;\n  this._images = [];\n  if (interlace) {\n    let passes = interlaceUtils.getImagePasses(width, height);\n    for (let i = 0; i < passes.length; i++) {\n      this._images.push({\n        byteWidth: getByteWidth(passes[i].width, bpp, depth),\n        height: passes[i].height,\n        lineIndex: 0,\n      });\n    }\n  } else {\n    this._images.push({\n      byteWidth: getByteWidth(width, bpp, depth),\n      height: height,\n      lineIndex: 0,\n    });\n  }\n\n  // when filtering the line we look at the pixel to the left\n  // the spec also says it is done on a byte level regardless of the number of pixels\n  // so if the depth is byte compatible (8 or 16) we subtract the bpp in order to compare back\n  // a pixel rather than just a different byte part. However if we are sub byte, we ignore.\n  if (depth === 8) {\n    this._xComparison = bpp;\n  } else if (depth === 16) {\n    this._xComparison = bpp * 2;\n  } else {\n    this._xComparison = 1;\n  }\n});\n\nFilter.prototype.start = function () {\n  this.read(\n    this._images[this._imageIndex].byteWidth + 1,\n    this._reverseFilterLine.bind(this)\n  );\n};\n\nFilter.prototype._unFilterType1 = function (\n  rawData,\n  unfilteredLine,\n  byteWidth\n) {\n  let xComparison = this._xComparison;\n  let xBiggerThan = xComparison - 1;\n\n  for (let x = 0; x < byteWidth; x++) {\n    let rawByte = rawData[1 + x];\n    let f1Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n    unfilteredLine[x] = rawByte + f1Left;\n  }\n};\n\nFilter.prototype._unFilterType2 = function (\n  rawData,\n  unfilteredLine,\n  byteWidth\n) {\n  let lastLine = this._lastLine;\n\n  for (let x = 0; x < byteWidth; x++) {\n    let rawByte = rawData[1 + x];\n    let f2Up = lastLine ? lastLine[x] : 0;\n    unfilteredLine[x] = rawByte + f2Up;\n  }\n};\n\nFilter.prototype._unFilterType3 = function (\n  rawData,\n  unfilteredLine,\n  byteWidth\n) {\n  let xComparison = this._xComparison;\n  let xBiggerThan = xComparison - 1;\n  let lastLine = this._lastLine;\n\n  for (let x = 0; x < byteWidth; x++) {\n    let rawByte = rawData[1 + x];\n    let f3Up = lastLine ? lastLine[x] : 0;\n    let f3Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n    let f3Add = Math.floor((f3Left + f3Up) / 2);\n    unfilteredLine[x] = rawByte + f3Add;\n  }\n};\n\nFilter.prototype._unFilterType4 = function (\n  rawData,\n  unfilteredLine,\n  byteWidth\n) {\n  let xComparison = this._xComparison;\n  let xBiggerThan = xComparison - 1;\n  let lastLine = this._lastLine;\n\n  for (let x = 0; x < byteWidth; x++) {\n    let rawByte = rawData[1 + x];\n    let f4Up = lastLine ? lastLine[x] : 0;\n    let f4Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n    let f4UpLeft = x > xBiggerThan && lastLine ? lastLine[x - xComparison] : 0;\n    let f4Add = paethPredictor(f4Left, f4Up, f4UpLeft);\n    unfilteredLine[x] = rawByte + f4Add;\n  }\n};\n\nFilter.prototype._reverseFilterLine = function (rawData) {\n  let filter = rawData[0];\n  let unfilteredLine;\n  let currentImage = this._images[this._imageIndex];\n  let byteWidth = currentImage.byteWidth;\n\n  if (filter === 0) {\n    unfilteredLine = rawData.slice(1, byteWidth + 1);\n  } else {\n    unfilteredLine = Buffer.alloc(byteWidth);\n\n    switch (filter) {\n      case 1:\n        this._unFilterType1(rawData, unfilteredLine, byteWidth);\n        break;\n      case 2:\n        this._unFilterType2(rawData, unfilteredLine, byteWidth);\n        break;\n      case 3:\n        this._unFilterType3(rawData, unfilteredLine, byteWidth);\n        break;\n      case 4:\n        this._unFilterType4(rawData, unfilteredLine, byteWidth);\n        break;\n      default:\n        throw new Error(\"Unrecognised filter type - \" + filter);\n    }\n  }\n\n  this.write(unfilteredLine);\n\n  currentImage.lineIndex++;\n  if (currentImage.lineIndex >= currentImage.height) {\n    this._lastLine = null;\n    this._imageIndex++;\n    currentImage = this._images[this._imageIndex];\n  } else {\n    this._lastLine = unfilteredLine;\n  }\n\n  if (currentImage) {\n    // read, using the byte width that may be from the new current image\n    this.read(currentImage.byteWidth + 1, this._reverseFilterLine.bind(this));\n  } else {\n    this._lastLine = null;\n    this.complete();\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/filter-parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/format-normaliser.js":
/*!*****************************************************!*\
  !*** ./node_modules/pngjs/lib/format-normaliser.js ***!
  \*****************************************************/
/***/ ((module) => {

eval("\n\nfunction dePalette(indata, outdata, width, height, palette) {\n  let pxPos = 0;\n  // use values from palette\n  for (let y = 0; y < height; y++) {\n    for (let x = 0; x < width; x++) {\n      let color = palette[indata[pxPos]];\n\n      if (!color) {\n        throw new Error(\"index \" + indata[pxPos] + \" not in palette\");\n      }\n\n      for (let i = 0; i < 4; i++) {\n        outdata[pxPos + i] = color[i];\n      }\n      pxPos += 4;\n    }\n  }\n}\n\nfunction replaceTransparentColor(indata, outdata, width, height, transColor) {\n  let pxPos = 0;\n  for (let y = 0; y < height; y++) {\n    for (let x = 0; x < width; x++) {\n      let makeTrans = false;\n\n      if (transColor.length === 1) {\n        if (transColor[0] === indata[pxPos]) {\n          makeTrans = true;\n        }\n      } else if (\n        transColor[0] === indata[pxPos] &&\n        transColor[1] === indata[pxPos + 1] &&\n        transColor[2] === indata[pxPos + 2]\n      ) {\n        makeTrans = true;\n      }\n      if (makeTrans) {\n        for (let i = 0; i < 4; i++) {\n          outdata[pxPos + i] = 0;\n        }\n      }\n      pxPos += 4;\n    }\n  }\n}\n\nfunction scaleDepth(indata, outdata, width, height, depth) {\n  let maxOutSample = 255;\n  let maxInSample = Math.pow(2, depth) - 1;\n  let pxPos = 0;\n\n  for (let y = 0; y < height; y++) {\n    for (let x = 0; x < width; x++) {\n      for (let i = 0; i < 4; i++) {\n        outdata[pxPos + i] = Math.floor(\n          (indata[pxPos + i] * maxOutSample) / maxInSample + 0.5\n        );\n      }\n      pxPos += 4;\n    }\n  }\n}\n\nmodule.exports = function (indata, imageData) {\n  let depth = imageData.depth;\n  let width = imageData.width;\n  let height = imageData.height;\n  let colorType = imageData.colorType;\n  let transColor = imageData.transColor;\n  let palette = imageData.palette;\n\n  let outdata = indata; // only different for 16 bits\n\n  if (colorType === 3) {\n    // paletted\n    dePalette(indata, outdata, width, height, palette);\n  } else {\n    if (transColor) {\n      replaceTransparentColor(indata, outdata, width, height, transColor);\n    }\n    // if it needs scaling\n    if (depth !== 8) {\n      // if we need to change the buffer size\n      if (depth === 16) {\n        outdata = Buffer.alloc(width * height * 4);\n      }\n      scaleDepth(indata, outdata, width, height, depth);\n    }\n  }\n  return outdata;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/format-normaliser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/interlace.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/interlace.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\n// Adam 7\n//   0 1 2 3 4 5 6 7\n// 0 x 6 4 6 x 6 4 6\n// 1 7 7 7 7 7 7 7 7\n// 2 5 6 5 6 5 6 5 6\n// 3 7 7 7 7 7 7 7 7\n// 4 3 6 4 6 3 6 4 6\n// 5 7 7 7 7 7 7 7 7\n// 6 5 6 5 6 5 6 5 6\n// 7 7 7 7 7 7 7 7 7\n\nlet imagePasses = [\n  {\n    // pass 1 - 1px\n    x: [0],\n    y: [0],\n  },\n  {\n    // pass 2 - 1px\n    x: [4],\n    y: [0],\n  },\n  {\n    // pass 3 - 2px\n    x: [0, 4],\n    y: [4],\n  },\n  {\n    // pass 4 - 4px\n    x: [2, 6],\n    y: [0, 4],\n  },\n  {\n    // pass 5 - 8px\n    x: [0, 2, 4, 6],\n    y: [2, 6],\n  },\n  {\n    // pass 6 - 16px\n    x: [1, 3, 5, 7],\n    y: [0, 2, 4, 6],\n  },\n  {\n    // pass 7 - 32px\n    x: [0, 1, 2, 3, 4, 5, 6, 7],\n    y: [1, 3, 5, 7],\n  },\n];\n\nexports.getImagePasses = function (width, height) {\n  let images = [];\n  let xLeftOver = width % 8;\n  let yLeftOver = height % 8;\n  let xRepeats = (width - xLeftOver) / 8;\n  let yRepeats = (height - yLeftOver) / 8;\n  for (let i = 0; i < imagePasses.length; i++) {\n    let pass = imagePasses[i];\n    let passWidth = xRepeats * pass.x.length;\n    let passHeight = yRepeats * pass.y.length;\n    for (let j = 0; j < pass.x.length; j++) {\n      if (pass.x[j] < xLeftOver) {\n        passWidth++;\n      } else {\n        break;\n      }\n    }\n    for (let j = 0; j < pass.y.length; j++) {\n      if (pass.y[j] < yLeftOver) {\n        passHeight++;\n      } else {\n        break;\n      }\n    }\n    if (passWidth > 0 && passHeight > 0) {\n      images.push({ width: passWidth, height: passHeight, index: i });\n    }\n  }\n  return images;\n};\n\nexports.getInterlaceIterator = function (width) {\n  return function (x, y, pass) {\n    let outerXLeftOver = x % imagePasses[pass].x.length;\n    let outerX =\n      ((x - outerXLeftOver) / imagePasses[pass].x.length) * 8 +\n      imagePasses[pass].x[outerXLeftOver];\n    let outerYLeftOver = y % imagePasses[pass].y.length;\n    let outerY =\n      ((y - outerYLeftOver) / imagePasses[pass].y.length) * 8 +\n      imagePasses[pass].y[outerYLeftOver];\n    return outerX * 4 + outerY * width * 4;\n  };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/interlace.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/packer-async.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/packer-async.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet util = __webpack_require__(/*! util */ \"util\");\nlet Stream = __webpack_require__(/*! stream */ \"stream\");\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nlet Packer = __webpack_require__(/*! ./packer */ \"(ssr)/./node_modules/pngjs/lib/packer.js\");\n\nlet PackerAsync = (module.exports = function (opt) {\n  Stream.call(this);\n\n  let options = opt || {};\n\n  this._packer = new Packer(options);\n  this._deflate = this._packer.createDeflate();\n\n  this.readable = true;\n});\nutil.inherits(PackerAsync, Stream);\n\nPackerAsync.prototype.pack = function (data, width, height, gamma) {\n  // Signature\n  this.emit(\"data\", Buffer.from(constants.PNG_SIGNATURE));\n  this.emit(\"data\", this._packer.packIHDR(width, height));\n\n  if (gamma) {\n    this.emit(\"data\", this._packer.packGAMA(gamma));\n  }\n\n  let filteredData = this._packer.filterData(data, width, height);\n\n  // compress it\n  this._deflate.on(\"error\", this.emit.bind(this, \"error\"));\n\n  this._deflate.on(\n    \"data\",\n    function (compressedData) {\n      this.emit(\"data\", this._packer.packIDAT(compressedData));\n    }.bind(this)\n  );\n\n  this._deflate.on(\n    \"end\",\n    function () {\n      this.emit(\"data\", this._packer.packIEND());\n      this.emit(\"end\");\n    }.bind(this)\n  );\n\n  this._deflate.end(filteredData);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/packer-async.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/packer-sync.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/packer-sync.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet hasSyncZlib = true;\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nif (!zlib.deflateSync) {\n  hasSyncZlib = false;\n}\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nlet Packer = __webpack_require__(/*! ./packer */ \"(ssr)/./node_modules/pngjs/lib/packer.js\");\n\nmodule.exports = function (metaData, opt) {\n  if (!hasSyncZlib) {\n    throw new Error(\n      \"To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0\"\n    );\n  }\n\n  let options = opt || {};\n\n  let packer = new Packer(options);\n\n  let chunks = [];\n\n  // Signature\n  chunks.push(Buffer.from(constants.PNG_SIGNATURE));\n\n  // Header\n  chunks.push(packer.packIHDR(metaData.width, metaData.height));\n\n  if (metaData.gamma) {\n    chunks.push(packer.packGAMA(metaData.gamma));\n  }\n\n  let filteredData = packer.filterData(\n    metaData.data,\n    metaData.width,\n    metaData.height\n  );\n\n  // compress it\n  let compressedData = zlib.deflateSync(\n    filteredData,\n    packer.getDeflateOptions()\n  );\n  filteredData = null;\n\n  if (!compressedData || !compressedData.length) {\n    throw new Error(\"bad png - invalid compressed data response\");\n  }\n  chunks.push(packer.packIDAT(compressedData));\n\n  // End\n  chunks.push(packer.packIEND());\n\n  return Buffer.concat(chunks);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/packer-sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/packer.js":
/*!******************************************!*\
  !*** ./node_modules/pngjs/lib/packer.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nlet CrcStream = __webpack_require__(/*! ./crc */ \"(ssr)/./node_modules/pngjs/lib/crc.js\");\nlet bitPacker = __webpack_require__(/*! ./bitpacker */ \"(ssr)/./node_modules/pngjs/lib/bitpacker.js\");\nlet filter = __webpack_require__(/*! ./filter-pack */ \"(ssr)/./node_modules/pngjs/lib/filter-pack.js\");\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\n\nlet Packer = (module.exports = function (options) {\n  this._options = options;\n\n  options.deflateChunkSize = options.deflateChunkSize || 32 * 1024;\n  options.deflateLevel =\n    options.deflateLevel != null ? options.deflateLevel : 9;\n  options.deflateStrategy =\n    options.deflateStrategy != null ? options.deflateStrategy : 3;\n  options.inputHasAlpha =\n    options.inputHasAlpha != null ? options.inputHasAlpha : true;\n  options.deflateFactory = options.deflateFactory || zlib.createDeflate;\n  options.bitDepth = options.bitDepth || 8;\n  // This is outputColorType\n  options.colorType =\n    typeof options.colorType === \"number\"\n      ? options.colorType\n      : constants.COLORTYPE_COLOR_ALPHA;\n  options.inputColorType =\n    typeof options.inputColorType === \"number\"\n      ? options.inputColorType\n      : constants.COLORTYPE_COLOR_ALPHA;\n\n  if (\n    [\n      constants.COLORTYPE_GRAYSCALE,\n      constants.COLORTYPE_COLOR,\n      constants.COLORTYPE_COLOR_ALPHA,\n      constants.COLORTYPE_ALPHA,\n    ].indexOf(options.colorType) === -1\n  ) {\n    throw new Error(\n      \"option color type:\" + options.colorType + \" is not supported at present\"\n    );\n  }\n  if (\n    [\n      constants.COLORTYPE_GRAYSCALE,\n      constants.COLORTYPE_COLOR,\n      constants.COLORTYPE_COLOR_ALPHA,\n      constants.COLORTYPE_ALPHA,\n    ].indexOf(options.inputColorType) === -1\n  ) {\n    throw new Error(\n      \"option input color type:\" +\n        options.inputColorType +\n        \" is not supported at present\"\n    );\n  }\n  if (options.bitDepth !== 8 && options.bitDepth !== 16) {\n    throw new Error(\n      \"option bit depth:\" + options.bitDepth + \" is not supported at present\"\n    );\n  }\n});\n\nPacker.prototype.getDeflateOptions = function () {\n  return {\n    chunkSize: this._options.deflateChunkSize,\n    level: this._options.deflateLevel,\n    strategy: this._options.deflateStrategy,\n  };\n};\n\nPacker.prototype.createDeflate = function () {\n  return this._options.deflateFactory(this.getDeflateOptions());\n};\n\nPacker.prototype.filterData = function (data, width, height) {\n  // convert to correct format for filtering (e.g. right bpp and bit depth)\n  let packedData = bitPacker(data, width, height, this._options);\n\n  // filter pixel data\n  let bpp = constants.COLORTYPE_TO_BPP_MAP[this._options.colorType];\n  let filteredData = filter(packedData, width, height, this._options, bpp);\n  return filteredData;\n};\n\nPacker.prototype._packChunk = function (type, data) {\n  let len = data ? data.length : 0;\n  let buf = Buffer.alloc(len + 12);\n\n  buf.writeUInt32BE(len, 0);\n  buf.writeUInt32BE(type, 4);\n\n  if (data) {\n    data.copy(buf, 8);\n  }\n\n  buf.writeInt32BE(\n    CrcStream.crc32(buf.slice(4, buf.length - 4)),\n    buf.length - 4\n  );\n  return buf;\n};\n\nPacker.prototype.packGAMA = function (gamma) {\n  let buf = Buffer.alloc(4);\n  buf.writeUInt32BE(Math.floor(gamma * constants.GAMMA_DIVISION), 0);\n  return this._packChunk(constants.TYPE_gAMA, buf);\n};\n\nPacker.prototype.packIHDR = function (width, height) {\n  let buf = Buffer.alloc(13);\n  buf.writeUInt32BE(width, 0);\n  buf.writeUInt32BE(height, 4);\n  buf[8] = this._options.bitDepth; // Bit depth\n  buf[9] = this._options.colorType; // colorType\n  buf[10] = 0; // compression\n  buf[11] = 0; // filter\n  buf[12] = 0; // interlace\n\n  return this._packChunk(constants.TYPE_IHDR, buf);\n};\n\nPacker.prototype.packIDAT = function (data) {\n  return this._packChunk(constants.TYPE_IDAT, data);\n};\n\nPacker.prototype.packIEND = function () {\n  return this._packChunk(constants.TYPE_IEND, null);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/packer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/paeth-predictor.js":
/*!***************************************************!*\
  !*** ./node_modules/pngjs/lib/paeth-predictor.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = function paethPredictor(left, above, upLeft) {\n  let paeth = left + above - upLeft;\n  let pLeft = Math.abs(paeth - left);\n  let pAbove = Math.abs(paeth - above);\n  let pUpLeft = Math.abs(paeth - upLeft);\n\n  if (pLeft <= pAbove && pLeft <= pUpLeft) {\n    return left;\n  }\n  if (pAbove <= pUpLeft) {\n    return above;\n  }\n  return upLeft;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL3BhZXRoLXByZWRpY3Rvci5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvcG5nanMvbGliL3BhZXRoLXByZWRpY3Rvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBwYWV0aFByZWRpY3RvcihsZWZ0LCBhYm92ZSwgdXBMZWZ0KSB7XG4gIGxldCBwYWV0aCA9IGxlZnQgKyBhYm92ZSAtIHVwTGVmdDtcbiAgbGV0IHBMZWZ0ID0gTWF0aC5hYnMocGFldGggLSBsZWZ0KTtcbiAgbGV0IHBBYm92ZSA9IE1hdGguYWJzKHBhZXRoIC0gYWJvdmUpO1xuICBsZXQgcFVwTGVmdCA9IE1hdGguYWJzKHBhZXRoIC0gdXBMZWZ0KTtcblxuICBpZiAocExlZnQgPD0gcEFib3ZlICYmIHBMZWZ0IDw9IHBVcExlZnQpIHtcbiAgICByZXR1cm4gbGVmdDtcbiAgfVxuICBpZiAocEFib3ZlIDw9IHBVcExlZnQpIHtcbiAgICByZXR1cm4gYWJvdmU7XG4gIH1cbiAgcmV0dXJuIHVwTGVmdDtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/paeth-predictor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/parser-async.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/parser-async.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet util = __webpack_require__(/*! util */ \"util\");\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet ChunkStream = __webpack_require__(/*! ./chunkstream */ \"(ssr)/./node_modules/pngjs/lib/chunkstream.js\");\nlet FilterAsync = __webpack_require__(/*! ./filter-parse-async */ \"(ssr)/./node_modules/pngjs/lib/filter-parse-async.js\");\nlet Parser = __webpack_require__(/*! ./parser */ \"(ssr)/./node_modules/pngjs/lib/parser.js\");\nlet bitmapper = __webpack_require__(/*! ./bitmapper */ \"(ssr)/./node_modules/pngjs/lib/bitmapper.js\");\nlet formatNormaliser = __webpack_require__(/*! ./format-normaliser */ \"(ssr)/./node_modules/pngjs/lib/format-normaliser.js\");\n\nlet ParserAsync = (module.exports = function (options) {\n  ChunkStream.call(this);\n\n  this._parser = new Parser(options, {\n    read: this.read.bind(this),\n    error: this._handleError.bind(this),\n    metadata: this._handleMetaData.bind(this),\n    gamma: this.emit.bind(this, \"gamma\"),\n    palette: this._handlePalette.bind(this),\n    transColor: this._handleTransColor.bind(this),\n    finished: this._finished.bind(this),\n    inflateData: this._inflateData.bind(this),\n    simpleTransparency: this._simpleTransparency.bind(this),\n    headersFinished: this._headersFinished.bind(this),\n  });\n  this._options = options;\n  this.writable = true;\n\n  this._parser.start();\n});\nutil.inherits(ParserAsync, ChunkStream);\n\nParserAsync.prototype._handleError = function (err) {\n  this.emit(\"error\", err);\n\n  this.writable = false;\n\n  this.destroy();\n\n  if (this._inflate && this._inflate.destroy) {\n    this._inflate.destroy();\n  }\n\n  if (this._filter) {\n    this._filter.destroy();\n    // For backward compatibility with Node 7 and below.\n    // Suppress errors due to _inflate calling write() even after\n    // it's destroy()'ed.\n    this._filter.on(\"error\", function () {});\n  }\n\n  this.errord = true;\n};\n\nParserAsync.prototype._inflateData = function (data) {\n  if (!this._inflate) {\n    if (this._bitmapInfo.interlace) {\n      this._inflate = zlib.createInflate();\n\n      this._inflate.on(\"error\", this.emit.bind(this, \"error\"));\n      this._filter.on(\"complete\", this._complete.bind(this));\n\n      this._inflate.pipe(this._filter);\n    } else {\n      let rowSize =\n        ((this._bitmapInfo.width *\n          this._bitmapInfo.bpp *\n          this._bitmapInfo.depth +\n          7) >>\n          3) +\n        1;\n      let imageSize = rowSize * this._bitmapInfo.height;\n      let chunkSize = Math.max(imageSize, zlib.Z_MIN_CHUNK);\n\n      this._inflate = zlib.createInflate({ chunkSize: chunkSize });\n      let leftToInflate = imageSize;\n\n      let emitError = this.emit.bind(this, \"error\");\n      this._inflate.on(\"error\", function (err) {\n        if (!leftToInflate) {\n          return;\n        }\n\n        emitError(err);\n      });\n      this._filter.on(\"complete\", this._complete.bind(this));\n\n      let filterWrite = this._filter.write.bind(this._filter);\n      this._inflate.on(\"data\", function (chunk) {\n        if (!leftToInflate) {\n          return;\n        }\n\n        if (chunk.length > leftToInflate) {\n          chunk = chunk.slice(0, leftToInflate);\n        }\n\n        leftToInflate -= chunk.length;\n\n        filterWrite(chunk);\n      });\n\n      this._inflate.on(\"end\", this._filter.end.bind(this._filter));\n    }\n  }\n  this._inflate.write(data);\n};\n\nParserAsync.prototype._handleMetaData = function (metaData) {\n  this._metaData = metaData;\n  this._bitmapInfo = Object.create(metaData);\n\n  this._filter = new FilterAsync(this._bitmapInfo);\n};\n\nParserAsync.prototype._handleTransColor = function (transColor) {\n  this._bitmapInfo.transColor = transColor;\n};\n\nParserAsync.prototype._handlePalette = function (palette) {\n  this._bitmapInfo.palette = palette;\n};\n\nParserAsync.prototype._simpleTransparency = function () {\n  this._metaData.alpha = true;\n};\n\nParserAsync.prototype._headersFinished = function () {\n  // Up until this point, we don't know if we have a tRNS chunk (alpha)\n  // so we can't emit metadata any earlier\n  this.emit(\"metadata\", this._metaData);\n};\n\nParserAsync.prototype._finished = function () {\n  if (this.errord) {\n    return;\n  }\n\n  if (!this._inflate) {\n    this.emit(\"error\", \"No Inflate block\");\n  } else {\n    // no more data to inflate\n    this._inflate.end();\n  }\n};\n\nParserAsync.prototype._complete = function (filteredData) {\n  if (this.errord) {\n    return;\n  }\n\n  let normalisedBitmapData;\n\n  try {\n    let bitmapData = bitmapper.dataToBitMap(filteredData, this._bitmapInfo);\n\n    normalisedBitmapData = formatNormaliser(bitmapData, this._bitmapInfo);\n    bitmapData = null;\n  } catch (ex) {\n    this._handleError(ex);\n    return;\n  }\n\n  this.emit(\"parsed\", normalisedBitmapData);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/parser-async.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/parser-sync.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/parser-sync.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet hasSyncZlib = true;\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet inflateSync = __webpack_require__(/*! ./sync-inflate */ \"(ssr)/./node_modules/pngjs/lib/sync-inflate.js\");\nif (!zlib.deflateSync) {\n  hasSyncZlib = false;\n}\nlet SyncReader = __webpack_require__(/*! ./sync-reader */ \"(ssr)/./node_modules/pngjs/lib/sync-reader.js\");\nlet FilterSync = __webpack_require__(/*! ./filter-parse-sync */ \"(ssr)/./node_modules/pngjs/lib/filter-parse-sync.js\");\nlet Parser = __webpack_require__(/*! ./parser */ \"(ssr)/./node_modules/pngjs/lib/parser.js\");\nlet bitmapper = __webpack_require__(/*! ./bitmapper */ \"(ssr)/./node_modules/pngjs/lib/bitmapper.js\");\nlet formatNormaliser = __webpack_require__(/*! ./format-normaliser */ \"(ssr)/./node_modules/pngjs/lib/format-normaliser.js\");\n\nmodule.exports = function (buffer, options) {\n  if (!hasSyncZlib) {\n    throw new Error(\n      \"To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0\"\n    );\n  }\n\n  let err;\n  function handleError(_err_) {\n    err = _err_;\n  }\n\n  let metaData;\n  function handleMetaData(_metaData_) {\n    metaData = _metaData_;\n  }\n\n  function handleTransColor(transColor) {\n    metaData.transColor = transColor;\n  }\n\n  function handlePalette(palette) {\n    metaData.palette = palette;\n  }\n\n  function handleSimpleTransparency() {\n    metaData.alpha = true;\n  }\n\n  let gamma;\n  function handleGamma(_gamma_) {\n    gamma = _gamma_;\n  }\n\n  let inflateDataList = [];\n  function handleInflateData(inflatedData) {\n    inflateDataList.push(inflatedData);\n  }\n\n  let reader = new SyncReader(buffer);\n\n  let parser = new Parser(options, {\n    read: reader.read.bind(reader),\n    error: handleError,\n    metadata: handleMetaData,\n    gamma: handleGamma,\n    palette: handlePalette,\n    transColor: handleTransColor,\n    inflateData: handleInflateData,\n    simpleTransparency: handleSimpleTransparency,\n  });\n\n  parser.start();\n  reader.process();\n\n  if (err) {\n    throw err;\n  }\n\n  //join together the inflate datas\n  let inflateData = Buffer.concat(inflateDataList);\n  inflateDataList.length = 0;\n\n  let inflatedData;\n  if (metaData.interlace) {\n    inflatedData = zlib.inflateSync(inflateData);\n  } else {\n    let rowSize =\n      ((metaData.width * metaData.bpp * metaData.depth + 7) >> 3) + 1;\n    let imageSize = rowSize * metaData.height;\n    inflatedData = inflateSync(inflateData, {\n      chunkSize: imageSize,\n      maxLength: imageSize,\n    });\n  }\n  inflateData = null;\n\n  if (!inflatedData || !inflatedData.length) {\n    throw new Error(\"bad png - invalid inflate data response\");\n  }\n\n  let unfilteredData = FilterSync.process(inflatedData, metaData);\n  inflateData = null;\n\n  let bitmapData = bitmapper.dataToBitMap(unfilteredData, metaData);\n  unfilteredData = null;\n\n  let normalisedBitmapData = formatNormaliser(bitmapData, metaData);\n\n  metaData.data = normalisedBitmapData;\n  metaData.gamma = gamma || 0;\n\n  return metaData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/parser-sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/parser.js":
/*!******************************************!*\
  !*** ./node_modules/pngjs/lib/parser.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nlet CrcCalculator = __webpack_require__(/*! ./crc */ \"(ssr)/./node_modules/pngjs/lib/crc.js\");\n\nlet Parser = (module.exports = function (options, dependencies) {\n  this._options = options;\n  options.checkCRC = options.checkCRC !== false;\n\n  this._hasIHDR = false;\n  this._hasIEND = false;\n  this._emittedHeadersFinished = false;\n\n  // input flags/metadata\n  this._palette = [];\n  this._colorType = 0;\n\n  this._chunks = {};\n  this._chunks[constants.TYPE_IHDR] = this._handleIHDR.bind(this);\n  this._chunks[constants.TYPE_IEND] = this._handleIEND.bind(this);\n  this._chunks[constants.TYPE_IDAT] = this._handleIDAT.bind(this);\n  this._chunks[constants.TYPE_PLTE] = this._handlePLTE.bind(this);\n  this._chunks[constants.TYPE_tRNS] = this._handleTRNS.bind(this);\n  this._chunks[constants.TYPE_gAMA] = this._handleGAMA.bind(this);\n\n  this.read = dependencies.read;\n  this.error = dependencies.error;\n  this.metadata = dependencies.metadata;\n  this.gamma = dependencies.gamma;\n  this.transColor = dependencies.transColor;\n  this.palette = dependencies.palette;\n  this.parsed = dependencies.parsed;\n  this.inflateData = dependencies.inflateData;\n  this.finished = dependencies.finished;\n  this.simpleTransparency = dependencies.simpleTransparency;\n  this.headersFinished = dependencies.headersFinished || function () {};\n});\n\nParser.prototype.start = function () {\n  this.read(constants.PNG_SIGNATURE.length, this._parseSignature.bind(this));\n};\n\nParser.prototype._parseSignature = function (data) {\n  let signature = constants.PNG_SIGNATURE;\n\n  for (let i = 0; i < signature.length; i++) {\n    if (data[i] !== signature[i]) {\n      this.error(new Error(\"Invalid file signature\"));\n      return;\n    }\n  }\n  this.read(8, this._parseChunkBegin.bind(this));\n};\n\nParser.prototype._parseChunkBegin = function (data) {\n  // chunk content length\n  let length = data.readUInt32BE(0);\n\n  // chunk type\n  let type = data.readUInt32BE(4);\n  let name = \"\";\n  for (let i = 4; i < 8; i++) {\n    name += String.fromCharCode(data[i]);\n  }\n\n  //console.log('chunk ', name, length);\n\n  // chunk flags\n  let ancillary = Boolean(data[4] & 0x20); // or critical\n  //    priv = Boolean(data[5] & 0x20), // or public\n  //    safeToCopy = Boolean(data[7] & 0x20); // or unsafe\n\n  if (!this._hasIHDR && type !== constants.TYPE_IHDR) {\n    this.error(new Error(\"Expected IHDR on beggining\"));\n    return;\n  }\n\n  this._crc = new CrcCalculator();\n  this._crc.write(Buffer.from(name));\n\n  if (this._chunks[type]) {\n    return this._chunks[type](length);\n  }\n\n  if (!ancillary) {\n    this.error(new Error(\"Unsupported critical chunk type \" + name));\n    return;\n  }\n\n  this.read(length + 4, this._skipChunk.bind(this));\n};\n\nParser.prototype._skipChunk = function (/*data*/) {\n  this.read(8, this._parseChunkBegin.bind(this));\n};\n\nParser.prototype._handleChunkEnd = function () {\n  this.read(4, this._parseChunkEnd.bind(this));\n};\n\nParser.prototype._parseChunkEnd = function (data) {\n  let fileCrc = data.readInt32BE(0);\n  let calcCrc = this._crc.crc32();\n\n  // check CRC\n  if (this._options.checkCRC && calcCrc !== fileCrc) {\n    this.error(new Error(\"Crc error - \" + fileCrc + \" - \" + calcCrc));\n    return;\n  }\n\n  if (!this._hasIEND) {\n    this.read(8, this._parseChunkBegin.bind(this));\n  }\n};\n\nParser.prototype._handleIHDR = function (length) {\n  this.read(length, this._parseIHDR.bind(this));\n};\nParser.prototype._parseIHDR = function (data) {\n  this._crc.write(data);\n\n  let width = data.readUInt32BE(0);\n  let height = data.readUInt32BE(4);\n  let depth = data[8];\n  let colorType = data[9]; // bits: 1 palette, 2 color, 4 alpha\n  let compr = data[10];\n  let filter = data[11];\n  let interlace = data[12];\n\n  // console.log('    width', width, 'height', height,\n  //     'depth', depth, 'colorType', colorType,\n  //     'compr', compr, 'filter', filter, 'interlace', interlace\n  // );\n\n  if (\n    depth !== 8 &&\n    depth !== 4 &&\n    depth !== 2 &&\n    depth !== 1 &&\n    depth !== 16\n  ) {\n    this.error(new Error(\"Unsupported bit depth \" + depth));\n    return;\n  }\n  if (!(colorType in constants.COLORTYPE_TO_BPP_MAP)) {\n    this.error(new Error(\"Unsupported color type\"));\n    return;\n  }\n  if (compr !== 0) {\n    this.error(new Error(\"Unsupported compression method\"));\n    return;\n  }\n  if (filter !== 0) {\n    this.error(new Error(\"Unsupported filter method\"));\n    return;\n  }\n  if (interlace !== 0 && interlace !== 1) {\n    this.error(new Error(\"Unsupported interlace method\"));\n    return;\n  }\n\n  this._colorType = colorType;\n\n  let bpp = constants.COLORTYPE_TO_BPP_MAP[this._colorType];\n\n  this._hasIHDR = true;\n\n  this.metadata({\n    width: width,\n    height: height,\n    depth: depth,\n    interlace: Boolean(interlace),\n    palette: Boolean(colorType & constants.COLORTYPE_PALETTE),\n    color: Boolean(colorType & constants.COLORTYPE_COLOR),\n    alpha: Boolean(colorType & constants.COLORTYPE_ALPHA),\n    bpp: bpp,\n    colorType: colorType,\n  });\n\n  this._handleChunkEnd();\n};\n\nParser.prototype._handlePLTE = function (length) {\n  this.read(length, this._parsePLTE.bind(this));\n};\nParser.prototype._parsePLTE = function (data) {\n  this._crc.write(data);\n\n  let entries = Math.floor(data.length / 3);\n  // console.log('Palette:', entries);\n\n  for (let i = 0; i < entries; i++) {\n    this._palette.push([data[i * 3], data[i * 3 + 1], data[i * 3 + 2], 0xff]);\n  }\n\n  this.palette(this._palette);\n\n  this._handleChunkEnd();\n};\n\nParser.prototype._handleTRNS = function (length) {\n  this.simpleTransparency();\n  this.read(length, this._parseTRNS.bind(this));\n};\nParser.prototype._parseTRNS = function (data) {\n  this._crc.write(data);\n\n  // palette\n  if (this._colorType === constants.COLORTYPE_PALETTE_COLOR) {\n    if (this._palette.length === 0) {\n      this.error(new Error(\"Transparency chunk must be after palette\"));\n      return;\n    }\n    if (data.length > this._palette.length) {\n      this.error(new Error(\"More transparent colors than palette size\"));\n      return;\n    }\n    for (let i = 0; i < data.length; i++) {\n      this._palette[i][3] = data[i];\n    }\n    this.palette(this._palette);\n  }\n\n  // for colorType 0 (grayscale) and 2 (rgb)\n  // there might be one gray/color defined as transparent\n  if (this._colorType === constants.COLORTYPE_GRAYSCALE) {\n    // grey, 2 bytes\n    this.transColor([data.readUInt16BE(0)]);\n  }\n  if (this._colorType === constants.COLORTYPE_COLOR) {\n    this.transColor([\n      data.readUInt16BE(0),\n      data.readUInt16BE(2),\n      data.readUInt16BE(4),\n    ]);\n  }\n\n  this._handleChunkEnd();\n};\n\nParser.prototype._handleGAMA = function (length) {\n  this.read(length, this._parseGAMA.bind(this));\n};\nParser.prototype._parseGAMA = function (data) {\n  this._crc.write(data);\n  this.gamma(data.readUInt32BE(0) / constants.GAMMA_DIVISION);\n\n  this._handleChunkEnd();\n};\n\nParser.prototype._handleIDAT = function (length) {\n  if (!this._emittedHeadersFinished) {\n    this._emittedHeadersFinished = true;\n    this.headersFinished();\n  }\n  this.read(-length, this._parseIDAT.bind(this, length));\n};\nParser.prototype._parseIDAT = function (length, data) {\n  this._crc.write(data);\n\n  if (\n    this._colorType === constants.COLORTYPE_PALETTE_COLOR &&\n    this._palette.length === 0\n  ) {\n    throw new Error(\"Expected palette not found\");\n  }\n\n  this.inflateData(data);\n  let leftOverLength = length - data.length;\n\n  if (leftOverLength > 0) {\n    this._handleIDAT(leftOverLength);\n  } else {\n    this._handleChunkEnd();\n  }\n};\n\nParser.prototype._handleIEND = function (length) {\n  this.read(length, this._parseIEND.bind(this));\n};\nParser.prototype._parseIEND = function (data) {\n  this._crc.write(data);\n\n  this._hasIEND = true;\n  this._handleChunkEnd();\n\n  if (this.finished) {\n    this.finished();\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/png-sync.js":
/*!********************************************!*\
  !*** ./node_modules/pngjs/lib/png-sync.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nlet parse = __webpack_require__(/*! ./parser-sync */ \"(ssr)/./node_modules/pngjs/lib/parser-sync.js\");\nlet pack = __webpack_require__(/*! ./packer-sync */ \"(ssr)/./node_modules/pngjs/lib/packer-sync.js\");\n\nexports.read = function (buffer, options) {\n  return parse(buffer, options || {});\n};\n\nexports.write = function (png, options) {\n  return pack(png, options);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL3BuZy1zeW5jLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLFlBQVksbUJBQU8sQ0FBQyxvRUFBZTtBQUNuQyxXQUFXLG1CQUFPLENBQUMsb0VBQWU7O0FBRWxDLFlBQVk7QUFDWixvQ0FBb0M7QUFDcEM7O0FBRUEsYUFBYTtBQUNiO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvcG5nanMvbGliL3BuZy1zeW5jLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5sZXQgcGFyc2UgPSByZXF1aXJlKFwiLi9wYXJzZXItc3luY1wiKTtcbmxldCBwYWNrID0gcmVxdWlyZShcIi4vcGFja2VyLXN5bmNcIik7XG5cbmV4cG9ydHMucmVhZCA9IGZ1bmN0aW9uIChidWZmZXIsIG9wdGlvbnMpIHtcbiAgcmV0dXJuIHBhcnNlKGJ1ZmZlciwgb3B0aW9ucyB8fCB7fSk7XG59O1xuXG5leHBvcnRzLndyaXRlID0gZnVuY3Rpb24gKHBuZywgb3B0aW9ucykge1xuICByZXR1cm4gcGFjayhwbmcsIG9wdGlvbnMpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/png-sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/png.js":
/*!***************************************!*\
  !*** ./node_modules/pngjs/lib/png.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nlet util = __webpack_require__(/*! util */ \"util\");\nlet Stream = __webpack_require__(/*! stream */ \"stream\");\nlet Parser = __webpack_require__(/*! ./parser-async */ \"(ssr)/./node_modules/pngjs/lib/parser-async.js\");\nlet Packer = __webpack_require__(/*! ./packer-async */ \"(ssr)/./node_modules/pngjs/lib/packer-async.js\");\nlet PNGSync = __webpack_require__(/*! ./png-sync */ \"(ssr)/./node_modules/pngjs/lib/png-sync.js\");\n\nlet PNG = (exports.PNG = function (options) {\n  Stream.call(this);\n\n  options = options || {}; // eslint-disable-line no-param-reassign\n\n  // coerce pixel dimensions to integers (also coerces undefined -> 0):\n  this.width = options.width | 0;\n  this.height = options.height | 0;\n\n  this.data =\n    this.width > 0 && this.height > 0\n      ? Buffer.alloc(4 * this.width * this.height)\n      : null;\n\n  if (options.fill && this.data) {\n    this.data.fill(0);\n  }\n\n  this.gamma = 0;\n  this.readable = this.writable = true;\n\n  this._parser = new Parser(options);\n\n  this._parser.on(\"error\", this.emit.bind(this, \"error\"));\n  this._parser.on(\"close\", this._handleClose.bind(this));\n  this._parser.on(\"metadata\", this._metadata.bind(this));\n  this._parser.on(\"gamma\", this._gamma.bind(this));\n  this._parser.on(\n    \"parsed\",\n    function (data) {\n      this.data = data;\n      this.emit(\"parsed\", data);\n    }.bind(this)\n  );\n\n  this._packer = new Packer(options);\n  this._packer.on(\"data\", this.emit.bind(this, \"data\"));\n  this._packer.on(\"end\", this.emit.bind(this, \"end\"));\n  this._parser.on(\"close\", this._handleClose.bind(this));\n  this._packer.on(\"error\", this.emit.bind(this, \"error\"));\n});\nutil.inherits(PNG, Stream);\n\nPNG.sync = PNGSync;\n\nPNG.prototype.pack = function () {\n  if (!this.data || !this.data.length) {\n    this.emit(\"error\", \"No data provided\");\n    return this;\n  }\n\n  process.nextTick(\n    function () {\n      this._packer.pack(this.data, this.width, this.height, this.gamma);\n    }.bind(this)\n  );\n\n  return this;\n};\n\nPNG.prototype.parse = function (data, callback) {\n  if (callback) {\n    let onParsed, onError;\n\n    onParsed = function (parsedData) {\n      this.removeListener(\"error\", onError);\n\n      this.data = parsedData;\n      callback(null, this);\n    }.bind(this);\n\n    onError = function (err) {\n      this.removeListener(\"parsed\", onParsed);\n\n      callback(err, null);\n    }.bind(this);\n\n    this.once(\"parsed\", onParsed);\n    this.once(\"error\", onError);\n  }\n\n  this.end(data);\n  return this;\n};\n\nPNG.prototype.write = function (data) {\n  this._parser.write(data);\n  return true;\n};\n\nPNG.prototype.end = function (data) {\n  this._parser.end(data);\n};\n\nPNG.prototype._metadata = function (metadata) {\n  this.width = metadata.width;\n  this.height = metadata.height;\n\n  this.emit(\"metadata\", metadata);\n};\n\nPNG.prototype._gamma = function (gamma) {\n  this.gamma = gamma;\n};\n\nPNG.prototype._handleClose = function () {\n  if (!this._parser.writable && !this._packer.readable) {\n    this.emit(\"close\");\n  }\n};\n\nPNG.bitblt = function (src, dst, srcX, srcY, width, height, deltaX, deltaY) {\n  // eslint-disable-line max-params\n  // coerce pixel dimensions to integers (also coerces undefined -> 0):\n  /* eslint-disable no-param-reassign */\n  srcX |= 0;\n  srcY |= 0;\n  width |= 0;\n  height |= 0;\n  deltaX |= 0;\n  deltaY |= 0;\n  /* eslint-enable no-param-reassign */\n\n  if (\n    srcX > src.width ||\n    srcY > src.height ||\n    srcX + width > src.width ||\n    srcY + height > src.height\n  ) {\n    throw new Error(\"bitblt reading outside image\");\n  }\n\n  if (\n    deltaX > dst.width ||\n    deltaY > dst.height ||\n    deltaX + width > dst.width ||\n    deltaY + height > dst.height\n  ) {\n    throw new Error(\"bitblt writing outside image\");\n  }\n\n  for (let y = 0; y < height; y++) {\n    src.data.copy(\n      dst.data,\n      ((deltaY + y) * dst.width + deltaX) << 2,\n      ((srcY + y) * src.width + srcX) << 2,\n      ((srcY + y) * src.width + srcX + width) << 2\n    );\n  }\n};\n\nPNG.prototype.bitblt = function (\n  dst,\n  srcX,\n  srcY,\n  width,\n  height,\n  deltaX,\n  deltaY\n) {\n  // eslint-disable-line max-params\n\n  PNG.bitblt(this, dst, srcX, srcY, width, height, deltaX, deltaY);\n  return this;\n};\n\nPNG.adjustGamma = function (src) {\n  if (src.gamma) {\n    for (let y = 0; y < src.height; y++) {\n      for (let x = 0; x < src.width; x++) {\n        let idx = (src.width * y + x) << 2;\n\n        for (let i = 0; i < 3; i++) {\n          let sample = src.data[idx + i] / 255;\n          sample = Math.pow(sample, 1 / 2.2 / src.gamma);\n          src.data[idx + i] = Math.round(sample * 255);\n        }\n      }\n    }\n    src.gamma = 0;\n  }\n};\n\nPNG.prototype.adjustGamma = function () {\n  PNG.adjustGamma(this);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL3BuZy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixXQUFXLG1CQUFPLENBQUMsa0JBQU07QUFDekIsYUFBYSxtQkFBTyxDQUFDLHNCQUFRO0FBQzdCLGFBQWEsbUJBQU8sQ0FBQyxzRUFBZ0I7QUFDckMsYUFBYSxtQkFBTyxDQUFDLHNFQUFnQjtBQUNyQyxjQUFjLG1CQUFPLENBQUMsOERBQVk7O0FBRWxDLFdBQVcsV0FBVztBQUN0Qjs7QUFFQSwyQkFBMkI7O0FBRTNCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxLQUFLOztBQUVMO0FBQ0E7O0FBRUE7QUFDQSxLQUFLOztBQUVMO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGtCQUFrQixZQUFZO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxvQkFBb0IsZ0JBQWdCO0FBQ3BDLHNCQUFzQixlQUFlO0FBQ3JDOztBQUVBLHdCQUF3QixPQUFPO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvcG5nanMvbGliL3BuZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubGV0IHV0aWwgPSByZXF1aXJlKFwidXRpbFwiKTtcbmxldCBTdHJlYW0gPSByZXF1aXJlKFwic3RyZWFtXCIpO1xubGV0IFBhcnNlciA9IHJlcXVpcmUoXCIuL3BhcnNlci1hc3luY1wiKTtcbmxldCBQYWNrZXIgPSByZXF1aXJlKFwiLi9wYWNrZXItYXN5bmNcIik7XG5sZXQgUE5HU3luYyA9IHJlcXVpcmUoXCIuL3BuZy1zeW5jXCIpO1xuXG5sZXQgUE5HID0gKGV4cG9ydHMuUE5HID0gZnVuY3Rpb24gKG9wdGlvbnMpIHtcbiAgU3RyZWFtLmNhbGwodGhpcyk7XG5cbiAgb3B0aW9ucyA9IG9wdGlvbnMgfHwge307IC8vIGVzbGludC1kaXNhYmxlLWxpbmUgbm8tcGFyYW0tcmVhc3NpZ25cblxuICAvLyBjb2VyY2UgcGl4ZWwgZGltZW5zaW9ucyB0byBpbnRlZ2VycyAoYWxzbyBjb2VyY2VzIHVuZGVmaW5lZCAtPiAwKTpcbiAgdGhpcy53aWR0aCA9IG9wdGlvbnMud2lkdGggfCAwO1xuICB0aGlzLmhlaWdodCA9IG9wdGlvbnMuaGVpZ2h0IHwgMDtcblxuICB0aGlzLmRhdGEgPVxuICAgIHRoaXMud2lkdGggPiAwICYmIHRoaXMuaGVpZ2h0ID4gMFxuICAgICAgPyBCdWZmZXIuYWxsb2MoNCAqIHRoaXMud2lkdGggKiB0aGlzLmhlaWdodClcbiAgICAgIDogbnVsbDtcblxuICBpZiAob3B0aW9ucy5maWxsICYmIHRoaXMuZGF0YSkge1xuICAgIHRoaXMuZGF0YS5maWxsKDApO1xuICB9XG5cbiAgdGhpcy5nYW1tYSA9IDA7XG4gIHRoaXMucmVhZGFibGUgPSB0aGlzLndyaXRhYmxlID0gdHJ1ZTtcblxuICB0aGlzLl9wYXJzZXIgPSBuZXcgUGFyc2VyKG9wdGlvbnMpO1xuXG4gIHRoaXMuX3BhcnNlci5vbihcImVycm9yXCIsIHRoaXMuZW1pdC5iaW5kKHRoaXMsIFwiZXJyb3JcIikpO1xuICB0aGlzLl9wYXJzZXIub24oXCJjbG9zZVwiLCB0aGlzLl9oYW5kbGVDbG9zZS5iaW5kKHRoaXMpKTtcbiAgdGhpcy5fcGFyc2VyLm9uKFwibWV0YWRhdGFcIiwgdGhpcy5fbWV0YWRhdGEuYmluZCh0aGlzKSk7XG4gIHRoaXMuX3BhcnNlci5vbihcImdhbW1hXCIsIHRoaXMuX2dhbW1hLmJpbmQodGhpcykpO1xuICB0aGlzLl9wYXJzZXIub24oXG4gICAgXCJwYXJzZWRcIixcbiAgICBmdW5jdGlvbiAoZGF0YSkge1xuICAgICAgdGhpcy5kYXRhID0gZGF0YTtcbiAgICAgIHRoaXMuZW1pdChcInBhcnNlZFwiLCBkYXRhKTtcbiAgICB9LmJpbmQodGhpcylcbiAgKTtcblxuICB0aGlzLl9wYWNrZXIgPSBuZXcgUGFja2VyKG9wdGlvbnMpO1xuICB0aGlzLl9wYWNrZXIub24oXCJkYXRhXCIsIHRoaXMuZW1pdC5iaW5kKHRoaXMsIFwiZGF0YVwiKSk7XG4gIHRoaXMuX3BhY2tlci5vbihcImVuZFwiLCB0aGlzLmVtaXQuYmluZCh0aGlzLCBcImVuZFwiKSk7XG4gIHRoaXMuX3BhcnNlci5vbihcImNsb3NlXCIsIHRoaXMuX2hhbmRsZUNsb3NlLmJpbmQodGhpcykpO1xuICB0aGlzLl9wYWNrZXIub24oXCJlcnJvclwiLCB0aGlzLmVtaXQuYmluZCh0aGlzLCBcImVycm9yXCIpKTtcbn0pO1xudXRpbC5pbmhlcml0cyhQTkcsIFN0cmVhbSk7XG5cblBORy5zeW5jID0gUE5HU3luYztcblxuUE5HLnByb3RvdHlwZS5wYWNrID0gZnVuY3Rpb24gKCkge1xuICBpZiAoIXRoaXMuZGF0YSB8fCAhdGhpcy5kYXRhLmxlbmd0aCkge1xuICAgIHRoaXMuZW1pdChcImVycm9yXCIsIFwiTm8gZGF0YSBwcm92aWRlZFwiKTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuXG4gIHByb2Nlc3MubmV4dFRpY2soXG4gICAgZnVuY3Rpb24gKCkge1xuICAgICAgdGhpcy5fcGFja2VyLnBhY2sodGhpcy5kYXRhLCB0aGlzLndpZHRoLCB0aGlzLmhlaWdodCwgdGhpcy5nYW1tYSk7XG4gICAgfS5iaW5kKHRoaXMpXG4gICk7XG5cbiAgcmV0dXJuIHRoaXM7XG59O1xuXG5QTkcucHJvdG90eXBlLnBhcnNlID0gZnVuY3Rpb24gKGRhdGEsIGNhbGxiYWNrKSB7XG4gIGlmIChjYWxsYmFjaykge1xuICAgIGxldCBvblBhcnNlZCwgb25FcnJvcjtcblxuICAgIG9uUGFyc2VkID0gZnVuY3Rpb24gKHBhcnNlZERhdGEpIHtcbiAgICAgIHRoaXMucmVtb3ZlTGlzdGVuZXIoXCJlcnJvclwiLCBvbkVycm9yKTtcblxuICAgICAgdGhpcy5kYXRhID0gcGFyc2VkRGF0YTtcbiAgICAgIGNhbGxiYWNrKG51bGwsIHRoaXMpO1xuICAgIH0uYmluZCh0aGlzKTtcblxuICAgIG9uRXJyb3IgPSBmdW5jdGlvbiAoZXJyKSB7XG4gICAgICB0aGlzLnJlbW92ZUxpc3RlbmVyKFwicGFyc2VkXCIsIG9uUGFyc2VkKTtcblxuICAgICAgY2FsbGJhY2soZXJyLCBudWxsKTtcbiAgICB9LmJpbmQodGhpcyk7XG5cbiAgICB0aGlzLm9uY2UoXCJwYXJzZWRcIiwgb25QYXJzZWQpO1xuICAgIHRoaXMub25jZShcImVycm9yXCIsIG9uRXJyb3IpO1xuICB9XG5cbiAgdGhpcy5lbmQoZGF0YSk7XG4gIHJldHVybiB0aGlzO1xufTtcblxuUE5HLnByb3RvdHlwZS53cml0ZSA9IGZ1bmN0aW9uIChkYXRhKSB7XG4gIHRoaXMuX3BhcnNlci53cml0ZShkYXRhKTtcbiAgcmV0dXJuIHRydWU7XG59O1xuXG5QTkcucHJvdG90eXBlLmVuZCA9IGZ1bmN0aW9uIChkYXRhKSB7XG4gIHRoaXMuX3BhcnNlci5lbmQoZGF0YSk7XG59O1xuXG5QTkcucHJvdG90eXBlLl9tZXRhZGF0YSA9IGZ1bmN0aW9uIChtZXRhZGF0YSkge1xuICB0aGlzLndpZHRoID0gbWV0YWRhdGEud2lkdGg7XG4gIHRoaXMuaGVpZ2h0ID0gbWV0YWRhdGEuaGVpZ2h0O1xuXG4gIHRoaXMuZW1pdChcIm1ldGFkYXRhXCIsIG1ldGFkYXRhKTtcbn07XG5cblBORy5wcm90b3R5cGUuX2dhbW1hID0gZnVuY3Rpb24gKGdhbW1hKSB7XG4gIHRoaXMuZ2FtbWEgPSBnYW1tYTtcbn07XG5cblBORy5wcm90b3R5cGUuX2hhbmRsZUNsb3NlID0gZnVuY3Rpb24gKCkge1xuICBpZiAoIXRoaXMuX3BhcnNlci53cml0YWJsZSAmJiAhdGhpcy5fcGFja2VyLnJlYWRhYmxlKSB7XG4gICAgdGhpcy5lbWl0KFwiY2xvc2VcIik7XG4gIH1cbn07XG5cblBORy5iaXRibHQgPSBmdW5jdGlvbiAoc3JjLCBkc3QsIHNyY1gsIHNyY1ksIHdpZHRoLCBoZWlnaHQsIGRlbHRhWCwgZGVsdGFZKSB7XG4gIC8vIGVzbGludC1kaXNhYmxlLWxpbmUgbWF4LXBhcmFtc1xuICAvLyBjb2VyY2UgcGl4ZWwgZGltZW5zaW9ucyB0byBpbnRlZ2VycyAoYWxzbyBjb2VyY2VzIHVuZGVmaW5lZCAtPiAwKTpcbiAgLyogZXNsaW50LWRpc2FibGUgbm8tcGFyYW0tcmVhc3NpZ24gKi9cbiAgc3JjWCB8PSAwO1xuICBzcmNZIHw9IDA7XG4gIHdpZHRoIHw9IDA7XG4gIGhlaWdodCB8PSAwO1xuICBkZWx0YVggfD0gMDtcbiAgZGVsdGFZIHw9IDA7XG4gIC8qIGVzbGludC1lbmFibGUgbm8tcGFyYW0tcmVhc3NpZ24gKi9cblxuICBpZiAoXG4gICAgc3JjWCA+IHNyYy53aWR0aCB8fFxuICAgIHNyY1kgPiBzcmMuaGVpZ2h0IHx8XG4gICAgc3JjWCArIHdpZHRoID4gc3JjLndpZHRoIHx8XG4gICAgc3JjWSArIGhlaWdodCA+IHNyYy5oZWlnaHRcbiAgKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiYml0Ymx0IHJlYWRpbmcgb3V0c2lkZSBpbWFnZVwiKTtcbiAgfVxuXG4gIGlmIChcbiAgICBkZWx0YVggPiBkc3Qud2lkdGggfHxcbiAgICBkZWx0YVkgPiBkc3QuaGVpZ2h0IHx8XG4gICAgZGVsdGFYICsgd2lkdGggPiBkc3Qud2lkdGggfHxcbiAgICBkZWx0YVkgKyBoZWlnaHQgPiBkc3QuaGVpZ2h0XG4gICkge1xuICAgIHRocm93IG5ldyBFcnJvcihcImJpdGJsdCB3cml0aW5nIG91dHNpZGUgaW1hZ2VcIik7XG4gIH1cblxuICBmb3IgKGxldCB5ID0gMDsgeSA8IGhlaWdodDsgeSsrKSB7XG4gICAgc3JjLmRhdGEuY29weShcbiAgICAgIGRzdC5kYXRhLFxuICAgICAgKChkZWx0YVkgKyB5KSAqIGRzdC53aWR0aCArIGRlbHRhWCkgPDwgMixcbiAgICAgICgoc3JjWSArIHkpICogc3JjLndpZHRoICsgc3JjWCkgPDwgMixcbiAgICAgICgoc3JjWSArIHkpICogc3JjLndpZHRoICsgc3JjWCArIHdpZHRoKSA8PCAyXG4gICAgKTtcbiAgfVxufTtcblxuUE5HLnByb3RvdHlwZS5iaXRibHQgPSBmdW5jdGlvbiAoXG4gIGRzdCxcbiAgc3JjWCxcbiAgc3JjWSxcbiAgd2lkdGgsXG4gIGhlaWdodCxcbiAgZGVsdGFYLFxuICBkZWx0YVlcbikge1xuICAvLyBlc2xpbnQtZGlzYWJsZS1saW5lIG1heC1wYXJhbXNcblxuICBQTkcuYml0Ymx0KHRoaXMsIGRzdCwgc3JjWCwgc3JjWSwgd2lkdGgsIGhlaWdodCwgZGVsdGFYLCBkZWx0YVkpO1xuICByZXR1cm4gdGhpcztcbn07XG5cblBORy5hZGp1c3RHYW1tYSA9IGZ1bmN0aW9uIChzcmMpIHtcbiAgaWYgKHNyYy5nYW1tYSkge1xuICAgIGZvciAobGV0IHkgPSAwOyB5IDwgc3JjLmhlaWdodDsgeSsrKSB7XG4gICAgICBmb3IgKGxldCB4ID0gMDsgeCA8IHNyYy53aWR0aDsgeCsrKSB7XG4gICAgICAgIGxldCBpZHggPSAoc3JjLndpZHRoICogeSArIHgpIDw8IDI7XG5cbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCAzOyBpKyspIHtcbiAgICAgICAgICBsZXQgc2FtcGxlID0gc3JjLmRhdGFbaWR4ICsgaV0gLyAyNTU7XG4gICAgICAgICAgc2FtcGxlID0gTWF0aC5wb3coc2FtcGxlLCAxIC8gMi4yIC8gc3JjLmdhbW1hKTtcbiAgICAgICAgICBzcmMuZGF0YVtpZHggKyBpXSA9IE1hdGgucm91bmQoc2FtcGxlICogMjU1KTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgICBzcmMuZ2FtbWEgPSAwO1xuICB9XG59O1xuXG5QTkcucHJvdG90eXBlLmFkanVzdEdhbW1hID0gZnVuY3Rpb24gKCkge1xuICBQTkcuYWRqdXN0R2FtbWEodGhpcyk7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/png.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/sync-inflate.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/sync-inflate.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\n\nlet assert = (__webpack_require__(/*! assert */ \"assert\").ok);\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet util = __webpack_require__(/*! util */ \"util\");\n\nlet kMaxLength = (__webpack_require__(/*! buffer */ \"buffer\").kMaxLength);\n\nfunction Inflate(opts) {\n  if (!(this instanceof Inflate)) {\n    return new Inflate(opts);\n  }\n\n  if (opts && opts.chunkSize < zlib.Z_MIN_CHUNK) {\n    opts.chunkSize = zlib.Z_MIN_CHUNK;\n  }\n\n  zlib.Inflate.call(this, opts);\n\n  // Node 8 --> 9 compatibility check\n  this._offset = this._offset === undefined ? this._outOffset : this._offset;\n  this._buffer = this._buffer || this._outBuffer;\n\n  if (opts && opts.maxLength != null) {\n    this._maxLength = opts.maxLength;\n  }\n}\n\nfunction createInflate(opts) {\n  return new Inflate(opts);\n}\n\nfunction _close(engine, callback) {\n  if (callback) {\n    process.nextTick(callback);\n  }\n\n  // Caller may invoke .close after a zlib error (which will null _handle).\n  if (!engine._handle) {\n    return;\n  }\n\n  engine._handle.close();\n  engine._handle = null;\n}\n\nInflate.prototype._processChunk = function (chunk, flushFlag, asyncCb) {\n  if (typeof asyncCb === \"function\") {\n    return zlib.Inflate._processChunk.call(this, chunk, flushFlag, asyncCb);\n  }\n\n  let self = this;\n\n  let availInBefore = chunk && chunk.length;\n  let availOutBefore = this._chunkSize - this._offset;\n  let leftToInflate = this._maxLength;\n  let inOff = 0;\n\n  let buffers = [];\n  let nread = 0;\n\n  let error;\n  this.on(\"error\", function (err) {\n    error = err;\n  });\n\n  function handleChunk(availInAfter, availOutAfter) {\n    if (self._hadError) {\n      return;\n    }\n\n    let have = availOutBefore - availOutAfter;\n    assert(have >= 0, \"have should not go down\");\n\n    if (have > 0) {\n      let out = self._buffer.slice(self._offset, self._offset + have);\n      self._offset += have;\n\n      if (out.length > leftToInflate) {\n        out = out.slice(0, leftToInflate);\n      }\n\n      buffers.push(out);\n      nread += out.length;\n      leftToInflate -= out.length;\n\n      if (leftToInflate === 0) {\n        return false;\n      }\n    }\n\n    if (availOutAfter === 0 || self._offset >= self._chunkSize) {\n      availOutBefore = self._chunkSize;\n      self._offset = 0;\n      self._buffer = Buffer.allocUnsafe(self._chunkSize);\n    }\n\n    if (availOutAfter === 0) {\n      inOff += availInBefore - availInAfter;\n      availInBefore = availInAfter;\n\n      return true;\n    }\n\n    return false;\n  }\n\n  assert(this._handle, \"zlib binding closed\");\n  let res;\n  do {\n    res = this._handle.writeSync(\n      flushFlag,\n      chunk, // in\n      inOff, // in_off\n      availInBefore, // in_len\n      this._buffer, // out\n      this._offset, //out_off\n      availOutBefore\n    ); // out_len\n    // Node 8 --> 9 compatibility check\n    res = res || this._writeState;\n  } while (!this._hadError && handleChunk(res[0], res[1]));\n\n  if (this._hadError) {\n    throw error;\n  }\n\n  if (nread >= kMaxLength) {\n    _close(this);\n    throw new RangeError(\n      \"Cannot create final Buffer. It would be larger than 0x\" +\n        kMaxLength.toString(16) +\n        \" bytes\"\n    );\n  }\n\n  let buf = Buffer.concat(buffers, nread);\n  _close(this);\n\n  return buf;\n};\n\nutil.inherits(Inflate, zlib.Inflate);\n\nfunction zlibBufferSync(engine, buffer) {\n  if (typeof buffer === \"string\") {\n    buffer = Buffer.from(buffer);\n  }\n  if (!(buffer instanceof Buffer)) {\n    throw new TypeError(\"Not a string or buffer\");\n  }\n\n  let flushFlag = engine._finishFlushFlag;\n  if (flushFlag == null) {\n    flushFlag = zlib.Z_FINISH;\n  }\n\n  return engine._processChunk(buffer, flushFlag);\n}\n\nfunction inflateSync(buffer, opts) {\n  return zlibBufferSync(new Inflate(opts), buffer);\n}\n\nmodule.exports = exports = inflateSync;\nexports.Inflate = Inflate;\nexports.createInflate = createInflate;\nexports.inflateSync = inflateSync;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/sync-inflate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/sync-reader.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/sync-reader.js ***!
  \***********************************************/
/***/ ((module) => {

eval("\n\nlet SyncReader = (module.exports = function (buffer) {\n  this._buffer = buffer;\n  this._reads = [];\n});\n\nSyncReader.prototype.read = function (length, callback) {\n  this._reads.push({\n    length: Math.abs(length), // if length < 0 then at most this length\n    allowLess: length < 0,\n    func: callback,\n  });\n};\n\nSyncReader.prototype.process = function () {\n  // as long as there is any data and read requests\n  while (this._reads.length > 0 && this._buffer.length) {\n    let read = this._reads[0];\n\n    if (\n      this._buffer.length &&\n      (this._buffer.length >= read.length || read.allowLess)\n    ) {\n      // ok there is any data so that we can satisfy this request\n      this._reads.shift(); // == read\n\n      let buf = this._buffer;\n\n      this._buffer = buf.slice(read.length);\n\n      read.func.call(this, buf.slice(0, read.length));\n    } else {\n      break;\n    }\n  }\n\n  if (this._reads.length > 0) {\n    return new Error(\"There are some read requests waitng on finished stream\");\n  }\n\n  if (this._buffer.length > 0) {\n    return new Error(\"unrecognised content at end of stream\");\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/sync-reader.js\n");

/***/ })

};
;