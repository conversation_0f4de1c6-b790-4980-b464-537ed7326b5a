"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/process-warning";
exports.ids = ["vendor-chunks/process-warning"];
exports.modules = {

/***/ "(ssr)/./node_modules/process-warning/index.js":
/*!***********************************************!*\
  !*** ./node_modules/process-warning/index.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { format } = __webpack_require__(/*! util */ \"util\")\n\nfunction build () {\n  const codes = {}\n  const emitted = new Map()\n\n  function create (name, code, message) {\n    if (!name) throw new Error('Warning name must not be empty')\n    if (!code) throw new Error('Warning code must not be empty')\n    if (!message) throw new Error('Warning message must not be empty')\n\n    code = code.toUpperCase()\n\n    if (codes[code] !== undefined) {\n      throw new Error(`The code '${code}' already exist`)\n    }\n\n    function buildWarnOpts (a, b, c) {\n      // more performant than spread (...) operator\n      let formatted\n      if (a && b && c) {\n        formatted = format(message, a, b, c)\n      } else if (a && b) {\n        formatted = format(message, a, b)\n      } else if (a) {\n        formatted = format(message, a)\n      } else {\n        formatted = message\n      }\n\n      return {\n        code,\n        name,\n        message: formatted\n      }\n    }\n\n    emitted.set(code, false)\n    codes[code] = buildWarnOpts\n\n    return codes[code]\n  }\n\n  function emit (code, a, b, c) {\n    if (codes[code] === undefined) throw new Error(`The code '${code}' does not exist`)\n    if (emitted.get(code) === true) return\n    emitted.set(code, true)\n\n    const warning = codes[code](a, b, c)\n    process.emitWarning(warning.message, warning.name, warning.code)\n  }\n\n  return {\n    create,\n    emit,\n    emitted\n  }\n}\n\nmodule.exports = build\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/process-warning/index.js\n");

/***/ })

};
;