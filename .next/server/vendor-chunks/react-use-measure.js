"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-use-measure";
exports.ids = ["vendor-chunks/react-use-measure"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-use-measure/dist/index.js":
/*!******************************************************!*\
  !*** ./node_modules/react-use-measure/dist/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ j)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nfunction g(n,t){let o;return(...i)=>{window.clearTimeout(o),o=window.setTimeout(()=>n(...i),t)}}function j({debounce:n,scroll:t,polyfill:o,offsetSize:i}={debounce:0,scroll:!1,offsetSize:!1}){const a=o||(typeof window==\"undefined\"?class{}:window.ResizeObserver);if(!a)throw new Error(\"This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills\");const[c,h]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({left:0,top:0,width:0,height:0,bottom:0,right:0,x:0,y:0}),e=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({element:null,scrollContainers:null,resizeObserver:null,lastBounds:c,orientationHandler:null}),d=n?typeof n==\"number\"?n:n.scroll:null,f=n?typeof n==\"number\"?n:n.resize:null,w=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(!1);(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>(w.current=!0,()=>void(w.current=!1)));const[z,m,s]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{const r=()=>{if(!e.current.element)return;const{left:y,top:C,width:H,height:O,bottom:S,right:x,x:B,y:R}=e.current.element.getBoundingClientRect(),l={left:y,top:C,width:H,height:O,bottom:S,right:x,x:B,y:R};e.current.element instanceof HTMLElement&&i&&(l.height=e.current.element.offsetHeight,l.width=e.current.element.offsetWidth),Object.freeze(l),w.current&&!D(e.current.lastBounds,l)&&h(e.current.lastBounds=l)};return[r,f?g(r,f):r,d?g(r,d):r]},[h,i,d,f]);function v(){e.current.scrollContainers&&(e.current.scrollContainers.forEach(r=>r.removeEventListener(\"scroll\",s,!0)),e.current.scrollContainers=null),e.current.resizeObserver&&(e.current.resizeObserver.disconnect(),e.current.resizeObserver=null),e.current.orientationHandler&&(\"orientation\"in screen&&\"removeEventListener\"in screen.orientation?screen.orientation.removeEventListener(\"change\",e.current.orientationHandler):\"onorientationchange\"in window&&window.removeEventListener(\"orientationchange\",e.current.orientationHandler))}function b(){e.current.element&&(e.current.resizeObserver=new a(s),e.current.resizeObserver.observe(e.current.element),t&&e.current.scrollContainers&&e.current.scrollContainers.forEach(r=>r.addEventListener(\"scroll\",s,{capture:!0,passive:!0})),e.current.orientationHandler=()=>{s()},\"orientation\"in screen&&\"addEventListener\"in screen.orientation?screen.orientation.addEventListener(\"change\",e.current.orientationHandler):\"onorientationchange\"in window&&window.addEventListener(\"orientationchange\",e.current.orientationHandler))}const L=r=>{!r||r===e.current.element||(v(),e.current.element=r,e.current.scrollContainers=E(r),b())};return X(s,!!t),W(m),(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{v(),b()},[t,s,m]),(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>v,[]),[L,c,z]}function W(n){(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{const t=n;return window.addEventListener(\"resize\",t),()=>void window.removeEventListener(\"resize\",t)},[n])}function X(n,t){(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{if(t){const o=n;return window.addEventListener(\"scroll\",o,{capture:!0,passive:!0}),()=>void window.removeEventListener(\"scroll\",o,!0)}},[n,t])}function E(n){const t=[];if(!n||n===document.body)return t;const{overflow:o,overflowX:i,overflowY:a}=window.getComputedStyle(n);return[o,i,a].some(c=>c===\"auto\"||c===\"scroll\")&&t.push(n),[...t,...E(n.parentElement)]}const k=[\"x\",\"y\",\"top\",\"bottom\",\"left\",\"right\",\"width\",\"height\"],D=(n,t)=>k.every(o=>n[o]===t[o]);\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-use-measure/dist/index.js\n");

/***/ })

};
;