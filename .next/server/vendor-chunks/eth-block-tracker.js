"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eth-block-tracker";
exports.ids = ["vendor-chunks/eth-block-tracker"];
exports.modules = {

/***/ "(ssr)/./node_modules/eth-block-tracker/dist/BaseBlockTracker.js":
/*!*****************************************************************!*\
  !*** ./node_modules/eth-block-tracker/dist/BaseBlockTracker.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BaseBlockTracker = void 0;\nconst safe_event_emitter_1 = __importDefault(__webpack_require__(/*! @metamask/safe-event-emitter */ \"(ssr)/./node_modules/@metamask/safe-event-emitter/dist/cjs/index.js\"));\nconst sec = 1000;\nconst calculateSum = (accumulator, currentValue) => accumulator + currentValue;\nconst blockTrackerEvents = ['sync', 'latest'];\nclass BaseBlockTracker extends safe_event_emitter_1.default {\n    constructor(opts) {\n        super();\n        // config\n        this._blockResetDuration = opts.blockResetDuration || 20 * sec;\n        this._usePastBlocks = opts.usePastBlocks || false;\n        // state\n        this._currentBlock = null;\n        this._isRunning = false;\n        // bind functions for internal use\n        this._onNewListener = this._onNewListener.bind(this);\n        this._onRemoveListener = this._onRemoveListener.bind(this);\n        this._resetCurrentBlock = this._resetCurrentBlock.bind(this);\n        // listen for handler changes\n        this._setupInternalEvents();\n    }\n    async destroy() {\n        this._cancelBlockResetTimeout();\n        await this._maybeEnd();\n        super.removeAllListeners();\n    }\n    isRunning() {\n        return this._isRunning;\n    }\n    getCurrentBlock() {\n        return this._currentBlock;\n    }\n    async getLatestBlock() {\n        // return if available\n        if (this._currentBlock) {\n            return this._currentBlock;\n        }\n        // wait for a new latest block\n        const latestBlock = await new Promise((resolve) => this.once('latest', resolve));\n        // return newly set current block\n        return latestBlock;\n    }\n    // dont allow module consumer to remove our internal event listeners\n    removeAllListeners(eventName) {\n        // perform default behavior, preserve fn arity\n        if (eventName) {\n            super.removeAllListeners(eventName);\n        }\n        else {\n            super.removeAllListeners();\n        }\n        // re-add internal events\n        this._setupInternalEvents();\n        // trigger stop check just in case\n        this._onRemoveListener();\n        return this;\n    }\n    _setupInternalEvents() {\n        // first remove listeners for idempotence\n        this.removeListener('newListener', this._onNewListener);\n        this.removeListener('removeListener', this._onRemoveListener);\n        // then add them\n        this.on('newListener', this._onNewListener);\n        this.on('removeListener', this._onRemoveListener);\n    }\n    _onNewListener(eventName) {\n        // `newListener` is called *before* the listener is added\n        if (blockTrackerEvents.includes(eventName)) {\n            this._maybeStart();\n        }\n    }\n    _onRemoveListener() {\n        // `removeListener` is called *after* the listener is removed\n        if (this._getBlockTrackerEventCount() > 0) {\n            return;\n        }\n        this._maybeEnd();\n    }\n    async _maybeStart() {\n        if (this._isRunning) {\n            return;\n        }\n        this._isRunning = true;\n        // cancel setting latest block to stale\n        this._cancelBlockResetTimeout();\n        await this._start();\n        this.emit('_started');\n    }\n    async _maybeEnd() {\n        if (!this._isRunning) {\n            return;\n        }\n        this._isRunning = false;\n        this._setupBlockResetTimeout();\n        await this._end();\n        this.emit('_ended');\n    }\n    _getBlockTrackerEventCount() {\n        return blockTrackerEvents\n            .map((eventName) => this.listenerCount(eventName))\n            .reduce(calculateSum);\n    }\n    _shouldUseNewBlock(newBlock) {\n        const currentBlock = this._currentBlock;\n        if (!currentBlock) {\n            return true;\n        }\n        const newBlockInt = hexToInt(newBlock);\n        const currentBlockInt = hexToInt(currentBlock);\n        return ((this._usePastBlocks && newBlockInt < currentBlockInt) ||\n            newBlockInt > currentBlockInt);\n    }\n    _newPotentialLatest(newBlock) {\n        if (!this._shouldUseNewBlock(newBlock)) {\n            return;\n        }\n        this._setCurrentBlock(newBlock);\n    }\n    _setCurrentBlock(newBlock) {\n        const oldBlock = this._currentBlock;\n        this._currentBlock = newBlock;\n        this.emit('latest', newBlock);\n        this.emit('sync', { oldBlock, newBlock });\n    }\n    _setupBlockResetTimeout() {\n        // clear any existing timeout\n        this._cancelBlockResetTimeout();\n        // clear latest block when stale\n        this._blockResetTimeout = setTimeout(this._resetCurrentBlock, this._blockResetDuration);\n        // nodejs - dont hold process open\n        if (this._blockResetTimeout.unref) {\n            this._blockResetTimeout.unref();\n        }\n    }\n    _cancelBlockResetTimeout() {\n        if (this._blockResetTimeout) {\n            clearTimeout(this._blockResetTimeout);\n        }\n    }\n    _resetCurrentBlock() {\n        this._currentBlock = null;\n    }\n}\nexports.BaseBlockTracker = BaseBlockTracker;\n/**\n * Converts a number represented as a string in hexadecimal format into a native\n * number.\n *\n * @param hexInt - The hex string.\n * @returns The number.\n */\nfunction hexToInt(hexInt) {\n    return Number.parseInt(hexInt, 16);\n}\n//# sourceMappingURL=BaseBlockTracker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvZGlzdC9CYXNlQmxvY2tUcmFja2VyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSw2Q0FBNkM7QUFDN0M7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsd0JBQXdCO0FBQ3hCLDZDQUE2QyxtQkFBTyxDQUFDLHlHQUE4QjtBQUNuRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsb0JBQW9CO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0I7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9ldGgtYmxvY2stdHJhY2tlci9kaXN0L0Jhc2VCbG9ja1RyYWNrZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgX19pbXBvcnREZWZhdWx0ID0gKHRoaXMgJiYgdGhpcy5fX2ltcG9ydERlZmF1bHQpIHx8IGZ1bmN0aW9uIChtb2QpIHtcbiAgICByZXR1cm4gKG1vZCAmJiBtb2QuX19lc01vZHVsZSkgPyBtb2QgOiB7IFwiZGVmYXVsdFwiOiBtb2QgfTtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkJhc2VCbG9ja1RyYWNrZXIgPSB2b2lkIDA7XG5jb25zdCBzYWZlX2V2ZW50X2VtaXR0ZXJfMSA9IF9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwiQG1ldGFtYXNrL3NhZmUtZXZlbnQtZW1pdHRlclwiKSk7XG5jb25zdCBzZWMgPSAxMDAwO1xuY29uc3QgY2FsY3VsYXRlU3VtID0gKGFjY3VtdWxhdG9yLCBjdXJyZW50VmFsdWUpID0+IGFjY3VtdWxhdG9yICsgY3VycmVudFZhbHVlO1xuY29uc3QgYmxvY2tUcmFja2VyRXZlbnRzID0gWydzeW5jJywgJ2xhdGVzdCddO1xuY2xhc3MgQmFzZUJsb2NrVHJhY2tlciBleHRlbmRzIHNhZmVfZXZlbnRfZW1pdHRlcl8xLmRlZmF1bHQge1xuICAgIGNvbnN0cnVjdG9yKG9wdHMpIHtcbiAgICAgICAgc3VwZXIoKTtcbiAgICAgICAgLy8gY29uZmlnXG4gICAgICAgIHRoaXMuX2Jsb2NrUmVzZXREdXJhdGlvbiA9IG9wdHMuYmxvY2tSZXNldER1cmF0aW9uIHx8IDIwICogc2VjO1xuICAgICAgICB0aGlzLl91c2VQYXN0QmxvY2tzID0gb3B0cy51c2VQYXN0QmxvY2tzIHx8IGZhbHNlO1xuICAgICAgICAvLyBzdGF0ZVxuICAgICAgICB0aGlzLl9jdXJyZW50QmxvY2sgPSBudWxsO1xuICAgICAgICB0aGlzLl9pc1J1bm5pbmcgPSBmYWxzZTtcbiAgICAgICAgLy8gYmluZCBmdW5jdGlvbnMgZm9yIGludGVybmFsIHVzZVxuICAgICAgICB0aGlzLl9vbk5ld0xpc3RlbmVyID0gdGhpcy5fb25OZXdMaXN0ZW5lci5iaW5kKHRoaXMpO1xuICAgICAgICB0aGlzLl9vblJlbW92ZUxpc3RlbmVyID0gdGhpcy5fb25SZW1vdmVMaXN0ZW5lci5iaW5kKHRoaXMpO1xuICAgICAgICB0aGlzLl9yZXNldEN1cnJlbnRCbG9jayA9IHRoaXMuX3Jlc2V0Q3VycmVudEJsb2NrLmJpbmQodGhpcyk7XG4gICAgICAgIC8vIGxpc3RlbiBmb3IgaGFuZGxlciBjaGFuZ2VzXG4gICAgICAgIHRoaXMuX3NldHVwSW50ZXJuYWxFdmVudHMoKTtcbiAgICB9XG4gICAgYXN5bmMgZGVzdHJveSgpIHtcbiAgICAgICAgdGhpcy5fY2FuY2VsQmxvY2tSZXNldFRpbWVvdXQoKTtcbiAgICAgICAgYXdhaXQgdGhpcy5fbWF5YmVFbmQoKTtcbiAgICAgICAgc3VwZXIucmVtb3ZlQWxsTGlzdGVuZXJzKCk7XG4gICAgfVxuICAgIGlzUnVubmluZygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2lzUnVubmluZztcbiAgICB9XG4gICAgZ2V0Q3VycmVudEJsb2NrKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5fY3VycmVudEJsb2NrO1xuICAgIH1cbiAgICBhc3luYyBnZXRMYXRlc3RCbG9jaygpIHtcbiAgICAgICAgLy8gcmV0dXJuIGlmIGF2YWlsYWJsZVxuICAgICAgICBpZiAodGhpcy5fY3VycmVudEJsb2NrKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5fY3VycmVudEJsb2NrO1xuICAgICAgICB9XG4gICAgICAgIC8vIHdhaXQgZm9yIGEgbmV3IGxhdGVzdCBibG9ja1xuICAgICAgICBjb25zdCBsYXRlc3RCbG9jayA9IGF3YWl0IG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiB0aGlzLm9uY2UoJ2xhdGVzdCcsIHJlc29sdmUpKTtcbiAgICAgICAgLy8gcmV0dXJuIG5ld2x5IHNldCBjdXJyZW50IGJsb2NrXG4gICAgICAgIHJldHVybiBsYXRlc3RCbG9jaztcbiAgICB9XG4gICAgLy8gZG9udCBhbGxvdyBtb2R1bGUgY29uc3VtZXIgdG8gcmVtb3ZlIG91ciBpbnRlcm5hbCBldmVudCBsaXN0ZW5lcnNcbiAgICByZW1vdmVBbGxMaXN0ZW5lcnMoZXZlbnROYW1lKSB7XG4gICAgICAgIC8vIHBlcmZvcm0gZGVmYXVsdCBiZWhhdmlvciwgcHJlc2VydmUgZm4gYXJpdHlcbiAgICAgICAgaWYgKGV2ZW50TmFtZSkge1xuICAgICAgICAgICAgc3VwZXIucmVtb3ZlQWxsTGlzdGVuZXJzKGV2ZW50TmFtZSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBzdXBlci5yZW1vdmVBbGxMaXN0ZW5lcnMoKTtcbiAgICAgICAgfVxuICAgICAgICAvLyByZS1hZGQgaW50ZXJuYWwgZXZlbnRzXG4gICAgICAgIHRoaXMuX3NldHVwSW50ZXJuYWxFdmVudHMoKTtcbiAgICAgICAgLy8gdHJpZ2dlciBzdG9wIGNoZWNrIGp1c3QgaW4gY2FzZVxuICAgICAgICB0aGlzLl9vblJlbW92ZUxpc3RlbmVyKCk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBfc2V0dXBJbnRlcm5hbEV2ZW50cygpIHtcbiAgICAgICAgLy8gZmlyc3QgcmVtb3ZlIGxpc3RlbmVycyBmb3IgaWRlbXBvdGVuY2VcbiAgICAgICAgdGhpcy5yZW1vdmVMaXN0ZW5lcignbmV3TGlzdGVuZXInLCB0aGlzLl9vbk5ld0xpc3RlbmVyKTtcbiAgICAgICAgdGhpcy5yZW1vdmVMaXN0ZW5lcigncmVtb3ZlTGlzdGVuZXInLCB0aGlzLl9vblJlbW92ZUxpc3RlbmVyKTtcbiAgICAgICAgLy8gdGhlbiBhZGQgdGhlbVxuICAgICAgICB0aGlzLm9uKCduZXdMaXN0ZW5lcicsIHRoaXMuX29uTmV3TGlzdGVuZXIpO1xuICAgICAgICB0aGlzLm9uKCdyZW1vdmVMaXN0ZW5lcicsIHRoaXMuX29uUmVtb3ZlTGlzdGVuZXIpO1xuICAgIH1cbiAgICBfb25OZXdMaXN0ZW5lcihldmVudE5hbWUpIHtcbiAgICAgICAgLy8gYG5ld0xpc3RlbmVyYCBpcyBjYWxsZWQgKmJlZm9yZSogdGhlIGxpc3RlbmVyIGlzIGFkZGVkXG4gICAgICAgIGlmIChibG9ja1RyYWNrZXJFdmVudHMuaW5jbHVkZXMoZXZlbnROYW1lKSkge1xuICAgICAgICAgICAgdGhpcy5fbWF5YmVTdGFydCgpO1xuICAgICAgICB9XG4gICAgfVxuICAgIF9vblJlbW92ZUxpc3RlbmVyKCkge1xuICAgICAgICAvLyBgcmVtb3ZlTGlzdGVuZXJgIGlzIGNhbGxlZCAqYWZ0ZXIqIHRoZSBsaXN0ZW5lciBpcyByZW1vdmVkXG4gICAgICAgIGlmICh0aGlzLl9nZXRCbG9ja1RyYWNrZXJFdmVudENvdW50KCkgPiAwKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5fbWF5YmVFbmQoKTtcbiAgICB9XG4gICAgYXN5bmMgX21heWJlU3RhcnQoKSB7XG4gICAgICAgIGlmICh0aGlzLl9pc1J1bm5pbmcpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9pc1J1bm5pbmcgPSB0cnVlO1xuICAgICAgICAvLyBjYW5jZWwgc2V0dGluZyBsYXRlc3QgYmxvY2sgdG8gc3RhbGVcbiAgICAgICAgdGhpcy5fY2FuY2VsQmxvY2tSZXNldFRpbWVvdXQoKTtcbiAgICAgICAgYXdhaXQgdGhpcy5fc3RhcnQoKTtcbiAgICAgICAgdGhpcy5lbWl0KCdfc3RhcnRlZCcpO1xuICAgIH1cbiAgICBhc3luYyBfbWF5YmVFbmQoKSB7XG4gICAgICAgIGlmICghdGhpcy5faXNSdW5uaW5nKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5faXNSdW5uaW5nID0gZmFsc2U7XG4gICAgICAgIHRoaXMuX3NldHVwQmxvY2tSZXNldFRpbWVvdXQoKTtcbiAgICAgICAgYXdhaXQgdGhpcy5fZW5kKCk7XG4gICAgICAgIHRoaXMuZW1pdCgnX2VuZGVkJyk7XG4gICAgfVxuICAgIF9nZXRCbG9ja1RyYWNrZXJFdmVudENvdW50KCkge1xuICAgICAgICByZXR1cm4gYmxvY2tUcmFja2VyRXZlbnRzXG4gICAgICAgICAgICAubWFwKChldmVudE5hbWUpID0+IHRoaXMubGlzdGVuZXJDb3VudChldmVudE5hbWUpKVxuICAgICAgICAgICAgLnJlZHVjZShjYWxjdWxhdGVTdW0pO1xuICAgIH1cbiAgICBfc2hvdWxkVXNlTmV3QmxvY2sobmV3QmxvY2spIHtcbiAgICAgICAgY29uc3QgY3VycmVudEJsb2NrID0gdGhpcy5fY3VycmVudEJsb2NrO1xuICAgICAgICBpZiAoIWN1cnJlbnRCbG9jaykge1xuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgbmV3QmxvY2tJbnQgPSBoZXhUb0ludChuZXdCbG9jayk7XG4gICAgICAgIGNvbnN0IGN1cnJlbnRCbG9ja0ludCA9IGhleFRvSW50KGN1cnJlbnRCbG9jayk7XG4gICAgICAgIHJldHVybiAoKHRoaXMuX3VzZVBhc3RCbG9ja3MgJiYgbmV3QmxvY2tJbnQgPCBjdXJyZW50QmxvY2tJbnQpIHx8XG4gICAgICAgICAgICBuZXdCbG9ja0ludCA+IGN1cnJlbnRCbG9ja0ludCk7XG4gICAgfVxuICAgIF9uZXdQb3RlbnRpYWxMYXRlc3QobmV3QmxvY2spIHtcbiAgICAgICAgaWYgKCF0aGlzLl9zaG91bGRVc2VOZXdCbG9jayhuZXdCbG9jaykpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9zZXRDdXJyZW50QmxvY2sobmV3QmxvY2spO1xuICAgIH1cbiAgICBfc2V0Q3VycmVudEJsb2NrKG5ld0Jsb2NrKSB7XG4gICAgICAgIGNvbnN0IG9sZEJsb2NrID0gdGhpcy5fY3VycmVudEJsb2NrO1xuICAgICAgICB0aGlzLl9jdXJyZW50QmxvY2sgPSBuZXdCbG9jaztcbiAgICAgICAgdGhpcy5lbWl0KCdsYXRlc3QnLCBuZXdCbG9jayk7XG4gICAgICAgIHRoaXMuZW1pdCgnc3luYycsIHsgb2xkQmxvY2ssIG5ld0Jsb2NrIH0pO1xuICAgIH1cbiAgICBfc2V0dXBCbG9ja1Jlc2V0VGltZW91dCgpIHtcbiAgICAgICAgLy8gY2xlYXIgYW55IGV4aXN0aW5nIHRpbWVvdXRcbiAgICAgICAgdGhpcy5fY2FuY2VsQmxvY2tSZXNldFRpbWVvdXQoKTtcbiAgICAgICAgLy8gY2xlYXIgbGF0ZXN0IGJsb2NrIHdoZW4gc3RhbGVcbiAgICAgICAgdGhpcy5fYmxvY2tSZXNldFRpbWVvdXQgPSBzZXRUaW1lb3V0KHRoaXMuX3Jlc2V0Q3VycmVudEJsb2NrLCB0aGlzLl9ibG9ja1Jlc2V0RHVyYXRpb24pO1xuICAgICAgICAvLyBub2RlanMgLSBkb250IGhvbGQgcHJvY2VzcyBvcGVuXG4gICAgICAgIGlmICh0aGlzLl9ibG9ja1Jlc2V0VGltZW91dC51bnJlZikge1xuICAgICAgICAgICAgdGhpcy5fYmxvY2tSZXNldFRpbWVvdXQudW5yZWYoKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBfY2FuY2VsQmxvY2tSZXNldFRpbWVvdXQoKSB7XG4gICAgICAgIGlmICh0aGlzLl9ibG9ja1Jlc2V0VGltZW91dCkge1xuICAgICAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMuX2Jsb2NrUmVzZXRUaW1lb3V0KTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBfcmVzZXRDdXJyZW50QmxvY2soKSB7XG4gICAgICAgIHRoaXMuX2N1cnJlbnRCbG9jayA9IG51bGw7XG4gICAgfVxufVxuZXhwb3J0cy5CYXNlQmxvY2tUcmFja2VyID0gQmFzZUJsb2NrVHJhY2tlcjtcbi8qKlxuICogQ29udmVydHMgYSBudW1iZXIgcmVwcmVzZW50ZWQgYXMgYSBzdHJpbmcgaW4gaGV4YWRlY2ltYWwgZm9ybWF0IGludG8gYSBuYXRpdmVcbiAqIG51bWJlci5cbiAqXG4gKiBAcGFyYW0gaGV4SW50IC0gVGhlIGhleCBzdHJpbmcuXG4gKiBAcmV0dXJucyBUaGUgbnVtYmVyLlxuICovXG5mdW5jdGlvbiBoZXhUb0ludChoZXhJbnQpIHtcbiAgICByZXR1cm4gTnVtYmVyLnBhcnNlSW50KGhleEludCwgMTYpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9QmFzZUJsb2NrVHJhY2tlci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/dist/BaseBlockTracker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/dist/PollingBlockTracker.js":
/*!********************************************************************!*\
  !*** ./node_modules/eth-block-tracker/dist/PollingBlockTracker.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PollingBlockTracker = void 0;\nconst json_rpc_random_id_1 = __importDefault(__webpack_require__(/*! json-rpc-random-id */ \"(ssr)/./node_modules/json-rpc-random-id/index.js\"));\nconst pify_1 = __importDefault(__webpack_require__(/*! pify */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/pify/index.js\"));\nconst BaseBlockTracker_1 = __webpack_require__(/*! ./BaseBlockTracker */ \"(ssr)/./node_modules/eth-block-tracker/dist/BaseBlockTracker.js\");\nconst logging_utils_1 = __webpack_require__(/*! ./logging-utils */ \"(ssr)/./node_modules/eth-block-tracker/dist/logging-utils.js\");\nconst log = (0, logging_utils_1.createModuleLogger)(logging_utils_1.projectLogger, 'polling-block-tracker');\nconst createRandomId = (0, json_rpc_random_id_1.default)();\nconst sec = 1000;\nclass PollingBlockTracker extends BaseBlockTracker_1.BaseBlockTracker {\n    constructor(opts = {}) {\n        var _a;\n        // parse + validate args\n        if (!opts.provider) {\n            throw new Error('PollingBlockTracker - no provider specified.');\n        }\n        super(Object.assign(Object.assign({}, opts), { blockResetDuration: (_a = opts.blockResetDuration) !== null && _a !== void 0 ? _a : opts.pollingInterval }));\n        // config\n        this._provider = opts.provider;\n        this._pollingInterval = opts.pollingInterval || 20 * sec;\n        this._retryTimeout = opts.retryTimeout || this._pollingInterval / 10;\n        this._keepEventLoopActive =\n            opts.keepEventLoopActive === undefined ? true : opts.keepEventLoopActive;\n        this._setSkipCacheFlag = opts.setSkipCacheFlag || false;\n    }\n    // trigger block polling\n    async checkForLatestBlock() {\n        await this._updateLatestBlock();\n        return await this.getLatestBlock();\n    }\n    async _start() {\n        this._synchronize();\n    }\n    async _end() {\n        // No-op\n    }\n    async _synchronize() {\n        var _a;\n        while (this._isRunning) {\n            try {\n                await this._updateLatestBlock();\n                const promise = timeout(this._pollingInterval, !this._keepEventLoopActive);\n                this.emit('_waitingForNextIteration');\n                await promise;\n            }\n            catch (err) {\n                const newErr = new Error(`PollingBlockTracker - encountered an error while attempting to update latest block:\\n${(_a = err.stack) !== null && _a !== void 0 ? _a : err}`);\n                try {\n                    this.emit('error', newErr);\n                }\n                catch (emitErr) {\n                    console.error(newErr);\n                }\n                const promise = timeout(this._retryTimeout, !this._keepEventLoopActive);\n                this.emit('_waitingForNextIteration');\n                await promise;\n            }\n        }\n    }\n    async _updateLatestBlock() {\n        // fetch + set latest block\n        const latestBlock = await this._fetchLatestBlock();\n        this._newPotentialLatest(latestBlock);\n    }\n    async _fetchLatestBlock() {\n        const req = {\n            jsonrpc: '2.0',\n            id: createRandomId(),\n            method: 'eth_blockNumber',\n            params: [],\n        };\n        if (this._setSkipCacheFlag) {\n            req.skipCache = true;\n        }\n        log('Making request', req);\n        const res = await (0, pify_1.default)((cb) => this._provider.sendAsync(req, cb))();\n        log('Got response', res);\n        if (res.error) {\n            throw new Error(`PollingBlockTracker - encountered error fetching block:\\n${res.error.message}`);\n        }\n        return res.result;\n    }\n}\nexports.PollingBlockTracker = PollingBlockTracker;\n/**\n * Waits for the specified amount of time.\n *\n * @param duration - The amount of time in milliseconds.\n * @param unref - Assuming this function is run in a Node context, governs\n * whether Node should wait before the `setTimeout` has completed before ending\n * the process (true for no, false for yes). Defaults to false.\n * @returns A promise that can be used to wait.\n */\nfunction timeout(duration, unref) {\n    return new Promise((resolve) => {\n        const timeoutRef = setTimeout(resolve, duration);\n        // don't keep process open\n        if (timeoutRef.unref && unref) {\n            timeoutRef.unref();\n        }\n    });\n}\n//# sourceMappingURL=PollingBlockTracker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/dist/PollingBlockTracker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/dist/SubscribeBlockTracker.js":
/*!**********************************************************************!*\
  !*** ./node_modules/eth-block-tracker/dist/SubscribeBlockTracker.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SubscribeBlockTracker = void 0;\nconst json_rpc_random_id_1 = __importDefault(__webpack_require__(/*! json-rpc-random-id */ \"(ssr)/./node_modules/json-rpc-random-id/index.js\"));\nconst BaseBlockTracker_1 = __webpack_require__(/*! ./BaseBlockTracker */ \"(ssr)/./node_modules/eth-block-tracker/dist/BaseBlockTracker.js\");\nconst createRandomId = (0, json_rpc_random_id_1.default)();\nclass SubscribeBlockTracker extends BaseBlockTracker_1.BaseBlockTracker {\n    constructor(opts = {}) {\n        // parse + validate args\n        if (!opts.provider) {\n            throw new Error('SubscribeBlockTracker - no provider specified.');\n        }\n        // BaseBlockTracker constructor\n        super(opts);\n        // config\n        this._provider = opts.provider;\n        this._subscriptionId = null;\n    }\n    async checkForLatestBlock() {\n        return await this.getLatestBlock();\n    }\n    async _start() {\n        if (this._subscriptionId === undefined || this._subscriptionId === null) {\n            try {\n                const blockNumber = (await this._call('eth_blockNumber'));\n                this._subscriptionId = (await this._call('eth_subscribe', 'newHeads'));\n                this._provider.on('data', this._handleSubData.bind(this));\n                this._newPotentialLatest(blockNumber);\n            }\n            catch (e) {\n                this.emit('error', e);\n            }\n        }\n    }\n    async _end() {\n        if (this._subscriptionId !== null && this._subscriptionId !== undefined) {\n            try {\n                await this._call('eth_unsubscribe', this._subscriptionId);\n                this._subscriptionId = null;\n            }\n            catch (e) {\n                this.emit('error', e);\n            }\n        }\n    }\n    _call(method, ...params) {\n        return new Promise((resolve, reject) => {\n            this._provider.sendAsync({\n                id: createRandomId(),\n                method,\n                params,\n                jsonrpc: '2.0',\n            }, (err, res) => {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(res.result);\n                }\n            });\n        });\n    }\n    _handleSubData(_, response) {\n        var _a;\n        if (response.method === 'eth_subscription' &&\n            ((_a = response.params) === null || _a === void 0 ? void 0 : _a.subscription) === this._subscriptionId) {\n            this._newPotentialLatest(response.params.result.number);\n        }\n    }\n}\nexports.SubscribeBlockTracker = SubscribeBlockTracker;\n//# sourceMappingURL=SubscribeBlockTracker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvZGlzdC9TdWJzY3JpYmVCbG9ja1RyYWNrZXIuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLDZDQUE2QztBQUM3QztBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCw2QkFBNkI7QUFDN0IsNkNBQTZDLG1CQUFPLENBQUMsNEVBQW9CO0FBQ3pFLDJCQUEyQixtQkFBTyxDQUFDLDJGQUFvQjtBQUN2RDtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0IiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvZGlzdC9TdWJzY3JpYmVCbG9ja1RyYWNrZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgX19pbXBvcnREZWZhdWx0ID0gKHRoaXMgJiYgdGhpcy5fX2ltcG9ydERlZmF1bHQpIHx8IGZ1bmN0aW9uIChtb2QpIHtcbiAgICByZXR1cm4gKG1vZCAmJiBtb2QuX19lc01vZHVsZSkgPyBtb2QgOiB7IFwiZGVmYXVsdFwiOiBtb2QgfTtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlN1YnNjcmliZUJsb2NrVHJhY2tlciA9IHZvaWQgMDtcbmNvbnN0IGpzb25fcnBjX3JhbmRvbV9pZF8xID0gX19pbXBvcnREZWZhdWx0KHJlcXVpcmUoXCJqc29uLXJwYy1yYW5kb20taWRcIikpO1xuY29uc3QgQmFzZUJsb2NrVHJhY2tlcl8xID0gcmVxdWlyZShcIi4vQmFzZUJsb2NrVHJhY2tlclwiKTtcbmNvbnN0IGNyZWF0ZVJhbmRvbUlkID0gKDAsIGpzb25fcnBjX3JhbmRvbV9pZF8xLmRlZmF1bHQpKCk7XG5jbGFzcyBTdWJzY3JpYmVCbG9ja1RyYWNrZXIgZXh0ZW5kcyBCYXNlQmxvY2tUcmFja2VyXzEuQmFzZUJsb2NrVHJhY2tlciB7XG4gICAgY29uc3RydWN0b3Iob3B0cyA9IHt9KSB7XG4gICAgICAgIC8vIHBhcnNlICsgdmFsaWRhdGUgYXJnc1xuICAgICAgICBpZiAoIW9wdHMucHJvdmlkZXIpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignU3Vic2NyaWJlQmxvY2tUcmFja2VyIC0gbm8gcHJvdmlkZXIgc3BlY2lmaWVkLicpO1xuICAgICAgICB9XG4gICAgICAgIC8vIEJhc2VCbG9ja1RyYWNrZXIgY29uc3RydWN0b3JcbiAgICAgICAgc3VwZXIob3B0cyk7XG4gICAgICAgIC8vIGNvbmZpZ1xuICAgICAgICB0aGlzLl9wcm92aWRlciA9IG9wdHMucHJvdmlkZXI7XG4gICAgICAgIHRoaXMuX3N1YnNjcmlwdGlvbklkID0gbnVsbDtcbiAgICB9XG4gICAgYXN5bmMgY2hlY2tGb3JMYXRlc3RCbG9jaygpIHtcbiAgICAgICAgcmV0dXJuIGF3YWl0IHRoaXMuZ2V0TGF0ZXN0QmxvY2soKTtcbiAgICB9XG4gICAgYXN5bmMgX3N0YXJ0KCkge1xuICAgICAgICBpZiAodGhpcy5fc3Vic2NyaXB0aW9uSWQgPT09IHVuZGVmaW5lZCB8fCB0aGlzLl9zdWJzY3JpcHRpb25JZCA9PT0gbnVsbCkge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBibG9ja051bWJlciA9IChhd2FpdCB0aGlzLl9jYWxsKCdldGhfYmxvY2tOdW1iZXInKSk7XG4gICAgICAgICAgICAgICAgdGhpcy5fc3Vic2NyaXB0aW9uSWQgPSAoYXdhaXQgdGhpcy5fY2FsbCgnZXRoX3N1YnNjcmliZScsICduZXdIZWFkcycpKTtcbiAgICAgICAgICAgICAgICB0aGlzLl9wcm92aWRlci5vbignZGF0YScsIHRoaXMuX2hhbmRsZVN1YkRhdGEuYmluZCh0aGlzKSk7XG4gICAgICAgICAgICAgICAgdGhpcy5fbmV3UG90ZW50aWFsTGF0ZXN0KGJsb2NrTnVtYmVyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5lbWl0KCdlcnJvcicsIGUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGFzeW5jIF9lbmQoKSB7XG4gICAgICAgIGlmICh0aGlzLl9zdWJzY3JpcHRpb25JZCAhPT0gbnVsbCAmJiB0aGlzLl9zdWJzY3JpcHRpb25JZCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGF3YWl0IHRoaXMuX2NhbGwoJ2V0aF91bnN1YnNjcmliZScsIHRoaXMuX3N1YnNjcmlwdGlvbklkKTtcbiAgICAgICAgICAgICAgICB0aGlzLl9zdWJzY3JpcHRpb25JZCA9IG51bGw7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgICAgIHRoaXMuZW1pdCgnZXJyb3InLCBlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBfY2FsbChtZXRob2QsIC4uLnBhcmFtcykge1xuICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgdGhpcy5fcHJvdmlkZXIuc2VuZEFzeW5jKHtcbiAgICAgICAgICAgICAgICBpZDogY3JlYXRlUmFuZG9tSWQoKSxcbiAgICAgICAgICAgICAgICBtZXRob2QsXG4gICAgICAgICAgICAgICAgcGFyYW1zLFxuICAgICAgICAgICAgICAgIGpzb25ycGM6ICcyLjAnLFxuICAgICAgICAgICAgfSwgKGVyciwgcmVzKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgICAgICAgICAgICByZWplY3QoZXJyKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHJlc29sdmUocmVzLnJlc3VsdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBfaGFuZGxlU3ViRGF0YShfLCByZXNwb25zZSkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIGlmIChyZXNwb25zZS5tZXRob2QgPT09ICdldGhfc3Vic2NyaXB0aW9uJyAmJlxuICAgICAgICAgICAgKChfYSA9IHJlc3BvbnNlLnBhcmFtcykgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnN1YnNjcmlwdGlvbikgPT09IHRoaXMuX3N1YnNjcmlwdGlvbklkKSB7XG4gICAgICAgICAgICB0aGlzLl9uZXdQb3RlbnRpYWxMYXRlc3QocmVzcG9uc2UucGFyYW1zLnJlc3VsdC5udW1iZXIpO1xuICAgICAgICB9XG4gICAgfVxufVxuZXhwb3J0cy5TdWJzY3JpYmVCbG9ja1RyYWNrZXIgPSBTdWJzY3JpYmVCbG9ja1RyYWNrZXI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1TdWJzY3JpYmVCbG9ja1RyYWNrZXIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/dist/SubscribeBlockTracker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/dist/index.js":
/*!******************************************************!*\
  !*** ./node_modules/eth-block-tracker/dist/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./PollingBlockTracker */ \"(ssr)/./node_modules/eth-block-tracker/dist/PollingBlockTracker.js\"), exports);\n__exportStar(__webpack_require__(/*! ./SubscribeBlockTracker */ \"(ssr)/./node_modules/eth-block-tracker/dist/SubscribeBlockTracker.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQSxtQ0FBbUMsb0NBQW9DLGdCQUFnQjtBQUN2RixDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsYUFBYSxtQkFBTyxDQUFDLGlHQUF1QjtBQUM1QyxhQUFhLG1CQUFPLENBQUMscUdBQXlCO0FBQzlDIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL2Rpc3QvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgX19jcmVhdGVCaW5kaW5nID0gKHRoaXMgJiYgdGhpcy5fX2NyZWF0ZUJpbmRpbmcpIHx8IChPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkobywgazIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbigpIHsgcmV0dXJuIG1ba107IH0gfSk7XG59KSA6IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgb1trMl0gPSBtW2tdO1xufSkpO1xudmFyIF9fZXhwb3J0U3RhciA9ICh0aGlzICYmIHRoaXMuX19leHBvcnRTdGFyKSB8fCBmdW5jdGlvbihtLCBleHBvcnRzKSB7XG4gICAgZm9yICh2YXIgcCBpbiBtKSBpZiAocCAhPT0gXCJkZWZhdWx0XCIgJiYgIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChleHBvcnRzLCBwKSkgX19jcmVhdGVCaW5kaW5nKGV4cG9ydHMsIG0sIHApO1xufTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9Qb2xsaW5nQmxvY2tUcmFja2VyXCIpLCBleHBvcnRzKTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9TdWJzY3JpYmVCbG9ja1RyYWNrZXJcIiksIGV4cG9ydHMpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/dist/logging-utils.js":
/*!**************************************************************!*\
  !*** ./node_modules/eth-block-tracker/dist/logging-utils.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createModuleLogger = exports.projectLogger = void 0;\nconst utils_1 = __webpack_require__(/*! @metamask/utils */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/index.js\");\nObject.defineProperty(exports, \"createModuleLogger\", ({ enumerable: true, get: function () { return utils_1.createModuleLogger; } }));\nexports.projectLogger = (0, utils_1.createProjectLogger)('eth-block-tracker');\n//# sourceMappingURL=logging-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvZGlzdC9sb2dnaW5nLXV0aWxzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDBCQUEwQixHQUFHLHFCQUFxQjtBQUNsRCxnQkFBZ0IsbUJBQU8sQ0FBQywwR0FBaUI7QUFDekMsc0RBQXFELEVBQUUscUNBQXFDLHNDQUFzQyxFQUFDO0FBQ25JLHFCQUFxQjtBQUNyQiIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9ldGgtYmxvY2stdHJhY2tlci9kaXN0L2xvZ2dpbmctdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmNyZWF0ZU1vZHVsZUxvZ2dlciA9IGV4cG9ydHMucHJvamVjdExvZ2dlciA9IHZvaWQgMDtcbmNvbnN0IHV0aWxzXzEgPSByZXF1aXJlKFwiQG1ldGFtYXNrL3V0aWxzXCIpO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiY3JlYXRlTW9kdWxlTG9nZ2VyXCIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbiAoKSB7IHJldHVybiB1dGlsc18xLmNyZWF0ZU1vZHVsZUxvZ2dlcjsgfSB9KTtcbmV4cG9ydHMucHJvamVjdExvZ2dlciA9ICgwLCB1dGlsc18xLmNyZWF0ZVByb2plY3RMb2dnZXIpKCdldGgtYmxvY2stdHJhY2tlcicpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bG9nZ2luZy11dGlscy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/dist/logging-utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js":
/*!************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.assertExhaustive = exports.assertStruct = exports.assert = exports.AssertionError = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/./node_modules/superstruct/dist/index.mjs\");\n/**\n * Type guard for determining whether the given value is an error object with a\n * `message` property, such as an instance of Error.\n *\n * @param error - The object to check.\n * @returns True or false, depending on the result.\n */\nfunction isErrorWithMessage(error) {\n    return typeof error === 'object' && error !== null && 'message' in error;\n}\n/**\n * Check if a value is a constructor, i.e., a function that can be called with\n * the `new` keyword.\n *\n * @param fn - The value to check.\n * @returns `true` if the value is a constructor, or `false` otherwise.\n */\nfunction isConstructable(fn) {\n    var _a, _b;\n    /* istanbul ignore next */\n    return Boolean(typeof ((_b = (_a = fn === null || fn === void 0 ? void 0 : fn.prototype) === null || _a === void 0 ? void 0 : _a.constructor) === null || _b === void 0 ? void 0 : _b.name) === 'string');\n}\n/**\n * Get the error message from an unknown error object. If the error object has\n * a `message` property, that property is returned. Otherwise, the stringified\n * error object is returned.\n *\n * @param error - The error object to get the message from.\n * @returns The error message.\n */\nfunction getErrorMessage(error) {\n    const message = isErrorWithMessage(error) ? error.message : String(error);\n    // If the error ends with a period, remove it, as we'll add our own period.\n    if (message.endsWith('.')) {\n        return message.slice(0, -1);\n    }\n    return message;\n}\n/**\n * Initialise an {@link AssertionErrorConstructor} error.\n *\n * @param ErrorWrapper - The error class to use.\n * @param message - The error message.\n * @returns The error object.\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nfunction getError(ErrorWrapper, message) {\n    if (isConstructable(ErrorWrapper)) {\n        return new ErrorWrapper({\n            message,\n        });\n    }\n    return ErrorWrapper({\n        message,\n    });\n}\n/**\n * The default error class that is thrown if an assertion fails.\n */\nclass AssertionError extends Error {\n    constructor(options) {\n        super(options.message);\n        this.code = 'ERR_ASSERTION';\n    }\n}\nexports.AssertionError = AssertionError;\n/**\n * Same as Node.js assert.\n * If the value is falsy, throws an error, does nothing otherwise.\n *\n * @throws {@link AssertionError} If value is falsy.\n * @param value - The test that should be truthy to pass.\n * @param message - Message to be passed to {@link AssertionError} or an\n * {@link Error} instance to throw.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}. If a custom error class is provided for\n * the `message` argument, this argument is ignored.\n */\nfunction assert(value, message = 'Assertion failed.', \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper = AssertionError) {\n    if (!value) {\n        if (message instanceof Error) {\n            throw message;\n        }\n        throw getError(ErrorWrapper, message);\n    }\n}\nexports.assert = assert;\n/**\n * Assert a value against a Superstruct struct.\n *\n * @param value - The value to validate.\n * @param struct - The struct to validate against.\n * @param errorPrefix - A prefix to add to the error message. Defaults to\n * \"Assertion failed\".\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the value is not valid.\n */\nfunction assertStruct(value, struct, errorPrefix = 'Assertion failed', \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper = AssertionError) {\n    try {\n        (0, superstruct_1.assert)(value, struct);\n    }\n    catch (error) {\n        throw getError(ErrorWrapper, `${errorPrefix}: ${getErrorMessage(error)}.`);\n    }\n}\nexports.assertStruct = assertStruct;\n/**\n * Use in the default case of a switch that you want to be fully exhaustive.\n * Using this function forces the compiler to enforce exhaustivity during\n * compile-time.\n *\n * @example\n * ```\n * const number = 1;\n * switch (number) {\n *   case 0:\n *     ...\n *   case 1:\n *     ...\n *   default:\n *     assertExhaustive(snapPrefix);\n * }\n * ```\n * @param _object - The object on which the switch is being operated.\n */\nfunction assertExhaustive(_object) {\n    throw new Error('Invalid branch reached. Should be detected during compilation.');\n}\nexports.assertExhaustive = assertExhaustive;\n//# sourceMappingURL=assert.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/base64.js":
/*!************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/base64.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.base64 = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/./node_modules/superstruct/dist/index.mjs\");\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\n/**\n * Ensure that a provided string-based struct is valid base64.\n *\n * @param struct - The string based struct.\n * @param options - Optional options to specialize base64 validation. See {@link Base64Options} documentation.\n * @returns A superstruct validating base64.\n */\nconst base64 = (struct, options = {}) => {\n    var _a, _b;\n    const paddingRequired = (_a = options.paddingRequired) !== null && _a !== void 0 ? _a : false;\n    const characterSet = (_b = options.characterSet) !== null && _b !== void 0 ? _b : 'base64';\n    let letters;\n    if (characterSet === 'base64') {\n        letters = String.raw `[A-Za-z0-9+\\/]`;\n    }\n    else {\n        (0, assert_1.assert)(characterSet === 'base64url');\n        letters = String.raw `[-_A-Za-z0-9]`;\n    }\n    let re;\n    if (paddingRequired) {\n        re = new RegExp(`^(?:${letters}{4})*(?:${letters}{3}=|${letters}{2}==)?$`, 'u');\n    }\n    else {\n        re = new RegExp(`^(?:${letters}{4})*(?:${letters}{2,3}|${letters}{3}=|${letters}{2}==)?$`, 'u');\n    }\n    return (0, superstruct_1.pattern)(struct, re);\n};\nexports.base64 = base64;\n//# sourceMappingURL=base64.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/base64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/bytes.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/bytes.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createDataView = exports.concatBytes = exports.valueToBytes = exports.stringToBytes = exports.numberToBytes = exports.signedBigIntToBytes = exports.bigIntToBytes = exports.hexToBytes = exports.bytesToString = exports.bytesToNumber = exports.bytesToSignedBigInt = exports.bytesToBigInt = exports.bytesToHex = exports.assertIsBytes = exports.isBytes = void 0;\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\nconst hex_1 = __webpack_require__(/*! ./hex */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js\");\n// '0'.charCodeAt(0) === 48\nconst HEX_MINIMUM_NUMBER_CHARACTER = 48;\n// '9'.charCodeAt(0) === 57\nconst HEX_MAXIMUM_NUMBER_CHARACTER = 58;\nconst HEX_CHARACTER_OFFSET = 87;\n/**\n * Memoized function that returns an array to be used as a lookup table for\n * converting bytes to hexadecimal values.\n *\n * The array is created lazily and then cached for future use. The benefit of\n * this approach is that the performance of converting bytes to hex is much\n * better than if we were to call `toString(16)` on each byte.\n *\n * The downside is that the array is created once and then never garbage\n * collected. This is not a problem in practice because the array is only 256\n * elements long.\n *\n * @returns A function that returns the lookup table.\n */\nfunction getPrecomputedHexValuesBuilder() {\n    // To avoid issues with tree shaking, we need to use a function to return the\n    // array. This is because the array is only used in the `bytesToHex` function\n    // and if we were to use a global variable, the array might be removed by the\n    // tree shaker.\n    const lookupTable = [];\n    return () => {\n        if (lookupTable.length === 0) {\n            for (let i = 0; i < 256; i++) {\n                lookupTable.push(i.toString(16).padStart(2, '0'));\n            }\n        }\n        return lookupTable;\n    };\n}\n/**\n * Function implementation of the {@link getPrecomputedHexValuesBuilder}\n * function.\n */\nconst getPrecomputedHexValues = getPrecomputedHexValuesBuilder();\n/**\n * Check if a value is a `Uint8Array`.\n *\n * @param value - The value to check.\n * @returns Whether the value is a `Uint8Array`.\n */\nfunction isBytes(value) {\n    return value instanceof Uint8Array;\n}\nexports.isBytes = isBytes;\n/**\n * Assert that a value is a `Uint8Array`.\n *\n * @param value - The value to check.\n * @throws If the value is not a `Uint8Array`.\n */\nfunction assertIsBytes(value) {\n    (0, assert_1.assert)(isBytes(value), 'Value must be a Uint8Array.');\n}\nexports.assertIsBytes = assertIsBytes;\n/**\n * Convert a `Uint8Array` to a hexadecimal string.\n *\n * @param bytes - The bytes to convert to a hexadecimal string.\n * @returns The hexadecimal string.\n */\nfunction bytesToHex(bytes) {\n    assertIsBytes(bytes);\n    if (bytes.length === 0) {\n        return '0x';\n    }\n    const lookupTable = getPrecomputedHexValues();\n    const hexadecimal = new Array(bytes.length);\n    for (let i = 0; i < bytes.length; i++) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        hexadecimal[i] = lookupTable[bytes[i]];\n    }\n    return (0, hex_1.add0x)(hexadecimal.join(''));\n}\nexports.bytesToHex = bytesToHex;\n/**\n * Convert a `Uint8Array` to a `bigint`.\n *\n * To convert a `Uint8Array` to a `number` instead, use {@link bytesToNumber}.\n * To convert a two's complement encoded `Uint8Array` to a `bigint`, use\n * {@link bytesToSignedBigInt}.\n *\n * @param bytes - The bytes to convert to a `bigint`.\n * @returns The `bigint`.\n */\nfunction bytesToBigInt(bytes) {\n    assertIsBytes(bytes);\n    const hexadecimal = bytesToHex(bytes);\n    return BigInt(hexadecimal);\n}\nexports.bytesToBigInt = bytesToBigInt;\n/**\n * Convert a `Uint8Array` to a signed `bigint`. This assumes that the bytes are\n * encoded in two's complement.\n *\n * To convert a `Uint8Array` to an unsigned `bigint` instead, use\n * {@link bytesToBigInt}.\n *\n * @see https://en.wikipedia.org/wiki/Two%27s_complement\n * @param bytes - The bytes to convert to a signed `bigint`.\n * @returns The signed `bigint`.\n */\nfunction bytesToSignedBigInt(bytes) {\n    assertIsBytes(bytes);\n    let value = BigInt(0);\n    for (const byte of bytes) {\n        // eslint-disable-next-line no-bitwise\n        value = (value << BigInt(8)) + BigInt(byte);\n    }\n    return BigInt.asIntN(bytes.length * 8, value);\n}\nexports.bytesToSignedBigInt = bytesToSignedBigInt;\n/**\n * Convert a `Uint8Array` to a `number`.\n *\n * To convert a `Uint8Array` to a `bigint` instead, use {@link bytesToBigInt}.\n *\n * @param bytes - The bytes to convert to a number.\n * @returns The number.\n * @throws If the resulting number is not a safe integer.\n */\nfunction bytesToNumber(bytes) {\n    assertIsBytes(bytes);\n    const bigint = bytesToBigInt(bytes);\n    (0, assert_1.assert)(bigint <= BigInt(Number.MAX_SAFE_INTEGER), 'Number is not a safe integer. Use `bytesToBigInt` instead.');\n    return Number(bigint);\n}\nexports.bytesToNumber = bytesToNumber;\n/**\n * Convert a UTF-8 encoded `Uint8Array` to a `string`.\n *\n * @param bytes - The bytes to convert to a string.\n * @returns The string.\n */\nfunction bytesToString(bytes) {\n    assertIsBytes(bytes);\n    return new TextDecoder().decode(bytes);\n}\nexports.bytesToString = bytesToString;\n/**\n * Convert a hexadecimal string to a `Uint8Array`. The string can optionally be\n * prefixed with `0x`. It accepts even and odd length strings.\n *\n * If the value is \"0x\", an empty `Uint8Array` is returned.\n *\n * @param value - The hexadecimal string to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nfunction hexToBytes(value) {\n    var _a;\n    // \"0x\" is often used as empty byte array.\n    if (((_a = value === null || value === void 0 ? void 0 : value.toLowerCase) === null || _a === void 0 ? void 0 : _a.call(value)) === '0x') {\n        return new Uint8Array();\n    }\n    (0, hex_1.assertIsHexString)(value);\n    // Remove the `0x` prefix if it exists, and pad the string to have an even\n    // number of characters.\n    const strippedValue = (0, hex_1.remove0x)(value).toLowerCase();\n    const normalizedValue = strippedValue.length % 2 === 0 ? strippedValue : `0${strippedValue}`;\n    const bytes = new Uint8Array(normalizedValue.length / 2);\n    for (let i = 0; i < bytes.length; i++) {\n        // While this is not the prettiest way to convert a hexadecimal string to a\n        // `Uint8Array`, it is a lot faster than using `parseInt` to convert each\n        // character.\n        const c1 = normalizedValue.charCodeAt(i * 2);\n        const c2 = normalizedValue.charCodeAt(i * 2 + 1);\n        const n1 = c1 -\n            (c1 < HEX_MAXIMUM_NUMBER_CHARACTER\n                ? HEX_MINIMUM_NUMBER_CHARACTER\n                : HEX_CHARACTER_OFFSET);\n        const n2 = c2 -\n            (c2 < HEX_MAXIMUM_NUMBER_CHARACTER\n                ? HEX_MINIMUM_NUMBER_CHARACTER\n                : HEX_CHARACTER_OFFSET);\n        bytes[i] = n1 * 16 + n2;\n    }\n    return bytes;\n}\nexports.hexToBytes = hexToBytes;\n/**\n * Convert a `bigint` to a `Uint8Array`.\n *\n * This assumes that the `bigint` is an unsigned integer. To convert a signed\n * `bigint` instead, use {@link signedBigIntToBytes}.\n *\n * @param value - The bigint to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nfunction bigIntToBytes(value) {\n    (0, assert_1.assert)(typeof value === 'bigint', 'Value must be a bigint.');\n    (0, assert_1.assert)(value >= BigInt(0), 'Value must be a non-negative bigint.');\n    const hexadecimal = value.toString(16);\n    return hexToBytes(hexadecimal);\n}\nexports.bigIntToBytes = bigIntToBytes;\n/**\n * Check if a `bigint` fits in a certain number of bytes.\n *\n * @param value - The `bigint` to check.\n * @param bytes - The number of bytes.\n * @returns Whether the `bigint` fits in the number of bytes.\n */\nfunction bigIntFits(value, bytes) {\n    (0, assert_1.assert)(bytes > 0);\n    /* eslint-disable no-bitwise */\n    const mask = value >> BigInt(31);\n    return !(((~value & mask) + (value & ~mask)) >> BigInt(bytes * 8 + ~0));\n    /* eslint-enable no-bitwise */\n}\n/**\n * Convert a signed `bigint` to a `Uint8Array`. This uses two's complement\n * encoding to represent negative numbers.\n *\n * To convert an unsigned `bigint` to a `Uint8Array` instead, use\n * {@link bigIntToBytes}.\n *\n * @see https://en.wikipedia.org/wiki/Two%27s_complement\n * @param value - The number to convert to bytes.\n * @param byteLength - The length of the resulting `Uint8Array`. If the number\n * is larger than the maximum value that can be represented by the given length,\n * an error is thrown.\n * @returns The bytes as `Uint8Array`.\n */\nfunction signedBigIntToBytes(value, byteLength) {\n    (0, assert_1.assert)(typeof value === 'bigint', 'Value must be a bigint.');\n    (0, assert_1.assert)(typeof byteLength === 'number', 'Byte length must be a number.');\n    (0, assert_1.assert)(byteLength > 0, 'Byte length must be greater than 0.');\n    (0, assert_1.assert)(bigIntFits(value, byteLength), 'Byte length is too small to represent the given value.');\n    // ESLint doesn't like mutating function parameters, so to avoid having to\n    // disable the rule, we create a new variable.\n    let numberValue = value;\n    const bytes = new Uint8Array(byteLength);\n    for (let i = 0; i < bytes.length; i++) {\n        bytes[i] = Number(BigInt.asUintN(8, numberValue));\n        // eslint-disable-next-line no-bitwise\n        numberValue >>= BigInt(8);\n    }\n    return bytes.reverse();\n}\nexports.signedBigIntToBytes = signedBigIntToBytes;\n/**\n * Convert a `number` to a `Uint8Array`.\n *\n * @param value - The number to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n * @throws If the number is not a safe integer.\n */\nfunction numberToBytes(value) {\n    (0, assert_1.assert)(typeof value === 'number', 'Value must be a number.');\n    (0, assert_1.assert)(value >= 0, 'Value must be a non-negative number.');\n    (0, assert_1.assert)(Number.isSafeInteger(value), 'Value is not a safe integer. Use `bigIntToBytes` instead.');\n    const hexadecimal = value.toString(16);\n    return hexToBytes(hexadecimal);\n}\nexports.numberToBytes = numberToBytes;\n/**\n * Convert a `string` to a UTF-8 encoded `Uint8Array`.\n *\n * @param value - The string to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nfunction stringToBytes(value) {\n    (0, assert_1.assert)(typeof value === 'string', 'Value must be a string.');\n    return new TextEncoder().encode(value);\n}\nexports.stringToBytes = stringToBytes;\n/**\n * Convert a byte-like value to a `Uint8Array`. The value can be a `Uint8Array`,\n * a `bigint`, a `number`, or a `string`.\n *\n * This will attempt to guess the type of the value based on its type and\n * contents. For more control over the conversion, use the more specific\n * conversion functions, such as {@link hexToBytes} or {@link stringToBytes}.\n *\n * If the value is a `string`, and it is prefixed with `0x`, it will be\n * interpreted as a hexadecimal string. Otherwise, it will be interpreted as a\n * UTF-8 string. To convert a hexadecimal string to bytes without interpreting\n * it as a UTF-8 string, use {@link hexToBytes} instead.\n *\n * If the value is a `bigint`, it is assumed to be unsigned. To convert a signed\n * `bigint` to bytes, use {@link signedBigIntToBytes} instead.\n *\n * If the value is a `Uint8Array`, it will be returned as-is.\n *\n * @param value - The value to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nfunction valueToBytes(value) {\n    if (typeof value === 'bigint') {\n        return bigIntToBytes(value);\n    }\n    if (typeof value === 'number') {\n        return numberToBytes(value);\n    }\n    if (typeof value === 'string') {\n        if (value.startsWith('0x')) {\n            return hexToBytes(value);\n        }\n        return stringToBytes(value);\n    }\n    if (isBytes(value)) {\n        return value;\n    }\n    throw new TypeError(`Unsupported value type: \"${typeof value}\".`);\n}\nexports.valueToBytes = valueToBytes;\n/**\n * Concatenate multiple byte-like values into a single `Uint8Array`. The values\n * can be `Uint8Array`, `bigint`, `number`, or `string`. This uses\n * {@link valueToBytes} under the hood to convert each value to bytes. Refer to\n * the documentation of that function for more information.\n *\n * @param values - The values to concatenate.\n * @returns The concatenated bytes as `Uint8Array`.\n */\nfunction concatBytes(values) {\n    const normalizedValues = new Array(values.length);\n    let byteLength = 0;\n    for (let i = 0; i < values.length; i++) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const value = valueToBytes(values[i]);\n        normalizedValues[i] = value;\n        byteLength += value.length;\n    }\n    const bytes = new Uint8Array(byteLength);\n    for (let i = 0, offset = 0; i < normalizedValues.length; i++) {\n        // While we could simply spread the values into an array and use\n        // `Uint8Array.from`, that is a lot slower than using `Uint8Array.set`.\n        bytes.set(normalizedValues[i], offset);\n        offset += normalizedValues[i].length;\n    }\n    return bytes;\n}\nexports.concatBytes = concatBytes;\n/**\n * Create a {@link DataView} from a {@link Uint8Array}. This is a convenience\n * function that avoids having to create a {@link DataView} manually, which\n * requires passing the `byteOffset` and `byteLength` parameters every time.\n *\n * Not passing the `byteOffset` and `byteLength` parameters can result in\n * unexpected behavior when the {@link Uint8Array} is a view of a larger\n * {@link ArrayBuffer}, e.g., when using {@link Uint8Array.subarray}.\n *\n * This function also supports Node.js {@link Buffer}s.\n *\n * @example\n * ```typescript\n * const bytes = new Uint8Array([1, 2, 3]);\n *\n * // This is equivalent to:\n * // const dataView = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n * const dataView = createDataView(bytes);\n * ```\n * @param bytes - The bytes to create the {@link DataView} from.\n * @returns The {@link DataView}.\n */\nfunction createDataView(bytes) {\n    // To maintain compatibility with Node.js, we need to check if the bytes are\n    // a Buffer. If so, we need to slice the buffer to get the underlying\n    // ArrayBuffer.\n    // eslint-disable-next-line no-restricted-globals\n    if (typeof Buffer !== 'undefined' && bytes instanceof Buffer) {\n        const buffer = bytes.buffer.slice(bytes.byteOffset, bytes.byteOffset + bytes.byteLength);\n        return new DataView(buffer);\n    }\n    return new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n}\nexports.createDataView = createDataView;\n//# sourceMappingURL=bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/bytes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/checksum.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/checksum.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ChecksumStruct = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/./node_modules/superstruct/dist/index.mjs\");\nconst base64_1 = __webpack_require__(/*! ./base64 */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/base64.js\");\nexports.ChecksumStruct = (0, superstruct_1.size)((0, base64_1.base64)((0, superstruct_1.string)(), { paddingRequired: true }), 44, 44);\n//# sourceMappingURL=checksum.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL0BtZXRhbWFzay91dGlscy9kaXN0L2NoZWNrc3VtLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHNCQUFzQjtBQUN0QixzQkFBc0IsbUJBQU8sQ0FBQyxvRUFBYTtBQUMzQyxpQkFBaUIsbUJBQU8sQ0FBQyxvR0FBVTtBQUNuQyxzQkFBc0IsK0VBQStFLHVCQUF1QjtBQUM1SCIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9ldGgtYmxvY2stdHJhY2tlci9ub2RlX21vZHVsZXMvQG1ldGFtYXNrL3V0aWxzL2Rpc3QvY2hlY2tzdW0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkNoZWNrc3VtU3RydWN0ID0gdm9pZCAwO1xuY29uc3Qgc3VwZXJzdHJ1Y3RfMSA9IHJlcXVpcmUoXCJzdXBlcnN0cnVjdFwiKTtcbmNvbnN0IGJhc2U2NF8xID0gcmVxdWlyZShcIi4vYmFzZTY0XCIpO1xuZXhwb3J0cy5DaGVja3N1bVN0cnVjdCA9ICgwLCBzdXBlcnN0cnVjdF8xLnNpemUpKCgwLCBiYXNlNjRfMS5iYXNlNjQpKCgwLCBzdXBlcnN0cnVjdF8xLnN0cmluZykoKSwgeyBwYWRkaW5nUmVxdWlyZWQ6IHRydWUgfSksIDQ0LCA0NCk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jaGVja3N1bS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/checksum.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/coercers.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/coercers.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createHex = exports.createBytes = exports.createBigInt = exports.createNumber = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/./node_modules/superstruct/dist/index.mjs\");\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\nconst bytes_1 = __webpack_require__(/*! ./bytes */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/bytes.js\");\nconst hex_1 = __webpack_require__(/*! ./hex */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js\");\nconst NumberLikeStruct = (0, superstruct_1.union)([(0, superstruct_1.number)(), (0, superstruct_1.bigint)(), (0, superstruct_1.string)(), hex_1.StrictHexStruct]);\nconst NumberCoercer = (0, superstruct_1.coerce)((0, superstruct_1.number)(), NumberLikeStruct, Number);\nconst BigIntCoercer = (0, superstruct_1.coerce)((0, superstruct_1.bigint)(), NumberLikeStruct, BigInt);\nconst BytesLikeStruct = (0, superstruct_1.union)([hex_1.StrictHexStruct, (0, superstruct_1.instance)(Uint8Array)]);\nconst BytesCoercer = (0, superstruct_1.coerce)((0, superstruct_1.instance)(Uint8Array), (0, superstruct_1.union)([hex_1.StrictHexStruct]), bytes_1.hexToBytes);\nconst HexCoercer = (0, superstruct_1.coerce)(hex_1.StrictHexStruct, (0, superstruct_1.instance)(Uint8Array), bytes_1.bytesToHex);\n/**\n * Create a number from a number-like value.\n *\n * - If the value is a number, it is returned as-is.\n * - If the value is a `bigint`, it is converted to a number.\n * - If the value is a string, it is interpreted as a decimal number.\n * - If the value is a hex string (i.e., it starts with \"0x\"), it is\n * interpreted as a hexadecimal number.\n *\n * This validates that the value is a number-like value, and that the resulting\n * number is not `NaN` or `Infinity`.\n *\n * @example\n * ```typescript\n * const value = createNumber('0x010203');\n * console.log(value); // 66051\n *\n * const otherValue = createNumber(123n);\n * console.log(otherValue); // 123\n * ```\n * @param value - The value to create the number from.\n * @returns The created number.\n * @throws If the value is not a number-like value, or if the resulting number\n * is `NaN` or `Infinity`.\n */\nfunction createNumber(value) {\n    try {\n        const result = (0, superstruct_1.create)(value, NumberCoercer);\n        (0, assert_1.assert)(Number.isFinite(result), `Expected a number-like value, got \"${value}\".`);\n        return result;\n    }\n    catch (error) {\n        if (error instanceof superstruct_1.StructError) {\n            throw new Error(`Expected a number-like value, got \"${value}\".`);\n        }\n        /* istanbul ignore next */\n        throw error;\n    }\n}\nexports.createNumber = createNumber;\n/**\n * Create a `bigint` from a number-like value.\n *\n * - If the value is a number, it is converted to a `bigint`.\n * - If the value is a `bigint`, it is returned as-is.\n * - If the value is a string, it is interpreted as a decimal number and\n * converted to a `bigint`.\n * - If the value is a hex string (i.e., it starts with \"0x\"), it is\n * interpreted as a hexadecimal number and converted to a `bigint`.\n *\n * @example\n * ```typescript\n * const value = createBigInt('0x010203');\n * console.log(value); // 16909060n\n *\n * const otherValue = createBigInt(123);\n * console.log(otherValue); // 123n\n * ```\n * @param value - The value to create the bigint from.\n * @returns The created bigint.\n * @throws If the value is not a number-like value.\n */\nfunction createBigInt(value) {\n    try {\n        // The `BigInt` constructor throws if the value is not a number-like value.\n        // There is no need to validate the value manually.\n        return (0, superstruct_1.create)(value, BigIntCoercer);\n    }\n    catch (error) {\n        if (error instanceof superstruct_1.StructError) {\n            throw new Error(`Expected a number-like value, got \"${String(error.value)}\".`);\n        }\n        /* istanbul ignore next */\n        throw error;\n    }\n}\nexports.createBigInt = createBigInt;\n/**\n * Create a byte array from a bytes-like value.\n *\n * - If the value is a byte array, it is returned as-is.\n * - If the value is a hex string (i.e., it starts with \"0x\"), it is interpreted\n * as a hexadecimal number and converted to a byte array.\n *\n * @example\n * ```typescript\n * const value = createBytes('0x010203');\n * console.log(value); // Uint8Array [ 1, 2, 3 ]\n *\n * const otherValue = createBytes('0x010203');\n * console.log(otherValue); // Uint8Array [ 1, 2, 3 ]\n * ```\n * @param value - The value to create the byte array from.\n * @returns The created byte array.\n * @throws If the value is not a bytes-like value.\n */\nfunction createBytes(value) {\n    if (typeof value === 'string' && value.toLowerCase() === '0x') {\n        return new Uint8Array();\n    }\n    try {\n        return (0, superstruct_1.create)(value, BytesCoercer);\n    }\n    catch (error) {\n        if (error instanceof superstruct_1.StructError) {\n            throw new Error(`Expected a bytes-like value, got \"${String(error.value)}\".`);\n        }\n        /* istanbul ignore next */\n        throw error;\n    }\n}\nexports.createBytes = createBytes;\n/**\n * Create a hexadecimal string from a bytes-like value.\n *\n * - If the value is a hex string (i.e., it starts with \"0x\"), it is returned\n * as-is.\n * - If the value is a `Uint8Array`, it is converted to a hex string.\n *\n * @example\n * ```typescript\n * const value = createHex(new Uint8Array([1, 2, 3]));\n * console.log(value); // '0x010203'\n *\n * const otherValue = createHex('0x010203');\n * console.log(otherValue); // '0x010203'\n * ```\n * @param value - The value to create the hex string from.\n * @returns The created hex string.\n * @throws If the value is not a bytes-like value.\n */\nfunction createHex(value) {\n    if ((value instanceof Uint8Array && value.length === 0) ||\n        (typeof value === 'string' && value.toLowerCase() === '0x')) {\n        return '0x';\n    }\n    try {\n        return (0, superstruct_1.create)(value, HexCoercer);\n    }\n    catch (error) {\n        if (error instanceof superstruct_1.StructError) {\n            throw new Error(`Expected a bytes-like value, got \"${String(error.value)}\".`);\n        }\n        /* istanbul ignore next */\n        throw error;\n    }\n}\nexports.createHex = createHex;\n//# sourceMappingURL=coercers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/coercers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/collections.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/collections.js ***!
  \*****************************************************************************************/
/***/ (function(__unused_webpack_module, exports) {

eval("\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _FrozenMap_map, _FrozenSet_set;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FrozenSet = exports.FrozenMap = void 0;\n/**\n * A {@link ReadonlyMap} that cannot be modified after instantiation.\n * The implementation uses an inner map hidden via a private field, and the\n * immutability guarantee relies on it being impossible to get a reference\n * to this map.\n */\nclass FrozenMap {\n    constructor(entries) {\n        _FrozenMap_map.set(this, void 0);\n        __classPrivateFieldSet(this, _FrozenMap_map, new Map(entries), \"f\");\n        Object.freeze(this);\n    }\n    get size() {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").size;\n    }\n    [(_FrozenMap_map = new WeakMap(), Symbol.iterator)]() {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\")[Symbol.iterator]();\n    }\n    entries() {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").entries();\n    }\n    forEach(callbackfn, thisArg) {\n        // We have to wrap the specified callback in order to prevent it from\n        // receiving a reference to the inner map.\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").forEach((value, key, _map) => callbackfn.call(thisArg, value, key, this));\n    }\n    get(key) {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").get(key);\n    }\n    has(key) {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").has(key);\n    }\n    keys() {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").keys();\n    }\n    values() {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").values();\n    }\n    toString() {\n        return `FrozenMap(${this.size}) {${this.size > 0\n            ? ` ${[...this.entries()]\n                .map(([key, value]) => `${String(key)} => ${String(value)}`)\n                .join(', ')} `\n            : ''}}`;\n    }\n}\nexports.FrozenMap = FrozenMap;\n/**\n * A {@link ReadonlySet} that cannot be modified after instantiation.\n * The implementation uses an inner set hidden via a private field, and the\n * immutability guarantee relies on it being impossible to get a reference\n * to this set.\n */\nclass FrozenSet {\n    constructor(values) {\n        _FrozenSet_set.set(this, void 0);\n        __classPrivateFieldSet(this, _FrozenSet_set, new Set(values), \"f\");\n        Object.freeze(this);\n    }\n    get size() {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").size;\n    }\n    [(_FrozenSet_set = new WeakMap(), Symbol.iterator)]() {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\")[Symbol.iterator]();\n    }\n    entries() {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").entries();\n    }\n    forEach(callbackfn, thisArg) {\n        // We have to wrap the specified callback in order to prevent it from\n        // receiving a reference to the inner set.\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").forEach((value, value2, _set) => callbackfn.call(thisArg, value, value2, this));\n    }\n    has(value) {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").has(value);\n    }\n    keys() {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").keys();\n    }\n    values() {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").values();\n    }\n    toString() {\n        return `FrozenSet(${this.size}) {${this.size > 0\n            ? ` ${[...this.values()].map((member) => String(member)).join(', ')} `\n            : ''}}`;\n    }\n}\nexports.FrozenSet = FrozenSet;\nObject.freeze(FrozenMap);\nObject.freeze(FrozenMap.prototype);\nObject.freeze(FrozenSet);\nObject.freeze(FrozenSet.prototype);\n//# sourceMappingURL=collections.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/collections.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/encryption-types.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/encryption-types.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=encryption-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL0BtZXRhbWFzay91dGlscy9kaXN0L2VuY3J5cHRpb24tdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL0BtZXRhbWFzay91dGlscy9kaXN0L2VuY3J5cHRpb24tdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1lbmNyeXB0aW9uLXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/encryption-types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.remove0x = exports.add0x = exports.assertIsStrictHexString = exports.assertIsHexString = exports.isStrictHexString = exports.isHexString = exports.StrictHexStruct = exports.HexStruct = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/./node_modules/superstruct/dist/index.mjs\");\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\nexports.HexStruct = (0, superstruct_1.pattern)((0, superstruct_1.string)(), /^(?:0x)?[0-9a-f]+$/iu);\nexports.StrictHexStruct = (0, superstruct_1.pattern)((0, superstruct_1.string)(), /^0x[0-9a-f]+$/iu);\n/**\n * Check if a string is a valid hex string.\n *\n * @param value - The value to check.\n * @returns Whether the value is a valid hex string.\n */\nfunction isHexString(value) {\n    return (0, superstruct_1.is)(value, exports.HexStruct);\n}\nexports.isHexString = isHexString;\n/**\n * Strictly check if a string is a valid hex string. A valid hex string must\n * start with the \"0x\"-prefix.\n *\n * @param value - The value to check.\n * @returns Whether the value is a valid hex string.\n */\nfunction isStrictHexString(value) {\n    return (0, superstruct_1.is)(value, exports.StrictHexStruct);\n}\nexports.isStrictHexString = isStrictHexString;\n/**\n * Assert that a value is a valid hex string.\n *\n * @param value - The value to check.\n * @throws If the value is not a valid hex string.\n */\nfunction assertIsHexString(value) {\n    (0, assert_1.assert)(isHexString(value), 'Value must be a hexadecimal string.');\n}\nexports.assertIsHexString = assertIsHexString;\n/**\n * Assert that a value is a valid hex string. A valid hex string must start with\n * the \"0x\"-prefix.\n *\n * @param value - The value to check.\n * @throws If the value is not a valid hex string.\n */\nfunction assertIsStrictHexString(value) {\n    (0, assert_1.assert)(isStrictHexString(value), 'Value must be a hexadecimal string, starting with \"0x\".');\n}\nexports.assertIsStrictHexString = assertIsStrictHexString;\n/**\n * Add the `0x`-prefix to a hexadecimal string. If the string already has the\n * prefix, it is returned as-is.\n *\n * @param hexadecimal - The hexadecimal string to add the prefix to.\n * @returns The prefixed hexadecimal string.\n */\nfunction add0x(hexadecimal) {\n    if (hexadecimal.startsWith('0x')) {\n        return hexadecimal;\n    }\n    if (hexadecimal.startsWith('0X')) {\n        return `0x${hexadecimal.substring(2)}`;\n    }\n    return `0x${hexadecimal}`;\n}\nexports.add0x = add0x;\n/**\n * Remove the `0x`-prefix from a hexadecimal string. If the string doesn't have\n * the prefix, it is returned as-is.\n *\n * @param hexadecimal - The hexadecimal string to remove the prefix from.\n * @returns The un-prefixed hexadecimal string.\n */\nfunction remove0x(hexadecimal) {\n    if (hexadecimal.startsWith('0x') || hexadecimal.startsWith('0X')) {\n        return hexadecimal.substring(2);\n    }\n    return hexadecimal;\n}\nexports.remove0x = remove0x;\n//# sourceMappingURL=hex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/index.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/index.js ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\"), exports);\n__exportStar(__webpack_require__(/*! ./base64 */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/base64.js\"), exports);\n__exportStar(__webpack_require__(/*! ./bytes */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/bytes.js\"), exports);\n__exportStar(__webpack_require__(/*! ./checksum */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/checksum.js\"), exports);\n__exportStar(__webpack_require__(/*! ./coercers */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/coercers.js\"), exports);\n__exportStar(__webpack_require__(/*! ./collections */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/collections.js\"), exports);\n__exportStar(__webpack_require__(/*! ./encryption-types */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/encryption-types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./hex */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js\"), exports);\n__exportStar(__webpack_require__(/*! ./json */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/json.js\"), exports);\n__exportStar(__webpack_require__(/*! ./keyring */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/keyring.js\"), exports);\n__exportStar(__webpack_require__(/*! ./logging */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/logging.js\"), exports);\n__exportStar(__webpack_require__(/*! ./misc */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/misc.js\"), exports);\n__exportStar(__webpack_require__(/*! ./number */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/number.js\"), exports);\n__exportStar(__webpack_require__(/*! ./opaque */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/opaque.js\"), exports);\n__exportStar(__webpack_require__(/*! ./time */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/time.js\"), exports);\n__exportStar(__webpack_require__(/*! ./transaction-types */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/transaction-types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./versions */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/versions.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/json.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/json.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getJsonRpcIdValidator = exports.assertIsJsonRpcError = exports.isJsonRpcError = exports.assertIsJsonRpcFailure = exports.isJsonRpcFailure = exports.assertIsJsonRpcSuccess = exports.isJsonRpcSuccess = exports.assertIsJsonRpcResponse = exports.isJsonRpcResponse = exports.assertIsPendingJsonRpcResponse = exports.isPendingJsonRpcResponse = exports.JsonRpcResponseStruct = exports.JsonRpcFailureStruct = exports.JsonRpcSuccessStruct = exports.PendingJsonRpcResponseStruct = exports.assertIsJsonRpcRequest = exports.isJsonRpcRequest = exports.assertIsJsonRpcNotification = exports.isJsonRpcNotification = exports.JsonRpcNotificationStruct = exports.JsonRpcRequestStruct = exports.JsonRpcParamsStruct = exports.JsonRpcErrorStruct = exports.JsonRpcIdStruct = exports.JsonRpcVersionStruct = exports.jsonrpc2 = exports.getJsonSize = exports.isValidJson = exports.JsonStruct = exports.UnsafeJsonStruct = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/./node_modules/superstruct/dist/index.mjs\");\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\n/**\n * A struct to check if the given value is finite number. Superstruct's\n * `number()` struct does not check if the value is finite.\n *\n * @returns A struct to check if the given value is finite number.\n */\nconst finiteNumber = () => (0, superstruct_1.define)('finite number', (value) => {\n    return (0, superstruct_1.is)(value, (0, superstruct_1.number)()) && Number.isFinite(value);\n});\n/**\n * A struct to check if the given value is a valid JSON-serializable value.\n *\n * Note that this struct is unsafe. For safe validation, use {@link JsonStruct}.\n */\n// We cannot infer the type of the struct, because it is recursive.\nexports.UnsafeJsonStruct = (0, superstruct_1.union)([\n    (0, superstruct_1.literal)(null),\n    (0, superstruct_1.boolean)(),\n    finiteNumber(),\n    (0, superstruct_1.string)(),\n    (0, superstruct_1.array)((0, superstruct_1.lazy)(() => exports.UnsafeJsonStruct)),\n    (0, superstruct_1.record)((0, superstruct_1.string)(), (0, superstruct_1.lazy)(() => exports.UnsafeJsonStruct)),\n]);\n/**\n * A struct to check if the given value is a valid JSON-serializable value.\n *\n * This struct sanitizes the value before validating it, so that it is safe to\n * use with untrusted input.\n */\nexports.JsonStruct = (0, superstruct_1.define)('Json', (value, context) => {\n    /**\n     * Helper function that runs the given struct validator and returns the\n     * validation errors, if any. If the value is valid, it returns `true`.\n     *\n     * @param innerValue - The value to validate.\n     * @param struct - The struct to use for validation.\n     * @returns The validation errors, or `true` if the value is valid.\n     */\n    function checkStruct(innerValue, struct) {\n        const iterator = struct.validator(innerValue, context);\n        const errors = [...iterator];\n        if (errors.length > 0) {\n            return errors;\n        }\n        return true;\n    }\n    try {\n        // The plain value must be a valid JSON value, but it may be altered in the\n        // process of JSON serialization, so we need to validate it again after\n        // serialization. This has the added benefit that the returned error messages\n        // will be more helpful, as they will point to the exact location of the\n        // invalid value.\n        //\n        // This seems overcomplicated, but without checking the plain value first,\n        // there are some cases where the validation passes, even though the value is\n        // not valid JSON. For example, `undefined` is not valid JSON, but serializing\n        // it will remove it from the object, so the validation will pass.\n        const unsafeResult = checkStruct(value, exports.UnsafeJsonStruct);\n        if (unsafeResult !== true) {\n            return unsafeResult;\n        }\n        // JavaScript engines are highly optimized for this specific use case of\n        // JSON parsing and stringifying, so there should be no performance impact.\n        return checkStruct(JSON.parse(JSON.stringify(value)), exports.UnsafeJsonStruct);\n    }\n    catch (error) {\n        if (error instanceof RangeError) {\n            return 'Circular reference detected';\n        }\n        return false;\n    }\n});\n/**\n * Check if the given value is a valid {@link Json} value, i.e., a value that is\n * serializable to JSON.\n *\n * @param value - The value to check.\n * @returns Whether the value is a valid {@link Json} value.\n */\nfunction isValidJson(value) {\n    return (0, superstruct_1.is)(value, exports.JsonStruct);\n}\nexports.isValidJson = isValidJson;\n/**\n * Get the size of a JSON value in bytes. This also validates the value.\n *\n * @param value - The JSON value to get the size of.\n * @returns The size of the JSON value in bytes.\n */\nfunction getJsonSize(value) {\n    (0, assert_1.assertStruct)(value, exports.JsonStruct, 'Invalid JSON value');\n    const json = JSON.stringify(value);\n    return new TextEncoder().encode(json).byteLength;\n}\nexports.getJsonSize = getJsonSize;\n/**\n * The string '2.0'.\n */\nexports.jsonrpc2 = '2.0';\nexports.JsonRpcVersionStruct = (0, superstruct_1.literal)(exports.jsonrpc2);\nexports.JsonRpcIdStruct = (0, superstruct_1.nullable)((0, superstruct_1.union)([(0, superstruct_1.number)(), (0, superstruct_1.string)()]));\nexports.JsonRpcErrorStruct = (0, superstruct_1.object)({\n    code: (0, superstruct_1.integer)(),\n    message: (0, superstruct_1.string)(),\n    data: (0, superstruct_1.optional)(exports.JsonStruct),\n    stack: (0, superstruct_1.optional)((0, superstruct_1.string)()),\n});\nexports.JsonRpcParamsStruct = (0, superstruct_1.optional)((0, superstruct_1.union)([(0, superstruct_1.record)((0, superstruct_1.string)(), exports.JsonStruct), (0, superstruct_1.array)(exports.JsonStruct)]));\nexports.JsonRpcRequestStruct = (0, superstruct_1.object)({\n    id: exports.JsonRpcIdStruct,\n    jsonrpc: exports.JsonRpcVersionStruct,\n    method: (0, superstruct_1.string)(),\n    params: exports.JsonRpcParamsStruct,\n});\nexports.JsonRpcNotificationStruct = (0, superstruct_1.omit)(exports.JsonRpcRequestStruct, ['id']);\n/**\n * Check if the given value is a valid {@link JsonRpcNotification} object.\n *\n * @param value - The value to check.\n * @returns Whether the given value is a valid {@link JsonRpcNotification}\n * object.\n */\nfunction isJsonRpcNotification(value) {\n    return (0, superstruct_1.is)(value, exports.JsonRpcNotificationStruct);\n}\nexports.isJsonRpcNotification = isJsonRpcNotification;\n/**\n * Assert that the given value is a valid {@link JsonRpcNotification} object.\n *\n * @param value - The value to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcNotification} object.\n */\nfunction assertIsJsonRpcNotification(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcNotificationStruct, 'Invalid JSON-RPC notification', ErrorWrapper);\n}\nexports.assertIsJsonRpcNotification = assertIsJsonRpcNotification;\n/**\n * Check if the given value is a valid {@link JsonRpcRequest} object.\n *\n * @param value - The value to check.\n * @returns Whether the given value is a valid {@link JsonRpcRequest} object.\n */\nfunction isJsonRpcRequest(value) {\n    return (0, superstruct_1.is)(value, exports.JsonRpcRequestStruct);\n}\nexports.isJsonRpcRequest = isJsonRpcRequest;\n/**\n * Assert that the given value is a valid {@link JsonRpcRequest} object.\n *\n * @param value - The JSON-RPC request or notification to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcRequest} object.\n */\nfunction assertIsJsonRpcRequest(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcRequestStruct, 'Invalid JSON-RPC request', ErrorWrapper);\n}\nexports.assertIsJsonRpcRequest = assertIsJsonRpcRequest;\nexports.PendingJsonRpcResponseStruct = (0, superstruct_1.object)({\n    id: exports.JsonRpcIdStruct,\n    jsonrpc: exports.JsonRpcVersionStruct,\n    result: (0, superstruct_1.optional)((0, superstruct_1.unknown)()),\n    error: (0, superstruct_1.optional)(exports.JsonRpcErrorStruct),\n});\nexports.JsonRpcSuccessStruct = (0, superstruct_1.object)({\n    id: exports.JsonRpcIdStruct,\n    jsonrpc: exports.JsonRpcVersionStruct,\n    result: exports.JsonStruct,\n});\nexports.JsonRpcFailureStruct = (0, superstruct_1.object)({\n    id: exports.JsonRpcIdStruct,\n    jsonrpc: exports.JsonRpcVersionStruct,\n    error: exports.JsonRpcErrorStruct,\n});\nexports.JsonRpcResponseStruct = (0, superstruct_1.union)([\n    exports.JsonRpcSuccessStruct,\n    exports.JsonRpcFailureStruct,\n]);\n/**\n * Type guard to check whether specified JSON-RPC response is a\n * {@link PendingJsonRpcResponse}.\n *\n * @param response - The JSON-RPC response to check.\n * @returns Whether the specified JSON-RPC response is pending.\n */\nfunction isPendingJsonRpcResponse(response) {\n    return (0, superstruct_1.is)(response, exports.PendingJsonRpcResponseStruct);\n}\nexports.isPendingJsonRpcResponse = isPendingJsonRpcResponse;\n/**\n * Assert that the given value is a valid {@link PendingJsonRpcResponse} object.\n *\n * @param response - The JSON-RPC response to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link PendingJsonRpcResponse}\n * object.\n */\nfunction assertIsPendingJsonRpcResponse(response, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(response, exports.PendingJsonRpcResponseStruct, 'Invalid pending JSON-RPC response', ErrorWrapper);\n}\nexports.assertIsPendingJsonRpcResponse = assertIsPendingJsonRpcResponse;\n/**\n * Type guard to check if a value is a {@link JsonRpcResponse}.\n *\n * @param response - The object to check.\n * @returns Whether the object is a JsonRpcResponse.\n */\nfunction isJsonRpcResponse(response) {\n    return (0, superstruct_1.is)(response, exports.JsonRpcResponseStruct);\n}\nexports.isJsonRpcResponse = isJsonRpcResponse;\n/**\n * Assert that the given value is a valid {@link JsonRpcResponse} object.\n *\n * @param value - The value to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcResponse} object.\n */\nfunction assertIsJsonRpcResponse(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcResponseStruct, 'Invalid JSON-RPC response', ErrorWrapper);\n}\nexports.assertIsJsonRpcResponse = assertIsJsonRpcResponse;\n/**\n * Check if the given value is a valid {@link JsonRpcSuccess} object.\n *\n * @param value - The value to check.\n * @returns Whether the given value is a valid {@link JsonRpcSuccess} object.\n */\nfunction isJsonRpcSuccess(value) {\n    return (0, superstruct_1.is)(value, exports.JsonRpcSuccessStruct);\n}\nexports.isJsonRpcSuccess = isJsonRpcSuccess;\n/**\n * Assert that the given value is a valid {@link JsonRpcSuccess} object.\n *\n * @param value - The value to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcSuccess} object.\n */\nfunction assertIsJsonRpcSuccess(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcSuccessStruct, 'Invalid JSON-RPC success response', ErrorWrapper);\n}\nexports.assertIsJsonRpcSuccess = assertIsJsonRpcSuccess;\n/**\n * Check if the given value is a valid {@link JsonRpcFailure} object.\n *\n * @param value - The value to check.\n * @returns Whether the given value is a valid {@link JsonRpcFailure} object.\n */\nfunction isJsonRpcFailure(value) {\n    return (0, superstruct_1.is)(value, exports.JsonRpcFailureStruct);\n}\nexports.isJsonRpcFailure = isJsonRpcFailure;\n/**\n * Assert that the given value is a valid {@link JsonRpcFailure} object.\n *\n * @param value - The value to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcFailure} object.\n */\nfunction assertIsJsonRpcFailure(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcFailureStruct, 'Invalid JSON-RPC failure response', ErrorWrapper);\n}\nexports.assertIsJsonRpcFailure = assertIsJsonRpcFailure;\n/**\n * Check if the given value is a valid {@link JsonRpcError} object.\n *\n * @param value - The value to check.\n * @returns Whether the given value is a valid {@link JsonRpcError} object.\n */\nfunction isJsonRpcError(value) {\n    return (0, superstruct_1.is)(value, exports.JsonRpcErrorStruct);\n}\nexports.isJsonRpcError = isJsonRpcError;\n/**\n * Assert that the given value is a valid {@link JsonRpcError} object.\n *\n * @param value - The value to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcError} object.\n */\nfunction assertIsJsonRpcError(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcErrorStruct, 'Invalid JSON-RPC error', ErrorWrapper);\n}\nexports.assertIsJsonRpcError = assertIsJsonRpcError;\n/**\n * Gets a function for validating JSON-RPC request / response `id` values.\n *\n * By manipulating the options of this factory, you can control the behavior\n * of the resulting validator for some edge cases. This is useful because e.g.\n * `null` should sometimes but not always be permitted.\n *\n * Note that the empty string (`''`) is always permitted by the JSON-RPC\n * specification, but that kind of sucks and you may want to forbid it in some\n * instances anyway.\n *\n * For more details, see the\n * [JSON-RPC Specification](https://www.jsonrpc.org/specification).\n *\n * @param options - An options object.\n * @param options.permitEmptyString - Whether the empty string (i.e. `''`)\n * should be treated as a valid ID. Default: `true`\n * @param options.permitFractions - Whether fractional numbers (e.g. `1.2`)\n * should be treated as valid IDs. Default: `false`\n * @param options.permitNull - Whether `null` should be treated as a valid ID.\n * Default: `true`\n * @returns The JSON-RPC ID validator function.\n */\nfunction getJsonRpcIdValidator(options) {\n    const { permitEmptyString, permitFractions, permitNull } = Object.assign({ permitEmptyString: true, permitFractions: false, permitNull: true }, options);\n    /**\n     * Type guard for {@link JsonRpcId}.\n     *\n     * @param id - The JSON-RPC ID value to check.\n     * @returns Whether the given ID is valid per the options given to the\n     * factory.\n     */\n    const isValidJsonRpcId = (id) => {\n        return Boolean((typeof id === 'number' && (permitFractions || Number.isInteger(id))) ||\n            (typeof id === 'string' && (permitEmptyString || id.length > 0)) ||\n            (permitNull && id === null));\n    };\n    return isValidJsonRpcId;\n}\nexports.getJsonRpcIdValidator = getJsonRpcIdValidator;\n//# sourceMappingURL=json.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/json.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/keyring.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/keyring.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=keyring.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL0BtZXRhbWFzay91dGlscy9kaXN0L2tleXJpbmcuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL0BtZXRhbWFzay91dGlscy9kaXN0L2tleXJpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1rZXlyaW5nLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/keyring.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/logging.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/logging.js ***!
  \*************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createModuleLogger = exports.createProjectLogger = void 0;\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\"));\nconst globalLogger = (0, debug_1.default)('metamask');\n/**\n * Creates a logger via the `debug` library whose log messages will be tagged\n * using the name of your project. By default, such messages will be\n * suppressed, but you can reveal them by setting the `DEBUG` environment\n * variable to `metamask:<projectName>`. You can also set this variable to\n * `metamask:*` if you want to see log messages from all MetaMask projects that\n * are also using this function to create their loggers.\n *\n * @param projectName - The name of your project. This should be the name of\n * your NPM package if you're developing one.\n * @returns An instance of `debug`.\n */\nfunction createProjectLogger(projectName) {\n    return globalLogger.extend(projectName);\n}\nexports.createProjectLogger = createProjectLogger;\n/**\n * Creates a logger via the `debug` library which is derived from the logger for\n * the whole project whose log messages will be tagged using the name of your\n * module. By default, such messages will be suppressed, but you can reveal them\n * by setting the `DEBUG` environment variable to\n * `metamask:<projectName>:<moduleName>`. You can also set this variable to\n * `metamask:<projectName>:*` if you want to see log messages from the project,\n * or `metamask:*` if you want to see log messages from all MetaMask projects.\n *\n * @param projectLogger - The logger created via {@link createProjectLogger}.\n * @param moduleName - The name of your module. You could use the name of the\n * file where you're using this logger or some other name.\n * @returns An instance of `debug`.\n */\nfunction createModuleLogger(projectLogger, moduleName) {\n    return projectLogger.extend(moduleName);\n}\nexports.createModuleLogger = createModuleLogger;\n//# sourceMappingURL=logging.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL0BtZXRhbWFzay91dGlscy9kaXN0L2xvZ2dpbmcuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLDZDQUE2QztBQUM3QztBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCwwQkFBMEIsR0FBRywyQkFBMkI7QUFDeEQsZ0NBQWdDLG1CQUFPLENBQUMsc0RBQU87QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0RBQWtELDBCQUEwQjtBQUM1RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQiIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9ldGgtYmxvY2stdHJhY2tlci9ub2RlX21vZHVsZXMvQG1ldGFtYXNrL3V0aWxzL2Rpc3QvbG9nZ2luZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2ltcG9ydERlZmF1bHQgPSAodGhpcyAmJiB0aGlzLl9faW1wb3J0RGVmYXVsdCkgfHwgZnVuY3Rpb24gKG1vZCkge1xuICAgIHJldHVybiAobW9kICYmIG1vZC5fX2VzTW9kdWxlKSA/IG1vZCA6IHsgXCJkZWZhdWx0XCI6IG1vZCB9O1xufTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuY3JlYXRlTW9kdWxlTG9nZ2VyID0gZXhwb3J0cy5jcmVhdGVQcm9qZWN0TG9nZ2VyID0gdm9pZCAwO1xuY29uc3QgZGVidWdfMSA9IF9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwiZGVidWdcIikpO1xuY29uc3QgZ2xvYmFsTG9nZ2VyID0gKDAsIGRlYnVnXzEuZGVmYXVsdCkoJ21ldGFtYXNrJyk7XG4vKipcbiAqIENyZWF0ZXMgYSBsb2dnZXIgdmlhIHRoZSBgZGVidWdgIGxpYnJhcnkgd2hvc2UgbG9nIG1lc3NhZ2VzIHdpbGwgYmUgdGFnZ2VkXG4gKiB1c2luZyB0aGUgbmFtZSBvZiB5b3VyIHByb2plY3QuIEJ5IGRlZmF1bHQsIHN1Y2ggbWVzc2FnZXMgd2lsbCBiZVxuICogc3VwcHJlc3NlZCwgYnV0IHlvdSBjYW4gcmV2ZWFsIHRoZW0gYnkgc2V0dGluZyB0aGUgYERFQlVHYCBlbnZpcm9ubWVudFxuICogdmFyaWFibGUgdG8gYG1ldGFtYXNrOjxwcm9qZWN0TmFtZT5gLiBZb3UgY2FuIGFsc28gc2V0IHRoaXMgdmFyaWFibGUgdG9cbiAqIGBtZXRhbWFzazoqYCBpZiB5b3Ugd2FudCB0byBzZWUgbG9nIG1lc3NhZ2VzIGZyb20gYWxsIE1ldGFNYXNrIHByb2plY3RzIHRoYXRcbiAqIGFyZSBhbHNvIHVzaW5nIHRoaXMgZnVuY3Rpb24gdG8gY3JlYXRlIHRoZWlyIGxvZ2dlcnMuXG4gKlxuICogQHBhcmFtIHByb2plY3ROYW1lIC0gVGhlIG5hbWUgb2YgeW91ciBwcm9qZWN0LiBUaGlzIHNob3VsZCBiZSB0aGUgbmFtZSBvZlxuICogeW91ciBOUE0gcGFja2FnZSBpZiB5b3UncmUgZGV2ZWxvcGluZyBvbmUuXG4gKiBAcmV0dXJucyBBbiBpbnN0YW5jZSBvZiBgZGVidWdgLlxuICovXG5mdW5jdGlvbiBjcmVhdGVQcm9qZWN0TG9nZ2VyKHByb2plY3ROYW1lKSB7XG4gICAgcmV0dXJuIGdsb2JhbExvZ2dlci5leHRlbmQocHJvamVjdE5hbWUpO1xufVxuZXhwb3J0cy5jcmVhdGVQcm9qZWN0TG9nZ2VyID0gY3JlYXRlUHJvamVjdExvZ2dlcjtcbi8qKlxuICogQ3JlYXRlcyBhIGxvZ2dlciB2aWEgdGhlIGBkZWJ1Z2AgbGlicmFyeSB3aGljaCBpcyBkZXJpdmVkIGZyb20gdGhlIGxvZ2dlciBmb3JcbiAqIHRoZSB3aG9sZSBwcm9qZWN0IHdob3NlIGxvZyBtZXNzYWdlcyB3aWxsIGJlIHRhZ2dlZCB1c2luZyB0aGUgbmFtZSBvZiB5b3VyXG4gKiBtb2R1bGUuIEJ5IGRlZmF1bHQsIHN1Y2ggbWVzc2FnZXMgd2lsbCBiZSBzdXBwcmVzc2VkLCBidXQgeW91IGNhbiByZXZlYWwgdGhlbVxuICogYnkgc2V0dGluZyB0aGUgYERFQlVHYCBlbnZpcm9ubWVudCB2YXJpYWJsZSB0b1xuICogYG1ldGFtYXNrOjxwcm9qZWN0TmFtZT46PG1vZHVsZU5hbWU+YC4gWW91IGNhbiBhbHNvIHNldCB0aGlzIHZhcmlhYmxlIHRvXG4gKiBgbWV0YW1hc2s6PHByb2plY3ROYW1lPjoqYCBpZiB5b3Ugd2FudCB0byBzZWUgbG9nIG1lc3NhZ2VzIGZyb20gdGhlIHByb2plY3QsXG4gKiBvciBgbWV0YW1hc2s6KmAgaWYgeW91IHdhbnQgdG8gc2VlIGxvZyBtZXNzYWdlcyBmcm9tIGFsbCBNZXRhTWFzayBwcm9qZWN0cy5cbiAqXG4gKiBAcGFyYW0gcHJvamVjdExvZ2dlciAtIFRoZSBsb2dnZXIgY3JlYXRlZCB2aWEge0BsaW5rIGNyZWF0ZVByb2plY3RMb2dnZXJ9LlxuICogQHBhcmFtIG1vZHVsZU5hbWUgLSBUaGUgbmFtZSBvZiB5b3VyIG1vZHVsZS4gWW91IGNvdWxkIHVzZSB0aGUgbmFtZSBvZiB0aGVcbiAqIGZpbGUgd2hlcmUgeW91J3JlIHVzaW5nIHRoaXMgbG9nZ2VyIG9yIHNvbWUgb3RoZXIgbmFtZS5cbiAqIEByZXR1cm5zIEFuIGluc3RhbmNlIG9mIGBkZWJ1Z2AuXG4gKi9cbmZ1bmN0aW9uIGNyZWF0ZU1vZHVsZUxvZ2dlcihwcm9qZWN0TG9nZ2VyLCBtb2R1bGVOYW1lKSB7XG4gICAgcmV0dXJuIHByb2plY3RMb2dnZXIuZXh0ZW5kKG1vZHVsZU5hbWUpO1xufVxuZXhwb3J0cy5jcmVhdGVNb2R1bGVMb2dnZXIgPSBjcmVhdGVNb2R1bGVMb2dnZXI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1sb2dnaW5nLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/logging.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/misc.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/misc.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n//\n// Types\n//\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.calculateNumberSize = exports.calculateStringSize = exports.isASCII = exports.isPlainObject = exports.ESCAPE_CHARACTERS_REGEXP = exports.JsonSize = exports.hasProperty = exports.isObject = exports.isNullOrUndefined = exports.isNonEmptyArray = void 0;\n//\n// Type Guards\n//\n/**\n * A {@link NonEmptyArray} type guard.\n *\n * @template Element - The non-empty array member type.\n * @param value - The value to check.\n * @returns Whether the value is a non-empty array.\n */\nfunction isNonEmptyArray(value) {\n    return Array.isArray(value) && value.length > 0;\n}\nexports.isNonEmptyArray = isNonEmptyArray;\n/**\n * Type guard for \"nullishness\".\n *\n * @param value - Any value.\n * @returns `true` if the value is null or undefined, `false` otherwise.\n */\nfunction isNullOrUndefined(value) {\n    return value === null || value === undefined;\n}\nexports.isNullOrUndefined = isNullOrUndefined;\n/**\n * A type guard for {@link RuntimeObject}.\n *\n * @param value - The value to check.\n * @returns Whether the specified value has a runtime type of `object` and is\n * neither `null` nor an `Array`.\n */\nfunction isObject(value) {\n    return Boolean(value) && typeof value === 'object' && !Array.isArray(value);\n}\nexports.isObject = isObject;\n//\n// Other utility functions\n//\n/**\n * A type guard for ensuring an object has a property.\n *\n * @param objectToCheck - The object to check.\n * @param name - The property name to check for.\n * @returns Whether the specified object has an own property with the specified\n * name, regardless of whether it is enumerable or not.\n */\nconst hasProperty = (objectToCheck, name) => Object.hasOwnProperty.call(objectToCheck, name);\nexports.hasProperty = hasProperty;\n/**\n * Predefined sizes (in Bytes) of specific parts of JSON structure.\n */\nvar JsonSize;\n(function (JsonSize) {\n    JsonSize[JsonSize[\"Null\"] = 4] = \"Null\";\n    JsonSize[JsonSize[\"Comma\"] = 1] = \"Comma\";\n    JsonSize[JsonSize[\"Wrapper\"] = 1] = \"Wrapper\";\n    JsonSize[JsonSize[\"True\"] = 4] = \"True\";\n    JsonSize[JsonSize[\"False\"] = 5] = \"False\";\n    JsonSize[JsonSize[\"Quote\"] = 1] = \"Quote\";\n    JsonSize[JsonSize[\"Colon\"] = 1] = \"Colon\";\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    JsonSize[JsonSize[\"Date\"] = 24] = \"Date\";\n})(JsonSize = exports.JsonSize || (exports.JsonSize = {}));\n/**\n * Regular expression with pattern matching for (special) escaped characters.\n */\nexports.ESCAPE_CHARACTERS_REGEXP = /\"|\\\\|\\n|\\r|\\t/gu;\n/**\n * Check if the value is plain object.\n *\n * @param value - Value to be checked.\n * @returns True if an object is the plain JavaScript object,\n * false if the object is not plain (e.g. function).\n */\nfunction isPlainObject(value) {\n    if (typeof value !== 'object' || value === null) {\n        return false;\n    }\n    try {\n        let proto = value;\n        while (Object.getPrototypeOf(proto) !== null) {\n            proto = Object.getPrototypeOf(proto);\n        }\n        return Object.getPrototypeOf(value) === proto;\n    }\n    catch (_) {\n        return false;\n    }\n}\nexports.isPlainObject = isPlainObject;\n/**\n * Check if character is ASCII.\n *\n * @param character - Character.\n * @returns True if a character code is ASCII, false if not.\n */\nfunction isASCII(character) {\n    return character.charCodeAt(0) <= 127;\n}\nexports.isASCII = isASCII;\n/**\n * Calculate string size.\n *\n * @param value - String value to calculate size.\n * @returns Number of bytes used to store whole string value.\n */\nfunction calculateStringSize(value) {\n    var _a;\n    const size = value.split('').reduce((total, character) => {\n        if (isASCII(character)) {\n            return total + 1;\n        }\n        return total + 2;\n    }, 0);\n    // Also detect characters that need backslash escape\n    return size + ((_a = value.match(exports.ESCAPE_CHARACTERS_REGEXP)) !== null && _a !== void 0 ? _a : []).length;\n}\nexports.calculateStringSize = calculateStringSize;\n/**\n * Calculate size of a number ofter JSON serialization.\n *\n * @param value - Number value to calculate size.\n * @returns Number of bytes used to store whole number in JSON.\n */\nfunction calculateNumberSize(value) {\n    return value.toString().length;\n}\nexports.calculateNumberSize = calculateNumberSize;\n//# sourceMappingURL=misc.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/misc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/number.js":
/*!************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/number.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.hexToBigInt = exports.hexToNumber = exports.bigIntToHex = exports.numberToHex = void 0;\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\nconst hex_1 = __webpack_require__(/*! ./hex */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js\");\n/**\n * Convert a number to a hexadecimal string. This verifies that the number is a\n * non-negative safe integer.\n *\n * To convert a `bigint` to a hexadecimal string instead, use\n * {@link bigIntToHex}.\n *\n * @example\n * ```typescript\n * numberToHex(0); // '0x0'\n * numberToHex(1); // '0x1'\n * numberToHex(16); // '0x10'\n * ```\n * @param value - The number to convert to a hexadecimal string.\n * @returns The hexadecimal string, with the \"0x\"-prefix.\n * @throws If the number is not a non-negative safe integer.\n */\nconst numberToHex = (value) => {\n    (0, assert_1.assert)(typeof value === 'number', 'Value must be a number.');\n    (0, assert_1.assert)(value >= 0, 'Value must be a non-negative number.');\n    (0, assert_1.assert)(Number.isSafeInteger(value), 'Value is not a safe integer. Use `bigIntToHex` instead.');\n    return (0, hex_1.add0x)(value.toString(16));\n};\nexports.numberToHex = numberToHex;\n/**\n * Convert a `bigint` to a hexadecimal string. This verifies that the `bigint`\n * is a non-negative integer.\n *\n * To convert a number to a hexadecimal string instead, use {@link numberToHex}.\n *\n * @example\n * ```typescript\n * bigIntToHex(0n); // '0x0'\n * bigIntToHex(1n); // '0x1'\n * bigIntToHex(16n); // '0x10'\n * ```\n * @param value - The `bigint` to convert to a hexadecimal string.\n * @returns The hexadecimal string, with the \"0x\"-prefix.\n * @throws If the `bigint` is not a non-negative integer.\n */\nconst bigIntToHex = (value) => {\n    (0, assert_1.assert)(typeof value === 'bigint', 'Value must be a bigint.');\n    (0, assert_1.assert)(value >= 0, 'Value must be a non-negative bigint.');\n    return (0, hex_1.add0x)(value.toString(16));\n};\nexports.bigIntToHex = bigIntToHex;\n/**\n * Convert a hexadecimal string to a number. This verifies that the string is a\n * valid hex string, and that the resulting number is a safe integer. Both\n * \"0x\"-prefixed and unprefixed strings are supported.\n *\n * To convert a hexadecimal string to a `bigint` instead, use\n * {@link hexToBigInt}.\n *\n * @example\n * ```typescript\n * hexToNumber('0x0'); // 0\n * hexToNumber('0x1'); // 1\n * hexToNumber('0x10'); // 16\n * ```\n * @param value - The hexadecimal string to convert to a number.\n * @returns The number.\n * @throws If the value is not a valid hexadecimal string, or if the resulting\n * number is not a safe integer.\n */\nconst hexToNumber = (value) => {\n    (0, hex_1.assertIsHexString)(value);\n    // `parseInt` accepts values without the \"0x\"-prefix, whereas `Number` does\n    // not. Using this is slightly faster than `Number(add0x(value))`.\n    const numberValue = parseInt(value, 16);\n    (0, assert_1.assert)(Number.isSafeInteger(numberValue), 'Value is not a safe integer. Use `hexToBigInt` instead.');\n    return numberValue;\n};\nexports.hexToNumber = hexToNumber;\n/**\n * Convert a hexadecimal string to a `bigint`. This verifies that the string is\n * a valid hex string. Both \"0x\"-prefixed and unprefixed strings are supported.\n *\n * To convert a hexadecimal string to a number instead, use {@link hexToNumber}.\n *\n * @example\n * ```typescript\n * hexToBigInt('0x0'); // 0n\n * hexToBigInt('0x1'); // 1n\n * hexToBigInt('0x10'); // 16n\n * ```\n * @param value - The hexadecimal string to convert to a `bigint`.\n * @returns The `bigint`.\n * @throws If the value is not a valid hexadecimal string.\n */\nconst hexToBigInt = (value) => {\n    (0, hex_1.assertIsHexString)(value);\n    // The `BigInt` constructor requires the \"0x\"-prefix to parse a hex string.\n    return BigInt((0, hex_1.add0x)(value));\n};\nexports.hexToBigInt = hexToBigInt;\n//# sourceMappingURL=number.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/number.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/opaque.js":
/*!************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/opaque.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=opaque.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL0BtZXRhbWFzay91dGlscy9kaXN0L29wYXF1ZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9ldGgtYmxvY2stdHJhY2tlci9ub2RlX21vZHVsZXMvQG1ldGFtYXNrL3V0aWxzL2Rpc3Qvb3BhcXVlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9b3BhcXVlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/opaque.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/time.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/time.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.timeSince = exports.inMilliseconds = exports.Duration = void 0;\n/**\n * Common duration constants, in milliseconds.\n */\nvar Duration;\n(function (Duration) {\n    /**\n     * A millisecond.\n     */\n    Duration[Duration[\"Millisecond\"] = 1] = \"Millisecond\";\n    /**\n     * A second, in milliseconds.\n     */\n    Duration[Duration[\"Second\"] = 1000] = \"Second\";\n    /**\n     * A minute, in milliseconds.\n     */\n    Duration[Duration[\"Minute\"] = 60000] = \"Minute\";\n    /**\n     * An hour, in milliseconds.\n     */\n    Duration[Duration[\"Hour\"] = 3600000] = \"Hour\";\n    /**\n     * A day, in milliseconds.\n     */\n    Duration[Duration[\"Day\"] = 86400000] = \"Day\";\n    /**\n     * A week, in milliseconds.\n     */\n    Duration[Duration[\"Week\"] = 604800000] = \"Week\";\n    /**\n     * A year, in milliseconds.\n     */\n    Duration[Duration[\"Year\"] = 31536000000] = \"Year\";\n})(Duration = exports.Duration || (exports.Duration = {}));\nconst isNonNegativeInteger = (number) => Number.isInteger(number) && number >= 0;\nconst assertIsNonNegativeInteger = (number, name) => {\n    if (!isNonNegativeInteger(number)) {\n        throw new Error(`\"${name}\" must be a non-negative integer. Received: \"${number}\".`);\n    }\n};\n/**\n * Calculates the millisecond value of the specified number of units of time.\n *\n * @param count - The number of units of time.\n * @param duration - The unit of time to count.\n * @returns The count multiplied by the specified duration.\n */\nfunction inMilliseconds(count, duration) {\n    assertIsNonNegativeInteger(count, 'count');\n    return count * duration;\n}\nexports.inMilliseconds = inMilliseconds;\n/**\n * Gets the milliseconds since a particular Unix epoch timestamp.\n *\n * @param timestamp - A Unix millisecond timestamp.\n * @returns The number of milliseconds elapsed since the specified timestamp.\n */\nfunction timeSince(timestamp) {\n    assertIsNonNegativeInteger(timestamp, 'timestamp');\n    return Date.now() - timestamp;\n}\nexports.timeSince = timeSince;\n//# sourceMappingURL=time.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/time.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/transaction-types.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/transaction-types.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=transaction-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL0BtZXRhbWFzay91dGlscy9kaXN0L3RyYW5zYWN0aW9uLXR5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9AbWV0YW1hc2svdXRpbHMvZGlzdC90cmFuc2FjdGlvbi10eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRyYW5zYWN0aW9uLXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/transaction-types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/versions.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/versions.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.satisfiesVersionRange = exports.gtRange = exports.gtVersion = exports.assertIsSemVerRange = exports.assertIsSemVerVersion = exports.isValidSemVerRange = exports.isValidSemVerVersion = exports.VersionRangeStruct = exports.VersionStruct = void 0;\nconst semver_1 = __webpack_require__(/*! semver */ \"(ssr)/./node_modules/semver/index.js\");\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/./node_modules/superstruct/dist/index.mjs\");\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\n/**\n * A struct for validating a version string.\n */\nexports.VersionStruct = (0, superstruct_1.refine)((0, superstruct_1.string)(), 'Version', (value) => {\n    if ((0, semver_1.valid)(value) === null) {\n        return `Expected SemVer version, got \"${value}\"`;\n    }\n    return true;\n});\nexports.VersionRangeStruct = (0, superstruct_1.refine)((0, superstruct_1.string)(), 'Version range', (value) => {\n    if ((0, semver_1.validRange)(value) === null) {\n        return `Expected SemVer range, got \"${value}\"`;\n    }\n    return true;\n});\n/**\n * Checks whether a SemVer version is valid.\n *\n * @param version - A potential version.\n * @returns `true` if the version is valid, and `false` otherwise.\n */\nfunction isValidSemVerVersion(version) {\n    return (0, superstruct_1.is)(version, exports.VersionStruct);\n}\nexports.isValidSemVerVersion = isValidSemVerVersion;\n/**\n * Checks whether a SemVer version range is valid.\n *\n * @param versionRange - A potential version range.\n * @returns `true` if the version range is valid, and `false` otherwise.\n */\nfunction isValidSemVerRange(versionRange) {\n    return (0, superstruct_1.is)(versionRange, exports.VersionRangeStruct);\n}\nexports.isValidSemVerRange = isValidSemVerRange;\n/**\n * Asserts that a value is a valid concrete SemVer version.\n *\n * @param version - A potential SemVer concrete version.\n */\nfunction assertIsSemVerVersion(version) {\n    (0, assert_1.assertStruct)(version, exports.VersionStruct);\n}\nexports.assertIsSemVerVersion = assertIsSemVerVersion;\n/**\n * Asserts that a value is a valid SemVer range.\n *\n * @param range - A potential SemVer range.\n */\nfunction assertIsSemVerRange(range) {\n    (0, assert_1.assertStruct)(range, exports.VersionRangeStruct);\n}\nexports.assertIsSemVerRange = assertIsSemVerRange;\n/**\n * Checks whether a SemVer version is greater than another.\n *\n * @param version1 - The left-hand version.\n * @param version2 - The right-hand version.\n * @returns `version1 > version2`.\n */\nfunction gtVersion(version1, version2) {\n    return (0, semver_1.gt)(version1, version2);\n}\nexports.gtVersion = gtVersion;\n/**\n * Checks whether a SemVer version is greater than all possibilities in a range.\n *\n * @param version - A SemvVer version.\n * @param range - The range to check against.\n * @returns `version > range`.\n */\nfunction gtRange(version, range) {\n    return (0, semver_1.gtr)(version, range);\n}\nexports.gtRange = gtRange;\n/**\n * Returns whether a SemVer version satisfies a SemVer range.\n *\n * @param version - The SemVer version to check.\n * @param versionRange - The SemVer version range to check against.\n * @returns Whether the version satisfied the version range.\n */\nfunction satisfiesVersionRange(version, versionRange) {\n    return (0, semver_1.satisfies)(version, versionRange, {\n        includePrerelease: true,\n    });\n}\nexports.satisfiesVersionRange = satisfiesVersionRange;\n//# sourceMappingURL=versions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL0BtZXRhbWFzay91dGlscy9kaXN0L3ZlcnNpb25zLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDZCQUE2QixHQUFHLGVBQWUsR0FBRyxpQkFBaUIsR0FBRywyQkFBMkIsR0FBRyw2QkFBNkIsR0FBRywwQkFBMEIsR0FBRyw0QkFBNEIsR0FBRywwQkFBMEIsR0FBRyxxQkFBcUI7QUFDbFAsaUJBQWlCLG1CQUFPLENBQUMsb0RBQVE7QUFDakMsc0JBQXNCLG1CQUFPLENBQUMsb0VBQWE7QUFDM0MsaUJBQWlCLG1CQUFPLENBQUMsb0dBQVU7QUFDbkM7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0EsZ0RBQWdELE1BQU07QUFDdEQ7QUFDQTtBQUNBLENBQUM7QUFDRCwwQkFBMEI7QUFDMUI7QUFDQSw4Q0FBOEMsTUFBTTtBQUNwRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QjtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsNkJBQTZCO0FBQzdCIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9AbWV0YW1hc2svdXRpbHMvZGlzdC92ZXJzaW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuc2F0aXNmaWVzVmVyc2lvblJhbmdlID0gZXhwb3J0cy5ndFJhbmdlID0gZXhwb3J0cy5ndFZlcnNpb24gPSBleHBvcnRzLmFzc2VydElzU2VtVmVyUmFuZ2UgPSBleHBvcnRzLmFzc2VydElzU2VtVmVyVmVyc2lvbiA9IGV4cG9ydHMuaXNWYWxpZFNlbVZlclJhbmdlID0gZXhwb3J0cy5pc1ZhbGlkU2VtVmVyVmVyc2lvbiA9IGV4cG9ydHMuVmVyc2lvblJhbmdlU3RydWN0ID0gZXhwb3J0cy5WZXJzaW9uU3RydWN0ID0gdm9pZCAwO1xuY29uc3Qgc2VtdmVyXzEgPSByZXF1aXJlKFwic2VtdmVyXCIpO1xuY29uc3Qgc3VwZXJzdHJ1Y3RfMSA9IHJlcXVpcmUoXCJzdXBlcnN0cnVjdFwiKTtcbmNvbnN0IGFzc2VydF8xID0gcmVxdWlyZShcIi4vYXNzZXJ0XCIpO1xuLyoqXG4gKiBBIHN0cnVjdCBmb3IgdmFsaWRhdGluZyBhIHZlcnNpb24gc3RyaW5nLlxuICovXG5leHBvcnRzLlZlcnNpb25TdHJ1Y3QgPSAoMCwgc3VwZXJzdHJ1Y3RfMS5yZWZpbmUpKCgwLCBzdXBlcnN0cnVjdF8xLnN0cmluZykoKSwgJ1ZlcnNpb24nLCAodmFsdWUpID0+IHtcbiAgICBpZiAoKDAsIHNlbXZlcl8xLnZhbGlkKSh2YWx1ZSkgPT09IG51bGwpIHtcbiAgICAgICAgcmV0dXJuIGBFeHBlY3RlZCBTZW1WZXIgdmVyc2lvbiwgZ290IFwiJHt2YWx1ZX1cImA7XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xufSk7XG5leHBvcnRzLlZlcnNpb25SYW5nZVN0cnVjdCA9ICgwLCBzdXBlcnN0cnVjdF8xLnJlZmluZSkoKDAsIHN1cGVyc3RydWN0XzEuc3RyaW5nKSgpLCAnVmVyc2lvbiByYW5nZScsICh2YWx1ZSkgPT4ge1xuICAgIGlmICgoMCwgc2VtdmVyXzEudmFsaWRSYW5nZSkodmFsdWUpID09PSBudWxsKSB7XG4gICAgICAgIHJldHVybiBgRXhwZWN0ZWQgU2VtVmVyIHJhbmdlLCBnb3QgXCIke3ZhbHVlfVwiYDtcbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG59KTtcbi8qKlxuICogQ2hlY2tzIHdoZXRoZXIgYSBTZW1WZXIgdmVyc2lvbiBpcyB2YWxpZC5cbiAqXG4gKiBAcGFyYW0gdmVyc2lvbiAtIEEgcG90ZW50aWFsIHZlcnNpb24uXG4gKiBAcmV0dXJucyBgdHJ1ZWAgaWYgdGhlIHZlcnNpb24gaXMgdmFsaWQsIGFuZCBgZmFsc2VgIG90aGVyd2lzZS5cbiAqL1xuZnVuY3Rpb24gaXNWYWxpZFNlbVZlclZlcnNpb24odmVyc2lvbikge1xuICAgIHJldHVybiAoMCwgc3VwZXJzdHJ1Y3RfMS5pcykodmVyc2lvbiwgZXhwb3J0cy5WZXJzaW9uU3RydWN0KTtcbn1cbmV4cG9ydHMuaXNWYWxpZFNlbVZlclZlcnNpb24gPSBpc1ZhbGlkU2VtVmVyVmVyc2lvbjtcbi8qKlxuICogQ2hlY2tzIHdoZXRoZXIgYSBTZW1WZXIgdmVyc2lvbiByYW5nZSBpcyB2YWxpZC5cbiAqXG4gKiBAcGFyYW0gdmVyc2lvblJhbmdlIC0gQSBwb3RlbnRpYWwgdmVyc2lvbiByYW5nZS5cbiAqIEByZXR1cm5zIGB0cnVlYCBpZiB0aGUgdmVyc2lvbiByYW5nZSBpcyB2YWxpZCwgYW5kIGBmYWxzZWAgb3RoZXJ3aXNlLlxuICovXG5mdW5jdGlvbiBpc1ZhbGlkU2VtVmVyUmFuZ2UodmVyc2lvblJhbmdlKSB7XG4gICAgcmV0dXJuICgwLCBzdXBlcnN0cnVjdF8xLmlzKSh2ZXJzaW9uUmFuZ2UsIGV4cG9ydHMuVmVyc2lvblJhbmdlU3RydWN0KTtcbn1cbmV4cG9ydHMuaXNWYWxpZFNlbVZlclJhbmdlID0gaXNWYWxpZFNlbVZlclJhbmdlO1xuLyoqXG4gKiBBc3NlcnRzIHRoYXQgYSB2YWx1ZSBpcyBhIHZhbGlkIGNvbmNyZXRlIFNlbVZlciB2ZXJzaW9uLlxuICpcbiAqIEBwYXJhbSB2ZXJzaW9uIC0gQSBwb3RlbnRpYWwgU2VtVmVyIGNvbmNyZXRlIHZlcnNpb24uXG4gKi9cbmZ1bmN0aW9uIGFzc2VydElzU2VtVmVyVmVyc2lvbih2ZXJzaW9uKSB7XG4gICAgKDAsIGFzc2VydF8xLmFzc2VydFN0cnVjdCkodmVyc2lvbiwgZXhwb3J0cy5WZXJzaW9uU3RydWN0KTtcbn1cbmV4cG9ydHMuYXNzZXJ0SXNTZW1WZXJWZXJzaW9uID0gYXNzZXJ0SXNTZW1WZXJWZXJzaW9uO1xuLyoqXG4gKiBBc3NlcnRzIHRoYXQgYSB2YWx1ZSBpcyBhIHZhbGlkIFNlbVZlciByYW5nZS5cbiAqXG4gKiBAcGFyYW0gcmFuZ2UgLSBBIHBvdGVudGlhbCBTZW1WZXIgcmFuZ2UuXG4gKi9cbmZ1bmN0aW9uIGFzc2VydElzU2VtVmVyUmFuZ2UocmFuZ2UpIHtcbiAgICAoMCwgYXNzZXJ0XzEuYXNzZXJ0U3RydWN0KShyYW5nZSwgZXhwb3J0cy5WZXJzaW9uUmFuZ2VTdHJ1Y3QpO1xufVxuZXhwb3J0cy5hc3NlcnRJc1NlbVZlclJhbmdlID0gYXNzZXJ0SXNTZW1WZXJSYW5nZTtcbi8qKlxuICogQ2hlY2tzIHdoZXRoZXIgYSBTZW1WZXIgdmVyc2lvbiBpcyBncmVhdGVyIHRoYW4gYW5vdGhlci5cbiAqXG4gKiBAcGFyYW0gdmVyc2lvbjEgLSBUaGUgbGVmdC1oYW5kIHZlcnNpb24uXG4gKiBAcGFyYW0gdmVyc2lvbjIgLSBUaGUgcmlnaHQtaGFuZCB2ZXJzaW9uLlxuICogQHJldHVybnMgYHZlcnNpb24xID4gdmVyc2lvbjJgLlxuICovXG5mdW5jdGlvbiBndFZlcnNpb24odmVyc2lvbjEsIHZlcnNpb24yKSB7XG4gICAgcmV0dXJuICgwLCBzZW12ZXJfMS5ndCkodmVyc2lvbjEsIHZlcnNpb24yKTtcbn1cbmV4cG9ydHMuZ3RWZXJzaW9uID0gZ3RWZXJzaW9uO1xuLyoqXG4gKiBDaGVja3Mgd2hldGhlciBhIFNlbVZlciB2ZXJzaW9uIGlzIGdyZWF0ZXIgdGhhbiBhbGwgcG9zc2liaWxpdGllcyBpbiBhIHJhbmdlLlxuICpcbiAqIEBwYXJhbSB2ZXJzaW9uIC0gQSBTZW12VmVyIHZlcnNpb24uXG4gKiBAcGFyYW0gcmFuZ2UgLSBUaGUgcmFuZ2UgdG8gY2hlY2sgYWdhaW5zdC5cbiAqIEByZXR1cm5zIGB2ZXJzaW9uID4gcmFuZ2VgLlxuICovXG5mdW5jdGlvbiBndFJhbmdlKHZlcnNpb24sIHJhbmdlKSB7XG4gICAgcmV0dXJuICgwLCBzZW12ZXJfMS5ndHIpKHZlcnNpb24sIHJhbmdlKTtcbn1cbmV4cG9ydHMuZ3RSYW5nZSA9IGd0UmFuZ2U7XG4vKipcbiAqIFJldHVybnMgd2hldGhlciBhIFNlbVZlciB2ZXJzaW9uIHNhdGlzZmllcyBhIFNlbVZlciByYW5nZS5cbiAqXG4gKiBAcGFyYW0gdmVyc2lvbiAtIFRoZSBTZW1WZXIgdmVyc2lvbiB0byBjaGVjay5cbiAqIEBwYXJhbSB2ZXJzaW9uUmFuZ2UgLSBUaGUgU2VtVmVyIHZlcnNpb24gcmFuZ2UgdG8gY2hlY2sgYWdhaW5zdC5cbiAqIEByZXR1cm5zIFdoZXRoZXIgdGhlIHZlcnNpb24gc2F0aXNmaWVkIHRoZSB2ZXJzaW9uIHJhbmdlLlxuICovXG5mdW5jdGlvbiBzYXRpc2ZpZXNWZXJzaW9uUmFuZ2UodmVyc2lvbiwgdmVyc2lvblJhbmdlKSB7XG4gICAgcmV0dXJuICgwLCBzZW12ZXJfMS5zYXRpc2ZpZXMpKHZlcnNpb24sIHZlcnNpb25SYW5nZSwge1xuICAgICAgICBpbmNsdWRlUHJlcmVsZWFzZTogdHJ1ZSxcbiAgICB9KTtcbn1cbmV4cG9ydHMuc2F0aXNmaWVzVmVyc2lvblJhbmdlID0gc2F0aXNmaWVzVmVyc2lvblJhbmdlO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/versions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-block-tracker/node_modules/pify/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/eth-block-tracker/node_modules/pify/index.js ***!
  \*******************************************************************/
/***/ ((module) => {

eval("\n\nconst processFn = (fn, opts) => function () {\n\tconst P = opts.promiseModule;\n\tconst args = new Array(arguments.length);\n\n\tfor (let i = 0; i < arguments.length; i++) {\n\t\targs[i] = arguments[i];\n\t}\n\n\treturn new P((resolve, reject) => {\n\t\tif (opts.errorFirst) {\n\t\t\targs.push(function (err, result) {\n\t\t\t\tif (opts.multiArgs) {\n\t\t\t\t\tconst results = new Array(arguments.length - 1);\n\n\t\t\t\t\tfor (let i = 1; i < arguments.length; i++) {\n\t\t\t\t\t\tresults[i - 1] = arguments[i];\n\t\t\t\t\t}\n\n\t\t\t\t\tif (err) {\n\t\t\t\t\t\tresults.unshift(err);\n\t\t\t\t\t\treject(results);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresolve(results);\n\t\t\t\t\t}\n\t\t\t\t} else if (err) {\n\t\t\t\t\treject(err);\n\t\t\t\t} else {\n\t\t\t\t\tresolve(result);\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\targs.push(function (result) {\n\t\t\t\tif (opts.multiArgs) {\n\t\t\t\t\tconst results = new Array(arguments.length - 1);\n\n\t\t\t\t\tfor (let i = 0; i < arguments.length; i++) {\n\t\t\t\t\t\tresults[i] = arguments[i];\n\t\t\t\t\t}\n\n\t\t\t\t\tresolve(results);\n\t\t\t\t} else {\n\t\t\t\t\tresolve(result);\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\tfn.apply(this, args);\n\t});\n};\n\nmodule.exports = (obj, opts) => {\n\topts = Object.assign({\n\t\texclude: [/.+(Sync|Stream)$/],\n\t\terrorFirst: true,\n\t\tpromiseModule: Promise\n\t}, opts);\n\n\tconst filter = key => {\n\t\tconst match = pattern => typeof pattern === 'string' ? key === pattern : pattern.test(key);\n\t\treturn opts.include ? opts.include.some(match) : !opts.exclude.some(match);\n\t};\n\n\tlet ret;\n\tif (typeof obj === 'function') {\n\t\tret = function () {\n\t\t\tif (opts.excludeMain) {\n\t\t\t\treturn obj.apply(this, arguments);\n\t\t\t}\n\n\t\t\treturn processFn(obj, opts).apply(this, arguments);\n\t\t};\n\t} else {\n\t\tret = Object.create(Object.getPrototypeOf(obj));\n\t}\n\n\tfor (const key in obj) { // eslint-disable-line guard-for-in\n\t\tconst x = obj[key];\n\t\tret[key] = typeof x === 'function' && filter(key) ? processFn(x, opts) : x;\n\t}\n\n\treturn ret;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-block-tracker/node_modules/pify/index.js\n");

/***/ })

};
;