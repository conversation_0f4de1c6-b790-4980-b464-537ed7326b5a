"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/viem";
exports.ids = ["vendor-chunks/viem"];
exports.modules = {

/***/ "(ssr)/./node_modules/viem/_esm/accounts/utils/parseAccount.js":
/*!***************************************************************!*\
  !*** ./node_modules/viem/_esm/accounts/utils/parseAccount.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseAccount: () => (/* binding */ parseAccount)\n/* harmony export */ });\nfunction parseAccount(account) {\n    if (typeof account === 'string')\n        return { address: account, type: 'json-rpc' };\n    return account;\n}\n//# sourceMappingURL=parseAccount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2FjY291bnRzL3V0aWxzL3BhcnNlQWNjb3VudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2FjY291bnRzL3V0aWxzL3BhcnNlQWNjb3VudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gcGFyc2VBY2NvdW50KGFjY291bnQpIHtcbiAgICBpZiAodHlwZW9mIGFjY291bnQgPT09ICdzdHJpbmcnKVxuICAgICAgICByZXR1cm4geyBhZGRyZXNzOiBhY2NvdW50LCB0eXBlOiAnanNvbi1ycGMnIH07XG4gICAgcmV0dXJuIGFjY291bnQ7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYXJzZUFjY291bnQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/accounts/utils/parseAccount.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/chains/definitions/foundry.js":
/*!**************************************************************!*\
  !*** ./node_modules/viem/_esm/chains/definitions/foundry.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   foundry: () => (/* binding */ foundry)\n/* harmony export */ });\n/* harmony import */ var _utils_chain_defineChain_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/chain/defineChain.js */ \"(ssr)/./node_modules/viem/_esm/utils/chain/defineChain.js\");\n\nconst foundry = /*#__PURE__*/ (0,_utils_chain_defineChain_js__WEBPACK_IMPORTED_MODULE_0__.defineChain)({\n    id: 31_337,\n    name: 'Foundry',\n    nativeCurrency: {\n        decimals: 18,\n        name: 'Ether',\n        symbol: 'ETH',\n    },\n    rpcUrls: {\n        default: {\n            http: ['http://127.0.0.1:8545'],\n            webSocket: ['ws://127.0.0.1:8545'],\n        },\n    },\n});\n//# sourceMappingURL=foundry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2NoYWlucy9kZWZpbml0aW9ucy9mb3VuZHJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStEO0FBQ3hELDhCQUE4Qix3RUFBVztBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMLENBQUM7QUFDRCIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy92aWVtL19lc20vY2hhaW5zL2RlZmluaXRpb25zL2ZvdW5kcnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGVmaW5lQ2hhaW4gfSBmcm9tICcuLi8uLi91dGlscy9jaGFpbi9kZWZpbmVDaGFpbi5qcyc7XG5leHBvcnQgY29uc3QgZm91bmRyeSA9IC8qI19fUFVSRV9fKi8gZGVmaW5lQ2hhaW4oe1xuICAgIGlkOiAzMV8zMzcsXG4gICAgbmFtZTogJ0ZvdW5kcnknLFxuICAgIG5hdGl2ZUN1cnJlbmN5OiB7XG4gICAgICAgIGRlY2ltYWxzOiAxOCxcbiAgICAgICAgbmFtZTogJ0V0aGVyJyxcbiAgICAgICAgc3ltYm9sOiAnRVRIJyxcbiAgICB9LFxuICAgIHJwY1VybHM6IHtcbiAgICAgICAgZGVmYXVsdDoge1xuICAgICAgICAgICAgaHR0cDogWydodHRwOi8vMTI3LjAuMC4xOjg1NDUnXSxcbiAgICAgICAgICAgIHdlYlNvY2tldDogWyd3czovLzEyNy4wLjAuMTo4NTQ1J10sXG4gICAgICAgIH0sXG4gICAgfSxcbn0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Zm91bmRyeS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/chains/definitions/foundry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/chains/definitions/mainnet.js":
/*!**************************************************************!*\
  !*** ./node_modules/viem/_esm/chains/definitions/mainnet.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mainnet: () => (/* binding */ mainnet)\n/* harmony export */ });\n/* harmony import */ var _utils_chain_defineChain_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/chain/defineChain.js */ \"(ssr)/./node_modules/viem/_esm/utils/chain/defineChain.js\");\n\nconst mainnet = /*#__PURE__*/ (0,_utils_chain_defineChain_js__WEBPACK_IMPORTED_MODULE_0__.defineChain)({\n    id: 1,\n    name: 'Ethereum',\n    nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },\n    rpcUrls: {\n        default: {\n            http: ['https://eth.merkle.io'],\n        },\n    },\n    blockExplorers: {\n        default: {\n            name: 'Etherscan',\n            url: 'https://etherscan.io',\n            apiUrl: 'https://api.etherscan.io/api',\n        },\n    },\n    contracts: {\n        ensRegistry: {\n            address: '******************************************',\n        },\n        ensUniversalResolver: {\n            address: '******************************************',\n            blockCreated: 19_258_213,\n        },\n        multicall3: {\n            address: '******************************************',\n            blockCreated: 14_353_601,\n        },\n    },\n});\n//# sourceMappingURL=mainnet.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/chains/definitions/mainnet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/chains/definitions/sepolia.js":
/*!**************************************************************!*\
  !*** ./node_modules/viem/_esm/chains/definitions/sepolia.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sepolia: () => (/* binding */ sepolia)\n/* harmony export */ });\n/* harmony import */ var _utils_chain_defineChain_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/chain/defineChain.js */ \"(ssr)/./node_modules/viem/_esm/utils/chain/defineChain.js\");\n\nconst sepolia = /*#__PURE__*/ (0,_utils_chain_defineChain_js__WEBPACK_IMPORTED_MODULE_0__.defineChain)({\n    id: 11_155_111,\n    name: 'Sepolia',\n    nativeCurrency: { name: 'Sepolia Ether', symbol: 'ETH', decimals: 18 },\n    rpcUrls: {\n        default: {\n            http: ['https://sepolia.drpc.org'],\n        },\n    },\n    blockExplorers: {\n        default: {\n            name: 'Etherscan',\n            url: 'https://sepolia.etherscan.io',\n            apiUrl: 'https://api-sepolia.etherscan.io/api',\n        },\n    },\n    contracts: {\n        multicall3: {\n            address: '******************************************',\n            blockCreated: 751532,\n        },\n        ensRegistry: { address: '******************************************' },\n        ensUniversalResolver: {\n            address: '******************************************',\n            blockCreated: 5_317_080,\n        },\n    },\n    testnet: true,\n});\n//# sourceMappingURL=sepolia.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/chains/definitions/sepolia.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/clients/createClient.js":
/*!********************************************************!*\
  !*** ./node_modules/viem/_esm/clients/createClient.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   rpcSchema: () => (/* binding */ rpcSchema)\n/* harmony export */ });\n/* harmony import */ var _accounts_utils_parseAccount_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../accounts/utils/parseAccount.js */ \"(ssr)/./node_modules/viem/_esm/accounts/utils/parseAccount.js\");\n/* harmony import */ var _utils_uid_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/uid.js */ \"(ssr)/./node_modules/viem/_esm/utils/uid.js\");\n\n\nfunction createClient(parameters) {\n    const { batch, cacheTime = parameters.pollingInterval ?? 4_000, ccipRead, key = 'base', name = 'Base Client', pollingInterval = 4_000, type = 'base', } = parameters;\n    const chain = parameters.chain;\n    const account = parameters.account\n        ? (0,_accounts_utils_parseAccount_js__WEBPACK_IMPORTED_MODULE_0__.parseAccount)(parameters.account)\n        : undefined;\n    const { config, request, value } = parameters.transport({\n        chain,\n        pollingInterval,\n    });\n    const transport = { ...config, ...value };\n    const client = {\n        account,\n        batch,\n        cacheTime,\n        ccipRead,\n        chain,\n        key,\n        name,\n        pollingInterval,\n        request,\n        transport,\n        type,\n        uid: (0,_utils_uid_js__WEBPACK_IMPORTED_MODULE_1__.uid)(),\n    };\n    function extend(base) {\n        return (extendFn) => {\n            const extended = extendFn(base);\n            for (const key in client)\n                delete extended[key];\n            const combined = { ...base, ...extended };\n            return Object.assign(combined, { extend: extend(combined) });\n        };\n    }\n    return Object.assign(client, { extend: extend(client) });\n}\n/**\n * Defines a typed JSON-RPC schema for the client.\n * Note: This is a runtime noop function.\n */\nfunction rpcSchema() {\n    return null;\n}\n//# sourceMappingURL=createClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/clients/createClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/clients/transports/createTransport.js":
/*!**********************************************************************!*\
  !*** ./node_modules/viem/_esm/clients/transports/createTransport.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTransport: () => (/* binding */ createTransport)\n/* harmony export */ });\n/* harmony import */ var _utils_buildRequest_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/buildRequest.js */ \"(ssr)/./node_modules/viem/_esm/utils/buildRequest.js\");\n/* harmony import */ var _utils_uid_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/uid.js */ \"(ssr)/./node_modules/viem/_esm/utils/uid.js\");\n\n\n/**\n * @description Creates an transport intended to be used with a client.\n */\nfunction createTransport({ key, methods, name, request, retryCount = 3, retryDelay = 150, timeout, type, }, value) {\n    const uid = (0,_utils_uid_js__WEBPACK_IMPORTED_MODULE_0__.uid)();\n    return {\n        config: {\n            key,\n            methods,\n            name,\n            request,\n            retryCount,\n            retryDelay,\n            timeout,\n            type,\n        },\n        request: (0,_utils_buildRequest_js__WEBPACK_IMPORTED_MODULE_1__.buildRequest)(request, { methods, retryCount, retryDelay, uid }),\n        value,\n    };\n}\n//# sourceMappingURL=createTransport.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2NsaWVudHMvdHJhbnNwb3J0cy9jcmVhdGVUcmFuc3BvcnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTJEO0FBQ1Y7QUFDakQ7QUFDQTtBQUNBO0FBQ08sMkJBQTJCLCtFQUErRTtBQUNqSCxnQkFBZ0Isa0RBQUk7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsaUJBQWlCLG9FQUFZLFlBQVksc0NBQXNDO0FBQy9FO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3ZpZW0vX2VzbS9jbGllbnRzL3RyYW5zcG9ydHMvY3JlYXRlVHJhbnNwb3J0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGJ1aWxkUmVxdWVzdCB9IGZyb20gJy4uLy4uL3V0aWxzL2J1aWxkUmVxdWVzdC5qcyc7XG5pbXBvcnQgeyB1aWQgYXMgdWlkXyB9IGZyb20gJy4uLy4uL3V0aWxzL3VpZC5qcyc7XG4vKipcbiAqIEBkZXNjcmlwdGlvbiBDcmVhdGVzIGFuIHRyYW5zcG9ydCBpbnRlbmRlZCB0byBiZSB1c2VkIHdpdGggYSBjbGllbnQuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVUcmFuc3BvcnQoeyBrZXksIG1ldGhvZHMsIG5hbWUsIHJlcXVlc3QsIHJldHJ5Q291bnQgPSAzLCByZXRyeURlbGF5ID0gMTUwLCB0aW1lb3V0LCB0eXBlLCB9LCB2YWx1ZSkge1xuICAgIGNvbnN0IHVpZCA9IHVpZF8oKTtcbiAgICByZXR1cm4ge1xuICAgICAgICBjb25maWc6IHtcbiAgICAgICAgICAgIGtleSxcbiAgICAgICAgICAgIG1ldGhvZHMsXG4gICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgcmVxdWVzdCxcbiAgICAgICAgICAgIHJldHJ5Q291bnQsXG4gICAgICAgICAgICByZXRyeURlbGF5LFxuICAgICAgICAgICAgdGltZW91dCxcbiAgICAgICAgICAgIHR5cGUsXG4gICAgICAgIH0sXG4gICAgICAgIHJlcXVlc3Q6IGJ1aWxkUmVxdWVzdChyZXF1ZXN0LCB7IG1ldGhvZHMsIHJldHJ5Q291bnQsIHJldHJ5RGVsYXksIHVpZCB9KSxcbiAgICAgICAgdmFsdWUsXG4gICAgfTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNyZWF0ZVRyYW5zcG9ydC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/clients/transports/createTransport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/clients/transports/http.js":
/*!***********************************************************!*\
  !*** ./node_modules/viem/_esm/clients/transports/http.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   http: () => (/* binding */ http)\n/* harmony export */ });\n/* harmony import */ var _errors_request_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../errors/request.js */ \"(ssr)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var _errors_transport_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors/transport.js */ \"(ssr)/./node_modules/viem/_esm/errors/transport.js\");\n/* harmony import */ var _utils_promise_createBatchScheduler_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/promise/createBatchScheduler.js */ \"(ssr)/./node_modules/viem/_esm/utils/promise/createBatchScheduler.js\");\n/* harmony import */ var _utils_rpc_http_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/rpc/http.js */ \"(ssr)/./node_modules/viem/_esm/utils/rpc/http.js\");\n/* harmony import */ var _createTransport_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createTransport.js */ \"(ssr)/./node_modules/viem/_esm/clients/transports/createTransport.js\");\n\n\n\n\n\n/**\n * @description Creates a HTTP transport that connects to a JSON-RPC API.\n */\nfunction http(\n/** URL of the JSON-RPC API. Defaults to the chain's public RPC URL. */\nurl, config = {}) {\n    const { batch, fetchOptions, key = 'http', methods, name = 'HTTP JSON-RPC', onFetchRequest, onFetchResponse, retryDelay, raw, } = config;\n    return ({ chain, retryCount: retryCount_, timeout: timeout_ }) => {\n        const { batchSize = 1000, wait = 0 } = typeof batch === 'object' ? batch : {};\n        const retryCount = config.retryCount ?? retryCount_;\n        const timeout = timeout_ ?? config.timeout ?? 10_000;\n        const url_ = url || chain?.rpcUrls.default.http[0];\n        if (!url_)\n            throw new _errors_transport_js__WEBPACK_IMPORTED_MODULE_0__.UrlRequiredError();\n        const rpcClient = (0,_utils_rpc_http_js__WEBPACK_IMPORTED_MODULE_1__.getHttpRpcClient)(url_, {\n            fetchOptions,\n            onRequest: onFetchRequest,\n            onResponse: onFetchResponse,\n            timeout,\n        });\n        return (0,_createTransport_js__WEBPACK_IMPORTED_MODULE_2__.createTransport)({\n            key,\n            methods,\n            name,\n            async request({ method, params }) {\n                const body = { method, params };\n                const { schedule } = (0,_utils_promise_createBatchScheduler_js__WEBPACK_IMPORTED_MODULE_3__.createBatchScheduler)({\n                    id: url_,\n                    wait,\n                    shouldSplitBatch(requests) {\n                        return requests.length > batchSize;\n                    },\n                    fn: (body) => rpcClient.request({\n                        body,\n                    }),\n                    sort: (a, b) => a.id - b.id,\n                });\n                const fn = async (body) => batch\n                    ? schedule(body)\n                    : [\n                        await rpcClient.request({\n                            body,\n                        }),\n                    ];\n                const [{ error, result }] = await fn(body);\n                if (raw)\n                    return { error, result };\n                if (error)\n                    throw new _errors_request_js__WEBPACK_IMPORTED_MODULE_4__.RpcRequestError({\n                        body,\n                        error,\n                        url: url_,\n                    });\n                return result;\n            },\n            retryCount,\n            retryDelay,\n            timeout,\n            type: 'http',\n        }, {\n            fetchOptions,\n            url: url_,\n        });\n    };\n}\n//# sourceMappingURL=http.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/clients/transports/http.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/address.js":
/*!**************************************************!*\
  !*** ./node_modules/viem/_esm/errors/address.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidAddressError: () => (/* binding */ InvalidAddressError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/viem/_esm/errors/base.js\");\n\nclass InvalidAddressError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ address }) {\n        super(`Address \"${address}\" is invalid.`, {\n            metaMessages: [\n                '- Address must be a hex value of 20 bytes (40 hex characters).',\n                '- Address must match its checksum counterpart.',\n            ],\n            name: 'InvalidAddressError',\n        });\n    }\n}\n//# sourceMappingURL=address.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2Vycm9ycy9hZGRyZXNzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDO0FBQy9CLGtDQUFrQywrQ0FBUztBQUNsRCxrQkFBa0IsU0FBUztBQUMzQiwwQkFBMEIsUUFBUTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy92aWVtL19lc20vZXJyb3JzL2FkZHJlc3MuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZUVycm9yIH0gZnJvbSAnLi9iYXNlLmpzJztcbmV4cG9ydCBjbGFzcyBJbnZhbGlkQWRkcmVzc0Vycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcih7IGFkZHJlc3MgfSkge1xuICAgICAgICBzdXBlcihgQWRkcmVzcyBcIiR7YWRkcmVzc31cIiBpcyBpbnZhbGlkLmAsIHtcbiAgICAgICAgICAgIG1ldGFNZXNzYWdlczogW1xuICAgICAgICAgICAgICAgICctIEFkZHJlc3MgbXVzdCBiZSBhIGhleCB2YWx1ZSBvZiAyMCBieXRlcyAoNDAgaGV4IGNoYXJhY3RlcnMpLicsXG4gICAgICAgICAgICAgICAgJy0gQWRkcmVzcyBtdXN0IG1hdGNoIGl0cyBjaGVja3N1bSBjb3VudGVycGFydC4nLFxuICAgICAgICAgICAgXSxcbiAgICAgICAgICAgIG5hbWU6ICdJbnZhbGlkQWRkcmVzc0Vycm9yJyxcbiAgICAgICAgfSk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YWRkcmVzcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/address.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/base.js":
/*!***********************************************!*\
  !*** ./node_modules/viem/_esm/errors/base.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseError: () => (/* binding */ BaseError),\n/* harmony export */   setErrorConfig: () => (/* binding */ setErrorConfig)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version.js */ \"(ssr)/./node_modules/viem/_esm/errors/version.js\");\n\nlet errorConfig = {\n    getDocsUrl: ({ docsBaseUrl, docsPath = '', docsSlug, }) => docsPath\n        ? `${docsBaseUrl ?? 'https://viem.sh'}${docsPath}${docsSlug ? `#${docsSlug}` : ''}`\n        : undefined,\n    version: `viem@${_version_js__WEBPACK_IMPORTED_MODULE_0__.version}`,\n};\nfunction setErrorConfig(config) {\n    errorConfig = config;\n}\nclass BaseError extends Error {\n    constructor(shortMessage, args = {}) {\n        const details = (() => {\n            if (args.cause instanceof BaseError)\n                return args.cause.details;\n            if (args.cause?.message)\n                return args.cause.message;\n            return args.details;\n        })();\n        const docsPath = (() => {\n            if (args.cause instanceof BaseError)\n                return args.cause.docsPath || args.docsPath;\n            return args.docsPath;\n        })();\n        const docsUrl = errorConfig.getDocsUrl?.({ ...args, docsPath });\n        const message = [\n            shortMessage || 'An error occurred.',\n            '',\n            ...(args.metaMessages ? [...args.metaMessages, ''] : []),\n            ...(docsUrl ? [`Docs: ${docsUrl}`] : []),\n            ...(details ? [`Details: ${details}`] : []),\n            ...(errorConfig.version ? [`Version: ${errorConfig.version}`] : []),\n        ].join('\\n');\n        super(message, args.cause ? { cause: args.cause } : undefined);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"metaMessages\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"version\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'BaseError'\n        });\n        this.details = details;\n        this.docsPath = docsPath;\n        this.metaMessages = args.metaMessages;\n        this.name = args.name ?? this.name;\n        this.shortMessage = shortMessage;\n        this.version = _version_js__WEBPACK_IMPORTED_MODULE_0__.version;\n    }\n    walk(fn) {\n        return walk(this, fn);\n    }\n}\nfunction walk(err, fn) {\n    if (fn?.(err))\n        return err;\n    if (err &&\n        typeof err === 'object' &&\n        'cause' in err &&\n        err.cause !== undefined)\n        return walk(err.cause, fn);\n    return fn ? null : err;\n}\n//# sourceMappingURL=base.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/data.js":
/*!***********************************************!*\
  !*** ./node_modules/viem/_esm/errors/data.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidBytesLengthError: () => (/* binding */ InvalidBytesLengthError),\n/* harmony export */   SizeExceedsPaddingSizeError: () => (/* binding */ SizeExceedsPaddingSizeError),\n/* harmony export */   SliceOffsetOutOfBoundsError: () => (/* binding */ SliceOffsetOutOfBoundsError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/viem/_esm/errors/base.js\");\n\nclass SliceOffsetOutOfBoundsError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ offset, position, size, }) {\n        super(`Slice ${position === 'start' ? 'starting' : 'ending'} at offset \"${offset}\" is out-of-bounds (size: ${size}).`, { name: 'SliceOffsetOutOfBoundsError' });\n    }\n}\nclass SizeExceedsPaddingSizeError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ size, targetSize, type, }) {\n        super(`${type.charAt(0).toUpperCase()}${type\n            .slice(1)\n            .toLowerCase()} size (${size}) exceeds padding size (${targetSize}).`, { name: 'SizeExceedsPaddingSizeError' });\n    }\n}\nclass InvalidBytesLengthError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ size, targetSize, type, }) {\n        super(`${type.charAt(0).toUpperCase()}${type\n            .slice(1)\n            .toLowerCase()} is expected to be ${targetSize} ${type} long, but is ${size} ${type} long.`, { name: 'InvalidBytesLengthError' });\n    }\n}\n//# sourceMappingURL=data.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/data.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/encoding.js":
/*!***************************************************!*\
  !*** ./node_modules/viem/_esm/errors/encoding.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntegerOutOfRangeError: () => (/* binding */ IntegerOutOfRangeError),\n/* harmony export */   InvalidBytesBooleanError: () => (/* binding */ InvalidBytesBooleanError),\n/* harmony export */   InvalidHexBooleanError: () => (/* binding */ InvalidHexBooleanError),\n/* harmony export */   InvalidHexValueError: () => (/* binding */ InvalidHexValueError),\n/* harmony export */   SizeOverflowError: () => (/* binding */ SizeOverflowError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/viem/_esm/errors/base.js\");\n\nclass IntegerOutOfRangeError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ max, min, signed, size, value, }) {\n        super(`Number \"${value}\" is not in safe ${size ? `${size * 8}-bit ${signed ? 'signed' : 'unsigned'} ` : ''}integer range ${max ? `(${min} to ${max})` : `(above ${min})`}`, { name: 'IntegerOutOfRangeError' });\n    }\n}\nclass InvalidBytesBooleanError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor(bytes) {\n        super(`Bytes value \"${bytes}\" is not a valid boolean. The bytes array must contain a single byte of either a 0 or 1 value.`, {\n            name: 'InvalidBytesBooleanError',\n        });\n    }\n}\nclass InvalidHexBooleanError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor(hex) {\n        super(`Hex value \"${hex}\" is not a valid boolean. The hex value must be \"0x0\" (false) or \"0x1\" (true).`, { name: 'InvalidHexBooleanError' });\n    }\n}\nclass InvalidHexValueError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor(value) {\n        super(`Hex value \"${value}\" is an odd length (${value.length}). It must be an even length.`, { name: 'InvalidHexValueError' });\n    }\n}\nclass SizeOverflowError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ givenSize, maxSize }) {\n        super(`Size cannot exceed ${maxSize} bytes. Given size: ${givenSize} bytes.`, { name: 'SizeOverflowError' });\n    }\n}\n//# sourceMappingURL=encoding.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2Vycm9ycy9lbmNvZGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBc0M7QUFDL0IscUNBQXFDLCtDQUFTO0FBQ3JELGtCQUFrQixnQ0FBZ0M7QUFDbEQseUJBQXlCLE1BQU0sbUJBQW1CLFVBQVUsU0FBUyxPQUFPLGdDQUFnQyxPQUFPLGdCQUFnQixVQUFVLEtBQUssS0FBSyxJQUFJLGVBQWUsSUFBSSxHQUFHLEtBQUssZ0NBQWdDO0FBQ3ROO0FBQ0E7QUFDTyx1Q0FBdUMsK0NBQVM7QUFDdkQ7QUFDQSw4QkFBOEIsTUFBTTtBQUNwQztBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ08scUNBQXFDLCtDQUFTO0FBQ3JEO0FBQ0EsNEJBQTRCLElBQUksbUZBQW1GLGdDQUFnQztBQUNuSjtBQUNBO0FBQ08sbUNBQW1DLCtDQUFTO0FBQ25EO0FBQ0EsNEJBQTRCLE1BQU0sc0JBQXNCLGFBQWEsa0NBQWtDLDhCQUE4QjtBQUNySTtBQUNBO0FBQ08sZ0NBQWdDLCtDQUFTO0FBQ2hELGtCQUFrQixvQkFBb0I7QUFDdEMsb0NBQW9DLFNBQVMscUJBQXFCLFdBQVcsV0FBVywyQkFBMkI7QUFDbkg7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3ZpZW0vX2VzbS9lcnJvcnMvZW5jb2RpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZUVycm9yIH0gZnJvbSAnLi9iYXNlLmpzJztcbmV4cG9ydCBjbGFzcyBJbnRlZ2VyT3V0T2ZSYW5nZUVycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcih7IG1heCwgbWluLCBzaWduZWQsIHNpemUsIHZhbHVlLCB9KSB7XG4gICAgICAgIHN1cGVyKGBOdW1iZXIgXCIke3ZhbHVlfVwiIGlzIG5vdCBpbiBzYWZlICR7c2l6ZSA/IGAke3NpemUgKiA4fS1iaXQgJHtzaWduZWQgPyAnc2lnbmVkJyA6ICd1bnNpZ25lZCd9IGAgOiAnJ31pbnRlZ2VyIHJhbmdlICR7bWF4ID8gYCgke21pbn0gdG8gJHttYXh9KWAgOiBgKGFib3ZlICR7bWlufSlgfWAsIHsgbmFtZTogJ0ludGVnZXJPdXRPZlJhbmdlRXJyb3InIH0pO1xuICAgIH1cbn1cbmV4cG9ydCBjbGFzcyBJbnZhbGlkQnl0ZXNCb29sZWFuRXJyb3IgZXh0ZW5kcyBCYXNlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKGJ5dGVzKSB7XG4gICAgICAgIHN1cGVyKGBCeXRlcyB2YWx1ZSBcIiR7Ynl0ZXN9XCIgaXMgbm90IGEgdmFsaWQgYm9vbGVhbi4gVGhlIGJ5dGVzIGFycmF5IG11c3QgY29udGFpbiBhIHNpbmdsZSBieXRlIG9mIGVpdGhlciBhIDAgb3IgMSB2YWx1ZS5gLCB7XG4gICAgICAgICAgICBuYW1lOiAnSW52YWxpZEJ5dGVzQm9vbGVhbkVycm9yJyxcbiAgICAgICAgfSk7XG4gICAgfVxufVxuZXhwb3J0IGNsYXNzIEludmFsaWRIZXhCb29sZWFuRXJyb3IgZXh0ZW5kcyBCYXNlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKGhleCkge1xuICAgICAgICBzdXBlcihgSGV4IHZhbHVlIFwiJHtoZXh9XCIgaXMgbm90IGEgdmFsaWQgYm9vbGVhbi4gVGhlIGhleCB2YWx1ZSBtdXN0IGJlIFwiMHgwXCIgKGZhbHNlKSBvciBcIjB4MVwiICh0cnVlKS5gLCB7IG5hbWU6ICdJbnZhbGlkSGV4Qm9vbGVhbkVycm9yJyB9KTtcbiAgICB9XG59XG5leHBvcnQgY2xhc3MgSW52YWxpZEhleFZhbHVlRXJyb3IgZXh0ZW5kcyBCYXNlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKHZhbHVlKSB7XG4gICAgICAgIHN1cGVyKGBIZXggdmFsdWUgXCIke3ZhbHVlfVwiIGlzIGFuIG9kZCBsZW5ndGggKCR7dmFsdWUubGVuZ3RofSkuIEl0IG11c3QgYmUgYW4gZXZlbiBsZW5ndGguYCwgeyBuYW1lOiAnSW52YWxpZEhleFZhbHVlRXJyb3InIH0pO1xuICAgIH1cbn1cbmV4cG9ydCBjbGFzcyBTaXplT3ZlcmZsb3dFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoeyBnaXZlblNpemUsIG1heFNpemUgfSkge1xuICAgICAgICBzdXBlcihgU2l6ZSBjYW5ub3QgZXhjZWVkICR7bWF4U2l6ZX0gYnl0ZXMuIEdpdmVuIHNpemU6ICR7Z2l2ZW5TaXplfSBieXRlcy5gLCB7IG5hbWU6ICdTaXplT3ZlcmZsb3dFcnJvcicgfSk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZW5jb2RpbmcuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/encoding.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/request.js":
/*!**************************************************!*\
  !*** ./node_modules/viem/_esm/errors/request.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpRequestError: () => (/* binding */ HttpRequestError),\n/* harmony export */   RpcRequestError: () => (/* binding */ RpcRequestError),\n/* harmony export */   SocketClosedError: () => (/* binding */ SocketClosedError),\n/* harmony export */   TimeoutError: () => (/* binding */ TimeoutError),\n/* harmony export */   WebSocketRequestError: () => (/* binding */ WebSocketRequestError)\n/* harmony export */ });\n/* harmony import */ var _utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/stringify.js */ \"(ssr)/./node_modules/viem/_esm/utils/stringify.js\");\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/viem/_esm/errors/base.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/viem/_esm/errors/utils.js\");\n\n\n\nclass HttpRequestError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ body, cause, details, headers, status, url, }) {\n        super('HTTP request failed.', {\n            cause,\n            details,\n            metaMessages: [\n                status && `Status: ${status}`,\n                `URL: ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`,\n                body && `Request body: ${(0,_utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__.stringify)(body)}`,\n            ].filter(Boolean),\n            name: 'HttpRequestError',\n        });\n        Object.defineProperty(this, \"body\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"headers\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"status\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"url\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.body = body;\n        this.headers = headers;\n        this.status = status;\n        this.url = url;\n    }\n}\nclass WebSocketRequestError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ body, cause, details, url, }) {\n        super('WebSocket request failed.', {\n            cause,\n            details,\n            metaMessages: [\n                `URL: ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`,\n                body && `Request body: ${(0,_utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__.stringify)(body)}`,\n            ].filter(Boolean),\n            name: 'WebSocketRequestError',\n        });\n    }\n}\nclass RpcRequestError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ body, error, url, }) {\n        super('RPC Request failed.', {\n            cause: error,\n            details: error.message,\n            metaMessages: [`URL: ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`, `Request body: ${(0,_utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__.stringify)(body)}`],\n            name: 'RpcRequestError',\n        });\n        Object.defineProperty(this, \"code\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"data\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.code = error.code;\n        this.data = error.data;\n    }\n}\nclass SocketClosedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ url, } = {}) {\n        super('The socket has been closed.', {\n            metaMessages: [url && `URL: ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`].filter(Boolean),\n            name: 'SocketClosedError',\n        });\n    }\n}\nclass TimeoutError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ body, url, }) {\n        super('The request took too long to respond.', {\n            details: 'The request timed out.',\n            metaMessages: [`URL: ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getUrl)(url)}`, `Request body: ${(0,_utils_stringify_js__WEBPACK_IMPORTED_MODULE_2__.stringify)(body)}`],\n            name: 'TimeoutError',\n        });\n    }\n}\n//# sourceMappingURL=request.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/request.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/rpc.js":
/*!**********************************************!*\
  !*** ./node_modules/viem/_esm/errors/rpc.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AtomicReadyWalletRejectedUpgradeError: () => (/* binding */ AtomicReadyWalletRejectedUpgradeError),\n/* harmony export */   AtomicityNotSupportedError: () => (/* binding */ AtomicityNotSupportedError),\n/* harmony export */   BundleTooLargeError: () => (/* binding */ BundleTooLargeError),\n/* harmony export */   ChainDisconnectedError: () => (/* binding */ ChainDisconnectedError),\n/* harmony export */   DuplicateIdError: () => (/* binding */ DuplicateIdError),\n/* harmony export */   InternalRpcError: () => (/* binding */ InternalRpcError),\n/* harmony export */   InvalidInputRpcError: () => (/* binding */ InvalidInputRpcError),\n/* harmony export */   InvalidParamsRpcError: () => (/* binding */ InvalidParamsRpcError),\n/* harmony export */   InvalidRequestRpcError: () => (/* binding */ InvalidRequestRpcError),\n/* harmony export */   JsonRpcVersionUnsupportedError: () => (/* binding */ JsonRpcVersionUnsupportedError),\n/* harmony export */   LimitExceededRpcError: () => (/* binding */ LimitExceededRpcError),\n/* harmony export */   MethodNotFoundRpcError: () => (/* binding */ MethodNotFoundRpcError),\n/* harmony export */   MethodNotSupportedRpcError: () => (/* binding */ MethodNotSupportedRpcError),\n/* harmony export */   ParseRpcError: () => (/* binding */ ParseRpcError),\n/* harmony export */   ProviderDisconnectedError: () => (/* binding */ ProviderDisconnectedError),\n/* harmony export */   ProviderRpcError: () => (/* binding */ ProviderRpcError),\n/* harmony export */   ResourceNotFoundRpcError: () => (/* binding */ ResourceNotFoundRpcError),\n/* harmony export */   ResourceUnavailableRpcError: () => (/* binding */ ResourceUnavailableRpcError),\n/* harmony export */   RpcError: () => (/* binding */ RpcError),\n/* harmony export */   SwitchChainError: () => (/* binding */ SwitchChainError),\n/* harmony export */   TransactionRejectedRpcError: () => (/* binding */ TransactionRejectedRpcError),\n/* harmony export */   UnauthorizedProviderError: () => (/* binding */ UnauthorizedProviderError),\n/* harmony export */   UnknownBundleIdError: () => (/* binding */ UnknownBundleIdError),\n/* harmony export */   UnknownRpcError: () => (/* binding */ UnknownRpcError),\n/* harmony export */   UnsupportedChainIdError: () => (/* binding */ UnsupportedChainIdError),\n/* harmony export */   UnsupportedNonOptionalCapabilityError: () => (/* binding */ UnsupportedNonOptionalCapabilityError),\n/* harmony export */   UnsupportedProviderMethodError: () => (/* binding */ UnsupportedProviderMethodError),\n/* harmony export */   UserRejectedRequestError: () => (/* binding */ UserRejectedRequestError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/viem/_esm/errors/base.js\");\n/* harmony import */ var _request_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./request.js */ \"(ssr)/./node_modules/viem/_esm/errors/request.js\");\n\n\nconst unknownErrorCode = -1;\nclass RpcError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor(cause, { code, docsPath, metaMessages, name, shortMessage, }) {\n        super(shortMessage, {\n            cause,\n            docsPath,\n            metaMessages: metaMessages || cause?.metaMessages,\n            name: name || 'RpcError',\n        });\n        Object.defineProperty(this, \"code\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.name = name || cause.name;\n        this.code = (cause instanceof _request_js__WEBPACK_IMPORTED_MODULE_1__.RpcRequestError ? cause.code : (code ?? unknownErrorCode));\n    }\n}\nclass ProviderRpcError extends RpcError {\n    constructor(cause, options) {\n        super(cause, options);\n        Object.defineProperty(this, \"data\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.data = options.data;\n    }\n}\nclass ParseRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: ParseRpcError.code,\n            name: 'ParseRpcError',\n            shortMessage: 'Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text.',\n        });\n    }\n}\nObject.defineProperty(ParseRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32700\n});\nclass InvalidRequestRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: InvalidRequestRpcError.code,\n            name: 'InvalidRequestRpcError',\n            shortMessage: 'JSON is not a valid request object.',\n        });\n    }\n}\nObject.defineProperty(InvalidRequestRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32600\n});\nclass MethodNotFoundRpcError extends RpcError {\n    constructor(cause, { method } = {}) {\n        super(cause, {\n            code: MethodNotFoundRpcError.code,\n            name: 'MethodNotFoundRpcError',\n            shortMessage: `The method${method ? ` \"${method}\"` : ''} does not exist / is not available.`,\n        });\n    }\n}\nObject.defineProperty(MethodNotFoundRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32601\n});\nclass InvalidParamsRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: InvalidParamsRpcError.code,\n            name: 'InvalidParamsRpcError',\n            shortMessage: [\n                'Invalid parameters were provided to the RPC method.',\n                'Double check you have provided the correct parameters.',\n            ].join('\\n'),\n        });\n    }\n}\nObject.defineProperty(InvalidParamsRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32602\n});\nclass InternalRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: InternalRpcError.code,\n            name: 'InternalRpcError',\n            shortMessage: 'An internal error was received.',\n        });\n    }\n}\nObject.defineProperty(InternalRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32603\n});\nclass InvalidInputRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: InvalidInputRpcError.code,\n            name: 'InvalidInputRpcError',\n            shortMessage: [\n                'Missing or invalid parameters.',\n                'Double check you have provided the correct parameters.',\n            ].join('\\n'),\n        });\n    }\n}\nObject.defineProperty(InvalidInputRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32000\n});\nclass ResourceNotFoundRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: ResourceNotFoundRpcError.code,\n            name: 'ResourceNotFoundRpcError',\n            shortMessage: 'Requested resource not found.',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ResourceNotFoundRpcError'\n        });\n    }\n}\nObject.defineProperty(ResourceNotFoundRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32001\n});\nclass ResourceUnavailableRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: ResourceUnavailableRpcError.code,\n            name: 'ResourceUnavailableRpcError',\n            shortMessage: 'Requested resource not available.',\n        });\n    }\n}\nObject.defineProperty(ResourceUnavailableRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32002\n});\nclass TransactionRejectedRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: TransactionRejectedRpcError.code,\n            name: 'TransactionRejectedRpcError',\n            shortMessage: 'Transaction creation failed.',\n        });\n    }\n}\nObject.defineProperty(TransactionRejectedRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32003\n});\nclass MethodNotSupportedRpcError extends RpcError {\n    constructor(cause, { method } = {}) {\n        super(cause, {\n            code: MethodNotSupportedRpcError.code,\n            name: 'MethodNotSupportedRpcError',\n            shortMessage: `Method${method ? ` \"${method}\"` : ''} is not supported.`,\n        });\n    }\n}\nObject.defineProperty(MethodNotSupportedRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32004\n});\nclass LimitExceededRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: LimitExceededRpcError.code,\n            name: 'LimitExceededRpcError',\n            shortMessage: 'Request exceeds defined limit.',\n        });\n    }\n}\nObject.defineProperty(LimitExceededRpcError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32005\n});\nclass JsonRpcVersionUnsupportedError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            code: JsonRpcVersionUnsupportedError.code,\n            name: 'JsonRpcVersionUnsupportedError',\n            shortMessage: 'Version of JSON-RPC protocol is not supported.',\n        });\n    }\n}\nObject.defineProperty(JsonRpcVersionUnsupportedError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: -32006\n});\nclass UserRejectedRequestError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: UserRejectedRequestError.code,\n            name: 'UserRejectedRequestError',\n            shortMessage: 'User rejected the request.',\n        });\n    }\n}\nObject.defineProperty(UserRejectedRequestError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 4001\n});\nclass UnauthorizedProviderError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: UnauthorizedProviderError.code,\n            name: 'UnauthorizedProviderError',\n            shortMessage: 'The requested method and/or account has not been authorized by the user.',\n        });\n    }\n}\nObject.defineProperty(UnauthorizedProviderError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 4100\n});\nclass UnsupportedProviderMethodError extends ProviderRpcError {\n    constructor(cause, { method } = {}) {\n        super(cause, {\n            code: UnsupportedProviderMethodError.code,\n            name: 'UnsupportedProviderMethodError',\n            shortMessage: `The Provider does not support the requested method${method ? ` \" ${method}\"` : ''}.`,\n        });\n    }\n}\nObject.defineProperty(UnsupportedProviderMethodError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 4200\n});\nclass ProviderDisconnectedError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: ProviderDisconnectedError.code,\n            name: 'ProviderDisconnectedError',\n            shortMessage: 'The Provider is disconnected from all chains.',\n        });\n    }\n}\nObject.defineProperty(ProviderDisconnectedError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 4900\n});\nclass ChainDisconnectedError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: ChainDisconnectedError.code,\n            name: 'ChainDisconnectedError',\n            shortMessage: 'The Provider is not connected to the requested chain.',\n        });\n    }\n}\nObject.defineProperty(ChainDisconnectedError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 4901\n});\nclass SwitchChainError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: SwitchChainError.code,\n            name: 'SwitchChainError',\n            shortMessage: 'An error occurred when attempting to switch chain.',\n        });\n    }\n}\nObject.defineProperty(SwitchChainError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 4902\n});\nclass UnsupportedNonOptionalCapabilityError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: UnsupportedNonOptionalCapabilityError.code,\n            name: 'UnsupportedNonOptionalCapabilityError',\n            shortMessage: 'This Wallet does not support a capability that was not marked as optional.',\n        });\n    }\n}\nObject.defineProperty(UnsupportedNonOptionalCapabilityError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 5700\n});\nclass UnsupportedChainIdError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: UnsupportedChainIdError.code,\n            name: 'UnsupportedChainIdError',\n            shortMessage: 'This Wallet does not support the requested chain ID.',\n        });\n    }\n}\nObject.defineProperty(UnsupportedChainIdError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 5710\n});\nclass DuplicateIdError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: DuplicateIdError.code,\n            name: 'DuplicateIdError',\n            shortMessage: 'There is already a bundle submitted with this ID.',\n        });\n    }\n}\nObject.defineProperty(DuplicateIdError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 5720\n});\nclass UnknownBundleIdError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: UnknownBundleIdError.code,\n            name: 'UnknownBundleIdError',\n            shortMessage: 'This bundle id is unknown / has not been submitted',\n        });\n    }\n}\nObject.defineProperty(UnknownBundleIdError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 5730\n});\nclass BundleTooLargeError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: BundleTooLargeError.code,\n            name: 'BundleTooLargeError',\n            shortMessage: 'The call bundle is too large for the Wallet to process.',\n        });\n    }\n}\nObject.defineProperty(BundleTooLargeError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 5740\n});\nclass AtomicReadyWalletRejectedUpgradeError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: AtomicReadyWalletRejectedUpgradeError.code,\n            name: 'AtomicReadyWalletRejectedUpgradeError',\n            shortMessage: 'The Wallet can support atomicity after an upgrade, but the user rejected the upgrade.',\n        });\n    }\n}\nObject.defineProperty(AtomicReadyWalletRejectedUpgradeError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 5750\n});\nclass AtomicityNotSupportedError extends ProviderRpcError {\n    constructor(cause) {\n        super(cause, {\n            code: AtomicityNotSupportedError.code,\n            name: 'AtomicityNotSupportedError',\n            shortMessage: 'The wallet does not support atomic execution but the request requires it.',\n        });\n    }\n}\nObject.defineProperty(AtomicityNotSupportedError, \"code\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: 5760\n});\nclass UnknownRpcError extends RpcError {\n    constructor(cause) {\n        super(cause, {\n            name: 'UnknownRpcError',\n            shortMessage: 'An unknown RPC error occurred.',\n        });\n    }\n}\n//# sourceMappingURL=rpc.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/rpc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/transport.js":
/*!****************************************************!*\
  !*** ./node_modules/viem/_esm/errors/transport.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UrlRequiredError: () => (/* binding */ UrlRequiredError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/viem/_esm/errors/base.js\");\n\nclass UrlRequiredError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('No URL was provided to the Transport. Please provide a valid RPC URL to the Transport.', {\n            docsPath: '/docs/clients/intro',\n            name: 'UrlRequiredError',\n        });\n    }\n}\n//# sourceMappingURL=transport.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2Vycm9ycy90cmFuc3BvcnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBc0M7QUFDL0IsK0JBQStCLCtDQUFTO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy92aWVtL19lc20vZXJyb3JzL3RyYW5zcG9ydC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCYXNlRXJyb3IgfSBmcm9tICcuL2Jhc2UuanMnO1xuZXhwb3J0IGNsYXNzIFVybFJlcXVpcmVkRXJyb3IgZXh0ZW5kcyBCYXNlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlcignTm8gVVJMIHdhcyBwcm92aWRlZCB0byB0aGUgVHJhbnNwb3J0LiBQbGVhc2UgcHJvdmlkZSBhIHZhbGlkIFJQQyBVUkwgdG8gdGhlIFRyYW5zcG9ydC4nLCB7XG4gICAgICAgICAgICBkb2NzUGF0aDogJy9kb2NzL2NsaWVudHMvaW50cm8nLFxuICAgICAgICAgICAgbmFtZTogJ1VybFJlcXVpcmVkRXJyb3InLFxuICAgICAgICB9KTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD10cmFuc3BvcnQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/transport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/utils.js":
/*!************************************************!*\
  !*** ./node_modules/viem/_esm/errors/utils.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getContractAddress: () => (/* binding */ getContractAddress),\n/* harmony export */   getUrl: () => (/* binding */ getUrl)\n/* harmony export */ });\nconst getContractAddress = (address) => address;\nconst getUrl = (url) => url;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2Vycm9ycy91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ0E7QUFDUCIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy92aWVtL19lc20vZXJyb3JzL3V0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBnZXRDb250cmFjdEFkZHJlc3MgPSAoYWRkcmVzcykgPT4gYWRkcmVzcztcbmV4cG9ydCBjb25zdCBnZXRVcmwgPSAodXJsKSA9PiB1cmw7XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlscy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/errors/version.js":
/*!**************************************************!*\
  !*** ./node_modules/viem/_esm/errors/version.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.30.6';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL2Vycm9ycy92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3ZpZW0vX2VzbS9lcnJvcnMvdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdmVyc2lvbiA9ICcyLjMwLjYnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/errors/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js":
/*!************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/address/getAddress.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checksumAddress: () => (/* binding */ checksumAddress),\n/* harmony export */   getAddress: () => (/* binding */ getAddress)\n/* harmony export */ });\n/* harmony import */ var _errors_address_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../errors/address.js */ \"(ssr)/./node_modules/viem/_esm/errors/address.js\");\n/* harmony import */ var _encoding_toBytes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../encoding/toBytes.js */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toBytes.js\");\n/* harmony import */ var _hash_keccak256_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../hash/keccak256.js */ \"(ssr)/./node_modules/viem/_esm/utils/hash/keccak256.js\");\n/* harmony import */ var _lru_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lru.js */ \"(ssr)/./node_modules/viem/_esm/utils/lru.js\");\n/* harmony import */ var _isAddress_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./isAddress.js */ \"(ssr)/./node_modules/viem/_esm/utils/address/isAddress.js\");\n\n\n\n\n\nconst checksumAddressCache = /*#__PURE__*/ new _lru_js__WEBPACK_IMPORTED_MODULE_0__.LruMap(8192);\nfunction checksumAddress(address_, \n/**\n * Warning: EIP-1191 checksum addresses are generally not backwards compatible with the\n * wider Ethereum ecosystem, meaning it will break when validated against an application/tool\n * that relies on EIP-55 checksum encoding (checksum without chainId).\n *\n * It is highly recommended to not use this feature unless you\n * know what you are doing.\n *\n * See more: https://github.com/ethereum/EIPs/issues/1121\n */\nchainId) {\n    if (checksumAddressCache.has(`${address_}.${chainId}`))\n        return checksumAddressCache.get(`${address_}.${chainId}`);\n    const hexAddress = chainId\n        ? `${chainId}${address_.toLowerCase()}`\n        : address_.substring(2).toLowerCase();\n    const hash = (0,_hash_keccak256_js__WEBPACK_IMPORTED_MODULE_1__.keccak256)((0,_encoding_toBytes_js__WEBPACK_IMPORTED_MODULE_2__.stringToBytes)(hexAddress), 'bytes');\n    const address = (chainId ? hexAddress.substring(`${chainId}0x`.length) : hexAddress).split('');\n    for (let i = 0; i < 40; i += 2) {\n        if (hash[i >> 1] >> 4 >= 8 && address[i]) {\n            address[i] = address[i].toUpperCase();\n        }\n        if ((hash[i >> 1] & 0x0f) >= 8 && address[i + 1]) {\n            address[i + 1] = address[i + 1].toUpperCase();\n        }\n    }\n    const result = `0x${address.join('')}`;\n    checksumAddressCache.set(`${address_}.${chainId}`, result);\n    return result;\n}\nfunction getAddress(address, \n/**\n * Warning: EIP-1191 checksum addresses are generally not backwards compatible with the\n * wider Ethereum ecosystem, meaning it will break when validated against an application/tool\n * that relies on EIP-55 checksum encoding (checksum without chainId).\n *\n * It is highly recommended to not use this feature unless you\n * know what you are doing.\n *\n * See more: https://github.com/ethereum/EIPs/issues/1121\n */\nchainId) {\n    if (!(0,_isAddress_js__WEBPACK_IMPORTED_MODULE_3__.isAddress)(address, { strict: false }))\n        throw new _errors_address_js__WEBPACK_IMPORTED_MODULE_4__.InvalidAddressError({ address });\n    return checksumAddress(address, chainId);\n}\n//# sourceMappingURL=getAddress.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/address/isAddress.js":
/*!***********************************************************!*\
  !*** ./node_modules/viem/_esm/utils/address/isAddress.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isAddress: () => (/* binding */ isAddress),\n/* harmony export */   isAddressCache: () => (/* binding */ isAddressCache)\n/* harmony export */ });\n/* harmony import */ var _lru_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lru.js */ \"(ssr)/./node_modules/viem/_esm/utils/lru.js\");\n/* harmony import */ var _getAddress_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getAddress.js */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n\n\nconst addressRegex = /^0x[a-fA-F0-9]{40}$/;\n/** @internal */\nconst isAddressCache = /*#__PURE__*/ new _lru_js__WEBPACK_IMPORTED_MODULE_0__.LruMap(8192);\nfunction isAddress(address, options) {\n    const { strict = true } = options ?? {};\n    const cacheKey = `${address}.${strict}`;\n    if (isAddressCache.has(cacheKey))\n        return isAddressCache.get(cacheKey);\n    const result = (() => {\n        if (!addressRegex.test(address))\n            return false;\n        if (address.toLowerCase() === address)\n            return true;\n        if (strict)\n            return (0,_getAddress_js__WEBPACK_IMPORTED_MODULE_1__.checksumAddress)(address) === address;\n        return true;\n    })();\n    isAddressCache.set(cacheKey, result);\n    return result;\n}\n//# sourceMappingURL=isAddress.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2FkZHJlc3MvaXNBZGRyZXNzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBbUM7QUFDZTtBQUNsRCxxQ0FBcUMsR0FBRztBQUN4QztBQUNPLHlDQUF5QywyQ0FBTTtBQUMvQztBQUNQLFlBQVksZ0JBQWdCO0FBQzVCLHdCQUF3QixRQUFRLEdBQUcsT0FBTztBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLCtEQUFlO0FBQ2xDO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3ZpZW0vX2VzbS91dGlscy9hZGRyZXNzL2lzQWRkcmVzcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBMcnVNYXAgfSBmcm9tICcuLi9scnUuanMnO1xuaW1wb3J0IHsgY2hlY2tzdW1BZGRyZXNzIH0gZnJvbSAnLi9nZXRBZGRyZXNzLmpzJztcbmNvbnN0IGFkZHJlc3NSZWdleCA9IC9eMHhbYS1mQS1GMC05XXs0MH0kLztcbi8qKiBAaW50ZXJuYWwgKi9cbmV4cG9ydCBjb25zdCBpc0FkZHJlc3NDYWNoZSA9IC8qI19fUFVSRV9fKi8gbmV3IExydU1hcCg4MTkyKTtcbmV4cG9ydCBmdW5jdGlvbiBpc0FkZHJlc3MoYWRkcmVzcywgb3B0aW9ucykge1xuICAgIGNvbnN0IHsgc3RyaWN0ID0gdHJ1ZSB9ID0gb3B0aW9ucyA/PyB7fTtcbiAgICBjb25zdCBjYWNoZUtleSA9IGAke2FkZHJlc3N9LiR7c3RyaWN0fWA7XG4gICAgaWYgKGlzQWRkcmVzc0NhY2hlLmhhcyhjYWNoZUtleSkpXG4gICAgICAgIHJldHVybiBpc0FkZHJlc3NDYWNoZS5nZXQoY2FjaGVLZXkpO1xuICAgIGNvbnN0IHJlc3VsdCA9ICgoKSA9PiB7XG4gICAgICAgIGlmICghYWRkcmVzc1JlZ2V4LnRlc3QoYWRkcmVzcykpXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIGlmIChhZGRyZXNzLnRvTG93ZXJDYXNlKCkgPT09IGFkZHJlc3MpXG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgaWYgKHN0cmljdClcbiAgICAgICAgICAgIHJldHVybiBjaGVja3N1bUFkZHJlc3MoYWRkcmVzcykgPT09IGFkZHJlc3M7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH0pKCk7XG4gICAgaXNBZGRyZXNzQ2FjaGUuc2V0KGNhY2hlS2V5LCByZXN1bHQpO1xuICAgIHJldHVybiByZXN1bHQ7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pc0FkZHJlc3MuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/address/isAddress.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/buildRequest.js":
/*!******************************************************!*\
  !*** ./node_modules/viem/_esm/utils/buildRequest.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildRequest: () => (/* binding */ buildRequest),\n/* harmony export */   shouldRetry: () => (/* binding */ shouldRetry)\n/* harmony export */ });\n/* harmony import */ var _errors_base_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../errors/base.js */ \"(ssr)/./node_modules/viem/_esm/errors/base.js\");\n/* harmony import */ var _errors_request_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../errors/request.js */ \"(ssr)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/rpc.js */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var _encoding_toHex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./encoding/toHex.js */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var _promise_withDedupe_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./promise/withDedupe.js */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withDedupe.js\");\n/* harmony import */ var _promise_withRetry_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./promise/withRetry.js */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withRetry.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stringify.js */ \"(ssr)/./node_modules/viem/_esm/utils/stringify.js\");\n\n\n\n\n\n\n\nfunction buildRequest(request, options = {}) {\n    return async (args, overrideOptions = {}) => {\n        const { dedupe = false, methods, retryDelay = 150, retryCount = 3, uid, } = {\n            ...options,\n            ...overrideOptions,\n        };\n        const { method } = args;\n        if (methods?.exclude?.includes(method))\n            throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.MethodNotSupportedRpcError(new Error('method not supported'), {\n                method,\n            });\n        if (methods?.include && !methods.include.includes(method))\n            throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.MethodNotSupportedRpcError(new Error('method not supported'), {\n                method,\n            });\n        const requestId = dedupe\n            ? (0,_encoding_toHex_js__WEBPACK_IMPORTED_MODULE_1__.stringToHex)(`${uid}.${(0,_stringify_js__WEBPACK_IMPORTED_MODULE_2__.stringify)(args)}`)\n            : undefined;\n        return (0,_promise_withDedupe_js__WEBPACK_IMPORTED_MODULE_3__.withDedupe)(() => (0,_promise_withRetry_js__WEBPACK_IMPORTED_MODULE_4__.withRetry)(async () => {\n            try {\n                return await request(args);\n            }\n            catch (err_) {\n                const err = err_;\n                switch (err.code) {\n                    // -32700\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ParseRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ParseRpcError(err);\n                    // -32600\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InvalidRequestRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InvalidRequestRpcError(err);\n                    // -32601\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.MethodNotFoundRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.MethodNotFoundRpcError(err, { method: args.method });\n                    // -32602\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InvalidParamsRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InvalidParamsRpcError(err);\n                    // -32603\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InternalRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InternalRpcError(err);\n                    // -32000\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InvalidInputRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InvalidInputRpcError(err);\n                    // -32001\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ResourceNotFoundRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ResourceNotFoundRpcError(err);\n                    // -32002\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ResourceUnavailableRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ResourceUnavailableRpcError(err);\n                    // -32003\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.TransactionRejectedRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.TransactionRejectedRpcError(err);\n                    // -32004\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.MethodNotSupportedRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.MethodNotSupportedRpcError(err, {\n                            method: args.method,\n                        });\n                    // -32005\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.LimitExceededRpcError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.LimitExceededRpcError(err);\n                    // -32006\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.JsonRpcVersionUnsupportedError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.JsonRpcVersionUnsupportedError(err);\n                    // 4001\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UserRejectedRequestError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UserRejectedRequestError(err);\n                    // 4100\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnauthorizedProviderError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnauthorizedProviderError(err);\n                    // 4200\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnsupportedProviderMethodError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnsupportedProviderMethodError(err);\n                    // 4900\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ProviderDisconnectedError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ProviderDisconnectedError(err);\n                    // 4901\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ChainDisconnectedError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.ChainDisconnectedError(err);\n                    // 4902\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.SwitchChainError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.SwitchChainError(err);\n                    // 5700\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnsupportedNonOptionalCapabilityError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnsupportedNonOptionalCapabilityError(err);\n                    // 5710\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnsupportedChainIdError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnsupportedChainIdError(err);\n                    // 5720\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.DuplicateIdError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.DuplicateIdError(err);\n                    // 5730\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnknownBundleIdError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnknownBundleIdError(err);\n                    // 5740\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.BundleTooLargeError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.BundleTooLargeError(err);\n                    // 5750\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.AtomicReadyWalletRejectedUpgradeError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.AtomicReadyWalletRejectedUpgradeError(err);\n                    // 5760\n                    case _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.AtomicityNotSupportedError.code:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.AtomicityNotSupportedError(err);\n                    // CAIP-25: User Rejected Error\n                    // https://docs.walletconnect.com/2.0/specs/clients/sign/error-codes#rejected-caip-25\n                    case 5000:\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UserRejectedRequestError(err);\n                    default:\n                        if (err_ instanceof _errors_base_js__WEBPACK_IMPORTED_MODULE_5__.BaseError)\n                            throw err_;\n                        throw new _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.UnknownRpcError(err);\n                }\n            }\n        }, {\n            delay: ({ count, error }) => {\n                // If we find a Retry-After header, let's retry after the given time.\n                if (error && error instanceof _errors_request_js__WEBPACK_IMPORTED_MODULE_6__.HttpRequestError) {\n                    const retryAfter = error?.headers?.get('Retry-After');\n                    if (retryAfter?.match(/\\d/))\n                        return Number.parseInt(retryAfter) * 1000;\n                }\n                // Otherwise, let's retry with an exponential backoff.\n                return ~~(1 << count) * retryDelay;\n            },\n            retryCount,\n            shouldRetry: ({ error }) => shouldRetry(error),\n        }), { enabled: dedupe, id: requestId });\n    };\n}\n/** @internal */\nfunction shouldRetry(error) {\n    if ('code' in error && typeof error.code === 'number') {\n        if (error.code === -1)\n            return true; // Unknown error\n        if (error.code === _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.LimitExceededRpcError.code)\n            return true;\n        if (error.code === _errors_rpc_js__WEBPACK_IMPORTED_MODULE_0__.InternalRpcError.code)\n            return true;\n        return false;\n    }\n    if (error instanceof _errors_request_js__WEBPACK_IMPORTED_MODULE_6__.HttpRequestError && error.status) {\n        // Forbidden\n        if (error.status === 403)\n            return true;\n        // Request Timeout\n        if (error.status === 408)\n            return true;\n        // Request Entity Too Large\n        if (error.status === 413)\n            return true;\n        // Too Many Requests\n        if (error.status === 429)\n            return true;\n        // Internal Server Error\n        if (error.status === 500)\n            return true;\n        // Bad Gateway\n        if (error.status === 502)\n            return true;\n        // Service Unavailable\n        if (error.status === 503)\n            return true;\n        // Gateway Timeout\n        if (error.status === 504)\n            return true;\n        return false;\n    }\n    return true;\n}\n//# sourceMappingURL=buildRequest.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/buildRequest.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/chain/defineChain.js":
/*!***********************************************************!*\
  !*** ./node_modules/viem/_esm/utils/chain/defineChain.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defineChain: () => (/* binding */ defineChain)\n/* harmony export */ });\nfunction defineChain(chain) {\n    return {\n        formatters: undefined,\n        fees: undefined,\n        serializers: undefined,\n        ...chain,\n    };\n}\n//# sourceMappingURL=defineChain.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2NoYWluL2RlZmluZUNoYWluLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2NoYWluL2RlZmluZUNoYWluLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBkZWZpbmVDaGFpbihjaGFpbikge1xuICAgIHJldHVybiB7XG4gICAgICAgIGZvcm1hdHRlcnM6IHVuZGVmaW5lZCxcbiAgICAgICAgZmVlczogdW5kZWZpbmVkLFxuICAgICAgICBzZXJpYWxpemVyczogdW5kZWZpbmVkLFxuICAgICAgICAuLi5jaGFpbixcbiAgICB9O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGVmaW5lQ2hhaW4uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/chain/defineChain.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/data/isHex.js":
/*!****************************************************!*\
  !*** ./node_modules/viem/_esm/utils/data/isHex.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isHex: () => (/* binding */ isHex)\n/* harmony export */ });\nfunction isHex(value, { strict = true } = {}) {\n    if (!value)\n        return false;\n    if (typeof value !== 'string')\n        return false;\n    return strict ? /^0x[0-9a-fA-F]*$/.test(value) : value.startsWith('0x');\n}\n//# sourceMappingURL=isHex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2RhdGEvaXNIZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLHdCQUF3QixnQkFBZ0IsSUFBSTtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy92aWVtL19lc20vdXRpbHMvZGF0YS9pc0hleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gaXNIZXgodmFsdWUsIHsgc3RyaWN0ID0gdHJ1ZSB9ID0ge30pIHtcbiAgICBpZiAoIXZhbHVlKVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gJ3N0cmluZycpXG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICByZXR1cm4gc3RyaWN0ID8gL14weFswLTlhLWZBLUZdKiQvLnRlc3QodmFsdWUpIDogdmFsdWUuc3RhcnRzV2l0aCgnMHgnKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWlzSGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/data/isHex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/data/pad.js":
/*!**************************************************!*\
  !*** ./node_modules/viem/_esm/utils/data/pad.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pad: () => (/* binding */ pad),\n/* harmony export */   padBytes: () => (/* binding */ padBytes),\n/* harmony export */   padHex: () => (/* binding */ padHex)\n/* harmony export */ });\n/* harmony import */ var _errors_data_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors/data.js */ \"(ssr)/./node_modules/viem/_esm/errors/data.js\");\n\nfunction pad(hexOrBytes, { dir, size = 32 } = {}) {\n    if (typeof hexOrBytes === 'string')\n        return padHex(hexOrBytes, { dir, size });\n    return padBytes(hexOrBytes, { dir, size });\n}\nfunction padHex(hex_, { dir, size = 32 } = {}) {\n    if (size === null)\n        return hex_;\n    const hex = hex_.replace('0x', '');\n    if (hex.length > size * 2)\n        throw new _errors_data_js__WEBPACK_IMPORTED_MODULE_0__.SizeExceedsPaddingSizeError({\n            size: Math.ceil(hex.length / 2),\n            targetSize: size,\n            type: 'hex',\n        });\n    return `0x${hex[dir === 'right' ? 'padEnd' : 'padStart'](size * 2, '0')}`;\n}\nfunction padBytes(bytes, { dir, size = 32 } = {}) {\n    if (size === null)\n        return bytes;\n    if (bytes.length > size)\n        throw new _errors_data_js__WEBPACK_IMPORTED_MODULE_0__.SizeExceedsPaddingSizeError({\n            size: bytes.length,\n            targetSize: size,\n            type: 'bytes',\n        });\n    const paddedBytes = new Uint8Array(size);\n    for (let i = 0; i < size; i++) {\n        const padEnd = dir === 'right';\n        paddedBytes[padEnd ? i : size - i - 1] =\n            bytes[padEnd ? i : bytes.length - i - 1];\n    }\n    return paddedBytes;\n}\n//# sourceMappingURL=pad.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2RhdGEvcGFkLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0U7QUFDN0QsMkJBQTJCLGlCQUFpQixJQUFJO0FBQ3ZEO0FBQ0Esb0NBQW9DLFdBQVc7QUFDL0Msa0NBQWtDLFdBQVc7QUFDN0M7QUFDTyx3QkFBd0IsaUJBQWlCLElBQUk7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0Isd0VBQTJCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxnQkFBZ0IsNERBQTREO0FBQzVFO0FBQ08sMkJBQTJCLGlCQUFpQixJQUFJO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix3RUFBMkI7QUFDN0M7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0Esb0JBQW9CLFVBQVU7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2RhdGEvcGFkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFNpemVFeGNlZWRzUGFkZGluZ1NpemVFcnJvciwgfSBmcm9tICcuLi8uLi9lcnJvcnMvZGF0YS5qcyc7XG5leHBvcnQgZnVuY3Rpb24gcGFkKGhleE9yQnl0ZXMsIHsgZGlyLCBzaXplID0gMzIgfSA9IHt9KSB7XG4gICAgaWYgKHR5cGVvZiBoZXhPckJ5dGVzID09PSAnc3RyaW5nJylcbiAgICAgICAgcmV0dXJuIHBhZEhleChoZXhPckJ5dGVzLCB7IGRpciwgc2l6ZSB9KTtcbiAgICByZXR1cm4gcGFkQnl0ZXMoaGV4T3JCeXRlcywgeyBkaXIsIHNpemUgfSk7XG59XG5leHBvcnQgZnVuY3Rpb24gcGFkSGV4KGhleF8sIHsgZGlyLCBzaXplID0gMzIgfSA9IHt9KSB7XG4gICAgaWYgKHNpemUgPT09IG51bGwpXG4gICAgICAgIHJldHVybiBoZXhfO1xuICAgIGNvbnN0IGhleCA9IGhleF8ucmVwbGFjZSgnMHgnLCAnJyk7XG4gICAgaWYgKGhleC5sZW5ndGggPiBzaXplICogMilcbiAgICAgICAgdGhyb3cgbmV3IFNpemVFeGNlZWRzUGFkZGluZ1NpemVFcnJvcih7XG4gICAgICAgICAgICBzaXplOiBNYXRoLmNlaWwoaGV4Lmxlbmd0aCAvIDIpLFxuICAgICAgICAgICAgdGFyZ2V0U2l6ZTogc2l6ZSxcbiAgICAgICAgICAgIHR5cGU6ICdoZXgnLFxuICAgICAgICB9KTtcbiAgICByZXR1cm4gYDB4JHtoZXhbZGlyID09PSAncmlnaHQnID8gJ3BhZEVuZCcgOiAncGFkU3RhcnQnXShzaXplICogMiwgJzAnKX1gO1xufVxuZXhwb3J0IGZ1bmN0aW9uIHBhZEJ5dGVzKGJ5dGVzLCB7IGRpciwgc2l6ZSA9IDMyIH0gPSB7fSkge1xuICAgIGlmIChzaXplID09PSBudWxsKVxuICAgICAgICByZXR1cm4gYnl0ZXM7XG4gICAgaWYgKGJ5dGVzLmxlbmd0aCA+IHNpemUpXG4gICAgICAgIHRocm93IG5ldyBTaXplRXhjZWVkc1BhZGRpbmdTaXplRXJyb3Ioe1xuICAgICAgICAgICAgc2l6ZTogYnl0ZXMubGVuZ3RoLFxuICAgICAgICAgICAgdGFyZ2V0U2l6ZTogc2l6ZSxcbiAgICAgICAgICAgIHR5cGU6ICdieXRlcycsXG4gICAgICAgIH0pO1xuICAgIGNvbnN0IHBhZGRlZEJ5dGVzID0gbmV3IFVpbnQ4QXJyYXkoc2l6ZSk7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzaXplOyBpKyspIHtcbiAgICAgICAgY29uc3QgcGFkRW5kID0gZGlyID09PSAncmlnaHQnO1xuICAgICAgICBwYWRkZWRCeXRlc1twYWRFbmQgPyBpIDogc2l6ZSAtIGkgLSAxXSA9XG4gICAgICAgICAgICBieXRlc1twYWRFbmQgPyBpIDogYnl0ZXMubGVuZ3RoIC0gaSAtIDFdO1xuICAgIH1cbiAgICByZXR1cm4gcGFkZGVkQnl0ZXM7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYWQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/data/pad.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/data/size.js":
/*!***************************************************!*\
  !*** ./node_modules/viem/_esm/utils/data/size.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   size: () => (/* binding */ size)\n/* harmony export */ });\n/* harmony import */ var _isHex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isHex.js */ \"(ssr)/./node_modules/viem/_esm/utils/data/isHex.js\");\n\n/**\n * @description Retrieves the size of the value (in bytes).\n *\n * @param value The value (hex or byte array) to retrieve the size of.\n * @returns The size of the value (in bytes).\n */\nfunction size(value) {\n    if ((0,_isHex_js__WEBPACK_IMPORTED_MODULE_0__.isHex)(value, { strict: false }))\n        return Math.ceil((value.length - 2) / 2);\n    return value.length;\n}\n//# sourceMappingURL=size.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2RhdGEvc2l6ZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtQztBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLFFBQVEsZ0RBQUssVUFBVSxlQUFlO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3ZpZW0vX2VzbS91dGlscy9kYXRhL3NpemUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNIZXggfSBmcm9tICcuL2lzSGV4LmpzJztcbi8qKlxuICogQGRlc2NyaXB0aW9uIFJldHJpZXZlcyB0aGUgc2l6ZSBvZiB0aGUgdmFsdWUgKGluIGJ5dGVzKS5cbiAqXG4gKiBAcGFyYW0gdmFsdWUgVGhlIHZhbHVlIChoZXggb3IgYnl0ZSBhcnJheSkgdG8gcmV0cmlldmUgdGhlIHNpemUgb2YuXG4gKiBAcmV0dXJucyBUaGUgc2l6ZSBvZiB0aGUgdmFsdWUgKGluIGJ5dGVzKS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHNpemUodmFsdWUpIHtcbiAgICBpZiAoaXNIZXgodmFsdWUsIHsgc3RyaWN0OiBmYWxzZSB9KSlcbiAgICAgICAgcmV0dXJuIE1hdGguY2VpbCgodmFsdWUubGVuZ3RoIC0gMikgLyAyKTtcbiAgICByZXR1cm4gdmFsdWUubGVuZ3RoO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2l6ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/data/size.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/data/trim.js":
/*!***************************************************!*\
  !*** ./node_modules/viem/_esm/utils/data/trim.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   trim: () => (/* binding */ trim)\n/* harmony export */ });\nfunction trim(hexOrBytes, { dir = 'left' } = {}) {\n    let data = typeof hexOrBytes === 'string' ? hexOrBytes.replace('0x', '') : hexOrBytes;\n    let sliceLength = 0;\n    for (let i = 0; i < data.length - 1; i++) {\n        if (data[dir === 'left' ? i : data.length - i - 1].toString() === '0')\n            sliceLength++;\n        else\n            break;\n    }\n    data =\n        dir === 'left'\n            ? data.slice(sliceLength)\n            : data.slice(0, data.length - sliceLength);\n    if (typeof hexOrBytes === 'string') {\n        if (data.length === 1 && dir === 'right')\n            data = `${data}0`;\n        return `0x${data.length % 2 === 1 ? `0${data}` : data}`;\n    }\n    return data;\n}\n//# sourceMappingURL=trim.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2RhdGEvdHJpbS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8sNEJBQTRCLGVBQWUsSUFBSTtBQUN0RDtBQUNBO0FBQ0Esb0JBQW9CLHFCQUFxQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLEtBQUs7QUFDM0Isb0JBQW9CLDRCQUE0QixLQUFLLFNBQVM7QUFDOUQ7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2RhdGEvdHJpbS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gdHJpbShoZXhPckJ5dGVzLCB7IGRpciA9ICdsZWZ0JyB9ID0ge30pIHtcbiAgICBsZXQgZGF0YSA9IHR5cGVvZiBoZXhPckJ5dGVzID09PSAnc3RyaW5nJyA/IGhleE9yQnl0ZXMucmVwbGFjZSgnMHgnLCAnJykgOiBoZXhPckJ5dGVzO1xuICAgIGxldCBzbGljZUxlbmd0aCA9IDA7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkYXRhLmxlbmd0aCAtIDE7IGkrKykge1xuICAgICAgICBpZiAoZGF0YVtkaXIgPT09ICdsZWZ0JyA/IGkgOiBkYXRhLmxlbmd0aCAtIGkgLSAxXS50b1N0cmluZygpID09PSAnMCcpXG4gICAgICAgICAgICBzbGljZUxlbmd0aCsrO1xuICAgICAgICBlbHNlXG4gICAgICAgICAgICBicmVhaztcbiAgICB9XG4gICAgZGF0YSA9XG4gICAgICAgIGRpciA9PT0gJ2xlZnQnXG4gICAgICAgICAgICA/IGRhdGEuc2xpY2Uoc2xpY2VMZW5ndGgpXG4gICAgICAgICAgICA6IGRhdGEuc2xpY2UoMCwgZGF0YS5sZW5ndGggLSBzbGljZUxlbmd0aCk7XG4gICAgaWYgKHR5cGVvZiBoZXhPckJ5dGVzID09PSAnc3RyaW5nJykge1xuICAgICAgICBpZiAoZGF0YS5sZW5ndGggPT09IDEgJiYgZGlyID09PSAncmlnaHQnKVxuICAgICAgICAgICAgZGF0YSA9IGAke2RhdGF9MGA7XG4gICAgICAgIHJldHVybiBgMHgke2RhdGEubGVuZ3RoICUgMiA9PT0gMSA/IGAwJHtkYXRhfWAgOiBkYXRhfWA7XG4gICAgfVxuICAgIHJldHVybiBkYXRhO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHJpbS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/data/trim.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js":
/*!**********************************************************!*\
  !*** ./node_modules/viem/_esm/utils/encoding/fromHex.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assertSize: () => (/* binding */ assertSize),\n/* harmony export */   fromHex: () => (/* binding */ fromHex),\n/* harmony export */   hexToBigInt: () => (/* binding */ hexToBigInt),\n/* harmony export */   hexToBool: () => (/* binding */ hexToBool),\n/* harmony export */   hexToNumber: () => (/* binding */ hexToNumber),\n/* harmony export */   hexToString: () => (/* binding */ hexToString)\n/* harmony export */ });\n/* harmony import */ var _errors_encoding_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../errors/encoding.js */ \"(ssr)/./node_modules/viem/_esm/errors/encoding.js\");\n/* harmony import */ var _data_size_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../data/size.js */ \"(ssr)/./node_modules/viem/_esm/utils/data/size.js\");\n/* harmony import */ var _data_trim_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../data/trim.js */ \"(ssr)/./node_modules/viem/_esm/utils/data/trim.js\");\n/* harmony import */ var _toBytes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./toBytes.js */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toBytes.js\");\n\n\n\n\nfunction assertSize(hexOrBytes, { size }) {\n    if ((0,_data_size_js__WEBPACK_IMPORTED_MODULE_0__.size)(hexOrBytes) > size)\n        throw new _errors_encoding_js__WEBPACK_IMPORTED_MODULE_1__.SizeOverflowError({\n            givenSize: (0,_data_size_js__WEBPACK_IMPORTED_MODULE_0__.size)(hexOrBytes),\n            maxSize: size,\n        });\n}\n/**\n * Decodes a hex string into a string, number, bigint, boolean, or byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex\n * - Example: https://viem.sh/docs/utilities/fromHex#usage\n *\n * @param hex Hex string to decode.\n * @param toOrOpts Type to convert to or options.\n * @returns Decoded value.\n *\n * @example\n * import { fromHex } from 'viem'\n * const data = fromHex('0x1a4', 'number')\n * // 420\n *\n * @example\n * import { fromHex } from 'viem'\n * const data = fromHex('0x48656c6c6f20576f726c6421', 'string')\n * // 'Hello world'\n *\n * @example\n * import { fromHex } from 'viem'\n * const data = fromHex('0x48656c6c6f20576f726c64210000000000000000000000000000000000000000', {\n *   size: 32,\n *   to: 'string'\n * })\n * // 'Hello world'\n */\nfunction fromHex(hex, toOrOpts) {\n    const opts = typeof toOrOpts === 'string' ? { to: toOrOpts } : toOrOpts;\n    const to = opts.to;\n    if (to === 'number')\n        return hexToNumber(hex, opts);\n    if (to === 'bigint')\n        return hexToBigInt(hex, opts);\n    if (to === 'string')\n        return hexToString(hex, opts);\n    if (to === 'boolean')\n        return hexToBool(hex, opts);\n    return (0,_toBytes_js__WEBPACK_IMPORTED_MODULE_2__.hexToBytes)(hex, opts);\n}\n/**\n * Decodes a hex value into a bigint.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex#hextobigint\n *\n * @param hex Hex value to decode.\n * @param opts Options.\n * @returns BigInt value.\n *\n * @example\n * import { hexToBigInt } from 'viem'\n * const data = hexToBigInt('0x1a4', { signed: true })\n * // 420n\n *\n * @example\n * import { hexToBigInt } from 'viem'\n * const data = hexToBigInt('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // 420n\n */\nfunction hexToBigInt(hex, opts = {}) {\n    const { signed } = opts;\n    if (opts.size)\n        assertSize(hex, { size: opts.size });\n    const value = BigInt(hex);\n    if (!signed)\n        return value;\n    const size = (hex.length - 2) / 2;\n    const max = (1n << (BigInt(size) * 8n - 1n)) - 1n;\n    if (value <= max)\n        return value;\n    return value - BigInt(`0x${'f'.padStart(size * 2, 'f')}`) - 1n;\n}\n/**\n * Decodes a hex value into a boolean.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex#hextobool\n *\n * @param hex Hex value to decode.\n * @param opts Options.\n * @returns Boolean value.\n *\n * @example\n * import { hexToBool } from 'viem'\n * const data = hexToBool('0x01')\n * // true\n *\n * @example\n * import { hexToBool } from 'viem'\n * const data = hexToBool('0x0000000000000000000000000000000000000000000000000000000000000001', { size: 32 })\n * // true\n */\nfunction hexToBool(hex_, opts = {}) {\n    let hex = hex_;\n    if (opts.size) {\n        assertSize(hex, { size: opts.size });\n        hex = (0,_data_trim_js__WEBPACK_IMPORTED_MODULE_3__.trim)(hex);\n    }\n    if ((0,_data_trim_js__WEBPACK_IMPORTED_MODULE_3__.trim)(hex) === '0x00')\n        return false;\n    if ((0,_data_trim_js__WEBPACK_IMPORTED_MODULE_3__.trim)(hex) === '0x01')\n        return true;\n    throw new _errors_encoding_js__WEBPACK_IMPORTED_MODULE_1__.InvalidHexBooleanError(hex);\n}\n/**\n * Decodes a hex string into a number.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex#hextonumber\n *\n * @param hex Hex value to decode.\n * @param opts Options.\n * @returns Number value.\n *\n * @example\n * import { hexToNumber } from 'viem'\n * const data = hexToNumber('0x1a4')\n * // 420\n *\n * @example\n * import { hexToNumber } from 'viem'\n * const data = hexToBigInt('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // 420\n */\nfunction hexToNumber(hex, opts = {}) {\n    return Number(hexToBigInt(hex, opts));\n}\n/**\n * Decodes a hex value into a UTF-8 string.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex#hextostring\n *\n * @param hex Hex value to decode.\n * @param opts Options.\n * @returns String value.\n *\n * @example\n * import { hexToString } from 'viem'\n * const data = hexToString('0x48656c6c6f20576f726c6421')\n * // 'Hello world!'\n *\n * @example\n * import { hexToString } from 'viem'\n * const data = hexToString('0x48656c6c6f20576f726c64210000000000000000000000000000000000000000', {\n *  size: 32,\n * })\n * // 'Hello world'\n */\nfunction hexToString(hex, opts = {}) {\n    let bytes = (0,_toBytes_js__WEBPACK_IMPORTED_MODULE_2__.hexToBytes)(hex);\n    if (opts.size) {\n        assertSize(bytes, { size: opts.size });\n        bytes = (0,_data_trim_js__WEBPACK_IMPORTED_MODULE_3__.trim)(bytes, { dir: 'right' });\n    }\n    return new TextDecoder().decode(bytes);\n}\n//# sourceMappingURL=fromHex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/encoding/toBytes.js":
/*!**********************************************************!*\
  !*** ./node_modules/viem/_esm/utils/encoding/toBytes.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   boolToBytes: () => (/* binding */ boolToBytes),\n/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),\n/* harmony export */   numberToBytes: () => (/* binding */ numberToBytes),\n/* harmony export */   stringToBytes: () => (/* binding */ stringToBytes),\n/* harmony export */   toBytes: () => (/* binding */ toBytes)\n/* harmony export */ });\n/* harmony import */ var _errors_base_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../errors/base.js */ \"(ssr)/./node_modules/viem/_esm/errors/base.js\");\n/* harmony import */ var _data_isHex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../data/isHex.js */ \"(ssr)/./node_modules/viem/_esm/utils/data/isHex.js\");\n/* harmony import */ var _data_pad_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../data/pad.js */ \"(ssr)/./node_modules/viem/_esm/utils/data/pad.js\");\n/* harmony import */ var _fromHex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./fromHex.js */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n/* harmony import */ var _toHex_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./toHex.js */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n\n\n\n\n\nconst encoder = /*#__PURE__*/ new TextEncoder();\n/**\n * Encodes a UTF-8 string, hex value, bigint, number or boolean to a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes\n * - Example: https://viem.sh/docs/utilities/toBytes#usage\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { toBytes } from 'viem'\n * const data = toBytes('Hello world')\n * // Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n *\n * @example\n * import { toBytes } from 'viem'\n * const data = toBytes(420)\n * // Uint8Array([1, 164])\n *\n * @example\n * import { toBytes } from 'viem'\n * const data = toBytes(420, { size: 4 })\n * // Uint8Array([0, 0, 1, 164])\n */\nfunction toBytes(value, opts = {}) {\n    if (typeof value === 'number' || typeof value === 'bigint')\n        return numberToBytes(value, opts);\n    if (typeof value === 'boolean')\n        return boolToBytes(value, opts);\n    if ((0,_data_isHex_js__WEBPACK_IMPORTED_MODULE_0__.isHex)(value))\n        return hexToBytes(value, opts);\n    return stringToBytes(value, opts);\n}\n/**\n * Encodes a boolean into a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes#booltobytes\n *\n * @param value Boolean value to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { boolToBytes } from 'viem'\n * const data = boolToBytes(true)\n * // Uint8Array([1])\n *\n * @example\n * import { boolToBytes } from 'viem'\n * const data = boolToBytes(true, { size: 32 })\n * // Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1])\n */\nfunction boolToBytes(value, opts = {}) {\n    const bytes = new Uint8Array(1);\n    bytes[0] = Number(value);\n    if (typeof opts.size === 'number') {\n        (0,_fromHex_js__WEBPACK_IMPORTED_MODULE_1__.assertSize)(bytes, { size: opts.size });\n        return (0,_data_pad_js__WEBPACK_IMPORTED_MODULE_2__.pad)(bytes, { size: opts.size });\n    }\n    return bytes;\n}\n// We use very optimized technique to convert hex string to byte array\nconst charCodeMap = {\n    zero: 48,\n    nine: 57,\n    A: 65,\n    F: 70,\n    a: 97,\n    f: 102,\n};\nfunction charCodeToBase16(char) {\n    if (char >= charCodeMap.zero && char <= charCodeMap.nine)\n        return char - charCodeMap.zero;\n    if (char >= charCodeMap.A && char <= charCodeMap.F)\n        return char - (charCodeMap.A - 10);\n    if (char >= charCodeMap.a && char <= charCodeMap.f)\n        return char - (charCodeMap.a - 10);\n    return undefined;\n}\n/**\n * Encodes a hex string into a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes#hextobytes\n *\n * @param hex Hex string to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { hexToBytes } from 'viem'\n * const data = hexToBytes('0x48656c6c6f20776f726c6421')\n * // Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n *\n * @example\n * import { hexToBytes } from 'viem'\n * const data = hexToBytes('0x48656c6c6f20776f726c6421', { size: 32 })\n * // Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n */\nfunction hexToBytes(hex_, opts = {}) {\n    let hex = hex_;\n    if (opts.size) {\n        (0,_fromHex_js__WEBPACK_IMPORTED_MODULE_1__.assertSize)(hex, { size: opts.size });\n        hex = (0,_data_pad_js__WEBPACK_IMPORTED_MODULE_2__.pad)(hex, { dir: 'right', size: opts.size });\n    }\n    let hexString = hex.slice(2);\n    if (hexString.length % 2)\n        hexString = `0${hexString}`;\n    const length = hexString.length / 2;\n    const bytes = new Uint8Array(length);\n    for (let index = 0, j = 0; index < length; index++) {\n        const nibbleLeft = charCodeToBase16(hexString.charCodeAt(j++));\n        const nibbleRight = charCodeToBase16(hexString.charCodeAt(j++));\n        if (nibbleLeft === undefined || nibbleRight === undefined) {\n            throw new _errors_base_js__WEBPACK_IMPORTED_MODULE_3__.BaseError(`Invalid byte sequence (\"${hexString[j - 2]}${hexString[j - 1]}\" in \"${hexString}\").`);\n        }\n        bytes[index] = nibbleLeft * 16 + nibbleRight;\n    }\n    return bytes;\n}\n/**\n * Encodes a number into a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes#numbertobytes\n *\n * @param value Number to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { numberToBytes } from 'viem'\n * const data = numberToBytes(420)\n * // Uint8Array([1, 164])\n *\n * @example\n * import { numberToBytes } from 'viem'\n * const data = numberToBytes(420, { size: 4 })\n * // Uint8Array([0, 0, 1, 164])\n */\nfunction numberToBytes(value, opts) {\n    const hex = (0,_toHex_js__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(value, opts);\n    return hexToBytes(hex);\n}\n/**\n * Encodes a UTF-8 string into a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes#stringtobytes\n *\n * @param value String to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { stringToBytes } from 'viem'\n * const data = stringToBytes('Hello world!')\n * // Uint8Array([72, 101, 108, 108, 111, 32, 119, 111, 114, 108, 100, 33])\n *\n * @example\n * import { stringToBytes } from 'viem'\n * const data = stringToBytes('Hello world!', { size: 32 })\n * // Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n */\nfunction stringToBytes(value, opts = {}) {\n    const bytes = encoder.encode(value);\n    if (typeof opts.size === 'number') {\n        (0,_fromHex_js__WEBPACK_IMPORTED_MODULE_1__.assertSize)(bytes, { size: opts.size });\n        return (0,_data_pad_js__WEBPACK_IMPORTED_MODULE_2__.pad)(bytes, { dir: 'right', size: opts.size });\n    }\n    return bytes;\n}\n//# sourceMappingURL=toBytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/encoding/toBytes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js":
/*!********************************************************!*\
  !*** ./node_modules/viem/_esm/utils/encoding/toHex.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   boolToHex: () => (/* binding */ boolToHex),\n/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),\n/* harmony export */   numberToHex: () => (/* binding */ numberToHex),\n/* harmony export */   stringToHex: () => (/* binding */ stringToHex),\n/* harmony export */   toHex: () => (/* binding */ toHex)\n/* harmony export */ });\n/* harmony import */ var _errors_encoding_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../errors/encoding.js */ \"(ssr)/./node_modules/viem/_esm/errors/encoding.js\");\n/* harmony import */ var _data_pad_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../data/pad.js */ \"(ssr)/./node_modules/viem/_esm/utils/data/pad.js\");\n/* harmony import */ var _fromHex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fromHex.js */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n\n\n\nconst hexes = /*#__PURE__*/ Array.from({ length: 256 }, (_v, i) => i.toString(16).padStart(2, '0'));\n/**\n * Encodes a string, number, bigint, or ByteArray into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex\n * - Example: https://viem.sh/docs/utilities/toHex#usage\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { toHex } from 'viem'\n * const data = toHex('Hello world')\n * // '0x48656c6c6f20776f726c6421'\n *\n * @example\n * import { toHex } from 'viem'\n * const data = toHex(420)\n * // '0x1a4'\n *\n * @example\n * import { toHex } from 'viem'\n * const data = toHex('Hello world', { size: 32 })\n * // '0x48656c6c6f20776f726c64210000000000000000000000000000000000000000'\n */\nfunction toHex(value, opts = {}) {\n    if (typeof value === 'number' || typeof value === 'bigint')\n        return numberToHex(value, opts);\n    if (typeof value === 'string') {\n        return stringToHex(value, opts);\n    }\n    if (typeof value === 'boolean')\n        return boolToHex(value, opts);\n    return bytesToHex(value, opts);\n}\n/**\n * Encodes a boolean into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex#booltohex\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { boolToHex } from 'viem'\n * const data = boolToHex(true)\n * // '0x1'\n *\n * @example\n * import { boolToHex } from 'viem'\n * const data = boolToHex(false)\n * // '0x0'\n *\n * @example\n * import { boolToHex } from 'viem'\n * const data = boolToHex(true, { size: 32 })\n * // '0x0000000000000000000000000000000000000000000000000000000000000001'\n */\nfunction boolToHex(value, opts = {}) {\n    const hex = `0x${Number(value)}`;\n    if (typeof opts.size === 'number') {\n        (0,_fromHex_js__WEBPACK_IMPORTED_MODULE_0__.assertSize)(hex, { size: opts.size });\n        return (0,_data_pad_js__WEBPACK_IMPORTED_MODULE_1__.pad)(hex, { size: opts.size });\n    }\n    return hex;\n}\n/**\n * Encodes a bytes array into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex#bytestohex\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { bytesToHex } from 'viem'\n * const data = bytesToHex(Uint8Array.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n * // '0x48656c6c6f20576f726c6421'\n *\n * @example\n * import { bytesToHex } from 'viem'\n * const data = bytesToHex(Uint8Array.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]), { size: 32 })\n * // '0x48656c6c6f20576f726c64210000000000000000000000000000000000000000'\n */\nfunction bytesToHex(value, opts = {}) {\n    let string = '';\n    for (let i = 0; i < value.length; i++) {\n        string += hexes[value[i]];\n    }\n    const hex = `0x${string}`;\n    if (typeof opts.size === 'number') {\n        (0,_fromHex_js__WEBPACK_IMPORTED_MODULE_0__.assertSize)(hex, { size: opts.size });\n        return (0,_data_pad_js__WEBPACK_IMPORTED_MODULE_1__.pad)(hex, { dir: 'right', size: opts.size });\n    }\n    return hex;\n}\n/**\n * Encodes a number or bigint into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex#numbertohex\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { numberToHex } from 'viem'\n * const data = numberToHex(420)\n * // '0x1a4'\n *\n * @example\n * import { numberToHex } from 'viem'\n * const data = numberToHex(420, { size: 32 })\n * // '0x00000000000000000000000000000000000000000000000000000000000001a4'\n */\nfunction numberToHex(value_, opts = {}) {\n    const { signed, size } = opts;\n    const value = BigInt(value_);\n    let maxValue;\n    if (size) {\n        if (signed)\n            maxValue = (1n << (BigInt(size) * 8n - 1n)) - 1n;\n        else\n            maxValue = 2n ** (BigInt(size) * 8n) - 1n;\n    }\n    else if (typeof value_ === 'number') {\n        maxValue = BigInt(Number.MAX_SAFE_INTEGER);\n    }\n    const minValue = typeof maxValue === 'bigint' && signed ? -maxValue - 1n : 0;\n    if ((maxValue && value > maxValue) || value < minValue) {\n        const suffix = typeof value_ === 'bigint' ? 'n' : '';\n        throw new _errors_encoding_js__WEBPACK_IMPORTED_MODULE_2__.IntegerOutOfRangeError({\n            max: maxValue ? `${maxValue}${suffix}` : undefined,\n            min: `${minValue}${suffix}`,\n            signed,\n            size,\n            value: `${value_}${suffix}`,\n        });\n    }\n    const hex = `0x${(signed && value < 0 ? (1n << BigInt(size * 8)) + BigInt(value) : value).toString(16)}`;\n    if (size)\n        return (0,_data_pad_js__WEBPACK_IMPORTED_MODULE_1__.pad)(hex, { size });\n    return hex;\n}\nconst encoder = /*#__PURE__*/ new TextEncoder();\n/**\n * Encodes a UTF-8 string into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex#stringtohex\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { stringToHex } from 'viem'\n * const data = stringToHex('Hello World!')\n * // '0x48656c6c6f20576f726c6421'\n *\n * @example\n * import { stringToHex } from 'viem'\n * const data = stringToHex('Hello World!', { size: 32 })\n * // '0x48656c6c6f20576f726c64210000000000000000000000000000000000000000'\n */\nfunction stringToHex(value_, opts = {}) {\n    const value = encoder.encode(value_);\n    return bytesToHex(value, opts);\n}\n//# sourceMappingURL=toHex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/hash/keccak256.js":
/*!********************************************************!*\
  !*** ./node_modules/viem/_esm/utils/hash/keccak256.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   keccak256: () => (/* binding */ keccak256)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_sha3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/sha3 */ \"(ssr)/./node_modules/@noble/hashes/esm/sha3.js\");\n/* harmony import */ var _data_isHex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../data/isHex.js */ \"(ssr)/./node_modules/viem/_esm/utils/data/isHex.js\");\n/* harmony import */ var _encoding_toBytes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../encoding/toBytes.js */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toBytes.js\");\n/* harmony import */ var _encoding_toHex_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../encoding/toHex.js */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n\n\n\n\nfunction keccak256(value, to_) {\n    const to = to_ || 'hex';\n    const bytes = (0,_noble_hashes_sha3__WEBPACK_IMPORTED_MODULE_0__.keccak_256)((0,_data_isHex_js__WEBPACK_IMPORTED_MODULE_1__.isHex)(value, { strict: false }) ? (0,_encoding_toBytes_js__WEBPACK_IMPORTED_MODULE_2__.toBytes)(value) : value);\n    if (to === 'bytes')\n        return bytes;\n    return (0,_encoding_toHex_js__WEBPACK_IMPORTED_MODULE_3__.toHex)(bytes);\n}\n//# sourceMappingURL=keccak256.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2hhc2gva2VjY2FrMjU2LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQWdEO0FBQ1A7QUFDUTtBQUNKO0FBQ3RDO0FBQ1A7QUFDQSxrQkFBa0IsOERBQVUsQ0FBQyxxREFBSyxVQUFVLGVBQWUsSUFBSSw2REFBTztBQUN0RTtBQUNBO0FBQ0EsV0FBVyx5REFBSztBQUNoQjtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3ZpZW0vX2VzbS91dGlscy9oYXNoL2tlY2NhazI1Ni5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBrZWNjYWtfMjU2IH0gZnJvbSAnQG5vYmxlL2hhc2hlcy9zaGEzJztcbmltcG9ydCB7IGlzSGV4IH0gZnJvbSAnLi4vZGF0YS9pc0hleC5qcyc7XG5pbXBvcnQgeyB0b0J5dGVzIH0gZnJvbSAnLi4vZW5jb2RpbmcvdG9CeXRlcy5qcyc7XG5pbXBvcnQgeyB0b0hleCB9IGZyb20gJy4uL2VuY29kaW5nL3RvSGV4LmpzJztcbmV4cG9ydCBmdW5jdGlvbiBrZWNjYWsyNTYodmFsdWUsIHRvXykge1xuICAgIGNvbnN0IHRvID0gdG9fIHx8ICdoZXgnO1xuICAgIGNvbnN0IGJ5dGVzID0ga2VjY2FrXzI1Nihpc0hleCh2YWx1ZSwgeyBzdHJpY3Q6IGZhbHNlIH0pID8gdG9CeXRlcyh2YWx1ZSkgOiB2YWx1ZSk7XG4gICAgaWYgKHRvID09PSAnYnl0ZXMnKVxuICAgICAgICByZXR1cm4gYnl0ZXM7XG4gICAgcmV0dXJuIHRvSGV4KGJ5dGVzKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWtlY2NhazI1Ni5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/hash/keccak256.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/lru.js":
/*!*********************************************!*\
  !*** ./node_modules/viem/_esm/utils/lru.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LruMap: () => (/* binding */ LruMap)\n/* harmony export */ });\n/**\n * Map with a LRU (Least recently used) policy.\n *\n * @link https://en.wikipedia.org/wiki/Cache_replacement_policies#LRU\n */\nclass LruMap extends Map {\n    constructor(size) {\n        super();\n        Object.defineProperty(this, \"maxSize\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.maxSize = size;\n    }\n    get(key) {\n        const value = super.get(key);\n        if (super.has(key) && value !== undefined) {\n            this.delete(key);\n            super.set(key, value);\n        }\n        return value;\n    }\n    set(key, value) {\n        super.set(key, value);\n        if (this.maxSize && this.size > this.maxSize) {\n            const firstKey = this.keys().next().value;\n            if (firstKey)\n                this.delete(firstKey);\n        }\n        return this;\n    }\n}\n//# sourceMappingURL=lru.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL2xydS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3ZpZW0vX2VzbS91dGlscy9scnUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBNYXAgd2l0aCBhIExSVSAoTGVhc3QgcmVjZW50bHkgdXNlZCkgcG9saWN5LlxuICpcbiAqIEBsaW5rIGh0dHBzOi8vZW4ud2lraXBlZGlhLm9yZy93aWtpL0NhY2hlX3JlcGxhY2VtZW50X3BvbGljaWVzI0xSVVxuICovXG5leHBvcnQgY2xhc3MgTHJ1TWFwIGV4dGVuZHMgTWFwIHtcbiAgICBjb25zdHJ1Y3RvcihzaXplKSB7XG4gICAgICAgIHN1cGVyKCk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm1heFNpemVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IHZvaWQgMFxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5tYXhTaXplID0gc2l6ZTtcbiAgICB9XG4gICAgZ2V0KGtleSkge1xuICAgICAgICBjb25zdCB2YWx1ZSA9IHN1cGVyLmdldChrZXkpO1xuICAgICAgICBpZiAoc3VwZXIuaGFzKGtleSkgJiYgdmFsdWUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGhpcy5kZWxldGUoa2V5KTtcbiAgICAgICAgICAgIHN1cGVyLnNldChrZXksIHZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgfVxuICAgIHNldChrZXksIHZhbHVlKSB7XG4gICAgICAgIHN1cGVyLnNldChrZXksIHZhbHVlKTtcbiAgICAgICAgaWYgKHRoaXMubWF4U2l6ZSAmJiB0aGlzLnNpemUgPiB0aGlzLm1heFNpemUpIHtcbiAgICAgICAgICAgIGNvbnN0IGZpcnN0S2V5ID0gdGhpcy5rZXlzKCkubmV4dCgpLnZhbHVlO1xuICAgICAgICAgICAgaWYgKGZpcnN0S2V5KVxuICAgICAgICAgICAgICAgIHRoaXMuZGVsZXRlKGZpcnN0S2V5KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1scnUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/lru.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/promise/createBatchScheduler.js":
/*!**********************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/promise/createBatchScheduler.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBatchScheduler: () => (/* binding */ createBatchScheduler)\n/* harmony export */ });\n/* harmony import */ var _withResolvers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./withResolvers.js */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withResolvers.js\");\n\nconst schedulerCache = /*#__PURE__*/ new Map();\n/** @internal */\nfunction createBatchScheduler({ fn, id, shouldSplitBatch, wait = 0, sort, }) {\n    const exec = async () => {\n        const scheduler = getScheduler();\n        flush();\n        const args = scheduler.map(({ args }) => args);\n        if (args.length === 0)\n            return;\n        fn(args)\n            .then((data) => {\n            if (sort && Array.isArray(data))\n                data.sort(sort);\n            for (let i = 0; i < scheduler.length; i++) {\n                const { resolve } = scheduler[i];\n                resolve?.([data[i], data]);\n            }\n        })\n            .catch((err) => {\n            for (let i = 0; i < scheduler.length; i++) {\n                const { reject } = scheduler[i];\n                reject?.(err);\n            }\n        });\n    };\n    const flush = () => schedulerCache.delete(id);\n    const getBatchedArgs = () => getScheduler().map(({ args }) => args);\n    const getScheduler = () => schedulerCache.get(id) || [];\n    const setScheduler = (item) => schedulerCache.set(id, [...getScheduler(), item]);\n    return {\n        flush,\n        async schedule(args) {\n            const { promise, resolve, reject } = (0,_withResolvers_js__WEBPACK_IMPORTED_MODULE_0__.withResolvers)();\n            const split = shouldSplitBatch?.([...getBatchedArgs(), args]);\n            if (split)\n                exec();\n            const hasActiveScheduler = getScheduler().length > 0;\n            if (hasActiveScheduler) {\n                setScheduler({ args, resolve, reject });\n                return promise;\n            }\n            setScheduler({ args, resolve, reject });\n            setTimeout(exec, wait);\n            return promise;\n        },\n    };\n}\n//# sourceMappingURL=createBatchScheduler.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/promise/createBatchScheduler.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/promise/withDedupe.js":
/*!************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/promise/withDedupe.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   promiseCache: () => (/* binding */ promiseCache),\n/* harmony export */   withDedupe: () => (/* binding */ withDedupe)\n/* harmony export */ });\n/* harmony import */ var _lru_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lru.js */ \"(ssr)/./node_modules/viem/_esm/utils/lru.js\");\n\n/** @internal */\nconst promiseCache = /*#__PURE__*/ new _lru_js__WEBPACK_IMPORTED_MODULE_0__.LruMap(8192);\n/** Deduplicates in-flight promises. */\nfunction withDedupe(fn, { enabled = true, id }) {\n    if (!enabled || !id)\n        return fn();\n    if (promiseCache.get(id))\n        return promiseCache.get(id);\n    const promise = fn().finally(() => promiseCache.delete(id));\n    promiseCache.set(id, promise);\n    return promise;\n}\n//# sourceMappingURL=withDedupe.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL3Byb21pc2Uvd2l0aERlZHVwZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUM7QUFDbkM7QUFDTyx1Q0FBdUMsMkNBQU07QUFDcEQ7QUFDTywwQkFBMEIsb0JBQW9CO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy92aWVtL19lc20vdXRpbHMvcHJvbWlzZS93aXRoRGVkdXBlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IExydU1hcCB9IGZyb20gJy4uL2xydS5qcyc7XG4vKiogQGludGVybmFsICovXG5leHBvcnQgY29uc3QgcHJvbWlzZUNhY2hlID0gLyojX19QVVJFX18qLyBuZXcgTHJ1TWFwKDgxOTIpO1xuLyoqIERlZHVwbGljYXRlcyBpbi1mbGlnaHQgcHJvbWlzZXMuICovXG5leHBvcnQgZnVuY3Rpb24gd2l0aERlZHVwZShmbiwgeyBlbmFibGVkID0gdHJ1ZSwgaWQgfSkge1xuICAgIGlmICghZW5hYmxlZCB8fCAhaWQpXG4gICAgICAgIHJldHVybiBmbigpO1xuICAgIGlmIChwcm9taXNlQ2FjaGUuZ2V0KGlkKSlcbiAgICAgICAgcmV0dXJuIHByb21pc2VDYWNoZS5nZXQoaWQpO1xuICAgIGNvbnN0IHByb21pc2UgPSBmbigpLmZpbmFsbHkoKCkgPT4gcHJvbWlzZUNhY2hlLmRlbGV0ZShpZCkpO1xuICAgIHByb21pc2VDYWNoZS5zZXQoaWQsIHByb21pc2UpO1xuICAgIHJldHVybiBwcm9taXNlO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d2l0aERlZHVwZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/promise/withDedupe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/promise/withResolvers.js":
/*!***************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/promise/withResolvers.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withResolvers: () => (/* binding */ withResolvers)\n/* harmony export */ });\n/** @internal */\nfunction withResolvers() {\n    let resolve = () => undefined;\n    let reject = () => undefined;\n    const promise = new Promise((resolve_, reject_) => {\n        resolve = resolve_;\n        reject = reject_;\n    });\n    return { promise, resolve, reject };\n}\n//# sourceMappingURL=withResolvers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL3Byb21pc2Uvd2l0aFJlc29sdmVycy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsYUFBYTtBQUNiO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL3Byb21pc2Uvd2l0aFJlc29sdmVycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiogQGludGVybmFsICovXG5leHBvcnQgZnVuY3Rpb24gd2l0aFJlc29sdmVycygpIHtcbiAgICBsZXQgcmVzb2x2ZSA9ICgpID0+IHVuZGVmaW5lZDtcbiAgICBsZXQgcmVqZWN0ID0gKCkgPT4gdW5kZWZpbmVkO1xuICAgIGNvbnN0IHByb21pc2UgPSBuZXcgUHJvbWlzZSgocmVzb2x2ZV8sIHJlamVjdF8pID0+IHtcbiAgICAgICAgcmVzb2x2ZSA9IHJlc29sdmVfO1xuICAgICAgICByZWplY3QgPSByZWplY3RfO1xuICAgIH0pO1xuICAgIHJldHVybiB7IHByb21pc2UsIHJlc29sdmUsIHJlamVjdCB9O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d2l0aFJlc29sdmVycy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/promise/withResolvers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/promise/withRetry.js":
/*!***********************************************************!*\
  !*** ./node_modules/viem/_esm/utils/promise/withRetry.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withRetry: () => (/* binding */ withRetry)\n/* harmony export */ });\n/* harmony import */ var _wait_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../wait.js */ \"(ssr)/./node_modules/viem/_esm/utils/wait.js\");\n\nfunction withRetry(fn, { delay: delay_ = 100, retryCount = 2, shouldRetry = () => true, } = {}) {\n    return new Promise((resolve, reject) => {\n        const attemptRetry = async ({ count = 0 } = {}) => {\n            const retry = async ({ error }) => {\n                const delay = typeof delay_ === 'function' ? delay_({ count, error }) : delay_;\n                if (delay)\n                    await (0,_wait_js__WEBPACK_IMPORTED_MODULE_0__.wait)(delay);\n                attemptRetry({ count: count + 1 });\n            };\n            try {\n                const data = await fn();\n                resolve(data);\n            }\n            catch (err) {\n                if (count < retryCount &&\n                    (await shouldRetry({ count, error: err })))\n                    return retry({ error: err });\n                reject(err);\n            }\n        };\n        attemptRetry();\n    });\n}\n//# sourceMappingURL=withRetry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL3Byb21pc2Uvd2l0aFJldHJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtDO0FBQzNCLHlCQUF5QixpRUFBaUUsSUFBSTtBQUNyRztBQUNBLHNDQUFzQyxZQUFZLElBQUk7QUFDdEQsbUNBQW1DLE9BQU87QUFDMUMsc0VBQXNFLGNBQWM7QUFDcEY7QUFDQSwwQkFBMEIsOENBQUk7QUFDOUIsK0JBQStCLGtCQUFrQjtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QyxtQkFBbUI7QUFDNUQsbUNBQW1DLFlBQVk7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy92aWVtL19lc20vdXRpbHMvcHJvbWlzZS93aXRoUmV0cnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgd2FpdCB9IGZyb20gJy4uL3dhaXQuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIHdpdGhSZXRyeShmbiwgeyBkZWxheTogZGVsYXlfID0gMTAwLCByZXRyeUNvdW50ID0gMiwgc2hvdWxkUmV0cnkgPSAoKSA9PiB0cnVlLCB9ID0ge30pIHtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICBjb25zdCBhdHRlbXB0UmV0cnkgPSBhc3luYyAoeyBjb3VudCA9IDAgfSA9IHt9KSA9PiB7XG4gICAgICAgICAgICBjb25zdCByZXRyeSA9IGFzeW5jICh7IGVycm9yIH0pID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBkZWxheSA9IHR5cGVvZiBkZWxheV8gPT09ICdmdW5jdGlvbicgPyBkZWxheV8oeyBjb3VudCwgZXJyb3IgfSkgOiBkZWxheV87XG4gICAgICAgICAgICAgICAgaWYgKGRlbGF5KVxuICAgICAgICAgICAgICAgICAgICBhd2FpdCB3YWl0KGRlbGF5KTtcbiAgICAgICAgICAgICAgICBhdHRlbXB0UmV0cnkoeyBjb3VudDogY291bnQgKyAxIH0pO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGZuKCk7XG4gICAgICAgICAgICAgICAgcmVzb2x2ZShkYXRhKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgICAgICBpZiAoY291bnQgPCByZXRyeUNvdW50ICYmXG4gICAgICAgICAgICAgICAgICAgIChhd2FpdCBzaG91bGRSZXRyeSh7IGNvdW50LCBlcnJvcjogZXJyIH0pKSlcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJldHJ5KHsgZXJyb3I6IGVyciB9KTtcbiAgICAgICAgICAgICAgICByZWplY3QoZXJyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgICAgYXR0ZW1wdFJldHJ5KCk7XG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD13aXRoUmV0cnkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/promise/withRetry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js":
/*!*************************************************************!*\
  !*** ./node_modules/viem/_esm/utils/promise/withTimeout.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withTimeout: () => (/* binding */ withTimeout)\n/* harmony export */ });\nfunction withTimeout(fn, { errorInstance = new Error('timed out'), timeout, signal, }) {\n    return new Promise((resolve, reject) => {\n        ;\n        (async () => {\n            let timeoutId;\n            try {\n                const controller = new AbortController();\n                if (timeout > 0) {\n                    timeoutId = setTimeout(() => {\n                        if (signal) {\n                            controller.abort();\n                        }\n                        else {\n                            reject(errorInstance);\n                        }\n                    }, timeout); // need to cast because bun globals.d.ts overrides @types/node\n                }\n                resolve(await fn({ signal: controller?.signal || null }));\n            }\n            catch (err) {\n                if (err?.name === 'AbortError')\n                    reject(errorInstance);\n                reject(err);\n            }\n            finally {\n                clearTimeout(timeoutId);\n            }\n        })();\n    });\n}\n//# sourceMappingURL=withTimeout.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/rpc/http.js":
/*!**************************************************!*\
  !*** ./node_modules/viem/_esm/utils/rpc/http.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHttpRpcClient: () => (/* binding */ getHttpRpcClient)\n/* harmony export */ });\n/* harmony import */ var _errors_request_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../errors/request.js */ \"(ssr)/./node_modules/viem/_esm/errors/request.js\");\n/* harmony import */ var _promise_withTimeout_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../promise/withTimeout.js */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../stringify.js */ \"(ssr)/./node_modules/viem/_esm/utils/stringify.js\");\n/* harmony import */ var _id_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./id.js */ \"(ssr)/./node_modules/viem/_esm/utils/rpc/id.js\");\n\n\n\n\nfunction getHttpRpcClient(url, options = {}) {\n    return {\n        async request(params) {\n            const { body, onRequest = options.onRequest, onResponse = options.onResponse, timeout = options.timeout ?? 10_000, } = params;\n            const fetchOptions = {\n                ...(options.fetchOptions ?? {}),\n                ...(params.fetchOptions ?? {}),\n            };\n            const { headers, method, signal: signal_ } = fetchOptions;\n            try {\n                const response = await (0,_promise_withTimeout_js__WEBPACK_IMPORTED_MODULE_0__.withTimeout)(async ({ signal }) => {\n                    const init = {\n                        ...fetchOptions,\n                        body: Array.isArray(body)\n                            ? (0,_stringify_js__WEBPACK_IMPORTED_MODULE_1__.stringify)(body.map((body) => ({\n                                jsonrpc: '2.0',\n                                id: body.id ?? _id_js__WEBPACK_IMPORTED_MODULE_2__.idCache.take(),\n                                ...body,\n                            })))\n                            : (0,_stringify_js__WEBPACK_IMPORTED_MODULE_1__.stringify)({\n                                jsonrpc: '2.0',\n                                id: body.id ?? _id_js__WEBPACK_IMPORTED_MODULE_2__.idCache.take(),\n                                ...body,\n                            }),\n                        headers: {\n                            'Content-Type': 'application/json',\n                            ...headers,\n                        },\n                        method: method || 'POST',\n                        signal: signal_ || (timeout > 0 ? signal : null),\n                    };\n                    const request = new Request(url, init);\n                    const args = (await onRequest?.(request, init)) ?? { ...init, url };\n                    const response = await fetch(args.url ?? url, args);\n                    return response;\n                }, {\n                    errorInstance: new _errors_request_js__WEBPACK_IMPORTED_MODULE_3__.TimeoutError({ body, url }),\n                    timeout,\n                    signal: true,\n                });\n                if (onResponse)\n                    await onResponse(response);\n                let data;\n                if (response.headers.get('Content-Type')?.startsWith('application/json'))\n                    data = await response.json();\n                else {\n                    data = await response.text();\n                    try {\n                        data = JSON.parse(data || '{}');\n                    }\n                    catch (err) {\n                        if (response.ok)\n                            throw err;\n                        data = { error: data };\n                    }\n                }\n                if (!response.ok) {\n                    throw new _errors_request_js__WEBPACK_IMPORTED_MODULE_3__.HttpRequestError({\n                        body,\n                        details: (0,_stringify_js__WEBPACK_IMPORTED_MODULE_1__.stringify)(data.error) || response.statusText,\n                        headers: response.headers,\n                        status: response.status,\n                        url,\n                    });\n                }\n                return data;\n            }\n            catch (err) {\n                if (err instanceof _errors_request_js__WEBPACK_IMPORTED_MODULE_3__.HttpRequestError)\n                    throw err;\n                if (err instanceof _errors_request_js__WEBPACK_IMPORTED_MODULE_3__.TimeoutError)\n                    throw err;\n                throw new _errors_request_js__WEBPACK_IMPORTED_MODULE_3__.HttpRequestError({\n                    body,\n                    cause: err,\n                    url,\n                });\n            }\n        },\n    };\n}\n//# sourceMappingURL=http.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/rpc/http.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/rpc/id.js":
/*!************************************************!*\
  !*** ./node_modules/viem/_esm/utils/rpc/id.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   idCache: () => (/* binding */ idCache)\n/* harmony export */ });\nfunction createIdStore() {\n    return {\n        current: 0,\n        take() {\n            return this.current++;\n        },\n        reset() {\n            this.current = 0;\n        },\n    };\n}\nconst idCache = /*#__PURE__*/ createIdStore();\n//# sourceMappingURL=id.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL3JwYy9pZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDTztBQUNQIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3ZpZW0vX2VzbS91dGlscy9ycGMvaWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gY3JlYXRlSWRTdG9yZSgpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBjdXJyZW50OiAwLFxuICAgICAgICB0YWtlKCkge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuY3VycmVudCsrO1xuICAgICAgICB9LFxuICAgICAgICByZXNldCgpIHtcbiAgICAgICAgICAgIHRoaXMuY3VycmVudCA9IDA7XG4gICAgICAgIH0sXG4gICAgfTtcbn1cbmV4cG9ydCBjb25zdCBpZENhY2hlID0gLyojX19QVVJFX18qLyBjcmVhdGVJZFN0b3JlKCk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pZC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/rpc/id.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/stringify.js":
/*!***************************************************!*\
  !*** ./node_modules/viem/_esm/utils/stringify.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\nconst stringify = (value, replacer, space) => JSON.stringify(value, (key, value_) => {\n    const value = typeof value_ === 'bigint' ? value_.toString() : value_;\n    return typeof replacer === 'function' ? replacer(key, value) : value;\n}, space);\n//# sourceMappingURL=stringify.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL3N0cmluZ2lmeS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0EsQ0FBQztBQUNEIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3ZpZW0vX2VzbS91dGlscy9zdHJpbmdpZnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHN0cmluZ2lmeSA9ICh2YWx1ZSwgcmVwbGFjZXIsIHNwYWNlKSA9PiBKU09OLnN0cmluZ2lmeSh2YWx1ZSwgKGtleSwgdmFsdWVfKSA9PiB7XG4gICAgY29uc3QgdmFsdWUgPSB0eXBlb2YgdmFsdWVfID09PSAnYmlnaW50JyA/IHZhbHVlXy50b1N0cmluZygpIDogdmFsdWVfO1xuICAgIHJldHVybiB0eXBlb2YgcmVwbGFjZXIgPT09ICdmdW5jdGlvbicgPyByZXBsYWNlcihrZXksIHZhbHVlKSA6IHZhbHVlO1xufSwgc3BhY2UpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3RyaW5naWZ5LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/stringify.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/uid.js":
/*!*********************************************!*\
  !*** ./node_modules/viem/_esm/utils/uid.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uid: () => (/* binding */ uid)\n/* harmony export */ });\nconst size = 256;\nlet index = size;\nlet buffer;\nfunction uid(length = 11) {\n    if (!buffer || index + length > size * 2) {\n        buffer = '';\n        index = 0;\n        for (let i = 0; i < size; i++) {\n            buffer += ((256 + Math.random() * 256) | 0).toString(16).substring(1);\n        }\n    }\n    return buffer.substring(index, index++ + length);\n}\n//# sourceMappingURL=uid.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL3VpZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsVUFBVTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL3VpZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBzaXplID0gMjU2O1xubGV0IGluZGV4ID0gc2l6ZTtcbmxldCBidWZmZXI7XG5leHBvcnQgZnVuY3Rpb24gdWlkKGxlbmd0aCA9IDExKSB7XG4gICAgaWYgKCFidWZmZXIgfHwgaW5kZXggKyBsZW5ndGggPiBzaXplICogMikge1xuICAgICAgICBidWZmZXIgPSAnJztcbiAgICAgICAgaW5kZXggPSAwO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHNpemU7IGkrKykge1xuICAgICAgICAgICAgYnVmZmVyICs9ICgoMjU2ICsgTWF0aC5yYW5kb20oKSAqIDI1NikgfCAwKS50b1N0cmluZygxNikuc3Vic3RyaW5nKDEpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBidWZmZXIuc3Vic3RyaW5nKGluZGV4LCBpbmRleCsrICsgbGVuZ3RoKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXVpZC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/uid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/viem/_esm/utils/wait.js":
/*!**********************************************!*\
  !*** ./node_modules/viem/_esm/utils/wait.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wait: () => (/* binding */ wait)\n/* harmony export */ });\nasync function wait(time) {\n    return new Promise((res) => setTimeout(res, time));\n}\n//# sourceMappingURL=wait.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmllbS9fZXNtL3V0aWxzL3dhaXQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL3ZpZW0vX2VzbS91dGlscy93YWl0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBhc3luYyBmdW5jdGlvbiB3YWl0KHRpbWUpIHtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlcykgPT4gc2V0VGltZW91dChyZXMsIHRpbWUpKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdhaXQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/viem/_esm/utils/wait.js\n");

/***/ })

};
;