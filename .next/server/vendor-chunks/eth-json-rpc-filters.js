/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eth-json-rpc-filters";
exports.ids = ["vendor-chunks/eth-json-rpc-filters"];
exports.modules = {

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/base-filter-history.js":
/*!******************************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/base-filter-history.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const BaseFilter = __webpack_require__(/*! ./base-filter */ \"(ssr)/./node_modules/eth-json-rpc-filters/base-filter.js\")\n\n// tracks all results ever recorded\nclass BaseFilterWithHistory extends BaseFilter {\n\n  constructor () {\n    super()\n    this.allResults = []\n  }\n\n  async update () {\n    throw new Error('BaseFilterWithHistory - no update method specified')\n  }\n\n  addResults (newResults) {\n    this.allResults = this.allResults.concat(newResults)\n    super.addResults(newResults)\n  }\n\n  addInitialResults (newResults) {\n    this.allResults = this.allResults.concat(newResults)\n    super.addInitialResults(newResults)\n  }\n\n  getAllResults () {\n    return this.allResults\n  }\n\n}\n\nmodule.exports = BaseFilterWithHistory//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWpzb24tcnBjLWZpbHRlcnMvYmFzZS1maWx0ZXItaGlzdG9yeS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxtQkFBbUIsbUJBQU8sQ0FBQywrRUFBZTs7QUFFMUM7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL2V0aC1qc29uLXJwYy1maWx0ZXJzL2Jhc2UtZmlsdGVyLWhpc3RvcnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgQmFzZUZpbHRlciA9IHJlcXVpcmUoJy4vYmFzZS1maWx0ZXInKVxuXG4vLyB0cmFja3MgYWxsIHJlc3VsdHMgZXZlciByZWNvcmRlZFxuY2xhc3MgQmFzZUZpbHRlcldpdGhIaXN0b3J5IGV4dGVuZHMgQmFzZUZpbHRlciB7XG5cbiAgY29uc3RydWN0b3IgKCkge1xuICAgIHN1cGVyKClcbiAgICB0aGlzLmFsbFJlc3VsdHMgPSBbXVxuICB9XG5cbiAgYXN5bmMgdXBkYXRlICgpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0Jhc2VGaWx0ZXJXaXRoSGlzdG9yeSAtIG5vIHVwZGF0ZSBtZXRob2Qgc3BlY2lmaWVkJylcbiAgfVxuXG4gIGFkZFJlc3VsdHMgKG5ld1Jlc3VsdHMpIHtcbiAgICB0aGlzLmFsbFJlc3VsdHMgPSB0aGlzLmFsbFJlc3VsdHMuY29uY2F0KG5ld1Jlc3VsdHMpXG4gICAgc3VwZXIuYWRkUmVzdWx0cyhuZXdSZXN1bHRzKVxuICB9XG5cbiAgYWRkSW5pdGlhbFJlc3VsdHMgKG5ld1Jlc3VsdHMpIHtcbiAgICB0aGlzLmFsbFJlc3VsdHMgPSB0aGlzLmFsbFJlc3VsdHMuY29uY2F0KG5ld1Jlc3VsdHMpXG4gICAgc3VwZXIuYWRkSW5pdGlhbFJlc3VsdHMobmV3UmVzdWx0cylcbiAgfVxuXG4gIGdldEFsbFJlc3VsdHMgKCkge1xuICAgIHJldHVybiB0aGlzLmFsbFJlc3VsdHNcbiAgfVxuXG59XG5cbm1vZHVsZS5leHBvcnRzID0gQmFzZUZpbHRlcldpdGhIaXN0b3J5Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/base-filter-history.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/base-filter.js":
/*!**********************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/base-filter.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const SafeEventEmitter = (__webpack_require__(/*! @metamask/safe-event-emitter */ \"(ssr)/./node_modules/@metamask/safe-event-emitter/dist/cjs/index.js\")[\"default\"])\n\nclass BaseFilter extends SafeEventEmitter {\n\n  constructor () {\n    super()\n    this.updates = []\n  }\n\n  async initialize () {}\n\n  async update () {\n    throw new Error('BaseFilter - no update method specified')\n  }\n\n  addResults (newResults) {\n    this.updates = this.updates.concat(newResults)\n    newResults.forEach(result => this.emit('update', result))\n  }\n\n  addInitialResults (newResults) {}\n\n  getChangesAndClear () {\n    const updates = this.updates\n    this.updates = []\n    return updates\n  }\n  \n}\n\nmodule.exports = BaseFilter\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWpzb24tcnBjLWZpbHRlcnMvYmFzZS1maWx0ZXIuanMiLCJtYXBwaW5ncyI6IkFBQUEseUJBQXlCLDJJQUErQzs7QUFFeEU7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL2V0aC1qc29uLXJwYy1maWx0ZXJzL2Jhc2UtZmlsdGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IFNhZmVFdmVudEVtaXR0ZXIgPSByZXF1aXJlKCdAbWV0YW1hc2svc2FmZS1ldmVudC1lbWl0dGVyJykuZGVmYXVsdFxuXG5jbGFzcyBCYXNlRmlsdGVyIGV4dGVuZHMgU2FmZUV2ZW50RW1pdHRlciB7XG5cbiAgY29uc3RydWN0b3IgKCkge1xuICAgIHN1cGVyKClcbiAgICB0aGlzLnVwZGF0ZXMgPSBbXVxuICB9XG5cbiAgYXN5bmMgaW5pdGlhbGl6ZSAoKSB7fVxuXG4gIGFzeW5jIHVwZGF0ZSAoKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdCYXNlRmlsdGVyIC0gbm8gdXBkYXRlIG1ldGhvZCBzcGVjaWZpZWQnKVxuICB9XG5cbiAgYWRkUmVzdWx0cyAobmV3UmVzdWx0cykge1xuICAgIHRoaXMudXBkYXRlcyA9IHRoaXMudXBkYXRlcy5jb25jYXQobmV3UmVzdWx0cylcbiAgICBuZXdSZXN1bHRzLmZvckVhY2gocmVzdWx0ID0+IHRoaXMuZW1pdCgndXBkYXRlJywgcmVzdWx0KSlcbiAgfVxuXG4gIGFkZEluaXRpYWxSZXN1bHRzIChuZXdSZXN1bHRzKSB7fVxuXG4gIGdldENoYW5nZXNBbmRDbGVhciAoKSB7XG4gICAgY29uc3QgdXBkYXRlcyA9IHRoaXMudXBkYXRlc1xuICAgIHRoaXMudXBkYXRlcyA9IFtdXG4gICAgcmV0dXJuIHVwZGF0ZXNcbiAgfVxuICBcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBCYXNlRmlsdGVyXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/base-filter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/block-filter.js":
/*!***********************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/block-filter.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const BaseFilter = __webpack_require__(/*! ./base-filter */ \"(ssr)/./node_modules/eth-json-rpc-filters/base-filter.js\")\nconst getBlocksForRange = __webpack_require__(/*! ./getBlocksForRange */ \"(ssr)/./node_modules/eth-json-rpc-filters/getBlocksForRange.js\")\nconst { incrementHexInt } = __webpack_require__(/*! ./hexUtils */ \"(ssr)/./node_modules/eth-json-rpc-filters/hexUtils.js\")\n\nclass BlockFilter extends BaseFilter {\n\n  constructor ({ provider, params }) {\n    super()\n    this.type = 'block'\n    this.provider = provider\n  }\n\n  async update ({ oldBlock, newBlock }) {\n    const toBlock = newBlock\n    const fromBlock = incrementHexInt(oldBlock)\n    const blockBodies = await getBlocksForRange({ provider: this.provider, fromBlock, toBlock })\n    const blockHashes = blockBodies.map((block) => block.hash)\n    this.addResults(blockHashes)\n  }\n\n}\n\nmodule.exports = BlockFilter\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWpzb24tcnBjLWZpbHRlcnMvYmxvY2stZmlsdGVyLmpzIiwibWFwcGluZ3MiOiJBQUFBLG1CQUFtQixtQkFBTyxDQUFDLCtFQUFlO0FBQzFDLDBCQUEwQixtQkFBTyxDQUFDLDJGQUFxQjtBQUN2RCxRQUFRLGtCQUFrQixFQUFFLG1CQUFPLENBQUMseUVBQVk7O0FBRWhEOztBQUVBLGlCQUFpQixrQkFBa0I7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsa0JBQWtCLG9CQUFvQjtBQUN0QztBQUNBO0FBQ0Esa0RBQWtELDZDQUE2QztBQUMvRjtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvZXRoLWpzb24tcnBjLWZpbHRlcnMvYmxvY2stZmlsdGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IEJhc2VGaWx0ZXIgPSByZXF1aXJlKCcuL2Jhc2UtZmlsdGVyJylcbmNvbnN0IGdldEJsb2Nrc0ZvclJhbmdlID0gcmVxdWlyZSgnLi9nZXRCbG9ja3NGb3JSYW5nZScpXG5jb25zdCB7IGluY3JlbWVudEhleEludCB9ID0gcmVxdWlyZSgnLi9oZXhVdGlscycpXG5cbmNsYXNzIEJsb2NrRmlsdGVyIGV4dGVuZHMgQmFzZUZpbHRlciB7XG5cbiAgY29uc3RydWN0b3IgKHsgcHJvdmlkZXIsIHBhcmFtcyB9KSB7XG4gICAgc3VwZXIoKVxuICAgIHRoaXMudHlwZSA9ICdibG9jaydcbiAgICB0aGlzLnByb3ZpZGVyID0gcHJvdmlkZXJcbiAgfVxuXG4gIGFzeW5jIHVwZGF0ZSAoeyBvbGRCbG9jaywgbmV3QmxvY2sgfSkge1xuICAgIGNvbnN0IHRvQmxvY2sgPSBuZXdCbG9ja1xuICAgIGNvbnN0IGZyb21CbG9jayA9IGluY3JlbWVudEhleEludChvbGRCbG9jaylcbiAgICBjb25zdCBibG9ja0JvZGllcyA9IGF3YWl0IGdldEJsb2Nrc0ZvclJhbmdlKHsgcHJvdmlkZXI6IHRoaXMucHJvdmlkZXIsIGZyb21CbG9jaywgdG9CbG9jayB9KVxuICAgIGNvbnN0IGJsb2NrSGFzaGVzID0gYmxvY2tCb2RpZXMubWFwKChibG9jaykgPT4gYmxvY2suaGFzaClcbiAgICB0aGlzLmFkZFJlc3VsdHMoYmxvY2tIYXNoZXMpXG4gIH1cblxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IEJsb2NrRmlsdGVyXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/block-filter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/getBlocksForRange.js":
/*!****************************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/getBlocksForRange.js ***!
  \****************************************************************/
/***/ ((module) => {

eval("module.exports = getBlocksForRange\n\nasync function getBlocksForRange({ provider, fromBlock, toBlock }) {\n  if (!fromBlock) fromBlock = toBlock\n\n  const fromBlockNumber = hexToInt(fromBlock)\n  const toBlockNumber = hexToInt(toBlock)\n  const blockCountToQuery = toBlockNumber - fromBlockNumber + 1\n  // load all blocks from old to new (inclusive)\n  const missingBlockNumbers = Array(blockCountToQuery).fill()\n                              .map((_,index) => fromBlockNumber + index)\n                              .map(intToHex)\n  let blockBodies = await Promise.all(\n    missingBlockNumbers.map(blockNum => query(provider, 'eth_getBlockByNumber', [blockNum, false]))\n  )\n  blockBodies = blockBodies.filter(block => block !== null);\n  return blockBodies\n}\n\nfunction hexToInt(hexString) {\n  if (hexString === undefined || hexString === null) return hexString\n  return Number.parseInt(hexString, 16)\n}\n\nfunction incrementHexInt(hexString){\n  if (hexString === undefined || hexString === null) return hexString\n  const value = hexToInt(hexString)\n  return intToHex(value + 1)\n}\n\nfunction intToHex(int) {\n  if (int === undefined || int === null) return int\n  const hexString = int.toString(16)\n  return '0x' + hexString\n}\n\nfunction sendAsync(provider, request) {\n  return new Promise((resolve, reject) => {\n    provider.sendAsync(request, (error, response) => {\n      if (error) {\n        reject(error);\n      } else if (response.error) {\n        reject(response.error);\n      } else if (response.result) {\n        resolve(response.result);\n      } else {\n        reject(new Error(\"Result was empty\"));\n      }\n    });\n  });\n}\n\nasync function query(provider, method, params) {\n  for (let i = 0; i < 3; i++) {\n    try {\n      return await sendAsync(provider, {\n        id: 1,\n        jsonrpc: \"2.0\",\n        method,\n        params,\n      });\n    } catch (error) {\n      console.error(\n        `provider.sendAsync failed: ${error.stack || error.message || error}`\n      );\n    }\n  }\n  return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/getBlocksForRange.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/hexUtils.js":
/*!*******************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/hexUtils.js ***!
  \*******************************************************/
/***/ ((module) => {

eval("\nmodule.exports = {\n  minBlockRef,\n  maxBlockRef,\n  sortBlockRefs,\n  bnToHex,\n  blockRefIsNumber,\n  hexToInt,\n  incrementHexInt,\n  intToHex,\n  unsafeRandomBytes,\n}\n\nfunction minBlockRef(...refs) {\n  const sortedRefs = sortBlockRefs(refs)\n  return sortedRefs[0]\n}\n\nfunction maxBlockRef(...refs) {\n  const sortedRefs = sortBlockRefs(refs)\n  return sortedRefs[sortedRefs.length-1]\n}\n\nfunction sortBlockRefs(refs) {\n  return refs.sort((refA, refB) => {\n    if (refA === 'latest' || refB === 'earliest') return 1\n    if (refB === 'latest' || refA === 'earliest') return -1\n    return hexToInt(refA) - hexToInt(refB)\n  })\n}\n\nfunction bnToHex(bn) {\n  return '0x' + bn.toString(16)\n}\n\nfunction blockRefIsNumber(blockRef){\n  return blockRef && !['earliest', 'latest', 'pending'].includes(blockRef)\n}\n\nfunction hexToInt(hexString) {\n  if (hexString === undefined || hexString === null) return hexString\n  return Number.parseInt(hexString, 16)\n}\n\nfunction incrementHexInt(hexString){\n  if (hexString === undefined || hexString === null) return hexString\n  const value = hexToInt(hexString)\n  return intToHex(value + 1)\n}\n\nfunction intToHex(int) {\n  if (int === undefined || int === null) return int\n  let hexString = int.toString(16)\n  const needsLeftPad = hexString.length % 2\n  if (needsLeftPad) hexString = '0' + hexString\n  return '0x' + hexString\n}\n\nfunction unsafeRandomBytes(byteCount) {\n  let result = '0x'\n  for (let i = 0; i < byteCount; i++) {\n    result += unsafeRandomNibble()\n    result += unsafeRandomNibble()\n  }\n  return result\n}\n\nfunction unsafeRandomNibble() {\n  return Math.floor(Math.random() * 16).toString(16)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/hexUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/index.js":
/*!****************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Mutex = (__webpack_require__(/*! async-mutex */ \"(ssr)/./node_modules/async-mutex/lib/index.js\").Mutex)\nconst { createAsyncMiddleware, createScaffoldMiddleware } = __webpack_require__(/*! json-rpc-engine */ \"(ssr)/./node_modules/json-rpc-engine/dist/index.js\")\nconst LogFilter = __webpack_require__(/*! ./log-filter.js */ \"(ssr)/./node_modules/eth-json-rpc-filters/log-filter.js\")\nconst BlockFilter = __webpack_require__(/*! ./block-filter.js */ \"(ssr)/./node_modules/eth-json-rpc-filters/block-filter.js\")\nconst TxFilter = __webpack_require__(/*! ./tx-filter.js */ \"(ssr)/./node_modules/eth-json-rpc-filters/tx-filter.js\")\nconst { intToHex, hexToInt } = __webpack_require__(/*! ./hexUtils */ \"(ssr)/./node_modules/eth-json-rpc-filters/hexUtils.js\")\n\nmodule.exports = createEthFilterMiddleware\n\nfunction createEthFilterMiddleware({ blockTracker, provider }) {\n\n  // create filter collection\n  let filterIndex = 0\n  let filters = {}\n  // create update mutex\n  const mutex = new Mutex()\n  const waitForFree = mutexMiddlewareWrapper({ mutex })\n\n  const middleware = createScaffoldMiddleware({\n    // install filters\n    eth_newFilter:                   waitForFree(toFilterCreationMiddleware(newLogFilter)),\n    eth_newBlockFilter:              waitForFree(toFilterCreationMiddleware(newBlockFilter)),\n    eth_newPendingTransactionFilter: waitForFree(toFilterCreationMiddleware(newPendingTransactionFilter)),\n    // uninstall filters\n    eth_uninstallFilter:             waitForFree(toAsyncRpcMiddleware(uninstallFilterHandler)),\n    // checking filter changes\n    eth_getFilterChanges:            waitForFree(toAsyncRpcMiddleware(getFilterChanges)),\n    eth_getFilterLogs:               waitForFree(toAsyncRpcMiddleware(getFilterLogs)),\n  })\n\n  // setup filter updating and destroy handler\n  const filterUpdater = async ({ oldBlock, newBlock }) => {\n    if (filters.length === 0) return\n    // lock update reads\n    const releaseLock = await mutex.acquire()\n    try {\n      // process all filters in parallel\n      await Promise.all(objValues(filters).map(async (filter) => {\n        try {\n         await filter.update({ oldBlock, newBlock })\n        } catch (err) {\n          // handle each error individually so filter update errors don't affect other filters\n          console.error(err)\n        }\n      }))\n    } catch (err) {\n      // log error so we don't skip the releaseLock\n      console.error(err)\n    }\n    // unlock update reads\n    releaseLock()\n  }\n\n  // expose filter methods directly\n  middleware.newLogFilter = newLogFilter\n  middleware.newBlockFilter = newBlockFilter\n  middleware.newPendingTransactionFilter = newPendingTransactionFilter\n  middleware.uninstallFilter = uninstallFilterHandler\n  middleware.getFilterChanges = getFilterChanges\n  middleware.getFilterLogs = getFilterLogs\n\n  // expose destroy method for cleanup\n  middleware.destroy = () => {\n    uninstallAllFilters()\n  }\n\n  return middleware\n\n  //\n  // new filters\n  //\n\n  async function newLogFilter(params) {\n    const filter = new LogFilter({ provider, params })\n    const filterIndex = await installFilter(filter)\n    return filter\n  }\n\n  async function newBlockFilter() {\n    const filter = new BlockFilter({ provider })\n    const filterIndex = await installFilter(filter)\n    return filter\n  }\n\n  async function newPendingTransactionFilter() {\n    const filter = new TxFilter({ provider })\n    const filterIndex = await installFilter(filter)\n    return filter\n  }\n\n  //\n  // get filter changes\n  //\n\n  async function getFilterChanges(filterIndexHex) {\n    const filterIndex = hexToInt(filterIndexHex)\n    const filter = filters[filterIndex]\n    if (!filter) {\n      throw new Error(`No filter for index \"${filterIndex}\"`)\n    }\n    const results = filter.getChangesAndClear()\n    return results\n  }\n\n  async function getFilterLogs(filterIndexHex) {\n    const filterIndex = hexToInt(filterIndexHex)\n    const filter = filters[filterIndex]\n    if (!filter) {\n      throw new Error(`No filter for index \"${filterIndex}\"`)\n    }\n    // only return results for log filters\n    let results = []\n    if (filter.type === 'log') {\n      results = filter.getAllResults()\n    }\n    return results\n  }\n\n\n  //\n  // remove filters\n  //\n\n\n  async function uninstallFilterHandler(filterIndexHex) {\n    // check filter exists\n    const filterIndex = hexToInt(filterIndexHex)\n    const filter = filters[filterIndex]\n    const result = Boolean(filter)\n    // uninstall filter\n    if (result) {\n      await uninstallFilter(filterIndex)\n    }\n    return result\n  }\n\n  //\n  // utils\n  //\n\n  async function installFilter(filter) {\n    const prevFilterCount = objValues(filters).length\n    // install filter\n    const currentBlock = await blockTracker.getLatestBlock()\n    await filter.initialize({ currentBlock })\n    filterIndex++\n    filters[filterIndex] = filter\n    filter.id = filterIndex\n    filter.idHex = intToHex(filterIndex)\n    // update block tracker subs\n    const newFilterCount = objValues(filters).length\n    updateBlockTrackerSubs({ prevFilterCount, newFilterCount })\n    return filterIndex\n  }\n\n  async function uninstallFilter(filterIndex) {\n    const prevFilterCount = objValues(filters).length\n    delete filters[filterIndex]\n    // update block tracker subs\n    const newFilterCount = objValues(filters).length\n    updateBlockTrackerSubs({ prevFilterCount, newFilterCount })\n  }\n\n  async function uninstallAllFilters() {\n    const prevFilterCount = objValues(filters).length\n    filters = {}\n    // update block tracker subs\n    updateBlockTrackerSubs({ prevFilterCount, newFilterCount: 0 })\n  }\n\n  function updateBlockTrackerSubs({ prevFilterCount, newFilterCount }) {\n    // subscribe\n    if (prevFilterCount === 0 && newFilterCount > 0) {\n      blockTracker.on('sync', filterUpdater)\n      return\n    }\n    // unsubscribe\n    if (prevFilterCount > 0 && newFilterCount === 0) {\n      blockTracker.removeListener('sync', filterUpdater)\n      return\n    }\n  }\n\n}\n\n// helper for turning filter constructors into rpc middleware\nfunction toFilterCreationMiddleware(createFilterFn) {\n  return toAsyncRpcMiddleware(async (...args) => {\n    const filter = await createFilterFn(...args)\n    const result = intToHex(filter.id)\n    return result\n  })\n}\n\n// helper for pulling out req.params and setting res.result\nfunction toAsyncRpcMiddleware(asyncFn) {\n  return createAsyncMiddleware(async (req, res) => {\n    const result = await asyncFn.apply(null, req.params)\n    res.result = result\n  })\n}\n\nfunction mutexMiddlewareWrapper({ mutex }) {\n  return (middleware) => {\n    return async (req, res, next, end) => {\n      // wait for mutex available\n      // we can release immediately because\n      // we just need to make sure updates aren't active\n      const releaseLock = await mutex.acquire()\n      releaseLock()\n      middleware(req, res, next, end)\n    }\n  }\n}\n\nfunction objValues(obj, fn){\n  const values = []\n  for (let key in obj) {\n    values.push(obj[key])\n  }\n  return values\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/log-filter.js":
/*!*********************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/log-filter.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const EthQuery = __webpack_require__(/*! eth-query */ \"(ssr)/./node_modules/eth-query/index.js\")\nconst pify = __webpack_require__(/*! pify */ \"(ssr)/./node_modules/eth-json-rpc-filters/node_modules/pify/index.js\")\nconst BaseFilterWithHistory = __webpack_require__(/*! ./base-filter-history */ \"(ssr)/./node_modules/eth-json-rpc-filters/base-filter-history.js\")\nconst { bnToHex, hexToInt, incrementHexInt, minBlockRef, blockRefIsNumber } = __webpack_require__(/*! ./hexUtils */ \"(ssr)/./node_modules/eth-json-rpc-filters/hexUtils.js\")\n\nclass LogFilter extends BaseFilterWithHistory {\n\n  constructor ({ provider, params }) {\n    super()\n    this.type = 'log'\n    this.ethQuery = new EthQuery(provider)\n    this.params = Object.assign({\n      fromBlock: 'latest',\n      toBlock: 'latest',\n      address: undefined,\n      topics: [],\n    }, params)\n    // normalize address parameter\n    if (this.params.address) {\n      // ensure array\n      if (!Array.isArray(this.params.address)) {\n        this.params.address = [this.params.address]\n      }\n      // ensure lowercase\n      this.params.address = this.params.address.map(address => address.toLowerCase())\n    }\n  }\n\n  async initialize({ currentBlock }) {\n    // resolve params.fromBlock\n    let fromBlock = this.params.fromBlock\n    if (['latest', 'pending'].includes(fromBlock)) fromBlock = currentBlock\n    if ('earliest' === fromBlock) fromBlock = '0x0'\n    this.params.fromBlock = fromBlock\n    // set toBlock for initial lookup\n    const toBlock = minBlockRef(this.params.toBlock, currentBlock)\n    const params = Object.assign({}, this.params, { toBlock })\n    // fetch logs and add to results\n    const newLogs = await this._fetchLogs(params)\n    this.addInitialResults(newLogs)\n  }\n\n  async update ({ oldBlock, newBlock }) {\n    // configure params for this update\n    const toBlock = newBlock\n    let fromBlock\n    // oldBlock is empty on first sync\n    if (oldBlock) {\n      fromBlock = incrementHexInt(oldBlock)\n    } else {\n      fromBlock = newBlock\n    }\n    // fetch logs\n    const params = Object.assign({}, this.params, { fromBlock, toBlock })\n    const newLogs = await this._fetchLogs(params)\n    const matchingLogs = newLogs.filter(log => this.matchLog(log))\n\n    // add to results\n    this.addResults(matchingLogs)\n  }\n\n  async _fetchLogs (params) {\n    const newLogs = await pify(cb => this.ethQuery.getLogs(params, cb))()\n    // add to results\n    return newLogs\n  }\n\n  matchLog(log) {\n    // check if block number in bounds:\n    if (hexToInt(this.params.fromBlock) >= hexToInt(log.blockNumber)) return false\n    if (blockRefIsNumber(this.params.toBlock) && hexToInt(this.params.toBlock) <= hexToInt(log.blockNumber)) return false\n\n    // address is correct:\n    const normalizedLogAddress = log.address && log.address.toLowerCase()\n    if (this.params.address && normalizedLogAddress && !this.params.address.includes(normalizedLogAddress)) return false\n\n    // topics match:\n    // topics are position-dependant\n    // topics can be nested to represent `or` [[a || b], c]\n    // topics can be null, representing a wild card for that position\n    const topicsMatch = this.params.topics.every((topicPattern, index) => {\n      // pattern is longer than actual topics\n      let logTopic = log.topics[index]\n      if (!logTopic) return false\n      logTopic = logTopic.toLowerCase()\n      // normalize subTopics\n      let subtopicsToMatch = Array.isArray(topicPattern) ? topicPattern : [topicPattern]\n      // check for wild card\n      const subtopicsIncludeWildcard = subtopicsToMatch.includes(null)\n      if (subtopicsIncludeWildcard) return true\n      subtopicsToMatch = subtopicsToMatch.map(topic => topic.toLowerCase())\n      // check each possible matching topic\n      const topicDoesMatch = subtopicsToMatch.includes(logTopic)\n      return topicDoesMatch\n    })\n\n    return topicsMatch\n  }\n\n}\n\nmodule.exports = LogFilter\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/log-filter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/node_modules/pify/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/node_modules/pify/index.js ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\nconst processFn = (fn, options, proxy, unwrapped) => function (...arguments_) {\n\tconst P = options.promiseModule;\n\n\treturn new P((resolve, reject) => {\n\t\tif (options.multiArgs) {\n\t\t\targuments_.push((...result) => {\n\t\t\t\tif (options.errorFirst) {\n\t\t\t\t\tif (result[0]) {\n\t\t\t\t\t\treject(result);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresult.shift();\n\t\t\t\t\t\tresolve(result);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tresolve(result);\n\t\t\t\t}\n\t\t\t});\n\t\t} else if (options.errorFirst) {\n\t\t\targuments_.push((error, result) => {\n\t\t\t\tif (error) {\n\t\t\t\t\treject(error);\n\t\t\t\t} else {\n\t\t\t\t\tresolve(result);\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\targuments_.push(resolve);\n\t\t}\n\n\t\tconst self = this === proxy ? unwrapped : this;\n\t\tReflect.apply(fn, self, arguments_);\n\t});\n};\n\nconst filterCache = new WeakMap();\n\nmodule.exports = (input, options) => {\n\toptions = {\n\t\texclude: [/.+(?:Sync|Stream)$/],\n\t\terrorFirst: true,\n\t\tpromiseModule: Promise,\n\t\t...options\n\t};\n\n\tconst objectType = typeof input;\n\tif (!(input !== null && (objectType === 'object' || objectType === 'function'))) {\n\t\tthrow new TypeError(`Expected \\`input\\` to be a \\`Function\\` or \\`Object\\`, got \\`${input === null ? 'null' : objectType}\\``);\n\t}\n\n\tconst filter = (target, key) => {\n\t\tlet cached = filterCache.get(target);\n\n\t\tif (!cached) {\n\t\t\tcached = {};\n\t\t\tfilterCache.set(target, cached);\n\t\t}\n\n\t\tif (key in cached) {\n\t\t\treturn cached[key];\n\t\t}\n\n\t\tconst match = pattern => (typeof pattern === 'string' || typeof key === 'symbol') ? key === pattern : pattern.test(key);\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(target, key);\n\t\tconst writableOrConfigurableOwn = (desc === undefined || desc.writable || desc.configurable);\n\t\tconst included = options.include ? options.include.some(match) : !options.exclude.some(match);\n\t\tconst shouldFilter = included && writableOrConfigurableOwn;\n\t\tcached[key] = shouldFilter;\n\t\treturn shouldFilter;\n\t};\n\n\tconst cache = new WeakMap();\n\n\tconst proxy = new Proxy(input, {\n\t\tapply(target, thisArg, args) {\n\t\t\tconst cached = cache.get(target);\n\n\t\t\tif (cached) {\n\t\t\t\treturn Reflect.apply(cached, thisArg, args);\n\t\t\t}\n\n\t\t\tconst pified = options.excludeMain ? target : processFn(target, options, proxy, target);\n\t\t\tcache.set(target, pified);\n\t\t\treturn Reflect.apply(pified, thisArg, args);\n\t\t},\n\n\t\tget(target, key) {\n\t\t\tconst property = target[key];\n\n\t\t\t// eslint-disable-next-line no-use-extend-native/no-use-extend-native\n\t\t\tif (!filter(target, key) || property === Function.prototype[key]) {\n\t\t\t\treturn property;\n\t\t\t}\n\n\t\t\tconst cached = cache.get(property);\n\n\t\t\tif (cached) {\n\t\t\t\treturn cached;\n\t\t\t}\n\n\t\t\tif (typeof property === 'function') {\n\t\t\t\tconst pified = processFn(property, options, proxy, target);\n\t\t\t\tcache.set(property, pified);\n\t\t\t\treturn pified;\n\t\t\t}\n\n\t\t\treturn property;\n\t\t}\n\t});\n\n\treturn proxy;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/node_modules/pify/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/subscriptionManager.js":
/*!******************************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/subscriptionManager.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const SafeEventEmitter = (__webpack_require__(/*! @metamask/safe-event-emitter */ \"(ssr)/./node_modules/@metamask/safe-event-emitter/dist/cjs/index.js\")[\"default\"])\nconst { createAsyncMiddleware, createScaffoldMiddleware } = __webpack_require__(/*! json-rpc-engine */ \"(ssr)/./node_modules/json-rpc-engine/dist/index.js\")\nconst createFilterMiddleware = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/eth-json-rpc-filters/index.js\")\nconst { unsafeRandomBytes, incrementHexInt } = __webpack_require__(/*! ./hexUtils.js */ \"(ssr)/./node_modules/eth-json-rpc-filters/hexUtils.js\")\nconst getBlocksForRange = __webpack_require__(/*! ./getBlocksForRange.js */ \"(ssr)/./node_modules/eth-json-rpc-filters/getBlocksForRange.js\")\n\nmodule.exports = createSubscriptionMiddleware\n\n\nfunction createSubscriptionMiddleware({ blockTracker, provider }) {\n  // state and utilities for handling subscriptions\n  const subscriptions = {}\n  const filterManager = createFilterMiddleware({ blockTracker, provider })\n\n  // internal flag\n  let isDestroyed = false\n\n  // create subscriptionManager api object\n  const events = new SafeEventEmitter()\n  const middleware = createScaffoldMiddleware({\n    eth_subscribe: createAsyncMiddleware(subscribe),\n    eth_unsubscribe: createAsyncMiddleware(unsubscribe),\n  })\n  middleware.destroy = destroy\n  return { events, middleware }\n\n  async function subscribe(req, res) {\n\n    if (isDestroyed) throw new Error(\n      'SubscriptionManager - attempting to use after destroying'\n    )\n\n    const subscriptionType = req.params[0]\n    // subId is 16 byte hex string\n    const subId = unsafeRandomBytes(16)\n\n    // create sub\n    let sub\n    switch (subscriptionType) {\n      case 'newHeads':\n        sub = createSubNewHeads({ subId })\n        break\n      case 'logs':\n        const filterParams = req.params[1]\n        const filter = await filterManager.newLogFilter(filterParams)\n        sub = createSubFromFilter({ subId, filter })\n        break\n      default:\n        throw new Error(`SubscriptionManager - unsupported subscription type \"${subscriptionType}\"`)\n\n    }\n    subscriptions[subId] = sub\n\n    res.result = subId\n    return\n\n    function createSubNewHeads({ subId }) {\n      const sub = {\n        type: subscriptionType,\n        destroy: async () => {\n          blockTracker.removeListener('sync', sub.update)\n        },\n        update: async ({ oldBlock, newBlock }) => {\n          // for newHeads\n          const toBlock = newBlock\n          const fromBlock = incrementHexInt(oldBlock)\n          const rawBlocks = await getBlocksForRange({ provider, fromBlock, toBlock })\n          const results = rawBlocks.map(normalizeBlock).filter(block => block !== null)\n          results.forEach((value) => {\n            _emitSubscriptionResult(subId, value)\n          })\n        }\n      }\n      // check for subscription updates on new block\n      blockTracker.on('sync', sub.update)\n      return sub\n    }\n\n    function createSubFromFilter({ subId, filter }) {\n      filter.on('update', result => _emitSubscriptionResult(subId, result))\n      const sub = {\n        type: subscriptionType,\n        destroy: async () => {\n          return await filterManager.uninstallFilter(filter.idHex)\n        },\n      }\n      return sub\n    }\n  }\n\n  async function unsubscribe(req, res) {\n\n    if (isDestroyed) throw new Error(\n      'SubscriptionManager - attempting to use after destroying'\n    )\n\n    const id = req.params[0]\n    const subscription = subscriptions[id]\n    // if missing, return \"false\" to indicate it was not removed\n    if (!subscription) {\n      res.result = false\n      return\n    }\n    // cleanup subscription\n    delete subscriptions[id]\n    await subscription.destroy()\n    res.result = true\n  }\n\n  function _emitSubscriptionResult(filterIdHex, value) {\n    events.emit('notification', {\n      jsonrpc: '2.0',\n      method: 'eth_subscription',\n      params: {\n        subscription: filterIdHex,\n        result: value,\n      },\n    })\n  }\n\n  function destroy() {\n    events.removeAllListeners()\n    for (const id in subscriptions) {\n      subscriptions[id].destroy()\n      delete subscriptions[id]\n    }\n    isDestroyed = true\n  }\n}\n\nfunction normalizeBlock(block) {\n  if (block === null || block === undefined) {\n    return null;\n  }\n  return {\n    hash: block.hash,\n    parentHash: block.parentHash,\n    sha3Uncles: block.sha3Uncles,\n    miner: block.miner,\n    stateRoot: block.stateRoot,\n    transactionsRoot: block.transactionsRoot,\n    receiptsRoot: block.receiptsRoot,\n    logsBloom: block.logsBloom,\n    difficulty: block.difficulty,\n    number: block.number,\n    gasLimit: block.gasLimit,\n    gasUsed: block.gasUsed,\n    nonce: block.nonce,\n    mixHash: block.mixHash,\n    timestamp: block.timestamp,\n    extraData: block.extraData,\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/subscriptionManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-json-rpc-filters/tx-filter.js":
/*!********************************************************!*\
  !*** ./node_modules/eth-json-rpc-filters/tx-filter.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const BaseFilter = __webpack_require__(/*! ./base-filter */ \"(ssr)/./node_modules/eth-json-rpc-filters/base-filter.js\")\nconst getBlocksForRange = __webpack_require__(/*! ./getBlocksForRange */ \"(ssr)/./node_modules/eth-json-rpc-filters/getBlocksForRange.js\")\nconst { incrementHexInt } = __webpack_require__(/*! ./hexUtils */ \"(ssr)/./node_modules/eth-json-rpc-filters/hexUtils.js\")\n\nclass TxFilter extends BaseFilter {\n\n  constructor ({ provider }) {\n    super()\n    this.type = 'tx'\n    this.provider = provider\n  }\n\n  async update ({ oldBlock }) {\n    const toBlock = oldBlock\n    const fromBlock = incrementHexInt(oldBlock)\n    const blocks = await getBlocksForRange({ provider: this.provider, fromBlock, toBlock })\n    const blockTxHashes = []\n    for (const block of blocks) {\n      blockTxHashes.push(...block.transactions)\n    }\n    // add to results\n    this.addResults(blockTxHashes)\n  }\n\n}\n\nmodule.exports = TxFilter\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXRoLWpzb24tcnBjLWZpbHRlcnMvdHgtZmlsdGVyLmpzIiwibWFwcGluZ3MiOiJBQUFBLG1CQUFtQixtQkFBTyxDQUFDLCtFQUFlO0FBQzFDLDBCQUEwQixtQkFBTyxDQUFDLDJGQUFxQjtBQUN2RCxRQUFRLGtCQUFrQixFQUFFLG1CQUFPLENBQUMseUVBQVk7O0FBRWhEOztBQUVBLGlCQUFpQixVQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGtCQUFrQixVQUFVO0FBQzVCO0FBQ0E7QUFDQSw2Q0FBNkMsNkNBQTZDO0FBQzFGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL2V0aC1qc29uLXJwYy1maWx0ZXJzL3R4LWZpbHRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBCYXNlRmlsdGVyID0gcmVxdWlyZSgnLi9iYXNlLWZpbHRlcicpXG5jb25zdCBnZXRCbG9ja3NGb3JSYW5nZSA9IHJlcXVpcmUoJy4vZ2V0QmxvY2tzRm9yUmFuZ2UnKVxuY29uc3QgeyBpbmNyZW1lbnRIZXhJbnQgfSA9IHJlcXVpcmUoJy4vaGV4VXRpbHMnKVxuXG5jbGFzcyBUeEZpbHRlciBleHRlbmRzIEJhc2VGaWx0ZXIge1xuXG4gIGNvbnN0cnVjdG9yICh7IHByb3ZpZGVyIH0pIHtcbiAgICBzdXBlcigpXG4gICAgdGhpcy50eXBlID0gJ3R4J1xuICAgIHRoaXMucHJvdmlkZXIgPSBwcm92aWRlclxuICB9XG5cbiAgYXN5bmMgdXBkYXRlICh7IG9sZEJsb2NrIH0pIHtcbiAgICBjb25zdCB0b0Jsb2NrID0gb2xkQmxvY2tcbiAgICBjb25zdCBmcm9tQmxvY2sgPSBpbmNyZW1lbnRIZXhJbnQob2xkQmxvY2spXG4gICAgY29uc3QgYmxvY2tzID0gYXdhaXQgZ2V0QmxvY2tzRm9yUmFuZ2UoeyBwcm92aWRlcjogdGhpcy5wcm92aWRlciwgZnJvbUJsb2NrLCB0b0Jsb2NrIH0pXG4gICAgY29uc3QgYmxvY2tUeEhhc2hlcyA9IFtdXG4gICAgZm9yIChjb25zdCBibG9jayBvZiBibG9ja3MpIHtcbiAgICAgIGJsb2NrVHhIYXNoZXMucHVzaCguLi5ibG9jay50cmFuc2FjdGlvbnMpXG4gICAgfVxuICAgIC8vIGFkZCB0byByZXN1bHRzXG4gICAgdGhpcy5hZGRSZXN1bHRzKGJsb2NrVHhIYXNoZXMpXG4gIH1cblxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IFR4RmlsdGVyXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-json-rpc-filters/tx-filter.js\n");

/***/ })

};
;