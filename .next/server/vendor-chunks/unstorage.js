"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unstorage";
exports.ids = ["vendor-chunks/unstorage"];
exports.modules = {

/***/ "(ssr)/./node_modules/unstorage/dist/index.mjs":
/*!***********************************************!*\
  !*** ./node_modules/unstorage/dist/index.mjs ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   builtinDrivers: () => (/* binding */ builtinDrivers),\n/* harmony export */   createStorage: () => (/* binding */ createStorage),\n/* harmony export */   defineDriver: () => (/* binding */ defineDriver),\n/* harmony export */   filterKeyByBase: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   filterKeyByDepth: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   joinKeys: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   normalizeBaseKey: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   normalizeKey: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a),\n/* harmony export */   prefixStorage: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   restoreSnapshot: () => (/* binding */ restoreSnapshot),\n/* harmony export */   snapshot: () => (/* binding */ snapshot)\n/* harmony export */ });\n/* harmony import */ var destr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! destr */ \"(ssr)/./node_modules/destr/dist/index.mjs\");\n/* harmony import */ var _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./shared/unstorage.CoCt7NXC.mjs */ \"(ssr)/./node_modules/unstorage/dist/shared/unstorage.CoCt7NXC.mjs\");\n\n\n\n\nfunction defineDriver(factory) {\n  return factory;\n}\n\nconst DRIVER_NAME = \"memory\";\nconst memory = defineDriver(() => {\n  const data = /* @__PURE__ */ new Map();\n  return {\n    name: DRIVER_NAME,\n    getInstance: () => data,\n    hasItem(key) {\n      return data.has(key);\n    },\n    getItem(key) {\n      return data.get(key) ?? null;\n    },\n    getItemRaw(key) {\n      return data.get(key) ?? null;\n    },\n    setItem(key, value) {\n      data.set(key, value);\n    },\n    setItemRaw(key, value) {\n      data.set(key, value);\n    },\n    removeItem(key) {\n      data.delete(key);\n    },\n    getKeys() {\n      return [...data.keys()];\n    },\n    clear() {\n      data.clear();\n    },\n    dispose() {\n      data.clear();\n    }\n  };\n});\n\nfunction createStorage(options = {}) {\n  const context = {\n    mounts: { \"\": options.driver || memory() },\n    mountpoints: [\"\"],\n    watching: false,\n    watchListeners: [],\n    unwatch: {}\n  };\n  const getMount = (key) => {\n    for (const base of context.mountpoints) {\n      if (key.startsWith(base)) {\n        return {\n          base,\n          relativeKey: key.slice(base.length),\n          driver: context.mounts[base]\n        };\n      }\n    }\n    return {\n      base: \"\",\n      relativeKey: key,\n      driver: context.mounts[\"\"]\n    };\n  };\n  const getMounts = (base, includeParent) => {\n    return context.mountpoints.filter(\n      (mountpoint) => mountpoint.startsWith(base) || includeParent && base.startsWith(mountpoint)\n    ).map((mountpoint) => ({\n      relativeBase: base.length > mountpoint.length ? base.slice(mountpoint.length) : void 0,\n      mountpoint,\n      driver: context.mounts[mountpoint]\n    }));\n  };\n  const onChange = (event, key) => {\n    if (!context.watching) {\n      return;\n    }\n    key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n    for (const listener of context.watchListeners) {\n      listener(event, key);\n    }\n  };\n  const startWatch = async () => {\n    if (context.watching) {\n      return;\n    }\n    context.watching = true;\n    for (const mountpoint in context.mounts) {\n      context.unwatch[mountpoint] = await watch(\n        context.mounts[mountpoint],\n        onChange,\n        mountpoint\n      );\n    }\n  };\n  const stopWatch = async () => {\n    if (!context.watching) {\n      return;\n    }\n    for (const mountpoint in context.unwatch) {\n      await context.unwatch[mountpoint]();\n    }\n    context.unwatch = {};\n    context.watching = false;\n  };\n  const runBatch = (items, commonOptions, cb) => {\n    const batches = /* @__PURE__ */ new Map();\n    const getBatch = (mount) => {\n      let batch = batches.get(mount.base);\n      if (!batch) {\n        batch = {\n          driver: mount.driver,\n          base: mount.base,\n          items: []\n        };\n        batches.set(mount.base, batch);\n      }\n      return batch;\n    };\n    for (const item of items) {\n      const isStringItem = typeof item === \"string\";\n      const key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(isStringItem ? item : item.key);\n      const value = isStringItem ? void 0 : item.value;\n      const options2 = isStringItem || !item.options ? commonOptions : { ...commonOptions, ...item.options };\n      const mount = getMount(key);\n      getBatch(mount).items.push({\n        key,\n        value,\n        relativeKey: mount.relativeKey,\n        options: options2\n      });\n    }\n    return Promise.all([...batches.values()].map((batch) => cb(batch))).then(\n      (r) => r.flat()\n    );\n  };\n  const storage = {\n    // Item\n    hasItem(key, opts = {}) {\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.hasItem, relativeKey, opts);\n    },\n    getItem(key, opts = {}) {\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.getItem, relativeKey, opts).then(\n        (value) => (0,destr__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(value)\n      );\n    },\n    getItems(items, commonOptions = {}) {\n      return runBatch(items, commonOptions, (batch) => {\n        if (batch.driver.getItems) {\n          return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n            batch.driver.getItems,\n            batch.items.map((item) => ({\n              key: item.relativeKey,\n              options: item.options\n            })),\n            commonOptions\n          ).then(\n            (r) => r.map((item) => ({\n              key: (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.j)(batch.base, item.key),\n              value: (0,destr__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(item.value)\n            }))\n          );\n        }\n        return Promise.all(\n          batch.items.map((item) => {\n            return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n              batch.driver.getItem,\n              item.relativeKey,\n              item.options\n            ).then((value) => ({\n              key: item.key,\n              value: (0,destr__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(value)\n            }));\n          })\n        );\n      });\n    },\n    getItemRaw(key, opts = {}) {\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      if (driver.getItemRaw) {\n        return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.getItemRaw, relativeKey, opts);\n      }\n      return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.getItem, relativeKey, opts).then(\n        (value) => (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.e)(value)\n      );\n    },\n    async setItem(key, value, opts = {}) {\n      if (value === void 0) {\n        return storage.removeItem(key);\n      }\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      if (!driver.setItem) {\n        return;\n      }\n      await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.setItem, relativeKey, (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.d)(value), opts);\n      if (!driver.watch) {\n        onChange(\"update\", key);\n      }\n    },\n    async setItems(items, commonOptions) {\n      await runBatch(items, commonOptions, async (batch) => {\n        if (batch.driver.setItems) {\n          return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n            batch.driver.setItems,\n            batch.items.map((item) => ({\n              key: item.relativeKey,\n              value: (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.d)(item.value),\n              options: item.options\n            })),\n            commonOptions\n          );\n        }\n        if (!batch.driver.setItem) {\n          return;\n        }\n        await Promise.all(\n          batch.items.map((item) => {\n            return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n              batch.driver.setItem,\n              item.relativeKey,\n              (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.d)(item.value),\n              item.options\n            );\n          })\n        );\n      });\n    },\n    async setItemRaw(key, value, opts = {}) {\n      if (value === void 0) {\n        return storage.removeItem(key, opts);\n      }\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      if (driver.setItemRaw) {\n        await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.setItemRaw, relativeKey, value, opts);\n      } else if (driver.setItem) {\n        await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.setItem, relativeKey, (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(value), opts);\n      } else {\n        return;\n      }\n      if (!driver.watch) {\n        onChange(\"update\", key);\n      }\n    },\n    async removeItem(key, opts = {}) {\n      if (typeof opts === \"boolean\") {\n        opts = { removeMeta: opts };\n      }\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      if (!driver.removeItem) {\n        return;\n      }\n      await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.removeItem, relativeKey, opts);\n      if (opts.removeMeta || opts.removeMata) {\n        await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.removeItem, relativeKey + \"$\", opts);\n      }\n      if (!driver.watch) {\n        onChange(\"remove\", key);\n      }\n    },\n    // Meta\n    async getMeta(key, opts = {}) {\n      if (typeof opts === \"boolean\") {\n        opts = { nativeOnly: opts };\n      }\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      const meta = /* @__PURE__ */ Object.create(null);\n      if (driver.getMeta) {\n        Object.assign(meta, await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.getMeta, relativeKey, opts));\n      }\n      if (!opts.nativeOnly) {\n        const value = await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n          driver.getItem,\n          relativeKey + \"$\",\n          opts\n        ).then((value_) => (0,destr__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(value_));\n        if (value && typeof value === \"object\") {\n          if (typeof value.atime === \"string\") {\n            value.atime = new Date(value.atime);\n          }\n          if (typeof value.mtime === \"string\") {\n            value.mtime = new Date(value.mtime);\n          }\n          Object.assign(meta, value);\n        }\n      }\n      return meta;\n    },\n    setMeta(key, value, opts = {}) {\n      return this.setItem(key + \"$\", value, opts);\n    },\n    removeMeta(key, opts = {}) {\n      return this.removeItem(key + \"$\", opts);\n    },\n    // Keys\n    async getKeys(base, opts = {}) {\n      base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n      const mounts = getMounts(base, true);\n      let maskedMounts = [];\n      const allKeys = [];\n      let allMountsSupportMaxDepth = true;\n      for (const mount of mounts) {\n        if (!mount.driver.flags?.maxDepth) {\n          allMountsSupportMaxDepth = false;\n        }\n        const rawKeys = await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n          mount.driver.getKeys,\n          mount.relativeBase,\n          opts\n        );\n        for (const key of rawKeys) {\n          const fullKey = mount.mountpoint + (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n          if (!maskedMounts.some((p) => fullKey.startsWith(p))) {\n            allKeys.push(fullKey);\n          }\n        }\n        maskedMounts = [\n          mount.mountpoint,\n          ...maskedMounts.filter((p) => !p.startsWith(mount.mountpoint))\n        ];\n      }\n      const shouldFilterByDepth = opts.maxDepth !== void 0 && !allMountsSupportMaxDepth;\n      return allKeys.filter(\n        (key) => (!shouldFilterByDepth || (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.f)(key, opts.maxDepth)) && (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.c)(key, base)\n      );\n    },\n    // Utils\n    async clear(base, opts = {}) {\n      base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n      await Promise.all(\n        getMounts(base, false).map(async (m) => {\n          if (m.driver.clear) {\n            return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(m.driver.clear, m.relativeBase, opts);\n          }\n          if (m.driver.removeItem) {\n            const keys = await m.driver.getKeys(m.relativeBase || \"\", opts);\n            return Promise.all(\n              keys.map((key) => m.driver.removeItem(key, opts))\n            );\n          }\n        })\n      );\n    },\n    async dispose() {\n      await Promise.all(\n        Object.values(context.mounts).map((driver) => dispose(driver))\n      );\n    },\n    async watch(callback) {\n      await startWatch();\n      context.watchListeners.push(callback);\n      return async () => {\n        context.watchListeners = context.watchListeners.filter(\n          (listener) => listener !== callback\n        );\n        if (context.watchListeners.length === 0) {\n          await stopWatch();\n        }\n      };\n    },\n    async unwatch() {\n      context.watchListeners = [];\n      await stopWatch();\n    },\n    // Mount\n    mount(base, driver) {\n      base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n      if (base && context.mounts[base]) {\n        throw new Error(`already mounted at ${base}`);\n      }\n      if (base) {\n        context.mountpoints.push(base);\n        context.mountpoints.sort((a, b) => b.length - a.length);\n      }\n      context.mounts[base] = driver;\n      if (context.watching) {\n        Promise.resolve(watch(driver, onChange, base)).then((unwatcher) => {\n          context.unwatch[base] = unwatcher;\n        }).catch(console.error);\n      }\n      return storage;\n    },\n    async unmount(base, _dispose = true) {\n      base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n      if (!base || !context.mounts[base]) {\n        return;\n      }\n      if (context.watching && base in context.unwatch) {\n        context.unwatch[base]?.();\n        delete context.unwatch[base];\n      }\n      if (_dispose) {\n        await dispose(context.mounts[base]);\n      }\n      context.mountpoints = context.mountpoints.filter((key) => key !== base);\n      delete context.mounts[base];\n    },\n    getMount(key = \"\") {\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key) + \":\";\n      const m = getMount(key);\n      return {\n        driver: m.driver,\n        base: m.base\n      };\n    },\n    getMounts(base = \"\", opts = {}) {\n      base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(base);\n      const mounts = getMounts(base, opts.parents);\n      return mounts.map((m) => ({\n        driver: m.driver,\n        base: m.mountpoint\n      }));\n    },\n    // Aliases\n    keys: (base, opts = {}) => storage.getKeys(base, opts),\n    get: (key, opts = {}) => storage.getItem(key, opts),\n    set: (key, value, opts = {}) => storage.setItem(key, value, opts),\n    has: (key, opts = {}) => storage.hasItem(key, opts),\n    del: (key, opts = {}) => storage.removeItem(key, opts),\n    remove: (key, opts = {}) => storage.removeItem(key, opts)\n  };\n  return storage;\n}\nasync function snapshot(storage, base) {\n  base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n  const keys = await storage.getKeys(base);\n  const snapshot2 = {};\n  await Promise.all(\n    keys.map(async (key) => {\n      snapshot2[key.slice(base.length)] = await storage.getItem(key);\n    })\n  );\n  return snapshot2;\n}\nasync function restoreSnapshot(driver, snapshot2, base = \"\") {\n  base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n  await Promise.all(\n    Object.entries(snapshot2).map((e) => driver.setItem(base + e[0], e[1]))\n  );\n}\nfunction watch(driver, onChange, base) {\n  return driver.watch ? driver.watch((event, key) => onChange(event, base + key)) : () => {\n  };\n}\nasync function dispose(driver) {\n  if (typeof driver.dispose === \"function\") {\n    await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.dispose);\n  }\n}\n\nconst builtinDrivers = {\n  \"azure-app-configuration\": \"unstorage/drivers/azure-app-configuration\",\n  \"azureAppConfiguration\": \"unstorage/drivers/azure-app-configuration\",\n  \"azure-cosmos\": \"unstorage/drivers/azure-cosmos\",\n  \"azureCosmos\": \"unstorage/drivers/azure-cosmos\",\n  \"azure-key-vault\": \"unstorage/drivers/azure-key-vault\",\n  \"azureKeyVault\": \"unstorage/drivers/azure-key-vault\",\n  \"azure-storage-blob\": \"unstorage/drivers/azure-storage-blob\",\n  \"azureStorageBlob\": \"unstorage/drivers/azure-storage-blob\",\n  \"azure-storage-table\": \"unstorage/drivers/azure-storage-table\",\n  \"azureStorageTable\": \"unstorage/drivers/azure-storage-table\",\n  \"capacitor-preferences\": \"unstorage/drivers/capacitor-preferences\",\n  \"capacitorPreferences\": \"unstorage/drivers/capacitor-preferences\",\n  \"cloudflare-kv-binding\": \"unstorage/drivers/cloudflare-kv-binding\",\n  \"cloudflareKVBinding\": \"unstorage/drivers/cloudflare-kv-binding\",\n  \"cloudflare-kv-http\": \"unstorage/drivers/cloudflare-kv-http\",\n  \"cloudflareKVHttp\": \"unstorage/drivers/cloudflare-kv-http\",\n  \"cloudflare-r2-binding\": \"unstorage/drivers/cloudflare-r2-binding\",\n  \"cloudflareR2Binding\": \"unstorage/drivers/cloudflare-r2-binding\",\n  \"db0\": \"unstorage/drivers/db0\",\n  \"deno-kv-node\": \"unstorage/drivers/deno-kv-node\",\n  \"denoKVNode\": \"unstorage/drivers/deno-kv-node\",\n  \"deno-kv\": \"unstorage/drivers/deno-kv\",\n  \"denoKV\": \"unstorage/drivers/deno-kv\",\n  \"fs-lite\": \"unstorage/drivers/fs-lite\",\n  \"fsLite\": \"unstorage/drivers/fs-lite\",\n  \"fs\": \"unstorage/drivers/fs\",\n  \"github\": \"unstorage/drivers/github\",\n  \"http\": \"unstorage/drivers/http\",\n  \"indexedb\": \"unstorage/drivers/indexedb\",\n  \"localstorage\": \"unstorage/drivers/localstorage\",\n  \"lru-cache\": \"unstorage/drivers/lru-cache\",\n  \"lruCache\": \"unstorage/drivers/lru-cache\",\n  \"memory\": \"unstorage/drivers/memory\",\n  \"mongodb\": \"unstorage/drivers/mongodb\",\n  \"netlify-blobs\": \"unstorage/drivers/netlify-blobs\",\n  \"netlifyBlobs\": \"unstorage/drivers/netlify-blobs\",\n  \"null\": \"unstorage/drivers/null\",\n  \"overlay\": \"unstorage/drivers/overlay\",\n  \"planetscale\": \"unstorage/drivers/planetscale\",\n  \"redis\": \"unstorage/drivers/redis\",\n  \"s3\": \"unstorage/drivers/s3\",\n  \"session-storage\": \"unstorage/drivers/session-storage\",\n  \"sessionStorage\": \"unstorage/drivers/session-storage\",\n  \"uploadthing\": \"unstorage/drivers/uploadthing\",\n  \"upstash\": \"unstorage/drivers/upstash\",\n  \"vercel-blob\": \"unstorage/drivers/vercel-blob\",\n  \"vercelBlob\": \"unstorage/drivers/vercel-blob\",\n  \"vercel-kv\": \"unstorage/drivers/vercel-kv\",\n  \"vercelKV\": \"unstorage/drivers/vercel-kv\"\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdW5zdG9yYWdlL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMEI7QUFDcU07QUFDMUo7O0FBRXJFO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVELG1DQUFtQztBQUNuQztBQUNBLGNBQWMsZ0NBQWdDO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLGlFQUFZO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLGlFQUFZO0FBQzlCO0FBQ0EseUVBQXlFO0FBQ3pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQixZQUFZLGlFQUFZO0FBQ3hCLGNBQWMsc0JBQXNCO0FBQ3BDLGFBQWEsaUVBQVM7QUFDdEIsS0FBSztBQUNMLDBCQUEwQjtBQUMxQixZQUFZLGlFQUFZO0FBQ3hCLGNBQWMsc0JBQXNCO0FBQ3BDLGFBQWEsaUVBQVM7QUFDdEIsbUJBQW1CLGlEQUFLO0FBQ3hCO0FBQ0EsS0FBSztBQUNMLHNDQUFzQztBQUN0QztBQUNBO0FBQ0EsaUJBQWlCLGlFQUFTO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixpRUFBUTtBQUMzQixxQkFBcUIsaURBQUs7QUFDMUIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGlFQUFTO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsaURBQUs7QUFDMUIsYUFBYTtBQUNiLFdBQVc7QUFDWDtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0wsNkJBQTZCO0FBQzdCLFlBQVksaUVBQVk7QUFDeEIsY0FBYyxzQkFBc0I7QUFDcEM7QUFDQSxlQUFlLGlFQUFTO0FBQ3hCO0FBQ0EsYUFBYSxpRUFBUztBQUN0QixtQkFBbUIsaUVBQWM7QUFDakM7QUFDQSxLQUFLO0FBQ0wsdUNBQXVDO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBLFlBQVksaUVBQVk7QUFDeEIsY0FBYyxzQkFBc0I7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsWUFBWSxpRUFBUyw4QkFBOEIsaUVBQVM7QUFDNUQ7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixpRUFBUztBQUMxQjtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsaUVBQVM7QUFDOUI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixpRUFBUztBQUM1QjtBQUNBO0FBQ0EsY0FBYyxpRUFBUztBQUN2QjtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTCwwQ0FBMEM7QUFDMUM7QUFDQTtBQUNBO0FBQ0EsWUFBWSxpRUFBWTtBQUN4QixjQUFjLHNCQUFzQjtBQUNwQztBQUNBLGNBQWMsaUVBQVM7QUFDdkIsUUFBUTtBQUNSLGNBQWMsaUVBQVMsOEJBQThCLGlFQUFZO0FBQ2pFLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLG1DQUFtQztBQUNuQztBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLFlBQVksaUVBQVk7QUFDeEIsY0FBYyxzQkFBc0I7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsWUFBWSxpRUFBUztBQUNyQjtBQUNBLGNBQWMsaUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxnQ0FBZ0M7QUFDaEM7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxZQUFZLGlFQUFZO0FBQ3hCLGNBQWMsc0JBQXNCO0FBQ3BDO0FBQ0E7QUFDQSxrQ0FBa0MsaUVBQVM7QUFDM0M7QUFDQTtBQUNBLDRCQUE0QixpRUFBUztBQUNyQztBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsaURBQUs7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxpQ0FBaUM7QUFDakM7QUFDQSxLQUFLO0FBQ0wsNkJBQTZCO0FBQzdCO0FBQ0EsS0FBSztBQUNMO0FBQ0EsaUNBQWlDO0FBQ2pDLGFBQWEsaUVBQWdCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsaUVBQVM7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QyxpRUFBWTtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLGlFQUFnQix5QkFBeUIsaUVBQWU7QUFDbEc7QUFDQSxLQUFLO0FBQ0w7QUFDQSwrQkFBK0I7QUFDL0IsYUFBYSxpRUFBZ0I7QUFDN0I7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGlFQUFTO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxhQUFhLGlFQUFnQjtBQUM3QjtBQUNBLDhDQUE4QyxLQUFLO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsYUFBYSxpRUFBZ0I7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsWUFBWSxpRUFBWTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLGtDQUFrQztBQUNsQyxhQUFhLGlFQUFZO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBLDBCQUEwQjtBQUMxQix3QkFBd0I7QUFDeEIsK0JBQStCO0FBQy9CLHdCQUF3QjtBQUN4Qix3QkFBd0I7QUFDeEIsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyxpRUFBZ0I7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsaUVBQWdCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSxpRUFBUztBQUNuQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFK0oiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvdW5zdG9yYWdlL2Rpc3QvaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBkZXN0ciBmcm9tICdkZXN0cic7XG5pbXBvcnQgeyBuIGFzIG5vcm1hbGl6ZUJhc2VLZXksIGEgYXMgbm9ybWFsaXplS2V5LCBiIGFzIGFzeW5jQ2FsbCwgZiBhcyBmaWx0ZXJLZXlCeURlcHRoLCBjIGFzIGZpbHRlcktleUJ5QmFzZSwgcyBhcyBzZXJpYWxpemVSYXcsIGQgYXMgc3RyaW5naWZ5LCBlIGFzIGRlc2VyaWFsaXplUmF3LCBqIGFzIGpvaW5LZXlzIH0gZnJvbSAnLi9zaGFyZWQvdW5zdG9yYWdlLkNvQ3Q3TlhDLm1qcyc7XG5leHBvcnQgeyBwIGFzIHByZWZpeFN0b3JhZ2UgfSBmcm9tICcuL3NoYXJlZC91bnN0b3JhZ2UuQ29DdDdOWEMubWpzJztcblxuZnVuY3Rpb24gZGVmaW5lRHJpdmVyKGZhY3RvcnkpIHtcbiAgcmV0dXJuIGZhY3Rvcnk7XG59XG5cbmNvbnN0IERSSVZFUl9OQU1FID0gXCJtZW1vcnlcIjtcbmNvbnN0IG1lbW9yeSA9IGRlZmluZURyaXZlcigoKSA9PiB7XG4gIGNvbnN0IGRhdGEgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuICByZXR1cm4ge1xuICAgIG5hbWU6IERSSVZFUl9OQU1FLFxuICAgIGdldEluc3RhbmNlOiAoKSA9PiBkYXRhLFxuICAgIGhhc0l0ZW0oa2V5KSB7XG4gICAgICByZXR1cm4gZGF0YS5oYXMoa2V5KTtcbiAgICB9LFxuICAgIGdldEl0ZW0oa2V5KSB7XG4gICAgICByZXR1cm4gZGF0YS5nZXQoa2V5KSA/PyBudWxsO1xuICAgIH0sXG4gICAgZ2V0SXRlbVJhdyhrZXkpIHtcbiAgICAgIHJldHVybiBkYXRhLmdldChrZXkpID8/IG51bGw7XG4gICAgfSxcbiAgICBzZXRJdGVtKGtleSwgdmFsdWUpIHtcbiAgICAgIGRhdGEuc2V0KGtleSwgdmFsdWUpO1xuICAgIH0sXG4gICAgc2V0SXRlbVJhdyhrZXksIHZhbHVlKSB7XG4gICAgICBkYXRhLnNldChrZXksIHZhbHVlKTtcbiAgICB9LFxuICAgIHJlbW92ZUl0ZW0oa2V5KSB7XG4gICAgICBkYXRhLmRlbGV0ZShrZXkpO1xuICAgIH0sXG4gICAgZ2V0S2V5cygpIHtcbiAgICAgIHJldHVybiBbLi4uZGF0YS5rZXlzKCldO1xuICAgIH0sXG4gICAgY2xlYXIoKSB7XG4gICAgICBkYXRhLmNsZWFyKCk7XG4gICAgfSxcbiAgICBkaXNwb3NlKCkge1xuICAgICAgZGF0YS5jbGVhcigpO1xuICAgIH1cbiAgfTtcbn0pO1xuXG5mdW5jdGlvbiBjcmVhdGVTdG9yYWdlKG9wdGlvbnMgPSB7fSkge1xuICBjb25zdCBjb250ZXh0ID0ge1xuICAgIG1vdW50czogeyBcIlwiOiBvcHRpb25zLmRyaXZlciB8fCBtZW1vcnkoKSB9LFxuICAgIG1vdW50cG9pbnRzOiBbXCJcIl0sXG4gICAgd2F0Y2hpbmc6IGZhbHNlLFxuICAgIHdhdGNoTGlzdGVuZXJzOiBbXSxcbiAgICB1bndhdGNoOiB7fVxuICB9O1xuICBjb25zdCBnZXRNb3VudCA9IChrZXkpID0+IHtcbiAgICBmb3IgKGNvbnN0IGJhc2Ugb2YgY29udGV4dC5tb3VudHBvaW50cykge1xuICAgICAgaWYgKGtleS5zdGFydHNXaXRoKGJhc2UpKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgYmFzZSxcbiAgICAgICAgICByZWxhdGl2ZUtleToga2V5LnNsaWNlKGJhc2UubGVuZ3RoKSxcbiAgICAgICAgICBkcml2ZXI6IGNvbnRleHQubW91bnRzW2Jhc2VdXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiB7XG4gICAgICBiYXNlOiBcIlwiLFxuICAgICAgcmVsYXRpdmVLZXk6IGtleSxcbiAgICAgIGRyaXZlcjogY29udGV4dC5tb3VudHNbXCJcIl1cbiAgICB9O1xuICB9O1xuICBjb25zdCBnZXRNb3VudHMgPSAoYmFzZSwgaW5jbHVkZVBhcmVudCkgPT4ge1xuICAgIHJldHVybiBjb250ZXh0Lm1vdW50cG9pbnRzLmZpbHRlcihcbiAgICAgIChtb3VudHBvaW50KSA9PiBtb3VudHBvaW50LnN0YXJ0c1dpdGgoYmFzZSkgfHwgaW5jbHVkZVBhcmVudCAmJiBiYXNlLnN0YXJ0c1dpdGgobW91bnRwb2ludClcbiAgICApLm1hcCgobW91bnRwb2ludCkgPT4gKHtcbiAgICAgIHJlbGF0aXZlQmFzZTogYmFzZS5sZW5ndGggPiBtb3VudHBvaW50Lmxlbmd0aCA/IGJhc2Uuc2xpY2UobW91bnRwb2ludC5sZW5ndGgpIDogdm9pZCAwLFxuICAgICAgbW91bnRwb2ludCxcbiAgICAgIGRyaXZlcjogY29udGV4dC5tb3VudHNbbW91bnRwb2ludF1cbiAgICB9KSk7XG4gIH07XG4gIGNvbnN0IG9uQ2hhbmdlID0gKGV2ZW50LCBrZXkpID0+IHtcbiAgICBpZiAoIWNvbnRleHQud2F0Y2hpbmcpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAga2V5ID0gbm9ybWFsaXplS2V5KGtleSk7XG4gICAgZm9yIChjb25zdCBsaXN0ZW5lciBvZiBjb250ZXh0LndhdGNoTGlzdGVuZXJzKSB7XG4gICAgICBsaXN0ZW5lcihldmVudCwga2V5KTtcbiAgICB9XG4gIH07XG4gIGNvbnN0IHN0YXJ0V2F0Y2ggPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKGNvbnRleHQud2F0Y2hpbmcpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgY29udGV4dC53YXRjaGluZyA9IHRydWU7XG4gICAgZm9yIChjb25zdCBtb3VudHBvaW50IGluIGNvbnRleHQubW91bnRzKSB7XG4gICAgICBjb250ZXh0LnVud2F0Y2hbbW91bnRwb2ludF0gPSBhd2FpdCB3YXRjaChcbiAgICAgICAgY29udGV4dC5tb3VudHNbbW91bnRwb2ludF0sXG4gICAgICAgIG9uQ2hhbmdlLFxuICAgICAgICBtb3VudHBvaW50XG4gICAgICApO1xuICAgIH1cbiAgfTtcbiAgY29uc3Qgc3RvcFdhdGNoID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghY29udGV4dC53YXRjaGluZykge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBmb3IgKGNvbnN0IG1vdW50cG9pbnQgaW4gY29udGV4dC51bndhdGNoKSB7XG4gICAgICBhd2FpdCBjb250ZXh0LnVud2F0Y2hbbW91bnRwb2ludF0oKTtcbiAgICB9XG4gICAgY29udGV4dC51bndhdGNoID0ge307XG4gICAgY29udGV4dC53YXRjaGluZyA9IGZhbHNlO1xuICB9O1xuICBjb25zdCBydW5CYXRjaCA9IChpdGVtcywgY29tbW9uT3B0aW9ucywgY2IpID0+IHtcbiAgICBjb25zdCBiYXRjaGVzID0gLyogQF9fUFVSRV9fICovIG5ldyBNYXAoKTtcbiAgICBjb25zdCBnZXRCYXRjaCA9IChtb3VudCkgPT4ge1xuICAgICAgbGV0IGJhdGNoID0gYmF0Y2hlcy5nZXQobW91bnQuYmFzZSk7XG4gICAgICBpZiAoIWJhdGNoKSB7XG4gICAgICAgIGJhdGNoID0ge1xuICAgICAgICAgIGRyaXZlcjogbW91bnQuZHJpdmVyLFxuICAgICAgICAgIGJhc2U6IG1vdW50LmJhc2UsXG4gICAgICAgICAgaXRlbXM6IFtdXG4gICAgICAgIH07XG4gICAgICAgIGJhdGNoZXMuc2V0KG1vdW50LmJhc2UsIGJhdGNoKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBiYXRjaDtcbiAgICB9O1xuICAgIGZvciAoY29uc3QgaXRlbSBvZiBpdGVtcykge1xuICAgICAgY29uc3QgaXNTdHJpbmdJdGVtID0gdHlwZW9mIGl0ZW0gPT09IFwic3RyaW5nXCI7XG4gICAgICBjb25zdCBrZXkgPSBub3JtYWxpemVLZXkoaXNTdHJpbmdJdGVtID8gaXRlbSA6IGl0ZW0ua2V5KTtcbiAgICAgIGNvbnN0IHZhbHVlID0gaXNTdHJpbmdJdGVtID8gdm9pZCAwIDogaXRlbS52YWx1ZTtcbiAgICAgIGNvbnN0IG9wdGlvbnMyID0gaXNTdHJpbmdJdGVtIHx8ICFpdGVtLm9wdGlvbnMgPyBjb21tb25PcHRpb25zIDogeyAuLi5jb21tb25PcHRpb25zLCAuLi5pdGVtLm9wdGlvbnMgfTtcbiAgICAgIGNvbnN0IG1vdW50ID0gZ2V0TW91bnQoa2V5KTtcbiAgICAgIGdldEJhdGNoKG1vdW50KS5pdGVtcy5wdXNoKHtcbiAgICAgICAga2V5LFxuICAgICAgICB2YWx1ZSxcbiAgICAgICAgcmVsYXRpdmVLZXk6IG1vdW50LnJlbGF0aXZlS2V5LFxuICAgICAgICBvcHRpb25zOiBvcHRpb25zMlxuICAgICAgfSk7XG4gICAgfVxuICAgIHJldHVybiBQcm9taXNlLmFsbChbLi4uYmF0Y2hlcy52YWx1ZXMoKV0ubWFwKChiYXRjaCkgPT4gY2IoYmF0Y2gpKSkudGhlbihcbiAgICAgIChyKSA9PiByLmZsYXQoKVxuICAgICk7XG4gIH07XG4gIGNvbnN0IHN0b3JhZ2UgPSB7XG4gICAgLy8gSXRlbVxuICAgIGhhc0l0ZW0oa2V5LCBvcHRzID0ge30pIHtcbiAgICAgIGtleSA9IG5vcm1hbGl6ZUtleShrZXkpO1xuICAgICAgY29uc3QgeyByZWxhdGl2ZUtleSwgZHJpdmVyIH0gPSBnZXRNb3VudChrZXkpO1xuICAgICAgcmV0dXJuIGFzeW5jQ2FsbChkcml2ZXIuaGFzSXRlbSwgcmVsYXRpdmVLZXksIG9wdHMpO1xuICAgIH0sXG4gICAgZ2V0SXRlbShrZXksIG9wdHMgPSB7fSkge1xuICAgICAga2V5ID0gbm9ybWFsaXplS2V5KGtleSk7XG4gICAgICBjb25zdCB7IHJlbGF0aXZlS2V5LCBkcml2ZXIgfSA9IGdldE1vdW50KGtleSk7XG4gICAgICByZXR1cm4gYXN5bmNDYWxsKGRyaXZlci5nZXRJdGVtLCByZWxhdGl2ZUtleSwgb3B0cykudGhlbihcbiAgICAgICAgKHZhbHVlKSA9PiBkZXN0cih2YWx1ZSlcbiAgICAgICk7XG4gICAgfSxcbiAgICBnZXRJdGVtcyhpdGVtcywgY29tbW9uT3B0aW9ucyA9IHt9KSB7XG4gICAgICByZXR1cm4gcnVuQmF0Y2goaXRlbXMsIGNvbW1vbk9wdGlvbnMsIChiYXRjaCkgPT4ge1xuICAgICAgICBpZiAoYmF0Y2guZHJpdmVyLmdldEl0ZW1zKSB7XG4gICAgICAgICAgcmV0dXJuIGFzeW5jQ2FsbChcbiAgICAgICAgICAgIGJhdGNoLmRyaXZlci5nZXRJdGVtcyxcbiAgICAgICAgICAgIGJhdGNoLml0ZW1zLm1hcCgoaXRlbSkgPT4gKHtcbiAgICAgICAgICAgICAga2V5OiBpdGVtLnJlbGF0aXZlS2V5LFxuICAgICAgICAgICAgICBvcHRpb25zOiBpdGVtLm9wdGlvbnNcbiAgICAgICAgICAgIH0pKSxcbiAgICAgICAgICAgIGNvbW1vbk9wdGlvbnNcbiAgICAgICAgICApLnRoZW4oXG4gICAgICAgICAgICAocikgPT4gci5tYXAoKGl0ZW0pID0+ICh7XG4gICAgICAgICAgICAgIGtleTogam9pbktleXMoYmF0Y2guYmFzZSwgaXRlbS5rZXkpLFxuICAgICAgICAgICAgICB2YWx1ZTogZGVzdHIoaXRlbS52YWx1ZSlcbiAgICAgICAgICAgIH0pKVxuICAgICAgICAgICk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIFByb21pc2UuYWxsKFxuICAgICAgICAgIGJhdGNoLml0ZW1zLm1hcCgoaXRlbSkgPT4ge1xuICAgICAgICAgICAgcmV0dXJuIGFzeW5jQ2FsbChcbiAgICAgICAgICAgICAgYmF0Y2guZHJpdmVyLmdldEl0ZW0sXG4gICAgICAgICAgICAgIGl0ZW0ucmVsYXRpdmVLZXksXG4gICAgICAgICAgICAgIGl0ZW0ub3B0aW9uc1xuICAgICAgICAgICAgKS50aGVuKCh2YWx1ZSkgPT4gKHtcbiAgICAgICAgICAgICAga2V5OiBpdGVtLmtleSxcbiAgICAgICAgICAgICAgdmFsdWU6IGRlc3RyKHZhbHVlKVxuICAgICAgICAgICAgfSkpO1xuICAgICAgICAgIH0pXG4gICAgICAgICk7XG4gICAgICB9KTtcbiAgICB9LFxuICAgIGdldEl0ZW1SYXcoa2V5LCBvcHRzID0ge30pIHtcbiAgICAgIGtleSA9IG5vcm1hbGl6ZUtleShrZXkpO1xuICAgICAgY29uc3QgeyByZWxhdGl2ZUtleSwgZHJpdmVyIH0gPSBnZXRNb3VudChrZXkpO1xuICAgICAgaWYgKGRyaXZlci5nZXRJdGVtUmF3KSB7XG4gICAgICAgIHJldHVybiBhc3luY0NhbGwoZHJpdmVyLmdldEl0ZW1SYXcsIHJlbGF0aXZlS2V5LCBvcHRzKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBhc3luY0NhbGwoZHJpdmVyLmdldEl0ZW0sIHJlbGF0aXZlS2V5LCBvcHRzKS50aGVuKFxuICAgICAgICAodmFsdWUpID0+IGRlc2VyaWFsaXplUmF3KHZhbHVlKVxuICAgICAgKTtcbiAgICB9LFxuICAgIGFzeW5jIHNldEl0ZW0oa2V5LCB2YWx1ZSwgb3B0cyA9IHt9KSB7XG4gICAgICBpZiAodmFsdWUgPT09IHZvaWQgMCkge1xuICAgICAgICByZXR1cm4gc3RvcmFnZS5yZW1vdmVJdGVtKGtleSk7XG4gICAgICB9XG4gICAgICBrZXkgPSBub3JtYWxpemVLZXkoa2V5KTtcbiAgICAgIGNvbnN0IHsgcmVsYXRpdmVLZXksIGRyaXZlciB9ID0gZ2V0TW91bnQoa2V5KTtcbiAgICAgIGlmICghZHJpdmVyLnNldEl0ZW0pIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgYXdhaXQgYXN5bmNDYWxsKGRyaXZlci5zZXRJdGVtLCByZWxhdGl2ZUtleSwgc3RyaW5naWZ5KHZhbHVlKSwgb3B0cyk7XG4gICAgICBpZiAoIWRyaXZlci53YXRjaCkge1xuICAgICAgICBvbkNoYW5nZShcInVwZGF0ZVwiLCBrZXkpO1xuICAgICAgfVxuICAgIH0sXG4gICAgYXN5bmMgc2V0SXRlbXMoaXRlbXMsIGNvbW1vbk9wdGlvbnMpIHtcbiAgICAgIGF3YWl0IHJ1bkJhdGNoKGl0ZW1zLCBjb21tb25PcHRpb25zLCBhc3luYyAoYmF0Y2gpID0+IHtcbiAgICAgICAgaWYgKGJhdGNoLmRyaXZlci5zZXRJdGVtcykge1xuICAgICAgICAgIHJldHVybiBhc3luY0NhbGwoXG4gICAgICAgICAgICBiYXRjaC5kcml2ZXIuc2V0SXRlbXMsXG4gICAgICAgICAgICBiYXRjaC5pdGVtcy5tYXAoKGl0ZW0pID0+ICh7XG4gICAgICAgICAgICAgIGtleTogaXRlbS5yZWxhdGl2ZUtleSxcbiAgICAgICAgICAgICAgdmFsdWU6IHN0cmluZ2lmeShpdGVtLnZhbHVlKSxcbiAgICAgICAgICAgICAgb3B0aW9uczogaXRlbS5vcHRpb25zXG4gICAgICAgICAgICB9KSksXG4gICAgICAgICAgICBjb21tb25PcHRpb25zXG4gICAgICAgICAgKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIWJhdGNoLmRyaXZlci5zZXRJdGVtKSB7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGF3YWl0IFByb21pc2UuYWxsKFxuICAgICAgICAgIGJhdGNoLml0ZW1zLm1hcCgoaXRlbSkgPT4ge1xuICAgICAgICAgICAgcmV0dXJuIGFzeW5jQ2FsbChcbiAgICAgICAgICAgICAgYmF0Y2guZHJpdmVyLnNldEl0ZW0sXG4gICAgICAgICAgICAgIGl0ZW0ucmVsYXRpdmVLZXksXG4gICAgICAgICAgICAgIHN0cmluZ2lmeShpdGVtLnZhbHVlKSxcbiAgICAgICAgICAgICAgaXRlbS5vcHRpb25zXG4gICAgICAgICAgICApO1xuICAgICAgICAgIH0pXG4gICAgICAgICk7XG4gICAgICB9KTtcbiAgICB9LFxuICAgIGFzeW5jIHNldEl0ZW1SYXcoa2V5LCB2YWx1ZSwgb3B0cyA9IHt9KSB7XG4gICAgICBpZiAodmFsdWUgPT09IHZvaWQgMCkge1xuICAgICAgICByZXR1cm4gc3RvcmFnZS5yZW1vdmVJdGVtKGtleSwgb3B0cyk7XG4gICAgICB9XG4gICAgICBrZXkgPSBub3JtYWxpemVLZXkoa2V5KTtcbiAgICAgIGNvbnN0IHsgcmVsYXRpdmVLZXksIGRyaXZlciB9ID0gZ2V0TW91bnQoa2V5KTtcbiAgICAgIGlmIChkcml2ZXIuc2V0SXRlbVJhdykge1xuICAgICAgICBhd2FpdCBhc3luY0NhbGwoZHJpdmVyLnNldEl0ZW1SYXcsIHJlbGF0aXZlS2V5LCB2YWx1ZSwgb3B0cyk7XG4gICAgICB9IGVsc2UgaWYgKGRyaXZlci5zZXRJdGVtKSB7XG4gICAgICAgIGF3YWl0IGFzeW5jQ2FsbChkcml2ZXIuc2V0SXRlbSwgcmVsYXRpdmVLZXksIHNlcmlhbGl6ZVJhdyh2YWx1ZSksIG9wdHMpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgaWYgKCFkcml2ZXIud2F0Y2gpIHtcbiAgICAgICAgb25DaGFuZ2UoXCJ1cGRhdGVcIiwga2V5KTtcbiAgICAgIH1cbiAgICB9LFxuICAgIGFzeW5jIHJlbW92ZUl0ZW0oa2V5LCBvcHRzID0ge30pIHtcbiAgICAgIGlmICh0eXBlb2Ygb3B0cyA9PT0gXCJib29sZWFuXCIpIHtcbiAgICAgICAgb3B0cyA9IHsgcmVtb3ZlTWV0YTogb3B0cyB9O1xuICAgICAgfVxuICAgICAga2V5ID0gbm9ybWFsaXplS2V5KGtleSk7XG4gICAgICBjb25zdCB7IHJlbGF0aXZlS2V5LCBkcml2ZXIgfSA9IGdldE1vdW50KGtleSk7XG4gICAgICBpZiAoIWRyaXZlci5yZW1vdmVJdGVtKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGF3YWl0IGFzeW5jQ2FsbChkcml2ZXIucmVtb3ZlSXRlbSwgcmVsYXRpdmVLZXksIG9wdHMpO1xuICAgICAgaWYgKG9wdHMucmVtb3ZlTWV0YSB8fCBvcHRzLnJlbW92ZU1hdGEpIHtcbiAgICAgICAgYXdhaXQgYXN5bmNDYWxsKGRyaXZlci5yZW1vdmVJdGVtLCByZWxhdGl2ZUtleSArIFwiJFwiLCBvcHRzKTtcbiAgICAgIH1cbiAgICAgIGlmICghZHJpdmVyLndhdGNoKSB7XG4gICAgICAgIG9uQ2hhbmdlKFwicmVtb3ZlXCIsIGtleSk7XG4gICAgICB9XG4gICAgfSxcbiAgICAvLyBNZXRhXG4gICAgYXN5bmMgZ2V0TWV0YShrZXksIG9wdHMgPSB7fSkge1xuICAgICAgaWYgKHR5cGVvZiBvcHRzID09PSBcImJvb2xlYW5cIikge1xuICAgICAgICBvcHRzID0geyBuYXRpdmVPbmx5OiBvcHRzIH07XG4gICAgICB9XG4gICAgICBrZXkgPSBub3JtYWxpemVLZXkoa2V5KTtcbiAgICAgIGNvbnN0IHsgcmVsYXRpdmVLZXksIGRyaXZlciB9ID0gZ2V0TW91bnQoa2V5KTtcbiAgICAgIGNvbnN0IG1ldGEgPSAvKiBAX19QVVJFX18gKi8gT2JqZWN0LmNyZWF0ZShudWxsKTtcbiAgICAgIGlmIChkcml2ZXIuZ2V0TWV0YSkge1xuICAgICAgICBPYmplY3QuYXNzaWduKG1ldGEsIGF3YWl0IGFzeW5jQ2FsbChkcml2ZXIuZ2V0TWV0YSwgcmVsYXRpdmVLZXksIG9wdHMpKTtcbiAgICAgIH1cbiAgICAgIGlmICghb3B0cy5uYXRpdmVPbmx5KSB7XG4gICAgICAgIGNvbnN0IHZhbHVlID0gYXdhaXQgYXN5bmNDYWxsKFxuICAgICAgICAgIGRyaXZlci5nZXRJdGVtLFxuICAgICAgICAgIHJlbGF0aXZlS2V5ICsgXCIkXCIsXG4gICAgICAgICAgb3B0c1xuICAgICAgICApLnRoZW4oKHZhbHVlXykgPT4gZGVzdHIodmFsdWVfKSk7XG4gICAgICAgIGlmICh2YWx1ZSAmJiB0eXBlb2YgdmFsdWUgPT09IFwib2JqZWN0XCIpIHtcbiAgICAgICAgICBpZiAodHlwZW9mIHZhbHVlLmF0aW1lID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgICAgICB2YWx1ZS5hdGltZSA9IG5ldyBEYXRlKHZhbHVlLmF0aW1lKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgaWYgKHR5cGVvZiB2YWx1ZS5tdGltZSA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICAgICAgdmFsdWUubXRpbWUgPSBuZXcgRGF0ZSh2YWx1ZS5tdGltZSk7XG4gICAgICAgICAgfVxuICAgICAgICAgIE9iamVjdC5hc3NpZ24obWV0YSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICByZXR1cm4gbWV0YTtcbiAgICB9LFxuICAgIHNldE1ldGEoa2V5LCB2YWx1ZSwgb3B0cyA9IHt9KSB7XG4gICAgICByZXR1cm4gdGhpcy5zZXRJdGVtKGtleSArIFwiJFwiLCB2YWx1ZSwgb3B0cyk7XG4gICAgfSxcbiAgICByZW1vdmVNZXRhKGtleSwgb3B0cyA9IHt9KSB7XG4gICAgICByZXR1cm4gdGhpcy5yZW1vdmVJdGVtKGtleSArIFwiJFwiLCBvcHRzKTtcbiAgICB9LFxuICAgIC8vIEtleXNcbiAgICBhc3luYyBnZXRLZXlzKGJhc2UsIG9wdHMgPSB7fSkge1xuICAgICAgYmFzZSA9IG5vcm1hbGl6ZUJhc2VLZXkoYmFzZSk7XG4gICAgICBjb25zdCBtb3VudHMgPSBnZXRNb3VudHMoYmFzZSwgdHJ1ZSk7XG4gICAgICBsZXQgbWFza2VkTW91bnRzID0gW107XG4gICAgICBjb25zdCBhbGxLZXlzID0gW107XG4gICAgICBsZXQgYWxsTW91bnRzU3VwcG9ydE1heERlcHRoID0gdHJ1ZTtcbiAgICAgIGZvciAoY29uc3QgbW91bnQgb2YgbW91bnRzKSB7XG4gICAgICAgIGlmICghbW91bnQuZHJpdmVyLmZsYWdzPy5tYXhEZXB0aCkge1xuICAgICAgICAgIGFsbE1vdW50c1N1cHBvcnRNYXhEZXB0aCA9IGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHJhd0tleXMgPSBhd2FpdCBhc3luY0NhbGwoXG4gICAgICAgICAgbW91bnQuZHJpdmVyLmdldEtleXMsXG4gICAgICAgICAgbW91bnQucmVsYXRpdmVCYXNlLFxuICAgICAgICAgIG9wdHNcbiAgICAgICAgKTtcbiAgICAgICAgZm9yIChjb25zdCBrZXkgb2YgcmF3S2V5cykge1xuICAgICAgICAgIGNvbnN0IGZ1bGxLZXkgPSBtb3VudC5tb3VudHBvaW50ICsgbm9ybWFsaXplS2V5KGtleSk7XG4gICAgICAgICAgaWYgKCFtYXNrZWRNb3VudHMuc29tZSgocCkgPT4gZnVsbEtleS5zdGFydHNXaXRoKHApKSkge1xuICAgICAgICAgICAgYWxsS2V5cy5wdXNoKGZ1bGxLZXkpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBtYXNrZWRNb3VudHMgPSBbXG4gICAgICAgICAgbW91bnQubW91bnRwb2ludCxcbiAgICAgICAgICAuLi5tYXNrZWRNb3VudHMuZmlsdGVyKChwKSA9PiAhcC5zdGFydHNXaXRoKG1vdW50Lm1vdW50cG9pbnQpKVxuICAgICAgICBdO1xuICAgICAgfVxuICAgICAgY29uc3Qgc2hvdWxkRmlsdGVyQnlEZXB0aCA9IG9wdHMubWF4RGVwdGggIT09IHZvaWQgMCAmJiAhYWxsTW91bnRzU3VwcG9ydE1heERlcHRoO1xuICAgICAgcmV0dXJuIGFsbEtleXMuZmlsdGVyKFxuICAgICAgICAoa2V5KSA9PiAoIXNob3VsZEZpbHRlckJ5RGVwdGggfHwgZmlsdGVyS2V5QnlEZXB0aChrZXksIG9wdHMubWF4RGVwdGgpKSAmJiBmaWx0ZXJLZXlCeUJhc2Uoa2V5LCBiYXNlKVxuICAgICAgKTtcbiAgICB9LFxuICAgIC8vIFV0aWxzXG4gICAgYXN5bmMgY2xlYXIoYmFzZSwgb3B0cyA9IHt9KSB7XG4gICAgICBiYXNlID0gbm9ybWFsaXplQmFzZUtleShiYXNlKTtcbiAgICAgIGF3YWl0IFByb21pc2UuYWxsKFxuICAgICAgICBnZXRNb3VudHMoYmFzZSwgZmFsc2UpLm1hcChhc3luYyAobSkgPT4ge1xuICAgICAgICAgIGlmIChtLmRyaXZlci5jbGVhcikge1xuICAgICAgICAgICAgcmV0dXJuIGFzeW5jQ2FsbChtLmRyaXZlci5jbGVhciwgbS5yZWxhdGl2ZUJhc2UsIG9wdHMpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBpZiAobS5kcml2ZXIucmVtb3ZlSXRlbSkge1xuICAgICAgICAgICAgY29uc3Qga2V5cyA9IGF3YWl0IG0uZHJpdmVyLmdldEtleXMobS5yZWxhdGl2ZUJhc2UgfHwgXCJcIiwgb3B0cyk7XG4gICAgICAgICAgICByZXR1cm4gUHJvbWlzZS5hbGwoXG4gICAgICAgICAgICAgIGtleXMubWFwKChrZXkpID0+IG0uZHJpdmVyLnJlbW92ZUl0ZW0oa2V5LCBvcHRzKSlcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfVxuICAgICAgICB9KVxuICAgICAgKTtcbiAgICB9LFxuICAgIGFzeW5jIGRpc3Bvc2UoKSB7XG4gICAgICBhd2FpdCBQcm9taXNlLmFsbChcbiAgICAgICAgT2JqZWN0LnZhbHVlcyhjb250ZXh0Lm1vdW50cykubWFwKChkcml2ZXIpID0+IGRpc3Bvc2UoZHJpdmVyKSlcbiAgICAgICk7XG4gICAgfSxcbiAgICBhc3luYyB3YXRjaChjYWxsYmFjaykge1xuICAgICAgYXdhaXQgc3RhcnRXYXRjaCgpO1xuICAgICAgY29udGV4dC53YXRjaExpc3RlbmVycy5wdXNoKGNhbGxiYWNrKTtcbiAgICAgIHJldHVybiBhc3luYyAoKSA9PiB7XG4gICAgICAgIGNvbnRleHQud2F0Y2hMaXN0ZW5lcnMgPSBjb250ZXh0LndhdGNoTGlzdGVuZXJzLmZpbHRlcihcbiAgICAgICAgICAobGlzdGVuZXIpID0+IGxpc3RlbmVyICE9PSBjYWxsYmFja1xuICAgICAgICApO1xuICAgICAgICBpZiAoY29udGV4dC53YXRjaExpc3RlbmVycy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICBhd2FpdCBzdG9wV2F0Y2goKTtcbiAgICAgICAgfVxuICAgICAgfTtcbiAgICB9LFxuICAgIGFzeW5jIHVud2F0Y2goKSB7XG4gICAgICBjb250ZXh0LndhdGNoTGlzdGVuZXJzID0gW107XG4gICAgICBhd2FpdCBzdG9wV2F0Y2goKTtcbiAgICB9LFxuICAgIC8vIE1vdW50XG4gICAgbW91bnQoYmFzZSwgZHJpdmVyKSB7XG4gICAgICBiYXNlID0gbm9ybWFsaXplQmFzZUtleShiYXNlKTtcbiAgICAgIGlmIChiYXNlICYmIGNvbnRleHQubW91bnRzW2Jhc2VdKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgYWxyZWFkeSBtb3VudGVkIGF0ICR7YmFzZX1gKTtcbiAgICAgIH1cbiAgICAgIGlmIChiYXNlKSB7XG4gICAgICAgIGNvbnRleHQubW91bnRwb2ludHMucHVzaChiYXNlKTtcbiAgICAgICAgY29udGV4dC5tb3VudHBvaW50cy5zb3J0KChhLCBiKSA9PiBiLmxlbmd0aCAtIGEubGVuZ3RoKTtcbiAgICAgIH1cbiAgICAgIGNvbnRleHQubW91bnRzW2Jhc2VdID0gZHJpdmVyO1xuICAgICAgaWYgKGNvbnRleHQud2F0Y2hpbmcpIHtcbiAgICAgICAgUHJvbWlzZS5yZXNvbHZlKHdhdGNoKGRyaXZlciwgb25DaGFuZ2UsIGJhc2UpKS50aGVuKCh1bndhdGNoZXIpID0+IHtcbiAgICAgICAgICBjb250ZXh0LnVud2F0Y2hbYmFzZV0gPSB1bndhdGNoZXI7XG4gICAgICAgIH0pLmNhdGNoKGNvbnNvbGUuZXJyb3IpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHN0b3JhZ2U7XG4gICAgfSxcbiAgICBhc3luYyB1bm1vdW50KGJhc2UsIF9kaXNwb3NlID0gdHJ1ZSkge1xuICAgICAgYmFzZSA9IG5vcm1hbGl6ZUJhc2VLZXkoYmFzZSk7XG4gICAgICBpZiAoIWJhc2UgfHwgIWNvbnRleHQubW91bnRzW2Jhc2VdKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGlmIChjb250ZXh0LndhdGNoaW5nICYmIGJhc2UgaW4gY29udGV4dC51bndhdGNoKSB7XG4gICAgICAgIGNvbnRleHQudW53YXRjaFtiYXNlXT8uKCk7XG4gICAgICAgIGRlbGV0ZSBjb250ZXh0LnVud2F0Y2hbYmFzZV07XG4gICAgICB9XG4gICAgICBpZiAoX2Rpc3Bvc2UpIHtcbiAgICAgICAgYXdhaXQgZGlzcG9zZShjb250ZXh0Lm1vdW50c1tiYXNlXSk7XG4gICAgICB9XG4gICAgICBjb250ZXh0Lm1vdW50cG9pbnRzID0gY29udGV4dC5tb3VudHBvaW50cy5maWx0ZXIoKGtleSkgPT4ga2V5ICE9PSBiYXNlKTtcbiAgICAgIGRlbGV0ZSBjb250ZXh0Lm1vdW50c1tiYXNlXTtcbiAgICB9LFxuICAgIGdldE1vdW50KGtleSA9IFwiXCIpIHtcbiAgICAgIGtleSA9IG5vcm1hbGl6ZUtleShrZXkpICsgXCI6XCI7XG4gICAgICBjb25zdCBtID0gZ2V0TW91bnQoa2V5KTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGRyaXZlcjogbS5kcml2ZXIsXG4gICAgICAgIGJhc2U6IG0uYmFzZVxuICAgICAgfTtcbiAgICB9LFxuICAgIGdldE1vdW50cyhiYXNlID0gXCJcIiwgb3B0cyA9IHt9KSB7XG4gICAgICBiYXNlID0gbm9ybWFsaXplS2V5KGJhc2UpO1xuICAgICAgY29uc3QgbW91bnRzID0gZ2V0TW91bnRzKGJhc2UsIG9wdHMucGFyZW50cyk7XG4gICAgICByZXR1cm4gbW91bnRzLm1hcCgobSkgPT4gKHtcbiAgICAgICAgZHJpdmVyOiBtLmRyaXZlcixcbiAgICAgICAgYmFzZTogbS5tb3VudHBvaW50XG4gICAgICB9KSk7XG4gICAgfSxcbiAgICAvLyBBbGlhc2VzXG4gICAga2V5czogKGJhc2UsIG9wdHMgPSB7fSkgPT4gc3RvcmFnZS5nZXRLZXlzKGJhc2UsIG9wdHMpLFxuICAgIGdldDogKGtleSwgb3B0cyA9IHt9KSA9PiBzdG9yYWdlLmdldEl0ZW0oa2V5LCBvcHRzKSxcbiAgICBzZXQ6IChrZXksIHZhbHVlLCBvcHRzID0ge30pID0+IHN0b3JhZ2Uuc2V0SXRlbShrZXksIHZhbHVlLCBvcHRzKSxcbiAgICBoYXM6IChrZXksIG9wdHMgPSB7fSkgPT4gc3RvcmFnZS5oYXNJdGVtKGtleSwgb3B0cyksXG4gICAgZGVsOiAoa2V5LCBvcHRzID0ge30pID0+IHN0b3JhZ2UucmVtb3ZlSXRlbShrZXksIG9wdHMpLFxuICAgIHJlbW92ZTogKGtleSwgb3B0cyA9IHt9KSA9PiBzdG9yYWdlLnJlbW92ZUl0ZW0oa2V5LCBvcHRzKVxuICB9O1xuICByZXR1cm4gc3RvcmFnZTtcbn1cbmFzeW5jIGZ1bmN0aW9uIHNuYXBzaG90KHN0b3JhZ2UsIGJhc2UpIHtcbiAgYmFzZSA9IG5vcm1hbGl6ZUJhc2VLZXkoYmFzZSk7XG4gIGNvbnN0IGtleXMgPSBhd2FpdCBzdG9yYWdlLmdldEtleXMoYmFzZSk7XG4gIGNvbnN0IHNuYXBzaG90MiA9IHt9O1xuICBhd2FpdCBQcm9taXNlLmFsbChcbiAgICBrZXlzLm1hcChhc3luYyAoa2V5KSA9PiB7XG4gICAgICBzbmFwc2hvdDJba2V5LnNsaWNlKGJhc2UubGVuZ3RoKV0gPSBhd2FpdCBzdG9yYWdlLmdldEl0ZW0oa2V5KTtcbiAgICB9KVxuICApO1xuICByZXR1cm4gc25hcHNob3QyO1xufVxuYXN5bmMgZnVuY3Rpb24gcmVzdG9yZVNuYXBzaG90KGRyaXZlciwgc25hcHNob3QyLCBiYXNlID0gXCJcIikge1xuICBiYXNlID0gbm9ybWFsaXplQmFzZUtleShiYXNlKTtcbiAgYXdhaXQgUHJvbWlzZS5hbGwoXG4gICAgT2JqZWN0LmVudHJpZXMoc25hcHNob3QyKS5tYXAoKGUpID0+IGRyaXZlci5zZXRJdGVtKGJhc2UgKyBlWzBdLCBlWzFdKSlcbiAgKTtcbn1cbmZ1bmN0aW9uIHdhdGNoKGRyaXZlciwgb25DaGFuZ2UsIGJhc2UpIHtcbiAgcmV0dXJuIGRyaXZlci53YXRjaCA/IGRyaXZlci53YXRjaCgoZXZlbnQsIGtleSkgPT4gb25DaGFuZ2UoZXZlbnQsIGJhc2UgKyBrZXkpKSA6ICgpID0+IHtcbiAgfTtcbn1cbmFzeW5jIGZ1bmN0aW9uIGRpc3Bvc2UoZHJpdmVyKSB7XG4gIGlmICh0eXBlb2YgZHJpdmVyLmRpc3Bvc2UgPT09IFwiZnVuY3Rpb25cIikge1xuICAgIGF3YWl0IGFzeW5jQ2FsbChkcml2ZXIuZGlzcG9zZSk7XG4gIH1cbn1cblxuY29uc3QgYnVpbHRpbkRyaXZlcnMgPSB7XG4gIFwiYXp1cmUtYXBwLWNvbmZpZ3VyYXRpb25cIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9henVyZS1hcHAtY29uZmlndXJhdGlvblwiLFxuICBcImF6dXJlQXBwQ29uZmlndXJhdGlvblwiOiBcInVuc3RvcmFnZS9kcml2ZXJzL2F6dXJlLWFwcC1jb25maWd1cmF0aW9uXCIsXG4gIFwiYXp1cmUtY29zbW9zXCI6IFwidW5zdG9yYWdlL2RyaXZlcnMvYXp1cmUtY29zbW9zXCIsXG4gIFwiYXp1cmVDb3Ntb3NcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9henVyZS1jb3Ntb3NcIixcbiAgXCJhenVyZS1rZXktdmF1bHRcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9henVyZS1rZXktdmF1bHRcIixcbiAgXCJhenVyZUtleVZhdWx0XCI6IFwidW5zdG9yYWdlL2RyaXZlcnMvYXp1cmUta2V5LXZhdWx0XCIsXG4gIFwiYXp1cmUtc3RvcmFnZS1ibG9iXCI6IFwidW5zdG9yYWdlL2RyaXZlcnMvYXp1cmUtc3RvcmFnZS1ibG9iXCIsXG4gIFwiYXp1cmVTdG9yYWdlQmxvYlwiOiBcInVuc3RvcmFnZS9kcml2ZXJzL2F6dXJlLXN0b3JhZ2UtYmxvYlwiLFxuICBcImF6dXJlLXN0b3JhZ2UtdGFibGVcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9henVyZS1zdG9yYWdlLXRhYmxlXCIsXG4gIFwiYXp1cmVTdG9yYWdlVGFibGVcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9henVyZS1zdG9yYWdlLXRhYmxlXCIsXG4gIFwiY2FwYWNpdG9yLXByZWZlcmVuY2VzXCI6IFwidW5zdG9yYWdlL2RyaXZlcnMvY2FwYWNpdG9yLXByZWZlcmVuY2VzXCIsXG4gIFwiY2FwYWNpdG9yUHJlZmVyZW5jZXNcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9jYXBhY2l0b3ItcHJlZmVyZW5jZXNcIixcbiAgXCJjbG91ZGZsYXJlLWt2LWJpbmRpbmdcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9jbG91ZGZsYXJlLWt2LWJpbmRpbmdcIixcbiAgXCJjbG91ZGZsYXJlS1ZCaW5kaW5nXCI6IFwidW5zdG9yYWdlL2RyaXZlcnMvY2xvdWRmbGFyZS1rdi1iaW5kaW5nXCIsXG4gIFwiY2xvdWRmbGFyZS1rdi1odHRwXCI6IFwidW5zdG9yYWdlL2RyaXZlcnMvY2xvdWRmbGFyZS1rdi1odHRwXCIsXG4gIFwiY2xvdWRmbGFyZUtWSHR0cFwiOiBcInVuc3RvcmFnZS9kcml2ZXJzL2Nsb3VkZmxhcmUta3YtaHR0cFwiLFxuICBcImNsb3VkZmxhcmUtcjItYmluZGluZ1wiOiBcInVuc3RvcmFnZS9kcml2ZXJzL2Nsb3VkZmxhcmUtcjItYmluZGluZ1wiLFxuICBcImNsb3VkZmxhcmVSMkJpbmRpbmdcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9jbG91ZGZsYXJlLXIyLWJpbmRpbmdcIixcbiAgXCJkYjBcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9kYjBcIixcbiAgXCJkZW5vLWt2LW5vZGVcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9kZW5vLWt2LW5vZGVcIixcbiAgXCJkZW5vS1ZOb2RlXCI6IFwidW5zdG9yYWdlL2RyaXZlcnMvZGVuby1rdi1ub2RlXCIsXG4gIFwiZGVuby1rdlwiOiBcInVuc3RvcmFnZS9kcml2ZXJzL2Rlbm8ta3ZcIixcbiAgXCJkZW5vS1ZcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9kZW5vLWt2XCIsXG4gIFwiZnMtbGl0ZVwiOiBcInVuc3RvcmFnZS9kcml2ZXJzL2ZzLWxpdGVcIixcbiAgXCJmc0xpdGVcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9mcy1saXRlXCIsXG4gIFwiZnNcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9mc1wiLFxuICBcImdpdGh1YlwiOiBcInVuc3RvcmFnZS9kcml2ZXJzL2dpdGh1YlwiLFxuICBcImh0dHBcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9odHRwXCIsXG4gIFwiaW5kZXhlZGJcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9pbmRleGVkYlwiLFxuICBcImxvY2Fsc3RvcmFnZVwiOiBcInVuc3RvcmFnZS9kcml2ZXJzL2xvY2Fsc3RvcmFnZVwiLFxuICBcImxydS1jYWNoZVwiOiBcInVuc3RvcmFnZS9kcml2ZXJzL2xydS1jYWNoZVwiLFxuICBcImxydUNhY2hlXCI6IFwidW5zdG9yYWdlL2RyaXZlcnMvbHJ1LWNhY2hlXCIsXG4gIFwibWVtb3J5XCI6IFwidW5zdG9yYWdlL2RyaXZlcnMvbWVtb3J5XCIsXG4gIFwibW9uZ29kYlwiOiBcInVuc3RvcmFnZS9kcml2ZXJzL21vbmdvZGJcIixcbiAgXCJuZXRsaWZ5LWJsb2JzXCI6IFwidW5zdG9yYWdlL2RyaXZlcnMvbmV0bGlmeS1ibG9ic1wiLFxuICBcIm5ldGxpZnlCbG9ic1wiOiBcInVuc3RvcmFnZS9kcml2ZXJzL25ldGxpZnktYmxvYnNcIixcbiAgXCJudWxsXCI6IFwidW5zdG9yYWdlL2RyaXZlcnMvbnVsbFwiLFxuICBcIm92ZXJsYXlcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9vdmVybGF5XCIsXG4gIFwicGxhbmV0c2NhbGVcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9wbGFuZXRzY2FsZVwiLFxuICBcInJlZGlzXCI6IFwidW5zdG9yYWdlL2RyaXZlcnMvcmVkaXNcIixcbiAgXCJzM1wiOiBcInVuc3RvcmFnZS9kcml2ZXJzL3MzXCIsXG4gIFwic2Vzc2lvbi1zdG9yYWdlXCI6IFwidW5zdG9yYWdlL2RyaXZlcnMvc2Vzc2lvbi1zdG9yYWdlXCIsXG4gIFwic2Vzc2lvblN0b3JhZ2VcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy9zZXNzaW9uLXN0b3JhZ2VcIixcbiAgXCJ1cGxvYWR0aGluZ1wiOiBcInVuc3RvcmFnZS9kcml2ZXJzL3VwbG9hZHRoaW5nXCIsXG4gIFwidXBzdGFzaFwiOiBcInVuc3RvcmFnZS9kcml2ZXJzL3Vwc3Rhc2hcIixcbiAgXCJ2ZXJjZWwtYmxvYlwiOiBcInVuc3RvcmFnZS9kcml2ZXJzL3ZlcmNlbC1ibG9iXCIsXG4gIFwidmVyY2VsQmxvYlwiOiBcInVuc3RvcmFnZS9kcml2ZXJzL3ZlcmNlbC1ibG9iXCIsXG4gIFwidmVyY2VsLWt2XCI6IFwidW5zdG9yYWdlL2RyaXZlcnMvdmVyY2VsLWt2XCIsXG4gIFwidmVyY2VsS1ZcIjogXCJ1bnN0b3JhZ2UvZHJpdmVycy92ZXJjZWwta3ZcIlxufTtcblxuZXhwb3J0IHsgYnVpbHRpbkRyaXZlcnMsIGNyZWF0ZVN0b3JhZ2UsIGRlZmluZURyaXZlciwgZmlsdGVyS2V5QnlCYXNlLCBmaWx0ZXJLZXlCeURlcHRoLCBqb2luS2V5cywgbm9ybWFsaXplQmFzZUtleSwgbm9ybWFsaXplS2V5LCByZXN0b3JlU25hcHNob3QsIHNuYXBzaG90IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unstorage/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/unstorage/dist/shared/unstorage.CoCt7NXC.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/unstorage/dist/shared/unstorage.CoCt7NXC.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ normalizeKey),\n/* harmony export */   b: () => (/* binding */ asyncCall),\n/* harmony export */   c: () => (/* binding */ filterKeyByBase),\n/* harmony export */   d: () => (/* binding */ stringify),\n/* harmony export */   e: () => (/* binding */ deserializeRaw),\n/* harmony export */   f: () => (/* binding */ filterKeyByDepth),\n/* harmony export */   j: () => (/* binding */ joinKeys),\n/* harmony export */   n: () => (/* binding */ normalizeBaseKey),\n/* harmony export */   p: () => (/* binding */ prefixStorage),\n/* harmony export */   s: () => (/* binding */ serializeRaw)\n/* harmony export */ });\nfunction wrapToPromise(value) {\n  if (!value || typeof value.then !== \"function\") {\n    return Promise.resolve(value);\n  }\n  return value;\n}\nfunction asyncCall(function_, ...arguments_) {\n  try {\n    return wrapToPromise(function_(...arguments_));\n  } catch (error) {\n    return Promise.reject(error);\n  }\n}\nfunction isPrimitive(value) {\n  const type = typeof value;\n  return value === null || type !== \"object\" && type !== \"function\";\n}\nfunction isPureObject(value) {\n  const proto = Object.getPrototypeOf(value);\n  return !proto || proto.isPrototypeOf(Object);\n}\nfunction stringify(value) {\n  if (isPrimitive(value)) {\n    return String(value);\n  }\n  if (isPureObject(value) || Array.isArray(value)) {\n    return JSON.stringify(value);\n  }\n  if (typeof value.toJSON === \"function\") {\n    return stringify(value.toJSON());\n  }\n  throw new Error(\"[unstorage] Cannot stringify value!\");\n}\nconst BASE64_PREFIX = \"base64:\";\nfunction serializeRaw(value) {\n  if (typeof value === \"string\") {\n    return value;\n  }\n  return BASE64_PREFIX + base64Encode(value);\n}\nfunction deserializeRaw(value) {\n  if (typeof value !== \"string\") {\n    return value;\n  }\n  if (!value.startsWith(BASE64_PREFIX)) {\n    return value;\n  }\n  return base64Decode(value.slice(BASE64_PREFIX.length));\n}\nfunction base64Decode(input) {\n  if (globalThis.Buffer) {\n    return Buffer.from(input, \"base64\");\n  }\n  return Uint8Array.from(\n    globalThis.atob(input),\n    (c) => c.codePointAt(0)\n  );\n}\nfunction base64Encode(input) {\n  if (globalThis.Buffer) {\n    return Buffer.from(input).toString(\"base64\");\n  }\n  return globalThis.btoa(String.fromCodePoint(...input));\n}\n\nconst storageKeyProperties = [\n  \"has\",\n  \"hasItem\",\n  \"get\",\n  \"getItem\",\n  \"getItemRaw\",\n  \"set\",\n  \"setItem\",\n  \"setItemRaw\",\n  \"del\",\n  \"remove\",\n  \"removeItem\",\n  \"getMeta\",\n  \"setMeta\",\n  \"removeMeta\",\n  \"getKeys\",\n  \"clear\",\n  \"mount\",\n  \"unmount\"\n];\nfunction prefixStorage(storage, base) {\n  base = normalizeBaseKey(base);\n  if (!base) {\n    return storage;\n  }\n  const nsStorage = { ...storage };\n  for (const property of storageKeyProperties) {\n    nsStorage[property] = (key = \"\", ...args) => (\n      // @ts-ignore\n      storage[property](base + key, ...args)\n    );\n  }\n  nsStorage.getKeys = (key = \"\", ...arguments_) => storage.getKeys(base + key, ...arguments_).then((keys) => keys.map((key2) => key2.slice(base.length)));\n  nsStorage.getItems = async (items, commonOptions) => {\n    const prefixedItems = items.map(\n      (item) => typeof item === \"string\" ? base + item : { ...item, key: base + item.key }\n    );\n    const results = await storage.getItems(prefixedItems, commonOptions);\n    return results.map((entry) => ({\n      key: entry.key.slice(base.length),\n      value: entry.value\n    }));\n  };\n  nsStorage.setItems = async (items, commonOptions) => {\n    const prefixedItems = items.map((item) => ({\n      key: base + item.key,\n      value: item.value,\n      options: item.options\n    }));\n    return storage.setItems(prefixedItems, commonOptions);\n  };\n  return nsStorage;\n}\nfunction normalizeKey(key) {\n  if (!key) {\n    return \"\";\n  }\n  return key.split(\"?\")[0]?.replace(/[/\\\\]/g, \":\").replace(/:+/g, \":\").replace(/^:|:$/g, \"\") || \"\";\n}\nfunction joinKeys(...keys) {\n  return normalizeKey(keys.join(\":\"));\n}\nfunction normalizeBaseKey(base) {\n  base = normalizeKey(base);\n  return base ? base + \":\" : \"\";\n}\nfunction filterKeyByDepth(key, depth) {\n  if (depth === void 0) {\n    return true;\n  }\n  let substrCount = 0;\n  let index = key.indexOf(\":\");\n  while (index > -1) {\n    substrCount++;\n    index = key.indexOf(\":\", index + 1);\n  }\n  return substrCount <= depth;\n}\nfunction filterKeyByBase(key, base) {\n  if (base) {\n    return key.startsWith(base) && key[key.length - 1] !== \"$\";\n  }\n  return key[key.length - 1] !== \"$\";\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unstorage/dist/shared/unstorage.CoCt7NXC.mjs\n");

/***/ })

};
;