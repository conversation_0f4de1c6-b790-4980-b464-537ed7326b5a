"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@wagmi";
exports.ids = ["vendor-chunks/@wagmi"];
exports.modules = {

/***/ "(ssr)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js":
/*!*************************************************************!*\
  !*** ./node_modules/@wagmi/connectors/dist/esm/metaMask.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   metaMask: () => (/* binding */ metaMask)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withRetry.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n\n\nmetaMask.type = 'metaMask';\nfunction metaMask(parameters = {}) {\n    let sdk;\n    let provider;\n    let providerPromise;\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let displayUri;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'metaMaskSDK',\n        name: 'MetaMask',\n        rdns: ['io.metamask', 'io.metamask.mobile'],\n        type: metaMask.type,\n        async setup() {\n            const provider = await this.getProvider();\n            if (provider?.on) {\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n                // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n                // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n            }\n        },\n        async connect({ chainId, isReconnecting } = {}) {\n            const provider = await this.getProvider();\n            if (!displayUri) {\n                displayUri = this.onDisplayUri;\n                provider.on('display_uri', displayUri);\n            }\n            let accounts = [];\n            if (isReconnecting)\n                accounts = await this.getAccounts().catch(() => []);\n            try {\n                let signResponse;\n                let connectWithResponse;\n                if (!accounts?.length) {\n                    if (parameters.connectAndSign || parameters.connectWith) {\n                        if (parameters.connectAndSign)\n                            signResponse = await sdk.connectAndSign({\n                                msg: parameters.connectAndSign,\n                            });\n                        else if (parameters.connectWith)\n                            connectWithResponse = await sdk.connectWith({\n                                method: parameters.connectWith.method,\n                                params: parameters.connectWith.params,\n                            });\n                        accounts = await this.getAccounts();\n                    }\n                    else {\n                        const requestedAccounts = (await sdk.connect());\n                        accounts = requestedAccounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                    }\n                }\n                // Switch to chain if provided\n                let currentChainId = (await this.getChainId());\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                if (displayUri) {\n                    provider.removeListener('display_uri', displayUri);\n                    displayUri = undefined;\n                }\n                if (signResponse)\n                    provider.emit('connectAndSign', {\n                        accounts,\n                        chainId: currentChainId,\n                        signResponse,\n                    });\n                else if (connectWithResponse)\n                    provider.emit('connectWith', {\n                        accounts,\n                        chainId: currentChainId,\n                        connectWithResponse,\n                    });\n                // Manage EIP-1193 event listeners\n                // https://eips.ethereum.org/EIPS/eip-1193#events\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.ResourceUnavailableRpcError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.ResourceUnavailableRpcError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            await sdk.terminate();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            const accounts = (await provider.request({\n                method: 'eth_accounts',\n            }));\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = provider.getChainId() ||\n                (await provider?.request({ method: 'eth_chainId' }));\n            return Number(chainId);\n        },\n        async getProvider() {\n            async function initProvider() {\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const MetaMaskSDK = await (async () => {\n                    const { default: SDK } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/debug\"), __webpack_require__.e(\"vendor-chunks/ms\"), __webpack_require__.e(\"vendor-chunks/supports-color\"), __webpack_require__.e(\"vendor-chunks/has-flag\"), __webpack_require__.e(\"vendor-chunks/engine.io-client\"), __webpack_require__.e(\"vendor-chunks/ws\"), __webpack_require__.e(\"vendor-chunks/socket.io-client\"), __webpack_require__.e(\"vendor-chunks/socket.io-parser\"), __webpack_require__.e(\"vendor-chunks/whatwg-url\"), __webpack_require__.e(\"vendor-chunks/uuid\"), __webpack_require__.e(\"vendor-chunks/engine.io-parser\"), __webpack_require__.e(\"vendor-chunks/utf-8-validate\"), __webpack_require__.e(\"vendor-chunks/tr46\"), __webpack_require__.e(\"vendor-chunks/node-gyp-build\"), __webpack_require__.e(\"vendor-chunks/bufferutil\"), __webpack_require__.e(\"vendor-chunks/node-fetch\"), __webpack_require__.e(\"vendor-chunks/@socket.io\"), __webpack_require__.e(\"vendor-chunks/xmlhttprequest-ssl\"), __webpack_require__.e(\"vendor-chunks/webidl-conversions\"), __webpack_require__.e(\"vendor-chunks/eventemitter2\"), __webpack_require__.e(\"vendor-chunks/cross-fetch\"), __webpack_require__.e(\"vendor-chunks/@metamask\")]).then(__webpack_require__.bind(__webpack_require__, /*! @metamask/sdk */ \"(ssr)/./node_modules/@metamask/sdk/dist/browser/es/metamask-sdk.js\"));\n                    if (typeof SDK !== 'function' && typeof SDK.default === 'function')\n                        return SDK.default;\n                    return SDK;\n                })();\n                const readonlyRPCMap = {};\n                for (const chain of config.chains)\n                    readonlyRPCMap[(0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chain.id)] = (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_4__.extractRpcUrls)({\n                        chain,\n                        transports: config.transports,\n                    })?.[0];\n                sdk = new MetaMaskSDK({\n                    _source: 'wagmi',\n                    forceDeleteProvider: false,\n                    forceInjectProvider: false,\n                    injectProvider: false,\n                    // Workaround cast since MetaMask SDK does not support `'exactOptionalPropertyTypes'`\n                    ...parameters,\n                    readonlyRPCMap,\n                    dappMetadata: {\n                        ...parameters.dappMetadata,\n                        // Test if name and url are set AND not empty\n                        name: parameters.dappMetadata?.name\n                            ? parameters.dappMetadata?.name\n                            : 'wagmi',\n                        url: parameters.dappMetadata?.url\n                            ? parameters.dappMetadata?.url\n                            : typeof window !== 'undefined'\n                                ? window.location.origin\n                                : 'https://wagmi.sh',\n                    },\n                    useDeeplink: parameters.useDeeplink ?? true,\n                });\n                const result = await sdk.init();\n                // On initial load, sometimes `sdk.getProvider` does not return provider.\n                // https://github.com/wevm/wagmi/issues/4367\n                // Use result of `init` call if available.\n                const provider = (() => {\n                    if (result?.activeProvider)\n                        return result.activeProvider;\n                    return sdk.getProvider();\n                })();\n                if (!provider)\n                    throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_5__.ProviderNotFoundError();\n                return provider;\n            }\n            if (!provider) {\n                if (!providerPromise)\n                    providerPromise = initProvider();\n                provider = await providerPromise;\n            }\n            return provider;\n        },\n        async isAuthorized() {\n            try {\n                // MetaMask mobile provider sometimes fails to immediately resolve\n                // JSON-RPC requests on page load\n                const timeout = 200;\n                const accounts = await (0,viem__WEBPACK_IMPORTED_MODULE_6__.withRetry)(() => (0,viem__WEBPACK_IMPORTED_MODULE_7__.withTimeout)(() => this.getAccounts(), { timeout }), {\n                    delay: timeout + 1,\n                    retryCount: 3,\n                });\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_8__.ChainNotConfiguredError());\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chainId) }],\n                });\n                // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n                // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n                // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n                // this callback or an externally emitted `'chainChanged'` event.\n                // https://github.com/MetaMask/metamask-extension/issues/24247\n                await waitForChainIdToSync();\n                await sendAndWaitForChangeEvent(chainId);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                // Indicates chain is not added to provider\n                if (error.code === 4902 ||\n                    // Unwrapping for MetaMask Mobile\n                    // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n                    error\n                        ?.data?.originalError?.code === 4902) {\n                    try {\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [\n                                {\n                                    blockExplorerUrls: (() => {\n                                        const { default: blockExplorer, ...blockExplorers } = chain.blockExplorers ?? {};\n                                        if (addEthereumChainParameter?.blockExplorerUrls)\n                                            return addEthereumChainParameter.blockExplorerUrls;\n                                        if (blockExplorer)\n                                            return [\n                                                blockExplorer.url,\n                                                ...Object.values(blockExplorers).map((x) => x.url),\n                                            ];\n                                        return;\n                                    })(),\n                                    chainId: (0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chainId),\n                                    chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                                    iconUrls: addEthereumChainParameter?.iconUrls,\n                                    nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                        chain.nativeCurrency,\n                                    rpcUrls: (() => {\n                                        if (addEthereumChainParameter?.rpcUrls?.length)\n                                            return addEthereumChainParameter.rpcUrls;\n                                        return [chain.rpcUrls.default?.http[0] ?? ''];\n                                    })(),\n                                },\n                            ],\n                        });\n                        await waitForChainIdToSync();\n                        await sendAndWaitForChangeEvent(chainId);\n                        return chain;\n                    }\n                    catch (err) {\n                        const error = err;\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n            async function waitForChainIdToSync() {\n                // On mobile, there is a race condition between the result of `'wallet_addEthereumChain'` and `'eth_chainId'`.\n                // To avoid this, we wait for `'eth_chainId'` to return the expected chain ID with a retry loop.\n                await (0,viem__WEBPACK_IMPORTED_MODULE_6__.withRetry)(async () => {\n                    const value = (0,viem__WEBPACK_IMPORTED_MODULE_9__.hexToNumber)(\n                    // `'eth_chainId'` is cached by the MetaMask SDK side to avoid unnecessary deeplinks\n                    (await provider.request({ method: 'eth_chainId' })));\n                    // `value` doesn't match expected `chainId`, throw to trigger retry\n                    if (value !== chainId)\n                        throw new Error('User rejected switch after adding network.');\n                    return value;\n                }, {\n                    delay: 50,\n                    retryCount: 20, // android device encryption is slower\n                });\n            }\n            async function sendAndWaitForChangeEvent(chainId) {\n                await new Promise((resolve) => {\n                    const listener = ((data) => {\n                        if ('chainId' in data && data.chainId === chainId) {\n                            config.emitter.off('change', listener);\n                            resolve();\n                        }\n                    });\n                    config.emitter.on('change', listener);\n                    config.emitter.emit('change', { chainId });\n                });\n            }\n        },\n        async onAccountsChanged(accounts) {\n            // Disconnect if there are no accounts\n            if (accounts.length === 0) {\n                // ... and using browser extension\n                if (sdk.isExtensionActive())\n                    this.onDisconnect();\n                // FIXME(upstream): Mobile app sometimes emits invalid `accountsChanged` event with empty accounts array\n                else\n                    return;\n            }\n            // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n            else if (config.emitter.listenerCount('connect')) {\n                const chainId = (await this.getChainId()).toString();\n                this.onConnect({ chainId });\n            }\n            // Regular change event\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const accounts = await this.getAccounts();\n            if (accounts.length === 0)\n                return;\n            const chainId = Number(connectInfo.chainId);\n            config.emitter.emit('connect', { accounts, chainId });\n            const provider = await this.getProvider();\n            if (connect) {\n                provider.removeListener('connect', connect);\n                connect = undefined;\n            }\n            if (!accountsChanged) {\n                accountsChanged = this.onAccountsChanged.bind(this);\n                provider.on('accountsChanged', accountsChanged);\n            }\n            if (!chainChanged) {\n                chainChanged = this.onChainChanged.bind(this);\n                provider.on('chainChanged', chainChanged);\n            }\n            if (!disconnect) {\n                disconnect = this.onDisconnect.bind(this);\n                provider.on('disconnect', disconnect);\n            }\n        },\n        async onDisconnect(error) {\n            const provider = await this.getProvider();\n            // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n            // https://github.com/MetaMask/providers/pull/120\n            if (error && error.code === 1013) {\n                if (provider && !!(await this.getAccounts()).length)\n                    return;\n            }\n            config.emitter.emit('disconnect');\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n        },\n        onDisplayUri(uri) {\n            config.emitter.emit('message', { type: 'display_uri', data: uri });\n        },\n    }));\n}\n//# sourceMappingURL=metaMask.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/connect.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/connect.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   connect: () => (/* binding */ connect)\n/* harmony export */ });\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n\n/** https://wagmi.sh/core/api/actions/connect */\nasync function connect(config, parameters) {\n    // \"Register\" connector if not already created\n    let connector;\n    if (typeof parameters.connector === 'function') {\n        connector = config._internal.connectors.setup(parameters.connector);\n    }\n    else\n        connector = parameters.connector;\n    // Check if connector is already connected\n    if (connector.uid === config.state.current)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorAlreadyConnectedError();\n    try {\n        config.setState((x) => ({ ...x, status: 'connecting' }));\n        connector.emitter.emit('message', { type: 'connecting' });\n        const { connector: _, ...rest } = parameters;\n        const data = await connector.connect(rest);\n        const accounts = data.accounts;\n        connector.emitter.off('connect', config._internal.events.connect);\n        connector.emitter.on('change', config._internal.events.change);\n        connector.emitter.on('disconnect', config._internal.events.disconnect);\n        await config.storage?.setItem('recentConnectorId', connector.id);\n        config.setState((x) => ({\n            ...x,\n            connections: new Map(x.connections).set(connector.uid, {\n                accounts,\n                chainId: data.chainId,\n                connector: connector,\n            }),\n            current: connector.uid,\n            status: 'connected',\n        }));\n        return { accounts, chainId: data.chainId };\n    }\n    catch (error) {\n        config.setState((x) => ({\n            ...x,\n            // Keep existing connector connected in case of error\n            status: x.current ? 'connected' : 'disconnected',\n        }));\n        throw error;\n    }\n}\n//# sourceMappingURL=connect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/connect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/disconnect.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/disconnect.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   disconnect: () => (/* binding */ disconnect)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/disconnect */\nasync function disconnect(config, parameters = {}) {\n    let connector;\n    if (parameters.connector)\n        connector = parameters.connector;\n    else {\n        const { connections, current } = config.state;\n        const connection = connections.get(current);\n        connector = connection?.connector;\n    }\n    const connections = config.state.connections;\n    if (connector) {\n        await connector.disconnect();\n        connector.emitter.off('change', config._internal.events.change);\n        connector.emitter.off('disconnect', config._internal.events.disconnect);\n        connector.emitter.on('connect', config._internal.events.connect);\n        connections.delete(connector.uid);\n    }\n    config.setState((x) => {\n        // if no connections exist, move to disconnected state\n        if (connections.size === 0)\n            return {\n                ...x,\n                connections: new Map(),\n                current: null,\n                status: 'disconnected',\n            };\n        // switch over to another connection\n        const nextConnection = connections.values().next().value;\n        return {\n            ...x,\n            connections: new Map(connections),\n            current: nextConnection.connector.uid,\n        };\n    });\n    // Set recent connector if exists\n    {\n        const current = config.state.current;\n        if (!current)\n            return;\n        const connector = config.state.connections.get(current)?.connector;\n        if (!connector)\n            return;\n        await config.storage?.setItem('recentConnectorId', connector.id);\n    }\n}\n//# sourceMappingURL=disconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/disconnect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getAccount.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getAccount.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAccount: () => (/* binding */ getAccount)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/getAccount */\nfunction getAccount(config) {\n    const uid = config.state.current;\n    const connection = config.state.connections.get(uid);\n    const addresses = connection?.accounts;\n    const address = addresses?.[0];\n    const chain = config.chains.find((chain) => chain.id === connection?.chainId);\n    const status = config.state.status;\n    switch (status) {\n        case 'connected':\n            return {\n                address: address,\n                addresses: addresses,\n                chain,\n                chainId: connection?.chainId,\n                connector: connection?.connector,\n                isConnected: true,\n                isConnecting: false,\n                isDisconnected: false,\n                isReconnecting: false,\n                status,\n            };\n        case 'reconnecting':\n            return {\n                address,\n                addresses,\n                chain,\n                chainId: connection?.chainId,\n                connector: connection?.connector,\n                isConnected: !!address,\n                isConnecting: false,\n                isDisconnected: false,\n                isReconnecting: true,\n                status,\n            };\n        case 'connecting':\n            return {\n                address,\n                addresses,\n                chain,\n                chainId: connection?.chainId,\n                connector: connection?.connector,\n                isConnected: false,\n                isConnecting: true,\n                isDisconnected: false,\n                isReconnecting: false,\n                status,\n            };\n        case 'disconnected':\n            return {\n                address: undefined,\n                addresses: undefined,\n                chain: undefined,\n                chainId: undefined,\n                connector: undefined,\n                isConnected: false,\n                isConnecting: false,\n                isDisconnected: true,\n                isReconnecting: false,\n                status,\n            };\n    }\n}\n//# sourceMappingURL=getAccount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getAccount.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getChainId.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getChainId.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getChainId: () => (/* binding */ getChainId)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/getChainId */\nfunction getChainId(config) {\n    return config.state.chainId;\n}\n//# sourceMappingURL=getChainId.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9nZXRDaGFpbklkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvZ2V0Q2hhaW5JZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL2dldENoYWluSWQgKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRDaGFpbklkKGNvbmZpZykge1xuICAgIHJldHVybiBjb25maWcuc3RhdGUuY2hhaW5JZDtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldENoYWluSWQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getChainId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnections.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getConnections.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getConnections: () => (/* binding */ getConnections)\n/* harmony export */ });\n/* harmony import */ var _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/deepEqual.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\");\n\nlet previousConnections = [];\n/** https://wagmi.sh/core/api/actions/getConnections */\nfunction getConnections(config) {\n    const connections = [...config.state.connections.values()];\n    if (config.state.status === 'reconnecting')\n        return previousConnections;\n    if ((0,_utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_0__.deepEqual)(previousConnections, connections))\n        return previousConnections;\n    previousConnections = connections;\n    return connections;\n}\n//# sourceMappingURL=getConnections.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9nZXRDb25uZWN0aW9ucy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrRDtBQUNsRDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhEQUFTO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9nZXRDb25uZWN0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkZWVwRXF1YWwgfSBmcm9tICcuLi91dGlscy9kZWVwRXF1YWwuanMnO1xubGV0IHByZXZpb3VzQ29ubmVjdGlvbnMgPSBbXTtcbi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvZ2V0Q29ubmVjdGlvbnMgKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRDb25uZWN0aW9ucyhjb25maWcpIHtcbiAgICBjb25zdCBjb25uZWN0aW9ucyA9IFsuLi5jb25maWcuc3RhdGUuY29ubmVjdGlvbnMudmFsdWVzKCldO1xuICAgIGlmIChjb25maWcuc3RhdGUuc3RhdHVzID09PSAncmVjb25uZWN0aW5nJylcbiAgICAgICAgcmV0dXJuIHByZXZpb3VzQ29ubmVjdGlvbnM7XG4gICAgaWYgKGRlZXBFcXVhbChwcmV2aW91c0Nvbm5lY3Rpb25zLCBjb25uZWN0aW9ucykpXG4gICAgICAgIHJldHVybiBwcmV2aW91c0Nvbm5lY3Rpb25zO1xuICAgIHByZXZpb3VzQ29ubmVjdGlvbnMgPSBjb25uZWN0aW9ucztcbiAgICByZXR1cm4gY29ubmVjdGlvbnM7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXRDb25uZWN0aW9ucy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnections.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getConnectorClient: () => (/* binding */ getConnectorClient)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/createClient.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/transports/custom.js\");\n/* harmony import */ var viem_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/utils */ \"(ssr)/./node_modules/viem/_esm/accounts/utils/parseAccount.js\");\n/* harmony import */ var viem_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/utils */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/getConnectorClient */\nasync function getConnectorClient(config, parameters = {}) {\n    // Get connection\n    let connection;\n    if (parameters.connector) {\n        const { connector } = parameters;\n        if (config.state.status === 'reconnecting' &&\n            !connector.getAccounts &&\n            !connector.getChainId)\n            throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorUnavailableReconnectingError({ connector });\n        const [accounts, chainId] = await Promise.all([\n            connector.getAccounts().catch((e) => {\n                if (parameters.account === null)\n                    return [];\n                throw e;\n            }),\n            connector.getChainId(),\n        ]);\n        connection = {\n            accounts: accounts,\n            chainId,\n            connector,\n        };\n    }\n    else\n        connection = config.state.connections.get(config.state.current);\n    if (!connection)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorNotConnectedError();\n    const chainId = parameters.chainId ?? connection.chainId;\n    // Check connector using same chainId as connection\n    const connectorChainId = await connection.connector.getChainId();\n    if (connectorChainId !== connection.chainId)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorChainMismatchError({\n            connectionChainId: connection.chainId,\n            connectorChainId,\n        });\n    const connector = connection.connector;\n    if (connector.getClient)\n        return connector.getClient({ chainId });\n    // Default using `custom` transport\n    const account = (0,viem_utils__WEBPACK_IMPORTED_MODULE_1__.parseAccount)(parameters.account ?? connection.accounts[0]);\n    if (account)\n        account.address = (0,viem_utils__WEBPACK_IMPORTED_MODULE_2__.getAddress)(account.address); // TODO: Checksum address as part of `parseAccount`?\n    // If account was provided, check that it exists on the connector\n    if (parameters.account &&\n        !connection.accounts.some((x) => x.toLowerCase() === account.address.toLowerCase()))\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorAccountNotFoundError({\n            address: account.address,\n            connector,\n        });\n    const chain = config.chains.find((chain) => chain.id === chainId);\n    const provider = (await connection.connector.getProvider({ chainId }));\n    return (0,viem__WEBPACK_IMPORTED_MODULE_3__.createClient)({\n        account,\n        chain,\n        name: 'Connector Client',\n        transport: (opts) => (0,viem__WEBPACK_IMPORTED_MODULE_4__.custom)(provider)({ ...opts, retryCount: 0 }),\n    });\n}\n//# sourceMappingURL=getConnectorClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectors.js":
/*!********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getConnectors.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getConnectors: () => (/* binding */ getConnectors)\n/* harmony export */ });\n/* harmony import */ var _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/deepEqual.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\");\n\nlet previousConnectors = [];\n/** https://wagmi.sh/core/api/actions/getConnectors */\nfunction getConnectors(config) {\n    const connectors = config.connectors;\n    if ((0,_utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_0__.deepEqual)(previousConnectors, connectors))\n        return previousConnectors;\n    previousConnectors = connectors;\n    return connectors;\n}\n//# sourceMappingURL=getConnectors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9nZXRDb25uZWN0b3JzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtEO0FBQ2xEO0FBQ0E7QUFDTztBQUNQO0FBQ0EsUUFBUSw4REFBUztBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvZ2V0Q29ubmVjdG9ycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkZWVwRXF1YWwgfSBmcm9tICcuLi91dGlscy9kZWVwRXF1YWwuanMnO1xubGV0IHByZXZpb3VzQ29ubmVjdG9ycyA9IFtdO1xuLyoqIGh0dHBzOi8vd2FnbWkuc2gvY29yZS9hcGkvYWN0aW9ucy9nZXRDb25uZWN0b3JzICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0Q29ubmVjdG9ycyhjb25maWcpIHtcbiAgICBjb25zdCBjb25uZWN0b3JzID0gY29uZmlnLmNvbm5lY3RvcnM7XG4gICAgaWYgKGRlZXBFcXVhbChwcmV2aW91c0Nvbm5lY3RvcnMsIGNvbm5lY3RvcnMpKVxuICAgICAgICByZXR1cm4gcHJldmlvdXNDb25uZWN0b3JzO1xuICAgIHByZXZpb3VzQ29ubmVjdG9ycyA9IGNvbm5lY3RvcnM7XG4gICAgcmV0dXJuIGNvbm5lY3RvcnM7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXRDb25uZWN0b3JzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContract.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/readContract.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   readContract: () => (/* binding */ readContract)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/readContract.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\n/** https://wagmi.sh/core/api/actions/readContract */\nfunction readContract(config, parameters) {\n    const { chainId, ...rest } = parameters;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.readContract, 'readContract');\n    return action(rest);\n}\n//# sourceMappingURL=readContract.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9yZWFkQ29udHJhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtFO0FBQ2hCO0FBQ2xEO0FBQ087QUFDUCxZQUFZLG1CQUFtQjtBQUMvQixzQ0FBc0MsU0FBUztBQUMvQyxtQkFBbUIsOERBQVMsU0FBUyxzREFBaUI7QUFDdEQ7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvcmVhZENvbnRyYWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJlYWRDb250cmFjdCBhcyB2aWVtX3JlYWRDb250cmFjdCwgfSBmcm9tICd2aWVtL2FjdGlvbnMnO1xuaW1wb3J0IHsgZ2V0QWN0aW9uIH0gZnJvbSAnLi4vdXRpbHMvZ2V0QWN0aW9uLmpzJztcbi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvcmVhZENvbnRyYWN0ICovXG5leHBvcnQgZnVuY3Rpb24gcmVhZENvbnRyYWN0KGNvbmZpZywgcGFyYW1ldGVycykge1xuICAgIGNvbnN0IHsgY2hhaW5JZCwgLi4ucmVzdCB9ID0gcGFyYW1ldGVycztcbiAgICBjb25zdCBjbGllbnQgPSBjb25maWcuZ2V0Q2xpZW50KHsgY2hhaW5JZCB9KTtcbiAgICBjb25zdCBhY3Rpb24gPSBnZXRBY3Rpb24oY2xpZW50LCB2aWVtX3JlYWRDb250cmFjdCwgJ3JlYWRDb250cmFjdCcpO1xuICAgIHJldHVybiBhY3Rpb24ocmVzdCk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWFkQ29udHJhY3QuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/reconnect.js":
/*!****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/reconnect.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reconnect: () => (/* binding */ reconnect)\n/* harmony export */ });\nlet isReconnecting = false;\n/** https://wagmi.sh/core/api/actions/reconnect */\nasync function reconnect(config, parameters = {}) {\n    // If already reconnecting, do nothing\n    if (isReconnecting)\n        return [];\n    isReconnecting = true;\n    config.setState((x) => ({\n        ...x,\n        status: x.current ? 'reconnecting' : 'connecting',\n    }));\n    const connectors = [];\n    if (parameters.connectors?.length) {\n        for (const connector_ of parameters.connectors) {\n            let connector;\n            // \"Register\" connector if not already created\n            if (typeof connector_ === 'function')\n                connector = config._internal.connectors.setup(connector_);\n            else\n                connector = connector_;\n            connectors.push(connector);\n        }\n    }\n    else\n        connectors.push(...config.connectors);\n    // Try recently-used connectors first\n    let recentConnectorId;\n    try {\n        recentConnectorId = await config.storage?.getItem('recentConnectorId');\n    }\n    catch { }\n    const scores = {};\n    for (const [, connection] of config.state.connections) {\n        scores[connection.connector.id] = 1;\n    }\n    if (recentConnectorId)\n        scores[recentConnectorId] = 0;\n    const sorted = Object.keys(scores).length > 0\n        ? // .toSorted()\n            [...connectors].sort((a, b) => (scores[a.id] ?? 10) - (scores[b.id] ?? 10))\n        : connectors;\n    // Iterate through each connector and try to connect\n    let connected = false;\n    const connections = [];\n    const providers = [];\n    for (const connector of sorted) {\n        const provider = await connector.getProvider().catch(() => undefined);\n        if (!provider)\n            continue;\n        // If we already have an instance of this connector's provider,\n        // then we have already checked it (ie. injected connectors can\n        // share the same `window.ethereum` instance, so we don't want to\n        // connect to it again).\n        if (providers.some((x) => x === provider))\n            continue;\n        const isAuthorized = await connector.isAuthorized();\n        if (!isAuthorized)\n            continue;\n        const data = await connector\n            .connect({ isReconnecting: true })\n            .catch(() => null);\n        if (!data)\n            continue;\n        connector.emitter.off('connect', config._internal.events.connect);\n        connector.emitter.on('change', config._internal.events.change);\n        connector.emitter.on('disconnect', config._internal.events.disconnect);\n        config.setState((x) => {\n            const connections = new Map(connected ? x.connections : new Map()).set(connector.uid, { accounts: data.accounts, chainId: data.chainId, connector });\n            return {\n                ...x,\n                current: connected ? x.current : connector.uid,\n                connections,\n            };\n        });\n        connections.push({\n            accounts: data.accounts,\n            chainId: data.chainId,\n            connector,\n        });\n        providers.push(provider);\n        connected = true;\n    }\n    // Prevent overwriting connected status from race condition\n    if (config.state.status === 'reconnecting' ||\n        config.state.status === 'connecting') {\n        // If connecting didn't succeed, set to disconnected\n        if (!connected)\n            config.setState((x) => ({\n                ...x,\n                connections: new Map(),\n                current: null,\n                status: 'disconnected',\n            }));\n        else\n            config.setState((x) => ({ ...x, status: 'connected' }));\n    }\n    isReconnecting = false;\n    return connections;\n}\n//# sourceMappingURL=reconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9yZWNvbm5lY3QuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDTyxnREFBZ0Q7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLHNCQUFzQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9HQUFvRywyREFBMkQ7QUFDL0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLHNDQUFzQywyQkFBMkI7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9hY3Rpb25zL3JlY29ubmVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgaXNSZWNvbm5lY3RpbmcgPSBmYWxzZTtcbi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvcmVjb25uZWN0ICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmVjb25uZWN0KGNvbmZpZywgcGFyYW1ldGVycyA9IHt9KSB7XG4gICAgLy8gSWYgYWxyZWFkeSByZWNvbm5lY3RpbmcsIGRvIG5vdGhpbmdcbiAgICBpZiAoaXNSZWNvbm5lY3RpbmcpXG4gICAgICAgIHJldHVybiBbXTtcbiAgICBpc1JlY29ubmVjdGluZyA9IHRydWU7XG4gICAgY29uZmlnLnNldFN0YXRlKCh4KSA9PiAoe1xuICAgICAgICAuLi54LFxuICAgICAgICBzdGF0dXM6IHguY3VycmVudCA/ICdyZWNvbm5lY3RpbmcnIDogJ2Nvbm5lY3RpbmcnLFxuICAgIH0pKTtcbiAgICBjb25zdCBjb25uZWN0b3JzID0gW107XG4gICAgaWYgKHBhcmFtZXRlcnMuY29ubmVjdG9ycz8ubGVuZ3RoKSB7XG4gICAgICAgIGZvciAoY29uc3QgY29ubmVjdG9yXyBvZiBwYXJhbWV0ZXJzLmNvbm5lY3RvcnMpIHtcbiAgICAgICAgICAgIGxldCBjb25uZWN0b3I7XG4gICAgICAgICAgICAvLyBcIlJlZ2lzdGVyXCIgY29ubmVjdG9yIGlmIG5vdCBhbHJlYWR5IGNyZWF0ZWRcbiAgICAgICAgICAgIGlmICh0eXBlb2YgY29ubmVjdG9yXyA9PT0gJ2Z1bmN0aW9uJylcbiAgICAgICAgICAgICAgICBjb25uZWN0b3IgPSBjb25maWcuX2ludGVybmFsLmNvbm5lY3RvcnMuc2V0dXAoY29ubmVjdG9yXyk7XG4gICAgICAgICAgICBlbHNlXG4gICAgICAgICAgICAgICAgY29ubmVjdG9yID0gY29ubmVjdG9yXztcbiAgICAgICAgICAgIGNvbm5lY3RvcnMucHVzaChjb25uZWN0b3IpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGVsc2VcbiAgICAgICAgY29ubmVjdG9ycy5wdXNoKC4uLmNvbmZpZy5jb25uZWN0b3JzKTtcbiAgICAvLyBUcnkgcmVjZW50bHktdXNlZCBjb25uZWN0b3JzIGZpcnN0XG4gICAgbGV0IHJlY2VudENvbm5lY3RvcklkO1xuICAgIHRyeSB7XG4gICAgICAgIHJlY2VudENvbm5lY3RvcklkID0gYXdhaXQgY29uZmlnLnN0b3JhZ2U/LmdldEl0ZW0oJ3JlY2VudENvbm5lY3RvcklkJyk7XG4gICAgfVxuICAgIGNhdGNoIHsgfVxuICAgIGNvbnN0IHNjb3JlcyA9IHt9O1xuICAgIGZvciAoY29uc3QgWywgY29ubmVjdGlvbl0gb2YgY29uZmlnLnN0YXRlLmNvbm5lY3Rpb25zKSB7XG4gICAgICAgIHNjb3Jlc1tjb25uZWN0aW9uLmNvbm5lY3Rvci5pZF0gPSAxO1xuICAgIH1cbiAgICBpZiAocmVjZW50Q29ubmVjdG9ySWQpXG4gICAgICAgIHNjb3Jlc1tyZWNlbnRDb25uZWN0b3JJZF0gPSAwO1xuICAgIGNvbnN0IHNvcnRlZCA9IE9iamVjdC5rZXlzKHNjb3JlcykubGVuZ3RoID4gMFxuICAgICAgICA/IC8vIC50b1NvcnRlZCgpXG4gICAgICAgICAgICBbLi4uY29ubmVjdG9yc10uc29ydCgoYSwgYikgPT4gKHNjb3Jlc1thLmlkXSA/PyAxMCkgLSAoc2NvcmVzW2IuaWRdID8/IDEwKSlcbiAgICAgICAgOiBjb25uZWN0b3JzO1xuICAgIC8vIEl0ZXJhdGUgdGhyb3VnaCBlYWNoIGNvbm5lY3RvciBhbmQgdHJ5IHRvIGNvbm5lY3RcbiAgICBsZXQgY29ubmVjdGVkID0gZmFsc2U7XG4gICAgY29uc3QgY29ubmVjdGlvbnMgPSBbXTtcbiAgICBjb25zdCBwcm92aWRlcnMgPSBbXTtcbiAgICBmb3IgKGNvbnN0IGNvbm5lY3RvciBvZiBzb3J0ZWQpIHtcbiAgICAgICAgY29uc3QgcHJvdmlkZXIgPSBhd2FpdCBjb25uZWN0b3IuZ2V0UHJvdmlkZXIoKS5jYXRjaCgoKSA9PiB1bmRlZmluZWQpO1xuICAgICAgICBpZiAoIXByb3ZpZGVyKVxuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIC8vIElmIHdlIGFscmVhZHkgaGF2ZSBhbiBpbnN0YW5jZSBvZiB0aGlzIGNvbm5lY3RvcidzIHByb3ZpZGVyLFxuICAgICAgICAvLyB0aGVuIHdlIGhhdmUgYWxyZWFkeSBjaGVja2VkIGl0IChpZS4gaW5qZWN0ZWQgY29ubmVjdG9ycyBjYW5cbiAgICAgICAgLy8gc2hhcmUgdGhlIHNhbWUgYHdpbmRvdy5ldGhlcmV1bWAgaW5zdGFuY2UsIHNvIHdlIGRvbid0IHdhbnQgdG9cbiAgICAgICAgLy8gY29ubmVjdCB0byBpdCBhZ2FpbikuXG4gICAgICAgIGlmIChwcm92aWRlcnMuc29tZSgoeCkgPT4geCA9PT0gcHJvdmlkZXIpKVxuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIGNvbnN0IGlzQXV0aG9yaXplZCA9IGF3YWl0IGNvbm5lY3Rvci5pc0F1dGhvcml6ZWQoKTtcbiAgICAgICAgaWYgKCFpc0F1dGhvcml6ZWQpXG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGNvbm5lY3RvclxuICAgICAgICAgICAgLmNvbm5lY3QoeyBpc1JlY29ubmVjdGluZzogdHJ1ZSB9KVxuICAgICAgICAgICAgLmNhdGNoKCgpID0+IG51bGwpO1xuICAgICAgICBpZiAoIWRhdGEpXG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgY29ubmVjdG9yLmVtaXR0ZXIub2ZmKCdjb25uZWN0JywgY29uZmlnLl9pbnRlcm5hbC5ldmVudHMuY29ubmVjdCk7XG4gICAgICAgIGNvbm5lY3Rvci5lbWl0dGVyLm9uKCdjaGFuZ2UnLCBjb25maWcuX2ludGVybmFsLmV2ZW50cy5jaGFuZ2UpO1xuICAgICAgICBjb25uZWN0b3IuZW1pdHRlci5vbignZGlzY29ubmVjdCcsIGNvbmZpZy5faW50ZXJuYWwuZXZlbnRzLmRpc2Nvbm5lY3QpO1xuICAgICAgICBjb25maWcuc2V0U3RhdGUoKHgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGNvbm5lY3Rpb25zID0gbmV3IE1hcChjb25uZWN0ZWQgPyB4LmNvbm5lY3Rpb25zIDogbmV3IE1hcCgpKS5zZXQoY29ubmVjdG9yLnVpZCwgeyBhY2NvdW50czogZGF0YS5hY2NvdW50cywgY2hhaW5JZDogZGF0YS5jaGFpbklkLCBjb25uZWN0b3IgfSk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIC4uLngsXG4gICAgICAgICAgICAgICAgY3VycmVudDogY29ubmVjdGVkID8geC5jdXJyZW50IDogY29ubmVjdG9yLnVpZCxcbiAgICAgICAgICAgICAgICBjb25uZWN0aW9ucyxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0pO1xuICAgICAgICBjb25uZWN0aW9ucy5wdXNoKHtcbiAgICAgICAgICAgIGFjY291bnRzOiBkYXRhLmFjY291bnRzLFxuICAgICAgICAgICAgY2hhaW5JZDogZGF0YS5jaGFpbklkLFxuICAgICAgICAgICAgY29ubmVjdG9yLFxuICAgICAgICB9KTtcbiAgICAgICAgcHJvdmlkZXJzLnB1c2gocHJvdmlkZXIpO1xuICAgICAgICBjb25uZWN0ZWQgPSB0cnVlO1xuICAgIH1cbiAgICAvLyBQcmV2ZW50IG92ZXJ3cml0aW5nIGNvbm5lY3RlZCBzdGF0dXMgZnJvbSByYWNlIGNvbmRpdGlvblxuICAgIGlmIChjb25maWcuc3RhdGUuc3RhdHVzID09PSAncmVjb25uZWN0aW5nJyB8fFxuICAgICAgICBjb25maWcuc3RhdGUuc3RhdHVzID09PSAnY29ubmVjdGluZycpIHtcbiAgICAgICAgLy8gSWYgY29ubmVjdGluZyBkaWRuJ3Qgc3VjY2VlZCwgc2V0IHRvIGRpc2Nvbm5lY3RlZFxuICAgICAgICBpZiAoIWNvbm5lY3RlZClcbiAgICAgICAgICAgIGNvbmZpZy5zZXRTdGF0ZSgoeCkgPT4gKHtcbiAgICAgICAgICAgICAgICAuLi54LFxuICAgICAgICAgICAgICAgIGNvbm5lY3Rpb25zOiBuZXcgTWFwKCksXG4gICAgICAgICAgICAgICAgY3VycmVudDogbnVsbCxcbiAgICAgICAgICAgICAgICBzdGF0dXM6ICdkaXNjb25uZWN0ZWQnLFxuICAgICAgICAgICAgfSkpO1xuICAgICAgICBlbHNlXG4gICAgICAgICAgICBjb25maWcuc2V0U3RhdGUoKHgpID0+ICh7IC4uLngsIHN0YXR1czogJ2Nvbm5lY3RlZCcgfSkpO1xuICAgIH1cbiAgICBpc1JlY29ubmVjdGluZyA9IGZhbHNlO1xuICAgIHJldHVybiBjb25uZWN0aW9ucztcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlY29ubmVjdC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/reconnect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   waitForTransactionReceipt: () => (/* binding */ waitForTransactionReceipt)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/waitForTransactionReceipt.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/getTransaction.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/call.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\n\nasync function waitForTransactionReceipt(config, parameters) {\n    const { chainId, timeout = 0, ...rest } = parameters;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.waitForTransactionReceipt, 'waitForTransactionReceipt');\n    const receipt = await action({ ...rest, timeout });\n    if (receipt.status === 'reverted') {\n        const action_getTransaction = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.getTransaction, 'getTransaction');\n        const txn = await action_getTransaction({ hash: receipt.transactionHash });\n        const action_call = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_3__.call, 'call');\n        const code = await action_call({\n            ...txn,\n            data: txn.input,\n            gasPrice: txn.type !== 'eip1559' ? txn.gasPrice : undefined,\n            maxFeePerGas: txn.type === 'eip1559' ? txn.maxFeePerGas : undefined,\n            maxPriorityFeePerGas: txn.type === 'eip1559' ? txn.maxPriorityFeePerGas : undefined,\n        });\n        const reason = code?.data\n            ? (0,viem__WEBPACK_IMPORTED_MODULE_4__.hexToString)(`0x${code.data.substring(138)}`)\n            : 'unknown reason';\n        throw new Error(reason);\n    }\n    return {\n        ...receipt,\n        chainId: client.chain.id,\n    };\n}\n//# sourceMappingURL=waitForTransactionReceipt.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchAccount.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/watchAccount.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchAccount: () => (/* binding */ watchAccount)\n/* harmony export */ });\n/* harmony import */ var _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/deepEqual.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\");\n/* harmony import */ var _getAccount_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getAccount.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getAccount.js\");\n\n\n/** https://wagmi.sh/core/api/actions/watchAccount */\nfunction watchAccount(config, parameters) {\n    const { onChange } = parameters;\n    return config.subscribe(() => (0,_getAccount_js__WEBPACK_IMPORTED_MODULE_0__.getAccount)(config), onChange, {\n        equalityFn(a, b) {\n            const { connector: aConnector, ...aRest } = a;\n            const { connector: bConnector, ...bRest } = b;\n            return ((0,_utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_1__.deepEqual)(aRest, bRest) &&\n                // check connector separately\n                aConnector?.id === bConnector?.id &&\n                aConnector?.uid === bConnector?.uid);\n        },\n    });\n}\n//# sourceMappingURL=watchAccount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93YXRjaEFjY291bnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtEO0FBQ0w7QUFDN0M7QUFDTztBQUNQLFlBQVksV0FBVztBQUN2QixrQ0FBa0MsMERBQVU7QUFDNUM7QUFDQSxvQkFBb0Isa0NBQWtDO0FBQ3RELG9CQUFvQixrQ0FBa0M7QUFDdEQsb0JBQW9CLDhEQUFTO0FBQzdCO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9hY3Rpb25zL3dhdGNoQWNjb3VudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkZWVwRXF1YWwgfSBmcm9tICcuLi91dGlscy9kZWVwRXF1YWwuanMnO1xuaW1wb3J0IHsgZ2V0QWNjb3VudCB9IGZyb20gJy4vZ2V0QWNjb3VudC5qcyc7XG4vKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL3dhdGNoQWNjb3VudCAqL1xuZXhwb3J0IGZ1bmN0aW9uIHdhdGNoQWNjb3VudChjb25maWcsIHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IG9uQ2hhbmdlIH0gPSBwYXJhbWV0ZXJzO1xuICAgIHJldHVybiBjb25maWcuc3Vic2NyaWJlKCgpID0+IGdldEFjY291bnQoY29uZmlnKSwgb25DaGFuZ2UsIHtcbiAgICAgICAgZXF1YWxpdHlGbihhLCBiKSB7XG4gICAgICAgICAgICBjb25zdCB7IGNvbm5lY3RvcjogYUNvbm5lY3RvciwgLi4uYVJlc3QgfSA9IGE7XG4gICAgICAgICAgICBjb25zdCB7IGNvbm5lY3RvcjogYkNvbm5lY3RvciwgLi4uYlJlc3QgfSA9IGI7XG4gICAgICAgICAgICByZXR1cm4gKGRlZXBFcXVhbChhUmVzdCwgYlJlc3QpICYmXG4gICAgICAgICAgICAgICAgLy8gY2hlY2sgY29ubmVjdG9yIHNlcGFyYXRlbHlcbiAgICAgICAgICAgICAgICBhQ29ubmVjdG9yPy5pZCA9PT0gYkNvbm5lY3Rvcj8uaWQgJiZcbiAgICAgICAgICAgICAgICBhQ29ubmVjdG9yPy51aWQgPT09IGJDb25uZWN0b3I/LnVpZCk7XG4gICAgICAgIH0sXG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD13YXRjaEFjY291bnQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchAccount.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchChainId.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/watchChainId.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchChainId: () => (/* binding */ watchChainId)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/watchChainId */\nfunction watchChainId(config, parameters) {\n    const { onChange } = parameters;\n    return config.subscribe((state) => state.chainId, onChange);\n}\n//# sourceMappingURL=watchChainId.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93YXRjaENoYWluSWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUCxZQUFZLFdBQVc7QUFDdkI7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvd2F0Y2hDaGFpbklkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvd2F0Y2hDaGFpbklkICovXG5leHBvcnQgZnVuY3Rpb24gd2F0Y2hDaGFpbklkKGNvbmZpZywgcGFyYW1ldGVycykge1xuICAgIGNvbnN0IHsgb25DaGFuZ2UgfSA9IHBhcmFtZXRlcnM7XG4gICAgcmV0dXJuIGNvbmZpZy5zdWJzY3JpYmUoKHN0YXRlKSA9PiBzdGF0ZS5jaGFpbklkLCBvbkNoYW5nZSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD13YXRjaENoYWluSWQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchChainId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchConnections.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/watchConnections.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchConnections: () => (/* binding */ watchConnections)\n/* harmony export */ });\n/* harmony import */ var _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/deepEqual.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\");\n/* harmony import */ var _getConnections_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getConnections.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnections.js\");\n\n\n/** https://wagmi.sh/core/api/actions/watchConnections */\nfunction watchConnections(config, parameters) {\n    const { onChange } = parameters;\n    return config.subscribe(() => (0,_getConnections_js__WEBPACK_IMPORTED_MODULE_0__.getConnections)(config), onChange, {\n        equalityFn: _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_1__.deepEqual,\n    });\n}\n//# sourceMappingURL=watchConnections.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93YXRjaENvbm5lY3Rpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRDtBQUNJO0FBQ3REO0FBQ087QUFDUCxZQUFZLFdBQVc7QUFDdkIsa0NBQWtDLGtFQUFjO0FBQ2hELG9CQUFvQiwwREFBUztBQUM3QixLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9hY3Rpb25zL3dhdGNoQ29ubmVjdGlvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGVlcEVxdWFsIH0gZnJvbSAnLi4vdXRpbHMvZGVlcEVxdWFsLmpzJztcbmltcG9ydCB7IGdldENvbm5lY3Rpb25zLCB9IGZyb20gJy4vZ2V0Q29ubmVjdGlvbnMuanMnO1xuLyoqIGh0dHBzOi8vd2FnbWkuc2gvY29yZS9hcGkvYWN0aW9ucy93YXRjaENvbm5lY3Rpb25zICovXG5leHBvcnQgZnVuY3Rpb24gd2F0Y2hDb25uZWN0aW9ucyhjb25maWcsIHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IG9uQ2hhbmdlIH0gPSBwYXJhbWV0ZXJzO1xuICAgIHJldHVybiBjb25maWcuc3Vic2NyaWJlKCgpID0+IGdldENvbm5lY3Rpb25zKGNvbmZpZyksIG9uQ2hhbmdlLCB7XG4gICAgICAgIGVxdWFsaXR5Rm46IGRlZXBFcXVhbCxcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdhdGNoQ29ubmVjdGlvbnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchConnections.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchConnectors.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/watchConnectors.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchConnectors: () => (/* binding */ watchConnectors)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/watchConnectors */\nfunction watchConnectors(config, parameters) {\n    const { onChange } = parameters;\n    return config._internal.connectors.subscribe((connectors, prevConnectors) => {\n        onChange(Object.values(connectors), prevConnectors);\n    });\n}\n//# sourceMappingURL=watchConnectors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93YXRjaENvbm5lY3RvcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUCxZQUFZLFdBQVc7QUFDdkI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvd2F0Y2hDb25uZWN0b3JzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvd2F0Y2hDb25uZWN0b3JzICovXG5leHBvcnQgZnVuY3Rpb24gd2F0Y2hDb25uZWN0b3JzKGNvbmZpZywgcGFyYW1ldGVycykge1xuICAgIGNvbnN0IHsgb25DaGFuZ2UgfSA9IHBhcmFtZXRlcnM7XG4gICAgcmV0dXJuIGNvbmZpZy5faW50ZXJuYWwuY29ubmVjdG9ycy5zdWJzY3JpYmUoKGNvbm5lY3RvcnMsIHByZXZDb25uZWN0b3JzKSA9PiB7XG4gICAgICAgIG9uQ2hhbmdlKE9iamVjdC52YWx1ZXMoY29ubmVjdG9ycyksIHByZXZDb25uZWN0b3JzKTtcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdhdGNoQ29ubmVjdG9ycy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchConnectors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/writeContract.js":
/*!********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/writeContract.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   writeContract: () => (/* binding */ writeContract)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/wallet/writeContract.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getConnectorClient.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/writeContract */\nasync function writeContract(config, parameters) {\n    const { account, chainId, connector, ...request } = parameters;\n    let client;\n    if (typeof account === 'object' && account?.type === 'local')\n        client = config.getClient({ chainId });\n    else\n        client = await (0,_getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__.getConnectorClient)(config, {\n            account: account ?? undefined,\n            chainId,\n            connector,\n        });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.writeContract, 'writeContract');\n    const hash = await action({\n        ...request,\n        ...(account ? { account } : {}),\n        chain: chainId ? { id: chainId } : null,\n    });\n    return hash;\n}\n//# sourceMappingURL=writeContract.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93cml0ZUNvbnRyYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0U7QUFDbEI7QUFDWTtBQUM5RDtBQUNPO0FBQ1AsWUFBWSwwQ0FBMEM7QUFDdEQ7QUFDQTtBQUNBLG9DQUFvQyxTQUFTO0FBQzdDO0FBQ0EsdUJBQXVCLDBFQUFrQjtBQUN6QztBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsbUJBQW1CLDhEQUFTLFNBQVMsdURBQWtCO0FBQ3ZEO0FBQ0E7QUFDQSx3QkFBd0IsVUFBVSxJQUFJO0FBQ3RDLDJCQUEyQixjQUFjO0FBQ3pDLEtBQUs7QUFDTDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93cml0ZUNvbnRyYWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHdyaXRlQ29udHJhY3QgYXMgdmllbV93cml0ZUNvbnRyYWN0LCB9IGZyb20gJ3ZpZW0vYWN0aW9ucyc7XG5pbXBvcnQgeyBnZXRBY3Rpb24gfSBmcm9tICcuLi91dGlscy9nZXRBY3Rpb24uanMnO1xuaW1wb3J0IHsgZ2V0Q29ubmVjdG9yQ2xpZW50LCB9IGZyb20gJy4vZ2V0Q29ubmVjdG9yQ2xpZW50LmpzJztcbi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvd3JpdGVDb250cmFjdCAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHdyaXRlQ29udHJhY3QoY29uZmlnLCBwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBhY2NvdW50LCBjaGFpbklkLCBjb25uZWN0b3IsIC4uLnJlcXVlc3QgfSA9IHBhcmFtZXRlcnM7XG4gICAgbGV0IGNsaWVudDtcbiAgICBpZiAodHlwZW9mIGFjY291bnQgPT09ICdvYmplY3QnICYmIGFjY291bnQ/LnR5cGUgPT09ICdsb2NhbCcpXG4gICAgICAgIGNsaWVudCA9IGNvbmZpZy5nZXRDbGllbnQoeyBjaGFpbklkIH0pO1xuICAgIGVsc2VcbiAgICAgICAgY2xpZW50ID0gYXdhaXQgZ2V0Q29ubmVjdG9yQ2xpZW50KGNvbmZpZywge1xuICAgICAgICAgICAgYWNjb3VudDogYWNjb3VudCA/PyB1bmRlZmluZWQsXG4gICAgICAgICAgICBjaGFpbklkLFxuICAgICAgICAgICAgY29ubmVjdG9yLFxuICAgICAgICB9KTtcbiAgICBjb25zdCBhY3Rpb24gPSBnZXRBY3Rpb24oY2xpZW50LCB2aWVtX3dyaXRlQ29udHJhY3QsICd3cml0ZUNvbnRyYWN0Jyk7XG4gICAgY29uc3QgaGFzaCA9IGF3YWl0IGFjdGlvbih7XG4gICAgICAgIC4uLnJlcXVlc3QsXG4gICAgICAgIC4uLihhY2NvdW50ID8geyBhY2NvdW50IH0gOiB7fSksXG4gICAgICAgIGNoYWluOiBjaGFpbklkID8geyBpZDogY2hhaW5JZCB9IDogbnVsbCxcbiAgICB9KTtcbiAgICByZXR1cm4gaGFzaDtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdyaXRlQ29udHJhY3QuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/writeContract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConnector: () => (/* binding */ createConnector)\n/* harmony export */ });\nfunction createConnector(createConnectorFn) {\n    return createConnectorFn;\n}\n//# sourceMappingURL=createConnector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vY29ubmVjdG9ycy9jcmVhdGVDb25uZWN0b3IuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2Nvbm5lY3RvcnMvY3JlYXRlQ29ubmVjdG9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBjcmVhdGVDb25uZWN0b3IoY3JlYXRlQ29ubmVjdG9yRm4pIHtcbiAgICByZXR1cm4gY3JlYXRlQ29ubmVjdG9yRm47XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jcmVhdGVDb25uZWN0b3IuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/connectors/injected.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   injected: () => (/* binding */ injected)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withRetry.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/connector.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _createConnector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createConnector.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n\n\n\n\ninjected.type = 'injected';\nfunction injected(parameters = {}) {\n    const { shimDisconnect = true, unstable_shimAsyncInject } = parameters;\n    function getTarget() {\n        const target = parameters.target;\n        if (typeof target === 'function') {\n            const result = target();\n            if (result)\n                return result;\n        }\n        if (typeof target === 'object')\n            return target;\n        if (typeof target === 'string')\n            return {\n                ...(targetMap[target] ?? {\n                    id: target,\n                    name: `${target[0].toUpperCase()}${target.slice(1)}`,\n                    provider: `is${target[0].toUpperCase()}${target.slice(1)}`,\n                }),\n            };\n        return {\n            id: 'injected',\n            name: 'Injected',\n            provider(window) {\n                return window?.ethereum;\n            },\n        };\n    }\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let disconnect;\n    return (0,_createConnector_js__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        get icon() {\n            return getTarget().icon;\n        },\n        get id() {\n            return getTarget().id;\n        },\n        get name() {\n            return getTarget().name;\n        },\n        /** @deprecated */\n        get supportsSimulation() {\n            return true;\n        },\n        type: injected.type,\n        async setup() {\n            const provider = await this.getProvider();\n            // Only start listening for events if `target` is set, otherwise `injected()` will also receive events\n            if (provider?.on && parameters.target) {\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n                // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n                // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n            }\n        },\n        async connect({ chainId, isReconnecting } = {}) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            let accounts = [];\n            if (isReconnecting)\n                accounts = await this.getAccounts().catch(() => []);\n            else if (shimDisconnect) {\n                // Attempt to show another prompt for selecting account if `shimDisconnect` flag is enabled\n                try {\n                    const permissions = await provider.request({\n                        method: 'wallet_requestPermissions',\n                        params: [{ eth_accounts: {} }],\n                    });\n                    accounts = permissions[0]?.caveats?.[0]?.value?.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                    // `'wallet_requestPermissions'` can return a different order of accounts than `'eth_accounts'`\n                    // switch to `'eth_accounts'` ordering if more than one account is connected\n                    // https://github.com/wevm/wagmi/issues/4140\n                    if (accounts.length > 0) {\n                        const sortedAccounts = await this.getAccounts();\n                        accounts = sortedAccounts;\n                    }\n                }\n                catch (err) {\n                    const error = err;\n                    // Not all injected providers support `wallet_requestPermissions` (e.g. MetaMask iOS).\n                    // Only bubble up error if user rejects request\n                    if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                        throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                    // Or prompt is already open\n                    if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError.code)\n                        throw error;\n                }\n            }\n            try {\n                if (!accounts?.length && !isReconnecting) {\n                    const requestedAccounts = await provider.request({\n                        method: 'eth_requestAccounts',\n                    });\n                    accounts = requestedAccounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                }\n                // Manage EIP-1193 event listeners\n                // https://eips.ethereum.org/EIPS/eip-1193#events\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                // Remove disconnected shim if it exists\n                if (shimDisconnect)\n                    await config.storage?.removeItem(`${this.id}.disconnected`);\n                // Add connected shim if no target exists\n                if (!parameters.target)\n                    await config.storage?.setItem('injected.connected', true);\n                return { accounts, chainId: currentChainId };\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            // Experimental support for MetaMask disconnect\n            // https://github.com/MetaMask/metamask-improvement-proposals/blob/main/MIPs/mip-2.md\n            try {\n                // Adding timeout as not all wallets support this method and can hang\n                // https://github.com/wevm/wagmi/issues/4064\n                await (0,viem__WEBPACK_IMPORTED_MODULE_4__.withTimeout)(() => \n                // TODO: Remove explicit type for viem@3\n                provider.request({\n                    // `'wallet_revokePermissions'` added in `viem@2.10.3`\n                    method: 'wallet_revokePermissions',\n                    params: [{ eth_accounts: {} }],\n                }), { timeout: 100 });\n            }\n            catch { }\n            // Add shim signalling connector is disconnected\n            if (shimDisconnect) {\n                await config.storage?.setItem(`${this.id}.disconnected`, true);\n            }\n            if (!parameters.target)\n                await config.storage?.removeItem('injected.connected');\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const accounts = await provider.request({ method: 'eth_accounts' });\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const hexChainId = await provider.request({ method: 'eth_chainId' });\n            return Number(hexChainId);\n        },\n        async getProvider() {\n            if (typeof window === 'undefined')\n                return undefined;\n            let provider;\n            const target = getTarget();\n            if (typeof target.provider === 'function')\n                provider = target.provider(window);\n            else if (typeof target.provider === 'string')\n                provider = findProvider(window, target.provider);\n            else\n                provider = target.provider;\n            // Some wallets do not conform to EIP-1193 (e.g. Trust Wallet)\n            // https://github.com/wevm/wagmi/issues/3526#issuecomment-**********\n            if (provider && !provider.removeListener) {\n                // Try using `off` handler if it exists, otherwise noop\n                if ('off' in provider && typeof provider.off === 'function')\n                    provider.removeListener =\n                        provider.off;\n                else\n                    provider.removeListener = () => { };\n            }\n            return provider;\n        },\n        async isAuthorized() {\n            try {\n                const isDisconnected = shimDisconnect &&\n                    // If shim exists in storage, connector is disconnected\n                    (await config.storage?.getItem(`${this.id}.disconnected`));\n                if (isDisconnected)\n                    return false;\n                // Don't allow injected connector to connect if no target is set and it hasn't already connected\n                // (e.g. flag in storage is not set). This prevents a targetless injected connector from connecting\n                // automatically whenever there is a targeted connector configured.\n                if (!parameters.target) {\n                    const connected = await config.storage?.getItem('injected.connected');\n                    if (!connected)\n                        return false;\n                }\n                const provider = await this.getProvider();\n                if (!provider) {\n                    if (unstable_shimAsyncInject !== undefined &&\n                        unstable_shimAsyncInject !== false) {\n                        // If no provider is found, check for async injection\n                        // https://github.com/wevm/references/issues/167\n                        // https://github.com/MetaMask/detect-provider\n                        const handleEthereum = async () => {\n                            if (typeof window !== 'undefined')\n                                window.removeEventListener('ethereum#initialized', handleEthereum);\n                            const provider = await this.getProvider();\n                            return !!provider;\n                        };\n                        const timeout = typeof unstable_shimAsyncInject === 'number'\n                            ? unstable_shimAsyncInject\n                            : 1_000;\n                        const res = await Promise.race([\n                            ...(typeof window !== 'undefined'\n                                ? [\n                                    new Promise((resolve) => window.addEventListener('ethereum#initialized', () => resolve(handleEthereum()), { once: true })),\n                                ]\n                                : []),\n                            new Promise((resolve) => setTimeout(() => resolve(handleEthereum()), timeout)),\n                        ]);\n                        if (res)\n                            return true;\n                    }\n                    throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n                }\n                // Use retry strategy as some injected wallets (e.g. MetaMask) fail to\n                // immediately resolve JSON-RPC requests on page load.\n                const accounts = await (0,viem__WEBPACK_IMPORTED_MODULE_5__.withRetry)(() => this.getAccounts());\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError());\n            const promise = new Promise((resolve) => {\n                const listener = ((data) => {\n                    if ('chainId' in data && data.chainId === chainId) {\n                        config.emitter.off('change', listener);\n                        resolve();\n                    }\n                });\n                config.emitter.on('change', listener);\n            });\n            try {\n                await Promise.all([\n                    provider\n                        .request({\n                        method: 'wallet_switchEthereumChain',\n                        params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_7__.numberToHex)(chainId) }],\n                    })\n                        // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n                        // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n                        // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n                        // this callback or an externally emitted `'chainChanged'` event.\n                        // https://github.com/MetaMask/metamask-extension/issues/24247\n                        .then(async () => {\n                        const currentChainId = await this.getChainId();\n                        if (currentChainId === chainId)\n                            config.emitter.emit('change', { chainId });\n                    }),\n                    promise,\n                ]);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                // Indicates chain is not added to provider\n                if (error.code === 4902 ||\n                    // Unwrapping for MetaMask Mobile\n                    // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n                    error\n                        ?.data?.originalError?.code === 4902) {\n                    try {\n                        const { default: blockExplorer, ...blockExplorers } = chain.blockExplorers ?? {};\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else if (blockExplorer)\n                            blockExplorerUrls = [\n                                blockExplorer.url,\n                                ...Object.values(blockExplorers).map((x) => x.url),\n                            ];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_7__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await Promise.all([\n                            provider\n                                .request({\n                                method: 'wallet_addEthereumChain',\n                                params: [addEthereumChain],\n                            })\n                                .then(async () => {\n                                const currentChainId = await this.getChainId();\n                                if (currentChainId === chainId)\n                                    config.emitter.emit('change', { chainId });\n                                else\n                                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(new Error('User rejected switch after adding network.'));\n                            }),\n                            promise,\n                        ]);\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                    }\n                }\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(error);\n            }\n        },\n        async onAccountsChanged(accounts) {\n            // Disconnect if there are no accounts\n            if (accounts.length === 0)\n                this.onDisconnect();\n            // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n            else if (config.emitter.listenerCount('connect')) {\n                const chainId = (await this.getChainId()).toString();\n                this.onConnect({ chainId });\n                // Remove disconnected shim if it exists\n                if (shimDisconnect)\n                    await config.storage?.removeItem(`${this.id}.disconnected`);\n            }\n            // Regular change event\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const accounts = await this.getAccounts();\n            if (accounts.length === 0)\n                return;\n            const chainId = Number(connectInfo.chainId);\n            config.emitter.emit('connect', { accounts, chainId });\n            // Manage EIP-1193 event listeners\n            const provider = await this.getProvider();\n            if (provider) {\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n            }\n        },\n        async onDisconnect(error) {\n            const provider = await this.getProvider();\n            // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n            // https://github.com/MetaMask/providers/pull/120\n            if (error && error.code === 1013) {\n                if (provider && !!(await this.getAccounts()).length)\n                    return;\n            }\n            // No need to remove `${this.id}.disconnected` from storage because `onDisconnect` is typically\n            // only called when the wallet is disconnected through the wallet's interface, meaning the wallet\n            // actually disconnected and we don't need to simulate it.\n            config.emitter.emit('disconnect');\n            // Manage EIP-1193 event listeners\n            if (provider) {\n                if (chainChanged) {\n                    provider.removeListener('chainChanged', chainChanged);\n                    chainChanged = undefined;\n                }\n                if (disconnect) {\n                    provider.removeListener('disconnect', disconnect);\n                    disconnect = undefined;\n                }\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n            }\n        },\n    }));\n}\nconst targetMap = {\n    coinbaseWallet: {\n        id: 'coinbaseWallet',\n        name: 'Coinbase Wallet',\n        provider(window) {\n            if (window?.coinbaseWalletExtension)\n                return window.coinbaseWalletExtension;\n            return findProvider(window, 'isCoinbaseWallet');\n        },\n    },\n    metaMask: {\n        id: 'metaMask',\n        name: 'MetaMask',\n        provider(window) {\n            return findProvider(window, (provider) => {\n                if (!provider.isMetaMask)\n                    return false;\n                // Brave tries to make itself look like MetaMask\n                // Could also try RPC `web3_clientVersion` if following is unreliable\n                if (provider.isBraveWallet && !provider._events && !provider._state)\n                    return false;\n                // Other wallets that try to look like MetaMask\n                const flags = [\n                    'isApexWallet',\n                    'isAvalanche',\n                    'isBitKeep',\n                    'isBlockWallet',\n                    'isKuCoinWallet',\n                    'isMathWallet',\n                    'isOkxWallet',\n                    'isOKExWallet',\n                    'isOneInchIOSWallet',\n                    'isOneInchAndroidWallet',\n                    'isOpera',\n                    'isPhantom',\n                    'isPortal',\n                    'isRabby',\n                    'isTokenPocket',\n                    'isTokenary',\n                    'isUniswapWallet',\n                    'isZerion',\n                ];\n                for (const flag of flags)\n                    if (provider[flag])\n                        return false;\n                return true;\n            });\n        },\n    },\n    phantom: {\n        id: 'phantom',\n        name: 'Phantom',\n        provider(window) {\n            if (window?.phantom?.ethereum)\n                return window.phantom?.ethereum;\n            return findProvider(window, 'isPhantom');\n        },\n    },\n};\nfunction findProvider(window, select) {\n    function isProvider(provider) {\n        if (typeof select === 'function')\n            return select(provider);\n        if (typeof select === 'string')\n            return provider[select];\n        return true;\n    }\n    const ethereum = window.ethereum;\n    if (ethereum?.providers)\n        return ethereum.providers.find((provider) => isProvider(provider));\n    if (ethereum && isProvider(ethereum))\n        return ethereum;\n    return undefined;\n}\n//# sourceMappingURL=injected.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/createConfig.js":
/*!***********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/createConfig.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConfig: () => (/* binding */ createConfig)\n/* harmony export */ });\n/* harmony import */ var mipd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mipd */ \"(ssr)/./node_modules/mipd/dist/esm/store.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/createClient.js\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/@wagmi/core/node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/vanilla */ \"(ssr)/./node_modules/@wagmi/core/node_modules/zustand/esm/vanilla.mjs\");\n/* harmony import */ var _connectors_injected_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./connectors/injected.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js\");\n/* harmony import */ var _createEmitter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./createEmitter.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/createEmitter.js\");\n/* harmony import */ var _createStorage_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createStorage.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/createStorage.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _utils_uid_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/uid.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/uid.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./version.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/version.js\");\n\n\n\n\n\n\n\n\n\n\nfunction createConfig(parameters) {\n    const { multiInjectedProviderDiscovery = true, storage = (0,_createStorage_js__WEBPACK_IMPORTED_MODULE_0__.createStorage)({\n        storage: (0,_createStorage_js__WEBPACK_IMPORTED_MODULE_0__.getDefaultStorage)(),\n    }), syncConnectedChain = true, ssr = false, ...rest } = parameters;\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Set up connectors, clients, etc.\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    const mipd = typeof window !== 'undefined' && multiInjectedProviderDiscovery\n        ? (0,mipd__WEBPACK_IMPORTED_MODULE_1__.createStore)()\n        : undefined;\n    const chains = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)(() => rest.chains);\n    const connectors = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)(() => {\n        const collection = [];\n        const rdnsSet = new Set();\n        for (const connectorFns of rest.connectors ?? []) {\n            const connector = setup(connectorFns);\n            collection.push(connector);\n            if (!ssr && connector.rdns) {\n                const rdnsValues = typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns;\n                for (const rdns of rdnsValues) {\n                    rdnsSet.add(rdns);\n                }\n            }\n        }\n        if (!ssr && mipd) {\n            const providers = mipd.getProviders();\n            for (const provider of providers) {\n                if (rdnsSet.has(provider.info.rdns))\n                    continue;\n                collection.push(setup(providerDetailToConnector(provider)));\n            }\n        }\n        return collection;\n    });\n    function setup(connectorFn) {\n        // Set up emitter with uid and add to connector so they are \"linked\" together.\n        const emitter = (0,_createEmitter_js__WEBPACK_IMPORTED_MODULE_3__.createEmitter)((0,_utils_uid_js__WEBPACK_IMPORTED_MODULE_4__.uid)());\n        const connector = {\n            ...connectorFn({\n                emitter,\n                chains: chains.getState(),\n                storage,\n                transports: rest.transports,\n            }),\n            emitter,\n            uid: emitter.uid,\n        };\n        // Start listening for `connect` events on connector setup\n        // This allows connectors to \"connect\" themselves without user interaction (e.g. MetaMask's \"Manually connect to current site\")\n        emitter.on('connect', connect);\n        connector.setup?.();\n        return connector;\n    }\n    function providerDetailToConnector(providerDetail) {\n        const { info } = providerDetail;\n        const provider = providerDetail.provider;\n        return (0,_connectors_injected_js__WEBPACK_IMPORTED_MODULE_5__.injected)({ target: { ...info, id: info.rdns, provider } });\n    }\n    const clients = new Map();\n    function getClient(config = {}) {\n        const chainId = config.chainId ?? store.getState().chainId;\n        const chain = chains.getState().find((x) => x.id === chainId);\n        // chainId specified and not configured\n        if (config.chainId && !chain)\n            throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError();\n        {\n            const client = clients.get(store.getState().chainId);\n            if (client && !chain)\n                return client;\n            if (!chain)\n                throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError();\n        }\n        // If a memoized client exists for a chain id, use that.\n        {\n            const client = clients.get(chainId);\n            if (client)\n                return client;\n        }\n        let client;\n        if (rest.client)\n            client = rest.client({ chain });\n        else {\n            const chainId = chain.id;\n            const chainIds = chains.getState().map((x) => x.id);\n            // Grab all properties off `rest` and resolve for use in `createClient`\n            const properties = {};\n            const entries = Object.entries(rest);\n            for (const [key, value] of entries) {\n                if (key === 'chains' ||\n                    key === 'client' ||\n                    key === 'connectors' ||\n                    key === 'transports')\n                    continue;\n                if (typeof value === 'object') {\n                    // check if value is chainId-specific since some values can be objects\n                    // e.g. { batch: { multicall: { batchSize: 1024 } } }\n                    if (chainId in value)\n                        properties[key] = value[chainId];\n                    else {\n                        // check if value is chainId-specific, but does not have value for current chainId\n                        const hasChainSpecificValue = chainIds.some((x) => x in value);\n                        if (hasChainSpecificValue)\n                            continue;\n                        properties[key] = value;\n                    }\n                }\n                else\n                    properties[key] = value;\n            }\n            client = (0,viem__WEBPACK_IMPORTED_MODULE_7__.createClient)({\n                ...properties,\n                chain,\n                batch: properties.batch ?? { multicall: true },\n                transport: (parameters) => rest.transports[chainId]({ ...parameters, connectors }),\n            });\n        }\n        clients.set(chainId, client);\n        return client;\n    }\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Create store\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    function getInitialState() {\n        return {\n            chainId: chains.getState()[0].id,\n            connections: new Map(),\n            current: null,\n            status: 'disconnected',\n        };\n    }\n    let currentVersion;\n    const prefix = '0.0.0-canary-';\n    if (_version_js__WEBPACK_IMPORTED_MODULE_8__.version.startsWith(prefix))\n        currentVersion = Number.parseInt(_version_js__WEBPACK_IMPORTED_MODULE_8__.version.replace(prefix, ''));\n    // use package major version to version store\n    else\n        currentVersion = Number.parseInt(_version_js__WEBPACK_IMPORTED_MODULE_8__.version.split('.')[0] ?? '0');\n    const store = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_9__.subscribeWithSelector)(\n    // only use persist middleware if storage exists\n    storage\n        ? (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_9__.persist)(getInitialState, {\n            migrate(persistedState, version) {\n                if (version === currentVersion)\n                    return persistedState;\n                const initialState = getInitialState();\n                const chainId = validatePersistedChainId(persistedState, initialState.chainId);\n                return { ...initialState, chainId };\n            },\n            name: 'store',\n            partialize(state) {\n                // Only persist \"critical\" store properties to preserve storage size.\n                return {\n                    connections: {\n                        __type: 'Map',\n                        value: Array.from(state.connections.entries()).map(([key, connection]) => {\n                            const { id, name, type, uid } = connection.connector;\n                            const connector = { id, name, type, uid };\n                            return [key, { ...connection, connector }];\n                        }),\n                    },\n                    chainId: state.chainId,\n                    current: state.current,\n                };\n            },\n            merge(persistedState, currentState) {\n                // `status` should not be persisted as it messes with reconnection\n                if (typeof persistedState === 'object' &&\n                    persistedState &&\n                    'status' in persistedState)\n                    delete persistedState.status;\n                // Make sure persisted `chainId` is valid\n                const chainId = validatePersistedChainId(persistedState, currentState.chainId);\n                return {\n                    ...currentState,\n                    ...persistedState,\n                    chainId,\n                };\n            },\n            skipHydration: ssr,\n            storage: storage,\n            version: currentVersion,\n        })\n        : getInitialState));\n    store.setState(getInitialState());\n    function validatePersistedChainId(persistedState, defaultChainId) {\n        return persistedState &&\n            typeof persistedState === 'object' &&\n            'chainId' in persistedState &&\n            typeof persistedState.chainId === 'number' &&\n            chains.getState().some((x) => x.id === persistedState.chainId)\n            ? persistedState.chainId\n            : defaultChainId;\n    }\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Subscribe to changes\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Update default chain when connector chain changes\n    if (syncConnectedChain)\n        store.subscribe(({ connections, current }) => current ? connections.get(current)?.chainId : undefined, (chainId) => {\n            // If chain is not configured, then don't switch over to it.\n            const isChainConfigured = chains\n                .getState()\n                .some((x) => x.id === chainId);\n            if (!isChainConfigured)\n                return;\n            return store.setState((x) => ({\n                ...x,\n                chainId: chainId ?? x.chainId,\n            }));\n        });\n    // EIP-6963 subscribe for new wallet providers\n    mipd?.subscribe((providerDetails) => {\n        const connectorIdSet = new Set();\n        const connectorRdnsSet = new Set();\n        for (const connector of connectors.getState()) {\n            connectorIdSet.add(connector.id);\n            if (connector.rdns) {\n                const rdnsValues = typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns;\n                for (const rdns of rdnsValues) {\n                    connectorRdnsSet.add(rdns);\n                }\n            }\n        }\n        const newConnectors = [];\n        for (const providerDetail of providerDetails) {\n            if (connectorRdnsSet.has(providerDetail.info.rdns))\n                continue;\n            const connector = setup(providerDetailToConnector(providerDetail));\n            if (connectorIdSet.has(connector.id))\n                continue;\n            newConnectors.push(connector);\n        }\n        if (storage && !store.persist.hasHydrated())\n            return;\n        connectors.setState((x) => [...x, ...newConnectors], true);\n    });\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Emitter listeners\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    function change(data) {\n        store.setState((x) => {\n            const connection = x.connections.get(data.uid);\n            if (!connection)\n                return x;\n            return {\n                ...x,\n                connections: new Map(x.connections).set(data.uid, {\n                    accounts: data.accounts ??\n                        connection.accounts,\n                    chainId: data.chainId ?? connection.chainId,\n                    connector: connection.connector,\n                }),\n            };\n        });\n    }\n    function connect(data) {\n        // Disable handling if reconnecting/connecting\n        if (store.getState().status === 'connecting' ||\n            store.getState().status === 'reconnecting')\n            return;\n        store.setState((x) => {\n            const connector = connectors.getState().find((x) => x.uid === data.uid);\n            if (!connector)\n                return x;\n            if (connector.emitter.listenerCount('connect'))\n                connector.emitter.off('connect', change);\n            if (!connector.emitter.listenerCount('change'))\n                connector.emitter.on('change', change);\n            if (!connector.emitter.listenerCount('disconnect'))\n                connector.emitter.on('disconnect', disconnect);\n            return {\n                ...x,\n                connections: new Map(x.connections).set(data.uid, {\n                    accounts: data.accounts,\n                    chainId: data.chainId,\n                    connector: connector,\n                }),\n                current: data.uid,\n                status: 'connected',\n            };\n        });\n    }\n    function disconnect(data) {\n        store.setState((x) => {\n            const connection = x.connections.get(data.uid);\n            if (connection) {\n                const connector = connection.connector;\n                if (connector.emitter.listenerCount('change'))\n                    connection.connector.emitter.off('change', change);\n                if (connector.emitter.listenerCount('disconnect'))\n                    connection.connector.emitter.off('disconnect', disconnect);\n                if (!connector.emitter.listenerCount('connect'))\n                    connection.connector.emitter.on('connect', connect);\n            }\n            x.connections.delete(data.uid);\n            if (x.connections.size === 0)\n                return {\n                    ...x,\n                    connections: new Map(),\n                    current: null,\n                    status: 'disconnected',\n                };\n            const nextConnection = x.connections.values().next().value;\n            return {\n                ...x,\n                connections: new Map(x.connections),\n                current: nextConnection.connector.uid,\n            };\n        });\n    }\n    return {\n        get chains() {\n            return chains.getState();\n        },\n        get connectors() {\n            return connectors.getState();\n        },\n        storage,\n        getClient,\n        get state() {\n            return store.getState();\n        },\n        setState(value) {\n            let newState;\n            if (typeof value === 'function')\n                newState = value(store.getState());\n            else\n                newState = value;\n            // Reset state if it got set to something not matching the base state\n            const initialState = getInitialState();\n            if (typeof newState !== 'object')\n                newState = initialState;\n            const isCorrupt = Object.keys(initialState).some((x) => !(x in newState));\n            if (isCorrupt)\n                newState = initialState;\n            store.setState(newState, true);\n        },\n        subscribe(selector, listener, options) {\n            return store.subscribe(selector, listener, options\n                ? {\n                    ...options,\n                    fireImmediately: options.emitImmediately,\n                    // Workaround cast since Zustand does not support `'exactOptionalPropertyTypes'`\n                }\n                : undefined);\n        },\n        _internal: {\n            mipd,\n            store,\n            ssr: Boolean(ssr),\n            syncConnectedChain,\n            transports: rest.transports,\n            chains: {\n                setState(value) {\n                    const nextChains = (typeof value === 'function' ? value(chains.getState()) : value);\n                    if (nextChains.length === 0)\n                        return;\n                    return chains.setState(nextChains, true);\n                },\n                subscribe(listener) {\n                    return chains.subscribe(listener);\n                },\n            },\n            connectors: {\n                providerDetailToConnector,\n                setup: setup,\n                setState(value) {\n                    return connectors.setState(typeof value === 'function' ? value(connectors.getState()) : value, true);\n                },\n                subscribe(listener) {\n                    return connectors.subscribe(listener);\n                },\n            },\n            events: { change, connect, disconnect },\n        },\n    };\n}\n//# sourceMappingURL=createConfig.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/createConfig.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/createEmitter.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/createEmitter.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Emitter: () => (/* binding */ Emitter),\n/* harmony export */   createEmitter: () => (/* binding */ createEmitter)\n/* harmony export */ });\n/* harmony import */ var eventemitter3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! eventemitter3 */ \"(ssr)/./node_modules/eventemitter3/index.mjs\");\n\nclass Emitter {\n    constructor(uid) {\n        Object.defineProperty(this, \"uid\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: uid\n        });\n        Object.defineProperty(this, \"_emitter\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: new eventemitter3__WEBPACK_IMPORTED_MODULE_0__.EventEmitter()\n        });\n    }\n    on(eventName, fn) {\n        this._emitter.on(eventName, fn);\n    }\n    once(eventName, fn) {\n        this._emitter.once(eventName, fn);\n    }\n    off(eventName, fn) {\n        this._emitter.off(eventName, fn);\n    }\n    emit(eventName, ...params) {\n        const data = params[0];\n        this._emitter.emit(eventName, { uid: this.uid, ...data });\n    }\n    listenerCount(eventName) {\n        return this._emitter.listenerCount(eventName);\n    }\n}\nfunction createEmitter(uid) {\n    return new Emitter(uid);\n}\n//# sourceMappingURL=createEmitter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/createEmitter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/createStorage.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/createStorage.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStorage: () => (/* binding */ createStorage),\n/* harmony export */   getDefaultStorage: () => (/* binding */ getDefaultStorage),\n/* harmony export */   noopStorage: () => (/* binding */ noopStorage)\n/* harmony export */ });\n/* harmony import */ var _utils_deserialize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/deserialize.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deserialize.js\");\n/* harmony import */ var _utils_serialize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/serialize.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/serialize.js\");\n\n\nfunction createStorage(parameters) {\n    const { deserialize = _utils_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize, key: prefix = 'wagmi', serialize = _utils_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize, storage = noopStorage, } = parameters;\n    function unwrap(value) {\n        if (value instanceof Promise)\n            return value.then((x) => x).catch(() => null);\n        return value;\n    }\n    return {\n        ...storage,\n        key: prefix,\n        async getItem(key, defaultValue) {\n            const value = storage.getItem(`${prefix}.${key}`);\n            const unwrapped = await unwrap(value);\n            if (unwrapped)\n                return deserialize(unwrapped) ?? null;\n            return (defaultValue ?? null);\n        },\n        async setItem(key, value) {\n            const storageKey = `${prefix}.${key}`;\n            if (value === null)\n                await unwrap(storage.removeItem(storageKey));\n            else\n                await unwrap(storage.setItem(storageKey, serialize(value)));\n        },\n        async removeItem(key) {\n            await unwrap(storage.removeItem(`${prefix}.${key}`));\n        },\n    };\n}\nconst noopStorage = {\n    getItem: () => null,\n    setItem: () => { },\n    removeItem: () => { },\n};\nfunction getDefaultStorage() {\n    const storage = (() => {\n        if (typeof window !== 'undefined' && window.localStorage)\n            return window.localStorage;\n        return noopStorage;\n    })();\n    return {\n        getItem(key) {\n            return storage.getItem(key);\n        },\n        removeItem(key) {\n            storage.removeItem(key);\n        },\n        setItem(key, value) {\n            try {\n                storage.setItem(key, value);\n                // silence errors by default (QuotaExceededError, SecurityError, etc.)\n            }\n            catch { }\n        },\n    };\n}\n//# sourceMappingURL=createStorage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/createStorage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js":
/*!**********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/errors/base.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseError: () => (/* binding */ BaseError)\n/* harmony export */ });\n/* harmony import */ var _utils_getVersion_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getVersion.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getVersion.js\");\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _BaseError_instances, _BaseError_walk;\n\nclass BaseError extends Error {\n    get docsBaseUrl() {\n        return 'https://wagmi.sh/core';\n    }\n    get version() {\n        return (0,_utils_getVersion_js__WEBPACK_IMPORTED_MODULE_0__.getVersion)();\n    }\n    constructor(shortMessage, options = {}) {\n        super();\n        _BaseError_instances.add(this);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"metaMessages\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'WagmiCoreError'\n        });\n        const details = options.cause instanceof BaseError\n            ? options.cause.details\n            : options.cause?.message\n                ? options.cause.message\n                : options.details;\n        const docsPath = options.cause instanceof BaseError\n            ? options.cause.docsPath || options.docsPath\n            : options.docsPath;\n        this.message = [\n            shortMessage || 'An error occurred.',\n            '',\n            ...(options.metaMessages ? [...options.metaMessages, ''] : []),\n            ...(docsPath\n                ? [\n                    `Docs: ${this.docsBaseUrl}${docsPath}.html${options.docsSlug ? `#${options.docsSlug}` : ''}`,\n                ]\n                : []),\n            ...(details ? [`Details: ${details}`] : []),\n            `Version: ${this.version}`,\n        ].join('\\n');\n        if (options.cause)\n            this.cause = options.cause;\n        this.details = details;\n        this.docsPath = docsPath;\n        this.metaMessages = options.metaMessages;\n        this.shortMessage = shortMessage;\n    }\n    walk(fn) {\n        return __classPrivateFieldGet(this, _BaseError_instances, \"m\", _BaseError_walk).call(this, this, fn);\n    }\n}\n_BaseError_instances = new WeakSet(), _BaseError_walk = function _BaseError_walk(err, fn) {\n    if (fn?.(err))\n        return err;\n    if (err.cause)\n        return __classPrivateFieldGet(this, _BaseError_instances, \"m\", _BaseError_walk).call(this, err.cause, fn);\n    return err;\n};\n//# sourceMappingURL=base.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/errors/config.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChainNotConfiguredError: () => (/* binding */ ChainNotConfiguredError),\n/* harmony export */   ConnectorAccountNotFoundError: () => (/* binding */ ConnectorAccountNotFoundError),\n/* harmony export */   ConnectorAlreadyConnectedError: () => (/* binding */ ConnectorAlreadyConnectedError),\n/* harmony export */   ConnectorChainMismatchError: () => (/* binding */ ConnectorChainMismatchError),\n/* harmony export */   ConnectorNotConnectedError: () => (/* binding */ ConnectorNotConnectedError),\n/* harmony export */   ConnectorNotFoundError: () => (/* binding */ ConnectorNotFoundError),\n/* harmony export */   ConnectorUnavailableReconnectingError: () => (/* binding */ ConnectorUnavailableReconnectingError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js\");\n\nclass ChainNotConfiguredError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Chain not configured.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ChainNotConfiguredError'\n        });\n    }\n}\nclass ConnectorAlreadyConnectedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector already connected.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorAlreadyConnectedError'\n        });\n    }\n}\nclass ConnectorNotConnectedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector not connected.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorNotConnectedError'\n        });\n    }\n}\nclass ConnectorNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector not found.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorNotFoundError'\n        });\n    }\n}\nclass ConnectorAccountNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ address, connector, }) {\n        super(`Account \"${address}\" not found for connector \"${connector.name}\".`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorAccountNotFoundError'\n        });\n    }\n}\nclass ConnectorChainMismatchError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connectionChainId, connectorChainId, }) {\n        super(`The current chain of the connector (id: ${connectorChainId}) does not match the connection's chain (id: ${connectionChainId}).`, {\n            metaMessages: [\n                `Current Chain ID:  ${connectorChainId}`,\n                `Expected Chain ID: ${connectionChainId}`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorChainMismatchError'\n        });\n    }\n}\nclass ConnectorUnavailableReconnectingError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connector }) {\n        super(`Connector \"${connector.name}\" unavailable while reconnecting.`, {\n            details: [\n                'During the reconnection step, the only connector methods guaranteed to be available are: `id`, `name`, `type`, `uid`.',\n                'All other methods are not guaranteed to be available until reconnection completes and connectors are fully restored.',\n                'This error commonly occurs for connectors that asynchronously inject after reconnection has already started.',\n            ].join(' '),\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorUnavailableReconnectingError'\n        });\n    }\n}\n//# sourceMappingURL=config.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js":
/*!***************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/errors/connector.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProviderNotFoundError: () => (/* binding */ ProviderNotFoundError),\n/* harmony export */   SwitchChainNotSupportedError: () => (/* binding */ SwitchChainNotSupportedError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js\");\n\nclass ProviderNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Provider not found.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ProviderNotFoundError'\n        });\n    }\n}\nclass SwitchChainNotSupportedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connector }) {\n        super(`\"${connector.name}\" does not support programmatic chain switching.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'SwitchChainNotSupportedError'\n        });\n    }\n}\n//# sourceMappingURL=connector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vZXJyb3JzL2Nvbm5lY3Rvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBc0M7QUFDL0Isb0NBQW9DLCtDQUFTO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDTywyQ0FBMkMsK0NBQVM7QUFDM0Qsa0JBQWtCLFdBQVc7QUFDN0Isa0JBQWtCLGVBQWU7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vZXJyb3JzL2Nvbm5lY3Rvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCYXNlRXJyb3IgfSBmcm9tICcuL2Jhc2UuanMnO1xuZXhwb3J0IGNsYXNzIFByb3ZpZGVyTm90Rm91bmRFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKCdQcm92aWRlciBub3QgZm91bmQuJyk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm5hbWVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6ICdQcm92aWRlck5vdEZvdW5kRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbmV4cG9ydCBjbGFzcyBTd2l0Y2hDaGFpbk5vdFN1cHBvcnRlZEVycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcih7IGNvbm5lY3RvciB9KSB7XG4gICAgICAgIHN1cGVyKGBcIiR7Y29ubmVjdG9yLm5hbWV9XCIgZG9lcyBub3Qgc3VwcG9ydCBwcm9ncmFtbWF0aWMgY2hhaW4gc3dpdGNoaW5nLmApO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJuYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiAnU3dpdGNoQ2hhaW5Ob3RTdXBwb3J0ZWRFcnJvcidcbiAgICAgICAgfSk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29ubmVjdG9yLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/hydrate.js":
/*!******************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/hydrate.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hydrate: () => (/* binding */ hydrate)\n/* harmony export */ });\n/* harmony import */ var _actions_reconnect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions/reconnect.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/reconnect.js\");\n\nfunction hydrate(config, parameters) {\n    const { initialState, reconnectOnMount } = parameters;\n    if (initialState && !config._internal.store.persist.hasHydrated())\n        config.setState({\n            ...initialState,\n            chainId: config.chains.some((x) => x.id === initialState.chainId)\n                ? initialState.chainId\n                : config.chains[0].id,\n            connections: reconnectOnMount ? initialState.connections : new Map(),\n            status: reconnectOnMount ? 'reconnecting' : 'disconnected',\n        });\n    return {\n        async onMount() {\n            if (config._internal.ssr) {\n                await config._internal.store.persist.rehydrate();\n                if (config._internal.mipd) {\n                    config._internal.connectors.setState((connectors) => {\n                        const rdnsSet = new Set();\n                        for (const connector of connectors ?? []) {\n                            if (connector.rdns) {\n                                const rdnsValues = Array.isArray(connector.rdns)\n                                    ? connector.rdns\n                                    : [connector.rdns];\n                                for (const rdns of rdnsValues) {\n                                    rdnsSet.add(rdns);\n                                }\n                            }\n                        }\n                        const mipdConnectors = [];\n                        const providers = config._internal.mipd?.getProviders() ?? [];\n                        for (const provider of providers) {\n                            if (rdnsSet.has(provider.info.rdns))\n                                continue;\n                            const connectorFn = config._internal.connectors.providerDetailToConnector(provider);\n                            const connector = config._internal.connectors.setup(connectorFn);\n                            mipdConnectors.push(connector);\n                        }\n                        return [...connectors, ...mipdConnectors];\n                    });\n                }\n            }\n            if (reconnectOnMount)\n                (0,_actions_reconnect_js__WEBPACK_IMPORTED_MODULE_0__.reconnect)(config);\n            else if (config.storage)\n                // Reset connections that may have been hydrated from storage.\n                config.setState((x) => ({\n                    ...x,\n                    connections: new Map(),\n                }));\n        },\n    };\n}\n//# sourceMappingURL=hydrate.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/hydrate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/query/connect.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/query/connect.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   connectMutationOptions: () => (/* binding */ connectMutationOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_connect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/connect.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/connect.js\");\n\nfunction connectMutationOptions(config) {\n    return {\n        mutationFn(variables) {\n            return (0,_actions_connect_js__WEBPACK_IMPORTED_MODULE_0__.connect)(config, variables);\n        },\n        mutationKey: ['connect'],\n    };\n}\n//# sourceMappingURL=connect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vcXVlcnkvY29ubmVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpRDtBQUMxQztBQUNQO0FBQ0E7QUFDQSxtQkFBbUIsNERBQU87QUFDMUIsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3F1ZXJ5L2Nvbm5lY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29ubmVjdCwgfSBmcm9tICcuLi9hY3Rpb25zL2Nvbm5lY3QuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGNvbm5lY3RNdXRhdGlvbk9wdGlvbnMoY29uZmlnKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgbXV0YXRpb25Gbih2YXJpYWJsZXMpIHtcbiAgICAgICAgICAgIHJldHVybiBjb25uZWN0KGNvbmZpZywgdmFyaWFibGVzKTtcbiAgICAgICAgfSxcbiAgICAgICAgbXV0YXRpb25LZXk6IFsnY29ubmVjdCddLFxuICAgIH07XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25uZWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/query/connect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/query/disconnect.js":
/*!***************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/query/disconnect.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   disconnectMutationOptions: () => (/* binding */ disconnectMutationOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_disconnect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/disconnect.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/disconnect.js\");\n\nfunction disconnectMutationOptions(config) {\n    return {\n        mutationFn(variables) {\n            return (0,_actions_disconnect_js__WEBPACK_IMPORTED_MODULE_0__.disconnect)(config, variables);\n        },\n        mutationKey: ['disconnect'],\n    };\n}\n//# sourceMappingURL=disconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vcXVlcnkvZGlzY29ubmVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1RDtBQUNoRDtBQUNQO0FBQ0E7QUFDQSxtQkFBbUIsa0VBQVU7QUFDN0IsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3F1ZXJ5L2Rpc2Nvbm5lY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGlzY29ubmVjdCwgfSBmcm9tICcuLi9hY3Rpb25zL2Rpc2Nvbm5lY3QuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGRpc2Nvbm5lY3RNdXRhdGlvbk9wdGlvbnMoY29uZmlnKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgbXV0YXRpb25Gbih2YXJpYWJsZXMpIHtcbiAgICAgICAgICAgIHJldHVybiBkaXNjb25uZWN0KGNvbmZpZywgdmFyaWFibGVzKTtcbiAgICAgICAgfSxcbiAgICAgICAgbXV0YXRpb25LZXk6IFsnZGlzY29ubmVjdCddLFxuICAgIH07XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kaXNjb25uZWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/query/disconnect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/query/readContract.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/query/readContract.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   readContractQueryKey: () => (/* binding */ readContractQueryKey),\n/* harmony export */   readContractQueryOptions: () => (/* binding */ readContractQueryOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_readContract_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/readContract.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContract.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/query/utils.js\");\n\n\nfunction readContractQueryOptions(config, options = {}) {\n    return {\n        // TODO: Support `signal` once Viem actions allow passthrough\n        // https://tkdodo.eu/blog/why-you-want-react-query#bonus-cancellation\n        async queryFn({ queryKey }) {\n            const abi = options.abi;\n            if (!abi)\n                throw new Error('abi is required');\n            const { functionName, scopeKey: _, ...parameters } = queryKey[1];\n            const addressOrCodeParams = (() => {\n                const params = queryKey[1];\n                if (params.address)\n                    return { address: params.address };\n                if (params.code)\n                    return { code: params.code };\n                throw new Error('address or code is required');\n            })();\n            if (!functionName)\n                throw new Error('functionName is required');\n            return (0,_actions_readContract_js__WEBPACK_IMPORTED_MODULE_0__.readContract)(config, {\n                abi,\n                functionName,\n                args: parameters.args,\n                ...addressOrCodeParams,\n                ...parameters,\n            });\n        },\n        queryKey: readContractQueryKey(options),\n    };\n}\nfunction readContractQueryKey(options = {}) {\n    const { abi: _, ...rest } = options;\n    return ['readContract', (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.filterQueryOptions)(rest)];\n}\n//# sourceMappingURL=readContract.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/query/readContract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/query/utils.js":
/*!**********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/query/utils.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   filterQueryOptions: () => (/* binding */ filterQueryOptions),\n/* harmony export */   hashFn: () => (/* binding */ hashFn),\n/* harmony export */   structuralSharing: () => (/* binding */ structuralSharing)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n\nfunction structuralSharing(oldData, newData) {\n    return (0,_tanstack_query_core__WEBPACK_IMPORTED_MODULE_0__.replaceEqualDeep)(oldData, newData);\n}\nfunction hashFn(queryKey) {\n    return JSON.stringify(queryKey, (_, value) => {\n        if (isPlainObject(value))\n            return Object.keys(value)\n                .sort()\n                .reduce((result, key) => {\n                result[key] = value[key];\n                return result;\n            }, {});\n        if (typeof value === 'bigint')\n            return value.toString();\n        return value;\n    });\n}\n// biome-ignore lint/complexity/noBannedTypes:\nfunction isPlainObject(value) {\n    if (!hasObjectPrototype(value)) {\n        return false;\n    }\n    // If has modified constructor\n    const ctor = value.constructor;\n    if (typeof ctor === 'undefined')\n        return true;\n    // If has modified prototype\n    const prot = ctor.prototype;\n    if (!hasObjectPrototype(prot))\n        return false;\n    // If constructor does not have an Object-specific method\n    // biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>\n    if (!prot.hasOwnProperty('isPrototypeOf'))\n        return false;\n    // Most likely a plain Object\n    return true;\n}\nfunction hasObjectPrototype(o) {\n    return Object.prototype.toString.call(o) === '[object Object]';\n}\nfunction filterQueryOptions(options) {\n    // destructuring is super fast\n    // biome-ignore format: no formatting\n    const { \n    // import('@tanstack/query-core').QueryOptions\n    _defaulted, behavior, gcTime, initialData, initialDataUpdatedAt, maxPages, meta, networkMode, queryFn, queryHash, queryKey, queryKeyHashFn, retry, retryDelay, structuralSharing, \n    // import('@tanstack/query-core').InfiniteQueryObserverOptions\n    getPreviousPageParam, getNextPageParam, initialPageParam, \n    // import('@tanstack/react-query').UseQueryOptions\n    _optimisticResults, enabled, notifyOnChangeProps, placeholderData, refetchInterval, refetchIntervalInBackground, refetchOnMount, refetchOnReconnect, refetchOnWindowFocus, retryOnMount, select, staleTime, suspense, throwOnError, \n    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n    // wagmi\n    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n    config, connector, query, ...rest } = options;\n    return rest;\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/query/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/query/waitForTransactionReceipt.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/query/waitForTransactionReceipt.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   waitForTransactionReceiptQueryKey: () => (/* binding */ waitForTransactionReceiptQueryKey),\n/* harmony export */   waitForTransactionReceiptQueryOptions: () => (/* binding */ waitForTransactionReceiptQueryOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_waitForTransactionReceipt_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/waitForTransactionReceipt.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/query/utils.js\");\n\n\nfunction waitForTransactionReceiptQueryOptions(config, options = {}) {\n    return {\n        async queryFn({ queryKey }) {\n            const { hash, ...parameters } = queryKey[1];\n            if (!hash)\n                throw new Error('hash is required');\n            return (0,_actions_waitForTransactionReceipt_js__WEBPACK_IMPORTED_MODULE_0__.waitForTransactionReceipt)(config, {\n                ...parameters,\n                onReplaced: options.onReplaced,\n                hash,\n            });\n        },\n        queryKey: waitForTransactionReceiptQueryKey(options),\n    };\n}\nfunction waitForTransactionReceiptQueryKey(options = {}) {\n    const { onReplaced: _, ...rest } = options;\n    return ['waitForTransactionReceipt', (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.filterQueryOptions)(rest)];\n}\n//# sourceMappingURL=waitForTransactionReceipt.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vcXVlcnkvd2FpdEZvclRyYW5zYWN0aW9uUmVjZWlwdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXFGO0FBQ3JDO0FBQ3pDLG1FQUFtRTtBQUMxRTtBQUNBLHdCQUF3QixVQUFVO0FBQ2xDLG9CQUFvQixzQkFBc0I7QUFDMUM7QUFDQTtBQUNBLG1CQUFtQixnR0FBeUI7QUFDNUM7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDTyx1REFBdUQ7QUFDOUQsWUFBWSx5QkFBeUI7QUFDckMseUNBQXlDLDZEQUFrQjtBQUMzRDtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3F1ZXJ5L3dhaXRGb3JUcmFuc2FjdGlvblJlY2VpcHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgd2FpdEZvclRyYW5zYWN0aW9uUmVjZWlwdCwgfSBmcm9tICcuLi9hY3Rpb25zL3dhaXRGb3JUcmFuc2FjdGlvblJlY2VpcHQuanMnO1xuaW1wb3J0IHsgZmlsdGVyUXVlcnlPcHRpb25zIH0gZnJvbSAnLi91dGlscy5qcyc7XG5leHBvcnQgZnVuY3Rpb24gd2FpdEZvclRyYW5zYWN0aW9uUmVjZWlwdFF1ZXJ5T3B0aW9ucyhjb25maWcsIG9wdGlvbnMgPSB7fSkge1xuICAgIHJldHVybiB7XG4gICAgICAgIGFzeW5jIHF1ZXJ5Rm4oeyBxdWVyeUtleSB9KSB7XG4gICAgICAgICAgICBjb25zdCB7IGhhc2gsIC4uLnBhcmFtZXRlcnMgfSA9IHF1ZXJ5S2V5WzFdO1xuICAgICAgICAgICAgaWYgKCFoYXNoKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignaGFzaCBpcyByZXF1aXJlZCcpO1xuICAgICAgICAgICAgcmV0dXJuIHdhaXRGb3JUcmFuc2FjdGlvblJlY2VpcHQoY29uZmlnLCB7XG4gICAgICAgICAgICAgICAgLi4ucGFyYW1ldGVycyxcbiAgICAgICAgICAgICAgICBvblJlcGxhY2VkOiBvcHRpb25zLm9uUmVwbGFjZWQsXG4gICAgICAgICAgICAgICAgaGFzaCxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9LFxuICAgICAgICBxdWVyeUtleTogd2FpdEZvclRyYW5zYWN0aW9uUmVjZWlwdFF1ZXJ5S2V5KG9wdGlvbnMpLFxuICAgIH07XG59XG5leHBvcnQgZnVuY3Rpb24gd2FpdEZvclRyYW5zYWN0aW9uUmVjZWlwdFF1ZXJ5S2V5KG9wdGlvbnMgPSB7fSkge1xuICAgIGNvbnN0IHsgb25SZXBsYWNlZDogXywgLi4ucmVzdCB9ID0gb3B0aW9ucztcbiAgICByZXR1cm4gWyd3YWl0Rm9yVHJhbnNhY3Rpb25SZWNlaXB0JywgZmlsdGVyUXVlcnlPcHRpb25zKHJlc3QpXTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdhaXRGb3JUcmFuc2FjdGlvblJlY2VpcHQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/query/waitForTransactionReceipt.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/query/writeContract.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/query/writeContract.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   writeContractMutationOptions: () => (/* binding */ writeContractMutationOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_writeContract_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/writeContract.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/writeContract.js\");\n\nfunction writeContractMutationOptions(config) {\n    return {\n        mutationFn(variables) {\n            return (0,_actions_writeContract_js__WEBPACK_IMPORTED_MODULE_0__.writeContract)(config, variables);\n        },\n        mutationKey: ['writeContract'],\n    };\n}\n//# sourceMappingURL=writeContract.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vcXVlcnkvd3JpdGVDb250cmFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE2RDtBQUN0RDtBQUNQO0FBQ0E7QUFDQSxtQkFBbUIsd0VBQWE7QUFDaEMsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3F1ZXJ5L3dyaXRlQ29udHJhY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgd3JpdGVDb250cmFjdCwgfSBmcm9tICcuLi9hY3Rpb25zL3dyaXRlQ29udHJhY3QuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIHdyaXRlQ29udHJhY3RNdXRhdGlvbk9wdGlvbnMoY29uZmlnKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgbXV0YXRpb25Gbih2YXJpYWJsZXMpIHtcbiAgICAgICAgICAgIHJldHVybiB3cml0ZUNvbnRyYWN0KGNvbmZpZywgdmFyaWFibGVzKTtcbiAgICAgICAgfSxcbiAgICAgICAgbXV0YXRpb25LZXk6IFsnd3JpdGVDb250cmFjdCddLFxuICAgIH07XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD13cml0ZUNvbnRyYWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/query/writeContract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deepEqual: () => (/* binding */ deepEqual)\n/* harmony export */ });\n/** Forked from https://github.com/epoberezkin/fast-deep-equal */\nfunction deepEqual(a, b) {\n    if (a === b)\n        return true;\n    if (a && b && typeof a === 'object' && typeof b === 'object') {\n        if (a.constructor !== b.constructor)\n            return false;\n        let length;\n        let i;\n        if (Array.isArray(a) && Array.isArray(b)) {\n            length = a.length;\n            if (length !== b.length)\n                return false;\n            for (i = length; i-- !== 0;)\n                if (!deepEqual(a[i], b[i]))\n                    return false;\n            return true;\n        }\n        if (a.valueOf !== Object.prototype.valueOf)\n            return a.valueOf() === b.valueOf();\n        if (a.toString !== Object.prototype.toString)\n            return a.toString() === b.toString();\n        const keys = Object.keys(a);\n        length = keys.length;\n        if (length !== Object.keys(b).length)\n            return false;\n        for (i = length; i-- !== 0;)\n            if (!Object.prototype.hasOwnProperty.call(b, keys[i]))\n                return false;\n        for (i = length; i-- !== 0;) {\n            const key = keys[i];\n            if (key && !deepEqual(a[key], b[key]))\n                return false;\n        }\n        return true;\n    }\n    // true if both NaN, false otherwise\n    // biome-ignore lint/suspicious/noSelfCompare: <explanation>\n    return a !== a && b !== b;\n}\n//# sourceMappingURL=deepEqual.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZGVlcEVxdWFsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixVQUFVO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixVQUFVO0FBQ25DO0FBQ0E7QUFDQSx5QkFBeUIsVUFBVTtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3V0aWxzL2RlZXBFcXVhbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiogRm9ya2VkIGZyb20gaHR0cHM6Ly9naXRodWIuY29tL2Vwb2JlcmV6a2luL2Zhc3QtZGVlcC1lcXVhbCAqL1xuZXhwb3J0IGZ1bmN0aW9uIGRlZXBFcXVhbChhLCBiKSB7XG4gICAgaWYgKGEgPT09IGIpXG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIGlmIChhICYmIGIgJiYgdHlwZW9mIGEgPT09ICdvYmplY3QnICYmIHR5cGVvZiBiID09PSAnb2JqZWN0Jykge1xuICAgICAgICBpZiAoYS5jb25zdHJ1Y3RvciAhPT0gYi5jb25zdHJ1Y3RvcilcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgbGV0IGxlbmd0aDtcbiAgICAgICAgbGV0IGk7XG4gICAgICAgIGlmIChBcnJheS5pc0FycmF5KGEpICYmIEFycmF5LmlzQXJyYXkoYikpIHtcbiAgICAgICAgICAgIGxlbmd0aCA9IGEubGVuZ3RoO1xuICAgICAgICAgICAgaWYgKGxlbmd0aCAhPT0gYi5sZW5ndGgpXG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgZm9yIChpID0gbGVuZ3RoOyBpLS0gIT09IDA7KVxuICAgICAgICAgICAgICAgIGlmICghZGVlcEVxdWFsKGFbaV0sIGJbaV0pKVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoYS52YWx1ZU9mICE9PSBPYmplY3QucHJvdG90eXBlLnZhbHVlT2YpXG4gICAgICAgICAgICByZXR1cm4gYS52YWx1ZU9mKCkgPT09IGIudmFsdWVPZigpO1xuICAgICAgICBpZiAoYS50b1N0cmluZyAhPT0gT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZylcbiAgICAgICAgICAgIHJldHVybiBhLnRvU3RyaW5nKCkgPT09IGIudG9TdHJpbmcoKTtcbiAgICAgICAgY29uc3Qga2V5cyA9IE9iamVjdC5rZXlzKGEpO1xuICAgICAgICBsZW5ndGggPSBrZXlzLmxlbmd0aDtcbiAgICAgICAgaWYgKGxlbmd0aCAhPT0gT2JqZWN0LmtleXMoYikubGVuZ3RoKVxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICBmb3IgKGkgPSBsZW5ndGg7IGktLSAhPT0gMDspXG4gICAgICAgICAgICBpZiAoIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChiLCBrZXlzW2ldKSlcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIGZvciAoaSA9IGxlbmd0aDsgaS0tICE9PSAwOykge1xuICAgICAgICAgICAgY29uc3Qga2V5ID0ga2V5c1tpXTtcbiAgICAgICAgICAgIGlmIChrZXkgJiYgIWRlZXBFcXVhbChhW2tleV0sIGJba2V5XSkpXG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICAvLyB0cnVlIGlmIGJvdGggTmFOLCBmYWxzZSBvdGhlcndpc2VcbiAgICAvLyBiaW9tZS1pZ25vcmUgbGludC9zdXNwaWNpb3VzL25vU2VsZkNvbXBhcmU6IDxleHBsYW5hdGlvbj5cbiAgICByZXR1cm4gYSAhPT0gYSAmJiBiICE9PSBiO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGVlcEVxdWFsLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deserialize.js":
/*!****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/deserialize.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserialize: () => (/* binding */ deserialize)\n/* harmony export */ });\nfunction deserialize(value, reviver) {\n    return JSON.parse(value, (key, value_) => {\n        let value = value_;\n        if (value?.__type === 'bigint')\n            value = BigInt(value.value);\n        if (value?.__type === 'Map')\n            value = new Map(value.value);\n        return reviver?.(key, value) ?? value;\n    });\n}\n//# sourceMappingURL=deserialize.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZGVzZXJpYWxpemUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS91dGlscy9kZXNlcmlhbGl6ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZGVzZXJpYWxpemUodmFsdWUsIHJldml2ZXIpIHtcbiAgICByZXR1cm4gSlNPTi5wYXJzZSh2YWx1ZSwgKGtleSwgdmFsdWVfKSA9PiB7XG4gICAgICAgIGxldCB2YWx1ZSA9IHZhbHVlXztcbiAgICAgICAgaWYgKHZhbHVlPy5fX3R5cGUgPT09ICdiaWdpbnQnKVxuICAgICAgICAgICAgdmFsdWUgPSBCaWdJbnQodmFsdWUudmFsdWUpO1xuICAgICAgICBpZiAodmFsdWU/Ll9fdHlwZSA9PT0gJ01hcCcpXG4gICAgICAgICAgICB2YWx1ZSA9IG5ldyBNYXAodmFsdWUudmFsdWUpO1xuICAgICAgICByZXR1cm4gcmV2aXZlcj8uKGtleSwgdmFsdWUpID8/IHZhbHVlO1xuICAgIH0pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGVzZXJpYWxpemUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deserialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractRpcUrls: () => (/* binding */ extractRpcUrls)\n/* harmony export */ });\nfunction extractRpcUrls(parameters) {\n    const { chain } = parameters;\n    const fallbackUrl = chain.rpcUrls.default.http[0];\n    if (!parameters.transports)\n        return [fallbackUrl];\n    const transport = parameters.transports?.[chain.id]?.({ chain });\n    const transports = transport?.value?.transports || [transport];\n    return transports.map(({ value }) => value?.url || fallbackUrl);\n}\n//# sourceMappingURL=extractRpcUrls.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZXh0cmFjdFJwY1VybHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AsWUFBWSxRQUFRO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBLDREQUE0RCxPQUFPO0FBQ25FO0FBQ0EsNkJBQTZCLE9BQU87QUFDcEM7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS91dGlscy9leHRyYWN0UnBjVXJscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZXh0cmFjdFJwY1VybHMocGFyYW1ldGVycykge1xuICAgIGNvbnN0IHsgY2hhaW4gfSA9IHBhcmFtZXRlcnM7XG4gICAgY29uc3QgZmFsbGJhY2tVcmwgPSBjaGFpbi5ycGNVcmxzLmRlZmF1bHQuaHR0cFswXTtcbiAgICBpZiAoIXBhcmFtZXRlcnMudHJhbnNwb3J0cylcbiAgICAgICAgcmV0dXJuIFtmYWxsYmFja1VybF07XG4gICAgY29uc3QgdHJhbnNwb3J0ID0gcGFyYW1ldGVycy50cmFuc3BvcnRzPy5bY2hhaW4uaWRdPy4oeyBjaGFpbiB9KTtcbiAgICBjb25zdCB0cmFuc3BvcnRzID0gdHJhbnNwb3J0Py52YWx1ZT8udHJhbnNwb3J0cyB8fCBbdHJhbnNwb3J0XTtcbiAgICByZXR1cm4gdHJhbnNwb3J0cy5tYXAoKHsgdmFsdWUgfSkgPT4gdmFsdWU/LnVybCB8fCBmYWxsYmFja1VybCk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1leHRyYWN0UnBjVXJscy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/getAction.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAction: () => (/* binding */ getAction)\n/* harmony export */ });\n/**\n * Retrieves and returns an action from the client (if exists), and falls\n * back to the tree-shakable action.\n *\n * Useful for extracting overridden actions from a client (ie. if a consumer\n * wants to override the `sendTransaction` implementation).\n */\nfunction getAction(client, actionFn, \n// Some minifiers drop `Function.prototype.name`, or replace it with short letters,\n// meaning that `actionFn.name` will not always work. For that case, the consumer\n// needs to pass the name explicitly.\nname) {\n    const action_implicit = client[actionFn.name];\n    if (typeof action_implicit === 'function')\n        return action_implicit;\n    const action_explicit = client[name];\n    if (typeof action_explicit === 'function')\n        return action_explicit;\n    return (params) => actionFn(client, params);\n}\n//# sourceMappingURL=getAction.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZ2V0QWN0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZ2V0QWN0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogUmV0cmlldmVzIGFuZCByZXR1cm5zIGFuIGFjdGlvbiBmcm9tIHRoZSBjbGllbnQgKGlmIGV4aXN0cyksIGFuZCBmYWxsc1xuICogYmFjayB0byB0aGUgdHJlZS1zaGFrYWJsZSBhY3Rpb24uXG4gKlxuICogVXNlZnVsIGZvciBleHRyYWN0aW5nIG92ZXJyaWRkZW4gYWN0aW9ucyBmcm9tIGEgY2xpZW50IChpZS4gaWYgYSBjb25zdW1lclxuICogd2FudHMgdG8gb3ZlcnJpZGUgdGhlIGBzZW5kVHJhbnNhY3Rpb25gIGltcGxlbWVudGF0aW9uKS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldEFjdGlvbihjbGllbnQsIGFjdGlvbkZuLCBcbi8vIFNvbWUgbWluaWZpZXJzIGRyb3AgYEZ1bmN0aW9uLnByb3RvdHlwZS5uYW1lYCwgb3IgcmVwbGFjZSBpdCB3aXRoIHNob3J0IGxldHRlcnMsXG4vLyBtZWFuaW5nIHRoYXQgYGFjdGlvbkZuLm5hbWVgIHdpbGwgbm90IGFsd2F5cyB3b3JrLiBGb3IgdGhhdCBjYXNlLCB0aGUgY29uc3VtZXJcbi8vIG5lZWRzIHRvIHBhc3MgdGhlIG5hbWUgZXhwbGljaXRseS5cbm5hbWUpIHtcbiAgICBjb25zdCBhY3Rpb25faW1wbGljaXQgPSBjbGllbnRbYWN0aW9uRm4ubmFtZV07XG4gICAgaWYgKHR5cGVvZiBhY3Rpb25faW1wbGljaXQgPT09ICdmdW5jdGlvbicpXG4gICAgICAgIHJldHVybiBhY3Rpb25faW1wbGljaXQ7XG4gICAgY29uc3QgYWN0aW9uX2V4cGxpY2l0ID0gY2xpZW50W25hbWVdO1xuICAgIGlmICh0eXBlb2YgYWN0aW9uX2V4cGxpY2l0ID09PSAnZnVuY3Rpb24nKVxuICAgICAgICByZXR1cm4gYWN0aW9uX2V4cGxpY2l0O1xuICAgIHJldHVybiAocGFyYW1zKSA9PiBhY3Rpb25GbihjbGllbnQsIHBhcmFtcyk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXRBY3Rpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getVersion.js":
/*!***************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/getVersion.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getVersion: () => (/* binding */ getVersion)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/version.js\");\n\nconst getVersion = () => `@wagmi/core@${_version_js__WEBPACK_IMPORTED_MODULE_0__.version}`;\n//# sourceMappingURL=getVersion.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZ2V0VmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3QztBQUNqQyx3Q0FBd0MsZ0RBQU8sQ0FBQztBQUN2RCIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS91dGlscy9nZXRWZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHZlcnNpb24gfSBmcm9tICcuLi92ZXJzaW9uLmpzJztcbmV4cG9ydCBjb25zdCBnZXRWZXJzaW9uID0gKCkgPT4gYEB3YWdtaS9jb3JlQCR7dmVyc2lvbn1gO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2V0VmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getVersion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/serialize.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/serialize.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serialize: () => (/* binding */ serialize)\n/* harmony export */ });\n/**\n * Get the reference key for the circular value\n *\n * @param keys the keys to build the reference key from\n * @param cutoff the maximum number of keys to include\n * @returns the reference key\n */\nfunction getReferenceKey(keys, cutoff) {\n    return keys.slice(0, cutoff).join('.') || '.';\n}\n/**\n * Faster `Array.prototype.indexOf` implementation build for slicing / splicing\n *\n * @param array the array to match the value in\n * @param value the value to match\n * @returns the matching index, or -1\n */\nfunction getCutoff(array, value) {\n    const { length } = array;\n    for (let index = 0; index < length; ++index) {\n        if (array[index] === value) {\n            return index + 1;\n        }\n    }\n    return 0;\n}\n/**\n * Create a replacer method that handles circular values\n *\n * @param [replacer] a custom replacer to use for non-circular values\n * @param [circularReplacer] a custom replacer to use for circular methods\n * @returns the value to stringify\n */\nfunction createReplacer(replacer, circularReplacer) {\n    const hasReplacer = typeof replacer === 'function';\n    const hasCircularReplacer = typeof circularReplacer === 'function';\n    const cache = [];\n    const keys = [];\n    return function replace(key, value) {\n        if (typeof value === 'object') {\n            if (cache.length) {\n                const thisCutoff = getCutoff(cache, this);\n                if (thisCutoff === 0) {\n                    cache[cache.length] = this;\n                }\n                else {\n                    cache.splice(thisCutoff);\n                    keys.splice(thisCutoff);\n                }\n                keys[keys.length] = key;\n                const valueCutoff = getCutoff(cache, value);\n                if (valueCutoff !== 0) {\n                    return hasCircularReplacer\n                        ? circularReplacer.call(this, key, value, getReferenceKey(keys, valueCutoff))\n                        : `[ref=${getReferenceKey(keys, valueCutoff)}]`;\n                }\n            }\n            else {\n                cache[0] = value;\n                keys[0] = key;\n            }\n        }\n        return hasReplacer ? replacer.call(this, key, value) : value;\n    };\n}\n/**\n * Stringifier that handles circular values\n *\n * Forked from https://github.com/planttheidea/fast-stringify\n *\n * @param value to stringify\n * @param [replacer] a custom replacer function for handling standard values\n * @param [indent] the number of spaces to indent the output by\n * @param [circularReplacer] a custom replacer function for handling circular values\n * @returns the stringified output\n */\nfunction serialize(value, replacer, indent, circularReplacer) {\n    return JSON.stringify(value, createReplacer((key, value_) => {\n        let value = value_;\n        if (typeof value === 'bigint')\n            value = { __type: 'bigint', value: value_.toString() };\n        if (value instanceof Map)\n            value = { __type: 'Map', value: Array.from(value_.entries()) };\n        return replacer?.(key, value) ?? value;\n    }, circularReplacer), indent ?? undefined);\n}\n//# sourceMappingURL=serialize.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/serialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/uid.js":
/*!********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/uid.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uid: () => (/* binding */ uid)\n/* harmony export */ });\nconst size = 256;\nlet index = size;\nlet buffer;\nfunction uid(length = 11) {\n    if (!buffer || index + length > size * 2) {\n        buffer = '';\n        index = 0;\n        for (let i = 0; i < size; i++) {\n            buffer += ((256 + Math.random() * 256) | 0).toString(16).substring(1);\n        }\n    }\n    return buffer.substring(index, index++ + length);\n}\n//# sourceMappingURL=uid.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvdWlkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixVQUFVO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS91dGlscy91aWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgc2l6ZSA9IDI1NjtcbmxldCBpbmRleCA9IHNpemU7XG5sZXQgYnVmZmVyO1xuZXhwb3J0IGZ1bmN0aW9uIHVpZChsZW5ndGggPSAxMSkge1xuICAgIGlmICghYnVmZmVyIHx8IGluZGV4ICsgbGVuZ3RoID4gc2l6ZSAqIDIpIHtcbiAgICAgICAgYnVmZmVyID0gJyc7XG4gICAgICAgIGluZGV4ID0gMDtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzaXplOyBpKyspIHtcbiAgICAgICAgICAgIGJ1ZmZlciArPSAoKDI1NiArIE1hdGgucmFuZG9tKCkgKiAyNTYpIHwgMCkudG9TdHJpbmcoMTYpLnN1YnN0cmluZygxKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gYnVmZmVyLnN1YnN0cmluZyhpbmRleCwgaW5kZXgrKyArIGxlbmd0aCk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD11aWQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/uid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/version.js":
/*!******************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/version.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.17.3';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS92ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCB2ZXJzaW9uID0gJzIuMTcuMyc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/node_modules/zustand/esm/middleware.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/@wagmi/core/node_modules/zustand/esm/middleware.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   combine: () => (/* binding */ combine),\n/* harmony export */   createJSONStorage: () => (/* binding */ createJSONStorage),\n/* harmony export */   devtools: () => (/* binding */ devtools),\n/* harmony export */   persist: () => (/* binding */ persist),\n/* harmony export */   redux: () => (/* binding */ redux),\n/* harmony export */   subscribeWithSelector: () => (/* binding */ subscribeWithSelector)\n/* harmony export */ });\nconst reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...a) => api.dispatch(...a), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === void 0) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : ( false ? 0 : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extensionConnector) {\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = (state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const action = nameOrAction === void 0 ? { type: anonymousActionType || \"anonymous\" } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === void 0) {\n      connection == null ? void 0 : connection.send(action, get());\n      return r;\n    }\n    connection == null ? void 0 : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? void 0 : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? void 0 : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...a) => {\n      if (( false ? 0 : void 0) !== \"production\" && a[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...a);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === void 0) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format.\n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === void 0) {\n              return connection == null ? void 0 : connection.init(api.getState());\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === void 0) {\n              connection == null ? void 0 : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                connection == null ? void 0 : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n            if (!lastComputedState) return;\n            if (store === void 0) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? void 0 : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, f) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== void 0) f(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? void 0 : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nconst combine = (initialState, create) => (...a) => Object.assign({}, initialState, create(...a));\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? void 0 : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(\n      name,\n      JSON.stringify(newValue, options == null ? void 0 : options.replacer)\n    ),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst persistImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            return [\n              true,\n              options.migrate(\n                deserializedStorageValue.state,\n                deserializedStorageValue.version\n              )\n            ];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, void 0];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persist = persistImpl;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvbm9kZV9tb2R1bGVzL3p1c3RhbmQvZXNtL21pZGRsZXdhcmUubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0EsOENBQThDO0FBQzlDLFVBQVUsa0RBQWtEO0FBQzVEO0FBQ0E7QUFDQSx1REFBdUQsTUFBZSxHQUFHLENBQW9CO0FBQzdGLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsdUNBQXVDO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0NBQStDLDJDQUEyQyx1Q0FBdUMscUJBQXFCO0FBQ3RKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLE1BQU0sR0FBRyxZQUFZO0FBQ3RDLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxNQUFlLEdBQUcsQ0FBb0I7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvRkFBb0YsaUNBQWlDLGlCQUFpQjtBQUN0STtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLG9CQUFvQixrQkFBa0I7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsb0VBQW9FOztBQUVwRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFLGFBQWE7QUFDOUU7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLFVBQVU7QUFDakQ7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXVGIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL25vZGVfbW9kdWxlcy96dXN0YW5kL2VzbS9taWRkbGV3YXJlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCByZWR1eEltcGwgPSAocmVkdWNlciwgaW5pdGlhbCkgPT4gKHNldCwgX2dldCwgYXBpKSA9PiB7XG4gIGFwaS5kaXNwYXRjaCA9IChhY3Rpb24pID0+IHtcbiAgICBzZXQoKHN0YXRlKSA9PiByZWR1Y2VyKHN0YXRlLCBhY3Rpb24pLCBmYWxzZSwgYWN0aW9uKTtcbiAgICByZXR1cm4gYWN0aW9uO1xuICB9O1xuICBhcGkuZGlzcGF0Y2hGcm9tRGV2dG9vbHMgPSB0cnVlO1xuICByZXR1cm4geyBkaXNwYXRjaDogKC4uLmEpID0+IGFwaS5kaXNwYXRjaCguLi5hKSwgLi4uaW5pdGlhbCB9O1xufTtcbmNvbnN0IHJlZHV4ID0gcmVkdXhJbXBsO1xuXG5jb25zdCB0cmFja2VkQ29ubmVjdGlvbnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuY29uc3QgZ2V0VHJhY2tlZENvbm5lY3Rpb25TdGF0ZSA9IChuYW1lKSA9PiB7XG4gIGNvbnN0IGFwaSA9IHRyYWNrZWRDb25uZWN0aW9ucy5nZXQobmFtZSk7XG4gIGlmICghYXBpKSByZXR1cm4ge307XG4gIHJldHVybiBPYmplY3QuZnJvbUVudHJpZXMoXG4gICAgT2JqZWN0LmVudHJpZXMoYXBpLnN0b3JlcykubWFwKChba2V5LCBhcGkyXSkgPT4gW2tleSwgYXBpMi5nZXRTdGF0ZSgpXSlcbiAgKTtcbn07XG5jb25zdCBleHRyYWN0Q29ubmVjdGlvbkluZm9ybWF0aW9uID0gKHN0b3JlLCBleHRlbnNpb25Db25uZWN0b3IsIG9wdGlvbnMpID0+IHtcbiAgaWYgKHN0b3JlID09PSB2b2lkIDApIHtcbiAgICByZXR1cm4ge1xuICAgICAgdHlwZTogXCJ1bnRyYWNrZWRcIixcbiAgICAgIGNvbm5lY3Rpb246IGV4dGVuc2lvbkNvbm5lY3Rvci5jb25uZWN0KG9wdGlvbnMpXG4gICAgfTtcbiAgfVxuICBjb25zdCBleGlzdGluZ0Nvbm5lY3Rpb24gPSB0cmFja2VkQ29ubmVjdGlvbnMuZ2V0KG9wdGlvbnMubmFtZSk7XG4gIGlmIChleGlzdGluZ0Nvbm5lY3Rpb24pIHtcbiAgICByZXR1cm4geyB0eXBlOiBcInRyYWNrZWRcIiwgc3RvcmUsIC4uLmV4aXN0aW5nQ29ubmVjdGlvbiB9O1xuICB9XG4gIGNvbnN0IG5ld0Nvbm5lY3Rpb24gPSB7XG4gICAgY29ubmVjdGlvbjogZXh0ZW5zaW9uQ29ubmVjdG9yLmNvbm5lY3Qob3B0aW9ucyksXG4gICAgc3RvcmVzOiB7fVxuICB9O1xuICB0cmFja2VkQ29ubmVjdGlvbnMuc2V0KG9wdGlvbnMubmFtZSwgbmV3Q29ubmVjdGlvbik7XG4gIHJldHVybiB7IHR5cGU6IFwidHJhY2tlZFwiLCBzdG9yZSwgLi4ubmV3Q29ubmVjdGlvbiB9O1xufTtcbmNvbnN0IGRldnRvb2xzSW1wbCA9IChmbiwgZGV2dG9vbHNPcHRpb25zID0ge30pID0+IChzZXQsIGdldCwgYXBpKSA9PiB7XG4gIGNvbnN0IHsgZW5hYmxlZCwgYW5vbnltb3VzQWN0aW9uVHlwZSwgc3RvcmUsIC4uLm9wdGlvbnMgfSA9IGRldnRvb2xzT3B0aW9ucztcbiAgbGV0IGV4dGVuc2lvbkNvbm5lY3RvcjtcbiAgdHJ5IHtcbiAgICBleHRlbnNpb25Db25uZWN0b3IgPSAoZW5hYmxlZCAhPSBudWxsID8gZW5hYmxlZCA6IChpbXBvcnQubWV0YS5lbnYgPyBpbXBvcnQubWV0YS5lbnYuTU9ERSA6IHZvaWQgMCkgIT09IFwicHJvZHVjdGlvblwiKSAmJiB3aW5kb3cuX19SRURVWF9ERVZUT09MU19FWFRFTlNJT05fXztcbiAgfSBjYXRjaCAoZSkge1xuICB9XG4gIGlmICghZXh0ZW5zaW9uQ29ubmVjdG9yKSB7XG4gICAgcmV0dXJuIGZuKHNldCwgZ2V0LCBhcGkpO1xuICB9XG4gIGNvbnN0IHsgY29ubmVjdGlvbiwgLi4uY29ubmVjdGlvbkluZm9ybWF0aW9uIH0gPSBleHRyYWN0Q29ubmVjdGlvbkluZm9ybWF0aW9uKHN0b3JlLCBleHRlbnNpb25Db25uZWN0b3IsIG9wdGlvbnMpO1xuICBsZXQgaXNSZWNvcmRpbmcgPSB0cnVlO1xuICBhcGkuc2V0U3RhdGUgPSAoc3RhdGUsIHJlcGxhY2UsIG5hbWVPckFjdGlvbikgPT4ge1xuICAgIGNvbnN0IHIgPSBzZXQoc3RhdGUsIHJlcGxhY2UpO1xuICAgIGlmICghaXNSZWNvcmRpbmcpIHJldHVybiByO1xuICAgIGNvbnN0IGFjdGlvbiA9IG5hbWVPckFjdGlvbiA9PT0gdm9pZCAwID8geyB0eXBlOiBhbm9ueW1vdXNBY3Rpb25UeXBlIHx8IFwiYW5vbnltb3VzXCIgfSA6IHR5cGVvZiBuYW1lT3JBY3Rpb24gPT09IFwic3RyaW5nXCIgPyB7IHR5cGU6IG5hbWVPckFjdGlvbiB9IDogbmFtZU9yQWN0aW9uO1xuICAgIGlmIChzdG9yZSA9PT0gdm9pZCAwKSB7XG4gICAgICBjb25uZWN0aW9uID09IG51bGwgPyB2b2lkIDAgOiBjb25uZWN0aW9uLnNlbmQoYWN0aW9uLCBnZXQoKSk7XG4gICAgICByZXR1cm4gcjtcbiAgICB9XG4gICAgY29ubmVjdGlvbiA9PSBudWxsID8gdm9pZCAwIDogY29ubmVjdGlvbi5zZW5kKFxuICAgICAge1xuICAgICAgICAuLi5hY3Rpb24sXG4gICAgICAgIHR5cGU6IGAke3N0b3JlfS8ke2FjdGlvbi50eXBlfWBcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIC4uLmdldFRyYWNrZWRDb25uZWN0aW9uU3RhdGUob3B0aW9ucy5uYW1lKSxcbiAgICAgICAgW3N0b3JlXTogYXBpLmdldFN0YXRlKClcbiAgICAgIH1cbiAgICApO1xuICAgIHJldHVybiByO1xuICB9O1xuICBjb25zdCBzZXRTdGF0ZUZyb21EZXZ0b29scyA9ICguLi5hKSA9PiB7XG4gICAgY29uc3Qgb3JpZ2luYWxJc1JlY29yZGluZyA9IGlzUmVjb3JkaW5nO1xuICAgIGlzUmVjb3JkaW5nID0gZmFsc2U7XG4gICAgc2V0KC4uLmEpO1xuICAgIGlzUmVjb3JkaW5nID0gb3JpZ2luYWxJc1JlY29yZGluZztcbiAgfTtcbiAgY29uc3QgaW5pdGlhbFN0YXRlID0gZm4oYXBpLnNldFN0YXRlLCBnZXQsIGFwaSk7XG4gIGlmIChjb25uZWN0aW9uSW5mb3JtYXRpb24udHlwZSA9PT0gXCJ1bnRyYWNrZWRcIikge1xuICAgIGNvbm5lY3Rpb24gPT0gbnVsbCA/IHZvaWQgMCA6IGNvbm5lY3Rpb24uaW5pdChpbml0aWFsU3RhdGUpO1xuICB9IGVsc2Uge1xuICAgIGNvbm5lY3Rpb25JbmZvcm1hdGlvbi5zdG9yZXNbY29ubmVjdGlvbkluZm9ybWF0aW9uLnN0b3JlXSA9IGFwaTtcbiAgICBjb25uZWN0aW9uID09IG51bGwgPyB2b2lkIDAgOiBjb25uZWN0aW9uLmluaXQoXG4gICAgICBPYmplY3QuZnJvbUVudHJpZXMoXG4gICAgICAgIE9iamVjdC5lbnRyaWVzKGNvbm5lY3Rpb25JbmZvcm1hdGlvbi5zdG9yZXMpLm1hcCgoW2tleSwgc3RvcmUyXSkgPT4gW1xuICAgICAgICAgIGtleSxcbiAgICAgICAgICBrZXkgPT09IGNvbm5lY3Rpb25JbmZvcm1hdGlvbi5zdG9yZSA/IGluaXRpYWxTdGF0ZSA6IHN0b3JlMi5nZXRTdGF0ZSgpXG4gICAgICAgIF0pXG4gICAgICApXG4gICAgKTtcbiAgfVxuICBpZiAoYXBpLmRpc3BhdGNoRnJvbURldnRvb2xzICYmIHR5cGVvZiBhcGkuZGlzcGF0Y2ggPT09IFwiZnVuY3Rpb25cIikge1xuICAgIGxldCBkaWRXYXJuQWJvdXRSZXNlcnZlZEFjdGlvblR5cGUgPSBmYWxzZTtcbiAgICBjb25zdCBvcmlnaW5hbERpc3BhdGNoID0gYXBpLmRpc3BhdGNoO1xuICAgIGFwaS5kaXNwYXRjaCA9ICguLi5hKSA9PiB7XG4gICAgICBpZiAoKGltcG9ydC5tZXRhLmVudiA/IGltcG9ydC5tZXRhLmVudi5NT0RFIDogdm9pZCAwKSAhPT0gXCJwcm9kdWN0aW9uXCIgJiYgYVswXS50eXBlID09PSBcIl9fc2V0U3RhdGVcIiAmJiAhZGlkV2FybkFib3V0UmVzZXJ2ZWRBY3Rpb25UeXBlKSB7XG4gICAgICAgIGNvbnNvbGUud2FybihcbiAgICAgICAgICAnW3p1c3RhbmQgZGV2dG9vbHMgbWlkZGxld2FyZV0gXCJfX3NldFN0YXRlXCIgYWN0aW9uIHR5cGUgaXMgcmVzZXJ2ZWQgdG8gc2V0IHN0YXRlIGZyb20gdGhlIGRldnRvb2xzLiBBdm9pZCB1c2luZyBpdC4nXG4gICAgICAgICk7XG4gICAgICAgIGRpZFdhcm5BYm91dFJlc2VydmVkQWN0aW9uVHlwZSA9IHRydWU7XG4gICAgICB9XG4gICAgICBvcmlnaW5hbERpc3BhdGNoKC4uLmEpO1xuICAgIH07XG4gIH1cbiAgY29ubmVjdGlvbi5zdWJzY3JpYmUoKG1lc3NhZ2UpID0+IHtcbiAgICB2YXIgX2E7XG4gICAgc3dpdGNoIChtZXNzYWdlLnR5cGUpIHtcbiAgICAgIGNhc2UgXCJBQ1RJT05cIjpcbiAgICAgICAgaWYgKHR5cGVvZiBtZXNzYWdlLnBheWxvYWQgIT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgXCJbenVzdGFuZCBkZXZ0b29scyBtaWRkbGV3YXJlXSBVbnN1cHBvcnRlZCBhY3Rpb24gZm9ybWF0XCJcbiAgICAgICAgICApO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcGFyc2VKc29uVGhlbihcbiAgICAgICAgICBtZXNzYWdlLnBheWxvYWQsXG4gICAgICAgICAgKGFjdGlvbikgPT4ge1xuICAgICAgICAgICAgaWYgKGFjdGlvbi50eXBlID09PSBcIl9fc2V0U3RhdGVcIikge1xuICAgICAgICAgICAgICBpZiAoc3RvcmUgPT09IHZvaWQgMCkge1xuICAgICAgICAgICAgICAgIHNldFN0YXRlRnJvbURldnRvb2xzKGFjdGlvbi5zdGF0ZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIGlmIChPYmplY3Qua2V5cyhhY3Rpb24uc3RhdGUpLmxlbmd0aCAhPT0gMSkge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICAgICAgICBgXG4gICAgICAgICAgICAgICAgICAgIFt6dXN0YW5kIGRldnRvb2xzIG1pZGRsZXdhcmVdIFVuc3VwcG9ydGVkIF9fc2V0U3RhdGUgYWN0aW9uIGZvcm1hdC5cbiAgICAgICAgICAgICAgICAgICAgV2hlbiB1c2luZyAnc3RvcmUnIG9wdGlvbiBpbiBkZXZ0b29scygpLCB0aGUgJ3N0YXRlJyBzaG91bGQgaGF2ZSBvbmx5IG9uZSBrZXksIHdoaWNoIGlzIGEgdmFsdWUgb2YgJ3N0b3JlJyB0aGF0IHdhcyBwYXNzZWQgaW4gZGV2dG9vbHMoKSxcbiAgICAgICAgICAgICAgICAgICAgYW5kIHZhbHVlIG9mIHRoaXMgb25seSBrZXkgc2hvdWxkIGJlIGEgc3RhdGUgb2JqZWN0LiBFeGFtcGxlOiB7IFwidHlwZVwiOiBcIl9fc2V0U3RhdGVcIiwgXCJzdGF0ZVwiOiB7IFwiYWJjMTIzU3RvcmVcIjogeyBcImZvb1wiOiBcImJhclwiIH0gfSB9XG4gICAgICAgICAgICAgICAgICAgIGBcbiAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIGNvbnN0IHN0YXRlRnJvbURldnRvb2xzID0gYWN0aW9uLnN0YXRlW3N0b3JlXTtcbiAgICAgICAgICAgICAgaWYgKHN0YXRlRnJvbURldnRvb2xzID09PSB2b2lkIDAgfHwgc3RhdGVGcm9tRGV2dG9vbHMgPT09IG51bGwpIHtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgaWYgKEpTT04uc3RyaW5naWZ5KGFwaS5nZXRTdGF0ZSgpKSAhPT0gSlNPTi5zdHJpbmdpZnkoc3RhdGVGcm9tRGV2dG9vbHMpKSB7XG4gICAgICAgICAgICAgICAgc2V0U3RhdGVGcm9tRGV2dG9vbHMoc3RhdGVGcm9tRGV2dG9vbHMpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghYXBpLmRpc3BhdGNoRnJvbURldnRvb2xzKSByZXR1cm47XG4gICAgICAgICAgICBpZiAodHlwZW9mIGFwaS5kaXNwYXRjaCAhPT0gXCJmdW5jdGlvblwiKSByZXR1cm47XG4gICAgICAgICAgICBhcGkuZGlzcGF0Y2goYWN0aW9uKTtcbiAgICAgICAgICB9XG4gICAgICAgICk7XG4gICAgICBjYXNlIFwiRElTUEFUQ0hcIjpcbiAgICAgICAgc3dpdGNoIChtZXNzYWdlLnBheWxvYWQudHlwZSkge1xuICAgICAgICAgIGNhc2UgXCJSRVNFVFwiOlxuICAgICAgICAgICAgc2V0U3RhdGVGcm9tRGV2dG9vbHMoaW5pdGlhbFN0YXRlKTtcbiAgICAgICAgICAgIGlmIChzdG9yZSA9PT0gdm9pZCAwKSB7XG4gICAgICAgICAgICAgIHJldHVybiBjb25uZWN0aW9uID09IG51bGwgPyB2b2lkIDAgOiBjb25uZWN0aW9uLmluaXQoYXBpLmdldFN0YXRlKCkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGNvbm5lY3Rpb24gPT0gbnVsbCA/IHZvaWQgMCA6IGNvbm5lY3Rpb24uaW5pdChnZXRUcmFja2VkQ29ubmVjdGlvblN0YXRlKG9wdGlvbnMubmFtZSkpO1xuICAgICAgICAgIGNhc2UgXCJDT01NSVRcIjpcbiAgICAgICAgICAgIGlmIChzdG9yZSA9PT0gdm9pZCAwKSB7XG4gICAgICAgICAgICAgIGNvbm5lY3Rpb24gPT0gbnVsbCA/IHZvaWQgMCA6IGNvbm5lY3Rpb24uaW5pdChhcGkuZ2V0U3RhdGUoKSk7XG4gICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBjb25uZWN0aW9uID09IG51bGwgPyB2b2lkIDAgOiBjb25uZWN0aW9uLmluaXQoZ2V0VHJhY2tlZENvbm5lY3Rpb25TdGF0ZShvcHRpb25zLm5hbWUpKTtcbiAgICAgICAgICBjYXNlIFwiUk9MTEJBQ0tcIjpcbiAgICAgICAgICAgIHJldHVybiBwYXJzZUpzb25UaGVuKG1lc3NhZ2Uuc3RhdGUsIChzdGF0ZSkgPT4ge1xuICAgICAgICAgICAgICBpZiAoc3RvcmUgPT09IHZvaWQgMCkge1xuICAgICAgICAgICAgICAgIHNldFN0YXRlRnJvbURldnRvb2xzKHN0YXRlKTtcbiAgICAgICAgICAgICAgICBjb25uZWN0aW9uID09IG51bGwgPyB2b2lkIDAgOiBjb25uZWN0aW9uLmluaXQoYXBpLmdldFN0YXRlKCkpO1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICBzZXRTdGF0ZUZyb21EZXZ0b29scyhzdGF0ZVtzdG9yZV0pO1xuICAgICAgICAgICAgICBjb25uZWN0aW9uID09IG51bGwgPyB2b2lkIDAgOiBjb25uZWN0aW9uLmluaXQoZ2V0VHJhY2tlZENvbm5lY3Rpb25TdGF0ZShvcHRpb25zLm5hbWUpKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIGNhc2UgXCJKVU1QX1RPX1NUQVRFXCI6XG4gICAgICAgICAgY2FzZSBcIkpVTVBfVE9fQUNUSU9OXCI6XG4gICAgICAgICAgICByZXR1cm4gcGFyc2VKc29uVGhlbihtZXNzYWdlLnN0YXRlLCAoc3RhdGUpID0+IHtcbiAgICAgICAgICAgICAgaWYgKHN0b3JlID09PSB2b2lkIDApIHtcbiAgICAgICAgICAgICAgICBzZXRTdGF0ZUZyb21EZXZ0b29scyhzdGF0ZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIGlmIChKU09OLnN0cmluZ2lmeShhcGkuZ2V0U3RhdGUoKSkgIT09IEpTT04uc3RyaW5naWZ5KHN0YXRlW3N0b3JlXSkpIHtcbiAgICAgICAgICAgICAgICBzZXRTdGF0ZUZyb21EZXZ0b29scyhzdGF0ZVtzdG9yZV0pO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICBjYXNlIFwiSU1QT1JUX1NUQVRFXCI6IHtcbiAgICAgICAgICAgIGNvbnN0IHsgbmV4dExpZnRlZFN0YXRlIH0gPSBtZXNzYWdlLnBheWxvYWQ7XG4gICAgICAgICAgICBjb25zdCBsYXN0Q29tcHV0ZWRTdGF0ZSA9IChfYSA9IG5leHRMaWZ0ZWRTdGF0ZS5jb21wdXRlZFN0YXRlcy5zbGljZSgtMSlbMF0pID09IG51bGwgPyB2b2lkIDAgOiBfYS5zdGF0ZTtcbiAgICAgICAgICAgIGlmICghbGFzdENvbXB1dGVkU3RhdGUpIHJldHVybjtcbiAgICAgICAgICAgIGlmIChzdG9yZSA9PT0gdm9pZCAwKSB7XG4gICAgICAgICAgICAgIHNldFN0YXRlRnJvbURldnRvb2xzKGxhc3RDb21wdXRlZFN0YXRlKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgIHNldFN0YXRlRnJvbURldnRvb2xzKGxhc3RDb21wdXRlZFN0YXRlW3N0b3JlXSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25uZWN0aW9uID09IG51bGwgPyB2b2lkIDAgOiBjb25uZWN0aW9uLnNlbmQoXG4gICAgICAgICAgICAgIG51bGwsXG4gICAgICAgICAgICAgIC8vIEZJWE1FIG5vLWFueVxuICAgICAgICAgICAgICBuZXh0TGlmdGVkU3RhdGVcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgfVxuICAgICAgICAgIGNhc2UgXCJQQVVTRV9SRUNPUkRJTkdcIjpcbiAgICAgICAgICAgIHJldHVybiBpc1JlY29yZGluZyA9ICFpc1JlY29yZGluZztcbiAgICAgICAgfVxuICAgICAgICByZXR1cm47XG4gICAgfVxuICB9KTtcbiAgcmV0dXJuIGluaXRpYWxTdGF0ZTtcbn07XG5jb25zdCBkZXZ0b29scyA9IGRldnRvb2xzSW1wbDtcbmNvbnN0IHBhcnNlSnNvblRoZW4gPSAoc3RyaW5naWZpZWQsIGYpID0+IHtcbiAgbGV0IHBhcnNlZDtcbiAgdHJ5IHtcbiAgICBwYXJzZWQgPSBKU09OLnBhcnNlKHN0cmluZ2lmaWVkKTtcbiAgfSBjYXRjaCAoZSkge1xuICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICBcIlt6dXN0YW5kIGRldnRvb2xzIG1pZGRsZXdhcmVdIENvdWxkIG5vdCBwYXJzZSB0aGUgcmVjZWl2ZWQganNvblwiLFxuICAgICAgZVxuICAgICk7XG4gIH1cbiAgaWYgKHBhcnNlZCAhPT0gdm9pZCAwKSBmKHBhcnNlZCk7XG59O1xuXG5jb25zdCBzdWJzY3JpYmVXaXRoU2VsZWN0b3JJbXBsID0gKGZuKSA9PiAoc2V0LCBnZXQsIGFwaSkgPT4ge1xuICBjb25zdCBvcmlnU3Vic2NyaWJlID0gYXBpLnN1YnNjcmliZTtcbiAgYXBpLnN1YnNjcmliZSA9IChzZWxlY3Rvciwgb3B0TGlzdGVuZXIsIG9wdGlvbnMpID0+IHtcbiAgICBsZXQgbGlzdGVuZXIgPSBzZWxlY3RvcjtcbiAgICBpZiAob3B0TGlzdGVuZXIpIHtcbiAgICAgIGNvbnN0IGVxdWFsaXR5Rm4gPSAob3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogb3B0aW9ucy5lcXVhbGl0eUZuKSB8fCBPYmplY3QuaXM7XG4gICAgICBsZXQgY3VycmVudFNsaWNlID0gc2VsZWN0b3IoYXBpLmdldFN0YXRlKCkpO1xuICAgICAgbGlzdGVuZXIgPSAoc3RhdGUpID0+IHtcbiAgICAgICAgY29uc3QgbmV4dFNsaWNlID0gc2VsZWN0b3Ioc3RhdGUpO1xuICAgICAgICBpZiAoIWVxdWFsaXR5Rm4oY3VycmVudFNsaWNlLCBuZXh0U2xpY2UpKSB7XG4gICAgICAgICAgY29uc3QgcHJldmlvdXNTbGljZSA9IGN1cnJlbnRTbGljZTtcbiAgICAgICAgICBvcHRMaXN0ZW5lcihjdXJyZW50U2xpY2UgPSBuZXh0U2xpY2UsIHByZXZpb3VzU2xpY2UpO1xuICAgICAgICB9XG4gICAgICB9O1xuICAgICAgaWYgKG9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IG9wdGlvbnMuZmlyZUltbWVkaWF0ZWx5KSB7XG4gICAgICAgIG9wdExpc3RlbmVyKGN1cnJlbnRTbGljZSwgY3VycmVudFNsaWNlKTtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIG9yaWdTdWJzY3JpYmUobGlzdGVuZXIpO1xuICB9O1xuICBjb25zdCBpbml0aWFsU3RhdGUgPSBmbihzZXQsIGdldCwgYXBpKTtcbiAgcmV0dXJuIGluaXRpYWxTdGF0ZTtcbn07XG5jb25zdCBzdWJzY3JpYmVXaXRoU2VsZWN0b3IgPSBzdWJzY3JpYmVXaXRoU2VsZWN0b3JJbXBsO1xuXG5jb25zdCBjb21iaW5lID0gKGluaXRpYWxTdGF0ZSwgY3JlYXRlKSA9PiAoLi4uYSkgPT4gT2JqZWN0LmFzc2lnbih7fSwgaW5pdGlhbFN0YXRlLCBjcmVhdGUoLi4uYSkpO1xuXG5mdW5jdGlvbiBjcmVhdGVKU09OU3RvcmFnZShnZXRTdG9yYWdlLCBvcHRpb25zKSB7XG4gIGxldCBzdG9yYWdlO1xuICB0cnkge1xuICAgIHN0b3JhZ2UgPSBnZXRTdG9yYWdlKCk7XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICByZXR1cm47XG4gIH1cbiAgY29uc3QgcGVyc2lzdFN0b3JhZ2UgPSB7XG4gICAgZ2V0SXRlbTogKG5hbWUpID0+IHtcbiAgICAgIHZhciBfYTtcbiAgICAgIGNvbnN0IHBhcnNlID0gKHN0cjIpID0+IHtcbiAgICAgICAgaWYgKHN0cjIgPT09IG51bGwpIHtcbiAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gSlNPTi5wYXJzZShzdHIyLCBvcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiBvcHRpb25zLnJldml2ZXIpO1xuICAgICAgfTtcbiAgICAgIGNvbnN0IHN0ciA9IChfYSA9IHN0b3JhZ2UuZ2V0SXRlbShuYW1lKSkgIT0gbnVsbCA/IF9hIDogbnVsbDtcbiAgICAgIGlmIChzdHIgaW5zdGFuY2VvZiBQcm9taXNlKSB7XG4gICAgICAgIHJldHVybiBzdHIudGhlbihwYXJzZSk7XG4gICAgICB9XG4gICAgICByZXR1cm4gcGFyc2Uoc3RyKTtcbiAgICB9LFxuICAgIHNldEl0ZW06IChuYW1lLCBuZXdWYWx1ZSkgPT4gc3RvcmFnZS5zZXRJdGVtKFxuICAgICAgbmFtZSxcbiAgICAgIEpTT04uc3RyaW5naWZ5KG5ld1ZhbHVlLCBvcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiBvcHRpb25zLnJlcGxhY2VyKVxuICAgICksXG4gICAgcmVtb3ZlSXRlbTogKG5hbWUpID0+IHN0b3JhZ2UucmVtb3ZlSXRlbShuYW1lKVxuICB9O1xuICByZXR1cm4gcGVyc2lzdFN0b3JhZ2U7XG59XG5jb25zdCB0b1RoZW5hYmxlID0gKGZuKSA9PiAoaW5wdXQpID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCByZXN1bHQgPSBmbihpbnB1dCk7XG4gICAgaWYgKHJlc3VsdCBpbnN0YW5jZW9mIFByb21pc2UpIHtcbiAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfVxuICAgIHJldHVybiB7XG4gICAgICB0aGVuKG9uRnVsZmlsbGVkKSB7XG4gICAgICAgIHJldHVybiB0b1RoZW5hYmxlKG9uRnVsZmlsbGVkKShyZXN1bHQpO1xuICAgICAgfSxcbiAgICAgIGNhdGNoKF9vblJlamVjdGVkKSB7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgICAgfVxuICAgIH07XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICByZXR1cm4ge1xuICAgICAgdGhlbihfb25GdWxmaWxsZWQpIHtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgICB9LFxuICAgICAgY2F0Y2gob25SZWplY3RlZCkge1xuICAgICAgICByZXR1cm4gdG9UaGVuYWJsZShvblJlamVjdGVkKShlKTtcbiAgICAgIH1cbiAgICB9O1xuICB9XG59O1xuY29uc3QgcGVyc2lzdEltcGwgPSAoY29uZmlnLCBiYXNlT3B0aW9ucykgPT4gKHNldCwgZ2V0LCBhcGkpID0+IHtcbiAgbGV0IG9wdGlvbnMgPSB7XG4gICAgc3RvcmFnZTogY3JlYXRlSlNPTlN0b3JhZ2UoKCkgPT4gbG9jYWxTdG9yYWdlKSxcbiAgICBwYXJ0aWFsaXplOiAoc3RhdGUpID0+IHN0YXRlLFxuICAgIHZlcnNpb246IDAsXG4gICAgbWVyZ2U6IChwZXJzaXN0ZWRTdGF0ZSwgY3VycmVudFN0YXRlKSA9PiAoe1xuICAgICAgLi4uY3VycmVudFN0YXRlLFxuICAgICAgLi4ucGVyc2lzdGVkU3RhdGVcbiAgICB9KSxcbiAgICAuLi5iYXNlT3B0aW9uc1xuICB9O1xuICBsZXQgaGFzSHlkcmF0ZWQgPSBmYWxzZTtcbiAgY29uc3QgaHlkcmF0aW9uTGlzdGVuZXJzID0gLyogQF9fUFVSRV9fICovIG5ldyBTZXQoKTtcbiAgY29uc3QgZmluaXNoSHlkcmF0aW9uTGlzdGVuZXJzID0gLyogQF9fUFVSRV9fICovIG5ldyBTZXQoKTtcbiAgbGV0IHN0b3JhZ2UgPSBvcHRpb25zLnN0b3JhZ2U7XG4gIGlmICghc3RvcmFnZSkge1xuICAgIHJldHVybiBjb25maWcoXG4gICAgICAoLi4uYXJncykgPT4ge1xuICAgICAgICBjb25zb2xlLndhcm4oXG4gICAgICAgICAgYFt6dXN0YW5kIHBlcnNpc3QgbWlkZGxld2FyZV0gVW5hYmxlIHRvIHVwZGF0ZSBpdGVtICcke29wdGlvbnMubmFtZX0nLCB0aGUgZ2l2ZW4gc3RvcmFnZSBpcyBjdXJyZW50bHkgdW5hdmFpbGFibGUuYFxuICAgICAgICApO1xuICAgICAgICBzZXQoLi4uYXJncyk7XG4gICAgICB9LFxuICAgICAgZ2V0LFxuICAgICAgYXBpXG4gICAgKTtcbiAgfVxuICBjb25zdCBzZXRJdGVtID0gKCkgPT4ge1xuICAgIGNvbnN0IHN0YXRlID0gb3B0aW9ucy5wYXJ0aWFsaXplKHsgLi4uZ2V0KCkgfSk7XG4gICAgcmV0dXJuIHN0b3JhZ2Uuc2V0SXRlbShvcHRpb25zLm5hbWUsIHtcbiAgICAgIHN0YXRlLFxuICAgICAgdmVyc2lvbjogb3B0aW9ucy52ZXJzaW9uXG4gICAgfSk7XG4gIH07XG4gIGNvbnN0IHNhdmVkU2V0U3RhdGUgPSBhcGkuc2V0U3RhdGU7XG4gIGFwaS5zZXRTdGF0ZSA9IChzdGF0ZSwgcmVwbGFjZSkgPT4ge1xuICAgIHNhdmVkU2V0U3RhdGUoc3RhdGUsIHJlcGxhY2UpO1xuICAgIHZvaWQgc2V0SXRlbSgpO1xuICB9O1xuICBjb25zdCBjb25maWdSZXN1bHQgPSBjb25maWcoXG4gICAgKC4uLmFyZ3MpID0+IHtcbiAgICAgIHNldCguLi5hcmdzKTtcbiAgICAgIHZvaWQgc2V0SXRlbSgpO1xuICAgIH0sXG4gICAgZ2V0LFxuICAgIGFwaVxuICApO1xuICBhcGkuZ2V0SW5pdGlhbFN0YXRlID0gKCkgPT4gY29uZmlnUmVzdWx0O1xuICBsZXQgc3RhdGVGcm9tU3RvcmFnZTtcbiAgY29uc3QgaHlkcmF0ZSA9ICgpID0+IHtcbiAgICB2YXIgX2EsIF9iO1xuICAgIGlmICghc3RvcmFnZSkgcmV0dXJuO1xuICAgIGhhc0h5ZHJhdGVkID0gZmFsc2U7XG4gICAgaHlkcmF0aW9uTGlzdGVuZXJzLmZvckVhY2goKGNiKSA9PiB7XG4gICAgICB2YXIgX2EyO1xuICAgICAgcmV0dXJuIGNiKChfYTIgPSBnZXQoKSkgIT0gbnVsbCA/IF9hMiA6IGNvbmZpZ1Jlc3VsdCk7XG4gICAgfSk7XG4gICAgY29uc3QgcG9zdFJlaHlkcmF0aW9uQ2FsbGJhY2sgPSAoKF9iID0gb3B0aW9ucy5vblJlaHlkcmF0ZVN0b3JhZ2UpID09IG51bGwgPyB2b2lkIDAgOiBfYi5jYWxsKG9wdGlvbnMsIChfYSA9IGdldCgpKSAhPSBudWxsID8gX2EgOiBjb25maWdSZXN1bHQpKSB8fCB2b2lkIDA7XG4gICAgcmV0dXJuIHRvVGhlbmFibGUoc3RvcmFnZS5nZXRJdGVtLmJpbmQoc3RvcmFnZSkpKG9wdGlvbnMubmFtZSkudGhlbigoZGVzZXJpYWxpemVkU3RvcmFnZVZhbHVlKSA9PiB7XG4gICAgICBpZiAoZGVzZXJpYWxpemVkU3RvcmFnZVZhbHVlKSB7XG4gICAgICAgIGlmICh0eXBlb2YgZGVzZXJpYWxpemVkU3RvcmFnZVZhbHVlLnZlcnNpb24gPT09IFwibnVtYmVyXCIgJiYgZGVzZXJpYWxpemVkU3RvcmFnZVZhbHVlLnZlcnNpb24gIT09IG9wdGlvbnMudmVyc2lvbikge1xuICAgICAgICAgIGlmIChvcHRpb25zLm1pZ3JhdGUpIHtcbiAgICAgICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICAgIHRydWUsXG4gICAgICAgICAgICAgIG9wdGlvbnMubWlncmF0ZShcbiAgICAgICAgICAgICAgICBkZXNlcmlhbGl6ZWRTdG9yYWdlVmFsdWUuc3RhdGUsXG4gICAgICAgICAgICAgICAgZGVzZXJpYWxpemVkU3RvcmFnZVZhbHVlLnZlcnNpb25cbiAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgXTtcbiAgICAgICAgICB9XG4gICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgIGBTdGF0ZSBsb2FkZWQgZnJvbSBzdG9yYWdlIGNvdWxkbid0IGJlIG1pZ3JhdGVkIHNpbmNlIG5vIG1pZ3JhdGUgZnVuY3Rpb24gd2FzIHByb3ZpZGVkYFxuICAgICAgICAgICk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcmV0dXJuIFtmYWxzZSwgZGVzZXJpYWxpemVkU3RvcmFnZVZhbHVlLnN0YXRlXTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgcmV0dXJuIFtmYWxzZSwgdm9pZCAwXTtcbiAgICB9KS50aGVuKChtaWdyYXRpb25SZXN1bHQpID0+IHtcbiAgICAgIHZhciBfYTI7XG4gICAgICBjb25zdCBbbWlncmF0ZWQsIG1pZ3JhdGVkU3RhdGVdID0gbWlncmF0aW9uUmVzdWx0O1xuICAgICAgc3RhdGVGcm9tU3RvcmFnZSA9IG9wdGlvbnMubWVyZ2UoXG4gICAgICAgIG1pZ3JhdGVkU3RhdGUsXG4gICAgICAgIChfYTIgPSBnZXQoKSkgIT0gbnVsbCA/IF9hMiA6IGNvbmZpZ1Jlc3VsdFxuICAgICAgKTtcbiAgICAgIHNldChzdGF0ZUZyb21TdG9yYWdlLCB0cnVlKTtcbiAgICAgIGlmIChtaWdyYXRlZCkge1xuICAgICAgICByZXR1cm4gc2V0SXRlbSgpO1xuICAgICAgfVxuICAgIH0pLnRoZW4oKCkgPT4ge1xuICAgICAgcG9zdFJlaHlkcmF0aW9uQ2FsbGJhY2sgPT0gbnVsbCA/IHZvaWQgMCA6IHBvc3RSZWh5ZHJhdGlvbkNhbGxiYWNrKHN0YXRlRnJvbVN0b3JhZ2UsIHZvaWQgMCk7XG4gICAgICBzdGF0ZUZyb21TdG9yYWdlID0gZ2V0KCk7XG4gICAgICBoYXNIeWRyYXRlZCA9IHRydWU7XG4gICAgICBmaW5pc2hIeWRyYXRpb25MaXN0ZW5lcnMuZm9yRWFjaCgoY2IpID0+IGNiKHN0YXRlRnJvbVN0b3JhZ2UpKTtcbiAgICB9KS5jYXRjaCgoZSkgPT4ge1xuICAgICAgcG9zdFJlaHlkcmF0aW9uQ2FsbGJhY2sgPT0gbnVsbCA/IHZvaWQgMCA6IHBvc3RSZWh5ZHJhdGlvbkNhbGxiYWNrKHZvaWQgMCwgZSk7XG4gICAgfSk7XG4gIH07XG4gIGFwaS5wZXJzaXN0ID0ge1xuICAgIHNldE9wdGlvbnM6IChuZXdPcHRpb25zKSA9PiB7XG4gICAgICBvcHRpb25zID0ge1xuICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICAuLi5uZXdPcHRpb25zXG4gICAgICB9O1xuICAgICAgaWYgKG5ld09wdGlvbnMuc3RvcmFnZSkge1xuICAgICAgICBzdG9yYWdlID0gbmV3T3B0aW9ucy5zdG9yYWdlO1xuICAgICAgfVxuICAgIH0sXG4gICAgY2xlYXJTdG9yYWdlOiAoKSA9PiB7XG4gICAgICBzdG9yYWdlID09IG51bGwgPyB2b2lkIDAgOiBzdG9yYWdlLnJlbW92ZUl0ZW0ob3B0aW9ucy5uYW1lKTtcbiAgICB9LFxuICAgIGdldE9wdGlvbnM6ICgpID0+IG9wdGlvbnMsXG4gICAgcmVoeWRyYXRlOiAoKSA9PiBoeWRyYXRlKCksXG4gICAgaGFzSHlkcmF0ZWQ6ICgpID0+IGhhc0h5ZHJhdGVkLFxuICAgIG9uSHlkcmF0ZTogKGNiKSA9PiB7XG4gICAgICBoeWRyYXRpb25MaXN0ZW5lcnMuYWRkKGNiKTtcbiAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGh5ZHJhdGlvbkxpc3RlbmVycy5kZWxldGUoY2IpO1xuICAgICAgfTtcbiAgICB9LFxuICAgIG9uRmluaXNoSHlkcmF0aW9uOiAoY2IpID0+IHtcbiAgICAgIGZpbmlzaEh5ZHJhdGlvbkxpc3RlbmVycy5hZGQoY2IpO1xuICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgZmluaXNoSHlkcmF0aW9uTGlzdGVuZXJzLmRlbGV0ZShjYik7XG4gICAgICB9O1xuICAgIH1cbiAgfTtcbiAgaWYgKCFvcHRpb25zLnNraXBIeWRyYXRpb24pIHtcbiAgICBoeWRyYXRlKCk7XG4gIH1cbiAgcmV0dXJuIHN0YXRlRnJvbVN0b3JhZ2UgfHwgY29uZmlnUmVzdWx0O1xufTtcbmNvbnN0IHBlcnNpc3QgPSBwZXJzaXN0SW1wbDtcblxuZXhwb3J0IHsgY29tYmluZSwgY3JlYXRlSlNPTlN0b3JhZ2UsIGRldnRvb2xzLCBwZXJzaXN0LCByZWR1eCwgc3Vic2NyaWJlV2l0aFNlbGVjdG9yIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/node_modules/zustand/esm/middleware.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/node_modules/zustand/esm/vanilla.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@wagmi/core/node_modules/zustand/esm/vanilla.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStore: () => (/* binding */ createStore)\n/* harmony export */ });\nconst createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvbm9kZV9tb2R1bGVzL3p1c3RhbmQvZXNtL3ZhbmlsbGEubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhIQUE4SDtBQUM5SDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7O0FBRXVCIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL25vZGVfbW9kdWxlcy96dXN0YW5kL2VzbS92YW5pbGxhLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBjcmVhdGVTdG9yZUltcGwgPSAoY3JlYXRlU3RhdGUpID0+IHtcbiAgbGV0IHN0YXRlO1xuICBjb25zdCBsaXN0ZW5lcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpO1xuICBjb25zdCBzZXRTdGF0ZSA9IChwYXJ0aWFsLCByZXBsYWNlKSA9PiB7XG4gICAgY29uc3QgbmV4dFN0YXRlID0gdHlwZW9mIHBhcnRpYWwgPT09IFwiZnVuY3Rpb25cIiA/IHBhcnRpYWwoc3RhdGUpIDogcGFydGlhbDtcbiAgICBpZiAoIU9iamVjdC5pcyhuZXh0U3RhdGUsIHN0YXRlKSkge1xuICAgICAgY29uc3QgcHJldmlvdXNTdGF0ZSA9IHN0YXRlO1xuICAgICAgc3RhdGUgPSAocmVwbGFjZSAhPSBudWxsID8gcmVwbGFjZSA6IHR5cGVvZiBuZXh0U3RhdGUgIT09IFwib2JqZWN0XCIgfHwgbmV4dFN0YXRlID09PSBudWxsKSA/IG5leHRTdGF0ZSA6IE9iamVjdC5hc3NpZ24oe30sIHN0YXRlLCBuZXh0U3RhdGUpO1xuICAgICAgbGlzdGVuZXJzLmZvckVhY2goKGxpc3RlbmVyKSA9PiBsaXN0ZW5lcihzdGF0ZSwgcHJldmlvdXNTdGF0ZSkpO1xuICAgIH1cbiAgfTtcbiAgY29uc3QgZ2V0U3RhdGUgPSAoKSA9PiBzdGF0ZTtcbiAgY29uc3QgZ2V0SW5pdGlhbFN0YXRlID0gKCkgPT4gaW5pdGlhbFN0YXRlO1xuICBjb25zdCBzdWJzY3JpYmUgPSAobGlzdGVuZXIpID0+IHtcbiAgICBsaXN0ZW5lcnMuYWRkKGxpc3RlbmVyKTtcbiAgICByZXR1cm4gKCkgPT4gbGlzdGVuZXJzLmRlbGV0ZShsaXN0ZW5lcik7XG4gIH07XG4gIGNvbnN0IGFwaSA9IHsgc2V0U3RhdGUsIGdldFN0YXRlLCBnZXRJbml0aWFsU3RhdGUsIHN1YnNjcmliZSB9O1xuICBjb25zdCBpbml0aWFsU3RhdGUgPSBzdGF0ZSA9IGNyZWF0ZVN0YXRlKHNldFN0YXRlLCBnZXRTdGF0ZSwgYXBpKTtcbiAgcmV0dXJuIGFwaTtcbn07XG5jb25zdCBjcmVhdGVTdG9yZSA9IChjcmVhdGVTdGF0ZSkgPT4gY3JlYXRlU3RhdGUgPyBjcmVhdGVTdG9yZUltcGwoY3JlYXRlU3RhdGUpIDogY3JlYXRlU3RvcmVJbXBsO1xuXG5leHBvcnQgeyBjcmVhdGVTdG9yZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/node_modules/zustand/esm/vanilla.mjs\n");

/***/ })

};
;