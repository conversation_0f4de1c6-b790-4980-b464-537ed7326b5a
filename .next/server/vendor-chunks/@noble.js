"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@noble";
exports.ids = ["vendor-chunks/@noble"];
exports.modules = {

/***/ "(ssr)/./node_modules/@noble/hashes/esm/_u64.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/_u64.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   add3H: () => (/* binding */ add3H),\n/* harmony export */   add3L: () => (/* binding */ add3L),\n/* harmony export */   add4H: () => (/* binding */ add4H),\n/* harmony export */   add4L: () => (/* binding */ add4L),\n/* harmony export */   add5H: () => (/* binding */ add5H),\n/* harmony export */   add5L: () => (/* binding */ add5L),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fromBig: () => (/* binding */ fromBig),\n/* harmony export */   rotlBH: () => (/* binding */ rotlBH),\n/* harmony export */   rotlBL: () => (/* binding */ rotlBL),\n/* harmony export */   rotlSH: () => (/* binding */ rotlSH),\n/* harmony export */   rotlSL: () => (/* binding */ rotlSL),\n/* harmony export */   rotr32H: () => (/* binding */ rotr32H),\n/* harmony export */   rotr32L: () => (/* binding */ rotr32L),\n/* harmony export */   rotrBH: () => (/* binding */ rotrBH),\n/* harmony export */   rotrBL: () => (/* binding */ rotrBL),\n/* harmony export */   rotrSH: () => (/* binding */ rotrSH),\n/* harmony export */   rotrSL: () => (/* binding */ rotrSL),\n/* harmony export */   shrSH: () => (/* binding */ shrSH),\n/* harmony export */   shrSL: () => (/* binding */ shrSL),\n/* harmony export */   split: () => (/* binding */ split),\n/* harmony export */   toBig: () => (/* binding */ toBig)\n/* harmony export */ });\n/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\nfunction fromBig(n, le = false) {\n    if (le)\n        return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n    return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\nfunction split(lst, le = false) {\n    const len = lst.length;\n    let Ah = new Uint32Array(len);\n    let Al = new Uint32Array(len);\n    for (let i = 0; i < len; i++) {\n        const { h, l } = fromBig(lst[i], le);\n        [Ah[i], Al[i]] = [h, l];\n    }\n    return [Ah, Al];\n}\nconst toBig = (h, l) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nconst shrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h, l, s) => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nconst rotr32L = (h, _l) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h, l, s) => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h, l, s) => (h << (s - 32)) | (l >>> (64 - s));\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n    const l = (Al >>> 0) + (Bl >>> 0);\n    return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low, Ah, Bh, Ch) => (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low, Ah, Bh, Ch, Dh) => (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n// prettier-ignore\n\n// prettier-ignore\nconst u64 = {\n    fromBig, split, toBig,\n    shrSH, shrSL,\n    rotrSH, rotrSL, rotrBH, rotrBL,\n    rotr32H, rotr32L,\n    rotlSH, rotlSL, rotlBH, rotlBL,\n    add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (u64);\n//# sourceMappingURL=_u64.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/_u64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/cryptoNode.js":
/*!******************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/cryptoNode.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   crypto: () => (/* binding */ crypto)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/**\n * Internal webcrypto alias.\n * We prefer WebCrypto aka globalThis.crypto, which exists in node.js 16+.\n * Falls back to Node.js built-in crypto for Node.js <=v14.\n * See utils.ts for details.\n * @module\n */\n// @ts-ignore\n\nconst crypto = /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) && typeof /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) === 'object' && \"webcrypto\" in /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2)))\n    ? node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto\n    : /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) && typeof /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) === 'object' && \"randomBytes\" in /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2)))\n        ? /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2)))\n        : undefined;\n//# sourceMappingURL=cryptoNode.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9lc20vY3J5cHRvTm9kZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2tDO0FBQzNCLGVBQWUsMk1BQUUsV0FBVywyTUFBRSxpQkFBaUIsME5BQWlCO0FBQ3ZFLE1BQU0sa0RBQVk7QUFDbEIsTUFBTSwyTUFBRSxXQUFXLDJNQUFFLGlCQUFpQiw0TkFBbUI7QUFDekQsVUFBVSwyTUFBRTtBQUNaO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9lc20vY3J5cHRvTm9kZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEludGVybmFsIHdlYmNyeXB0byBhbGlhcy5cbiAqIFdlIHByZWZlciBXZWJDcnlwdG8gYWthIGdsb2JhbFRoaXMuY3J5cHRvLCB3aGljaCBleGlzdHMgaW4gbm9kZS5qcyAxNisuXG4gKiBGYWxscyBiYWNrIHRvIE5vZGUuanMgYnVpbHQtaW4gY3J5cHRvIGZvciBOb2RlLmpzIDw9djE0LlxuICogU2VlIHV0aWxzLnRzIGZvciBkZXRhaWxzLlxuICogQG1vZHVsZVxuICovXG4vLyBAdHMtaWdub3JlXG5pbXBvcnQgKiBhcyBuYyBmcm9tICdub2RlOmNyeXB0byc7XG5leHBvcnQgY29uc3QgY3J5cHRvID0gbmMgJiYgdHlwZW9mIG5jID09PSAnb2JqZWN0JyAmJiAnd2ViY3J5cHRvJyBpbiBuY1xuICAgID8gbmMud2ViY3J5cHRvXG4gICAgOiBuYyAmJiB0eXBlb2YgbmMgPT09ICdvYmplY3QnICYmICdyYW5kb21CeXRlcycgaW4gbmNcbiAgICAgICAgPyBuY1xuICAgICAgICA6IHVuZGVmaW5lZDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNyeXB0b05vZGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/cryptoNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/sha3.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/sha3.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Keccak: () => (/* binding */ Keccak),\n/* harmony export */   keccakP: () => (/* binding */ keccakP),\n/* harmony export */   keccak_224: () => (/* binding */ keccak_224),\n/* harmony export */   keccak_256: () => (/* binding */ keccak_256),\n/* harmony export */   keccak_384: () => (/* binding */ keccak_384),\n/* harmony export */   keccak_512: () => (/* binding */ keccak_512),\n/* harmony export */   sha3_224: () => (/* binding */ sha3_224),\n/* harmony export */   sha3_256: () => (/* binding */ sha3_256),\n/* harmony export */   sha3_384: () => (/* binding */ sha3_384),\n/* harmony export */   sha3_512: () => (/* binding */ sha3_512),\n/* harmony export */   shake128: () => (/* binding */ shake128),\n/* harmony export */   shake256: () => (/* binding */ shake256)\n/* harmony export */ });\n/* harmony import */ var _u64_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_u64.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_u64.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n/**\n * SHA3 (keccak) hash function, based on a new \"Sponge function\" design.\n * Different from older hashes, the internal state is bigger than output size.\n *\n * Check out [FIPS-202](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.202.pdf),\n * [Website](https://keccak.team/keccak.html),\n * [the differences between SHA-3 and Keccak](https://crypto.stackexchange.com/questions/15727/what-are-the-key-differences-between-the-draft-sha-3-standard-and-the-keccak-sub).\n *\n * Check out `sha3-addons` module for cSHAKE, k12, and others.\n * @module\n */\n\n// prettier-ignore\n\n// No __PURE__ annotations in sha3 header:\n// EVERYTHING is in fact used on every export.\n// Various per round constants calculations\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst _7n = BigInt(7);\nconst _256n = BigInt(256);\nconst _0x71n = BigInt(0x71);\nconst SHA3_PI = [];\nconst SHA3_ROTL = [];\nconst _SHA3_IOTA = [];\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n    // Pi\n    [x, y] = [y, (2 * x + 3 * y) % 5];\n    SHA3_PI.push(2 * (5 * y + x));\n    // Rotational\n    SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);\n    // Iota\n    let t = _0n;\n    for (let j = 0; j < 7; j++) {\n        R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;\n        if (R & _2n)\n            t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);\n    }\n    _SHA3_IOTA.push(t);\n}\nconst IOTAS = (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.split)(_SHA3_IOTA, true);\nconst SHA3_IOTA_H = IOTAS[0];\nconst SHA3_IOTA_L = IOTAS[1];\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h, l, s) => (s > 32 ? (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlBH)(h, l, s) : (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlSH)(h, l, s));\nconst rotlL = (h, l, s) => (s > 32 ? (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlBL)(h, l, s) : (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlSL)(h, l, s));\n/** `keccakf1600` internal function, additionally allows to adjust round count. */\nfunction keccakP(s, rounds = 24) {\n    const B = new Uint32Array(5 * 2);\n    // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n    for (let round = 24 - rounds; round < 24; round++) {\n        // Theta θ\n        for (let x = 0; x < 10; x++)\n            B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n        for (let x = 0; x < 10; x += 2) {\n            const idx1 = (x + 8) % 10;\n            const idx0 = (x + 2) % 10;\n            const B0 = B[idx0];\n            const B1 = B[idx0 + 1];\n            const Th = rotlH(B0, B1, 1) ^ B[idx1];\n            const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n            for (let y = 0; y < 50; y += 10) {\n                s[x + y] ^= Th;\n                s[x + y + 1] ^= Tl;\n            }\n        }\n        // Rho (ρ) and Pi (π)\n        let curH = s[2];\n        let curL = s[3];\n        for (let t = 0; t < 24; t++) {\n            const shift = SHA3_ROTL[t];\n            const Th = rotlH(curH, curL, shift);\n            const Tl = rotlL(curH, curL, shift);\n            const PI = SHA3_PI[t];\n            curH = s[PI];\n            curL = s[PI + 1];\n            s[PI] = Th;\n            s[PI + 1] = Tl;\n        }\n        // Chi (χ)\n        for (let y = 0; y < 50; y += 10) {\n            for (let x = 0; x < 10; x++)\n                B[x] = s[y + x];\n            for (let x = 0; x < 10; x++)\n                s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n        }\n        // Iota (ι)\n        s[0] ^= SHA3_IOTA_H[round];\n        s[1] ^= SHA3_IOTA_L[round];\n    }\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(B);\n}\n/** Keccak sponge function. */\nclass Keccak extends _utils_js__WEBPACK_IMPORTED_MODULE_1__.Hash {\n    // NOTE: we accept arguments in bytes instead of bits here.\n    constructor(blockLen, suffix, outputLen, enableXOF = false, rounds = 24) {\n        super();\n        this.pos = 0;\n        this.posOut = 0;\n        this.finished = false;\n        this.destroyed = false;\n        this.enableXOF = false;\n        this.blockLen = blockLen;\n        this.suffix = suffix;\n        this.outputLen = outputLen;\n        this.enableXOF = enableXOF;\n        this.rounds = rounds;\n        // Can be passed from user as dkLen\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.anumber)(outputLen);\n        // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n        // 0 < blockLen < 200\n        if (!(0 < blockLen && blockLen < 200))\n            throw new Error('only keccak-f1600 function is supported');\n        this.state = new Uint8Array(200);\n        this.state32 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.u32)(this.state);\n    }\n    clone() {\n        return this._cloneInto();\n    }\n    keccak() {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap32IfBE)(this.state32);\n        keccakP(this.state32, this.rounds);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.swap32IfBE)(this.state32);\n        this.posOut = 0;\n        this.pos = 0;\n    }\n    update(data) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this);\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.toBytes)(data);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.abytes)(data);\n        const { blockLen, state } = this;\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            for (let i = 0; i < take; i++)\n                state[this.pos++] ^= data[pos++];\n            if (this.pos === blockLen)\n                this.keccak();\n        }\n        return this;\n    }\n    finish() {\n        if (this.finished)\n            return;\n        this.finished = true;\n        const { state, suffix, pos, blockLen } = this;\n        // Do the padding\n        state[pos] ^= suffix;\n        if ((suffix & 0x80) !== 0 && pos === blockLen - 1)\n            this.keccak();\n        state[blockLen - 1] ^= 0x80;\n        this.keccak();\n    }\n    writeInto(out) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.aexists)(this, false);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.abytes)(out);\n        this.finish();\n        const bufferOut = this.state;\n        const { blockLen } = this;\n        for (let pos = 0, len = out.length; pos < len;) {\n            if (this.posOut >= blockLen)\n                this.keccak();\n            const take = Math.min(blockLen - this.posOut, len - pos);\n            out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n            this.posOut += take;\n            pos += take;\n        }\n        return out;\n    }\n    xofInto(out) {\n        // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n        if (!this.enableXOF)\n            throw new Error('XOF is not possible for this instance');\n        return this.writeInto(out);\n    }\n    xof(bytes) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.anumber)(bytes);\n        return this.xofInto(new Uint8Array(bytes));\n    }\n    digestInto(out) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.aoutput)(out, this);\n        if (this.finished)\n            throw new Error('digest() was already called');\n        this.writeInto(out);\n        this.destroy();\n        return out;\n    }\n    digest() {\n        return this.digestInto(new Uint8Array(this.outputLen));\n    }\n    destroy() {\n        this.destroyed = true;\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(this.state);\n    }\n    _cloneInto(to) {\n        const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n        to || (to = new Keccak(blockLen, suffix, outputLen, enableXOF, rounds));\n        to.state32.set(this.state32);\n        to.pos = this.pos;\n        to.posOut = this.posOut;\n        to.finished = this.finished;\n        to.rounds = rounds;\n        // Suffix can change in cSHAKE\n        to.suffix = suffix;\n        to.outputLen = outputLen;\n        to.enableXOF = enableXOF;\n        to.destroyed = this.destroyed;\n        return to;\n    }\n}\nconst gen = (suffix, blockLen, outputLen) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new Keccak(blockLen, suffix, outputLen));\n/** SHA3-224 hash function. */\nconst sha3_224 = /* @__PURE__ */ (() => gen(0x06, 144, 224 / 8))();\n/** SHA3-256 hash function. Different from keccak-256. */\nconst sha3_256 = /* @__PURE__ */ (() => gen(0x06, 136, 256 / 8))();\n/** SHA3-384 hash function. */\nconst sha3_384 = /* @__PURE__ */ (() => gen(0x06, 104, 384 / 8))();\n/** SHA3-512 hash function. */\nconst sha3_512 = /* @__PURE__ */ (() => gen(0x06, 72, 512 / 8))();\n/** keccak-224 hash function. */\nconst keccak_224 = /* @__PURE__ */ (() => gen(0x01, 144, 224 / 8))();\n/** keccak-256 hash function. Different from SHA3-256. */\nconst keccak_256 = /* @__PURE__ */ (() => gen(0x01, 136, 256 / 8))();\n/** keccak-384 hash function. */\nconst keccak_384 = /* @__PURE__ */ (() => gen(0x01, 104, 384 / 8))();\n/** keccak-512 hash function. */\nconst keccak_512 = /* @__PURE__ */ (() => gen(0x01, 72, 512 / 8))();\nconst genShake = (suffix, blockLen, outputLen) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createXOFer)((opts = {}) => new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true));\n/** SHAKE128 XOF with 128-bit security. */\nconst shake128 = /* @__PURE__ */ (() => genShake(0x1f, 168, 128 / 8))();\n/** SHAKE256 XOF with 256-bit security. */\nconst shake256 = /* @__PURE__ */ (() => genShake(0x1f, 136, 256 / 8))();\n//# sourceMappingURL=sha3.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/sha3.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/utils.js":
/*!*************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/utils.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hash: () => (/* binding */ Hash),\n/* harmony export */   abytes: () => (/* binding */ abytes),\n/* harmony export */   aexists: () => (/* binding */ aexists),\n/* harmony export */   ahash: () => (/* binding */ ahash),\n/* harmony export */   anumber: () => (/* binding */ anumber),\n/* harmony export */   aoutput: () => (/* binding */ aoutput),\n/* harmony export */   asyncLoop: () => (/* binding */ asyncLoop),\n/* harmony export */   byteSwap: () => (/* binding */ byteSwap),\n/* harmony export */   byteSwap32: () => (/* binding */ byteSwap32),\n/* harmony export */   byteSwapIfBE: () => (/* binding */ byteSwapIfBE),\n/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),\n/* harmony export */   bytesToUtf8: () => (/* binding */ bytesToUtf8),\n/* harmony export */   checkOpts: () => (/* binding */ checkOpts),\n/* harmony export */   clean: () => (/* binding */ clean),\n/* harmony export */   concatBytes: () => (/* binding */ concatBytes),\n/* harmony export */   createHasher: () => (/* binding */ createHasher),\n/* harmony export */   createOptHasher: () => (/* binding */ createOptHasher),\n/* harmony export */   createView: () => (/* binding */ createView),\n/* harmony export */   createXOFer: () => (/* binding */ createXOFer),\n/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),\n/* harmony export */   isBytes: () => (/* binding */ isBytes),\n/* harmony export */   isLE: () => (/* binding */ isLE),\n/* harmony export */   kdfInputToBytes: () => (/* binding */ kdfInputToBytes),\n/* harmony export */   nextTick: () => (/* binding */ nextTick),\n/* harmony export */   randomBytes: () => (/* binding */ randomBytes),\n/* harmony export */   rotl: () => (/* binding */ rotl),\n/* harmony export */   rotr: () => (/* binding */ rotr),\n/* harmony export */   swap32IfBE: () => (/* binding */ swap32IfBE),\n/* harmony export */   swap8IfBE: () => (/* binding */ swap8IfBE),\n/* harmony export */   toBytes: () => (/* binding */ toBytes),\n/* harmony export */   u32: () => (/* binding */ u32),\n/* harmony export */   u8: () => (/* binding */ u8),\n/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),\n/* harmony export */   wrapConstructor: () => (/* binding */ wrapConstructor),\n/* harmony export */   wrapConstructorWithOpts: () => (/* binding */ wrapConstructorWithOpts),\n/* harmony export */   wrapXOFConstructorWithOpts: () => (/* binding */ wrapXOFConstructorWithOpts)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/crypto */ \"(ssr)/./node_modules/@noble/hashes/esm/cryptoNode.js\");\n/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\n\n/** Checks if something is Uint8Array. Be careful: nodejs Buffer will return true. */\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n/** Asserts something is positive integer. */\nfunction anumber(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error('positive integer expected, got ' + n);\n}\n/** Asserts something is Uint8Array. */\nfunction abytes(b, ...lengths) {\n    if (!isBytes(b))\n        throw new Error('Uint8Array expected');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n/** Asserts something is hash */\nfunction ahash(h) {\n    if (typeof h !== 'function' || typeof h.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.createHasher');\n    anumber(h.outputLen);\n    anumber(h.blockLen);\n}\n/** Asserts a hash instance has not been destroyed / finished */\nfunction aexists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\n/** Asserts output is properly-sized byte array */\nfunction aoutput(out, instance) {\n    abytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error('digestInto() expects output buffer of length at least ' + min);\n    }\n}\n/** Cast u8 / u16 / u32 to u8. */\nfunction u8(arr) {\n    return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** Cast u8 / u16 / u32 to u32. */\nfunction u32(arr) {\n    return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n/** Zeroize a byte array. Warning: JS provides no guarantees. */\nfunction clean(...arrays) {\n    for (let i = 0; i < arrays.length; i++) {\n        arrays[i].fill(0);\n    }\n}\n/** Create DataView of an array for easy byte-level manipulation. */\nfunction createView(arr) {\n    return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** The rotate right (circular right shift) operation for uint32 */\nfunction rotr(word, shift) {\n    return (word << (32 - shift)) | (word >>> shift);\n}\n/** The rotate left (circular left shift) operation for uint32 */\nfunction rotl(word, shift) {\n    return (word << shift) | ((word >>> (32 - shift)) >>> 0);\n}\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nconst isLE = /* @__PURE__ */ (() => new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n/** The byte swap operation for uint32 */\nfunction byteSwap(word) {\n    return (((word << 24) & 0xff000000) |\n        ((word << 8) & 0xff0000) |\n        ((word >>> 8) & 0xff00) |\n        ((word >>> 24) & 0xff));\n}\n/** Conditionally byte swap if on a big-endian platform */\nconst swap8IfBE = isLE\n    ? (n) => n\n    : (n) => byteSwap(n);\n/** @deprecated */\nconst byteSwapIfBE = swap8IfBE;\n/** In place byte swap for Uint32Array */\nfunction byteSwap32(arr) {\n    for (let i = 0; i < arr.length; i++) {\n        arr[i] = byteSwap(arr[i]);\n    }\n    return arr;\n}\nconst swap32IfBE = isLE\n    ? (u) => u\n    : byteSwap32;\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\nconst hasHexBuiltin = /* @__PURE__ */ (() => \n// @ts-ignore\ntypeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function')();\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * Convert byte array to hex string. Uses built-in function, when available.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nfunction bytesToHex(bytes) {\n    abytes(bytes);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return bytes.toHex();\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * Convert hex string to byte array. Uses built-in function, when available.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return Uint8Array.fromHex(hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n/**\n * There is no setImmediate in browser and setTimeout is slow.\n * Call of async fn will return Promise, which will be fullfiled only on\n * next scheduler queue processing step and this is exactly what we need.\n */\nconst nextTick = async () => { };\n/** Returns control to thread each 'tick' ms to avoid blocking. */\nasync function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await nextTick();\n        ts += diff;\n    }\n}\n/**\n * Converts string to bytes using UTF8 encoding.\n * @example utf8ToBytes('abc') // Uint8Array.from([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\nfunction bytesToUtf8(bytes) {\n    return new TextDecoder().decode(bytes);\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nfunction toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/**\n * Helper for KDFs: consumes uint8array or string.\n * When string is passed, does utf8 decoding, using TextDecoder.\n */\nfunction kdfInputToBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/** Copies several Uint8Arrays into one. */\nfunction concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\nfunction checkOpts(defaults, opts) {\n    if (opts !== undefined && {}.toString.call(opts) !== '[object Object]')\n        throw new Error('options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\n/** For runtime check if class implements interface */\nclass Hash {\n}\n/** Wraps hash function, creating an interface on top of it */\nfunction createHasher(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nfunction createOptHasher(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nfunction createXOFer(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nconst wrapConstructor = createHasher;\nconst wrapConstructorWithOpts = createOptHasher;\nconst wrapXOFConstructorWithOpts = createXOFer;\n/** Cryptographically secure PRNG. Uses internal OS-level `crypto.getRandomValues`. */\nfunction randomBytes(bytesLength = 32) {\n    if (_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto && typeof _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.getRandomValues === 'function') {\n        return _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    // Legacy Node.js compatibility\n    if (_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto && typeof _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.randomBytes === 'function') {\n        return Uint8Array.from(_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.randomBytes(bytesLength));\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/utils.js\n");

/***/ })

};
;