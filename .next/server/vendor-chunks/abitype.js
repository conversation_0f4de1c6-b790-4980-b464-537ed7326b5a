"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/abitype";
exports.ids = ["vendor-chunks/abitype"];
exports.modules = {

/***/ "(ssr)/./node_modules/abitype/dist/cjs/errors.js":
/*!*************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/errors.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BaseError = void 0;\nconst version_js_1 = __webpack_require__(/*! ./version.js */ \"(ssr)/./node_modules/abitype/dist/cjs/version.js\");\nclass BaseError extends Error {\n    constructor(shortMessage, args = {}) {\n        const details = args.cause instanceof BaseError\n            ? args.cause.details\n            : args.cause?.message\n                ? args.cause.message\n                : args.details;\n        const docsPath = args.cause instanceof BaseError\n            ? args.cause.docsPath || args.docsPath\n            : args.docsPath;\n        const message = [\n            shortMessage || 'An error occurred.',\n            '',\n            ...(args.metaMessages ? [...args.metaMessages, ''] : []),\n            ...(docsPath ? [`Docs: https://abitype.dev${docsPath}`] : []),\n            ...(details ? [`Details: ${details}`] : []),\n            `Version: abitype@${version_js_1.version}`,\n        ].join('\\n');\n        super(message);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"metaMessages\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'AbiTypeError'\n        });\n        if (args.cause)\n            this.cause = args.cause;\n        this.details = details;\n        this.docsPath = docsPath;\n        this.metaMessages = args.metaMessages;\n        this.shortMessage = shortMessage;\n    }\n}\nexports.BaseError = BaseError;\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/exports/index.js":
/*!********************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/exports/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.CircularReferenceError = exports.InvalidParenthesisError = exports.UnknownSignatureError = exports.InvalidSignatureError = exports.InvalidStructSignatureError = exports.InvalidAbiParameterError = exports.InvalidAbiParametersError = exports.InvalidParameterError = exports.SolidityProtectedKeywordError = exports.InvalidModifierError = exports.InvalidFunctionModifierError = exports.InvalidAbiTypeParameterError = exports.UnknownSolidityTypeError = exports.InvalidAbiItemError = exports.UnknownTypeError = exports.parseAbiParameters = exports.parseAbiParameter = exports.parseAbiItem = exports.parseAbi = exports.formatAbiParameters = exports.formatAbiParameter = exports.formatAbiItem = exports.formatAbi = exports.narrow = exports.BaseError = void 0;\nvar errors_js_1 = __webpack_require__(/*! ../errors.js */ \"(ssr)/./node_modules/abitype/dist/cjs/errors.js\");\nObject.defineProperty(exports, \"BaseError\", ({ enumerable: true, get: function () { return errors_js_1.BaseError; } }));\nvar narrow_js_1 = __webpack_require__(/*! ../narrow.js */ \"(ssr)/./node_modules/abitype/dist/cjs/narrow.js\");\nObject.defineProperty(exports, \"narrow\", ({ enumerable: true, get: function () { return narrow_js_1.narrow; } }));\nvar formatAbi_js_1 = __webpack_require__(/*! ../human-readable/formatAbi.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/formatAbi.js\");\nObject.defineProperty(exports, \"formatAbi\", ({ enumerable: true, get: function () { return formatAbi_js_1.formatAbi; } }));\nvar formatAbiItem_js_1 = __webpack_require__(/*! ../human-readable/formatAbiItem.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/formatAbiItem.js\");\nObject.defineProperty(exports, \"formatAbiItem\", ({ enumerable: true, get: function () { return formatAbiItem_js_1.formatAbiItem; } }));\nvar formatAbiParameter_js_1 = __webpack_require__(/*! ../human-readable/formatAbiParameter.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/formatAbiParameter.js\");\nObject.defineProperty(exports, \"formatAbiParameter\", ({ enumerable: true, get: function () { return formatAbiParameter_js_1.formatAbiParameter; } }));\nvar formatAbiParameters_js_1 = __webpack_require__(/*! ../human-readable/formatAbiParameters.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/formatAbiParameters.js\");\nObject.defineProperty(exports, \"formatAbiParameters\", ({ enumerable: true, get: function () { return formatAbiParameters_js_1.formatAbiParameters; } }));\nvar parseAbi_js_1 = __webpack_require__(/*! ../human-readable/parseAbi.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/parseAbi.js\");\nObject.defineProperty(exports, \"parseAbi\", ({ enumerable: true, get: function () { return parseAbi_js_1.parseAbi; } }));\nvar parseAbiItem_js_1 = __webpack_require__(/*! ../human-readable/parseAbiItem.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/parseAbiItem.js\");\nObject.defineProperty(exports, \"parseAbiItem\", ({ enumerable: true, get: function () { return parseAbiItem_js_1.parseAbiItem; } }));\nvar parseAbiParameter_js_1 = __webpack_require__(/*! ../human-readable/parseAbiParameter.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/parseAbiParameter.js\");\nObject.defineProperty(exports, \"parseAbiParameter\", ({ enumerable: true, get: function () { return parseAbiParameter_js_1.parseAbiParameter; } }));\nvar parseAbiParameters_js_1 = __webpack_require__(/*! ../human-readable/parseAbiParameters.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/parseAbiParameters.js\");\nObject.defineProperty(exports, \"parseAbiParameters\", ({ enumerable: true, get: function () { return parseAbiParameters_js_1.parseAbiParameters; } }));\nvar abiItem_js_1 = __webpack_require__(/*! ../human-readable/errors/abiItem.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/abiItem.js\");\nObject.defineProperty(exports, \"UnknownTypeError\", ({ enumerable: true, get: function () { return abiItem_js_1.UnknownTypeError; } }));\nObject.defineProperty(exports, \"InvalidAbiItemError\", ({ enumerable: true, get: function () { return abiItem_js_1.InvalidAbiItemError; } }));\nObject.defineProperty(exports, \"UnknownSolidityTypeError\", ({ enumerable: true, get: function () { return abiItem_js_1.UnknownSolidityTypeError; } }));\nvar abiParameter_js_1 = __webpack_require__(/*! ../human-readable/errors/abiParameter.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/abiParameter.js\");\nObject.defineProperty(exports, \"InvalidAbiTypeParameterError\", ({ enumerable: true, get: function () { return abiParameter_js_1.InvalidAbiTypeParameterError; } }));\nObject.defineProperty(exports, \"InvalidFunctionModifierError\", ({ enumerable: true, get: function () { return abiParameter_js_1.InvalidFunctionModifierError; } }));\nObject.defineProperty(exports, \"InvalidModifierError\", ({ enumerable: true, get: function () { return abiParameter_js_1.InvalidModifierError; } }));\nObject.defineProperty(exports, \"SolidityProtectedKeywordError\", ({ enumerable: true, get: function () { return abiParameter_js_1.SolidityProtectedKeywordError; } }));\nObject.defineProperty(exports, \"InvalidParameterError\", ({ enumerable: true, get: function () { return abiParameter_js_1.InvalidParameterError; } }));\nObject.defineProperty(exports, \"InvalidAbiParametersError\", ({ enumerable: true, get: function () { return abiParameter_js_1.InvalidAbiParametersError; } }));\nObject.defineProperty(exports, \"InvalidAbiParameterError\", ({ enumerable: true, get: function () { return abiParameter_js_1.InvalidAbiParameterError; } }));\nvar signature_js_1 = __webpack_require__(/*! ../human-readable/errors/signature.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/signature.js\");\nObject.defineProperty(exports, \"InvalidStructSignatureError\", ({ enumerable: true, get: function () { return signature_js_1.InvalidStructSignatureError; } }));\nObject.defineProperty(exports, \"InvalidSignatureError\", ({ enumerable: true, get: function () { return signature_js_1.InvalidSignatureError; } }));\nObject.defineProperty(exports, \"UnknownSignatureError\", ({ enumerable: true, get: function () { return signature_js_1.UnknownSignatureError; } }));\nvar splitParameters_js_1 = __webpack_require__(/*! ../human-readable/errors/splitParameters.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/splitParameters.js\");\nObject.defineProperty(exports, \"InvalidParenthesisError\", ({ enumerable: true, get: function () { return splitParameters_js_1.InvalidParenthesisError; } }));\nvar struct_js_1 = __webpack_require__(/*! ../human-readable/errors/struct.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/struct.js\");\nObject.defineProperty(exports, \"CircularReferenceError\", ({ enumerable: true, get: function () { return struct_js_1.CircularReferenceError; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/exports/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/abiItem.js":
/*!************************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/errors/abiItem.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.UnknownSolidityTypeError = exports.UnknownTypeError = exports.InvalidAbiItemError = void 0;\nconst errors_js_1 = __webpack_require__(/*! ../../errors.js */ \"(ssr)/./node_modules/abitype/dist/cjs/errors.js\");\nclass InvalidAbiItemError extends errors_js_1.BaseError {\n    constructor({ signature }) {\n        super('Failed to parse ABI item.', {\n            details: `parseAbiItem(${JSON.stringify(signature, null, 2)})`,\n            docsPath: '/api/human#parseabiitem-1',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiItemError'\n        });\n    }\n}\nexports.InvalidAbiItemError = InvalidAbiItemError;\nclass UnknownTypeError extends errors_js_1.BaseError {\n    constructor({ type }) {\n        super('Unknown type.', {\n            metaMessages: [\n                `Type \"${type}\" is not a valid ABI type. Perhaps you forgot to include a struct signature?`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'UnknownTypeError'\n        });\n    }\n}\nexports.UnknownTypeError = UnknownTypeError;\nclass UnknownSolidityTypeError extends errors_js_1.BaseError {\n    constructor({ type }) {\n        super('Unknown type.', {\n            metaMessages: [`Type \"${type}\" is not a valid ABI type.`],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'UnknownSolidityTypeError'\n        });\n    }\n}\nexports.UnknownSolidityTypeError = UnknownSolidityTypeError;\n//# sourceMappingURL=abiItem.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/abiItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/abiParameter.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/errors/abiParameter.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.InvalidAbiTypeParameterError = exports.InvalidFunctionModifierError = exports.InvalidModifierError = exports.SolidityProtectedKeywordError = exports.InvalidParameterError = exports.InvalidAbiParametersError = exports.InvalidAbiParameterError = void 0;\nconst errors_js_1 = __webpack_require__(/*! ../../errors.js */ \"(ssr)/./node_modules/abitype/dist/cjs/errors.js\");\nclass InvalidAbiParameterError extends errors_js_1.BaseError {\n    constructor({ param }) {\n        super('Failed to parse ABI parameter.', {\n            details: `parseAbiParameter(${JSON.stringify(param, null, 2)})`,\n            docsPath: '/api/human#parseabiparameter-1',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiParameterError'\n        });\n    }\n}\nexports.InvalidAbiParameterError = InvalidAbiParameterError;\nclass InvalidAbiParametersError extends errors_js_1.BaseError {\n    constructor({ params }) {\n        super('Failed to parse ABI parameters.', {\n            details: `parseAbiParameters(${JSON.stringify(params, null, 2)})`,\n            docsPath: '/api/human#parseabiparameters-1',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiParametersError'\n        });\n    }\n}\nexports.InvalidAbiParametersError = InvalidAbiParametersError;\nclass InvalidParameterError extends errors_js_1.BaseError {\n    constructor({ param }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidParameterError'\n        });\n    }\n}\nexports.InvalidParameterError = InvalidParameterError;\nclass SolidityProtectedKeywordError extends errors_js_1.BaseError {\n    constructor({ param, name }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n            metaMessages: [\n                `\"${name}\" is a protected Solidity keyword. More info: https://docs.soliditylang.org/en/latest/cheatsheet.html`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'SolidityProtectedKeywordError'\n        });\n    }\n}\nexports.SolidityProtectedKeywordError = SolidityProtectedKeywordError;\nclass InvalidModifierError extends errors_js_1.BaseError {\n    constructor({ param, type, modifier, }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n            metaMessages: [\n                `Modifier \"${modifier}\" not allowed${type ? ` in \"${type}\" type` : ''}.`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidModifierError'\n        });\n    }\n}\nexports.InvalidModifierError = InvalidModifierError;\nclass InvalidFunctionModifierError extends errors_js_1.BaseError {\n    constructor({ param, type, modifier, }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n            metaMessages: [\n                `Modifier \"${modifier}\" not allowed${type ? ` in \"${type}\" type` : ''}.`,\n                `Data location can only be specified for array, struct, or mapping types, but \"${modifier}\" was given.`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidFunctionModifierError'\n        });\n    }\n}\nexports.InvalidFunctionModifierError = InvalidFunctionModifierError;\nclass InvalidAbiTypeParameterError extends errors_js_1.BaseError {\n    constructor({ abiParameter, }) {\n        super('Invalid ABI parameter.', {\n            details: JSON.stringify(abiParameter, null, 2),\n            metaMessages: ['ABI parameter type is invalid.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiTypeParameterError'\n        });\n    }\n}\nexports.InvalidAbiTypeParameterError = InvalidAbiTypeParameterError;\n//# sourceMappingURL=abiParameter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/abiParameter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/signature.js":
/*!**************************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/errors/signature.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.InvalidStructSignatureError = exports.UnknownSignatureError = exports.InvalidSignatureError = void 0;\nconst errors_js_1 = __webpack_require__(/*! ../../errors.js */ \"(ssr)/./node_modules/abitype/dist/cjs/errors.js\");\nclass InvalidSignatureError extends errors_js_1.BaseError {\n    constructor({ signature, type, }) {\n        super(`Invalid ${type} signature.`, {\n            details: signature,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidSignatureError'\n        });\n    }\n}\nexports.InvalidSignatureError = InvalidSignatureError;\nclass UnknownSignatureError extends errors_js_1.BaseError {\n    constructor({ signature }) {\n        super('Unknown signature.', {\n            details: signature,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'UnknownSignatureError'\n        });\n    }\n}\nexports.UnknownSignatureError = UnknownSignatureError;\nclass InvalidStructSignatureError extends errors_js_1.BaseError {\n    constructor({ signature }) {\n        super('Invalid struct signature.', {\n            details: signature,\n            metaMessages: ['No properties exist.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidStructSignatureError'\n        });\n    }\n}\nexports.InvalidStructSignatureError = InvalidStructSignatureError;\n//# sourceMappingURL=signature.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/signature.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/splitParameters.js":
/*!********************************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/errors/splitParameters.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.InvalidParenthesisError = void 0;\nconst errors_js_1 = __webpack_require__(/*! ../../errors.js */ \"(ssr)/./node_modules/abitype/dist/cjs/errors.js\");\nclass InvalidParenthesisError extends errors_js_1.BaseError {\n    constructor({ current, depth }) {\n        super('Unbalanced parentheses.', {\n            metaMessages: [\n                `\"${current.trim()}\" has too many ${depth > 0 ? 'opening' : 'closing'} parentheses.`,\n            ],\n            details: `Depth \"${depth}\"`,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidParenthesisError'\n        });\n    }\n}\nexports.InvalidParenthesisError = InvalidParenthesisError;\n//# sourceMappingURL=splitParameters.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2Nqcy9odW1hbi1yZWFkYWJsZS9lcnJvcnMvc3BsaXRQYXJhbWV0ZXJzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELCtCQUErQjtBQUMvQixvQkFBb0IsbUJBQU8sQ0FBQyx3RUFBaUI7QUFDN0M7QUFDQSxrQkFBa0IsZ0JBQWdCO0FBQ2xDO0FBQ0E7QUFDQSxvQkFBb0IsZUFBZSxpQkFBaUIsbUNBQW1DO0FBQ3ZGO0FBQ0EsK0JBQStCLE1BQU07QUFDckMsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLCtCQUErQjtBQUMvQiIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9hYml0eXBlL2Rpc3QvY2pzL2h1bWFuLXJlYWRhYmxlL2Vycm9ycy9zcGxpdFBhcmFtZXRlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkludmFsaWRQYXJlbnRoZXNpc0Vycm9yID0gdm9pZCAwO1xuY29uc3QgZXJyb3JzX2pzXzEgPSByZXF1aXJlKFwiLi4vLi4vZXJyb3JzLmpzXCIpO1xuY2xhc3MgSW52YWxpZFBhcmVudGhlc2lzRXJyb3IgZXh0ZW5kcyBlcnJvcnNfanNfMS5CYXNlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKHsgY3VycmVudCwgZGVwdGggfSkge1xuICAgICAgICBzdXBlcignVW5iYWxhbmNlZCBwYXJlbnRoZXNlcy4nLCB7XG4gICAgICAgICAgICBtZXRhTWVzc2FnZXM6IFtcbiAgICAgICAgICAgICAgICBgXCIke2N1cnJlbnQudHJpbSgpfVwiIGhhcyB0b28gbWFueSAke2RlcHRoID4gMCA/ICdvcGVuaW5nJyA6ICdjbG9zaW5nJ30gcGFyZW50aGVzZXMuYCxcbiAgICAgICAgICAgIF0sXG4gICAgICAgICAgICBkZXRhaWxzOiBgRGVwdGggXCIke2RlcHRofVwiYCxcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm5hbWVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6ICdJbnZhbGlkUGFyZW50aGVzaXNFcnJvcidcbiAgICAgICAgfSk7XG4gICAgfVxufVxuZXhwb3J0cy5JbnZhbGlkUGFyZW50aGVzaXNFcnJvciA9IEludmFsaWRQYXJlbnRoZXNpc0Vycm9yO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3BsaXRQYXJhbWV0ZXJzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/splitParameters.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/struct.js":
/*!***********************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/errors/struct.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.CircularReferenceError = void 0;\nconst errors_js_1 = __webpack_require__(/*! ../../errors.js */ \"(ssr)/./node_modules/abitype/dist/cjs/errors.js\");\nclass CircularReferenceError extends errors_js_1.BaseError {\n    constructor({ type }) {\n        super('Circular reference detected.', {\n            metaMessages: [`Struct \"${type}\" is a circular reference.`],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'CircularReferenceError'\n        });\n    }\n}\nexports.CircularReferenceError = CircularReferenceError;\n//# sourceMappingURL=struct.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2Nqcy9odW1hbi1yZWFkYWJsZS9lcnJvcnMvc3RydWN0LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDhCQUE4QjtBQUM5QixvQkFBb0IsbUJBQU8sQ0FBQyx3RUFBaUI7QUFDN0M7QUFDQSxrQkFBa0IsTUFBTTtBQUN4QjtBQUNBLHNDQUFzQyxLQUFLO0FBQzNDLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUIiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2Nqcy9odW1hbi1yZWFkYWJsZS9lcnJvcnMvc3RydWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5DaXJjdWxhclJlZmVyZW5jZUVycm9yID0gdm9pZCAwO1xuY29uc3QgZXJyb3JzX2pzXzEgPSByZXF1aXJlKFwiLi4vLi4vZXJyb3JzLmpzXCIpO1xuY2xhc3MgQ2lyY3VsYXJSZWZlcmVuY2VFcnJvciBleHRlbmRzIGVycm9yc19qc18xLkJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoeyB0eXBlIH0pIHtcbiAgICAgICAgc3VwZXIoJ0NpcmN1bGFyIHJlZmVyZW5jZSBkZXRlY3RlZC4nLCB7XG4gICAgICAgICAgICBtZXRhTWVzc2FnZXM6IFtgU3RydWN0IFwiJHt0eXBlfVwiIGlzIGEgY2lyY3VsYXIgcmVmZXJlbmNlLmBdLFxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogJ0NpcmN1bGFyUmVmZXJlbmNlRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbmV4cG9ydHMuQ2lyY3VsYXJSZWZlcmVuY2VFcnJvciA9IENpcmN1bGFyUmVmZXJlbmNlRXJyb3I7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zdHJ1Y3QuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/struct.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/formatAbi.js":
/*!*******************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/formatAbi.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.formatAbi = formatAbi;\nconst formatAbiItem_js_1 = __webpack_require__(/*! ./formatAbiItem.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/formatAbiItem.js\");\nfunction formatAbi(abi) {\n    const signatures = [];\n    const length = abi.length;\n    for (let i = 0; i < length; i++) {\n        const abiItem = abi[i];\n        const signature = (0, formatAbiItem_js_1.formatAbiItem)(abiItem);\n        signatures.push(signature);\n    }\n    return signatures;\n}\n//# sourceMappingURL=formatAbi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2Nqcy9odW1hbi1yZWFkYWJsZS9mb3JtYXRBYmkuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsaUJBQWlCO0FBQ2pCLDJCQUEyQixtQkFBTyxDQUFDLGlHQUFvQjtBQUN2RDtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsWUFBWTtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9hYml0eXBlL2Rpc3QvY2pzL2h1bWFuLXJlYWRhYmxlL2Zvcm1hdEFiaS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuZm9ybWF0QWJpID0gZm9ybWF0QWJpO1xuY29uc3QgZm9ybWF0QWJpSXRlbV9qc18xID0gcmVxdWlyZShcIi4vZm9ybWF0QWJpSXRlbS5qc1wiKTtcbmZ1bmN0aW9uIGZvcm1hdEFiaShhYmkpIHtcbiAgICBjb25zdCBzaWduYXR1cmVzID0gW107XG4gICAgY29uc3QgbGVuZ3RoID0gYWJpLmxlbmd0aDtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IGFiaUl0ZW0gPSBhYmlbaV07XG4gICAgICAgIGNvbnN0IHNpZ25hdHVyZSA9ICgwLCBmb3JtYXRBYmlJdGVtX2pzXzEuZm9ybWF0QWJpSXRlbSkoYWJpSXRlbSk7XG4gICAgICAgIHNpZ25hdHVyZXMucHVzaChzaWduYXR1cmUpO1xuICAgIH1cbiAgICByZXR1cm4gc2lnbmF0dXJlcztcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWZvcm1hdEFiaS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/formatAbi.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/formatAbiItem.js":
/*!***********************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/formatAbiItem.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.formatAbiItem = formatAbiItem;\nconst formatAbiParameters_js_1 = __webpack_require__(/*! ./formatAbiParameters.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/formatAbiParameters.js\");\nfunction formatAbiItem(abiItem) {\n    if (abiItem.type === 'function')\n        return `function ${abiItem.name}(${(0, formatAbiParameters_js_1.formatAbiParameters)(abiItem.inputs)})${abiItem.stateMutability && abiItem.stateMutability !== 'nonpayable'\n            ? ` ${abiItem.stateMutability}`\n            : ''}${abiItem.outputs?.length\n            ? ` returns (${(0, formatAbiParameters_js_1.formatAbiParameters)(abiItem.outputs)})`\n            : ''}`;\n    if (abiItem.type === 'event')\n        return `event ${abiItem.name}(${(0, formatAbiParameters_js_1.formatAbiParameters)(abiItem.inputs)})`;\n    if (abiItem.type === 'error')\n        return `error ${abiItem.name}(${(0, formatAbiParameters_js_1.formatAbiParameters)(abiItem.inputs)})`;\n    if (abiItem.type === 'constructor')\n        return `constructor(${(0, formatAbiParameters_js_1.formatAbiParameters)(abiItem.inputs)})${abiItem.stateMutability === 'payable' ? ' payable' : ''}`;\n    if (abiItem.type === 'fallback')\n        return `fallback() external${abiItem.stateMutability === 'payable' ? ' payable' : ''}`;\n    return 'receive() external payable';\n}\n//# sourceMappingURL=formatAbiItem.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/formatAbiItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/formatAbiParameter.js":
/*!****************************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/formatAbiParameter.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.formatAbiParameter = formatAbiParameter;\nconst regex_js_1 = __webpack_require__(/*! ../regex.js */ \"(ssr)/./node_modules/abitype/dist/cjs/regex.js\");\nconst tupleRegex = /^tuple(?<array>(\\[(\\d*)\\])*)$/;\nfunction formatAbiParameter(abiParameter) {\n    let type = abiParameter.type;\n    if (tupleRegex.test(abiParameter.type) && 'components' in abiParameter) {\n        type = '(';\n        const length = abiParameter.components.length;\n        for (let i = 0; i < length; i++) {\n            const component = abiParameter.components[i];\n            type += formatAbiParameter(component);\n            if (i < length - 1)\n                type += ', ';\n        }\n        const result = (0, regex_js_1.execTyped)(tupleRegex, abiParameter.type);\n        type += `)${result?.array ?? ''}`;\n        return formatAbiParameter({\n            ...abiParameter,\n            type,\n        });\n    }\n    if ('indexed' in abiParameter && abiParameter.indexed)\n        type = `${type} indexed`;\n    if (abiParameter.name)\n        return `${type} ${abiParameter.name}`;\n    return type;\n}\n//# sourceMappingURL=formatAbiParameter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/formatAbiParameter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/formatAbiParameters.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/formatAbiParameters.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.formatAbiParameters = formatAbiParameters;\nconst formatAbiParameter_js_1 = __webpack_require__(/*! ./formatAbiParameter.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/formatAbiParameter.js\");\nfunction formatAbiParameters(abiParameters) {\n    let params = '';\n    const length = abiParameters.length;\n    for (let i = 0; i < length; i++) {\n        const abiParameter = abiParameters[i];\n        params += (0, formatAbiParameter_js_1.formatAbiParameter)(abiParameter);\n        if (i !== length - 1)\n            params += ', ';\n    }\n    return params;\n}\n//# sourceMappingURL=formatAbiParameters.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2Nqcy9odW1hbi1yZWFkYWJsZS9mb3JtYXRBYmlQYXJhbWV0ZXJzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDJCQUEyQjtBQUMzQixnQ0FBZ0MsbUJBQU8sQ0FBQywyR0FBeUI7QUFDakU7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLFlBQVk7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9hYml0eXBlL2Rpc3QvY2pzL2h1bWFuLXJlYWRhYmxlL2Zvcm1hdEFiaVBhcmFtZXRlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmZvcm1hdEFiaVBhcmFtZXRlcnMgPSBmb3JtYXRBYmlQYXJhbWV0ZXJzO1xuY29uc3QgZm9ybWF0QWJpUGFyYW1ldGVyX2pzXzEgPSByZXF1aXJlKFwiLi9mb3JtYXRBYmlQYXJhbWV0ZXIuanNcIik7XG5mdW5jdGlvbiBmb3JtYXRBYmlQYXJhbWV0ZXJzKGFiaVBhcmFtZXRlcnMpIHtcbiAgICBsZXQgcGFyYW1zID0gJyc7XG4gICAgY29uc3QgbGVuZ3RoID0gYWJpUGFyYW1ldGVycy5sZW5ndGg7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsZW5ndGg7IGkrKykge1xuICAgICAgICBjb25zdCBhYmlQYXJhbWV0ZXIgPSBhYmlQYXJhbWV0ZXJzW2ldO1xuICAgICAgICBwYXJhbXMgKz0gKDAsIGZvcm1hdEFiaVBhcmFtZXRlcl9qc18xLmZvcm1hdEFiaVBhcmFtZXRlcikoYWJpUGFyYW1ldGVyKTtcbiAgICAgICAgaWYgKGkgIT09IGxlbmd0aCAtIDEpXG4gICAgICAgICAgICBwYXJhbXMgKz0gJywgJztcbiAgICB9XG4gICAgcmV0dXJuIHBhcmFtcztcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWZvcm1hdEFiaVBhcmFtZXRlcnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/formatAbiParameters.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/parseAbi.js":
/*!******************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/parseAbi.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseAbi = parseAbi;\nconst signatures_js_1 = __webpack_require__(/*! ./runtime/signatures.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js\");\nconst structs_js_1 = __webpack_require__(/*! ./runtime/structs.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/structs.js\");\nconst utils_js_1 = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/utils.js\");\nfunction parseAbi(signatures) {\n    const structs = (0, structs_js_1.parseStructs)(signatures);\n    const abi = [];\n    const length = signatures.length;\n    for (let i = 0; i < length; i++) {\n        const signature = signatures[i];\n        if ((0, signatures_js_1.isStructSignature)(signature))\n            continue;\n        abi.push((0, utils_js_1.parseSignature)(signature, structs));\n    }\n    return abi;\n}\n//# sourceMappingURL=parseAbi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2Nqcy9odW1hbi1yZWFkYWJsZS9wYXJzZUFiaS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxnQkFBZ0I7QUFDaEIsd0JBQXdCLG1CQUFPLENBQUMsMkdBQXlCO0FBQ3pELHFCQUFxQixtQkFBTyxDQUFDLHFHQUFzQjtBQUNuRCxtQkFBbUIsbUJBQU8sQ0FBQyxpR0FBb0I7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsWUFBWTtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9janMvaHVtYW4tcmVhZGFibGUvcGFyc2VBYmkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnBhcnNlQWJpID0gcGFyc2VBYmk7XG5jb25zdCBzaWduYXR1cmVzX2pzXzEgPSByZXF1aXJlKFwiLi9ydW50aW1lL3NpZ25hdHVyZXMuanNcIik7XG5jb25zdCBzdHJ1Y3RzX2pzXzEgPSByZXF1aXJlKFwiLi9ydW50aW1lL3N0cnVjdHMuanNcIik7XG5jb25zdCB1dGlsc19qc18xID0gcmVxdWlyZShcIi4vcnVudGltZS91dGlscy5qc1wiKTtcbmZ1bmN0aW9uIHBhcnNlQWJpKHNpZ25hdHVyZXMpIHtcbiAgICBjb25zdCBzdHJ1Y3RzID0gKDAsIHN0cnVjdHNfanNfMS5wYXJzZVN0cnVjdHMpKHNpZ25hdHVyZXMpO1xuICAgIGNvbnN0IGFiaSA9IFtdO1xuICAgIGNvbnN0IGxlbmd0aCA9IHNpZ25hdHVyZXMubGVuZ3RoO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbGVuZ3RoOyBpKyspIHtcbiAgICAgICAgY29uc3Qgc2lnbmF0dXJlID0gc2lnbmF0dXJlc1tpXTtcbiAgICAgICAgaWYgKCgwLCBzaWduYXR1cmVzX2pzXzEuaXNTdHJ1Y3RTaWduYXR1cmUpKHNpZ25hdHVyZSkpXG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgYWJpLnB1c2goKDAsIHV0aWxzX2pzXzEucGFyc2VTaWduYXR1cmUpKHNpZ25hdHVyZSwgc3RydWN0cykpO1xuICAgIH1cbiAgICByZXR1cm4gYWJpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFyc2VBYmkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/parseAbi.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/parseAbiItem.js":
/*!**********************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/parseAbiItem.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseAbiItem = parseAbiItem;\nconst abiItem_js_1 = __webpack_require__(/*! ./errors/abiItem.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/abiItem.js\");\nconst signatures_js_1 = __webpack_require__(/*! ./runtime/signatures.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js\");\nconst structs_js_1 = __webpack_require__(/*! ./runtime/structs.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/structs.js\");\nconst utils_js_1 = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/utils.js\");\nfunction parseAbiItem(signature) {\n    let abiItem;\n    if (typeof signature === 'string')\n        abiItem = (0, utils_js_1.parseSignature)(signature);\n    else {\n        const structs = (0, structs_js_1.parseStructs)(signature);\n        const length = signature.length;\n        for (let i = 0; i < length; i++) {\n            const signature_ = signature[i];\n            if ((0, signatures_js_1.isStructSignature)(signature_))\n                continue;\n            abiItem = (0, utils_js_1.parseSignature)(signature_, structs);\n            break;\n        }\n    }\n    if (!abiItem)\n        throw new abiItem_js_1.InvalidAbiItemError({ signature });\n    return abiItem;\n}\n//# sourceMappingURL=parseAbiItem.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/parseAbiItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/parseAbiParameter.js":
/*!***************************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/parseAbiParameter.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseAbiParameter = parseAbiParameter;\nconst abiParameter_js_1 = __webpack_require__(/*! ./errors/abiParameter.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/abiParameter.js\");\nconst signatures_js_1 = __webpack_require__(/*! ./runtime/signatures.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js\");\nconst structs_js_1 = __webpack_require__(/*! ./runtime/structs.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/structs.js\");\nconst utils_js_1 = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/utils.js\");\nfunction parseAbiParameter(param) {\n    let abiParameter;\n    if (typeof param === 'string')\n        abiParameter = (0, utils_js_1.parseAbiParameter)(param, {\n            modifiers: signatures_js_1.modifiers,\n        });\n    else {\n        const structs = (0, structs_js_1.parseStructs)(param);\n        const length = param.length;\n        for (let i = 0; i < length; i++) {\n            const signature = param[i];\n            if ((0, signatures_js_1.isStructSignature)(signature))\n                continue;\n            abiParameter = (0, utils_js_1.parseAbiParameter)(signature, { modifiers: signatures_js_1.modifiers, structs });\n            break;\n        }\n    }\n    if (!abiParameter)\n        throw new abiParameter_js_1.InvalidAbiParameterError({ param });\n    return abiParameter;\n}\n//# sourceMappingURL=parseAbiParameter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/parseAbiParameter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/parseAbiParameters.js":
/*!****************************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/parseAbiParameters.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseAbiParameters = parseAbiParameters;\nconst abiParameter_js_1 = __webpack_require__(/*! ./errors/abiParameter.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/abiParameter.js\");\nconst signatures_js_1 = __webpack_require__(/*! ./runtime/signatures.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js\");\nconst structs_js_1 = __webpack_require__(/*! ./runtime/structs.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/structs.js\");\nconst utils_js_1 = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/utils.js\");\nconst utils_js_2 = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/utils.js\");\nfunction parseAbiParameters(params) {\n    const abiParameters = [];\n    if (typeof params === 'string') {\n        const parameters = (0, utils_js_1.splitParameters)(params);\n        const length = parameters.length;\n        for (let i = 0; i < length; i++) {\n            abiParameters.push((0, utils_js_2.parseAbiParameter)(parameters[i], { modifiers: signatures_js_1.modifiers }));\n        }\n    }\n    else {\n        const structs = (0, structs_js_1.parseStructs)(params);\n        const length = params.length;\n        for (let i = 0; i < length; i++) {\n            const signature = params[i];\n            if ((0, signatures_js_1.isStructSignature)(signature))\n                continue;\n            const parameters = (0, utils_js_1.splitParameters)(signature);\n            const length = parameters.length;\n            for (let k = 0; k < length; k++) {\n                abiParameters.push((0, utils_js_2.parseAbiParameter)(parameters[k], { modifiers: signatures_js_1.modifiers, structs }));\n            }\n        }\n    }\n    if (abiParameters.length === 0)\n        throw new abiParameter_js_1.InvalidAbiParametersError({ params });\n    return abiParameters;\n}\n//# sourceMappingURL=parseAbiParameters.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/parseAbiParameters.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/cache.js":
/*!***********************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/runtime/cache.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parameterCache = void 0;\nexports.getParameterCacheKey = getParameterCacheKey;\nfunction getParameterCacheKey(param, type, structs) {\n    let structKey = '';\n    if (structs)\n        for (const struct of Object.entries(structs)) {\n            if (!struct)\n                continue;\n            let propertyKey = '';\n            for (const property of struct[1]) {\n                propertyKey += `[${property.type}${property.name ? `:${property.name}` : ''}]`;\n            }\n            structKey += `(${struct[0]}{${propertyKey}})`;\n        }\n    if (type)\n        return `${type}:${param}${structKey}`;\n    return param;\n}\nexports.parameterCache = new Map([\n    ['address', { type: 'address' }],\n    ['bool', { type: 'bool' }],\n    ['bytes', { type: 'bytes' }],\n    ['bytes32', { type: 'bytes32' }],\n    ['int', { type: 'int256' }],\n    ['int256', { type: 'int256' }],\n    ['string', { type: 'string' }],\n    ['uint', { type: 'uint256' }],\n    ['uint8', { type: 'uint8' }],\n    ['uint16', { type: 'uint16' }],\n    ['uint24', { type: 'uint24' }],\n    ['uint32', { type: 'uint32' }],\n    ['uint64', { type: 'uint64' }],\n    ['uint96', { type: 'uint96' }],\n    ['uint112', { type: 'uint112' }],\n    ['uint160', { type: 'uint160' }],\n    ['uint192', { type: 'uint192' }],\n    ['uint256', { type: 'uint256' }],\n    ['address owner', { type: 'address', name: 'owner' }],\n    ['address to', { type: 'address', name: 'to' }],\n    ['bool approved', { type: 'bool', name: 'approved' }],\n    ['bytes _data', { type: 'bytes', name: '_data' }],\n    ['bytes data', { type: 'bytes', name: 'data' }],\n    ['bytes signature', { type: 'bytes', name: 'signature' }],\n    ['bytes32 hash', { type: 'bytes32', name: 'hash' }],\n    ['bytes32 r', { type: 'bytes32', name: 'r' }],\n    ['bytes32 root', { type: 'bytes32', name: 'root' }],\n    ['bytes32 s', { type: 'bytes32', name: 's' }],\n    ['string name', { type: 'string', name: 'name' }],\n    ['string symbol', { type: 'string', name: 'symbol' }],\n    ['string tokenURI', { type: 'string', name: 'tokenURI' }],\n    ['uint tokenId', { type: 'uint256', name: 'tokenId' }],\n    ['uint8 v', { type: 'uint8', name: 'v' }],\n    ['uint256 balance', { type: 'uint256', name: 'balance' }],\n    ['uint256 tokenId', { type: 'uint256', name: 'tokenId' }],\n    ['uint256 value', { type: 'uint256', name: 'value' }],\n    [\n        'event:address indexed from',\n        { type: 'address', name: 'from', indexed: true },\n    ],\n    ['event:address indexed to', { type: 'address', name: 'to', indexed: true }],\n    [\n        'event:uint indexed tokenId',\n        { type: 'uint256', name: 'tokenId', indexed: true },\n    ],\n    [\n        'event:uint256 indexed tokenId',\n        { type: 'uint256', name: 'tokenId', indexed: true },\n    ],\n]);\n//# sourceMappingURL=cache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/cache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js":
/*!****************************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.functionModifiers = exports.eventModifiers = exports.modifiers = void 0;\nexports.isErrorSignature = isErrorSignature;\nexports.execErrorSignature = execErrorSignature;\nexports.isEventSignature = isEventSignature;\nexports.execEventSignature = execEventSignature;\nexports.isFunctionSignature = isFunctionSignature;\nexports.execFunctionSignature = execFunctionSignature;\nexports.isStructSignature = isStructSignature;\nexports.execStructSignature = execStructSignature;\nexports.isConstructorSignature = isConstructorSignature;\nexports.execConstructorSignature = execConstructorSignature;\nexports.isFallbackSignature = isFallbackSignature;\nexports.execFallbackSignature = execFallbackSignature;\nexports.isReceiveSignature = isReceiveSignature;\nconst regex_js_1 = __webpack_require__(/*! ../../regex.js */ \"(ssr)/./node_modules/abitype/dist/cjs/regex.js\");\nconst errorSignatureRegex = /^error (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)$/;\nfunction isErrorSignature(signature) {\n    return errorSignatureRegex.test(signature);\n}\nfunction execErrorSignature(signature) {\n    return (0, regex_js_1.execTyped)(errorSignatureRegex, signature);\n}\nconst eventSignatureRegex = /^event (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)$/;\nfunction isEventSignature(signature) {\n    return eventSignatureRegex.test(signature);\n}\nfunction execEventSignature(signature) {\n    return (0, regex_js_1.execTyped)(eventSignatureRegex, signature);\n}\nconst functionSignatureRegex = /^function (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)(?: (?<scope>external|public{1}))?(?: (?<stateMutability>pure|view|nonpayable|payable{1}))?(?: returns\\s?\\((?<returns>.*?)\\))?$/;\nfunction isFunctionSignature(signature) {\n    return functionSignatureRegex.test(signature);\n}\nfunction execFunctionSignature(signature) {\n    return (0, regex_js_1.execTyped)(functionSignatureRegex, signature);\n}\nconst structSignatureRegex = /^struct (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*) \\{(?<properties>.*?)\\}$/;\nfunction isStructSignature(signature) {\n    return structSignatureRegex.test(signature);\n}\nfunction execStructSignature(signature) {\n    return (0, regex_js_1.execTyped)(structSignatureRegex, signature);\n}\nconst constructorSignatureRegex = /^constructor\\((?<parameters>.*?)\\)(?:\\s(?<stateMutability>payable{1}))?$/;\nfunction isConstructorSignature(signature) {\n    return constructorSignatureRegex.test(signature);\n}\nfunction execConstructorSignature(signature) {\n    return (0, regex_js_1.execTyped)(constructorSignatureRegex, signature);\n}\nconst fallbackSignatureRegex = /^fallback\\(\\) external(?:\\s(?<stateMutability>payable{1}))?$/;\nfunction isFallbackSignature(signature) {\n    return fallbackSignatureRegex.test(signature);\n}\nfunction execFallbackSignature(signature) {\n    return (0, regex_js_1.execTyped)(fallbackSignatureRegex, signature);\n}\nconst receiveSignatureRegex = /^receive\\(\\) external payable$/;\nfunction isReceiveSignature(signature) {\n    return receiveSignatureRegex.test(signature);\n}\nexports.modifiers = new Set([\n    'memory',\n    'indexed',\n    'storage',\n    'calldata',\n]);\nexports.eventModifiers = new Set(['indexed']);\nexports.functionModifiers = new Set([\n    'calldata',\n    'memory',\n    'storage',\n]);\n//# sourceMappingURL=signatures.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2Nqcy9odW1hbi1yZWFkYWJsZS9ydW50aW1lL3NpZ25hdHVyZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QseUJBQXlCLEdBQUcsc0JBQXNCLEdBQUcsaUJBQWlCO0FBQ3RFLHdCQUF3QjtBQUN4QiwwQkFBMEI7QUFDMUIsd0JBQXdCO0FBQ3hCLDBCQUEwQjtBQUMxQiwyQkFBMkI7QUFDM0IsNkJBQTZCO0FBQzdCLHlCQUF5QjtBQUN6QiwyQkFBMkI7QUFDM0IsOEJBQThCO0FBQzlCLGdDQUFnQztBQUNoQywyQkFBMkI7QUFDM0IsNkJBQTZCO0FBQzdCLDBCQUEwQjtBQUMxQixtQkFBbUIsbUJBQU8sQ0FBQyxzRUFBZ0I7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhIQUE4SCxFQUFFLHVEQUF1RCxFQUFFO0FBQ3pMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBFQUEwRSxvQkFBb0I7QUFDOUY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUdBQXFHLEVBQUU7QUFDdkc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0ZBQXNGLEVBQUU7QUFDeEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQjtBQUN0Qix5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9hYml0eXBlL2Rpc3QvY2pzL2h1bWFuLXJlYWRhYmxlL3J1bnRpbWUvc2lnbmF0dXJlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuZnVuY3Rpb25Nb2RpZmllcnMgPSBleHBvcnRzLmV2ZW50TW9kaWZpZXJzID0gZXhwb3J0cy5tb2RpZmllcnMgPSB2b2lkIDA7XG5leHBvcnRzLmlzRXJyb3JTaWduYXR1cmUgPSBpc0Vycm9yU2lnbmF0dXJlO1xuZXhwb3J0cy5leGVjRXJyb3JTaWduYXR1cmUgPSBleGVjRXJyb3JTaWduYXR1cmU7XG5leHBvcnRzLmlzRXZlbnRTaWduYXR1cmUgPSBpc0V2ZW50U2lnbmF0dXJlO1xuZXhwb3J0cy5leGVjRXZlbnRTaWduYXR1cmUgPSBleGVjRXZlbnRTaWduYXR1cmU7XG5leHBvcnRzLmlzRnVuY3Rpb25TaWduYXR1cmUgPSBpc0Z1bmN0aW9uU2lnbmF0dXJlO1xuZXhwb3J0cy5leGVjRnVuY3Rpb25TaWduYXR1cmUgPSBleGVjRnVuY3Rpb25TaWduYXR1cmU7XG5leHBvcnRzLmlzU3RydWN0U2lnbmF0dXJlID0gaXNTdHJ1Y3RTaWduYXR1cmU7XG5leHBvcnRzLmV4ZWNTdHJ1Y3RTaWduYXR1cmUgPSBleGVjU3RydWN0U2lnbmF0dXJlO1xuZXhwb3J0cy5pc0NvbnN0cnVjdG9yU2lnbmF0dXJlID0gaXNDb25zdHJ1Y3RvclNpZ25hdHVyZTtcbmV4cG9ydHMuZXhlY0NvbnN0cnVjdG9yU2lnbmF0dXJlID0gZXhlY0NvbnN0cnVjdG9yU2lnbmF0dXJlO1xuZXhwb3J0cy5pc0ZhbGxiYWNrU2lnbmF0dXJlID0gaXNGYWxsYmFja1NpZ25hdHVyZTtcbmV4cG9ydHMuZXhlY0ZhbGxiYWNrU2lnbmF0dXJlID0gZXhlY0ZhbGxiYWNrU2lnbmF0dXJlO1xuZXhwb3J0cy5pc1JlY2VpdmVTaWduYXR1cmUgPSBpc1JlY2VpdmVTaWduYXR1cmU7XG5jb25zdCByZWdleF9qc18xID0gcmVxdWlyZShcIi4uLy4uL3JlZ2V4LmpzXCIpO1xuY29uc3QgZXJyb3JTaWduYXR1cmVSZWdleCA9IC9eZXJyb3IgKD88bmFtZT5bYS16QS1aJF9dW2EtekEtWjAtOSRfXSopXFwoKD88cGFyYW1ldGVycz4uKj8pXFwpJC87XG5mdW5jdGlvbiBpc0Vycm9yU2lnbmF0dXJlKHNpZ25hdHVyZSkge1xuICAgIHJldHVybiBlcnJvclNpZ25hdHVyZVJlZ2V4LnRlc3Qoc2lnbmF0dXJlKTtcbn1cbmZ1bmN0aW9uIGV4ZWNFcnJvclNpZ25hdHVyZShzaWduYXR1cmUpIHtcbiAgICByZXR1cm4gKDAsIHJlZ2V4X2pzXzEuZXhlY1R5cGVkKShlcnJvclNpZ25hdHVyZVJlZ2V4LCBzaWduYXR1cmUpO1xufVxuY29uc3QgZXZlbnRTaWduYXR1cmVSZWdleCA9IC9eZXZlbnQgKD88bmFtZT5bYS16QS1aJF9dW2EtekEtWjAtOSRfXSopXFwoKD88cGFyYW1ldGVycz4uKj8pXFwpJC87XG5mdW5jdGlvbiBpc0V2ZW50U2lnbmF0dXJlKHNpZ25hdHVyZSkge1xuICAgIHJldHVybiBldmVudFNpZ25hdHVyZVJlZ2V4LnRlc3Qoc2lnbmF0dXJlKTtcbn1cbmZ1bmN0aW9uIGV4ZWNFdmVudFNpZ25hdHVyZShzaWduYXR1cmUpIHtcbiAgICByZXR1cm4gKDAsIHJlZ2V4X2pzXzEuZXhlY1R5cGVkKShldmVudFNpZ25hdHVyZVJlZ2V4LCBzaWduYXR1cmUpO1xufVxuY29uc3QgZnVuY3Rpb25TaWduYXR1cmVSZWdleCA9IC9eZnVuY3Rpb24gKD88bmFtZT5bYS16QS1aJF9dW2EtekEtWjAtOSRfXSopXFwoKD88cGFyYW1ldGVycz4uKj8pXFwpKD86ICg/PHNjb3BlPmV4dGVybmFsfHB1YmxpY3sxfSkpPyg/OiAoPzxzdGF0ZU11dGFiaWxpdHk+cHVyZXx2aWV3fG5vbnBheWFibGV8cGF5YWJsZXsxfSkpPyg/OiByZXR1cm5zXFxzP1xcKCg/PHJldHVybnM+Lio/KVxcKSk/JC87XG5mdW5jdGlvbiBpc0Z1bmN0aW9uU2lnbmF0dXJlKHNpZ25hdHVyZSkge1xuICAgIHJldHVybiBmdW5jdGlvblNpZ25hdHVyZVJlZ2V4LnRlc3Qoc2lnbmF0dXJlKTtcbn1cbmZ1bmN0aW9uIGV4ZWNGdW5jdGlvblNpZ25hdHVyZShzaWduYXR1cmUpIHtcbiAgICByZXR1cm4gKDAsIHJlZ2V4X2pzXzEuZXhlY1R5cGVkKShmdW5jdGlvblNpZ25hdHVyZVJlZ2V4LCBzaWduYXR1cmUpO1xufVxuY29uc3Qgc3RydWN0U2lnbmF0dXJlUmVnZXggPSAvXnN0cnVjdCAoPzxuYW1lPlthLXpBLVokX11bYS16QS1aMC05JF9dKikgXFx7KD88cHJvcGVydGllcz4uKj8pXFx9JC87XG5mdW5jdGlvbiBpc1N0cnVjdFNpZ25hdHVyZShzaWduYXR1cmUpIHtcbiAgICByZXR1cm4gc3RydWN0U2lnbmF0dXJlUmVnZXgudGVzdChzaWduYXR1cmUpO1xufVxuZnVuY3Rpb24gZXhlY1N0cnVjdFNpZ25hdHVyZShzaWduYXR1cmUpIHtcbiAgICByZXR1cm4gKDAsIHJlZ2V4X2pzXzEuZXhlY1R5cGVkKShzdHJ1Y3RTaWduYXR1cmVSZWdleCwgc2lnbmF0dXJlKTtcbn1cbmNvbnN0IGNvbnN0cnVjdG9yU2lnbmF0dXJlUmVnZXggPSAvXmNvbnN0cnVjdG9yXFwoKD88cGFyYW1ldGVycz4uKj8pXFwpKD86XFxzKD88c3RhdGVNdXRhYmlsaXR5PnBheWFibGV7MX0pKT8kLztcbmZ1bmN0aW9uIGlzQ29uc3RydWN0b3JTaWduYXR1cmUoc2lnbmF0dXJlKSB7XG4gICAgcmV0dXJuIGNvbnN0cnVjdG9yU2lnbmF0dXJlUmVnZXgudGVzdChzaWduYXR1cmUpO1xufVxuZnVuY3Rpb24gZXhlY0NvbnN0cnVjdG9yU2lnbmF0dXJlKHNpZ25hdHVyZSkge1xuICAgIHJldHVybiAoMCwgcmVnZXhfanNfMS5leGVjVHlwZWQpKGNvbnN0cnVjdG9yU2lnbmF0dXJlUmVnZXgsIHNpZ25hdHVyZSk7XG59XG5jb25zdCBmYWxsYmFja1NpZ25hdHVyZVJlZ2V4ID0gL15mYWxsYmFja1xcKFxcKSBleHRlcm5hbCg/Olxccyg/PHN0YXRlTXV0YWJpbGl0eT5wYXlhYmxlezF9KSk/JC87XG5mdW5jdGlvbiBpc0ZhbGxiYWNrU2lnbmF0dXJlKHNpZ25hdHVyZSkge1xuICAgIHJldHVybiBmYWxsYmFja1NpZ25hdHVyZVJlZ2V4LnRlc3Qoc2lnbmF0dXJlKTtcbn1cbmZ1bmN0aW9uIGV4ZWNGYWxsYmFja1NpZ25hdHVyZShzaWduYXR1cmUpIHtcbiAgICByZXR1cm4gKDAsIHJlZ2V4X2pzXzEuZXhlY1R5cGVkKShmYWxsYmFja1NpZ25hdHVyZVJlZ2V4LCBzaWduYXR1cmUpO1xufVxuY29uc3QgcmVjZWl2ZVNpZ25hdHVyZVJlZ2V4ID0gL15yZWNlaXZlXFwoXFwpIGV4dGVybmFsIHBheWFibGUkLztcbmZ1bmN0aW9uIGlzUmVjZWl2ZVNpZ25hdHVyZShzaWduYXR1cmUpIHtcbiAgICByZXR1cm4gcmVjZWl2ZVNpZ25hdHVyZVJlZ2V4LnRlc3Qoc2lnbmF0dXJlKTtcbn1cbmV4cG9ydHMubW9kaWZpZXJzID0gbmV3IFNldChbXG4gICAgJ21lbW9yeScsXG4gICAgJ2luZGV4ZWQnLFxuICAgICdzdG9yYWdlJyxcbiAgICAnY2FsbGRhdGEnLFxuXSk7XG5leHBvcnRzLmV2ZW50TW9kaWZpZXJzID0gbmV3IFNldChbJ2luZGV4ZWQnXSk7XG5leHBvcnRzLmZ1bmN0aW9uTW9kaWZpZXJzID0gbmV3IFNldChbXG4gICAgJ2NhbGxkYXRhJyxcbiAgICAnbWVtb3J5JyxcbiAgICAnc3RvcmFnZScsXG5dKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNpZ25hdHVyZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/structs.js":
/*!*************************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/runtime/structs.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseStructs = parseStructs;\nconst regex_js_1 = __webpack_require__(/*! ../../regex.js */ \"(ssr)/./node_modules/abitype/dist/cjs/regex.js\");\nconst abiItem_js_1 = __webpack_require__(/*! ../errors/abiItem.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/abiItem.js\");\nconst abiParameter_js_1 = __webpack_require__(/*! ../errors/abiParameter.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/abiParameter.js\");\nconst signature_js_1 = __webpack_require__(/*! ../errors/signature.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/signature.js\");\nconst struct_js_1 = __webpack_require__(/*! ../errors/struct.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/struct.js\");\nconst signatures_js_1 = __webpack_require__(/*! ./signatures.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/utils.js\");\nfunction parseStructs(signatures) {\n    const shallowStructs = {};\n    const signaturesLength = signatures.length;\n    for (let i = 0; i < signaturesLength; i++) {\n        const signature = signatures[i];\n        if (!(0, signatures_js_1.isStructSignature)(signature))\n            continue;\n        const match = (0, signatures_js_1.execStructSignature)(signature);\n        if (!match)\n            throw new signature_js_1.InvalidSignatureError({ signature, type: 'struct' });\n        const properties = match.properties.split(';');\n        const components = [];\n        const propertiesLength = properties.length;\n        for (let k = 0; k < propertiesLength; k++) {\n            const property = properties[k];\n            const trimmed = property.trim();\n            if (!trimmed)\n                continue;\n            const abiParameter = (0, utils_js_1.parseAbiParameter)(trimmed, {\n                type: 'struct',\n            });\n            components.push(abiParameter);\n        }\n        if (!components.length)\n            throw new signature_js_1.InvalidStructSignatureError({ signature });\n        shallowStructs[match.name] = components;\n    }\n    const resolvedStructs = {};\n    const entries = Object.entries(shallowStructs);\n    const entriesLength = entries.length;\n    for (let i = 0; i < entriesLength; i++) {\n        const [name, parameters] = entries[i];\n        resolvedStructs[name] = resolveStructs(parameters, shallowStructs);\n    }\n    return resolvedStructs;\n}\nconst typeWithoutTupleRegex = /^(?<type>[a-zA-Z$_][a-zA-Z0-9$_]*)(?<array>(?:\\[\\d*?\\])+?)?$/;\nfunction resolveStructs(abiParameters, structs, ancestors = new Set()) {\n    const components = [];\n    const length = abiParameters.length;\n    for (let i = 0; i < length; i++) {\n        const abiParameter = abiParameters[i];\n        const isTuple = regex_js_1.isTupleRegex.test(abiParameter.type);\n        if (isTuple)\n            components.push(abiParameter);\n        else {\n            const match = (0, regex_js_1.execTyped)(typeWithoutTupleRegex, abiParameter.type);\n            if (!match?.type)\n                throw new abiParameter_js_1.InvalidAbiTypeParameterError({ abiParameter });\n            const { array, type } = match;\n            if (type in structs) {\n                if (ancestors.has(type))\n                    throw new struct_js_1.CircularReferenceError({ type });\n                components.push({\n                    ...abiParameter,\n                    type: `tuple${array ?? ''}`,\n                    components: resolveStructs(structs[type] ?? [], structs, new Set([...ancestors, type])),\n                });\n            }\n            else {\n                if ((0, utils_js_1.isSolidityType)(type))\n                    components.push(abiParameter);\n                else\n                    throw new abiItem_js_1.UnknownTypeError({ type });\n            }\n        }\n    }\n    return components;\n}\n//# sourceMappingURL=structs.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/structs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/utils.js":
/*!***********************************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/human-readable/runtime/utils.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseSignature = parseSignature;\nexports.parseFunctionSignature = parseFunctionSignature;\nexports.parseEventSignature = parseEventSignature;\nexports.parseErrorSignature = parseErrorSignature;\nexports.parseConstructorSignature = parseConstructorSignature;\nexports.parseFallbackSignature = parseFallbackSignature;\nexports.parseAbiParameter = parseAbiParameter;\nexports.splitParameters = splitParameters;\nexports.isSolidityType = isSolidityType;\nexports.isSolidityKeyword = isSolidityKeyword;\nexports.isValidDataLocation = isValidDataLocation;\nconst regex_js_1 = __webpack_require__(/*! ../../regex.js */ \"(ssr)/./node_modules/abitype/dist/cjs/regex.js\");\nconst abiItem_js_1 = __webpack_require__(/*! ../errors/abiItem.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/abiItem.js\");\nconst abiParameter_js_1 = __webpack_require__(/*! ../errors/abiParameter.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/abiParameter.js\");\nconst signature_js_1 = __webpack_require__(/*! ../errors/signature.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/signature.js\");\nconst splitParameters_js_1 = __webpack_require__(/*! ../errors/splitParameters.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/errors/splitParameters.js\");\nconst cache_js_1 = __webpack_require__(/*! ./cache.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/cache.js\");\nconst signatures_js_1 = __webpack_require__(/*! ./signatures.js */ \"(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js\");\nfunction parseSignature(signature, structs = {}) {\n    if ((0, signatures_js_1.isFunctionSignature)(signature))\n        return parseFunctionSignature(signature, structs);\n    if ((0, signatures_js_1.isEventSignature)(signature))\n        return parseEventSignature(signature, structs);\n    if ((0, signatures_js_1.isErrorSignature)(signature))\n        return parseErrorSignature(signature, structs);\n    if ((0, signatures_js_1.isConstructorSignature)(signature))\n        return parseConstructorSignature(signature, structs);\n    if ((0, signatures_js_1.isFallbackSignature)(signature))\n        return parseFallbackSignature(signature);\n    if ((0, signatures_js_1.isReceiveSignature)(signature))\n        return {\n            type: 'receive',\n            stateMutability: 'payable',\n        };\n    throw new signature_js_1.UnknownSignatureError({ signature });\n}\nfunction parseFunctionSignature(signature, structs = {}) {\n    const match = (0, signatures_js_1.execFunctionSignature)(signature);\n    if (!match)\n        throw new signature_js_1.InvalidSignatureError({ signature, type: 'function' });\n    const inputParams = splitParameters(match.parameters);\n    const inputs = [];\n    const inputLength = inputParams.length;\n    for (let i = 0; i < inputLength; i++) {\n        inputs.push(parseAbiParameter(inputParams[i], {\n            modifiers: signatures_js_1.functionModifiers,\n            structs,\n            type: 'function',\n        }));\n    }\n    const outputs = [];\n    if (match.returns) {\n        const outputParams = splitParameters(match.returns);\n        const outputLength = outputParams.length;\n        for (let i = 0; i < outputLength; i++) {\n            outputs.push(parseAbiParameter(outputParams[i], {\n                modifiers: signatures_js_1.functionModifiers,\n                structs,\n                type: 'function',\n            }));\n        }\n    }\n    return {\n        name: match.name,\n        type: 'function',\n        stateMutability: match.stateMutability ?? 'nonpayable',\n        inputs,\n        outputs,\n    };\n}\nfunction parseEventSignature(signature, structs = {}) {\n    const match = (0, signatures_js_1.execEventSignature)(signature);\n    if (!match)\n        throw new signature_js_1.InvalidSignatureError({ signature, type: 'event' });\n    const params = splitParameters(match.parameters);\n    const abiParameters = [];\n    const length = params.length;\n    for (let i = 0; i < length; i++)\n        abiParameters.push(parseAbiParameter(params[i], {\n            modifiers: signatures_js_1.eventModifiers,\n            structs,\n            type: 'event',\n        }));\n    return { name: match.name, type: 'event', inputs: abiParameters };\n}\nfunction parseErrorSignature(signature, structs = {}) {\n    const match = (0, signatures_js_1.execErrorSignature)(signature);\n    if (!match)\n        throw new signature_js_1.InvalidSignatureError({ signature, type: 'error' });\n    const params = splitParameters(match.parameters);\n    const abiParameters = [];\n    const length = params.length;\n    for (let i = 0; i < length; i++)\n        abiParameters.push(parseAbiParameter(params[i], { structs, type: 'error' }));\n    return { name: match.name, type: 'error', inputs: abiParameters };\n}\nfunction parseConstructorSignature(signature, structs = {}) {\n    const match = (0, signatures_js_1.execConstructorSignature)(signature);\n    if (!match)\n        throw new signature_js_1.InvalidSignatureError({ signature, type: 'constructor' });\n    const params = splitParameters(match.parameters);\n    const abiParameters = [];\n    const length = params.length;\n    for (let i = 0; i < length; i++)\n        abiParameters.push(parseAbiParameter(params[i], { structs, type: 'constructor' }));\n    return {\n        type: 'constructor',\n        stateMutability: match.stateMutability ?? 'nonpayable',\n        inputs: abiParameters,\n    };\n}\nfunction parseFallbackSignature(signature) {\n    const match = (0, signatures_js_1.execFallbackSignature)(signature);\n    if (!match)\n        throw new signature_js_1.InvalidSignatureError({ signature, type: 'fallback' });\n    return {\n        type: 'fallback',\n        stateMutability: match.stateMutability ?? 'nonpayable',\n    };\n}\nconst abiParameterWithoutTupleRegex = /^(?<type>[a-zA-Z$_][a-zA-Z0-9$_]*)(?<array>(?:\\[\\d*?\\])+?)?(?:\\s(?<modifier>calldata|indexed|memory|storage{1}))?(?:\\s(?<name>[a-zA-Z$_][a-zA-Z0-9$_]*))?$/;\nconst abiParameterWithTupleRegex = /^\\((?<type>.+?)\\)(?<array>(?:\\[\\d*?\\])+?)?(?:\\s(?<modifier>calldata|indexed|memory|storage{1}))?(?:\\s(?<name>[a-zA-Z$_][a-zA-Z0-9$_]*))?$/;\nconst dynamicIntegerRegex = /^u?int$/;\nfunction parseAbiParameter(param, options) {\n    const parameterCacheKey = (0, cache_js_1.getParameterCacheKey)(param, options?.type, options?.structs);\n    if (cache_js_1.parameterCache.has(parameterCacheKey))\n        return cache_js_1.parameterCache.get(parameterCacheKey);\n    const isTuple = regex_js_1.isTupleRegex.test(param);\n    const match = (0, regex_js_1.execTyped)(isTuple ? abiParameterWithTupleRegex : abiParameterWithoutTupleRegex, param);\n    if (!match)\n        throw new abiParameter_js_1.InvalidParameterError({ param });\n    if (match.name && isSolidityKeyword(match.name))\n        throw new abiParameter_js_1.SolidityProtectedKeywordError({ param, name: match.name });\n    const name = match.name ? { name: match.name } : {};\n    const indexed = match.modifier === 'indexed' ? { indexed: true } : {};\n    const structs = options?.structs ?? {};\n    let type;\n    let components = {};\n    if (isTuple) {\n        type = 'tuple';\n        const params = splitParameters(match.type);\n        const components_ = [];\n        const length = params.length;\n        for (let i = 0; i < length; i++) {\n            components_.push(parseAbiParameter(params[i], { structs }));\n        }\n        components = { components: components_ };\n    }\n    else if (match.type in structs) {\n        type = 'tuple';\n        components = { components: structs[match.type] };\n    }\n    else if (dynamicIntegerRegex.test(match.type)) {\n        type = `${match.type}256`;\n    }\n    else {\n        type = match.type;\n        if (!(options?.type === 'struct') && !isSolidityType(type))\n            throw new abiItem_js_1.UnknownSolidityTypeError({ type });\n    }\n    if (match.modifier) {\n        if (!options?.modifiers?.has?.(match.modifier))\n            throw new abiParameter_js_1.InvalidModifierError({\n                param,\n                type: options?.type,\n                modifier: match.modifier,\n            });\n        if (signatures_js_1.functionModifiers.has(match.modifier) &&\n            !isValidDataLocation(type, !!match.array))\n            throw new abiParameter_js_1.InvalidFunctionModifierError({\n                param,\n                type: options?.type,\n                modifier: match.modifier,\n            });\n    }\n    const abiParameter = {\n        type: `${type}${match.array ?? ''}`,\n        ...name,\n        ...indexed,\n        ...components,\n    };\n    cache_js_1.parameterCache.set(parameterCacheKey, abiParameter);\n    return abiParameter;\n}\nfunction splitParameters(params, result = [], current = '', depth = 0) {\n    const length = params.trim().length;\n    for (let i = 0; i < length; i++) {\n        const char = params[i];\n        const tail = params.slice(i + 1);\n        switch (char) {\n            case ',':\n                return depth === 0\n                    ? splitParameters(tail, [...result, current.trim()])\n                    : splitParameters(tail, result, `${current}${char}`, depth);\n            case '(':\n                return splitParameters(tail, result, `${current}${char}`, depth + 1);\n            case ')':\n                return splitParameters(tail, result, `${current}${char}`, depth - 1);\n            default:\n                return splitParameters(tail, result, `${current}${char}`, depth);\n        }\n    }\n    if (current === '')\n        return result;\n    if (depth !== 0)\n        throw new splitParameters_js_1.InvalidParenthesisError({ current, depth });\n    result.push(current.trim());\n    return result;\n}\nfunction isSolidityType(type) {\n    return (type === 'address' ||\n        type === 'bool' ||\n        type === 'function' ||\n        type === 'string' ||\n        regex_js_1.bytesRegex.test(type) ||\n        regex_js_1.integerRegex.test(type));\n}\nconst protectedKeywordsRegex = /^(?:after|alias|anonymous|apply|auto|byte|calldata|case|catch|constant|copyof|default|defined|error|event|external|false|final|function|immutable|implements|in|indexed|inline|internal|let|mapping|match|memory|mutable|null|of|override|partial|private|promise|public|pure|reference|relocatable|return|returns|sizeof|static|storage|struct|super|supports|switch|this|true|try|typedef|typeof|var|view|virtual)$/;\nfunction isSolidityKeyword(name) {\n    return (name === 'address' ||\n        name === 'bool' ||\n        name === 'function' ||\n        name === 'string' ||\n        name === 'tuple' ||\n        regex_js_1.bytesRegex.test(name) ||\n        regex_js_1.integerRegex.test(name) ||\n        protectedKeywordsRegex.test(name));\n}\nfunction isValidDataLocation(type, isArray) {\n    return isArray || type === 'bytes' || type === 'string' || type === 'tuple';\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/human-readable/runtime/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/narrow.js":
/*!*************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/narrow.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.narrow = narrow;\nfunction narrow(value) {\n    return value;\n}\n//# sourceMappingURL=narrow.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2Nqcy9uYXJyb3cuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9janMvbmFycm93LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5uYXJyb3cgPSBuYXJyb3c7XG5mdW5jdGlvbiBuYXJyb3codmFsdWUpIHtcbiAgICByZXR1cm4gdmFsdWU7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1uYXJyb3cuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/narrow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/regex.js":
/*!************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/regex.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isTupleRegex = exports.integerRegex = exports.bytesRegex = void 0;\nexports.execTyped = execTyped;\nfunction execTyped(regex, string) {\n    const match = regex.exec(string);\n    return match?.groups;\n}\nexports.bytesRegex = /^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/;\nexports.integerRegex = /^u?int(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/;\nexports.isTupleRegex = /^\\(.+?\\).*?$/;\n//# sourceMappingURL=regex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2Nqcy9yZWdleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxvQkFBb0IsR0FBRyxvQkFBb0IsR0FBRyxrQkFBa0I7QUFDaEUsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCLG9CQUFvQjtBQUNwQixvQkFBb0I7QUFDcEIiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2Nqcy9yZWdleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuaXNUdXBsZVJlZ2V4ID0gZXhwb3J0cy5pbnRlZ2VyUmVnZXggPSBleHBvcnRzLmJ5dGVzUmVnZXggPSB2b2lkIDA7XG5leHBvcnRzLmV4ZWNUeXBlZCA9IGV4ZWNUeXBlZDtcbmZ1bmN0aW9uIGV4ZWNUeXBlZChyZWdleCwgc3RyaW5nKSB7XG4gICAgY29uc3QgbWF0Y2ggPSByZWdleC5leGVjKHN0cmluZyk7XG4gICAgcmV0dXJuIG1hdGNoPy5ncm91cHM7XG59XG5leHBvcnRzLmJ5dGVzUmVnZXggPSAvXmJ5dGVzKFsxLTldfDFbMC05XXwyWzAtOV18M1swLTJdKT8kLztcbmV4cG9ydHMuaW50ZWdlclJlZ2V4ID0gL151P2ludCg4fDE2fDI0fDMyfDQwfDQ4fDU2fDY0fDcyfDgwfDg4fDk2fDEwNHwxMTJ8MTIwfDEyOHwxMzZ8MTQ0fDE1MnwxNjB8MTY4fDE3NnwxODR8MTkyfDIwMHwyMDh8MjE2fDIyNHwyMzJ8MjQwfDI0OHwyNTYpPyQvO1xuZXhwb3J0cy5pc1R1cGxlUmVnZXggPSAvXlxcKC4rP1xcKS4qPyQvO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVnZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/regex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/cjs/version.js":
/*!**************************************************!*\
  !*** ./node_modules/abitype/dist/cjs/version.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.version = void 0;\nexports.version = '1.0.8';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2Nqcy92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGVBQWU7QUFDZixlQUFlO0FBQ2YiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2Nqcy92ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy52ZXJzaW9uID0gdm9pZCAwO1xuZXhwb3J0cy52ZXJzaW9uID0gJzEuMC44Jztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/cjs/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/errors.js":
/*!*************************************************!*\
  !*** ./node_modules/abitype/dist/esm/errors.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseError: () => (/* binding */ BaseError)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version.js */ \"(ssr)/./node_modules/abitype/dist/esm/version.js\");\n\nclass BaseError extends Error {\n    constructor(shortMessage, args = {}) {\n        const details = args.cause instanceof BaseError\n            ? args.cause.details\n            : args.cause?.message\n                ? args.cause.message\n                : args.details;\n        const docsPath = args.cause instanceof BaseError\n            ? args.cause.docsPath || args.docsPath\n            : args.docsPath;\n        const message = [\n            shortMessage || 'An error occurred.',\n            '',\n            ...(args.metaMessages ? [...args.metaMessages, ''] : []),\n            ...(docsPath ? [`Docs: https://abitype.dev${docsPath}`] : []),\n            ...(details ? [`Details: ${details}`] : []),\n            `Version: abitype@${_version_js__WEBPACK_IMPORTED_MODULE_0__.version}`,\n        ].join('\\n');\n        super(message);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"metaMessages\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'AbiTypeError'\n        });\n        if (args.cause)\n            this.cause = args.cause;\n        this.details = details;\n        this.docsPath = docsPath;\n        this.metaMessages = args.metaMessages;\n        this.shortMessage = shortMessage;\n    }\n}\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS9lcnJvcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUM7QUFDaEM7QUFDUCx1Q0FBdUM7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0RBQXdELFNBQVM7QUFDakUsdUNBQXVDLFFBQVE7QUFDL0MsZ0NBQWdDLGdEQUFPLENBQUM7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL2FrYXNoL2NoZXRhbi9zcGVjdHJhbWludC9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS9lcnJvcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdmVyc2lvbiB9IGZyb20gJy4vdmVyc2lvbi5qcyc7XG5leHBvcnQgY2xhc3MgQmFzZUVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKHNob3J0TWVzc2FnZSwgYXJncyA9IHt9KSB7XG4gICAgICAgIGNvbnN0IGRldGFpbHMgPSBhcmdzLmNhdXNlIGluc3RhbmNlb2YgQmFzZUVycm9yXG4gICAgICAgICAgICA/IGFyZ3MuY2F1c2UuZGV0YWlsc1xuICAgICAgICAgICAgOiBhcmdzLmNhdXNlPy5tZXNzYWdlXG4gICAgICAgICAgICAgICAgPyBhcmdzLmNhdXNlLm1lc3NhZ2VcbiAgICAgICAgICAgICAgICA6IGFyZ3MuZGV0YWlscztcbiAgICAgICAgY29uc3QgZG9jc1BhdGggPSBhcmdzLmNhdXNlIGluc3RhbmNlb2YgQmFzZUVycm9yXG4gICAgICAgICAgICA/IGFyZ3MuY2F1c2UuZG9jc1BhdGggfHwgYXJncy5kb2NzUGF0aFxuICAgICAgICAgICAgOiBhcmdzLmRvY3NQYXRoO1xuICAgICAgICBjb25zdCBtZXNzYWdlID0gW1xuICAgICAgICAgICAgc2hvcnRNZXNzYWdlIHx8ICdBbiBlcnJvciBvY2N1cnJlZC4nLFxuICAgICAgICAgICAgJycsXG4gICAgICAgICAgICAuLi4oYXJncy5tZXRhTWVzc2FnZXMgPyBbLi4uYXJncy5tZXRhTWVzc2FnZXMsICcnXSA6IFtdKSxcbiAgICAgICAgICAgIC4uLihkb2NzUGF0aCA/IFtgRG9jczogaHR0cHM6Ly9hYml0eXBlLmRldiR7ZG9jc1BhdGh9YF0gOiBbXSksXG4gICAgICAgICAgICAuLi4oZGV0YWlscyA/IFtgRGV0YWlsczogJHtkZXRhaWxzfWBdIDogW10pLFxuICAgICAgICAgICAgYFZlcnNpb246IGFiaXR5cGVAJHt2ZXJzaW9ufWAsXG4gICAgICAgIF0uam9pbignXFxuJyk7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJkZXRhaWxzXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiB2b2lkIDBcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcImRvY3NQYXRoXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiB2b2lkIDBcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm1ldGFNZXNzYWdlc1wiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogdm9pZCAwXG4gICAgICAgIH0pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJzaG9ydE1lc3NhZ2VcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IHZvaWQgMFxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogJ0FiaVR5cGVFcnJvcidcbiAgICAgICAgfSk7XG4gICAgICAgIGlmIChhcmdzLmNhdXNlKVxuICAgICAgICAgICAgdGhpcy5jYXVzZSA9IGFyZ3MuY2F1c2U7XG4gICAgICAgIHRoaXMuZGV0YWlscyA9IGRldGFpbHM7XG4gICAgICAgIHRoaXMuZG9jc1BhdGggPSBkb2NzUGF0aDtcbiAgICAgICAgdGhpcy5tZXRhTWVzc2FnZXMgPSBhcmdzLm1ldGFNZXNzYWdlcztcbiAgICAgICAgdGhpcy5zaG9ydE1lc3NhZ2UgPSBzaG9ydE1lc3NhZ2U7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZXJyb3JzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiItem.js":
/*!************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/errors/abiItem.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidAbiItemError: () => (/* binding */ InvalidAbiItemError),\n/* harmony export */   UnknownSolidityTypeError: () => (/* binding */ UnknownSolidityTypeError),\n/* harmony export */   UnknownTypeError: () => (/* binding */ UnknownTypeError)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors.js */ \"(ssr)/./node_modules/abitype/dist/esm/errors.js\");\n\nclass InvalidAbiItemError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ signature }) {\n        super('Failed to parse ABI item.', {\n            details: `parseAbiItem(${JSON.stringify(signature, null, 2)})`,\n            docsPath: '/api/human#parseabiitem-1',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiItemError'\n        });\n    }\n}\nclass UnknownTypeError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ type }) {\n        super('Unknown type.', {\n            metaMessages: [\n                `Type \"${type}\" is not a valid ABI type. Perhaps you forgot to include a struct signature?`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'UnknownTypeError'\n        });\n    }\n}\nclass UnknownSolidityTypeError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ type }) {\n        super('Unknown type.', {\n            metaMessages: [`Type \"${type}\" is not a valid ABI type.`],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'UnknownSolidityTypeError'\n        });\n    }\n}\n//# sourceMappingURL=abiItem.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidAbiParameterError: () => (/* binding */ InvalidAbiParameterError),\n/* harmony export */   InvalidAbiParametersError: () => (/* binding */ InvalidAbiParametersError),\n/* harmony export */   InvalidAbiTypeParameterError: () => (/* binding */ InvalidAbiTypeParameterError),\n/* harmony export */   InvalidFunctionModifierError: () => (/* binding */ InvalidFunctionModifierError),\n/* harmony export */   InvalidModifierError: () => (/* binding */ InvalidModifierError),\n/* harmony export */   InvalidParameterError: () => (/* binding */ InvalidParameterError),\n/* harmony export */   SolidityProtectedKeywordError: () => (/* binding */ SolidityProtectedKeywordError)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors.js */ \"(ssr)/./node_modules/abitype/dist/esm/errors.js\");\n\nclass InvalidAbiParameterError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ param }) {\n        super('Failed to parse ABI parameter.', {\n            details: `parseAbiParameter(${JSON.stringify(param, null, 2)})`,\n            docsPath: '/api/human#parseabiparameter-1',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiParameterError'\n        });\n    }\n}\nclass InvalidAbiParametersError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ params }) {\n        super('Failed to parse ABI parameters.', {\n            details: `parseAbiParameters(${JSON.stringify(params, null, 2)})`,\n            docsPath: '/api/human#parseabiparameters-1',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiParametersError'\n        });\n    }\n}\nclass InvalidParameterError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ param }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidParameterError'\n        });\n    }\n}\nclass SolidityProtectedKeywordError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ param, name }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n            metaMessages: [\n                `\"${name}\" is a protected Solidity keyword. More info: https://docs.soliditylang.org/en/latest/cheatsheet.html`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'SolidityProtectedKeywordError'\n        });\n    }\n}\nclass InvalidModifierError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ param, type, modifier, }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n            metaMessages: [\n                `Modifier \"${modifier}\" not allowed${type ? ` in \"${type}\" type` : ''}.`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidModifierError'\n        });\n    }\n}\nclass InvalidFunctionModifierError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ param, type, modifier, }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n            metaMessages: [\n                `Modifier \"${modifier}\" not allowed${type ? ` in \"${type}\" type` : ''}.`,\n                `Data location can only be specified for array, struct, or mapping types, but \"${modifier}\" was given.`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidFunctionModifierError'\n        });\n    }\n}\nclass InvalidAbiTypeParameterError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ abiParameter, }) {\n        super('Invalid ABI parameter.', {\n            details: JSON.stringify(abiParameter, null, 2),\n            metaMessages: ['ABI parameter type is invalid.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiTypeParameterError'\n        });\n    }\n}\n//# sourceMappingURL=abiParameter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/signature.js":
/*!**************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/errors/signature.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidSignatureError: () => (/* binding */ InvalidSignatureError),\n/* harmony export */   InvalidStructSignatureError: () => (/* binding */ InvalidStructSignatureError),\n/* harmony export */   UnknownSignatureError: () => (/* binding */ UnknownSignatureError)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors.js */ \"(ssr)/./node_modules/abitype/dist/esm/errors.js\");\n\nclass InvalidSignatureError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ signature, type, }) {\n        super(`Invalid ${type} signature.`, {\n            details: signature,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidSignatureError'\n        });\n    }\n}\nclass UnknownSignatureError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ signature }) {\n        super('Unknown signature.', {\n            details: signature,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'UnknownSignatureError'\n        });\n    }\n}\nclass InvalidStructSignatureError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ signature }) {\n        super('Invalid struct signature.', {\n            details: signature,\n            metaMessages: ['No properties exist.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidStructSignatureError'\n        });\n    }\n}\n//# sourceMappingURL=signature.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/signature.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/splitParameters.js":
/*!********************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/errors/splitParameters.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidParenthesisError: () => (/* binding */ InvalidParenthesisError)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors.js */ \"(ssr)/./node_modules/abitype/dist/esm/errors.js\");\n\nclass InvalidParenthesisError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ current, depth }) {\n        super('Unbalanced parentheses.', {\n            metaMessages: [\n                `\"${current.trim()}\" has too many ${depth > 0 ? 'opening' : 'closing'} parentheses.`,\n            ],\n            details: `Depth \"${depth}\"`,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidParenthesisError'\n        });\n    }\n}\n//# sourceMappingURL=splitParameters.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS9odW1hbi1yZWFkYWJsZS9lcnJvcnMvc3BsaXRQYXJhbWV0ZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRDO0FBQ3JDLHNDQUFzQyxpREFBUztBQUN0RCxrQkFBa0IsZ0JBQWdCO0FBQ2xDO0FBQ0E7QUFDQSxvQkFBb0IsZUFBZSxpQkFBaUIsbUNBQW1DO0FBQ3ZGO0FBQ0EsK0JBQStCLE1BQU07QUFDckMsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9lc20vaHVtYW4tcmVhZGFibGUvZXJyb3JzL3NwbGl0UGFyYW1ldGVycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCYXNlRXJyb3IgfSBmcm9tICcuLi8uLi9lcnJvcnMuanMnO1xuZXhwb3J0IGNsYXNzIEludmFsaWRQYXJlbnRoZXNpc0Vycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcih7IGN1cnJlbnQsIGRlcHRoIH0pIHtcbiAgICAgICAgc3VwZXIoJ1VuYmFsYW5jZWQgcGFyZW50aGVzZXMuJywge1xuICAgICAgICAgICAgbWV0YU1lc3NhZ2VzOiBbXG4gICAgICAgICAgICAgICAgYFwiJHtjdXJyZW50LnRyaW0oKX1cIiBoYXMgdG9vIG1hbnkgJHtkZXB0aCA+IDAgPyAnb3BlbmluZycgOiAnY2xvc2luZyd9IHBhcmVudGhlc2VzLmAsXG4gICAgICAgICAgICBdLFxuICAgICAgICAgICAgZGV0YWlsczogYERlcHRoIFwiJHtkZXB0aH1cImAsXG4gICAgICAgIH0pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJuYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiAnSW52YWxpZFBhcmVudGhlc2lzRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNwbGl0UGFyYW1ldGVycy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/splitParameters.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/struct.js":
/*!***********************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/errors/struct.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CircularReferenceError: () => (/* binding */ CircularReferenceError)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors.js */ \"(ssr)/./node_modules/abitype/dist/esm/errors.js\");\n\nclass CircularReferenceError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ type }) {\n        super('Circular reference detected.', {\n            metaMessages: [`Struct \"${type}\" is a circular reference.`],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'CircularReferenceError'\n        });\n    }\n}\n//# sourceMappingURL=struct.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS9odW1hbi1yZWFkYWJsZS9lcnJvcnMvc3RydWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRDO0FBQ3JDLHFDQUFxQyxpREFBUztBQUNyRCxrQkFBa0IsTUFBTTtBQUN4QjtBQUNBLHNDQUFzQyxLQUFLO0FBQzNDLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9hYml0eXBlL2Rpc3QvZXNtL2h1bWFuLXJlYWRhYmxlL2Vycm9ycy9zdHJ1Y3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZUVycm9yIH0gZnJvbSAnLi4vLi4vZXJyb3JzLmpzJztcbmV4cG9ydCBjbGFzcyBDaXJjdWxhclJlZmVyZW5jZUVycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcih7IHR5cGUgfSkge1xuICAgICAgICBzdXBlcignQ2lyY3VsYXIgcmVmZXJlbmNlIGRldGVjdGVkLicsIHtcbiAgICAgICAgICAgIG1ldGFNZXNzYWdlczogW2BTdHJ1Y3QgXCIke3R5cGV9XCIgaXMgYSBjaXJjdWxhciByZWZlcmVuY2UuYF0sXG4gICAgICAgIH0pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJuYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiAnQ2lyY3VsYXJSZWZlcmVuY2VFcnJvcidcbiAgICAgICAgfSk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3RydWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/struct.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/formatAbiItem.js":
/*!***********************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/formatAbiItem.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatAbiItem: () => (/* binding */ formatAbiItem)\n/* harmony export */ });\n/* harmony import */ var _formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatAbiParameters.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/formatAbiParameters.js\");\n\n/**\n * Formats ABI item (e.g. error, event, function) into human-readable ABI item\n *\n * @param abiItem - ABI item\n * @returns Human-readable ABI item\n */\nfunction formatAbiItem(abiItem) {\n    if (abiItem.type === 'function')\n        return `function ${abiItem.name}(${(0,_formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameters)(abiItem.inputs)})${abiItem.stateMutability && abiItem.stateMutability !== 'nonpayable'\n            ? ` ${abiItem.stateMutability}`\n            : ''}${abiItem.outputs?.length\n            ? ` returns (${(0,_formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameters)(abiItem.outputs)})`\n            : ''}`;\n    if (abiItem.type === 'event')\n        return `event ${abiItem.name}(${(0,_formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameters)(abiItem.inputs)})`;\n    if (abiItem.type === 'error')\n        return `error ${abiItem.name}(${(0,_formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameters)(abiItem.inputs)})`;\n    if (abiItem.type === 'constructor')\n        return `constructor(${(0,_formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameters)(abiItem.inputs)})${abiItem.stateMutability === 'payable' ? ' payable' : ''}`;\n    if (abiItem.type === 'fallback')\n        return `fallback() external${abiItem.stateMutability === 'payable' ? ' payable' : ''}`;\n    return 'receive() external payable';\n}\n//# sourceMappingURL=formatAbiItem.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/formatAbiItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/formatAbiParameter.js":
/*!****************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/formatAbiParameter.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatAbiParameter: () => (/* binding */ formatAbiParameter)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../regex.js */ \"(ssr)/./node_modules/abitype/dist/esm/regex.js\");\n\n// https://regexr.com/7f7rv\nconst tupleRegex = /^tuple(?<array>(\\[(\\d*)\\])*)$/;\n/**\n * Formats {@link AbiParameter} to human-readable ABI parameter.\n *\n * @param abiParameter - ABI parameter\n * @returns Human-readable ABI parameter\n *\n * @example\n * const result = formatAbiParameter({ type: 'address', name: 'from' })\n * //    ^? const result: 'address from'\n */\nfunction formatAbiParameter(abiParameter) {\n    let type = abiParameter.type;\n    if (tupleRegex.test(abiParameter.type) && 'components' in abiParameter) {\n        type = '(';\n        const length = abiParameter.components.length;\n        for (let i = 0; i < length; i++) {\n            const component = abiParameter.components[i];\n            type += formatAbiParameter(component);\n            if (i < length - 1)\n                type += ', ';\n        }\n        const result = (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(tupleRegex, abiParameter.type);\n        type += `)${result?.array ?? ''}`;\n        return formatAbiParameter({\n            ...abiParameter,\n            type,\n        });\n    }\n    // Add `indexed` to type if in `abiParameter`\n    if ('indexed' in abiParameter && abiParameter.indexed)\n        type = `${type} indexed`;\n    // Return human-readable ABI parameter\n    if (abiParameter.name)\n        return `${type} ${abiParameter.name}`;\n    return type;\n}\n//# sourceMappingURL=formatAbiParameter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/formatAbiParameter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/formatAbiParameters.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/formatAbiParameters.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatAbiParameters: () => (/* binding */ formatAbiParameters)\n/* harmony export */ });\n/* harmony import */ var _formatAbiParameter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatAbiParameter.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/formatAbiParameter.js\");\n\n/**\n * Formats {@link AbiParameter}s to human-readable ABI parameters.\n *\n * @param abiParameters - ABI parameters\n * @returns Human-readable ABI parameters\n *\n * @example\n * const result = formatAbiParameters([\n *   //  ^? const result: 'address from, uint256 tokenId'\n *   { type: 'address', name: 'from' },\n *   { type: 'uint256', name: 'tokenId' },\n * ])\n */\nfunction formatAbiParameters(abiParameters) {\n    let params = '';\n    const length = abiParameters.length;\n    for (let i = 0; i < length; i++) {\n        const abiParameter = abiParameters[i];\n        params += (0,_formatAbiParameter_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameter)(abiParameter);\n        if (i !== length - 1)\n            params += ', ';\n    }\n    return params;\n}\n//# sourceMappingURL=formatAbiParameters.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS9odW1hbi1yZWFkYWJsZS9mb3JtYXRBYmlQYXJhbWV0ZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQThEO0FBQzlEO0FBQ0EsWUFBWSxtQkFBbUI7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPLCtCQUErQjtBQUN0QyxPQUFPLGtDQUFrQztBQUN6QztBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0Esb0JBQW9CLFlBQVk7QUFDaEM7QUFDQSxrQkFBa0IsMEVBQWtCO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9hYml0eXBlL2Rpc3QvZXNtL2h1bWFuLXJlYWRhYmxlL2Zvcm1hdEFiaVBhcmFtZXRlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZm9ybWF0QWJpUGFyYW1ldGVyLCB9IGZyb20gJy4vZm9ybWF0QWJpUGFyYW1ldGVyLmpzJztcbi8qKlxuICogRm9ybWF0cyB7QGxpbmsgQWJpUGFyYW1ldGVyfXMgdG8gaHVtYW4tcmVhZGFibGUgQUJJIHBhcmFtZXRlcnMuXG4gKlxuICogQHBhcmFtIGFiaVBhcmFtZXRlcnMgLSBBQkkgcGFyYW1ldGVyc1xuICogQHJldHVybnMgSHVtYW4tcmVhZGFibGUgQUJJIHBhcmFtZXRlcnNcbiAqXG4gKiBAZXhhbXBsZVxuICogY29uc3QgcmVzdWx0ID0gZm9ybWF0QWJpUGFyYW1ldGVycyhbXG4gKiAgIC8vICBePyBjb25zdCByZXN1bHQ6ICdhZGRyZXNzIGZyb20sIHVpbnQyNTYgdG9rZW5JZCdcbiAqICAgeyB0eXBlOiAnYWRkcmVzcycsIG5hbWU6ICdmcm9tJyB9LFxuICogICB7IHR5cGU6ICd1aW50MjU2JywgbmFtZTogJ3Rva2VuSWQnIH0sXG4gKiBdKVxuICovXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0QWJpUGFyYW1ldGVycyhhYmlQYXJhbWV0ZXJzKSB7XG4gICAgbGV0IHBhcmFtcyA9ICcnO1xuICAgIGNvbnN0IGxlbmd0aCA9IGFiaVBhcmFtZXRlcnMubGVuZ3RoO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbGVuZ3RoOyBpKyspIHtcbiAgICAgICAgY29uc3QgYWJpUGFyYW1ldGVyID0gYWJpUGFyYW1ldGVyc1tpXTtcbiAgICAgICAgcGFyYW1zICs9IGZvcm1hdEFiaVBhcmFtZXRlcihhYmlQYXJhbWV0ZXIpO1xuICAgICAgICBpZiAoaSAhPT0gbGVuZ3RoIC0gMSlcbiAgICAgICAgICAgIHBhcmFtcyArPSAnLCAnO1xuICAgIH1cbiAgICByZXR1cm4gcGFyYW1zO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Zm9ybWF0QWJpUGFyYW1ldGVycy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/formatAbiParameters.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/parseAbi.js":
/*!******************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/parseAbi.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseAbi: () => (/* binding */ parseAbi)\n/* harmony export */ });\n/* harmony import */ var _runtime_signatures_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./runtime/signatures.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/signatures.js\");\n/* harmony import */ var _runtime_structs_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./runtime/structs.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/structs.js\");\n/* harmony import */ var _runtime_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/utils.js\");\n\n\n\n/**\n * Parses human-readable ABI into JSON {@link Abi}\n *\n * @param signatures - Human-Readable ABI\n * @returns Parsed {@link Abi}\n *\n * @example\n * const abi = parseAbi([\n *   //  ^? const abi: readonly [{ name: \"balanceOf\"; type: \"function\"; stateMutability:...\n *   'function balanceOf(address owner) view returns (uint256)',\n *   'event Transfer(address indexed from, address indexed to, uint256 amount)',\n * ])\n */\nfunction parseAbi(signatures) {\n    const structs = (0,_runtime_structs_js__WEBPACK_IMPORTED_MODULE_0__.parseStructs)(signatures);\n    const abi = [];\n    const length = signatures.length;\n    for (let i = 0; i < length; i++) {\n        const signature = signatures[i];\n        if ((0,_runtime_signatures_js__WEBPACK_IMPORTED_MODULE_1__.isStructSignature)(signature))\n            continue;\n        abi.push((0,_runtime_utils_js__WEBPACK_IMPORTED_MODULE_2__.parseSignature)(signature, structs));\n    }\n    return abi;\n}\n//# sourceMappingURL=parseAbi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/parseAbi.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/parseAbiItem.js":
/*!**********************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/parseAbiItem.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseAbiItem: () => (/* binding */ parseAbiItem)\n/* harmony export */ });\n/* harmony import */ var _errors_abiItem_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./errors/abiItem.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiItem.js\");\n/* harmony import */ var _runtime_signatures_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./runtime/signatures.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/signatures.js\");\n/* harmony import */ var _runtime_structs_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./runtime/structs.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/structs.js\");\n/* harmony import */ var _runtime_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/utils.js\");\n\n\n\n\n/**\n * Parses human-readable ABI item (e.g. error, event, function) into {@link Abi} item\n *\n * @param signature - Human-readable ABI item\n * @returns Parsed {@link Abi} item\n *\n * @example\n * const abiItem = parseAbiItem('function balanceOf(address owner) view returns (uint256)')\n * //    ^? const abiItem: { name: \"balanceOf\"; type: \"function\"; stateMutability: \"view\";...\n *\n * @example\n * const abiItem = parseAbiItem([\n *   //  ^? const abiItem: { name: \"foo\"; type: \"function\"; stateMutability: \"view\"; inputs:...\n *   'function foo(Baz bar) view returns (string)',\n *   'struct Baz { string name; }',\n * ])\n */\nfunction parseAbiItem(signature) {\n    let abiItem;\n    if (typeof signature === 'string')\n        abiItem = (0,_runtime_utils_js__WEBPACK_IMPORTED_MODULE_0__.parseSignature)(signature);\n    else {\n        const structs = (0,_runtime_structs_js__WEBPACK_IMPORTED_MODULE_1__.parseStructs)(signature);\n        const length = signature.length;\n        for (let i = 0; i < length; i++) {\n            const signature_ = signature[i];\n            if ((0,_runtime_signatures_js__WEBPACK_IMPORTED_MODULE_2__.isStructSignature)(signature_))\n                continue;\n            abiItem = (0,_runtime_utils_js__WEBPACK_IMPORTED_MODULE_0__.parseSignature)(signature_, structs);\n            break;\n        }\n    }\n    if (!abiItem)\n        throw new _errors_abiItem_js__WEBPACK_IMPORTED_MODULE_3__.InvalidAbiItemError({ signature });\n    return abiItem;\n}\n//# sourceMappingURL=parseAbiItem.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/parseAbiItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/parseAbiParameters.js":
/*!****************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/parseAbiParameters.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseAbiParameters: () => (/* binding */ parseAbiParameters)\n/* harmony export */ });\n/* harmony import */ var _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./errors/abiParameter.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js\");\n/* harmony import */ var _runtime_signatures_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./runtime/signatures.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/signatures.js\");\n/* harmony import */ var _runtime_structs_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./runtime/structs.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/structs.js\");\n/* harmony import */ var _runtime_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/utils.js\");\n\n\n\n\n\n/**\n * Parses human-readable ABI parameters into {@link AbiParameter}s\n *\n * @param params - Human-readable ABI parameters\n * @returns Parsed {@link AbiParameter}s\n *\n * @example\n * const abiParameters = parseAbiParameters('address from, address to, uint256 amount')\n * //    ^? const abiParameters: [{ type: \"address\"; name: \"from\"; }, { type: \"address\";...\n *\n * @example\n * const abiParameters = parseAbiParameters([\n *   //  ^? const abiParameters: [{ type: \"tuple\"; components: [{ type: \"string\"; name:...\n *   'Baz bar',\n *   'struct Baz { string name; }',\n * ])\n */\nfunction parseAbiParameters(params) {\n    const abiParameters = [];\n    if (typeof params === 'string') {\n        const parameters = (0,_runtime_utils_js__WEBPACK_IMPORTED_MODULE_0__.splitParameters)(params);\n        const length = parameters.length;\n        for (let i = 0; i < length; i++) {\n            abiParameters.push((0,_runtime_utils_js__WEBPACK_IMPORTED_MODULE_0__.parseAbiParameter)(parameters[i], { modifiers: _runtime_signatures_js__WEBPACK_IMPORTED_MODULE_1__.modifiers }));\n        }\n    }\n    else {\n        const structs = (0,_runtime_structs_js__WEBPACK_IMPORTED_MODULE_2__.parseStructs)(params);\n        const length = params.length;\n        for (let i = 0; i < length; i++) {\n            const signature = params[i];\n            if ((0,_runtime_signatures_js__WEBPACK_IMPORTED_MODULE_1__.isStructSignature)(signature))\n                continue;\n            const parameters = (0,_runtime_utils_js__WEBPACK_IMPORTED_MODULE_0__.splitParameters)(signature);\n            const length = parameters.length;\n            for (let k = 0; k < length; k++) {\n                abiParameters.push((0,_runtime_utils_js__WEBPACK_IMPORTED_MODULE_0__.parseAbiParameter)(parameters[k], { modifiers: _runtime_signatures_js__WEBPACK_IMPORTED_MODULE_1__.modifiers, structs }));\n            }\n        }\n    }\n    if (abiParameters.length === 0)\n        throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_3__.InvalidAbiParametersError({ params });\n    return abiParameters;\n}\n//# sourceMappingURL=parseAbiParameters.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/parseAbiParameters.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/cache.js":
/*!***********************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/runtime/cache.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getParameterCacheKey: () => (/* binding */ getParameterCacheKey),\n/* harmony export */   parameterCache: () => (/* binding */ parameterCache)\n/* harmony export */ });\n/**\n * Gets {@link parameterCache} cache key namespaced by {@link type}. This prevents parameters from being accessible to types that don't allow them (e.g. `string indexed foo` not allowed outside of `type: 'event'`).\n * @param param ABI parameter string\n * @param type ABI parameter type\n * @returns Cache key for {@link parameterCache}\n */\nfunction getParameterCacheKey(param, type, structs) {\n    let structKey = '';\n    if (structs)\n        for (const struct of Object.entries(structs)) {\n            if (!struct)\n                continue;\n            let propertyKey = '';\n            for (const property of struct[1]) {\n                propertyKey += `[${property.type}${property.name ? `:${property.name}` : ''}]`;\n            }\n            structKey += `(${struct[0]}{${propertyKey}})`;\n        }\n    if (type)\n        return `${type}:${param}${structKey}`;\n    return param;\n}\n/**\n * Basic cache seeded with common ABI parameter strings.\n *\n * **Note: When seeding more parameters, make sure you benchmark performance. The current number is the ideal balance between performance and having an already existing cache.**\n */\nconst parameterCache = new Map([\n    // Unnamed\n    ['address', { type: 'address' }],\n    ['bool', { type: 'bool' }],\n    ['bytes', { type: 'bytes' }],\n    ['bytes32', { type: 'bytes32' }],\n    ['int', { type: 'int256' }],\n    ['int256', { type: 'int256' }],\n    ['string', { type: 'string' }],\n    ['uint', { type: 'uint256' }],\n    ['uint8', { type: 'uint8' }],\n    ['uint16', { type: 'uint16' }],\n    ['uint24', { type: 'uint24' }],\n    ['uint32', { type: 'uint32' }],\n    ['uint64', { type: 'uint64' }],\n    ['uint96', { type: 'uint96' }],\n    ['uint112', { type: 'uint112' }],\n    ['uint160', { type: 'uint160' }],\n    ['uint192', { type: 'uint192' }],\n    ['uint256', { type: 'uint256' }],\n    // Named\n    ['address owner', { type: 'address', name: 'owner' }],\n    ['address to', { type: 'address', name: 'to' }],\n    ['bool approved', { type: 'bool', name: 'approved' }],\n    ['bytes _data', { type: 'bytes', name: '_data' }],\n    ['bytes data', { type: 'bytes', name: 'data' }],\n    ['bytes signature', { type: 'bytes', name: 'signature' }],\n    ['bytes32 hash', { type: 'bytes32', name: 'hash' }],\n    ['bytes32 r', { type: 'bytes32', name: 'r' }],\n    ['bytes32 root', { type: 'bytes32', name: 'root' }],\n    ['bytes32 s', { type: 'bytes32', name: 's' }],\n    ['string name', { type: 'string', name: 'name' }],\n    ['string symbol', { type: 'string', name: 'symbol' }],\n    ['string tokenURI', { type: 'string', name: 'tokenURI' }],\n    ['uint tokenId', { type: 'uint256', name: 'tokenId' }],\n    ['uint8 v', { type: 'uint8', name: 'v' }],\n    ['uint256 balance', { type: 'uint256', name: 'balance' }],\n    ['uint256 tokenId', { type: 'uint256', name: 'tokenId' }],\n    ['uint256 value', { type: 'uint256', name: 'value' }],\n    // Indexed\n    [\n        'event:address indexed from',\n        { type: 'address', name: 'from', indexed: true },\n    ],\n    ['event:address indexed to', { type: 'address', name: 'to', indexed: true }],\n    [\n        'event:uint indexed tokenId',\n        { type: 'uint256', name: 'tokenId', indexed: true },\n    ],\n    [\n        'event:uint256 indexed tokenId',\n        { type: 'uint256', name: 'tokenId', indexed: true },\n    ],\n]);\n//# sourceMappingURL=cache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/cache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/signatures.js":
/*!****************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/runtime/signatures.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eventModifiers: () => (/* binding */ eventModifiers),\n/* harmony export */   execConstructorSignature: () => (/* binding */ execConstructorSignature),\n/* harmony export */   execErrorSignature: () => (/* binding */ execErrorSignature),\n/* harmony export */   execEventSignature: () => (/* binding */ execEventSignature),\n/* harmony export */   execFallbackSignature: () => (/* binding */ execFallbackSignature),\n/* harmony export */   execFunctionSignature: () => (/* binding */ execFunctionSignature),\n/* harmony export */   execStructSignature: () => (/* binding */ execStructSignature),\n/* harmony export */   functionModifiers: () => (/* binding */ functionModifiers),\n/* harmony export */   isConstructorSignature: () => (/* binding */ isConstructorSignature),\n/* harmony export */   isErrorSignature: () => (/* binding */ isErrorSignature),\n/* harmony export */   isEventSignature: () => (/* binding */ isEventSignature),\n/* harmony export */   isFallbackSignature: () => (/* binding */ isFallbackSignature),\n/* harmony export */   isFunctionSignature: () => (/* binding */ isFunctionSignature),\n/* harmony export */   isReceiveSignature: () => (/* binding */ isReceiveSignature),\n/* harmony export */   isStructSignature: () => (/* binding */ isStructSignature),\n/* harmony export */   modifiers: () => (/* binding */ modifiers)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../regex.js */ \"(ssr)/./node_modules/abitype/dist/esm/regex.js\");\n\n// https://regexr.com/7gmok\nconst errorSignatureRegex = /^error (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)$/;\nfunction isErrorSignature(signature) {\n    return errorSignatureRegex.test(signature);\n}\nfunction execErrorSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(errorSignatureRegex, signature);\n}\n// https://regexr.com/7gmoq\nconst eventSignatureRegex = /^event (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)$/;\nfunction isEventSignature(signature) {\n    return eventSignatureRegex.test(signature);\n}\nfunction execEventSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(eventSignatureRegex, signature);\n}\n// https://regexr.com/7gmot\nconst functionSignatureRegex = /^function (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)(?: (?<scope>external|public{1}))?(?: (?<stateMutability>pure|view|nonpayable|payable{1}))?(?: returns\\s?\\((?<returns>.*?)\\))?$/;\nfunction isFunctionSignature(signature) {\n    return functionSignatureRegex.test(signature);\n}\nfunction execFunctionSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(functionSignatureRegex, signature);\n}\n// https://regexr.com/7gmp3\nconst structSignatureRegex = /^struct (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*) \\{(?<properties>.*?)\\}$/;\nfunction isStructSignature(signature) {\n    return structSignatureRegex.test(signature);\n}\nfunction execStructSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(structSignatureRegex, signature);\n}\n// https://regexr.com/78u01\nconst constructorSignatureRegex = /^constructor\\((?<parameters>.*?)\\)(?:\\s(?<stateMutability>payable{1}))?$/;\nfunction isConstructorSignature(signature) {\n    return constructorSignatureRegex.test(signature);\n}\nfunction execConstructorSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(constructorSignatureRegex, signature);\n}\n// https://regexr.com/7srtn\nconst fallbackSignatureRegex = /^fallback\\(\\) external(?:\\s(?<stateMutability>payable{1}))?$/;\nfunction isFallbackSignature(signature) {\n    return fallbackSignatureRegex.test(signature);\n}\nfunction execFallbackSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(fallbackSignatureRegex, signature);\n}\n// https://regexr.com/78u1k\nconst receiveSignatureRegex = /^receive\\(\\) external payable$/;\nfunction isReceiveSignature(signature) {\n    return receiveSignatureRegex.test(signature);\n}\nconst modifiers = new Set([\n    'memory',\n    'indexed',\n    'storage',\n    'calldata',\n]);\nconst eventModifiers = new Set(['indexed']);\nconst functionModifiers = new Set([\n    'calldata',\n    'memory',\n    'storage',\n]);\n//# sourceMappingURL=signatures.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/signatures.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/structs.js":
/*!*************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/runtime/structs.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseStructs: () => (/* binding */ parseStructs)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../regex.js */ \"(ssr)/./node_modules/abitype/dist/esm/regex.js\");\n/* harmony import */ var _errors_abiItem_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../errors/abiItem.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiItem.js\");\n/* harmony import */ var _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../errors/abiParameter.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js\");\n/* harmony import */ var _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/signature.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/signature.js\");\n/* harmony import */ var _errors_struct_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../errors/struct.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/struct.js\");\n/* harmony import */ var _signatures_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./signatures.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/signatures.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/utils.js\");\n\n\n\n\n\n\n\nfunction parseStructs(signatures) {\n    // Create \"shallow\" version of each struct (and filter out non-structs or invalid structs)\n    const shallowStructs = {};\n    const signaturesLength = signatures.length;\n    for (let i = 0; i < signaturesLength; i++) {\n        const signature = signatures[i];\n        if (!(0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isStructSignature)(signature))\n            continue;\n        const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execStructSignature)(signature);\n        if (!match)\n            throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'struct' });\n        const properties = match.properties.split(';');\n        const components = [];\n        const propertiesLength = properties.length;\n        for (let k = 0; k < propertiesLength; k++) {\n            const property = properties[k];\n            const trimmed = property.trim();\n            if (!trimmed)\n                continue;\n            const abiParameter = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.parseAbiParameter)(trimmed, {\n                type: 'struct',\n            });\n            components.push(abiParameter);\n        }\n        if (!components.length)\n            throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidStructSignatureError({ signature });\n        shallowStructs[match.name] = components;\n    }\n    // Resolve nested structs inside each parameter\n    const resolvedStructs = {};\n    const entries = Object.entries(shallowStructs);\n    const entriesLength = entries.length;\n    for (let i = 0; i < entriesLength; i++) {\n        const [name, parameters] = entries[i];\n        resolvedStructs[name] = resolveStructs(parameters, shallowStructs);\n    }\n    return resolvedStructs;\n}\nconst typeWithoutTupleRegex = /^(?<type>[a-zA-Z$_][a-zA-Z0-9$_]*)(?<array>(?:\\[\\d*?\\])+?)?$/;\nfunction resolveStructs(abiParameters, structs, ancestors = new Set()) {\n    const components = [];\n    const length = abiParameters.length;\n    for (let i = 0; i < length; i++) {\n        const abiParameter = abiParameters[i];\n        const isTuple = _regex_js__WEBPACK_IMPORTED_MODULE_3__.isTupleRegex.test(abiParameter.type);\n        if (isTuple)\n            components.push(abiParameter);\n        else {\n            const match = (0,_regex_js__WEBPACK_IMPORTED_MODULE_3__.execTyped)(typeWithoutTupleRegex, abiParameter.type);\n            if (!match?.type)\n                throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__.InvalidAbiTypeParameterError({ abiParameter });\n            const { array, type } = match;\n            if (type in structs) {\n                if (ancestors.has(type))\n                    throw new _errors_struct_js__WEBPACK_IMPORTED_MODULE_5__.CircularReferenceError({ type });\n                components.push({\n                    ...abiParameter,\n                    type: `tuple${array ?? ''}`,\n                    components: resolveStructs(structs[type] ?? [], structs, new Set([...ancestors, type])),\n                });\n            }\n            else {\n                if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isSolidityType)(type))\n                    components.push(abiParameter);\n                else\n                    throw new _errors_abiItem_js__WEBPACK_IMPORTED_MODULE_6__.UnknownTypeError({ type });\n            }\n        }\n    }\n    return components;\n}\n//# sourceMappingURL=structs.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/structs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/utils.js":
/*!***********************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/runtime/utils.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSolidityKeyword: () => (/* binding */ isSolidityKeyword),\n/* harmony export */   isSolidityType: () => (/* binding */ isSolidityType),\n/* harmony export */   isValidDataLocation: () => (/* binding */ isValidDataLocation),\n/* harmony export */   parseAbiParameter: () => (/* binding */ parseAbiParameter),\n/* harmony export */   parseConstructorSignature: () => (/* binding */ parseConstructorSignature),\n/* harmony export */   parseErrorSignature: () => (/* binding */ parseErrorSignature),\n/* harmony export */   parseEventSignature: () => (/* binding */ parseEventSignature),\n/* harmony export */   parseFallbackSignature: () => (/* binding */ parseFallbackSignature),\n/* harmony export */   parseFunctionSignature: () => (/* binding */ parseFunctionSignature),\n/* harmony export */   parseSignature: () => (/* binding */ parseSignature),\n/* harmony export */   splitParameters: () => (/* binding */ splitParameters)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../regex.js */ \"(ssr)/./node_modules/abitype/dist/esm/regex.js\");\n/* harmony import */ var _errors_abiItem_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../errors/abiItem.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiItem.js\");\n/* harmony import */ var _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../errors/abiParameter.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js\");\n/* harmony import */ var _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/signature.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/signature.js\");\n/* harmony import */ var _errors_splitParameters_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../errors/splitParameters.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/splitParameters.js\");\n/* harmony import */ var _cache_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cache.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/cache.js\");\n/* harmony import */ var _signatures_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./signatures.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/signatures.js\");\n\n\n\n\n\n\n\nfunction parseSignature(signature, structs = {}) {\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isFunctionSignature)(signature))\n        return parseFunctionSignature(signature, structs);\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isEventSignature)(signature))\n        return parseEventSignature(signature, structs);\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isErrorSignature)(signature))\n        return parseErrorSignature(signature, structs);\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isConstructorSignature)(signature))\n        return parseConstructorSignature(signature, structs);\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isFallbackSignature)(signature))\n        return parseFallbackSignature(signature);\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isReceiveSignature)(signature))\n        return {\n            type: 'receive',\n            stateMutability: 'payable',\n        };\n    throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.UnknownSignatureError({ signature });\n}\nfunction parseFunctionSignature(signature, structs = {}) {\n    const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execFunctionSignature)(signature);\n    if (!match)\n        throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'function' });\n    const inputParams = splitParameters(match.parameters);\n    const inputs = [];\n    const inputLength = inputParams.length;\n    for (let i = 0; i < inputLength; i++) {\n        inputs.push(parseAbiParameter(inputParams[i], {\n            modifiers: _signatures_js__WEBPACK_IMPORTED_MODULE_0__.functionModifiers,\n            structs,\n            type: 'function',\n        }));\n    }\n    const outputs = [];\n    if (match.returns) {\n        const outputParams = splitParameters(match.returns);\n        const outputLength = outputParams.length;\n        for (let i = 0; i < outputLength; i++) {\n            outputs.push(parseAbiParameter(outputParams[i], {\n                modifiers: _signatures_js__WEBPACK_IMPORTED_MODULE_0__.functionModifiers,\n                structs,\n                type: 'function',\n            }));\n        }\n    }\n    return {\n        name: match.name,\n        type: 'function',\n        stateMutability: match.stateMutability ?? 'nonpayable',\n        inputs,\n        outputs,\n    };\n}\nfunction parseEventSignature(signature, structs = {}) {\n    const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execEventSignature)(signature);\n    if (!match)\n        throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'event' });\n    const params = splitParameters(match.parameters);\n    const abiParameters = [];\n    const length = params.length;\n    for (let i = 0; i < length; i++)\n        abiParameters.push(parseAbiParameter(params[i], {\n            modifiers: _signatures_js__WEBPACK_IMPORTED_MODULE_0__.eventModifiers,\n            structs,\n            type: 'event',\n        }));\n    return { name: match.name, type: 'event', inputs: abiParameters };\n}\nfunction parseErrorSignature(signature, structs = {}) {\n    const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execErrorSignature)(signature);\n    if (!match)\n        throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'error' });\n    const params = splitParameters(match.parameters);\n    const abiParameters = [];\n    const length = params.length;\n    for (let i = 0; i < length; i++)\n        abiParameters.push(parseAbiParameter(params[i], { structs, type: 'error' }));\n    return { name: match.name, type: 'error', inputs: abiParameters };\n}\nfunction parseConstructorSignature(signature, structs = {}) {\n    const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execConstructorSignature)(signature);\n    if (!match)\n        throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'constructor' });\n    const params = splitParameters(match.parameters);\n    const abiParameters = [];\n    const length = params.length;\n    for (let i = 0; i < length; i++)\n        abiParameters.push(parseAbiParameter(params[i], { structs, type: 'constructor' }));\n    return {\n        type: 'constructor',\n        stateMutability: match.stateMutability ?? 'nonpayable',\n        inputs: abiParameters,\n    };\n}\nfunction parseFallbackSignature(signature) {\n    const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execFallbackSignature)(signature);\n    if (!match)\n        throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'fallback' });\n    return {\n        type: 'fallback',\n        stateMutability: match.stateMutability ?? 'nonpayable',\n    };\n}\nconst abiParameterWithoutTupleRegex = /^(?<type>[a-zA-Z$_][a-zA-Z0-9$_]*)(?<array>(?:\\[\\d*?\\])+?)?(?:\\s(?<modifier>calldata|indexed|memory|storage{1}))?(?:\\s(?<name>[a-zA-Z$_][a-zA-Z0-9$_]*))?$/;\nconst abiParameterWithTupleRegex = /^\\((?<type>.+?)\\)(?<array>(?:\\[\\d*?\\])+?)?(?:\\s(?<modifier>calldata|indexed|memory|storage{1}))?(?:\\s(?<name>[a-zA-Z$_][a-zA-Z0-9$_]*))?$/;\nconst dynamicIntegerRegex = /^u?int$/;\nfunction parseAbiParameter(param, options) {\n    // optional namespace cache by `type`\n    const parameterCacheKey = (0,_cache_js__WEBPACK_IMPORTED_MODULE_2__.getParameterCacheKey)(param, options?.type, options?.structs);\n    if (_cache_js__WEBPACK_IMPORTED_MODULE_2__.parameterCache.has(parameterCacheKey))\n        return _cache_js__WEBPACK_IMPORTED_MODULE_2__.parameterCache.get(parameterCacheKey);\n    const isTuple = _regex_js__WEBPACK_IMPORTED_MODULE_3__.isTupleRegex.test(param);\n    const match = (0,_regex_js__WEBPACK_IMPORTED_MODULE_3__.execTyped)(isTuple ? abiParameterWithTupleRegex : abiParameterWithoutTupleRegex, param);\n    if (!match)\n        throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__.InvalidParameterError({ param });\n    if (match.name && isSolidityKeyword(match.name))\n        throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__.SolidityProtectedKeywordError({ param, name: match.name });\n    const name = match.name ? { name: match.name } : {};\n    const indexed = match.modifier === 'indexed' ? { indexed: true } : {};\n    const structs = options?.structs ?? {};\n    let type;\n    let components = {};\n    if (isTuple) {\n        type = 'tuple';\n        const params = splitParameters(match.type);\n        const components_ = [];\n        const length = params.length;\n        for (let i = 0; i < length; i++) {\n            // remove `modifiers` from `options` to prevent from being added to tuple components\n            components_.push(parseAbiParameter(params[i], { structs }));\n        }\n        components = { components: components_ };\n    }\n    else if (match.type in structs) {\n        type = 'tuple';\n        components = { components: structs[match.type] };\n    }\n    else if (dynamicIntegerRegex.test(match.type)) {\n        type = `${match.type}256`;\n    }\n    else {\n        type = match.type;\n        if (!(options?.type === 'struct') && !isSolidityType(type))\n            throw new _errors_abiItem_js__WEBPACK_IMPORTED_MODULE_5__.UnknownSolidityTypeError({ type });\n    }\n    if (match.modifier) {\n        // Check if modifier exists, but is not allowed (e.g. `indexed` in `functionModifiers`)\n        if (!options?.modifiers?.has?.(match.modifier))\n            throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__.InvalidModifierError({\n                param,\n                type: options?.type,\n                modifier: match.modifier,\n            });\n        // Check if resolved `type` is valid if there is a function modifier\n        if (_signatures_js__WEBPACK_IMPORTED_MODULE_0__.functionModifiers.has(match.modifier) &&\n            !isValidDataLocation(type, !!match.array))\n            throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__.InvalidFunctionModifierError({\n                param,\n                type: options?.type,\n                modifier: match.modifier,\n            });\n    }\n    const abiParameter = {\n        type: `${type}${match.array ?? ''}`,\n        ...name,\n        ...indexed,\n        ...components,\n    };\n    _cache_js__WEBPACK_IMPORTED_MODULE_2__.parameterCache.set(parameterCacheKey, abiParameter);\n    return abiParameter;\n}\n// s/o latika for this\nfunction splitParameters(params, result = [], current = '', depth = 0) {\n    const length = params.trim().length;\n    // biome-ignore lint/correctness/noUnreachable: recursive\n    for (let i = 0; i < length; i++) {\n        const char = params[i];\n        const tail = params.slice(i + 1);\n        switch (char) {\n            case ',':\n                return depth === 0\n                    ? splitParameters(tail, [...result, current.trim()])\n                    : splitParameters(tail, result, `${current}${char}`, depth);\n            case '(':\n                return splitParameters(tail, result, `${current}${char}`, depth + 1);\n            case ')':\n                return splitParameters(tail, result, `${current}${char}`, depth - 1);\n            default:\n                return splitParameters(tail, result, `${current}${char}`, depth);\n        }\n    }\n    if (current === '')\n        return result;\n    if (depth !== 0)\n        throw new _errors_splitParameters_js__WEBPACK_IMPORTED_MODULE_6__.InvalidParenthesisError({ current, depth });\n    result.push(current.trim());\n    return result;\n}\nfunction isSolidityType(type) {\n    return (type === 'address' ||\n        type === 'bool' ||\n        type === 'function' ||\n        type === 'string' ||\n        _regex_js__WEBPACK_IMPORTED_MODULE_3__.bytesRegex.test(type) ||\n        _regex_js__WEBPACK_IMPORTED_MODULE_3__.integerRegex.test(type));\n}\nconst protectedKeywordsRegex = /^(?:after|alias|anonymous|apply|auto|byte|calldata|case|catch|constant|copyof|default|defined|error|event|external|false|final|function|immutable|implements|in|indexed|inline|internal|let|mapping|match|memory|mutable|null|of|override|partial|private|promise|public|pure|reference|relocatable|return|returns|sizeof|static|storage|struct|super|supports|switch|this|true|try|typedef|typeof|var|view|virtual)$/;\n/** @internal */\nfunction isSolidityKeyword(name) {\n    return (name === 'address' ||\n        name === 'bool' ||\n        name === 'function' ||\n        name === 'string' ||\n        name === 'tuple' ||\n        _regex_js__WEBPACK_IMPORTED_MODULE_3__.bytesRegex.test(name) ||\n        _regex_js__WEBPACK_IMPORTED_MODULE_3__.integerRegex.test(name) ||\n        protectedKeywordsRegex.test(name));\n}\n/** @internal */\nfunction isValidDataLocation(type, isArray) {\n    return isArray || type === 'bytes' || type === 'string' || type === 'tuple';\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/regex.js":
/*!************************************************!*\
  !*** ./node_modules/abitype/dist/esm/regex.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bytesRegex: () => (/* binding */ bytesRegex),\n/* harmony export */   execTyped: () => (/* binding */ execTyped),\n/* harmony export */   integerRegex: () => (/* binding */ integerRegex),\n/* harmony export */   isTupleRegex: () => (/* binding */ isTupleRegex)\n/* harmony export */ });\n// TODO: This looks cool. Need to check the performance of `new RegExp` versus defined inline though.\n// https://twitter.com/GabrielVergnaud/status/1622906834343366657\nfunction execTyped(regex, string) {\n    const match = regex.exec(string);\n    return match?.groups;\n}\n// `bytes<M>`: binary type of `M` bytes, `0 < M <= 32`\n// https://regexr.com/6va55\nconst bytesRegex = /^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/;\n// `(u)int<M>`: (un)signed integer type of `M` bits, `0 < M <= 256`, `M % 8 == 0`\n// https://regexr.com/6v8hp\nconst integerRegex = /^u?int(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/;\nconst isTupleRegex = /^\\(.+?\\).*?$/;\n//# sourceMappingURL=regex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS9yZWdleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNPO0FBQ0E7QUFDUCIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9hYml0eXBlL2Rpc3QvZXNtL3JlZ2V4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRPRE86IFRoaXMgbG9va3MgY29vbC4gTmVlZCB0byBjaGVjayB0aGUgcGVyZm9ybWFuY2Ugb2YgYG5ldyBSZWdFeHBgIHZlcnN1cyBkZWZpbmVkIGlubGluZSB0aG91Z2guXG4vLyBodHRwczovL3R3aXR0ZXIuY29tL0dhYnJpZWxWZXJnbmF1ZC9zdGF0dXMvMTYyMjkwNjgzNDM0MzM2NjY1N1xuZXhwb3J0IGZ1bmN0aW9uIGV4ZWNUeXBlZChyZWdleCwgc3RyaW5nKSB7XG4gICAgY29uc3QgbWF0Y2ggPSByZWdleC5leGVjKHN0cmluZyk7XG4gICAgcmV0dXJuIG1hdGNoPy5ncm91cHM7XG59XG4vLyBgYnl0ZXM8TT5gOiBiaW5hcnkgdHlwZSBvZiBgTWAgYnl0ZXMsIGAwIDwgTSA8PSAzMmBcbi8vIGh0dHBzOi8vcmVnZXhyLmNvbS82dmE1NVxuZXhwb3J0IGNvbnN0IGJ5dGVzUmVnZXggPSAvXmJ5dGVzKFsxLTldfDFbMC05XXwyWzAtOV18M1swLTJdKT8kLztcbi8vIGAodSlpbnQ8TT5gOiAodW4pc2lnbmVkIGludGVnZXIgdHlwZSBvZiBgTWAgYml0cywgYDAgPCBNIDw9IDI1NmAsIGBNICUgOCA9PSAwYFxuLy8gaHR0cHM6Ly9yZWdleHIuY29tLzZ2OGhwXG5leHBvcnQgY29uc3QgaW50ZWdlclJlZ2V4ID0gL151P2ludCg4fDE2fDI0fDMyfDQwfDQ4fDU2fDY0fDcyfDgwfDg4fDk2fDEwNHwxMTJ8MTIwfDEyOHwxMzZ8MTQ0fDE1MnwxNjB8MTY4fDE3NnwxODR8MTkyfDIwMHwyMDh8MjE2fDIyNHwyMzJ8MjQwfDI0OHwyNTYpPyQvO1xuZXhwb3J0IGNvbnN0IGlzVHVwbGVSZWdleCA9IC9eXFwoLis/XFwpLio/JC87XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWdleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/regex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/version.js":
/*!**************************************************!*\
  !*** ./node_modules/abitype/dist/esm/version.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '1.0.8';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyIvaG9tZS9ha2FzaC9jaGV0YW4vc3BlY3RyYW1pbnQvbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9lc20vdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdmVyc2lvbiA9ICcxLjAuOCc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/version.js\n");

/***/ })

};
;