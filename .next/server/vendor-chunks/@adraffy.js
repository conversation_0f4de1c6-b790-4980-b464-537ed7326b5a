"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@adraffy";
exports.ids = ["vendor-chunks/@adraffy"];
exports.modules = {

/***/ "(ssr)/./node_modules/@adraffy/ens-normalize/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@adraffy/ens-normalize/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ens_beautify: () => (/* binding */ ens_beautify),\n/* harmony export */   ens_emoji: () => (/* binding */ ens_emoji),\n/* harmony export */   ens_normalize: () => (/* binding */ ens_normalize),\n/* harmony export */   ens_normalize_fragment: () => (/* binding */ ens_normalize_fragment),\n/* harmony export */   ens_split: () => (/* binding */ ens_split),\n/* harmony export */   ens_tokenize: () => (/* binding */ ens_tokenize),\n/* harmony export */   is_combining_mark: () => (/* binding */ is_combining_mark),\n/* harmony export */   nfc: () => (/* binding */ nfc),\n/* harmony export */   nfd: () => (/* binding */ nfd),\n/* harmony export */   safe_str_from_cps: () => (/* binding */ safe_str_from_cps),\n/* harmony export */   should_escape: () => (/* binding */ should_escape)\n/* harmony export */ });\n// created 2024-09-13T06:42:45.675Z\n// compressed base64-encoded blob for include-ens data\n// source: https://github.com/adraffy/ens-normalize.js/blob/main/src/make.js\n// see: https://github.com/adraffy/ens-normalize.js#security\n// SHA-256: 0ca3917bee1eee342d64c631cb1dbaac37b167d848887d59f6d68328dc99ac09\nvar COMPRESSED$1 = '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';\nconst FENCED = new Map([[8217,\"apostrophe\"],[8260,\"fraction slash\"],[12539,\"middle dot\"]]);\nconst NSM_MAX = 4;\n\nfunction decode_arithmetic(bytes) {\r\n\tlet pos = 0;\r\n\tfunction u16() { return (bytes[pos++] << 8) | bytes[pos++]; }\r\n\t\r\n\t// decode the frequency table\r\n\tlet symbol_count = u16();\r\n\tlet total = 1;\r\n\tlet acc = [0, 1]; // first symbol has frequency 1\r\n\tfor (let i = 1; i < symbol_count; i++) {\r\n\t\tacc.push(total += u16());\r\n\t}\r\n\r\n\t// skip the sized-payload that the last 3 symbols index into\r\n\tlet skip = u16();\r\n\tlet pos_payload = pos;\r\n\tpos += skip;\r\n\r\n\tlet read_width = 0;\r\n\tlet read_buffer = 0; \r\n\tfunction read_bit() {\r\n\t\tif (read_width == 0) {\r\n\t\t\t// this will read beyond end of buffer\r\n\t\t\t// but (undefined|0) => zero pad\r\n\t\t\tread_buffer = (read_buffer << 8) | bytes[pos++];\r\n\t\t\tread_width = 8;\r\n\t\t}\r\n\t\treturn (read_buffer >> --read_width) & 1;\r\n\t}\r\n\r\n\tconst N = 31;\r\n\tconst FULL = 2**N;\r\n\tconst HALF = FULL >>> 1;\r\n\tconst QRTR = HALF >> 1;\r\n\tconst MASK = FULL - 1;\r\n\r\n\t// fill register\r\n\tlet register = 0;\r\n\tfor (let i = 0; i < N; i++) register = (register << 1) | read_bit();\r\n\r\n\tlet symbols = [];\r\n\tlet low = 0;\r\n\tlet range = FULL; // treat like a float\r\n\twhile (true) {\r\n\t\tlet value = Math.floor((((register - low + 1) * total) - 1) / range);\r\n\t\tlet start = 0;\r\n\t\tlet end = symbol_count;\r\n\t\twhile (end - start > 1) { // binary search\r\n\t\t\tlet mid = (start + end) >>> 1;\r\n\t\t\tif (value < acc[mid]) {\r\n\t\t\t\tend = mid;\r\n\t\t\t} else {\r\n\t\t\t\tstart = mid;\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (start == 0) break; // first symbol is end mark\r\n\t\tsymbols.push(start);\r\n\t\tlet a = low + Math.floor(range * acc[start]   / total);\r\n\t\tlet b = low + Math.floor(range * acc[start+1] / total) - 1;\r\n\t\twhile (((a ^ b) & HALF) == 0) {\r\n\t\t\tregister = (register << 1) & MASK | read_bit();\r\n\t\t\ta = (a << 1) & MASK;\r\n\t\t\tb = (b << 1) & MASK | 1;\r\n\t\t}\r\n\t\twhile (a & ~b & QRTR) {\r\n\t\t\tregister = (register & HALF) | ((register << 1) & (MASK >>> 1)) | read_bit();\r\n\t\t\ta = (a << 1) ^ HALF;\r\n\t\t\tb = ((b ^ HALF) << 1) | HALF | 1;\r\n\t\t}\r\n\t\tlow = a;\r\n\t\trange = 1 + b - a;\r\n\t}\r\n\tlet offset = symbol_count - 4;\r\n\treturn symbols.map(x => { // index into payload\r\n\t\tswitch (x - offset) {\r\n\t\t\tcase 3: return offset + 0x10100 + ((bytes[pos_payload++] << 16) | (bytes[pos_payload++] << 8) | bytes[pos_payload++]);\r\n\t\t\tcase 2: return offset + 0x100 + ((bytes[pos_payload++] << 8) | bytes[pos_payload++]);\r\n\t\t\tcase 1: return offset + bytes[pos_payload++];\r\n\t\t\tdefault: return x - 1;\r\n\t\t}\r\n\t});\r\n}\t\r\n\r\n// returns an iterator which returns the next symbol\r\nfunction read_payload(v) {\r\n\tlet pos = 0;\r\n\treturn () => v[pos++];\r\n}\r\nfunction read_compressed_payload(s) {\r\n\treturn read_payload(decode_arithmetic(unsafe_atob(s)));\r\n}\r\n\r\n// unsafe in the sense:\r\n// expected well-formed Base64 w/o padding \r\n// 20220922: added for https://github.com/adraffy/ens-normalize.js/issues/4\r\nfunction unsafe_atob(s) {\r\n\tlet lookup = [];\r\n\t[...'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'].forEach((c, i) => lookup[c.charCodeAt(0)] = i);\r\n\tlet n = s.length;\r\n\tlet ret = new Uint8Array((6 * n) >> 3);\r\n\tfor (let i = 0, pos = 0, width = 0, carry = 0; i < n; i++) {\r\n\t\tcarry = (carry << 6) | lookup[s.charCodeAt(i)];\r\n\t\twidth += 6;\r\n\t\tif (width >= 8) {\r\n\t\t\tret[pos++] = (carry >> (width -= 8));\r\n\t\t}\r\n\t}\r\n\treturn ret;\r\n}\r\n\r\n// eg. [0,1,2,3...] => [0,-1,1,-2,...]\r\nfunction signed(i) { \r\n\treturn (i & 1) ? (~i >> 1) : (i >> 1);\r\n}\r\n\r\nfunction read_deltas(n, next) {\r\n\tlet v = Array(n);\r\n\tfor (let i = 0, x = 0; i < n; i++) v[i] = x += signed(next());\r\n\treturn v;\r\n}\r\n\r\n// [123][5] => [0 3] [1 1] [0 0]\r\nfunction read_sorted(next, prev = 0) {\r\n\tlet ret = [];\r\n\twhile (true) {\r\n\t\tlet x = next();\r\n\t\tlet n = next();\r\n\t\tif (!n) break;\r\n\t\tprev += x;\r\n\t\tfor (let i = 0; i < n; i++) {\r\n\t\t\tret.push(prev + i);\r\n\t\t}\r\n\t\tprev += n + 1;\r\n\t}\r\n\treturn ret;\r\n}\r\n\r\nfunction read_sorted_arrays(next) {\r\n\treturn read_array_while(() => { \r\n\t\tlet v = read_sorted(next);\r\n\t\tif (v.length) return v;\r\n\t});\r\n}\r\n\r\n// returns map of x => ys\r\nfunction read_mapped(next) {\r\n\tlet ret = [];\r\n\twhile (true) {\r\n\t\tlet w = next();\r\n\t\tif (w == 0) break;\r\n\t\tret.push(read_linear_table(w, next));\r\n\t}\r\n\twhile (true) {\r\n\t\tlet w = next() - 1;\r\n\t\tif (w < 0) break;\r\n\t\tret.push(read_replacement_table(w, next));\r\n\t}\r\n\treturn ret.flat();\r\n}\r\n\r\n// read until next is falsy\r\n// return array of read values\r\nfunction read_array_while(next) {\r\n\tlet v = [];\r\n\twhile (true) {\r\n\t\tlet x = next(v.length);\r\n\t\tif (!x) break;\r\n\t\tv.push(x);\r\n\t}\r\n\treturn v;\r\n}\r\n\r\n// read w columns of length n\r\n// return as n rows of length w\r\nfunction read_transposed(n, w, next) {\r\n\tlet m = Array(n).fill().map(() => []);\r\n\tfor (let i = 0; i < w; i++) {\r\n\t\tread_deltas(n, next).forEach((x, j) => m[j].push(x));\r\n\t}\r\n\treturn m;\r\n}\r\n \r\n// returns [[x, ys], [x+dx, ys+dy], [x+2*dx, ys+2*dy], ...]\r\n// where dx/dy = steps, n = run size, w = length of y\r\nfunction read_linear_table(w, next) {\r\n\tlet dx = 1 + next();\r\n\tlet dy = next();\r\n\tlet vN = read_array_while(next);\r\n\tlet m = read_transposed(vN.length, 1+w, next);\r\n\treturn m.flatMap((v, i) => {\r\n\t\tlet [x, ...ys] = v;\r\n\t\treturn Array(vN[i]).fill().map((_, j) => {\r\n\t\t\tlet j_dy = j * dy;\r\n\t\t\treturn [x + j * dx, ys.map(y => y + j_dy)];\r\n\t\t});\r\n\t});\r\n}\r\n\r\n// return [[x, ys...], ...]\r\n// where w = length of y\r\nfunction read_replacement_table(w, next) { \r\n\tlet n = 1 + next();\r\n\tlet m = read_transposed(n, 1+w, next);\r\n\treturn m.map(v => [v[0], v.slice(1)]);\r\n}\r\n\r\n\r\nfunction read_trie(next) {\r\n\tlet ret = [];\r\n\tlet sorted = read_sorted(next); \r\n\texpand(decode([]), []);\r\n\treturn ret; // not sorted\r\n\tfunction decode(Q) { // characters that lead into this node\r\n\t\tlet S = next(); // state: valid, save, check\r\n\t\tlet B = read_array_while(() => { // buckets leading to new nodes\r\n\t\t\tlet cps = read_sorted(next).map(i => sorted[i]);\r\n\t\t\tif (cps.length) return decode(cps);\r\n\t\t});\r\n\t\treturn {S, B, Q};\r\n\t}\r\n\tfunction expand({S, B}, cps, saved) {\r\n\t\tif (S & 4 && saved === cps[cps.length-1]) return;\r\n\t\tif (S & 2) saved = cps[cps.length-1];\r\n\t\tif (S & 1) ret.push(cps); \r\n\t\tfor (let br of B) {\r\n\t\t\tfor (let cp of br.Q) {\r\n\t\t\t\texpand(br, [...cps, cp], saved);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\n\nfunction hex_cp(cp) {\r\n\treturn cp.toString(16).toUpperCase().padStart(2, '0');\r\n}\r\n\r\nfunction quote_cp(cp) {\r\n\treturn `{${hex_cp(cp)}}`; // raffy convention: like \"\\u{X}\" w/o the \"\\u\"\r\n}\r\n\r\n/*\r\nexport function explode_cp(s) {\r\n\treturn [...s].map(c => c.codePointAt(0));\r\n}\r\n*/\r\nfunction explode_cp(s) { // this is about 2x faster\r\n\tlet cps = [];\r\n\tfor (let pos = 0, len = s.length; pos < len; ) {\r\n\t\tlet cp = s.codePointAt(pos);\r\n\t\tpos += cp < 0x10000 ? 1 : 2;\r\n\t\tcps.push(cp);\r\n\t}\r\n\treturn cps;\r\n}\r\n\r\nfunction str_from_cps(cps) {\r\n\tconst chunk = 4096;\r\n\tlet len = cps.length;\r\n\tif (len < chunk) return String.fromCodePoint(...cps);\r\n\tlet buf = [];\r\n\tfor (let i = 0; i < len; ) {\r\n\t\tbuf.push(String.fromCodePoint(...cps.slice(i, i += chunk)));\r\n\t}\r\n\treturn buf.join('');\r\n}\r\n\r\nfunction compare_arrays(a, b) {\r\n\tlet n = a.length;\r\n\tlet c = n - b.length;\r\n\tfor (let i = 0; c == 0 && i < n; i++) c = a[i] - b[i];\r\n\treturn c;\r\n}\r\n\r\nfunction array_replace(v, a, b) {\r\n\tlet prev = 0;\r\n\twhile (true) {\r\n\t\tlet next = v.indexOf(a, prev);\r\n\t\tif (next < 0) break;\r\n\t\tv[next] = b; \r\n\t\tprev = next + 1;\r\n\t}\r\n}\n\n// created 2024-09-13T06:42:45.675Z\n// compressed base64-encoded blob for include-nf data\n// source: https://github.com/adraffy/ens-normalize.js/blob/main/src/make.js\n// see: https://github.com/adraffy/ens-normalize.js#security\n// SHA-256: a79d5f9b1879a7b416aa659f4a3d788f80a8cf5f0ab955a456592c02f556a28c\nvar COMPRESSED = '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';\n\n// https://unicode.org/reports/tr15/\r\n// for reference implementation\r\n// see: /derive/nf.js\r\n\r\n\r\n// algorithmic hangul\r\n// https://www.unicode.org/versions/Unicode15.0.0/ch03.pdf (page 144)\r\nconst S0 = 0xAC00;\r\nconst L0 = 0x1100;\r\nconst V0 = 0x1161;\r\nconst T0 = 0x11A7;\r\nconst L_COUNT = 19;\r\nconst V_COUNT = 21;\r\nconst T_COUNT = 28;\r\nconst N_COUNT = V_COUNT * T_COUNT;\r\nconst S_COUNT = L_COUNT * N_COUNT;\r\nconst S1 = S0 + S_COUNT;\r\nconst L1 = L0 + L_COUNT;\r\nconst V1 = V0 + V_COUNT;\r\nconst T1 = T0 + T_COUNT;\r\n\r\nfunction unpack_cc(packed) {\r\n\treturn (packed >> 24) & 0xFF;\r\n}\r\nfunction unpack_cp(packed) {\r\n\treturn packed & 0xFFFFFF;\r\n}\r\n\r\nlet SHIFTED_RANK, EXCLUSIONS, DECOMP, RECOMP;\r\n\r\n// export function nf_deinit() {\r\n// \tif (!SHIFTED_RANK) return;\r\n// \tSHIFTED_RANK = EXCLUSIONS = DECOMP = RECOMP = undefined;\r\n// }\r\n\r\nfunction init$1() {\r\n\t//console.time('nf');\r\n\tlet r = read_compressed_payload(COMPRESSED);\r\n\tSHIFTED_RANK = new Map(read_sorted_arrays(r).flatMap((v, i) => v.map(x => [x, (i+1) << 24]))); // pre-shifted\r\n\tEXCLUSIONS = new Set(read_sorted(r));\r\n\tDECOMP = new Map();\r\n\tRECOMP = new Map();\r\n\tfor (let [cp, cps] of read_mapped(r)) {\r\n\t\tif (!EXCLUSIONS.has(cp) && cps.length == 2) {\r\n\t\t\tlet [a, b] = cps;\r\n\t\t\tlet bucket = RECOMP.get(a);\r\n\t\t\tif (!bucket) {\r\n\t\t\t\tbucket = new Map();\r\n\t\t\t\tRECOMP.set(a, bucket);\r\n\t\t\t}\r\n\t\t\tbucket.set(b, cp);\r\n\t\t}\r\n\t\tDECOMP.set(cp, cps.reverse()); // stored reversed\r\n\t}\r\n\t//console.timeEnd('nf');\r\n\t// 20230905: 11ms\r\n}\r\n\r\nfunction is_hangul(cp) {\r\n\treturn cp >= S0 && cp < S1;\r\n}\r\n\r\nfunction compose_pair(a, b) {\r\n\tif (a >= L0 && a < L1 && b >= V0 && b < V1) {\r\n\t\treturn S0 + (a - L0) * N_COUNT + (b - V0) * T_COUNT;\r\n\t} else if (is_hangul(a) && b > T0 && b < T1 && (a - S0) % T_COUNT == 0) {\r\n\t\treturn a + (b - T0);\r\n\t} else {\r\n\t\tlet recomp = RECOMP.get(a);\r\n\t\tif (recomp) {\r\n\t\t\trecomp = recomp.get(b);\r\n\t\t\tif (recomp) {\r\n\t\t\t\treturn recomp;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn -1;\r\n\t}\r\n}\r\n\r\nfunction decomposed(cps) {\r\n\tif (!SHIFTED_RANK) init$1();\r\n\tlet ret = [];\r\n\tlet buf = [];\r\n\tlet check_order = false;\r\n\tfunction add(cp) {\r\n\t\tlet cc = SHIFTED_RANK.get(cp);\r\n\t\tif (cc) {\r\n\t\t\tcheck_order = true;\r\n\t\t\tcp |= cc;\r\n\t\t}\r\n\t\tret.push(cp);\r\n\t}\r\n\tfor (let cp of cps) {\r\n\t\twhile (true) {\r\n\t\t\tif (cp < 0x80) {\r\n\t\t\t\tret.push(cp);\r\n\t\t\t} else if (is_hangul(cp)) {\r\n\t\t\t\tlet s_index = cp - S0;\r\n\t\t\t\tlet l_index = s_index / N_COUNT | 0;\r\n\t\t\t\tlet v_index = (s_index % N_COUNT) / T_COUNT | 0;\r\n\t\t\t\tlet t_index = s_index % T_COUNT;\r\n\t\t\t\tadd(L0 + l_index);\r\n\t\t\t\tadd(V0 + v_index);\r\n\t\t\t\tif (t_index > 0) add(T0 + t_index);\r\n\t\t\t} else {\r\n\t\t\t\tlet mapped = DECOMP.get(cp);\r\n\t\t\t\tif (mapped) {\r\n\t\t\t\t\tbuf.push(...mapped);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tadd(cp);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (!buf.length) break;\r\n\t\t\tcp = buf.pop();\r\n\t\t}\r\n\t}\r\n\tif (check_order && ret.length > 1) {\r\n\t\tlet prev_cc = unpack_cc(ret[0]);\r\n\t\tfor (let i = 1; i < ret.length; i++) {\r\n\t\t\tlet cc = unpack_cc(ret[i]);\r\n\t\t\tif (cc == 0 || prev_cc <= cc) {\r\n\t\t\t\tprev_cc = cc;\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t\tlet j = i-1;\r\n\t\t\twhile (true) {\r\n\t\t\t\tlet tmp = ret[j+1];\r\n\t\t\t\tret[j+1] = ret[j];\r\n\t\t\t\tret[j] = tmp;\r\n\t\t\t\tif (!j) break;\r\n\t\t\t\tprev_cc = unpack_cc(ret[--j]);\r\n\t\t\t\tif (prev_cc <= cc) break;\r\n\t\t\t}\r\n\t\t\tprev_cc = unpack_cc(ret[i]);\r\n\t\t}\r\n\t}\r\n\treturn ret;\r\n}\r\n\r\nfunction composed_from_decomposed(v) {\r\n\tlet ret = [];\r\n\tlet stack = [];\r\n\tlet prev_cp = -1;\r\n\tlet prev_cc = 0;\r\n\tfor (let packed of v) {\r\n\t\tlet cc = unpack_cc(packed);\r\n\t\tlet cp = unpack_cp(packed);\r\n\t\tif (prev_cp == -1) {\r\n\t\t\tif (cc == 0) {\r\n\t\t\t\tprev_cp = cp;\r\n\t\t\t} else {\r\n\t\t\t\tret.push(cp);\r\n\t\t\t}\r\n\t\t} else if (prev_cc > 0 && prev_cc >= cc) {\r\n\t\t\tif (cc == 0) {\r\n\t\t\t\tret.push(prev_cp, ...stack);\r\n\t\t\t\tstack.length = 0;\r\n\t\t\t\tprev_cp = cp;\r\n\t\t\t} else {\r\n\t\t\t\tstack.push(cp);\r\n\t\t\t}\r\n\t\t\tprev_cc = cc;\r\n\t\t} else {\r\n\t\t\tlet composed = compose_pair(prev_cp, cp);\r\n\t\t\tif (composed >= 0) {\r\n\t\t\t\tprev_cp = composed;\r\n\t\t\t} else if (prev_cc == 0 && cc == 0) {\r\n\t\t\t\tret.push(prev_cp);\r\n\t\t\t\tprev_cp = cp;\r\n\t\t\t} else {\r\n\t\t\t\tstack.push(cp);\r\n\t\t\t\tprev_cc = cc;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tif (prev_cp >= 0) {\r\n\t\tret.push(prev_cp, ...stack);\t\r\n\t}\r\n\treturn ret;\r\n}\r\n\r\n// note: cps can be iterable\r\nfunction nfd(cps) {\r\n\treturn decomposed(cps).map(unpack_cp);\r\n}\r\nfunction nfc(cps) {\r\n\treturn composed_from_decomposed(decomposed(cps));\r\n}\n\nconst HYPHEN = 0x2D;\r\nconst STOP = 0x2E;\r\nconst STOP_CH = '.';\r\nconst FE0F = 0xFE0F;\r\nconst UNIQUE_PH = 1;\r\n\r\n// 20230913: replace [...v] with Array_from(v) to avoid large spreads\r\nconst Array_from = x => Array.from(x); // Array.from.bind(Array);\r\n\r\nfunction group_has_cp(g, cp) {\r\n\t// 20230913: keep primary and secondary distinct instead of creating valid union\r\n\treturn g.P.has(cp) || g.Q.has(cp);\r\n}\r\n\r\nclass Emoji extends Array {\r\n\tget is_emoji() { return true; } // free tagging system\r\n}\r\n\r\nlet MAPPED, IGNORED, CM, NSM, ESCAPE, NFC_CHECK, GROUPS, WHOLE_VALID, WHOLE_MAP, VALID, EMOJI_LIST, EMOJI_ROOT;\r\n\r\n// export function ens_deinit() {\r\n// \tnf_deinit();\r\n// \tif (!MAPPED) return;\r\n// \tMAPPED = IGNORED = CM = NSM = ESCAPE = NFC_CHECK = GROUPS = WHOLE_VALID = WHOLE_MAP = VALID = EMOJI_LIST = EMOJI_ROOT = undefined;\r\n// }\r\n\r\nfunction init() {\r\n\tif (MAPPED) return;\r\n\t\r\n\tlet r = read_compressed_payload(COMPRESSED$1);\r\n\tconst read_sorted_array = () => read_sorted(r);\r\n\tconst read_sorted_set = () => new Set(read_sorted_array());\r\n\tconst set_add_many = (set, v) => v.forEach(x => set.add(x));\r\n\r\n\tMAPPED = new Map(read_mapped(r)); \r\n\tIGNORED = read_sorted_set(); // ignored characters are not valid, so just read raw codepoints\r\n\r\n\t/*\r\n\t// direct include from payload is smaller than the decompression code\r\n\tconst FENCED = new Map(read_array_while(() => {\r\n\t\tlet cp = r();\r\n\t\tif (cp) return [cp, read_str(r())];\r\n\t}));\r\n\t*/\r\n\t// 20230217: we still need all CM for proper error formatting\r\n\t// but norm only needs NSM subset that are potentially-valid\r\n\tCM = read_sorted_array();\r\n\tNSM = new Set(read_sorted_array().map(i => CM[i]));\r\n\tCM = new Set(CM);\r\n\t\r\n\tESCAPE = read_sorted_set(); // characters that should not be printed\r\n\tNFC_CHECK = read_sorted_set(); // only needed to illustrate ens_tokenize() transformations\r\n\r\n\tlet chunks = read_sorted_arrays(r);\r\n\tlet unrestricted = r();\r\n\t//const read_chunked = () => new Set(read_sorted_array().flatMap(i => chunks[i]).concat(read_sorted_array()));\r\n\tconst read_chunked = () => {\r\n\t\t// 20230921: build set in parts, 2x faster\r\n\t\tlet set = new Set();\r\n\t\tread_sorted_array().forEach(i => set_add_many(set, chunks[i]));\r\n\t\tset_add_many(set, read_sorted_array());\r\n\t\treturn set; \r\n\t};\r\n\tGROUPS = read_array_while(i => {\r\n\t\t// minifier property mangling seems unsafe\r\n\t\t// so these are manually renamed to single chars\r\n\t\tlet N = read_array_while(r).map(x => x+0x60);\r\n\t\tif (N.length) {\r\n\t\t\tlet R = i >= unrestricted; // unrestricted then restricted\r\n\t\t\tN[0] -= 32; // capitalize\r\n\t\t\tN = str_from_cps(N);\r\n\t\t\tif (R) N=`Restricted[${N}]`;\r\n\t\t\tlet P = read_chunked(); // primary\r\n\t\t\tlet Q = read_chunked(); // secondary\r\n\t\t\tlet M = !r(); // not-whitelisted, check for NSM\r\n\t\t\t// *** this code currently isn't needed ***\r\n\t\t\t/*\r\n\t\t\tlet V = [...P, ...Q].sort((a, b) => a-b); // derive: sorted valid\r\n\t\t\tlet M = r()-1; // number of combining mark\r\n\t\t\tif (M < 0) { // whitelisted\r\n\t\t\t\tM = new Map(read_array_while(() => {\r\n\t\t\t\t\tlet i = r();\r\n\t\t\t\t\tif (i) return [V[i-1], read_array_while(() => {\r\n\t\t\t\t\t\tlet v = read_array_while(r);\r\n\t\t\t\t\t\tif (v.length) return v.map(x => x-1);\r\n\t\t\t\t\t})];\r\n\t\t\t\t}));\r\n\t\t\t}*/\r\n\t\t\treturn {N, P, Q, M, R};\r\n\t\t}\r\n\t});\r\n\r\n\t// decode compressed wholes\r\n\tWHOLE_VALID = read_sorted_set();\r\n\tWHOLE_MAP = new Map();\r\n\tlet wholes = read_sorted_array().concat(Array_from(WHOLE_VALID)).sort((a, b) => a-b); // must be sorted\r\n\twholes.forEach((cp, i) => {\r\n\t\tlet d = r(); \r\n\t\tlet w = wholes[i] = d ? wholes[i-d] : {V: [], M: new Map()};\r\n\t\tw.V.push(cp); // add to member set\r\n\t\tif (!WHOLE_VALID.has(cp)) {\r\n\t\t\tWHOLE_MAP.set(cp, w);  // register with whole map\r\n\t\t}\r\n\t});\r\n\r\n\t// compute confusable-extent complements\r\n\t// usage: WHOLE_MAP.get(cp).M.get(cp) = complement set\r\n\tfor (let {V, M} of new Set(WHOLE_MAP.values())) {\r\n\t\t// connect all groups that have each whole character\r\n\t\tlet recs = [];\r\n\t\tfor (let cp of V) {\r\n\t\t\tlet gs = GROUPS.filter(g => group_has_cp(g, cp));\r\n\t\t\tlet rec = recs.find(({G}) => gs.some(g => G.has(g)));\r\n\t\t\tif (!rec) {\r\n\t\t\t\trec = {G: new Set(), V: []};\r\n\t\t\t\trecs.push(rec);\r\n\t\t\t}\r\n\t\t\trec.V.push(cp);\r\n\t\t\tset_add_many(rec.G, gs);\r\n\t\t}\r\n\t\t// per character cache groups which are not a member of the extent\r\n\t\tlet union = recs.flatMap(x => Array_from(x.G)); // all of the groups used by this whole\r\n\t\tfor (let {G, V} of recs) {\r\n\t\t\tlet complement = new Set(union.filter(g => !G.has(g))); // groups not covered by the extent\r\n\t\t\tfor (let cp of V) {\r\n\t\t\t\tM.set(cp, complement); // this is the same reference\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// compute valid set\r\n\t// 20230924: VALID was union but can be re-used\r\n\tVALID = new Set(); // exists in 1+ groups\r\n\tlet multi = new Set(); // exists in 2+ groups\r\n\tconst add_to_union = cp => VALID.has(cp) ? multi.add(cp) : VALID.add(cp);\r\n\tfor (let g of GROUPS) {\r\n\t\tfor (let cp of g.P) add_to_union(cp);\r\n\t\tfor (let cp of g.Q) add_to_union(cp);\r\n\t}\r\n\t// dual purpose WHOLE_MAP: return placeholder if unique non-confusable\r\n\tfor (let cp of VALID) {\r\n\t\tif (!WHOLE_MAP.has(cp) && !multi.has(cp)) {\r\n\t\t\tWHOLE_MAP.set(cp, UNIQUE_PH);\r\n\t\t}\r\n\t}\r\n\t// add all decomposed parts\r\n\t// see derive: \"Valid is Closed (via Brute-force)\"\r\n\tset_add_many(VALID, nfd(VALID));\r\n\t\r\n\t// decode emoji\r\n\t// 20230719: emoji are now fully-expanded to avoid quirk logic \r\n\tEMOJI_LIST = read_trie(r).map(v => Emoji.from(v)).sort(compare_arrays);\r\n\tEMOJI_ROOT = new Map(); // this has approx 7K nodes (2+ per emoji)\r\n\tfor (let cps of EMOJI_LIST) {\r\n\t\t// 20230719: change to *slightly* stricter algorithm which disallows \r\n\t\t// insertion of misplaced FE0F in emoji sequences (matching ENSIP-15)\r\n\t\t// example: beautified [A B] (eg. flag emoji) \r\n\t\t//  before: allow: [A FE0F B], error: [A FE0F FE0F B] \r\n\t\t//   after: error: both\r\n\t\t// note: this code now matches ENSNormalize.{cs,java} logic\r\n\t\tlet prev = [EMOJI_ROOT];\r\n\t\tfor (let cp of cps) {\r\n\t\t\tlet next = prev.map(node => {\r\n\t\t\t\tlet child = node.get(cp);\r\n\t\t\t\tif (!child) {\r\n\t\t\t\t\t// should this be object? \r\n\t\t\t\t\t// (most have 1-2 items, few have many)\r\n\t\t\t\t\t// 20230719: no, v8 default map is 4?\r\n\t\t\t\t\tchild = new Map();\r\n\t\t\t\t\tnode.set(cp, child);\r\n\t\t\t\t}\r\n\t\t\t\treturn child;\r\n\t\t\t});\r\n\t\t\tif (cp === FE0F) {\r\n\t\t\t\tprev.push(...next); // less than 20 elements\r\n\t\t\t} else {\r\n\t\t\t\tprev = next;\r\n\t\t\t}\r\n\t\t}\r\n\t\tfor (let x of prev) {\r\n\t\t\tx.V = cps;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// if escaped: {HEX}\r\n//       else: \"x\" {HEX}\r\nfunction quoted_cp(cp) {\r\n\treturn (should_escape(cp) ? '' : `${bidi_qq(safe_str_from_cps([cp]))} `) + quote_cp(cp);\r\n}\r\n\r\n// 20230211: some messages can be mixed-directional and result in spillover\r\n// use 200E after a quoted string to force the remainder of a string from \r\n// acquring the direction of the quote\r\n// https://www.w3.org/International/questions/qa-bidi-unicode-controls#exceptions\r\nfunction bidi_qq(s) {\r\n\treturn `\"${s}\"\\u200E`; // strong LTR\r\n}\r\n\r\nfunction check_label_extension(cps) {\r\n\tif (cps.length >= 4 && cps[2] == HYPHEN && cps[3] == HYPHEN) {\r\n\t\tthrow new Error(`invalid label extension: \"${str_from_cps(cps.slice(0, 4))}\"`); // this can only be ascii so cant be bidi\r\n\t}\r\n}\r\nfunction check_leading_underscore(cps) {\r\n\tconst UNDERSCORE = 0x5F;\r\n\tfor (let i = cps.lastIndexOf(UNDERSCORE); i > 0; ) {\r\n\t\tif (cps[--i] !== UNDERSCORE) {\r\n\t\t\tthrow new Error('underscore allowed only at start');\r\n\t\t}\r\n\t}\r\n}\r\n// check that a fenced cp is not leading, trailing, or touching another fenced cp\r\nfunction check_fenced(cps) {\r\n\tlet cp = cps[0];\r\n\tlet prev = FENCED.get(cp);\r\n\tif (prev) throw error_placement(`leading ${prev}`);\r\n\tlet n = cps.length;\r\n\tlet last = -1; // prevents trailing from throwing\r\n\tfor (let i = 1; i < n; i++) {\r\n\t\tcp = cps[i];\r\n\t\tlet match = FENCED.get(cp);\r\n\t\tif (match) {\r\n\t\t\t// since cps[0] isn't fenced, cps[1] cannot throw\r\n\t\t\tif (last == i) throw error_placement(`${prev} + ${match}`);\r\n\t\t\tlast = i + 1;\r\n\t\t\tprev = match;\r\n\t\t}\r\n\t}\r\n\tif (last == n) throw error_placement(`trailing ${prev}`);\r\n}\r\n\r\n// create a safe to print string \r\n// invisibles are escaped\r\n// leading cm uses placeholder\r\n// if cps exceed max, middle truncate with ellipsis\r\n// quoter(cp) => string, eg. 3000 => \"{3000}\"\r\n// note: in html, you'd call this function then replace [<>&] with entities\r\nfunction safe_str_from_cps(cps, max = Infinity, quoter = quote_cp) {\r\n\t//if (Number.isInteger(cps)) cps = [cps];\r\n\t//if (!Array.isArray(cps)) throw new TypeError(`expected codepoints`);\r\n\tlet buf = [];\r\n\tif (is_combining_mark(cps[0])) buf.push('◌');\r\n\tif (cps.length > max) {\r\n\t\tmax >>= 1;\r\n\t\tcps = [...cps.slice(0, max), 0x2026, ...cps.slice(-max)];\r\n\t}\r\n\tlet prev = 0;\r\n\tlet n = cps.length;\r\n\tfor (let i = 0; i < n; i++) {\r\n\t\tlet cp = cps[i];\r\n\t\tif (should_escape(cp)) {\r\n\t\t\tbuf.push(str_from_cps(cps.slice(prev, i)));\r\n\t\t\tbuf.push(quoter(cp));\r\n\t\t\tprev = i + 1;\r\n\t\t}\r\n\t}\r\n\tbuf.push(str_from_cps(cps.slice(prev, n)));\r\n\treturn buf.join('');\r\n}\r\n\r\n// note: set(s) cannot be exposed because they can be modified\r\n// note: Object.freeze() doesn't work\r\nfunction is_combining_mark(cp, only_nsm) { // 20240127: add extra argument\r\n\tinit();\r\n\treturn only_nsm ? NSM.has(cp) : CM.has(cp);\r\n}\r\nfunction should_escape(cp) {\r\n\tinit();\r\n\treturn ESCAPE.has(cp);\r\n}\r\n\r\n// return all supported emoji as fully-qualified emoji \r\n// ordered by length then lexicographic \r\nfunction ens_emoji() {\r\n\tinit();\r\n\treturn EMOJI_LIST.map(x => x.slice()); // emoji are exposed so copy\r\n}\r\n\r\nfunction ens_normalize_fragment(frag, decompose) {\r\n\tinit();\r\n\tlet nf = decompose ? nfd : nfc;\r\n\treturn frag.split(STOP_CH).map(label => str_from_cps(tokens_from_str(explode_cp(label), nf, filter_fe0f).flat())).join(STOP_CH);\r\n}\r\n\r\nfunction ens_normalize(name) {\r\n\treturn flatten(split(name, nfc, filter_fe0f));\r\n}\r\n\r\nfunction ens_beautify(name) {\r\n\tlet labels = split(name, nfc, x => x); // emoji not exposed\r\n\tfor (let {type, output, error} of labels) {\r\n\t\tif (error) break; // flatten will throw\r\n\r\n\t\t// replace leading/trailing hyphen\r\n\t\t// 20230121: consider beautifing all or leading/trailing hyphen to unicode variant\r\n\t\t// not exactly the same in every font, but very similar: \"-\" vs \"‐\"\r\n\t\t/*\r\n\t\tconst UNICODE_HYPHEN = 0x2010;\r\n\t\t// maybe this should replace all for visual consistancy?\r\n\t\t// `node tools/reg-count.js regex ^-\\{2,\\}` => 592\r\n\t\t//for (let i = 0; i < output.length; i++) if (output[i] == 0x2D) output[i] = 0x2010;\r\n\t\tif (output[0] == HYPHEN) output[0] = UNICODE_HYPHEN;\r\n\t\tlet end = output.length-1;\r\n\t\tif (output[end] == HYPHEN) output[end] = UNICODE_HYPHEN;\r\n\t\t*/\r\n\t\t// 20230123: WHATWG URL uses \"CheckHyphens\" false\r\n\t\t// https://url.spec.whatwg.org/#idna\r\n\r\n\t\t// update ethereum symbol\r\n\t\t// ξ => Ξ if not greek\r\n\t\tif (type !== 'Greek') array_replace(output, 0x3BE, 0x39E);\r\n\r\n\t\t// 20221213: fixes bidi subdomain issue, but breaks invariant (200E is disallowed)\r\n\t\t// could be fixed with special case for: 2D (.) + 200E (LTR)\r\n\t\t// https://discuss.ens.domains/t/bidi-label-ordering-spoof/15824\r\n\t\t//output.splice(0, 0, 0x200E);\r\n\t}\r\n\treturn flatten(labels);\r\n}\r\n\r\nfunction ens_split(name, preserve_emoji) {\r\n\treturn split(name, nfc, preserve_emoji ? x => x.slice() : filter_fe0f); // emoji are exposed so copy\r\n}\r\n\r\nfunction split(name, nf, ef) {\r\n\tif (!name) return []; // 20230719: empty name allowance\r\n\tinit();\r\n\tlet offset = 0;\r\n\t// https://unicode.org/reports/tr46/#Validity_Criteria\r\n\t// 4.) \"The label must not contain a U+002E ( . ) FULL STOP.\"\r\n\treturn name.split(STOP_CH).map(label => {\r\n\t\tlet input = explode_cp(label);\r\n\t\tlet info = {\r\n\t\t\tinput,\r\n\t\t\toffset, // codepoint, not substring!\r\n\t\t};\r\n\t\toffset += input.length + 1; // + stop\r\n\t\ttry {\r\n\t\t\t// 1.) \"The label must be in Unicode Normalization Form NFC\"\r\n\t\t\tlet tokens = info.tokens = tokens_from_str(input, nf, ef);\r\n\t\t\tlet token_count = tokens.length;\r\n\t\t\tlet type;\r\n\t\t\tif (!token_count) { // the label was effectively empty (could of had ignored characters)\r\n\t\t\t\t//norm = [];\r\n\t\t\t\t//type = 'None'; // use this instead of next match, \"ASCII\"\r\n\t\t\t\t// 20230120: change to strict\r\n\t\t\t\t// https://discuss.ens.domains/t/ens-name-normalization-2nd/14564/59\r\n\t\t\t\tthrow new Error(`empty label`);\r\n\t\t\t} \r\n\t\t\tlet norm = info.output = tokens.flat();\r\n\t\t\tcheck_leading_underscore(norm);\r\n\t\t\tlet emoji = info.emoji = token_count > 1 || tokens[0].is_emoji; // same as: tokens.some(x => x.is_emoji);\r\n\t\t\tif (!emoji && norm.every(cp => cp < 0x80)) { // special case for ascii\r\n\t\t\t\t// 20230123: matches matches WHATWG, see note 3.3\r\n\t\t\t\tcheck_label_extension(norm); // only needed for ascii\r\n\t\t\t\t// cant have fenced\r\n\t\t\t\t// cant have cm\r\n\t\t\t\t// cant have wholes\r\n\t\t\t\t// see derive: \"Fastpath ASCII\"\r\n\t\t\t\ttype = 'ASCII';\r\n\t\t\t} else {\r\n\t\t\t\tlet chars = tokens.flatMap(x => x.is_emoji ? [] : x); // all of the nfc tokens concat together\r\n\t\t\t\tif (!chars.length) { // theres no text, just emoji\r\n\t\t\t\t\ttype = 'Emoji';\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 5.) \"The label must not begin with a combining mark, that is: General_Category=Mark.\"\r\n\t\t\t\t\tif (CM.has(norm[0])) throw error_placement('leading combining mark');\r\n\t\t\t\t\tfor (let i = 1; i < token_count; i++) { // we've already checked the first token\r\n\t\t\t\t\t\tlet cps = tokens[i];\r\n\t\t\t\t\t\tif (!cps.is_emoji && CM.has(cps[0])) { // every text token has emoji neighbors, eg. EtEEEtEt...\r\n\t\t\t\t\t\t\t// bidi_qq() not needed since emoji is LTR and cps is a CM\r\n\t\t\t\t\t\t\tthrow error_placement(`emoji + combining mark: \"${str_from_cps(tokens[i-1])} + ${safe_str_from_cps([cps[0]])}\"`); \r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tcheck_fenced(norm);\r\n\t\t\t\t\tlet unique = Array_from(new Set(chars));\r\n\t\t\t\t\tlet [g] = determine_group(unique); // take the first match\r\n\t\t\t\t\t// see derive: \"Matching Groups have Same CM Style\"\r\n\t\t\t\t\t// alternative: could form a hybrid type: Latin/Japanese/...\t\r\n\t\t\t\t\tcheck_group(g, chars); // need text in order\r\n\t\t\t\t\tcheck_whole(g, unique); // only need unique text (order would be required for multiple-char confusables)\r\n\t\t\t\t\ttype = g.N;\r\n\t\t\t\t\t// 20230121: consider exposing restricted flag\r\n\t\t\t\t\t// it's simpler to just check for 'Restricted'\r\n\t\t\t\t\t// or even better: type.endsWith(']')\r\n\t\t\t\t\t//if (g.R) info.restricted = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tinfo.type = type;\r\n\t\t} catch (err) {\r\n\t\t\tinfo.error = err; // use full error object\r\n\t\t}\r\n\t\treturn info;\r\n\t});\r\n}\r\n\r\nfunction check_whole(group, unique) {\r\n\tlet maker;\r\n\tlet shared = [];\r\n\tfor (let cp of unique) {\r\n\t\tlet whole = WHOLE_MAP.get(cp);\r\n\t\tif (whole === UNIQUE_PH) return; // unique, non-confusable\r\n\t\tif (whole) {\r\n\t\t\tlet set = whole.M.get(cp); // groups which have a character that look-like this character\r\n\t\t\tmaker = maker ? maker.filter(g => set.has(g)) : Array_from(set);\r\n\t\t\tif (!maker.length) return; // confusable intersection is empty\r\n\t\t} else {\r\n\t\t\tshared.push(cp); \r\n\t\t}\r\n\t}\r\n\tif (maker) {\r\n\t\t// we have 1+ confusable\r\n\t\t// check if any of the remaining groups\r\n\t\t// contain the shared characters too\r\n\t\tfor (let g of maker) {\r\n\t\t\tif (shared.every(cp => group_has_cp(g, cp))) {\r\n\t\t\t\tthrow new Error(`whole-script confusable: ${group.N}/${g.N}`);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// assumption: unique.size > 0\r\n// returns list of matching groups\r\nfunction determine_group(unique) {\r\n\tlet groups = GROUPS;\r\n\tfor (let cp of unique) {\r\n\t\t// note: we need to dodge CM that are whitelisted\r\n\t\t// but that code isn't currently necessary\r\n\t\tlet gs = groups.filter(g => group_has_cp(g, cp));\r\n\t\tif (!gs.length) {\r\n\t\t\tif (!GROUPS.some(g => group_has_cp(g, cp))) { \r\n\t\t\t\t// the character was composed of valid parts\r\n\t\t\t\t// but it's NFC form is invalid\r\n\t\t\t\t// 20230716: change to more exact statement, see: ENSNormalize.{cs,java}\r\n\t\t\t\t// note: this doesn't have to be a composition\r\n\t\t\t\t// 20230720: change to full check\r\n\t\t\t\tthrow error_disallowed(cp); // this should be rare\r\n\t\t\t} else {\r\n\t\t\t\t// there is no group that contains all these characters\r\n\t\t\t\t// throw using the highest priority group that matched\r\n\t\t\t\t// https://www.unicode.org/reports/tr39/#mixed_script_confusables\r\n\t\t\t\tthrow error_group_member(groups[0], cp);\r\n\t\t\t}\r\n\t\t}\r\n\t\tgroups = gs;\r\n\t\tif (gs.length == 1) break; // there is only one group left\r\n\t}\r\n\t// there are at least 1 group(s) with all of these characters\r\n\treturn groups;\r\n}\r\n\r\n// throw on first error\r\nfunction flatten(split) {\r\n\treturn split.map(({input, error, output}) => {\r\n\t\tif (error) {\r\n\t\t\t// don't print label again if just a single label\r\n\t\t\tlet msg = error.message;\r\n\t\t\t// bidi_qq() only necessary if msg is digits\r\n\t\t\tthrow new Error(split.length == 1 ? msg : `Invalid label ${bidi_qq(safe_str_from_cps(input, 63))}: ${msg}`); \r\n\t\t}\r\n\t\treturn str_from_cps(output);\r\n\t}).join(STOP_CH);\r\n}\r\n\r\nfunction error_disallowed(cp) {\r\n\t// TODO: add cp to error?\r\n\treturn new Error(`disallowed character: ${quoted_cp(cp)}`); \r\n}\r\nfunction error_group_member(g, cp) {\r\n\tlet quoted = quoted_cp(cp);\r\n\tlet gg = GROUPS.find(g => g.P.has(cp)); // only check primary\r\n\tif (gg) {\r\n\t\tquoted = `${gg.N} ${quoted}`;\r\n\t}\r\n\treturn new Error(`illegal mixture: ${g.N} + ${quoted}`);\r\n}\r\nfunction error_placement(where) {\r\n\treturn new Error(`illegal placement: ${where}`);\r\n}\r\n\r\n// assumption: cps.length > 0\r\n// assumption: cps[0] isn't a CM\r\n// assumption: the previous character isn't an emoji\r\nfunction check_group(g, cps) {\r\n\tfor (let cp of cps) {\r\n\t\tif (!group_has_cp(g, cp)) {\r\n\t\t\t// for whitelisted scripts, this will throw illegal mixture on invalid cm, eg. \"e{300}{300}\"\r\n\t\t\t// at the moment, it's unnecessary to introduce an extra error type\r\n\t\t\t// until there exists a whitelisted multi-character\r\n\t\t\t//   eg. if (M < 0 && is_combining_mark(cp)) { ... }\r\n\t\t\t// there are 3 cases:\r\n\t\t\t//   1. illegal cm for wrong group => mixture error\r\n\t\t\t//   2. illegal cm for same group => cm error\r\n\t\t\t//       requires set of whitelist cm per group: \r\n\t\t\t//        eg. new Set([...g.P, ...g.Q].flatMap(nfc).filter(cp => CM.has(cp)))\r\n\t\t\t//   3. wrong group => mixture error\r\n\t\t\tthrow error_group_member(g, cp);\r\n\t\t}\r\n\t}\r\n\t//if (M >= 0) { // we have a known fixed cm count\r\n\tif (g.M) { // we need to check for NSM\r\n\t\tlet decomposed = nfd(cps);\r\n\t\tfor (let i = 1, e = decomposed.length; i < e; i++) { // see: assumption\r\n\t\t\t// 20230210: bugfix: using cps instead of decomposed h/t Carbon225\r\n\t\t\t/*\r\n\t\t\tif (CM.has(decomposed[i])) {\r\n\t\t\t\tlet j = i + 1;\r\n\t\t\t\twhile (j < e && CM.has(decomposed[j])) j++;\r\n\t\t\t\tif (j - i > M) {\r\n\t\t\t\t\tthrow new Error(`too many combining marks: ${g.N} ${bidi_qq(str_from_cps(decomposed.slice(i-1, j)))} (${j-i}/${M})`);\r\n\t\t\t\t}\r\n\t\t\t\ti = j;\r\n\t\t\t}\r\n\t\t\t*/\r\n\t\t\t// 20230217: switch to NSM counting\r\n\t\t\t// https://www.unicode.org/reports/tr39/#Optional_Detection\r\n\t\t\tif (NSM.has(decomposed[i])) {\r\n\t\t\t\tlet j = i + 1;\r\n\t\t\t\tfor (let cp; j < e && NSM.has(cp = decomposed[j]); j++) {\r\n\t\t\t\t\t// a. Forbid sequences of the same nonspacing mark.\r\n\t\t\t\t\tfor (let k = i; k < j; k++) { // O(n^2) but n < 100\r\n\t\t\t\t\t\tif (decomposed[k] == cp) {\r\n\t\t\t\t\t\t\tthrow new Error(`duplicate non-spacing marks: ${quoted_cp(cp)}`);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// parse to end so we have full nsm count\r\n\t\t\t\t// b. Forbid sequences of more than 4 nonspacing marks (gc=Mn or gc=Me).\r\n\t\t\t\tif (j - i > NSM_MAX) {\r\n\t\t\t\t\t// note: this slice starts with a base char or spacing-mark cm\r\n\t\t\t\t\tthrow new Error(`excessive non-spacing marks: ${bidi_qq(safe_str_from_cps(decomposed.slice(i-1, j)))} (${j-i}/${NSM_MAX})`);\r\n\t\t\t\t}\r\n\t\t\t\ti = j;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t// *** this code currently isn't needed ***\r\n\t/*\r\n\tlet cm_whitelist = M instanceof Map;\r\n\tfor (let i = 0, e = cps.length; i < e; ) {\r\n\t\tlet cp = cps[i++];\r\n\t\tlet seqs = cm_whitelist && M.get(cp);\r\n\t\tif (seqs) { \r\n\t\t\t// list of codepoints that can follow\r\n\t\t\t// if this exists, this will always be 1+\r\n\t\t\tlet j = i;\r\n\t\t\twhile (j < e && CM.has(cps[j])) j++;\r\n\t\t\tlet cms = cps.slice(i, j);\r\n\t\t\tlet match = seqs.find(seq => !compare_arrays(seq, cms));\r\n\t\t\tif (!match) throw new Error(`disallowed combining mark sequence: \"${safe_str_from_cps([cp, ...cms])}\"`);\r\n\t\t\ti = j;\r\n\t\t} else if (!V.has(cp)) {\r\n\t\t\t// https://www.unicode.org/reports/tr39/#mixed_script_confusables\r\n\t\t\tlet quoted = quoted_cp(cp);\r\n\t\t\tfor (let cp of cps) {\r\n\t\t\t\tlet u = UNIQUE.get(cp);\r\n\t\t\t\tif (u && u !== g) {\r\n\t\t\t\t\t// if both scripts are restricted this error is confusing\r\n\t\t\t\t\t// because we don't differentiate RestrictedA from RestrictedB \r\n\t\t\t\t\tif (!u.R) quoted = `${quoted} is ${u.N}`;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthrow new Error(`disallowed ${g.N} character: ${quoted}`);\r\n\t\t\t//throw new Error(`disallowed character: ${quoted} (expected ${g.N})`);\r\n\t\t\t//throw new Error(`${g.N} does not allow: ${quoted}`);\r\n\t\t}\r\n\t}\r\n\tif (!cm_whitelist) {\r\n\t\tlet decomposed = nfd(cps);\r\n\t\tfor (let i = 1, e = decomposed.length; i < e; i++) { // we know it can't be cm leading\r\n\t\t\tif (CM.has(decomposed[i])) {\r\n\t\t\t\tlet j = i + 1;\r\n\t\t\t\twhile (j < e && CM.has(decomposed[j])) j++;\r\n\t\t\t\tif (j - i > M) {\r\n\t\t\t\t\tthrow new Error(`too many combining marks: \"${str_from_cps(decomposed.slice(i-1, j))}\" (${j-i}/${M})`);\r\n\t\t\t\t}\r\n\t\t\t\ti = j;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t*/\r\n}\r\n\r\n// given a list of codepoints\r\n// returns a list of lists, where emoji are a fully-qualified (as Array subclass)\r\n// eg. explode_cp(\"abc💩d\") => [[61, 62, 63], Emoji[1F4A9, FE0F], [64]]\r\n// 20230818: rename for 'process' name collision h/t Javarome\r\n// https://github.com/adraffy/ens-normalize.js/issues/23\r\nfunction tokens_from_str(input, nf, ef) {\r\n\tlet ret = [];\r\n\tlet chars = [];\r\n\tinput = input.slice().reverse(); // flip so we can pop\r\n\twhile (input.length) {\r\n\t\tlet emoji = consume_emoji_reversed(input);\r\n\t\tif (emoji) {\r\n\t\t\tif (chars.length) {\r\n\t\t\t\tret.push(nf(chars));\r\n\t\t\t\tchars = [];\r\n\t\t\t}\r\n\t\t\tret.push(ef(emoji));\r\n\t\t} else {\r\n\t\t\tlet cp = input.pop();\r\n\t\t\tif (VALID.has(cp)) {\r\n\t\t\t\tchars.push(cp);\r\n\t\t\t} else {\r\n\t\t\t\tlet cps = MAPPED.get(cp);\r\n\t\t\t\tif (cps) {\r\n\t\t\t\t\tchars.push(...cps); // less than 10 elements\r\n\t\t\t\t} else if (!IGNORED.has(cp)) {\r\n\t\t\t\t\t// 20230912: unicode 15.1 changed the order of processing such that\r\n\t\t\t\t\t// disallowed parts are only rejected after NFC\r\n\t\t\t\t\t// https://unicode.org/reports/tr46/#Validity_Criteria\r\n\t\t\t\t\t// this doesn't impact normalization as of today\r\n\t\t\t\t\t// technically, this error can be removed as the group logic will apply similar logic\r\n\t\t\t\t\t// however the error type might be less clear\r\n\t\t\t\t\tthrow error_disallowed(cp);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tif (chars.length) {\r\n\t\tret.push(nf(chars));\r\n\t}\r\n\treturn ret;\r\n}\r\n\r\nfunction filter_fe0f(cps) {\r\n\treturn cps.filter(cp => cp != FE0F);\r\n}\r\n\r\n// given array of codepoints\r\n// returns the longest valid emoji sequence (or undefined if no match)\r\n// *MUTATES* the supplied array\r\n// disallows interleaved ignored characters\r\n// fills (optional) eaten array with matched codepoints\r\nfunction consume_emoji_reversed(cps, eaten) {\r\n\tlet node = EMOJI_ROOT;\r\n\tlet emoji;\r\n\tlet pos = cps.length;\r\n\twhile (pos) {\r\n\t\tnode = node.get(cps[--pos]);\r\n\t\tif (!node) break;\r\n\t\tlet {V} = node;\r\n\t\tif (V) { // this is a valid emoji (so far)\r\n\t\t\temoji = V;\r\n\t\t\tif (eaten) eaten.push(...cps.slice(pos).reverse()); // (optional) copy input, used for ens_tokenize()\r\n\t\t\tcps.length = pos; // truncate\r\n\t\t}\r\n\t}\r\n\treturn emoji;\r\n}\r\n\r\n// ************************************************************\r\n// tokenizer \r\n\r\nconst TY_VALID = 'valid';\r\nconst TY_MAPPED = 'mapped';\r\nconst TY_IGNORED = 'ignored';\r\nconst TY_DISALLOWED = 'disallowed';\r\nconst TY_EMOJI = 'emoji';\r\nconst TY_NFC = 'nfc';\r\nconst TY_STOP = 'stop';\r\n\r\nfunction ens_tokenize(name, {\r\n\tnf = true, // collapse unnormalized runs into a single token\r\n} = {}) {\r\n\tinit();\r\n\tlet input = explode_cp(name).reverse();\r\n\tlet eaten = [];\r\n\tlet tokens = [];\r\n\twhile (input.length) {\r\n\t\tlet emoji = consume_emoji_reversed(input, eaten);\r\n\t\tif (emoji) {\r\n\t\t\ttokens.push({\r\n\t\t\t\ttype: TY_EMOJI,\r\n\t\t\t\temoji: emoji.slice(), // copy emoji\r\n\t\t\t\tinput: eaten,\r\n\t\t\t\tcps: filter_fe0f(emoji)\r\n\t\t\t});\r\n\t\t\teaten = []; // reset buffer\r\n\t\t} else {\r\n\t\t\tlet cp = input.pop();\r\n\t\t\tif (cp == STOP) {\r\n\t\t\t\ttokens.push({type: TY_STOP, cp});\r\n\t\t\t} else if (VALID.has(cp)) {\r\n\t\t\t\ttokens.push({type: TY_VALID, cps: [cp]});\r\n\t\t\t} else if (IGNORED.has(cp)) {\r\n\t\t\t\ttokens.push({type: TY_IGNORED, cp});\r\n\t\t\t} else {\r\n\t\t\t\tlet cps = MAPPED.get(cp);\r\n\t\t\t\tif (cps) {\r\n\t\t\t\t\ttokens.push({type: TY_MAPPED, cp, cps: cps.slice()});\r\n\t\t\t\t} else {\r\n\t\t\t\t\ttokens.push({type: TY_DISALLOWED, cp});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tif (nf) {\r\n\t\tfor (let i = 0, start = -1; i < tokens.length; i++) {\r\n\t\t\tlet token = tokens[i];\r\n\t\t\tif (is_valid_or_mapped(token.type)) {\r\n\t\t\t\tif (requires_check(token.cps)) { // normalization might be needed\r\n\t\t\t\t\tlet end = i + 1;\r\n\t\t\t\t\tfor (let pos = end; pos < tokens.length; pos++) { // find adjacent text\r\n\t\t\t\t\t\tlet {type, cps} = tokens[pos];\r\n\t\t\t\t\t\tif (is_valid_or_mapped(type)) {\r\n\t\t\t\t\t\t\tif (!requires_check(cps)) break;\r\n\t\t\t\t\t\t\tend = pos + 1;\r\n\t\t\t\t\t\t} else if (type !== TY_IGNORED) { // || type !== TY_DISALLOWED) { \r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (start < 0) start = i;\r\n\t\t\t\t\tlet slice = tokens.slice(start, end);\r\n\t\t\t\t\tlet cps0 = slice.flatMap(x => is_valid_or_mapped(x.type) ? x.cps : []); // strip junk tokens\r\n\t\t\t\t\tlet cps = nfc(cps0);\r\n\t\t\t\t\tif (compare_arrays(cps, cps0)) { // bundle into an nfc token\r\n\t\t\t\t\t\ttokens.splice(start, end - start, {\r\n\t\t\t\t\t\t\ttype: TY_NFC, \r\n\t\t\t\t\t\t\tinput: cps0, // there are 3 states: tokens0 ==(process)=> input ==(nfc)=> tokens/cps\r\n\t\t\t\t\t\t\tcps, \r\n\t\t\t\t\t\t\ttokens0: collapse_valid_tokens(slice),\r\n\t\t\t\t\t\t\ttokens: ens_tokenize(str_from_cps(cps), {nf: false})\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\ti = start;\r\n\t\t\t\t\t} else { \r\n\t\t\t\t\t\ti = end - 1; // skip to end of slice\r\n\t\t\t\t\t}\r\n\t\t\t\t\tstart = -1; // reset\r\n\t\t\t\t} else {\r\n\t\t\t\t\tstart = i; // remember last\r\n\t\t\t\t}\r\n\t\t\t} else if (token.type !== TY_IGNORED) { // 20221024: is this correct?\r\n\t\t\t\tstart = -1; // reset\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn collapse_valid_tokens(tokens);\r\n}\r\n\r\nfunction is_valid_or_mapped(type) {\r\n\treturn type == TY_VALID || type == TY_MAPPED;\r\n}\r\n\r\nfunction requires_check(cps) {\r\n\treturn cps.some(cp => NFC_CHECK.has(cp));\r\n}\r\n\r\nfunction collapse_valid_tokens(tokens) {\r\n\tfor (let i = 0; i < tokens.length; i++) {\r\n\t\tif (tokens[i].type == TY_VALID) {\r\n\t\t\tlet j = i + 1;\r\n\t\t\twhile (j < tokens.length && tokens[j].type == TY_VALID) j++;\r\n\t\t\ttokens.splice(i, j - i, {type: TY_VALID, cps: tokens.slice(i, j).flatMap(x => x.cps)});\r\n\t\t}\r\n\t}\r\n\treturn tokens;\r\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@adraffy/ens-normalize/dist/index.mjs\n");

/***/ })

};
;