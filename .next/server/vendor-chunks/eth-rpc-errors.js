"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eth-rpc-errors";
exports.ids = ["vendor-chunks/eth-rpc-errors"];
exports.modules = {

/***/ "(ssr)/./node_modules/eth-rpc-errors/dist/classes.js":
/*!*****************************************************!*\
  !*** ./node_modules/eth-rpc-errors/dist/classes.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.EthereumProviderError = exports.EthereumRpcError = void 0;\nconst fast_safe_stringify_1 = __webpack_require__(/*! fast-safe-stringify */ \"(ssr)/./node_modules/fast-safe-stringify/index.js\");\n/**\n * Error subclass implementing JSON RPC 2.0 errors and Ethereum RPC errors\n * per EIP-1474.\n * Permits any integer error code.\n */\nclass EthereumRpcError extends Error {\n    constructor(code, message, data) {\n        if (!Number.isInteger(code)) {\n            throw new Error('\"code\" must be an integer.');\n        }\n        if (!message || typeof message !== 'string') {\n            throw new Error('\"message\" must be a nonempty string.');\n        }\n        super(message);\n        this.code = code;\n        if (data !== undefined) {\n            this.data = data;\n        }\n    }\n    /**\n     * Returns a plain object with all public class properties.\n     */\n    serialize() {\n        const serialized = {\n            code: this.code,\n            message: this.message,\n        };\n        if (this.data !== undefined) {\n            serialized.data = this.data;\n        }\n        if (this.stack) {\n            serialized.stack = this.stack;\n        }\n        return serialized;\n    }\n    /**\n     * Return a string representation of the serialized error, omitting\n     * any circular references.\n     */\n    toString() {\n        return fast_safe_stringify_1.default(this.serialize(), stringifyReplacer, 2);\n    }\n}\nexports.EthereumRpcError = EthereumRpcError;\n/**\n * Error subclass implementing Ethereum Provider errors per EIP-1193.\n * Permits integer error codes in the [ 1000 <= 4999 ] range.\n */\nclass EthereumProviderError extends EthereumRpcError {\n    /**\n     * Create an Ethereum Provider JSON-RPC error.\n     * `code` must be an integer in the 1000 <= 4999 range.\n     */\n    constructor(code, message, data) {\n        if (!isValidEthProviderCode(code)) {\n            throw new Error('\"code\" must be an integer such that: 1000 <= code <= 4999');\n        }\n        super(code, message, data);\n    }\n}\nexports.EthereumProviderError = EthereumProviderError;\n// Internal\nfunction isValidEthProviderCode(code) {\n    return Number.isInteger(code) && code >= 1000 && code <= 4999;\n}\nfunction stringifyReplacer(_, value) {\n    if (value === '[Circular]') {\n        return undefined;\n    }\n    return value;\n}\n//# sourceMappingURL=data:application/json;base64,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//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-rpc-errors/dist/classes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-rpc-errors/dist/error-constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/eth-rpc-errors/dist/error-constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.errorValues = exports.errorCodes = void 0;\nexports.errorCodes = {\n    rpc: {\n        invalidInput: -32000,\n        resourceNotFound: -32001,\n        resourceUnavailable: -32002,\n        transactionRejected: -32003,\n        methodNotSupported: -32004,\n        limitExceeded: -32005,\n        parse: -32700,\n        invalidRequest: -32600,\n        methodNotFound: -32601,\n        invalidParams: -32602,\n        internal: -32603,\n    },\n    provider: {\n        userRejectedRequest: 4001,\n        unauthorized: 4100,\n        unsupportedMethod: 4200,\n        disconnected: 4900,\n        chainDisconnected: 4901,\n    },\n};\nexports.errorValues = {\n    '-32700': {\n        standard: 'JSON RPC 2.0',\n        message: 'Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text.',\n    },\n    '-32600': {\n        standard: 'JSON RPC 2.0',\n        message: 'The JSON sent is not a valid Request object.',\n    },\n    '-32601': {\n        standard: 'JSON RPC 2.0',\n        message: 'The method does not exist / is not available.',\n    },\n    '-32602': {\n        standard: 'JSON RPC 2.0',\n        message: 'Invalid method parameter(s).',\n    },\n    '-32603': {\n        standard: 'JSON RPC 2.0',\n        message: 'Internal JSON-RPC error.',\n    },\n    '-32000': {\n        standard: 'EIP-1474',\n        message: 'Invalid input.',\n    },\n    '-32001': {\n        standard: 'EIP-1474',\n        message: 'Resource not found.',\n    },\n    '-32002': {\n        standard: 'EIP-1474',\n        message: 'Resource unavailable.',\n    },\n    '-32003': {\n        standard: 'EIP-1474',\n        message: 'Transaction rejected.',\n    },\n    '-32004': {\n        standard: 'EIP-1474',\n        message: 'Method not supported.',\n    },\n    '-32005': {\n        standard: 'EIP-1474',\n        message: 'Request limit exceeded.',\n    },\n    '4001': {\n        standard: 'EIP-1193',\n        message: 'User rejected the request.',\n    },\n    '4100': {\n        standard: 'EIP-1193',\n        message: 'The requested account and/or method has not been authorized by the user.',\n    },\n    '4200': {\n        standard: 'EIP-1193',\n        message: 'The requested method is not supported by this Ethereum provider.',\n    },\n    '4900': {\n        standard: 'EIP-1193',\n        message: 'The provider is disconnected from all chains.',\n    },\n    '4901': {\n        standard: 'EIP-1193',\n        message: 'The provider is disconnected from the specified chain.',\n    },\n};\n//# sourceMappingURL=data:application/json;base64,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//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-rpc-errors/dist/error-constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-rpc-errors/dist/errors.js":
/*!****************************************************!*\
  !*** ./node_modules/eth-rpc-errors/dist/errors.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ethErrors = void 0;\nconst classes_1 = __webpack_require__(/*! ./classes */ \"(ssr)/./node_modules/eth-rpc-errors/dist/classes.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/eth-rpc-errors/dist/utils.js\");\nconst error_constants_1 = __webpack_require__(/*! ./error-constants */ \"(ssr)/./node_modules/eth-rpc-errors/dist/error-constants.js\");\nexports.ethErrors = {\n    rpc: {\n        /**\n         * Get a JSON RPC 2.0 Parse (-32700) error.\n         */\n        parse: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.parse, arg),\n        /**\n         * Get a JSON RPC 2.0 Invalid Request (-32600) error.\n         */\n        invalidRequest: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.invalidRequest, arg),\n        /**\n         * Get a JSON RPC 2.0 Invalid Params (-32602) error.\n         */\n        invalidParams: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.invalidParams, arg),\n        /**\n         * Get a JSON RPC 2.0 Method Not Found (-32601) error.\n         */\n        methodNotFound: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.methodNotFound, arg),\n        /**\n         * Get a JSON RPC 2.0 Internal (-32603) error.\n         */\n        internal: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.internal, arg),\n        /**\n         * Get a JSON RPC 2.0 Server error.\n         * Permits integer error codes in the [ -32099 <= -32005 ] range.\n         * Codes -32000 through -32004 are reserved by EIP-1474.\n         */\n        server: (opts) => {\n            if (!opts || typeof opts !== 'object' || Array.isArray(opts)) {\n                throw new Error('Ethereum RPC Server errors must provide single object argument.');\n            }\n            const { code } = opts;\n            if (!Number.isInteger(code) || code > -32005 || code < -32099) {\n                throw new Error('\"code\" must be an integer such that: -32099 <= code <= -32005');\n            }\n            return getEthJsonRpcError(code, opts);\n        },\n        /**\n         * Get an Ethereum JSON RPC Invalid Input (-32000) error.\n         */\n        invalidInput: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.invalidInput, arg),\n        /**\n         * Get an Ethereum JSON RPC Resource Not Found (-32001) error.\n         */\n        resourceNotFound: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.resourceNotFound, arg),\n        /**\n         * Get an Ethereum JSON RPC Resource Unavailable (-32002) error.\n         */\n        resourceUnavailable: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.resourceUnavailable, arg),\n        /**\n         * Get an Ethereum JSON RPC Transaction Rejected (-32003) error.\n         */\n        transactionRejected: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.transactionRejected, arg),\n        /**\n         * Get an Ethereum JSON RPC Method Not Supported (-32004) error.\n         */\n        methodNotSupported: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.methodNotSupported, arg),\n        /**\n         * Get an Ethereum JSON RPC Limit Exceeded (-32005) error.\n         */\n        limitExceeded: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.limitExceeded, arg),\n    },\n    provider: {\n        /**\n         * Get an Ethereum Provider User Rejected Request (4001) error.\n         */\n        userRejectedRequest: (arg) => {\n            return getEthProviderError(error_constants_1.errorCodes.provider.userRejectedRequest, arg);\n        },\n        /**\n         * Get an Ethereum Provider Unauthorized (4100) error.\n         */\n        unauthorized: (arg) => {\n            return getEthProviderError(error_constants_1.errorCodes.provider.unauthorized, arg);\n        },\n        /**\n         * Get an Ethereum Provider Unsupported Method (4200) error.\n         */\n        unsupportedMethod: (arg) => {\n            return getEthProviderError(error_constants_1.errorCodes.provider.unsupportedMethod, arg);\n        },\n        /**\n         * Get an Ethereum Provider Not Connected (4900) error.\n         */\n        disconnected: (arg) => {\n            return getEthProviderError(error_constants_1.errorCodes.provider.disconnected, arg);\n        },\n        /**\n         * Get an Ethereum Provider Chain Not Connected (4901) error.\n         */\n        chainDisconnected: (arg) => {\n            return getEthProviderError(error_constants_1.errorCodes.provider.chainDisconnected, arg);\n        },\n        /**\n         * Get a custom Ethereum Provider error.\n         */\n        custom: (opts) => {\n            if (!opts || typeof opts !== 'object' || Array.isArray(opts)) {\n                throw new Error('Ethereum Provider custom errors must provide single object argument.');\n            }\n            const { code, message, data } = opts;\n            if (!message || typeof message !== 'string') {\n                throw new Error('\"message\" must be a nonempty string');\n            }\n            return new classes_1.EthereumProviderError(code, message, data);\n        },\n    },\n};\n// Internal\nfunction getEthJsonRpcError(code, arg) {\n    const [message, data] = parseOpts(arg);\n    return new classes_1.EthereumRpcError(code, message || utils_1.getMessageFromCode(code), data);\n}\nfunction getEthProviderError(code, arg) {\n    const [message, data] = parseOpts(arg);\n    return new classes_1.EthereumProviderError(code, message || utils_1.getMessageFromCode(code), data);\n}\nfunction parseOpts(arg) {\n    if (arg) {\n        if (typeof arg === 'string') {\n            return [arg];\n        }\n        else if (typeof arg === 'object' && !Array.isArray(arg)) {\n            const { message, data } = arg;\n            if (message && typeof message !== 'string') {\n                throw new Error('Must specify string message.');\n            }\n            return [message || undefined, data];\n        }\n    }\n    return [];\n}\n//# sourceMappingURL=data:application/json;base64,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# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-rpc-errors/dist/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-rpc-errors/dist/index.js":
/*!***************************************************!*\
  !*** ./node_modules/eth-rpc-errors/dist/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getMessageFromCode = exports.serializeError = exports.EthereumProviderError = exports.EthereumRpcError = exports.ethErrors = exports.errorCodes = void 0;\nconst classes_1 = __webpack_require__(/*! ./classes */ \"(ssr)/./node_modules/eth-rpc-errors/dist/classes.js\");\nObject.defineProperty(exports, \"EthereumRpcError\", ({ enumerable: true, get: function () { return classes_1.EthereumRpcError; } }));\nObject.defineProperty(exports, \"EthereumProviderError\", ({ enumerable: true, get: function () { return classes_1.EthereumProviderError; } }));\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/eth-rpc-errors/dist/utils.js\");\nObject.defineProperty(exports, \"serializeError\", ({ enumerable: true, get: function () { return utils_1.serializeError; } }));\nObject.defineProperty(exports, \"getMessageFromCode\", ({ enumerable: true, get: function () { return utils_1.getMessageFromCode; } }));\nconst errors_1 = __webpack_require__(/*! ./errors */ \"(ssr)/./node_modules/eth-rpc-errors/dist/errors.js\");\nObject.defineProperty(exports, \"ethErrors\", ({ enumerable: true, get: function () { return errors_1.ethErrors; } }));\nconst error_constants_1 = __webpack_require__(/*! ./error-constants */ \"(ssr)/./node_modules/eth-rpc-errors/dist/error-constants.js\");\nObject.defineProperty(exports, \"errorCodes\", ({ enumerable: true, get: function () { return error_constants_1.errorCodes; } }));\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvaW5kZXgudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEsdUNBQW9FO0FBVWxFLGlHQVZPLDBCQUFnQixPQVVQO0FBQ2hCLHNHQVh5QiwrQkFBcUIsT0FXekI7QUFWdkIsbUNBRWlCO0FBU2YsK0ZBVkEsc0JBQWMsT0FVQTtBQUNkLG1HQVhnQiwwQkFBa0IsT0FXaEI7QUFUcEIscUNBQXFDO0FBS25DLDBGQUxPLGtCQUFTLE9BS1A7QUFKWCx1REFBK0M7QUFHN0MsMkZBSE8sNEJBQVUsT0FHUCJ9//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-rpc-errors/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/eth-rpc-errors/dist/utils.js":
/*!***************************************************!*\
  !*** ./node_modules/eth-rpc-errors/dist/utils.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.serializeError = exports.isValidCode = exports.getMessageFromCode = exports.JSON_RPC_SERVER_ERROR_MESSAGE = void 0;\nconst error_constants_1 = __webpack_require__(/*! ./error-constants */ \"(ssr)/./node_modules/eth-rpc-errors/dist/error-constants.js\");\nconst classes_1 = __webpack_require__(/*! ./classes */ \"(ssr)/./node_modules/eth-rpc-errors/dist/classes.js\");\nconst FALLBACK_ERROR_CODE = error_constants_1.errorCodes.rpc.internal;\nconst FALLBACK_MESSAGE = 'Unspecified error message. This is a bug, please report it.';\nconst FALLBACK_ERROR = {\n    code: FALLBACK_ERROR_CODE,\n    message: getMessageFromCode(FALLBACK_ERROR_CODE),\n};\nexports.JSON_RPC_SERVER_ERROR_MESSAGE = 'Unspecified server error.';\n/**\n * Gets the message for a given code, or a fallback message if the code has\n * no corresponding message.\n */\nfunction getMessageFromCode(code, fallbackMessage = FALLBACK_MESSAGE) {\n    if (Number.isInteger(code)) {\n        const codeString = code.toString();\n        if (hasKey(error_constants_1.errorValues, codeString)) {\n            return error_constants_1.errorValues[codeString].message;\n        }\n        if (isJsonRpcServerError(code)) {\n            return exports.JSON_RPC_SERVER_ERROR_MESSAGE;\n        }\n    }\n    return fallbackMessage;\n}\nexports.getMessageFromCode = getMessageFromCode;\n/**\n * Returns whether the given code is valid.\n * A code is only valid if it has a message.\n */\nfunction isValidCode(code) {\n    if (!Number.isInteger(code)) {\n        return false;\n    }\n    const codeString = code.toString();\n    if (error_constants_1.errorValues[codeString]) {\n        return true;\n    }\n    if (isJsonRpcServerError(code)) {\n        return true;\n    }\n    return false;\n}\nexports.isValidCode = isValidCode;\n/**\n * Serializes the given error to an Ethereum JSON RPC-compatible error object.\n * Merely copies the given error's values if it is already compatible.\n * If the given error is not fully compatible, it will be preserved on the\n * returned object's data.originalError property.\n */\nfunction serializeError(error, { fallbackError = FALLBACK_ERROR, shouldIncludeStack = false, } = {}) {\n    var _a, _b;\n    if (!fallbackError ||\n        !Number.isInteger(fallbackError.code) ||\n        typeof fallbackError.message !== 'string') {\n        throw new Error('Must provide fallback error with integer number code and string message.');\n    }\n    if (error instanceof classes_1.EthereumRpcError) {\n        return error.serialize();\n    }\n    const serialized = {};\n    if (error &&\n        typeof error === 'object' &&\n        !Array.isArray(error) &&\n        hasKey(error, 'code') &&\n        isValidCode(error.code)) {\n        const _error = error;\n        serialized.code = _error.code;\n        if (_error.message && typeof _error.message === 'string') {\n            serialized.message = _error.message;\n            if (hasKey(_error, 'data')) {\n                serialized.data = _error.data;\n            }\n        }\n        else {\n            serialized.message = getMessageFromCode(serialized.code);\n            serialized.data = { originalError: assignOriginalError(error) };\n        }\n    }\n    else {\n        serialized.code = fallbackError.code;\n        const message = (_a = error) === null || _a === void 0 ? void 0 : _a.message;\n        serialized.message = (message && typeof message === 'string'\n            ? message\n            : fallbackError.message);\n        serialized.data = { originalError: assignOriginalError(error) };\n    }\n    const stack = (_b = error) === null || _b === void 0 ? void 0 : _b.stack;\n    if (shouldIncludeStack && error && stack && typeof stack === 'string') {\n        serialized.stack = stack;\n    }\n    return serialized;\n}\nexports.serializeError = serializeError;\n// Internal\nfunction isJsonRpcServerError(code) {\n    return code >= -32099 && code <= -32000;\n}\nfunction assignOriginalError(error) {\n    if (error && typeof error === 'object' && !Array.isArray(error)) {\n        return Object.assign({}, error);\n    }\n    return error;\n}\nfunction hasKey(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\n//# sourceMappingURL=data:application/json;base64,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# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eth-rpc-errors/dist/utils.js\n");

/***/ })

};
;