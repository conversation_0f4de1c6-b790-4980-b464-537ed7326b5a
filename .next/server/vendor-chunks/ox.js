"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ox";
exports.ids = ["vendor-chunks/ox"];
exports.modules = {

/***/ "(ssr)/./node_modules/ox/_esm/core/BlockOverrides.js":
/*!*****************************************************!*\
  !*** ./node_modules/ox/_esm/core/BlockOverrides.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromRpc: () => (/* binding */ fromRpc),\n/* harmony export */   toRpc: () => (/* binding */ toRpc)\n/* harmony export */ });\n/* harmony import */ var _Hex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_esm/core/Hex.js\");\n/* harmony import */ var _Withdrawal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Withdrawal.js */ \"(ssr)/./node_modules/ox/_esm/core/Withdrawal.js\");\n\n\n/**\n * Converts an {@link ox#BlockOverrides.Rpc} to an {@link ox#BlockOverrides.BlockOverrides}.\n *\n * @example\n * ```ts twoslash\n * import { BlockOverrides } from 'ox'\n *\n * const blockOverrides = BlockOverrides.fromRpc({\n *   baseFeePerGas: '0x1',\n *   blobBaseFee: '0x2',\n *   feeRecipient: '0x0000000000000000000000000000000000000000',\n *   gasLimit: '0x4',\n *   number: '0x5',\n *   prevRandao: '0x6',\n *   time: '0x1234567890',\n *   withdrawals: [\n *     {\n *       address: '0x0000000000000000000000000000000000000000',\n *       amount: '0x1',\n *       index: '0x0',\n *       validatorIndex: '0x1',\n *     },\n *   ],\n * })\n * ```\n *\n * @param rpcBlockOverrides - The RPC block overrides to convert.\n * @returns An instantiated {@link ox#BlockOverrides.BlockOverrides}.\n */\nfunction fromRpc(rpcBlockOverrides) {\n    return {\n        ...(rpcBlockOverrides.baseFeePerGas && {\n            baseFeePerGas: BigInt(rpcBlockOverrides.baseFeePerGas),\n        }),\n        ...(rpcBlockOverrides.blobBaseFee && {\n            blobBaseFee: BigInt(rpcBlockOverrides.blobBaseFee),\n        }),\n        ...(rpcBlockOverrides.feeRecipient && {\n            feeRecipient: rpcBlockOverrides.feeRecipient,\n        }),\n        ...(rpcBlockOverrides.gasLimit && {\n            gasLimit: BigInt(rpcBlockOverrides.gasLimit),\n        }),\n        ...(rpcBlockOverrides.number && {\n            number: BigInt(rpcBlockOverrides.number),\n        }),\n        ...(rpcBlockOverrides.prevRandao && {\n            prevRandao: BigInt(rpcBlockOverrides.prevRandao),\n        }),\n        ...(rpcBlockOverrides.time && {\n            time: BigInt(rpcBlockOverrides.time),\n        }),\n        ...(rpcBlockOverrides.withdrawals && {\n            withdrawals: rpcBlockOverrides.withdrawals.map(_Withdrawal_js__WEBPACK_IMPORTED_MODULE_0__.fromRpc),\n        }),\n    };\n}\n/**\n * Converts an {@link ox#BlockOverrides.BlockOverrides} to an {@link ox#BlockOverrides.Rpc}.\n *\n * @example\n * ```ts twoslash\n * import { BlockOverrides } from 'ox'\n *\n * const blockOverrides = BlockOverrides.toRpc({\n *   baseFeePerGas: 1n,\n *   blobBaseFee: 2n,\n *   feeRecipient: '0x0000000000000000000000000000000000000000',\n *   gasLimit: 4n,\n *   number: 5n,\n *   prevRandao: 6n,\n *   time: 78187493520n,\n *   withdrawals: [\n *     {\n *       address: '0x0000000000000000000000000000000000000000',\n *       amount: 1n,\n *       index: 0,\n *       validatorIndex: 1,\n *     },\n *   ],\n * })\n * ```\n *\n * @param blockOverrides - The block overrides to convert.\n * @returns An instantiated {@link ox#BlockOverrides.Rpc}.\n */\nfunction toRpc(blockOverrides) {\n    return {\n        ...(typeof blockOverrides.baseFeePerGas === 'bigint' && {\n            baseFeePerGas: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.baseFeePerGas),\n        }),\n        ...(typeof blockOverrides.blobBaseFee === 'bigint' && {\n            blobBaseFee: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.blobBaseFee),\n        }),\n        ...(typeof blockOverrides.feeRecipient === 'string' && {\n            feeRecipient: blockOverrides.feeRecipient,\n        }),\n        ...(typeof blockOverrides.gasLimit === 'bigint' && {\n            gasLimit: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.gasLimit),\n        }),\n        ...(typeof blockOverrides.number === 'bigint' && {\n            number: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.number),\n        }),\n        ...(typeof blockOverrides.prevRandao === 'bigint' && {\n            prevRandao: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.prevRandao),\n        }),\n        ...(typeof blockOverrides.time === 'bigint' && {\n            time: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.time),\n        }),\n        ...(blockOverrides.withdrawals && {\n            withdrawals: blockOverrides.withdrawals.map(_Withdrawal_js__WEBPACK_IMPORTED_MODULE_0__.toRpc),\n        }),\n    };\n}\n//# sourceMappingURL=BlockOverrides.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/BlockOverrides.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/Bytes.js":
/*!********************************************!*\
  !*** ./node_modules/ox/_esm/core/Bytes.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidBytesBooleanError: () => (/* binding */ InvalidBytesBooleanError),\n/* harmony export */   InvalidBytesTypeError: () => (/* binding */ InvalidBytesTypeError),\n/* harmony export */   SizeExceedsPaddingSizeError: () => (/* binding */ SizeExceedsPaddingSizeError),\n/* harmony export */   SizeOverflowError: () => (/* binding */ SizeOverflowError),\n/* harmony export */   SliceOffsetOutOfBoundsError: () => (/* binding */ SliceOffsetOutOfBoundsError),\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   concat: () => (/* binding */ concat),\n/* harmony export */   from: () => (/* binding */ from),\n/* harmony export */   fromArray: () => (/* binding */ fromArray),\n/* harmony export */   fromBoolean: () => (/* binding */ fromBoolean),\n/* harmony export */   fromHex: () => (/* binding */ fromHex),\n/* harmony export */   fromNumber: () => (/* binding */ fromNumber),\n/* harmony export */   fromString: () => (/* binding */ fromString),\n/* harmony export */   isEqual: () => (/* binding */ isEqual),\n/* harmony export */   padLeft: () => (/* binding */ padLeft),\n/* harmony export */   padRight: () => (/* binding */ padRight),\n/* harmony export */   random: () => (/* binding */ random),\n/* harmony export */   size: () => (/* binding */ size),\n/* harmony export */   slice: () => (/* binding */ slice),\n/* harmony export */   toBigInt: () => (/* binding */ toBigInt),\n/* harmony export */   toBoolean: () => (/* binding */ toBoolean),\n/* harmony export */   toHex: () => (/* binding */ toHex),\n/* harmony export */   toNumber: () => (/* binding */ toNumber),\n/* harmony export */   toString: () => (/* binding */ toString),\n/* harmony export */   trimLeft: () => (/* binding */ trimLeft),\n/* harmony export */   trimRight: () => (/* binding */ trimRight),\n/* harmony export */   validate: () => (/* binding */ validate)\n/* harmony export */ });\n/* harmony import */ var _noble_curves_abstract_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @noble/curves/abstract/utils */ \"(ssr)/./node_modules/ox/node_modules/@noble/curves/esm/utils.js\");\n/* harmony import */ var _Errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Errors.js */ \"(ssr)/./node_modules/ox/_esm/core/Errors.js\");\n/* harmony import */ var _Hex_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_esm/core/Hex.js\");\n/* harmony import */ var _Json_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Json.js */ \"(ssr)/./node_modules/ox/_esm/core/Json.js\");\n/* harmony import */ var _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./internal/bytes.js */ \"(ssr)/./node_modules/ox/_esm/core/internal/bytes.js\");\n/* harmony import */ var _internal_hex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./internal/hex.js */ \"(ssr)/./node_modules/ox/_esm/core/internal/hex.js\");\n\n\n\n\n\n\nconst decoder = /*#__PURE__*/ new TextDecoder();\nconst encoder = /*#__PURE__*/ new TextEncoder();\n/**\n * Asserts if the given value is {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.assert('abc')\n * // @error: Bytes.InvalidBytesTypeError:\n * // @error: Value `\"abc\"` of type `string` is an invalid Bytes value.\n * // @error: Bytes values must be of type `Uint8Array`.\n * ```\n *\n * @param value - Value to assert.\n */\nfunction assert(value) {\n    if (value instanceof Uint8Array)\n        return;\n    if (!value)\n        throw new InvalidBytesTypeError(value);\n    if (typeof value !== 'object')\n        throw new InvalidBytesTypeError(value);\n    if (!('BYTES_PER_ELEMENT' in value))\n        throw new InvalidBytesTypeError(value);\n    if (value.BYTES_PER_ELEMENT !== 1 || value.constructor.name !== 'Uint8Array')\n        throw new InvalidBytesTypeError(value);\n}\n/**\n * Concatenates two or more {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const bytes = Bytes.concat(\n *   Bytes.from([1]),\n *   Bytes.from([69]),\n *   Bytes.from([420, 69]),\n * )\n * // @log: Uint8Array [ 1, 69, 420, 69 ]\n * ```\n *\n * @param values - Values to concatenate.\n * @returns Concatenated {@link ox#Bytes.Bytes}.\n */\nfunction concat(...values) {\n    let length = 0;\n    for (const arr of values) {\n        length += arr.length;\n    }\n    const result = new Uint8Array(length);\n    for (let i = 0, index = 0; i < values.length; i++) {\n        const arr = values[i];\n        result.set(arr, index);\n        index += arr.length;\n    }\n    return result;\n}\n/**\n * Instantiates a {@link ox#Bytes.Bytes} value from a `Uint8Array`, a hex string, or an array of unsigned 8-bit integers.\n *\n * :::tip\n *\n * To instantiate from a **Boolean**, **String**, or **Number**, use one of the following:\n *\n * - `Bytes.fromBoolean`\n *\n * - `Bytes.fromString`\n *\n * - `Bytes.fromNumber`\n *\n * :::\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.from([255, 124, 5, 4])\n * // @log: Uint8Array([255, 124, 5, 4])\n *\n * const data = Bytes.from('0xdeadbeef')\n * // @log: Uint8Array([222, 173, 190, 239])\n * ```\n *\n * @param value - Value to convert.\n * @returns A {@link ox#Bytes.Bytes} instance.\n */\nfunction from(value) {\n    if (value instanceof Uint8Array)\n        return value;\n    if (typeof value === 'string')\n        return fromHex(value);\n    return fromArray(value);\n}\n/**\n * Converts an array of unsigned 8-bit integers into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromArray([255, 124, 5, 4])\n * // @log: Uint8Array([255, 124, 5, 4])\n * ```\n *\n * @param value - Value to convert.\n * @returns A {@link ox#Bytes.Bytes} instance.\n */\nfunction fromArray(value) {\n    return value instanceof Uint8Array ? value : new Uint8Array(value);\n}\n/**\n * Encodes a boolean value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromBoolean(true)\n * // @log: Uint8Array([1])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromBoolean(true, { size: 32 })\n * // @log: Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1])\n * ```\n *\n * @param value - Boolean value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nfunction fromBoolean(value, options = {}) {\n    const { size } = options;\n    const bytes = new Uint8Array(1);\n    bytes[0] = Number(value);\n    if (typeof size === 'number') {\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes, size);\n        return padLeft(bytes, size);\n    }\n    return bytes;\n}\n/**\n * Encodes a {@link ox#Hex.Hex} value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromHex('0x48656c6c6f20776f726c6421')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromHex('0x48656c6c6f20776f726c6421', { size: 32 })\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n * ```\n *\n * @param value - {@link ox#Hex.Hex} value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nfunction fromHex(value, options = {}) {\n    const { size } = options;\n    let hex = value;\n    if (size) {\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_1__.assertSize(value, size);\n        hex = _Hex_js__WEBPACK_IMPORTED_MODULE_2__.padRight(value, size);\n    }\n    let hexString = hex.slice(2);\n    if (hexString.length % 2)\n        hexString = `0${hexString}`;\n    const length = hexString.length / 2;\n    const bytes = new Uint8Array(length);\n    for (let index = 0, j = 0; index < length; index++) {\n        const nibbleLeft = _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.charCodeToBase16(hexString.charCodeAt(j++));\n        const nibbleRight = _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.charCodeToBase16(hexString.charCodeAt(j++));\n        if (nibbleLeft === undefined || nibbleRight === undefined) {\n            throw new _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError(`Invalid byte sequence (\"${hexString[j - 2]}${hexString[j - 1]}\" in \"${hexString}\").`);\n        }\n        bytes[index] = nibbleLeft * 16 + nibbleRight;\n    }\n    return bytes;\n}\n/**\n * Encodes a number value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromNumber(420)\n * // @log: Uint8Array([1, 164])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromNumber(420, { size: 4 })\n * // @log: Uint8Array([0, 0, 1, 164])\n * ```\n *\n * @param value - Number value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nfunction fromNumber(value, options) {\n    const hex = _Hex_js__WEBPACK_IMPORTED_MODULE_2__.fromNumber(value, options);\n    return fromHex(hex);\n}\n/**\n * Encodes a string into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromString('Hello world!')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 119, 111, 114, 108, 100, 33])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromString('Hello world!', { size: 32 })\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n * ```\n *\n * @param value - String to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nfunction fromString(value, options = {}) {\n    const { size } = options;\n    const bytes = encoder.encode(value);\n    if (typeof size === 'number') {\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes, size);\n        return padRight(bytes, size);\n    }\n    return bytes;\n}\n/**\n * Checks if two {@link ox#Bytes.Bytes} values are equal.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.isEqual(Bytes.from([1]), Bytes.from([1]))\n * // @log: true\n *\n * Bytes.isEqual(Bytes.from([1]), Bytes.from([2]))\n * // @log: false\n * ```\n *\n * @param bytesA - First {@link ox#Bytes.Bytes} value.\n * @param bytesB - Second {@link ox#Bytes.Bytes} value.\n * @returns `true` if the two values are equal, otherwise `false`.\n */\nfunction isEqual(bytesA, bytesB) {\n    return (0,_noble_curves_abstract_utils__WEBPACK_IMPORTED_MODULE_4__.equalBytes)(bytesA, bytesB);\n}\n/**\n * Pads a {@link ox#Bytes.Bytes} value to the left with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padLeft(Bytes.from([1]), 4)\n * // @log: Uint8Array([0, 0, 0, 1])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value to pad.\n * @param size - Size to pad the {@link ox#Bytes.Bytes} value to.\n * @returns Padded {@link ox#Bytes.Bytes} value.\n */\nfunction padLeft(value, size) {\n    return _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.pad(value, { dir: 'left', size });\n}\n/**\n * Pads a {@link ox#Bytes.Bytes} value to the right with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padRight(Bytes.from([1]), 4)\n * // @log: Uint8Array([1, 0, 0, 0])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value to pad.\n * @param size - Size to pad the {@link ox#Bytes.Bytes} value to.\n * @returns Padded {@link ox#Bytes.Bytes} value.\n */\nfunction padRight(value, size) {\n    return _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.pad(value, { dir: 'right', size });\n}\n/**\n * Generates random {@link ox#Bytes.Bytes} of the specified length.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const bytes = Bytes.random(32)\n * // @log: Uint8Array([... x32])\n * ```\n *\n * @param length - Length of the random {@link ox#Bytes.Bytes} to generate.\n * @returns Random {@link ox#Bytes.Bytes} of the specified length.\n */\nfunction random(length) {\n    return crypto.getRandomValues(new Uint8Array(length));\n}\n/**\n * Retrieves the size of a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.size(Bytes.from([1, 2, 3, 4]))\n * // @log: 4\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Size of the {@link ox#Bytes.Bytes} value.\n */\nfunction size(value) {\n    return value.length;\n}\n/**\n * Returns a section of a {@link ox#Bytes.Bytes} value given a start/end bytes offset.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.slice(\n *   Bytes.from([1, 2, 3, 4, 5, 6, 7, 8, 9]),\n *   1,\n *   4,\n * )\n * // @log: Uint8Array([2, 3, 4])\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value.\n * @param start - Start offset.\n * @param end - End offset.\n * @param options - Slice options.\n * @returns Sliced {@link ox#Bytes.Bytes} value.\n */\nfunction slice(value, start, end, options = {}) {\n    const { strict } = options;\n    _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertStartOffset(value, start);\n    const value_ = value.slice(start, end);\n    if (strict)\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertEndOffset(value_, start, end);\n    return value_;\n}\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a bigint.\n *\n * @example\n * ```ts\n * import { Bytes } from 'ox'\n *\n * Bytes.toBigInt(Bytes.from([1, 164]))\n * // @log: 420n\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Decoding options.\n * @returns Decoded bigint.\n */\nfunction toBigInt(bytes, options = {}) {\n    const { size } = options;\n    if (typeof size !== 'undefined')\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes, size);\n    const hex = _Hex_js__WEBPACK_IMPORTED_MODULE_2__.fromBytes(bytes, options);\n    return _Hex_js__WEBPACK_IMPORTED_MODULE_2__.toBigInt(hex, options);\n}\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a boolean.\n *\n * @example\n * ```ts\n * import { Bytes } from 'ox'\n *\n * Bytes.toBoolean(Bytes.from([1]))\n * // @log: true\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Decoding options.\n * @returns Decoded boolean.\n */\nfunction toBoolean(bytes, options = {}) {\n    const { size } = options;\n    let bytes_ = bytes;\n    if (typeof size !== 'undefined') {\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes_, size);\n        bytes_ = trimLeft(bytes_);\n    }\n    if (bytes_.length > 1 || bytes_[0] > 1)\n        throw new InvalidBytesBooleanError(bytes_);\n    return Boolean(bytes_[0]);\n}\n/**\n * Encodes a {@link ox#Bytes.Bytes} value into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toHex(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Options.\n * @returns Decoded {@link ox#Hex.Hex} value.\n */\nfunction toHex(value, options = {}) {\n    return _Hex_js__WEBPACK_IMPORTED_MODULE_2__.fromBytes(value, options);\n}\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a number.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toNumber(Bytes.from([1, 164]))\n * // @log: 420\n * ```\n */\nfunction toNumber(bytes, options = {}) {\n    const { size } = options;\n    if (typeof size !== 'undefined')\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes, size);\n    const hex = _Hex_js__WEBPACK_IMPORTED_MODULE_2__.fromBytes(bytes, options);\n    return _Hex_js__WEBPACK_IMPORTED_MODULE_2__.toNumber(hex, options);\n}\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a string.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.toString(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: 'Hello world'\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Options.\n * @returns Decoded string.\n */\nfunction toString(bytes, options = {}) {\n    const { size } = options;\n    let bytes_ = bytes;\n    if (typeof size !== 'undefined') {\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes_, size);\n        bytes_ = trimRight(bytes_);\n    }\n    return decoder.decode(bytes_);\n}\n/**\n * Trims leading zeros from a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.trimLeft(Bytes.from([0, 0, 0, 0, 1, 2, 3]))\n * // @log: Uint8Array([1, 2, 3])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Trimmed {@link ox#Bytes.Bytes} value.\n */\nfunction trimLeft(value) {\n    return _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.trim(value, { dir: 'left' });\n}\n/**\n * Trims trailing zeros from a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.trimRight(Bytes.from([1, 2, 3, 0, 0, 0, 0]))\n * // @log: Uint8Array([1, 2, 3])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Trimmed {@link ox#Bytes.Bytes} value.\n */\nfunction trimRight(value) {\n    return _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.trim(value, { dir: 'right' });\n}\n/**\n * Checks if the given value is {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.validate('0x')\n * // @log: false\n *\n * Bytes.validate(Bytes.from([1, 2, 3]))\n * // @log: true\n * ```\n *\n * @param value - Value to check.\n * @returns `true` if the value is {@link ox#Bytes.Bytes}, otherwise `false`.\n */\nfunction validate(value) {\n    try {\n        assert(value);\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\n/**\n * Thrown when the bytes value cannot be represented as a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toBoolean(Bytes.from([5]))\n * // @error: Bytes.InvalidBytesBooleanError: Bytes value `[5]` is not a valid boolean.\n * // @error: The bytes array must contain a single byte of either a `0` or `1` value.\n * ```\n */\nclass InvalidBytesBooleanError extends _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError {\n    constructor(bytes) {\n        super(`Bytes value \\`${bytes}\\` is not a valid boolean.`, {\n            metaMessages: [\n                'The bytes array must contain a single byte of either a `0` or `1` value.',\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.InvalidBytesBooleanError'\n        });\n    }\n}\n/**\n * Thrown when a value cannot be converted to bytes.\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { Bytes } from 'ox'\n *\n * Bytes.from('foo')\n * // @error: Bytes.InvalidBytesTypeError: Value `foo` of type `string` is an invalid Bytes value.\n * ```\n */\nclass InvalidBytesTypeError extends _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError {\n    constructor(value) {\n        super(`Value \\`${typeof value === 'object' ? _Json_js__WEBPACK_IMPORTED_MODULE_5__.stringify(value) : value}\\` of type \\`${typeof value}\\` is an invalid Bytes value.`, {\n            metaMessages: ['Bytes values must be of type `Bytes`.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.InvalidBytesTypeError'\n        });\n    }\n}\n/**\n * Thrown when a size exceeds the maximum allowed size.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.fromString('Hello World!', { size: 8 })\n * // @error: Bytes.SizeOverflowError: Size cannot exceed `8` bytes. Given size: `12` bytes.\n * ```\n */\nclass SizeOverflowError extends _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError {\n    constructor({ givenSize, maxSize }) {\n        super(`Size cannot exceed \\`${maxSize}\\` bytes. Given size: \\`${givenSize}\\` bytes.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.SizeOverflowError'\n        });\n    }\n}\n/**\n * Thrown when a slice offset is out-of-bounds.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.slice(Bytes.from([1, 2, 3]), 4)\n * // @error: Bytes.SliceOffsetOutOfBoundsError: Slice starting at offset `4` is out-of-bounds (size: `3`).\n * ```\n */\nclass SliceOffsetOutOfBoundsError extends _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError {\n    constructor({ offset, position, size, }) {\n        super(`Slice ${position === 'start' ? 'starting' : 'ending'} at offset \\`${offset}\\` is out-of-bounds (size: \\`${size}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.SliceOffsetOutOfBoundsError'\n        });\n    }\n}\n/**\n * Thrown when a the padding size exceeds the maximum allowed size.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padLeft(Bytes.fromString('Hello World!'), 8)\n * // @error: [Bytes.SizeExceedsPaddingSizeError: Bytes size (`12`) exceeds padding size (`8`).\n * ```\n */\nclass SizeExceedsPaddingSizeError extends _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError {\n    constructor({ size, targetSize, type, }) {\n        super(`${type.charAt(0).toUpperCase()}${type\n            .slice(1)\n            .toLowerCase()} size (\\`${size}\\`) exceeds padding size (\\`${targetSize}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.SizeExceedsPaddingSizeError'\n        });\n    }\n}\n//# sourceMappingURL=Bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/Bytes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/Errors.js":
/*!*********************************************!*\
  !*** ./node_modules/ox/_esm/core/Errors.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseError: () => (/* binding */ BaseError)\n/* harmony export */ });\n/* harmony import */ var _internal_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./internal/errors.js */ \"(ssr)/./node_modules/ox/_esm/core/internal/errors.js\");\n\n/**\n * Base error class inherited by all errors thrown by ox.\n *\n * @example\n * ```ts\n * import { Errors } from 'ox'\n * throw new Errors.BaseError('An error occurred')\n * ```\n */\nclass BaseError extends Error {\n    constructor(shortMessage, options = {}) {\n        const details = (() => {\n            if (options.cause instanceof BaseError) {\n                if (options.cause.details)\n                    return options.cause.details;\n                if (options.cause.shortMessage)\n                    return options.cause.shortMessage;\n            }\n            if (options.cause &&\n                'details' in options.cause &&\n                typeof options.cause.details === 'string')\n                return options.cause.details;\n            if (options.cause?.message)\n                return options.cause.message;\n            return options.details;\n        })();\n        const docsPath = (() => {\n            if (options.cause instanceof BaseError)\n                return options.cause.docsPath || options.docsPath;\n            return options.docsPath;\n        })();\n        const docsBaseUrl = 'https://oxlib.sh';\n        const docs = `${docsBaseUrl}${docsPath ?? ''}`;\n        const message = [\n            shortMessage || 'An error occurred.',\n            ...(options.metaMessages ? ['', ...options.metaMessages] : []),\n            ...(details || docsPath\n                ? [\n                    '',\n                    details ? `Details: ${details}` : undefined,\n                    docsPath ? `See: ${docs}` : undefined,\n                ]\n                : []),\n        ]\n            .filter((x) => typeof x === 'string')\n            .join('\\n');\n        super(message, options.cause ? { cause: options.cause } : undefined);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"cause\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'BaseError'\n        });\n        Object.defineProperty(this, \"version\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: `ox@${(0,_internal_errors_js__WEBPACK_IMPORTED_MODULE_0__.getVersion)()}`\n        });\n        this.cause = options.cause;\n        this.details = details;\n        this.docs = docs;\n        this.docsPath = docsPath;\n        this.shortMessage = shortMessage;\n    }\n    walk(fn) {\n        return walk(this, fn);\n    }\n}\n/** @internal */\nfunction walk(err, fn) {\n    if (fn?.(err))\n        return err;\n    if (err && typeof err === 'object' && 'cause' in err && err.cause)\n        return walk(err.cause, fn);\n    return fn ? null : err;\n}\n//# sourceMappingURL=Errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/Errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/Hex.js":
/*!******************************************!*\
  !*** ./node_modules/ox/_esm/core/Hex.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntegerOutOfRangeError: () => (/* binding */ IntegerOutOfRangeError),\n/* harmony export */   InvalidHexBooleanError: () => (/* binding */ InvalidHexBooleanError),\n/* harmony export */   InvalidHexTypeError: () => (/* binding */ InvalidHexTypeError),\n/* harmony export */   InvalidHexValueError: () => (/* binding */ InvalidHexValueError),\n/* harmony export */   InvalidLengthError: () => (/* binding */ InvalidLengthError),\n/* harmony export */   SizeExceedsPaddingSizeError: () => (/* binding */ SizeExceedsPaddingSizeError),\n/* harmony export */   SizeOverflowError: () => (/* binding */ SizeOverflowError),\n/* harmony export */   SliceOffsetOutOfBoundsError: () => (/* binding */ SliceOffsetOutOfBoundsError),\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   concat: () => (/* binding */ concat),\n/* harmony export */   from: () => (/* binding */ from),\n/* harmony export */   fromBoolean: () => (/* binding */ fromBoolean),\n/* harmony export */   fromBytes: () => (/* binding */ fromBytes),\n/* harmony export */   fromNumber: () => (/* binding */ fromNumber),\n/* harmony export */   fromString: () => (/* binding */ fromString),\n/* harmony export */   isEqual: () => (/* binding */ isEqual),\n/* harmony export */   padLeft: () => (/* binding */ padLeft),\n/* harmony export */   padRight: () => (/* binding */ padRight),\n/* harmony export */   random: () => (/* binding */ random),\n/* harmony export */   size: () => (/* binding */ size),\n/* harmony export */   slice: () => (/* binding */ slice),\n/* harmony export */   toBigInt: () => (/* binding */ toBigInt),\n/* harmony export */   toBoolean: () => (/* binding */ toBoolean),\n/* harmony export */   toBytes: () => (/* binding */ toBytes),\n/* harmony export */   toNumber: () => (/* binding */ toNumber),\n/* harmony export */   toString: () => (/* binding */ toString),\n/* harmony export */   trimLeft: () => (/* binding */ trimLeft),\n/* harmony export */   trimRight: () => (/* binding */ trimRight),\n/* harmony export */   validate: () => (/* binding */ validate)\n/* harmony export */ });\n/* harmony import */ var _noble_curves_abstract_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/curves/abstract/utils */ \"(ssr)/./node_modules/ox/node_modules/@noble/curves/esm/utils.js\");\n/* harmony import */ var _Bytes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Bytes.js */ \"(ssr)/./node_modules/ox/_esm/core/Bytes.js\");\n/* harmony import */ var _Errors_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Errors.js */ \"(ssr)/./node_modules/ox/_esm/core/Errors.js\");\n/* harmony import */ var _Json_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Json.js */ \"(ssr)/./node_modules/ox/_esm/core/Json.js\");\n/* harmony import */ var _internal_bytes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./internal/bytes.js */ \"(ssr)/./node_modules/ox/_esm/core/internal/bytes.js\");\n/* harmony import */ var _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./internal/hex.js */ \"(ssr)/./node_modules/ox/_esm/core/internal/hex.js\");\n\n\n\n\n\n\nconst encoder = /*#__PURE__*/ new TextEncoder();\nconst hexes = /*#__PURE__*/ Array.from({ length: 256 }, (_v, i) => i.toString(16).padStart(2, '0'));\n/**\n * Asserts if the given value is {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert('abc')\n * // @error: InvalidHexValueTypeError:\n * // @error: Value `\"abc\"` of type `string` is an invalid hex type.\n * // @error: Hex types must be represented as `\"0x\\${string}\"`.\n * ```\n *\n * @param value - The value to assert.\n * @param options - Options.\n */\nfunction assert(value, options = {}) {\n    const { strict = false } = options;\n    if (!value)\n        throw new InvalidHexTypeError(value);\n    if (typeof value !== 'string')\n        throw new InvalidHexTypeError(value);\n    if (strict) {\n        if (!/^0x[0-9a-fA-F]*$/.test(value))\n            throw new InvalidHexValueError(value);\n    }\n    if (!value.startsWith('0x'))\n        throw new InvalidHexValueError(value);\n}\n/**\n * Concatenates two or more {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.concat('0x123', '0x456')\n * // @log: '0x123456'\n * ```\n *\n * @param values - The {@link ox#Hex.Hex} values to concatenate.\n * @returns The concatenated {@link ox#Hex.Hex} value.\n */\nfunction concat(...values) {\n    return `0x${values.reduce((acc, x) => acc + x.replace('0x', ''), '')}`;\n}\n/**\n * Instantiates a {@link ox#Hex.Hex} value from a hex string or {@link ox#Bytes.Bytes} value.\n *\n * :::tip\n *\n * To instantiate from a **Boolean**, **String**, or **Number**, use one of the following:\n *\n * - `Hex.fromBoolean`\n *\n * - `Hex.fromString`\n *\n * - `Hex.fromNumber`\n *\n * :::\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.from('0x48656c6c6f20576f726c6421')\n * // @log: '0x48656c6c6f20576f726c6421'\n *\n * Hex.from(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value to encode.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nfunction from(value) {\n    if (value instanceof Uint8Array)\n        return fromBytes(value);\n    if (Array.isArray(value))\n        return fromBytes(new Uint8Array(value));\n    return value;\n}\n/**\n * Encodes a boolean into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromBoolean(true)\n * // @log: '0x1'\n *\n * Hex.fromBoolean(false)\n * // @log: '0x0'\n *\n * Hex.fromBoolean(true, { size: 32 })\n * // @log: '0x0000000000000000000000000000000000000000000000000000000000000001'\n * ```\n *\n * @param value - The boolean value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nfunction fromBoolean(value, options = {}) {\n    const hex = `0x${Number(value)}`;\n    if (typeof options.size === 'number') {\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(hex, options.size);\n        return padLeft(hex, options.size);\n    }\n    return hex;\n}\n/**\n * Encodes a {@link ox#Bytes.Bytes} value into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.fromBytes(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nfunction fromBytes(value, options = {}) {\n    let string = '';\n    for (let i = 0; i < value.length; i++)\n        string += hexes[value[i]];\n    const hex = `0x${string}`;\n    if (typeof options.size === 'number') {\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(hex, options.size);\n        return padRight(hex, options.size);\n    }\n    return hex;\n}\n/**\n * Encodes a number or bigint into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromNumber(420)\n * // @log: '0x1a4'\n *\n * Hex.fromNumber(420, { size: 32 })\n * // @log: '0x00000000000000000000000000000000000000000000000000000000000001a4'\n * ```\n *\n * @param value - The number or bigint value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nfunction fromNumber(value, options = {}) {\n    const { signed, size } = options;\n    const value_ = BigInt(value);\n    let maxValue;\n    if (size) {\n        if (signed)\n            maxValue = (1n << (BigInt(size) * 8n - 1n)) - 1n;\n        else\n            maxValue = 2n ** (BigInt(size) * 8n) - 1n;\n    }\n    else if (typeof value === 'number') {\n        maxValue = BigInt(Number.MAX_SAFE_INTEGER);\n    }\n    const minValue = typeof maxValue === 'bigint' && signed ? -maxValue - 1n : 0;\n    if ((maxValue && value_ > maxValue) || value_ < minValue) {\n        const suffix = typeof value === 'bigint' ? 'n' : '';\n        throw new IntegerOutOfRangeError({\n            max: maxValue ? `${maxValue}${suffix}` : undefined,\n            min: `${minValue}${suffix}`,\n            signed,\n            size,\n            value: `${value}${suffix}`,\n        });\n    }\n    const stringValue = (signed && value_ < 0 ? (1n << BigInt(size * 8)) + BigInt(value_) : value_).toString(16);\n    const hex = `0x${stringValue}`;\n    if (size)\n        return padLeft(hex, size);\n    return hex;\n}\n/**\n * Encodes a string into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n * Hex.fromString('Hello World!')\n * // '0x48656c6c6f20576f726c6421'\n *\n * Hex.fromString('Hello World!', { size: 32 })\n * // '0x48656c6c6f20576f726c64210000000000000000000000000000000000000000'\n * ```\n *\n * @param value - The string value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nfunction fromString(value, options = {}) {\n    return fromBytes(encoder.encode(value), options);\n}\n/**\n * Checks if two {@link ox#Hex.Hex} values are equal.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.isEqual('0xdeadbeef', '0xdeadbeef')\n * // @log: true\n *\n * Hex.isEqual('0xda', '0xba')\n * // @log: false\n * ```\n *\n * @param hexA - The first {@link ox#Hex.Hex} value.\n * @param hexB - The second {@link ox#Hex.Hex} value.\n * @returns `true` if the two {@link ox#Hex.Hex} values are equal, `false` otherwise.\n */\nfunction isEqual(hexA, hexB) {\n    return (0,_noble_curves_abstract_utils__WEBPACK_IMPORTED_MODULE_1__.equalBytes)(_Bytes_js__WEBPACK_IMPORTED_MODULE_2__.fromHex(hexA), _Bytes_js__WEBPACK_IMPORTED_MODULE_2__.fromHex(hexB));\n}\n/**\n * Pads a {@link ox#Hex.Hex} value to the left with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.padLeft('0x1234', 4)\n * // @log: '0x00001234'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to pad.\n * @param size - The size (in bytes) of the output hex value.\n * @returns The padded {@link ox#Hex.Hex} value.\n */\nfunction padLeft(value, size) {\n    return _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.pad(value, { dir: 'left', size });\n}\n/**\n * Pads a {@link ox#Hex.Hex} value to the right with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts\n * import { Hex } from 'ox'\n *\n * Hex.padRight('0x1234', 4)\n * // @log: '0x12340000'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to pad.\n * @param size - The size (in bytes) of the output hex value.\n * @returns The padded {@link ox#Hex.Hex} value.\n */\nfunction padRight(value, size) {\n    return _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.pad(value, { dir: 'right', size });\n}\n/**\n * Generates a random {@link ox#Hex.Hex} value of the specified length.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * const hex = Hex.random(32)\n * // @log: '0x...'\n * ```\n *\n * @returns Random {@link ox#Hex.Hex} value.\n */\nfunction random(length) {\n    return fromBytes(_Bytes_js__WEBPACK_IMPORTED_MODULE_2__.random(length));\n}\n/**\n * Returns a section of a {@link ox#Bytes.Bytes} value given a start/end bytes offset.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.slice('0x0123456789', 1, 4)\n * // @log: '0x234567'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to slice.\n * @param start - The start offset (in bytes).\n * @param end - The end offset (in bytes).\n * @param options - Options.\n * @returns The sliced {@link ox#Hex.Hex} value.\n */\nfunction slice(value, start, end, options = {}) {\n    const { strict } = options;\n    _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertStartOffset(value, start);\n    const value_ = `0x${value\n        .replace('0x', '')\n        .slice((start ?? 0) * 2, (end ?? value.length) * 2)}`;\n    if (strict)\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertEndOffset(value_, start, end);\n    return value_;\n}\n/**\n * Retrieves the size of a {@link ox#Hex.Hex} value (in bytes).\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.size('0xdeadbeef')\n * // @log: 4\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to get the size of.\n * @returns The size of the {@link ox#Hex.Hex} value (in bytes).\n */\nfunction size(value) {\n    return Math.ceil((value.length - 2) / 2);\n}\n/**\n * Trims leading zeros from a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.trimLeft('0x00000000deadbeef')\n * // @log: '0xdeadbeef'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to trim.\n * @returns The trimmed {@link ox#Hex.Hex} value.\n */\nfunction trimLeft(value) {\n    return _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.trim(value, { dir: 'left' });\n}\n/**\n * Trims trailing zeros from a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.trimRight('0xdeadbeef00000000')\n * // @log: '0xdeadbeef'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to trim.\n * @returns The trimmed {@link ox#Hex.Hex} value.\n */\nfunction trimRight(value) {\n    return _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.trim(value, { dir: 'right' });\n}\n/**\n * Decodes a {@link ox#Hex.Hex} value into a BigInt.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBigInt('0x1a4')\n * // @log: 420n\n *\n * Hex.toBigInt('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // @log: 420n\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded BigInt.\n */\nfunction toBigInt(hex, options = {}) {\n    const { signed } = options;\n    if (options.size)\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(hex, options.size);\n    const value = BigInt(hex);\n    if (!signed)\n        return value;\n    const size = (hex.length - 2) / 2;\n    const max_unsigned = (1n << (BigInt(size) * 8n)) - 1n;\n    const max_signed = max_unsigned >> 1n;\n    if (value <= max_signed)\n        return value;\n    return value - max_unsigned - 1n;\n}\n/**\n * Decodes a {@link ox#Hex.Hex} value into a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBoolean('0x01')\n * // @log: true\n *\n * Hex.toBoolean('0x0000000000000000000000000000000000000000000000000000000000000001', { size: 32 })\n * // @log: true\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded boolean.\n */\nfunction toBoolean(hex, options = {}) {\n    if (options.size)\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(hex, options.size);\n    const hex_ = trimLeft(hex);\n    if (hex_ === '0x')\n        return false;\n    if (hex_ === '0x1')\n        return true;\n    throw new InvalidHexBooleanError(hex);\n}\n/**\n * Decodes a {@link ox#Hex.Hex} value into a {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * const data = Hex.toBytes('0x48656c6c6f20776f726c6421')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded {@link ox#Bytes.Bytes}.\n */\nfunction toBytes(hex, options = {}) {\n    return _Bytes_js__WEBPACK_IMPORTED_MODULE_2__.fromHex(hex, options);\n}\n/**\n * Decodes a {@link ox#Hex.Hex} value into a number.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toNumber('0x1a4')\n * // @log: 420\n *\n * Hex.toNumber('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // @log: 420\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded number.\n */\nfunction toNumber(hex, options = {}) {\n    const { signed, size } = options;\n    if (!signed && !size)\n        return Number(hex);\n    return Number(toBigInt(hex, options));\n}\n/**\n * Decodes a {@link ox#Hex.Hex} value into a string.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toString('0x48656c6c6f20576f726c6421')\n * // @log: 'Hello world!'\n *\n * Hex.toString('0x48656c6c6f20576f726c64210000000000000000000000000000000000000000', {\n *  size: 32,\n * })\n * // @log: 'Hello world'\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded string.\n */\nfunction toString(hex, options = {}) {\n    const { size } = options;\n    let bytes = _Bytes_js__WEBPACK_IMPORTED_MODULE_2__.fromHex(hex);\n    if (size) {\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_3__.assertSize(bytes, size);\n        bytes = _Bytes_js__WEBPACK_IMPORTED_MODULE_2__.trimRight(bytes);\n    }\n    return new TextDecoder().decode(bytes);\n}\n/**\n * Checks if the given value is {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.validate('0xdeadbeef')\n * // @log: true\n *\n * Hex.validate(Bytes.from([1, 2, 3]))\n * // @log: false\n * ```\n *\n * @param value - The value to check.\n * @param options - Options.\n * @returns `true` if the value is a {@link ox#Hex.Hex}, `false` otherwise.\n */\nfunction validate(value, options = {}) {\n    const { strict = false } = options;\n    try {\n        assert(value, { strict });\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\n/**\n * Thrown when the provided integer is out of range, and cannot be represented as a hex value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromNumber(420182738912731283712937129)\n * // @error: Hex.IntegerOutOfRangeError: Number \\`4.2018273891273126e+26\\` is not in safe unsigned integer range (`0` to `9007199254740991`)\n * ```\n */\nclass IntegerOutOfRangeError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor({ max, min, signed, size, value, }) {\n        super(`Number \\`${value}\\` is not in safe${size ? ` ${size * 8}-bit` : ''}${signed ? ' signed' : ' unsigned'} integer range ${max ? `(\\`${min}\\` to \\`${max}\\`)` : `(above \\`${min}\\`)`}`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.IntegerOutOfRangeError'\n        });\n    }\n}\n/**\n * Thrown when the provided hex value cannot be represented as a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBoolean('0xa')\n * // @error: Hex.InvalidHexBooleanError: Hex value `\"0xa\"` is not a valid boolean.\n * // @error: The hex value must be `\"0x0\"` (false) or `\"0x1\"` (true).\n * ```\n */\nclass InvalidHexBooleanError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor(hex) {\n        super(`Hex value \\`\"${hex}\"\\` is not a valid boolean.`, {\n            metaMessages: [\n                'The hex value must be `\"0x0\"` (false) or `\"0x1\"` (true).',\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.InvalidHexBooleanError'\n        });\n    }\n}\n/**\n * Thrown when the provided value is not a valid hex type.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert(1)\n * // @error: Hex.InvalidHexTypeError: Value `1` of type `number` is an invalid hex type.\n * ```\n */\nclass InvalidHexTypeError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor(value) {\n        super(`Value \\`${typeof value === 'object' ? _Json_js__WEBPACK_IMPORTED_MODULE_5__.stringify(value) : value}\\` of type \\`${typeof value}\\` is an invalid hex type.`, {\n            metaMessages: ['Hex types must be represented as `\"0x${string}\"`.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.InvalidHexTypeError'\n        });\n    }\n}\n/**\n * Thrown when the provided hex value is invalid.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert('0x0123456789abcdefg')\n * // @error: Hex.InvalidHexValueError: Value `0x0123456789abcdefg` is an invalid hex value.\n * // @error: Hex values must start with `\"0x\"` and contain only hexadecimal characters (0-9, a-f, A-F).\n * ```\n */\nclass InvalidHexValueError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor(value) {\n        super(`Value \\`${value}\\` is an invalid hex value.`, {\n            metaMessages: [\n                'Hex values must start with `\"0x\"` and contain only hexadecimal characters (0-9, a-f, A-F).',\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.InvalidHexValueError'\n        });\n    }\n}\n/**\n * Thrown when the provided hex value is an odd length.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.fromHex('0xabcde')\n * // @error: Hex.InvalidLengthError: Hex value `\"0xabcde\"` is an odd length (5 nibbles).\n * ```\n */\nclass InvalidLengthError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor(value) {\n        super(`Hex value \\`\"${value}\"\\` is an odd length (${value.length - 2} nibbles).`, {\n            metaMessages: ['It must be an even length.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.InvalidLengthError'\n        });\n    }\n}\n/**\n * Thrown when the size of the value exceeds the expected max size.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromString('Hello World!', { size: 8 })\n * // @error: Hex.SizeOverflowError: Size cannot exceed `8` bytes. Given size: `12` bytes.\n * ```\n */\nclass SizeOverflowError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor({ givenSize, maxSize }) {\n        super(`Size cannot exceed \\`${maxSize}\\` bytes. Given size: \\`${givenSize}\\` bytes.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.SizeOverflowError'\n        });\n    }\n}\n/**\n * Thrown when the slice offset exceeds the bounds of the value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.slice('0x0123456789', 6)\n * // @error: Hex.SliceOffsetOutOfBoundsError: Slice starting at offset `6` is out-of-bounds (size: `5`).\n * ```\n */\nclass SliceOffsetOutOfBoundsError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor({ offset, position, size, }) {\n        super(`Slice ${position === 'start' ? 'starting' : 'ending'} at offset \\`${offset}\\` is out-of-bounds (size: \\`${size}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.SliceOffsetOutOfBoundsError'\n        });\n    }\n}\n/**\n * Thrown when the size of the value exceeds the pad size.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.padLeft('0x1a4e12a45a21323123aaa87a897a897a898a6567a578a867a98778a667a85a875a87a6a787a65a675a6a9', 32)\n * // @error: Hex.SizeExceedsPaddingSizeError: Hex size (`43`) exceeds padding size (`32`).\n * ```\n */\nclass SizeExceedsPaddingSizeError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor({ size, targetSize, type, }) {\n        super(`${type.charAt(0).toUpperCase()}${type\n            .slice(1)\n            .toLowerCase()} size (\\`${size}\\`) exceeds padding size (\\`${targetSize}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.SizeExceedsPaddingSizeError'\n        });\n    }\n}\n//# sourceMappingURL=Hex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/Hex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/Json.js":
/*!*******************************************!*\
  !*** ./node_modules/ox/_esm/core/Json.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\nconst bigIntSuffix = '#__bigint';\n/**\n * Parses a JSON string, with support for `bigint`.\n *\n * @example\n * ```ts twoslash\n * import { Json } from 'ox'\n *\n * const json = Json.parse('{\"foo\":\"bar\",\"baz\":\"69420694206942069420694206942069420694206942069420#__bigint\"}')\n * // @log: {\n * // @log:   foo: 'bar',\n * // @log:   baz: 69420694206942069420694206942069420694206942069420n\n * // @log: }\n * ```\n *\n * @param string - The value to parse.\n * @param reviver - A function that transforms the results.\n * @returns The parsed value.\n */\nfunction parse(string, reviver) {\n    return JSON.parse(string, (key, value_) => {\n        const value = value_;\n        if (typeof value === 'string' && value.endsWith(bigIntSuffix))\n            return BigInt(value.slice(0, -bigIntSuffix.length));\n        return typeof reviver === 'function' ? reviver(key, value) : value;\n    });\n}\n/**\n * Stringifies a value to its JSON representation, with support for `bigint`.\n *\n * @example\n * ```ts twoslash\n * import { Json } from 'ox'\n *\n * const json = Json.stringify({\n *   foo: 'bar',\n *   baz: 69420694206942069420694206942069420694206942069420n,\n * })\n * // @log: '{\"foo\":\"bar\",\"baz\":\"69420694206942069420694206942069420694206942069420#__bigint\"}'\n * ```\n *\n * @param value - The value to stringify.\n * @param replacer - A function that transforms the results. It is passed the key and value of the property, and must return the value to be used in the JSON string. If this function returns `undefined`, the property is not included in the resulting JSON string.\n * @param space - A string or number that determines the indentation of the JSON string. If it is a number, it indicates the number of spaces to use as indentation; if it is a string (e.g. `'\\t'`), it uses the string as the indentation character.\n * @returns The JSON string.\n */\nfunction stringify(value, replacer, space) {\n    return JSON.stringify(value, (key, value) => {\n        if (typeof replacer === 'function')\n            return replacer(key, value);\n        if (typeof value === 'bigint')\n            return value.toString() + bigIntSuffix;\n        return value;\n    }, space);\n}\n//# sourceMappingURL=Json.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/Json.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/Withdrawal.js":
/*!*************************************************!*\
  !*** ./node_modules/ox/_esm/core/Withdrawal.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromRpc: () => (/* binding */ fromRpc),\n/* harmony export */   toRpc: () => (/* binding */ toRpc)\n/* harmony export */ });\n/* harmony import */ var _Hex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_esm/core/Hex.js\");\n\n/**\n * Converts a {@link ox#Withdrawal.Rpc} to an {@link ox#Withdrawal.Withdrawal}.\n *\n * @example\n * ```ts twoslash\n * import { Withdrawal } from 'ox'\n *\n * const withdrawal = Withdrawal.fromRpc({\n *   address: '0x00000000219ab540356cBB839Cbe05303d7705Fa',\n *   amount: '0x620323',\n *   index: '0x0',\n *   validatorIndex: '0x1',\n * })\n * // @log: {\n * // @log:   address: '0x00000000219ab540356cBB839Cbe05303d7705Fa',\n * // @log:   amount: 6423331n,\n * // @log:   index: 0,\n * // @log:   validatorIndex: 1\n * // @log: }\n * ```\n *\n * @param withdrawal - The RPC withdrawal to convert.\n * @returns An instantiated {@link ox#Withdrawal.Withdrawal}.\n */\nfunction fromRpc(withdrawal) {\n    return {\n        ...withdrawal,\n        amount: BigInt(withdrawal.amount),\n        index: Number(withdrawal.index),\n        validatorIndex: Number(withdrawal.validatorIndex),\n    };\n}\n/**\n * Converts a {@link ox#Withdrawal.Withdrawal} to an {@link ox#Withdrawal.Rpc}.\n *\n * @example\n * ```ts twoslash\n * import { Withdrawal } from 'ox'\n *\n * const withdrawal = Withdrawal.toRpc({\n *   address: '0x00000000219ab540356cBB839Cbe05303d7705Fa',\n *   amount: 6423331n,\n *   index: 0,\n *   validatorIndex: 1,\n * })\n * // @log: {\n * // @log:   address: '0x00000000219ab540356cBB839Cbe05303d7705Fa',\n * // @log:   amount: '0x620323',\n * // @log:   index: '0x0',\n * // @log:   validatorIndex: '0x1',\n * // @log: }\n * ```\n *\n * @param withdrawal - The Withdrawal to convert.\n * @returns An RPC Withdrawal.\n */\nfunction toRpc(withdrawal) {\n    return {\n        address: withdrawal.address,\n        amount: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.fromNumber(withdrawal.amount),\n        index: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.fromNumber(withdrawal.index),\n        validatorIndex: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.fromNumber(withdrawal.validatorIndex),\n    };\n}\n//# sourceMappingURL=Withdrawal.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/Withdrawal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/internal/bytes.js":
/*!*****************************************************!*\
  !*** ./node_modules/ox/_esm/core/internal/bytes.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assertEndOffset: () => (/* binding */ assertEndOffset),\n/* harmony export */   assertSize: () => (/* binding */ assertSize),\n/* harmony export */   assertStartOffset: () => (/* binding */ assertStartOffset),\n/* harmony export */   charCodeMap: () => (/* binding */ charCodeMap),\n/* harmony export */   charCodeToBase16: () => (/* binding */ charCodeToBase16),\n/* harmony export */   pad: () => (/* binding */ pad),\n/* harmony export */   trim: () => (/* binding */ trim)\n/* harmony export */ });\n/* harmony import */ var _Bytes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Bytes.js */ \"(ssr)/./node_modules/ox/_esm/core/Bytes.js\");\n\n/** @internal */\nfunction assertSize(bytes, size_) {\n    if (_Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(bytes) > size_)\n        throw new _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.SizeOverflowError({\n            givenSize: _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(bytes),\n            maxSize: size_,\n        });\n}\n/** @internal */\nfunction assertStartOffset(value, start) {\n    if (typeof start === 'number' && start > 0 && start > _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(value) - 1)\n        throw new _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.SliceOffsetOutOfBoundsError({\n            offset: start,\n            position: 'start',\n            size: _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(value),\n        });\n}\n/** @internal */\nfunction assertEndOffset(value, start, end) {\n    if (typeof start === 'number' &&\n        typeof end === 'number' &&\n        _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(value) !== end - start) {\n        throw new _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.SliceOffsetOutOfBoundsError({\n            offset: end,\n            position: 'end',\n            size: _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(value),\n        });\n    }\n}\n/** @internal */\nconst charCodeMap = {\n    zero: 48,\n    nine: 57,\n    A: 65,\n    F: 70,\n    a: 97,\n    f: 102,\n};\n/** @internal */\nfunction charCodeToBase16(char) {\n    if (char >= charCodeMap.zero && char <= charCodeMap.nine)\n        return char - charCodeMap.zero;\n    if (char >= charCodeMap.A && char <= charCodeMap.F)\n        return char - (charCodeMap.A - 10);\n    if (char >= charCodeMap.a && char <= charCodeMap.f)\n        return char - (charCodeMap.a - 10);\n    return undefined;\n}\n/** @internal */\nfunction pad(bytes, options = {}) {\n    const { dir, size = 32 } = options;\n    if (size === 0)\n        return bytes;\n    if (bytes.length > size)\n        throw new _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.SizeExceedsPaddingSizeError({\n            size: bytes.length,\n            targetSize: size,\n            type: 'Bytes',\n        });\n    const paddedBytes = new Uint8Array(size);\n    for (let i = 0; i < size; i++) {\n        const padEnd = dir === 'right';\n        paddedBytes[padEnd ? i : size - i - 1] =\n            bytes[padEnd ? i : bytes.length - i - 1];\n    }\n    return paddedBytes;\n}\n/** @internal */\nfunction trim(value, options = {}) {\n    const { dir = 'left' } = options;\n    let data = value;\n    let sliceLength = 0;\n    for (let i = 0; i < data.length - 1; i++) {\n        if (data[dir === 'left' ? i : data.length - i - 1].toString() === '0')\n            sliceLength++;\n        else\n            break;\n    }\n    data =\n        dir === 'left'\n            ? data.slice(sliceLength)\n            : data.slice(0, data.length - sliceLength);\n    return data;\n}\n//# sourceMappingURL=bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/internal/bytes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/internal/errors.js":
/*!******************************************************!*\
  !*** ./node_modules/ox/_esm/core/internal/errors.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUrl: () => (/* binding */ getUrl),\n/* harmony export */   getVersion: () => (/* binding */ getVersion),\n/* harmony export */   prettyPrint: () => (/* binding */ prettyPrint)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/ox/_esm/core/version.js\");\n\n/** @internal */\nfunction getUrl(url) {\n    return url;\n}\n/** @internal */\nfunction getVersion() {\n    return _version_js__WEBPACK_IMPORTED_MODULE_0__.version;\n}\n/** @internal */\nfunction prettyPrint(args) {\n    if (!args)\n        return '';\n    const entries = Object.entries(args)\n        .map(([key, value]) => {\n        if (value === undefined || value === false)\n            return null;\n        return [key, value];\n    })\n        .filter(Boolean);\n    const maxLength = entries.reduce((acc, [key]) => Math.max(acc, key.length), 0);\n    return entries\n        .map(([key, value]) => `  ${`${key}:`.padEnd(maxLength + 1)}  ${value}`)\n        .join('\\n');\n}\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3gvX2VzbS9jb3JlL2ludGVybmFsL2Vycm9ycy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXdDO0FBQ3hDO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDTztBQUNQLFdBQVcsZ0RBQU87QUFDbEI7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLEdBQUcsSUFBSSwyQkFBMkIsRUFBRSxNQUFNO0FBQzlFO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9veC9fZXNtL2NvcmUvaW50ZXJuYWwvZXJyb3JzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHZlcnNpb24gfSBmcm9tICcuLi92ZXJzaW9uLmpzJztcbi8qKiBAaW50ZXJuYWwgKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRVcmwodXJsKSB7XG4gICAgcmV0dXJuIHVybDtcbn1cbi8qKiBAaW50ZXJuYWwgKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRWZXJzaW9uKCkge1xuICAgIHJldHVybiB2ZXJzaW9uO1xufVxuLyoqIEBpbnRlcm5hbCAqL1xuZXhwb3J0IGZ1bmN0aW9uIHByZXR0eVByaW50KGFyZ3MpIHtcbiAgICBpZiAoIWFyZ3MpXG4gICAgICAgIHJldHVybiAnJztcbiAgICBjb25zdCBlbnRyaWVzID0gT2JqZWN0LmVudHJpZXMoYXJncylcbiAgICAgICAgLm1hcCgoW2tleSwgdmFsdWVdKSA9PiB7XG4gICAgICAgIGlmICh2YWx1ZSA9PT0gdW5kZWZpbmVkIHx8IHZhbHVlID09PSBmYWxzZSlcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICByZXR1cm4gW2tleSwgdmFsdWVdO1xuICAgIH0pXG4gICAgICAgIC5maWx0ZXIoQm9vbGVhbik7XG4gICAgY29uc3QgbWF4TGVuZ3RoID0gZW50cmllcy5yZWR1Y2UoKGFjYywgW2tleV0pID0+IE1hdGgubWF4KGFjYywga2V5Lmxlbmd0aCksIDApO1xuICAgIHJldHVybiBlbnRyaWVzXG4gICAgICAgIC5tYXAoKFtrZXksIHZhbHVlXSkgPT4gYCAgJHtgJHtrZXl9OmAucGFkRW5kKG1heExlbmd0aCArIDEpfSAgJHt2YWx1ZX1gKVxuICAgICAgICAuam9pbignXFxuJyk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1lcnJvcnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/internal/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/internal/hex.js":
/*!***************************************************!*\
  !*** ./node_modules/ox/_esm/core/internal/hex.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assertEndOffset: () => (/* binding */ assertEndOffset),\n/* harmony export */   assertSize: () => (/* binding */ assertSize),\n/* harmony export */   assertStartOffset: () => (/* binding */ assertStartOffset),\n/* harmony export */   pad: () => (/* binding */ pad),\n/* harmony export */   trim: () => (/* binding */ trim)\n/* harmony export */ });\n/* harmony import */ var _Hex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Hex.js */ \"(ssr)/./node_modules/ox/_esm/core/Hex.js\");\n\n/** @internal */\nfunction assertSize(hex, size_) {\n    if (_Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(hex) > size_)\n        throw new _Hex_js__WEBPACK_IMPORTED_MODULE_0__.SizeOverflowError({\n            givenSize: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(hex),\n            maxSize: size_,\n        });\n}\n/** @internal */\nfunction assertStartOffset(value, start) {\n    if (typeof start === 'number' && start > 0 && start > _Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(value) - 1)\n        throw new _Hex_js__WEBPACK_IMPORTED_MODULE_0__.SliceOffsetOutOfBoundsError({\n            offset: start,\n            position: 'start',\n            size: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(value),\n        });\n}\n/** @internal */\nfunction assertEndOffset(value, start, end) {\n    if (typeof start === 'number' &&\n        typeof end === 'number' &&\n        _Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(value) !== end - start) {\n        throw new _Hex_js__WEBPACK_IMPORTED_MODULE_0__.SliceOffsetOutOfBoundsError({\n            offset: end,\n            position: 'end',\n            size: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(value),\n        });\n    }\n}\n/** @internal */\nfunction pad(hex_, options = {}) {\n    const { dir, size = 32 } = options;\n    if (size === 0)\n        return hex_;\n    const hex = hex_.replace('0x', '');\n    if (hex.length > size * 2)\n        throw new _Hex_js__WEBPACK_IMPORTED_MODULE_0__.SizeExceedsPaddingSizeError({\n            size: Math.ceil(hex.length / 2),\n            targetSize: size,\n            type: 'Hex',\n        });\n    return `0x${hex[dir === 'right' ? 'padEnd' : 'padStart'](size * 2, '0')}`;\n}\n/** @internal */\nfunction trim(value, options = {}) {\n    const { dir = 'left' } = options;\n    let data = value.replace('0x', '');\n    let sliceLength = 0;\n    for (let i = 0; i < data.length - 1; i++) {\n        if (data[dir === 'left' ? i : data.length - i - 1].toString() === '0')\n            sliceLength++;\n        else\n            break;\n    }\n    data =\n        dir === 'left'\n            ? data.slice(sliceLength)\n            : data.slice(0, data.length - sliceLength);\n    if (data === '0')\n        return '0x';\n    if (dir === 'right' && data.length % 2 === 1)\n        return `0x${data}0`;\n    return `0x${data}`;\n}\n//# sourceMappingURL=hex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/internal/hex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/version.js":
/*!**********************************************!*\
  !*** ./node_modules/ox/_esm/core/version.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\n/** @internal */\nconst version = '0.1.1';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3gvX2VzbS9jb3JlL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUCIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9veC9fZXNtL2NvcmUvdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiogQGludGVybmFsICovXG5leHBvcnQgY29uc3QgdmVyc2lvbiA9ICcwLjEuMSc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/node_modules/@noble/curves/esm/utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/ox/node_modules/@noble/curves/esm/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _validateObject: () => (/* binding */ _validateObject),\n/* harmony export */   aInRange: () => (/* binding */ aInRange),\n/* harmony export */   abool: () => (/* binding */ abool),\n/* harmony export */   abytes: () => (/* reexport safe */ _noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes),\n/* harmony export */   anumber: () => (/* reexport safe */ _noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.anumber),\n/* harmony export */   bitGet: () => (/* binding */ bitGet),\n/* harmony export */   bitLen: () => (/* binding */ bitLen),\n/* harmony export */   bitMask: () => (/* binding */ bitMask),\n/* harmony export */   bitSet: () => (/* binding */ bitSet),\n/* harmony export */   bytesToHex: () => (/* reexport safe */ _noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex),\n/* harmony export */   bytesToNumberBE: () => (/* binding */ bytesToNumberBE),\n/* harmony export */   bytesToNumberLE: () => (/* binding */ bytesToNumberLE),\n/* harmony export */   bytesToUtf8: () => (/* reexport safe */ _noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToUtf8),\n/* harmony export */   concatBytes: () => (/* reexport safe */ _noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes),\n/* harmony export */   createHmacDrbg: () => (/* binding */ createHmacDrbg),\n/* harmony export */   ensureBytes: () => (/* binding */ ensureBytes),\n/* harmony export */   equalBytes: () => (/* binding */ equalBytes),\n/* harmony export */   hexToBytes: () => (/* reexport safe */ _noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.hexToBytes),\n/* harmony export */   hexToNumber: () => (/* binding */ hexToNumber),\n/* harmony export */   inRange: () => (/* binding */ inRange),\n/* harmony export */   isBytes: () => (/* reexport safe */ _noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes),\n/* harmony export */   isHash: () => (/* binding */ isHash),\n/* harmony export */   memoized: () => (/* binding */ memoized),\n/* harmony export */   notImplemented: () => (/* binding */ notImplemented),\n/* harmony export */   numberToBytesBE: () => (/* binding */ numberToBytesBE),\n/* harmony export */   numberToBytesLE: () => (/* binding */ numberToBytesLE),\n/* harmony export */   numberToHexUnpadded: () => (/* binding */ numberToHexUnpadded),\n/* harmony export */   numberToVarBytesBE: () => (/* binding */ numberToVarBytesBE),\n/* harmony export */   randomBytes: () => (/* reexport safe */ _noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.randomBytes),\n/* harmony export */   utf8ToBytes: () => (/* reexport safe */ _noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes),\n/* harmony export */   validateObject: () => (/* binding */ validateObject)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n/**\n * Hex, bytes and number utilities.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nfunction abool(title, value) {\n    if (typeof value !== 'boolean')\n        throw new Error(title + ' boolean expected, got ' + value);\n}\n// Used in weierstrass, der\nfunction numberToHexUnpadded(num) {\n    const hex = num.toString(16);\n    return hex.length & 1 ? '0' + hex : hex;\n}\nfunction hexToNumber(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    return hex === '' ? _0n : BigInt('0x' + hex); // Big Endian\n}\n// BE: Big Endian, LE: Little Endian\nfunction bytesToNumberBE(bytes) {\n    return hexToNumber((0,_noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex)(bytes));\n}\nfunction bytesToNumberLE(bytes) {\n    (0,_noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(bytes);\n    return hexToNumber((0,_noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex)(Uint8Array.from(bytes).reverse()));\n}\nfunction numberToBytesBE(n, len) {\n    return (0,_noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.hexToBytes)(n.toString(16).padStart(len * 2, '0'));\n}\nfunction numberToBytesLE(n, len) {\n    return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nfunction numberToVarBytesBE(n) {\n    return (0,_noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.hexToBytes)(numberToHexUnpadded(n));\n}\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nfunction ensureBytes(title, hex, expectedLength) {\n    let res;\n    if (typeof hex === 'string') {\n        try {\n            res = (0,_noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.hexToBytes)(hex);\n        }\n        catch (e) {\n            throw new Error(title + ' must be hex string or Uint8Array, cause: ' + e);\n        }\n    }\n    else if ((0,_noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes)(hex)) {\n        // Uint8Array.from() instead of hash.slice() because node.js Buffer\n        // is instance of Uint8Array, and its slice() creates **mutable** copy\n        res = Uint8Array.from(hex);\n    }\n    else {\n        throw new Error(title + ' must be hex string or Uint8Array');\n    }\n    const len = res.length;\n    if (typeof expectedLength === 'number' && len !== expectedLength)\n        throw new Error(title + ' of length ' + expectedLength + ' expected, got ' + len);\n    return res;\n}\n// Compares 2 u8a-s in kinda constant time\nfunction equalBytes(a, b) {\n    if (a.length !== b.length)\n        return false;\n    let diff = 0;\n    for (let i = 0; i < a.length; i++)\n        diff |= a[i] ^ b[i];\n    return diff === 0;\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\n// export const utf8ToBytes: typeof utf8ToBytes_ = utf8ToBytes_;\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\n// export const bytesToUtf8: typeof bytesToUtf8_ = bytesToUtf8_;\n// Is positive bigint\nconst isPosBig = (n) => typeof n === 'bigint' && _0n <= n;\nfunction inRange(n, min, max) {\n    return isPosBig(n) && isPosBig(min) && isPosBig(max) && min <= n && n < max;\n}\n/**\n * Asserts min <= n < max. NOTE: It's < max and not <= max.\n * @example\n * aInRange('x', x, 1n, 256n); // would assume x is in (1n..255n)\n */\nfunction aInRange(title, n, min, max) {\n    // Why min <= n < max and not a (min < n < max) OR b (min <= n <= max)?\n    // consider P=256n, min=0n, max=P\n    // - a for min=0 would require -1:          `inRange('x', x, -1n, P)`\n    // - b would commonly require subtraction:  `inRange('x', x, 0n, P - 1n)`\n    // - our way is the cleanest:               `inRange('x', x, 0n, P)\n    if (!inRange(n, min, max))\n        throw new Error('expected valid ' + title + ': ' + min + ' <= n < ' + max + ', got ' + n);\n}\n// Bit operations\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n * TODO: merge with nLength in modular\n */\nfunction bitLen(n) {\n    let len;\n    for (len = 0; n > _0n; n >>= _1n, len += 1)\n        ;\n    return len;\n}\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nfunction bitGet(n, pos) {\n    return (n >> BigInt(pos)) & _1n;\n}\n/**\n * Sets single bit at position.\n */\nfunction bitSet(n, pos, value) {\n    return n | ((value ? _1n : _0n) << BigInt(pos));\n}\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nconst bitMask = (n) => (_1n << BigInt(n)) - _1n;\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nfunction createHmacDrbg(hashLen, qByteLen, hmacFn) {\n    if (typeof hashLen !== 'number' || hashLen < 2)\n        throw new Error('hashLen must be a number');\n    if (typeof qByteLen !== 'number' || qByteLen < 2)\n        throw new Error('qByteLen must be a number');\n    if (typeof hmacFn !== 'function')\n        throw new Error('hmacFn must be a function');\n    // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n    const u8n = (len) => new Uint8Array(len); // creates Uint8Array\n    const u8of = (byte) => Uint8Array.of(byte); // another shortcut\n    let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n    let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n    let i = 0; // Iterations counter, will throw when over 1000\n    const reset = () => {\n        v.fill(1);\n        k.fill(0);\n        i = 0;\n    };\n    const h = (...b) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n    const reseed = (seed = u8n(0)) => {\n        // HMAC-DRBG reseed() function. Steps D-G\n        k = h(u8of(0x00), seed); // k = hmac(k || v || 0x00 || seed)\n        v = h(); // v = hmac(k || v)\n        if (seed.length === 0)\n            return;\n        k = h(u8of(0x01), seed); // k = hmac(k || v || 0x01 || seed)\n        v = h(); // v = hmac(k || v)\n    };\n    const gen = () => {\n        // HMAC-DRBG generate() function\n        if (i++ >= 1000)\n            throw new Error('drbg: tried 1000 values');\n        let len = 0;\n        const out = [];\n        while (len < qByteLen) {\n            v = h();\n            const sl = v.slice();\n            out.push(sl);\n            len += v.length;\n        }\n        return (0,_noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...out);\n    };\n    const genUntil = (seed, pred) => {\n        reset();\n        reseed(seed); // Steps D-G\n        let res = undefined; // Step H: grind until k is in [1..n-1]\n        while (!(res = pred(gen())))\n            reseed();\n        reset();\n        return res;\n    };\n    return genUntil;\n}\n// Validating curves and fields\nconst validatorFns = {\n    bigint: (val) => typeof val === 'bigint',\n    function: (val) => typeof val === 'function',\n    boolean: (val) => typeof val === 'boolean',\n    string: (val) => typeof val === 'string',\n    stringOrUint8Array: (val) => typeof val === 'string' || (0,_noble_hashes_utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes)(val),\n    isSafeInteger: (val) => Number.isSafeInteger(val),\n    array: (val) => Array.isArray(val),\n    field: (val, object) => object.Fp.isValid(val),\n    hash: (val) => typeof val === 'function' && Number.isSafeInteger(val.outputLen),\n};\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\nfunction validateObject(object, validators, optValidators = {}) {\n    const checkField = (fieldName, type, isOptional) => {\n        const checkVal = validatorFns[type];\n        if (typeof checkVal !== 'function')\n            throw new Error('invalid validator function');\n        const val = object[fieldName];\n        if (isOptional && val === undefined)\n            return;\n        if (!checkVal(val, object)) {\n            throw new Error('param ' + String(fieldName) + ' is invalid. Expected ' + type + ', got ' + val);\n        }\n    };\n    for (const [fieldName, type] of Object.entries(validators))\n        checkField(fieldName, type, false);\n    for (const [fieldName, type] of Object.entries(optValidators))\n        checkField(fieldName, type, true);\n    return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\nfunction isHash(val) {\n    return typeof val === 'function' && Number.isSafeInteger(val.outputLen);\n}\nfunction _validateObject(object, fields, optFields = {}) {\n    if (!object || typeof object !== 'object')\n        throw new Error('expected valid options object');\n    function checkField(fieldName, expectedType, isOpt) {\n        const val = object[fieldName];\n        if (isOpt && val === undefined)\n            return;\n        const current = typeof val;\n        if (current !== expectedType || val === null)\n            throw new Error(`param \"${fieldName}\" is invalid: expected ${expectedType}, got ${current}`);\n    }\n    Object.entries(fields).forEach(([k, v]) => checkField(k, v, false));\n    Object.entries(optFields).forEach(([k, v]) => checkField(k, v, true));\n}\n/**\n * throws not implemented error\n */\nconst notImplemented = () => {\n    throw new Error('not implemented');\n};\n/**\n * Memoizes (caches) computation result.\n * Uses WeakMap: the value is going auto-cleaned by GC after last reference is removed.\n */\nfunction memoized(fn) {\n    const map = new WeakMap();\n    return (arg, ...args) => {\n        const val = map.get(arg);\n        if (val !== undefined)\n            return val;\n        const computed = fn(arg, ...args);\n        map.set(arg, computed);\n        return computed;\n    };\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/node_modules/@noble/curves/esm/utils.js\n");

/***/ })

};
;