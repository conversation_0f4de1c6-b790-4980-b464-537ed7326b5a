"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/big.js";
exports.ids = ["vendor-chunks/big.js"];
exports.modules = {

/***/ "(ssr)/./node_modules/big.js/big.mjs":
/*!*************************************!*\
  !*** ./node_modules/big.js/big.mjs ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Big: () => (/* binding */ Big),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*\r\n *  big.js v6.2.2\r\n *  A small, fast, easy-to-use library for arbitrary-precision decimal arithmetic.\r\n *  Copyright (c) 2024 Michael Mclaughlin\r\n *  https://github.com/MikeMcl/big.js/LICENCE.md\r\n */\r\n\r\n\r\n/************************************** EDITABLE DEFAULTS *****************************************/\r\n\r\n\r\n  // The default values below must be integers within the stated ranges.\r\n\r\n  /*\r\n   * The maximum number of decimal places (DP) of the results of operations involving division:\r\n   * div and sqrt, and pow with negative exponents.\r\n   */\r\nvar DP = 20,          // 0 to MAX_DP\r\n\r\n  /*\r\n   * The rounding mode (RM) used when rounding to the above decimal places.\r\n   *\r\n   *  0  Towards zero (i.e. truncate, no rounding).       (ROUND_DOWN)\r\n   *  1  To nearest neighbour. If equidistant, round up.  (ROUND_HALF_UP)\r\n   *  2  To nearest neighbour. If equidistant, to even.   (ROUND_HALF_EVEN)\r\n   *  3  Away from zero.                                  (ROUND_UP)\r\n   */\r\n  RM = 1,             // 0, 1, 2 or 3\r\n\r\n  // The maximum value of DP and Big.DP.\r\n  MAX_DP = 1E6,       // 0 to 1000000\r\n\r\n  // The maximum magnitude of the exponent argument to the pow method.\r\n  MAX_POWER = 1E6,    // 1 to 1000000\r\n\r\n  /*\r\n   * The negative exponent (NE) at and beneath which toString returns exponential notation.\r\n   * (JavaScript numbers: -7)\r\n   * -1000000 is the minimum recommended exponent value of a Big.\r\n   */\r\n  NE = -7,            // 0 to -1000000\r\n\r\n  /*\r\n   * The positive exponent (PE) at and above which toString returns exponential notation.\r\n   * (JavaScript numbers: 21)\r\n   * 1000000 is the maximum recommended exponent value of a Big, but this limit is not enforced.\r\n   */\r\n  PE = 21,            // 0 to 1000000\r\n\r\n  /*\r\n   * When true, an error will be thrown if a primitive number is passed to the Big constructor,\r\n   * or if valueOf is called, or if toNumber is called on a Big which cannot be converted to a\r\n   * primitive number without a loss of precision.\r\n   */\r\n  STRICT = false,     // true or false\r\n\r\n\r\n/**************************************************************************************************/\r\n\r\n\r\n  // Error messages.\r\n  NAME = '[big.js] ',\r\n  INVALID = NAME + 'Invalid ',\r\n  INVALID_DP = INVALID + 'decimal places',\r\n  INVALID_RM = INVALID + 'rounding mode',\r\n  DIV_BY_ZERO = NAME + 'Division by zero',\r\n\r\n  // The shared prototype object.\r\n  P = {},\r\n  UNDEFINED = void 0,\r\n  NUMERIC = /^-?(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i;\r\n\r\n\r\n/*\r\n * Create and return a Big constructor.\r\n */\r\nfunction _Big_() {\r\n\r\n  /*\r\n   * The Big constructor and exported function.\r\n   * Create and return a new instance of a Big number object.\r\n   *\r\n   * n {number|string|Big} A numeric value.\r\n   */\r\n  function Big(n) {\r\n    var x = this;\r\n\r\n    // Enable constructor usage without new.\r\n    if (!(x instanceof Big)) return n === UNDEFINED ? _Big_() : new Big(n);\r\n\r\n    // Duplicate.\r\n    if (n instanceof Big) {\r\n      x.s = n.s;\r\n      x.e = n.e;\r\n      x.c = n.c.slice();\r\n    } else {\r\n      if (typeof n !== 'string') {\r\n        if (Big.strict === true && typeof n !== 'bigint') {\r\n          throw TypeError(INVALID + 'value');\r\n        }\r\n\r\n        // Minus zero?\r\n        n = n === 0 && 1 / n < 0 ? '-0' : String(n);\r\n      }\r\n\r\n      parse(x, n);\r\n    }\r\n\r\n    // Retain a reference to this Big constructor.\r\n    // Shadow Big.prototype.constructor which points to Object.\r\n    x.constructor = Big;\r\n  }\r\n\r\n  Big.prototype = P;\r\n  Big.DP = DP;\r\n  Big.RM = RM;\r\n  Big.NE = NE;\r\n  Big.PE = PE;\r\n  Big.strict = STRICT;\r\n  Big.roundDown = 0;\r\n  Big.roundHalfUp = 1;\r\n  Big.roundHalfEven = 2;\r\n  Big.roundUp = 3;\r\n\r\n  return Big;\r\n}\r\n\r\n\r\n/*\r\n * Parse the number or string value passed to a Big constructor.\r\n *\r\n * x {Big} A Big number instance.\r\n * n {number|string} A numeric value.\r\n */\r\nfunction parse(x, n) {\r\n  var e, i, nl;\r\n\r\n  if (!NUMERIC.test(n)) {\r\n    throw Error(INVALID + 'number');\r\n  }\r\n\r\n  // Determine sign.\r\n  x.s = n.charAt(0) == '-' ? (n = n.slice(1), -1) : 1;\r\n\r\n  // Decimal point?\r\n  if ((e = n.indexOf('.')) > -1) n = n.replace('.', '');\r\n\r\n  // Exponential form?\r\n  if ((i = n.search(/e/i)) > 0) {\r\n\r\n    // Determine exponent.\r\n    if (e < 0) e = i;\r\n    e += +n.slice(i + 1);\r\n    n = n.substring(0, i);\r\n  } else if (e < 0) {\r\n\r\n    // Integer.\r\n    e = n.length;\r\n  }\r\n\r\n  nl = n.length;\r\n\r\n  // Determine leading zeros.\r\n  for (i = 0; i < nl && n.charAt(i) == '0';) ++i;\r\n\r\n  if (i == nl) {\r\n\r\n    // Zero.\r\n    x.c = [x.e = 0];\r\n  } else {\r\n\r\n    // Determine trailing zeros.\r\n    for (; nl > 0 && n.charAt(--nl) == '0';);\r\n    x.e = e - i - 1;\r\n    x.c = [];\r\n\r\n    // Convert string to array of digits without leading/trailing zeros.\r\n    for (e = 0; i <= nl;) x.c[e++] = +n.charAt(i++);\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Round Big x to a maximum of sd significant digits using rounding mode rm.\r\n *\r\n * x {Big} The Big to round.\r\n * sd {number} Significant digits: integer, 0 to MAX_DP inclusive.\r\n * rm {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n * [more] {boolean} Whether the result of division was truncated.\r\n */\r\nfunction round(x, sd, rm, more) {\r\n  var xc = x.c;\r\n\r\n  if (rm === UNDEFINED) rm = x.constructor.RM;\r\n  if (rm !== 0 && rm !== 1 && rm !== 2 && rm !== 3) {\r\n    throw Error(INVALID_RM);\r\n  }\r\n\r\n  if (sd < 1) {\r\n    more =\r\n      rm === 3 && (more || !!xc[0]) || sd === 0 && (\r\n      rm === 1 && xc[0] >= 5 ||\r\n      rm === 2 && (xc[0] > 5 || xc[0] === 5 && (more || xc[1] !== UNDEFINED))\r\n    );\r\n\r\n    xc.length = 1;\r\n\r\n    if (more) {\r\n\r\n      // 1, 0.1, 0.01, 0.001, 0.0001 etc.\r\n      x.e = x.e - sd + 1;\r\n      xc[0] = 1;\r\n    } else {\r\n\r\n      // Zero.\r\n      xc[0] = x.e = 0;\r\n    }\r\n  } else if (sd < xc.length) {\r\n\r\n    // xc[sd] is the digit after the digit that may be rounded up.\r\n    more =\r\n      rm === 1 && xc[sd] >= 5 ||\r\n      rm === 2 && (xc[sd] > 5 || xc[sd] === 5 &&\r\n        (more || xc[sd + 1] !== UNDEFINED || xc[sd - 1] & 1)) ||\r\n      rm === 3 && (more || !!xc[0]);\r\n\r\n    // Remove any digits after the required precision.\r\n    xc.length = sd;\r\n\r\n    // Round up?\r\n    if (more) {\r\n\r\n      // Rounding up may mean the previous digit has to be rounded up.\r\n      for (; ++xc[--sd] > 9;) {\r\n        xc[sd] = 0;\r\n        if (sd === 0) {\r\n          ++x.e;\r\n          xc.unshift(1);\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    for (sd = xc.length; !xc[--sd];) xc.pop();\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Return a string representing the value of Big x in normal or exponential notation.\r\n * Handles P.toExponential, P.toFixed, P.toJSON, P.toPrecision, P.toString and P.valueOf.\r\n */\r\nfunction stringify(x, doExponential, isNonzero) {\r\n  var e = x.e,\r\n    s = x.c.join(''),\r\n    n = s.length;\r\n\r\n  // Exponential notation?\r\n  if (doExponential) {\r\n    s = s.charAt(0) + (n > 1 ? '.' + s.slice(1) : '') + (e < 0 ? 'e' : 'e+') + e;\r\n\r\n  // Normal notation.\r\n  } else if (e < 0) {\r\n    for (; ++e;) s = '0' + s;\r\n    s = '0.' + s;\r\n  } else if (e > 0) {\r\n    if (++e > n) {\r\n      for (e -= n; e--;) s += '0';\r\n    } else if (e < n) {\r\n      s = s.slice(0, e) + '.' + s.slice(e);\r\n    }\r\n  } else if (n > 1) {\r\n    s = s.charAt(0) + '.' + s.slice(1);\r\n  }\r\n\r\n  return x.s < 0 && isNonzero ? '-' + s : s;\r\n}\r\n\r\n\r\n// Prototype/instance methods\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the absolute value of this Big.\r\n */\r\nP.abs = function () {\r\n  var x = new this.constructor(this);\r\n  x.s = 1;\r\n  return x;\r\n};\r\n\r\n\r\n/*\r\n * Return 1 if the value of this Big is greater than the value of Big y,\r\n *       -1 if the value of this Big is less than the value of Big y, or\r\n *        0 if they have the same value.\r\n */\r\nP.cmp = function (y) {\r\n  var isneg,\r\n    x = this,\r\n    xc = x.c,\r\n    yc = (y = new x.constructor(y)).c,\r\n    i = x.s,\r\n    j = y.s,\r\n    k = x.e,\r\n    l = y.e;\r\n\r\n  // Either zero?\r\n  if (!xc[0] || !yc[0]) return !xc[0] ? !yc[0] ? 0 : -j : i;\r\n\r\n  // Signs differ?\r\n  if (i != j) return i;\r\n\r\n  isneg = i < 0;\r\n\r\n  // Compare exponents.\r\n  if (k != l) return k > l ^ isneg ? 1 : -1;\r\n\r\n  j = (k = xc.length) < (l = yc.length) ? k : l;\r\n\r\n  // Compare digit by digit.\r\n  for (i = -1; ++i < j;) {\r\n    if (xc[i] != yc[i]) return xc[i] > yc[i] ^ isneg ? 1 : -1;\r\n  }\r\n\r\n  // Compare lengths.\r\n  return k == l ? 0 : k > l ^ isneg ? 1 : -1;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big divided by the value of Big y, rounded,\r\n * if necessary, to a maximum of Big.DP decimal places using rounding mode Big.RM.\r\n */\r\nP.div = function (y) {\r\n  var x = this,\r\n    Big = x.constructor,\r\n    a = x.c,                  // dividend\r\n    b = (y = new Big(y)).c,   // divisor\r\n    k = x.s == y.s ? 1 : -1,\r\n    dp = Big.DP;\r\n\r\n  if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\r\n    throw Error(INVALID_DP);\r\n  }\r\n\r\n  // Divisor is zero?\r\n  if (!b[0]) {\r\n    throw Error(DIV_BY_ZERO);\r\n  }\r\n\r\n  // Dividend is 0? Return +-0.\r\n  if (!a[0]) {\r\n    y.s = k;\r\n    y.c = [y.e = 0];\r\n    return y;\r\n  }\r\n\r\n  var bl, bt, n, cmp, ri,\r\n    bz = b.slice(),\r\n    ai = bl = b.length,\r\n    al = a.length,\r\n    r = a.slice(0, bl),   // remainder\r\n    rl = r.length,\r\n    q = y,                // quotient\r\n    qc = q.c = [],\r\n    qi = 0,\r\n    p = dp + (q.e = x.e - y.e) + 1;    // precision of the result\r\n\r\n  q.s = k;\r\n  k = p < 0 ? 0 : p;\r\n\r\n  // Create version of divisor with leading zero.\r\n  bz.unshift(0);\r\n\r\n  // Add zeros to make remainder as long as divisor.\r\n  for (; rl++ < bl;) r.push(0);\r\n\r\n  do {\r\n\r\n    // n is how many times the divisor goes into current remainder.\r\n    for (n = 0; n < 10; n++) {\r\n\r\n      // Compare divisor and remainder.\r\n      if (bl != (rl = r.length)) {\r\n        cmp = bl > rl ? 1 : -1;\r\n      } else {\r\n        for (ri = -1, cmp = 0; ++ri < bl;) {\r\n          if (b[ri] != r[ri]) {\r\n            cmp = b[ri] > r[ri] ? 1 : -1;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      // If divisor < remainder, subtract divisor from remainder.\r\n      if (cmp < 0) {\r\n\r\n        // Remainder can't be more than 1 digit longer than divisor.\r\n        // Equalise lengths using divisor with extra leading zero?\r\n        for (bt = rl == bl ? b : bz; rl;) {\r\n          if (r[--rl] < bt[rl]) {\r\n            ri = rl;\r\n            for (; ri && !r[--ri];) r[ri] = 9;\r\n            --r[ri];\r\n            r[rl] += 10;\r\n          }\r\n          r[rl] -= bt[rl];\r\n        }\r\n\r\n        for (; !r[0];) r.shift();\r\n      } else {\r\n        break;\r\n      }\r\n    }\r\n\r\n    // Add the digit n to the result array.\r\n    qc[qi++] = cmp ? n : ++n;\r\n\r\n    // Update the remainder.\r\n    if (r[0] && cmp) r[rl] = a[ai] || 0;\r\n    else r = [a[ai]];\r\n\r\n  } while ((ai++ < al || r[0] !== UNDEFINED) && k--);\r\n\r\n  // Leading zero? Do not remove if result is simply zero (qi == 1).\r\n  if (!qc[0] && qi != 1) {\r\n\r\n    // There can't be more than one zero.\r\n    qc.shift();\r\n    q.e--;\r\n    p--;\r\n  }\r\n\r\n  // Round?\r\n  if (qi > p) round(q, p, Big.RM, r[0] !== UNDEFINED);\r\n\r\n  return q;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is equal to the value of Big y, otherwise return false.\r\n */\r\nP.eq = function (y) {\r\n  return this.cmp(y) === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is greater than the value of Big y, otherwise return\r\n * false.\r\n */\r\nP.gt = function (y) {\r\n  return this.cmp(y) > 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is greater than or equal to the value of Big y, otherwise\r\n * return false.\r\n */\r\nP.gte = function (y) {\r\n  return this.cmp(y) > -1;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is less than the value of Big y, otherwise return false.\r\n */\r\nP.lt = function (y) {\r\n  return this.cmp(y) < 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is less than or equal to the value of Big y, otherwise\r\n * return false.\r\n */\r\nP.lte = function (y) {\r\n  return this.cmp(y) < 1;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big minus the value of Big y.\r\n */\r\nP.minus = P.sub = function (y) {\r\n  var i, j, t, xlty,\r\n    x = this,\r\n    Big = x.constructor,\r\n    a = x.s,\r\n    b = (y = new Big(y)).s;\r\n\r\n  // Signs differ?\r\n  if (a != b) {\r\n    y.s = -b;\r\n    return x.plus(y);\r\n  }\r\n\r\n  var xc = x.c.slice(),\r\n    xe = x.e,\r\n    yc = y.c,\r\n    ye = y.e;\r\n\r\n  // Either zero?\r\n  if (!xc[0] || !yc[0]) {\r\n    if (yc[0]) {\r\n      y.s = -b;\r\n    } else if (xc[0]) {\r\n      y = new Big(x);\r\n    } else {\r\n      y.s = 1;\r\n    }\r\n    return y;\r\n  }\r\n\r\n  // Determine which is the bigger number. Prepend zeros to equalise exponents.\r\n  if (a = xe - ye) {\r\n\r\n    if (xlty = a < 0) {\r\n      a = -a;\r\n      t = xc;\r\n    } else {\r\n      ye = xe;\r\n      t = yc;\r\n    }\r\n\r\n    t.reverse();\r\n    for (b = a; b--;) t.push(0);\r\n    t.reverse();\r\n  } else {\r\n\r\n    // Exponents equal. Check digit by digit.\r\n    j = ((xlty = xc.length < yc.length) ? xc : yc).length;\r\n\r\n    for (a = b = 0; b < j; b++) {\r\n      if (xc[b] != yc[b]) {\r\n        xlty = xc[b] < yc[b];\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  // x < y? Point xc to the array of the bigger number.\r\n  if (xlty) {\r\n    t = xc;\r\n    xc = yc;\r\n    yc = t;\r\n    y.s = -y.s;\r\n  }\r\n\r\n  /*\r\n   * Append zeros to xc if shorter. No need to add zeros to yc if shorter as subtraction only\r\n   * needs to start at yc.length.\r\n   */\r\n  if ((b = (j = yc.length) - (i = xc.length)) > 0) for (; b--;) xc[i++] = 0;\r\n\r\n  // Subtract yc from xc.\r\n  for (b = i; j > a;) {\r\n    if (xc[--j] < yc[j]) {\r\n      for (i = j; i && !xc[--i];) xc[i] = 9;\r\n      --xc[i];\r\n      xc[j] += 10;\r\n    }\r\n\r\n    xc[j] -= yc[j];\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (; xc[--b] === 0;) xc.pop();\r\n\r\n  // Remove leading zeros and adjust exponent accordingly.\r\n  for (; xc[0] === 0;) {\r\n    xc.shift();\r\n    --ye;\r\n  }\r\n\r\n  if (!xc[0]) {\r\n\r\n    // n - n = +0\r\n    y.s = 1;\r\n\r\n    // Result must be zero.\r\n    xc = [ye = 0];\r\n  }\r\n\r\n  y.c = xc;\r\n  y.e = ye;\r\n\r\n  return y;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big modulo the value of Big y.\r\n */\r\nP.mod = function (y) {\r\n  var ygtx,\r\n    x = this,\r\n    Big = x.constructor,\r\n    a = x.s,\r\n    b = (y = new Big(y)).s;\r\n\r\n  if (!y.c[0]) {\r\n    throw Error(DIV_BY_ZERO);\r\n  }\r\n\r\n  x.s = y.s = 1;\r\n  ygtx = y.cmp(x) == 1;\r\n  x.s = a;\r\n  y.s = b;\r\n\r\n  if (ygtx) return new Big(x);\r\n\r\n  a = Big.DP;\r\n  b = Big.RM;\r\n  Big.DP = Big.RM = 0;\r\n  x = x.div(y);\r\n  Big.DP = a;\r\n  Big.RM = b;\r\n\r\n  return this.minus(x.times(y));\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big negated.\r\n */\r\nP.neg = function () {\r\n  var x = new this.constructor(this);\r\n  x.s = -x.s;\r\n  return x;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big plus the value of Big y.\r\n */\r\nP.plus = P.add = function (y) {\r\n  var e, k, t,\r\n    x = this,\r\n    Big = x.constructor;\r\n\r\n  y = new Big(y);\r\n\r\n  // Signs differ?\r\n  if (x.s != y.s) {\r\n    y.s = -y.s;\r\n    return x.minus(y);\r\n  }\r\n\r\n  var xe = x.e,\r\n    xc = x.c,\r\n    ye = y.e,\r\n    yc = y.c;\r\n\r\n  // Either zero?\r\n  if (!xc[0] || !yc[0]) {\r\n    if (!yc[0]) {\r\n      if (xc[0]) {\r\n        y = new Big(x);\r\n      } else {\r\n        y.s = x.s;\r\n      }\r\n    }\r\n    return y;\r\n  }\r\n\r\n  xc = xc.slice();\r\n\r\n  // Prepend zeros to equalise exponents.\r\n  // Note: reverse faster than unshifts.\r\n  if (e = xe - ye) {\r\n    if (e > 0) {\r\n      ye = xe;\r\n      t = yc;\r\n    } else {\r\n      e = -e;\r\n      t = xc;\r\n    }\r\n\r\n    t.reverse();\r\n    for (; e--;) t.push(0);\r\n    t.reverse();\r\n  }\r\n\r\n  // Point xc to the longer array.\r\n  if (xc.length - yc.length < 0) {\r\n    t = yc;\r\n    yc = xc;\r\n    xc = t;\r\n  }\r\n\r\n  e = yc.length;\r\n\r\n  // Only start adding at yc.length - 1 as the further digits of xc can be left as they are.\r\n  for (k = 0; e; xc[e] %= 10) k = (xc[--e] = xc[e] + yc[e] + k) / 10 | 0;\r\n\r\n  // No need to check for zero, as +x + +y != 0 && -x + -y != 0\r\n\r\n  if (k) {\r\n    xc.unshift(k);\r\n    ++ye;\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (e = xc.length; xc[--e] === 0;) xc.pop();\r\n\r\n  y.c = xc;\r\n  y.e = ye;\r\n\r\n  return y;\r\n};\r\n\r\n\r\n/*\r\n * Return a Big whose value is the value of this Big raised to the power n.\r\n * If n is negative, round to a maximum of Big.DP decimal places using rounding\r\n * mode Big.RM.\r\n *\r\n * n {number} Integer, -MAX_POWER to MAX_POWER inclusive.\r\n */\r\nP.pow = function (n) {\r\n  var x = this,\r\n    one = new x.constructor('1'),\r\n    y = one,\r\n    isneg = n < 0;\r\n\r\n  if (n !== ~~n || n < -MAX_POWER || n > MAX_POWER) {\r\n    throw Error(INVALID + 'exponent');\r\n  }\r\n\r\n  if (isneg) n = -n;\r\n\r\n  for (;;) {\r\n    if (n & 1) y = y.times(x);\r\n    n >>= 1;\r\n    if (!n) break;\r\n    x = x.times(x);\r\n  }\r\n\r\n  return isneg ? one.div(y) : y;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big rounded to a maximum precision of sd\r\n * significant digits using rounding mode rm, or Big.RM if rm is not specified.\r\n *\r\n * sd {number} Significant digits: integer, 1 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.prec = function (sd, rm) {\r\n  if (sd !== ~~sd || sd < 1 || sd > MAX_DP) {\r\n    throw Error(INVALID + 'precision');\r\n  }\r\n  return round(new this.constructor(this), sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big rounded to a maximum of dp decimal places\r\n * using rounding mode rm, or Big.RM if rm is not specified.\r\n * If dp is negative, round to an integer which is a multiple of 10**-dp.\r\n * If dp is not specified, round to 0 decimal places.\r\n *\r\n * dp? {number} Integer, -MAX_DP to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.round = function (dp, rm) {\r\n  if (dp === UNDEFINED) dp = 0;\r\n  else if (dp !== ~~dp || dp < -MAX_DP || dp > MAX_DP) {\r\n    throw Error(INVALID_DP);\r\n  }\r\n  return round(new this.constructor(this), dp + this.e + 1, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the square root of the value of this Big, rounded, if\r\n * necessary, to a maximum of Big.DP decimal places using rounding mode Big.RM.\r\n */\r\nP.sqrt = function () {\r\n  var r, c, t,\r\n    x = this,\r\n    Big = x.constructor,\r\n    s = x.s,\r\n    e = x.e,\r\n    half = new Big('0.5');\r\n\r\n  // Zero?\r\n  if (!x.c[0]) return new Big(x);\r\n\r\n  // Negative?\r\n  if (s < 0) {\r\n    throw Error(NAME + 'No square root');\r\n  }\r\n\r\n  // Estimate.\r\n  s = Math.sqrt(+stringify(x, true, true));\r\n\r\n  // Math.sqrt underflow/overflow?\r\n  // Re-estimate: pass x coefficient to Math.sqrt as integer, then adjust the result exponent.\r\n  if (s === 0 || s === 1 / 0) {\r\n    c = x.c.join('');\r\n    if (!(c.length + e & 1)) c += '0';\r\n    s = Math.sqrt(c);\r\n    e = ((e + 1) / 2 | 0) - (e < 0 || e & 1);\r\n    r = new Big((s == 1 / 0 ? '5e' : (s = s.toExponential()).slice(0, s.indexOf('e') + 1)) + e);\r\n  } else {\r\n    r = new Big(s + '');\r\n  }\r\n\r\n  e = r.e + (Big.DP += 4);\r\n\r\n  // Newton-Raphson iteration.\r\n  do {\r\n    t = r;\r\n    r = half.times(t.plus(x.div(t)));\r\n  } while (t.c.slice(0, e).join('') !== r.c.slice(0, e).join(''));\r\n\r\n  return round(r, (Big.DP -= 4) + r.e + 1, Big.RM);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big times the value of Big y.\r\n */\r\nP.times = P.mul = function (y) {\r\n  var c,\r\n    x = this,\r\n    Big = x.constructor,\r\n    xc = x.c,\r\n    yc = (y = new Big(y)).c,\r\n    a = xc.length,\r\n    b = yc.length,\r\n    i = x.e,\r\n    j = y.e;\r\n\r\n  // Determine sign of result.\r\n  y.s = x.s == y.s ? 1 : -1;\r\n\r\n  // Return signed 0 if either 0.\r\n  if (!xc[0] || !yc[0]) {\r\n    y.c = [y.e = 0];\r\n    return y;\r\n  }\r\n\r\n  // Initialise exponent of result as x.e + y.e.\r\n  y.e = i + j;\r\n\r\n  // If array xc has fewer digits than yc, swap xc and yc, and lengths.\r\n  if (a < b) {\r\n    c = xc;\r\n    xc = yc;\r\n    yc = c;\r\n    j = a;\r\n    a = b;\r\n    b = j;\r\n  }\r\n\r\n  // Initialise coefficient array of result with zeros.\r\n  for (c = new Array(j = a + b); j--;) c[j] = 0;\r\n\r\n  // Multiply.\r\n\r\n  // i is initially xc.length.\r\n  for (i = b; i--;) {\r\n    b = 0;\r\n\r\n    // a is yc.length.\r\n    for (j = a + i; j > i;) {\r\n\r\n      // Current sum of products at this digit position, plus carry.\r\n      b = c[j] + yc[i] * xc[j - i - 1] + b;\r\n      c[j--] = b % 10;\r\n\r\n      // carry\r\n      b = b / 10 | 0;\r\n    }\r\n\r\n    c[j] = b;\r\n  }\r\n\r\n  // Increment result exponent if there is a final carry, otherwise remove leading zero.\r\n  if (b) ++y.e;\r\n  else c.shift();\r\n\r\n  // Remove trailing zeros.\r\n  for (i = c.length; !c[--i];) c.pop();\r\n  y.c = c;\r\n\r\n  return y;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big in exponential notation rounded to dp fixed\r\n * decimal places using rounding mode rm, or Big.RM if rm is not specified.\r\n *\r\n * dp? {number} Decimal places: integer, 0 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.toExponential = function (dp, rm) {\r\n  var x = this,\r\n    n = x.c[0];\r\n\r\n  if (dp !== UNDEFINED) {\r\n    if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\r\n      throw Error(INVALID_DP);\r\n    }\r\n    x = round(new x.constructor(x), ++dp, rm);\r\n    for (; x.c.length < dp;) x.c.push(0);\r\n  }\r\n\r\n  return stringify(x, true, !!n);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big in normal notation rounded to dp fixed\r\n * decimal places using rounding mode rm, or Big.RM if rm is not specified.\r\n *\r\n * dp? {number} Decimal places: integer, 0 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n *\r\n * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n */\r\nP.toFixed = function (dp, rm) {\r\n  var x = this,\r\n    n = x.c[0];\r\n\r\n  if (dp !== UNDEFINED) {\r\n    if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\r\n      throw Error(INVALID_DP);\r\n    }\r\n    x = round(new x.constructor(x), dp + x.e + 1, rm);\r\n\r\n    // x.e may have changed if the value is rounded up.\r\n    for (dp = dp + x.e + 1; x.c.length < dp;) x.c.push(0);\r\n  }\r\n\r\n  return stringify(x, false, !!n);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big.\r\n * Return exponential notation if this Big has a positive exponent equal to or greater than\r\n * Big.PE, or a negative exponent equal to or less than Big.NE.\r\n * Omit the sign for negative zero.\r\n */\r\nP[Symbol.for('nodejs.util.inspect.custom')] = P.toJSON = P.toString = function () {\r\n  var x = this,\r\n    Big = x.constructor;\r\n  return stringify(x, x.e <= Big.NE || x.e >= Big.PE, !!x.c[0]);\r\n};\r\n\r\n\r\n/*\r\n * Return the value of this Big as a primitve number.\r\n */\r\nP.toNumber = function () {\r\n  var n = +stringify(this, true, true);\r\n  if (this.constructor.strict === true && !this.eq(n.toString())) {\r\n    throw Error(NAME + 'Imprecise conversion');\r\n  }\r\n  return n;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big rounded to sd significant digits using\r\n * rounding mode rm, or Big.RM if rm is not specified.\r\n * Use exponential notation if sd is less than the number of digits necessary to represent\r\n * the integer part of the value in normal notation.\r\n *\r\n * sd {number} Significant digits: integer, 1 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.toPrecision = function (sd, rm) {\r\n  var x = this,\r\n    Big = x.constructor,\r\n    n = x.c[0];\r\n\r\n  if (sd !== UNDEFINED) {\r\n    if (sd !== ~~sd || sd < 1 || sd > MAX_DP) {\r\n      throw Error(INVALID + 'precision');\r\n    }\r\n    x = round(new Big(x), sd, rm);\r\n    for (; x.c.length < sd;) x.c.push(0);\r\n  }\r\n\r\n  return stringify(x, sd <= x.e || x.e <= Big.NE || x.e >= Big.PE, !!n);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big.\r\n * Return exponential notation if this Big has a positive exponent equal to or greater than\r\n * Big.PE, or a negative exponent equal to or less than Big.NE.\r\n * Include the sign for negative zero.\r\n */\r\nP.valueOf = function () {\r\n  var x = this,\r\n    Big = x.constructor;\r\n  if (Big.strict === true) {\r\n    throw Error(NAME + 'valueOf disallowed');\r\n  }\r\n  return stringify(x, x.e <= Big.NE || x.e >= Big.PE, true);\r\n};\r\n\r\n\r\n// Export\r\n\r\n\r\nvar Big = _Big_();\r\n\r\n/// <reference types=\"https://raw.githubusercontent.com/DefinitelyTyped/DefinitelyTyped/master/types/big.js/index.d.ts\" />\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Big);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/big.js/big.mjs\n");

/***/ })

};
;