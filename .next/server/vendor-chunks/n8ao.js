"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/n8ao";
exports.ids = ["vendor-chunks/n8ao"];
exports.modules = {

/***/ "(ssr)/./node_modules/n8ao/dist/N8AO.js":
/*!****************************************!*\
  !*** ./node_modules/n8ao/dist/N8AO.js ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DepthType: () => (/* binding */ $05f6997e4b65da14$export$ed4ee5d1e55474a5),\n/* harmony export */   N8AOPass: () => (/* binding */ $05f6997e4b65da14$export$2d57db20b5eb5e0a),\n/* harmony export */   N8AOPostPass: () => (/* binding */ $87431ee93b037844$export$2489f9981ab0fa82)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var three_examples_jsm_postprocessing_Pass_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three/examples/jsm/postprocessing/Pass.js */ \"(ssr)/./node_modules/three/examples/jsm/postprocessing/Pass.js\");\n/* harmony import */ var postprocessing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! postprocessing */ \"(ssr)/./node_modules/postprocessing/build/index.js\");\n\n\n\n\n\n\n\nclass $e4ca8dcb0218f846$var$FullScreenTriangleGeometry extends three__WEBPACK_IMPORTED_MODULE_0__.BufferGeometry {\n    boundingSphere = new three__WEBPACK_IMPORTED_MODULE_0__.Sphere();\n    constructor(){\n        super();\n        this.setAttribute(\"position\", new three__WEBPACK_IMPORTED_MODULE_0__.BufferAttribute(new Float32Array([\n            -1,\n            -1,\n            3,\n            -1,\n            -1,\n            3\n        ]), 2));\n        this.setAttribute(\"uv\", new three__WEBPACK_IMPORTED_MODULE_0__.BufferAttribute(new Float32Array([\n            0,\n            0,\n            2,\n            0,\n            0,\n            2\n        ]), 2));\n    }\n    computeBoundingSphere() {}\n}\nconst $e4ca8dcb0218f846$var$_geometry = /* @__PURE__ */ new $e4ca8dcb0218f846$var$FullScreenTriangleGeometry();\nconst $e4ca8dcb0218f846$var$_camera = /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.OrthographicCamera();\nclass $e4ca8dcb0218f846$export$dcd670d73db751f5 {\n    constructor(material){\n        this._mesh = new three__WEBPACK_IMPORTED_MODULE_0__.Mesh($e4ca8dcb0218f846$var$_geometry, material);\n        this._mesh.frustumCulled = false;\n    }\n    render(renderer) {\n        renderer.render(this._mesh, $e4ca8dcb0218f846$var$_camera);\n    }\n    get material() {\n        return this._mesh.material;\n    }\n    set material(value) {\n        this._mesh.material = value;\n    }\n    dispose() {\n        this._mesh.material.dispose();\n        this._mesh.geometry.dispose();\n    }\n}\n\n\n\nconst $1ed45968c1160c3c$export$c9b263b9a17dffd7 = {\n    uniforms: {\n        \"sceneDiffuse\": {\n            value: null\n        },\n        \"sceneDepth\": {\n            value: null\n        },\n        \"sceneNormal\": {\n            value: null\n        },\n        \"projMat\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4()\n        },\n        \"viewMat\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4()\n        },\n        \"projViewMat\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4()\n        },\n        \"projectionMatrixInv\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4()\n        },\n        \"viewMatrixInv\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4()\n        },\n        \"cameraPos\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Vector3()\n        },\n        \"resolution\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Vector2()\n        },\n        \"biasAdjustment\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Vector2()\n        },\n        \"time\": {\n            value: 0.0\n        },\n        \"samples\": {\n            value: []\n        },\n        \"bluenoise\": {\n            value: null\n        },\n        \"distanceFalloff\": {\n            value: 1.0\n        },\n        \"radius\": {\n            value: 5.0\n        },\n        \"near\": {\n            value: 0.1\n        },\n        \"far\": {\n            value: 1000.0\n        },\n        \"ortho\": {\n            value: false\n        },\n        \"screenSpaceRadius\": {\n            value: false\n        },\n        \"frame\": {\n            value: 0.0\n        }\n    },\n    depthWrite: false,\n    depthTest: false,\n    vertexShader: /* glsl */ `\nvarying vec2 vUv;\nvoid main() {\n  vUv = uv;\n  gl_Position = vec4(position, 1);\n}`,\n    fragmentShader: /* glsl */ `\n    #define SAMPLES 16\n    #define FSAMPLES 16.0\nuniform sampler2D sceneDiffuse;\nuniform highp sampler2D sceneNormal;\nuniform highp sampler2D sceneDepth;\nuniform mat4 projectionMatrixInv;\nuniform mat4 viewMatrixInv;\nuniform mat4 projMat;\nuniform mat4 viewMat;\nuniform mat4 projViewMat;\nuniform vec3 cameraPos;\nuniform vec2 resolution;\nuniform vec2 biasAdjustment;\nuniform float time;\nuniform vec3[SAMPLES] samples;\nuniform float radius;\nuniform float distanceFalloff;\nuniform float near;\nuniform float far;\nuniform float frame;\nuniform bool ortho;\nuniform bool screenSpaceRadius;\nuniform sampler2D bluenoise;\n    varying vec2 vUv;\n    highp float linearize_depth(highp float d, highp float zNear,highp float zFar)\n    {\n        return (zFar * zNear) / (zFar - d * (zFar - zNear));\n    }\n    highp float linearize_depth_ortho(highp float d, highp float nearZ, highp float farZ) {\n      return nearZ + (farZ - nearZ) * d;\n    }\n    highp float linearize_depth_log(highp float d, highp float nearZ,highp float farZ) {\n      float depth = pow(2.0, d * log2(farZ + 1.0)) - 1.0;\n      float a = farZ / (farZ - nearZ);\n      float b = farZ * nearZ / (nearZ - farZ);\n      float linDepth = a + b / depth;\n      /*return ortho ? linearize_depth_ortho(\n        linDepth,\n        nearZ,\n        farZ\n      ) :linearize_depth(linDepth, nearZ, farZ);*/\n       #ifdef ORTHO\n\n       return linearize_depth_ortho(d, nearZ, farZ);\n\n        #else\n        return linearize_depth(linDepth, nearZ, farZ);\n        #endif\n    }\n\n    vec3 getWorldPosLog(vec3 posS) {\n      vec2 uv = posS.xy;\n      float z = posS.z;\n      float nearZ =near;\n      float farZ = far;\n      float depth = pow(2.0, z * log2(farZ + 1.0)) - 1.0;\n      float a = farZ / (farZ - nearZ);\n      float b = farZ * nearZ / (nearZ - farZ);\n      float linDepth = a + b / depth;\n      vec4 clipVec = vec4(uv, linDepth, 1.0) * 2.0 - 1.0;\n      vec4 wpos = projectionMatrixInv * clipVec;\n      return wpos.xyz / wpos.w;\n    }\n    vec3 getWorldPos(float depth, vec2 coord) {\n      #ifdef LOGDEPTH\n        #ifndef ORTHO\n          return getWorldPosLog(vec3(coord, depth));\n        #endif\n      #endif\n      float z = depth * 2.0 - 1.0;\n      vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n      vec4 viewSpacePosition = projectionMatrixInv * clipSpacePosition;\n      // Perspective division\n     vec4 worldSpacePosition = viewSpacePosition;\n     worldSpacePosition.xyz /= worldSpacePosition.w;\n      return worldSpacePosition.xyz;\n  }\n\n  vec3 computeNormal(vec3 worldPos, vec2 vUv) {\n    ivec2 p = ivec2(vUv * resolution);\n    #ifdef REVERSEDEPTH\n    float c0 = 1.0 - texelFetch(sceneDepth, p, 0).x;\n    float l2 = 1.0 - texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n    float l1 = 1.0 - texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n    float r1 = 1.0 - texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n    float r2 = 1.0 - texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n    float b2 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n    float b1 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n    float t1 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n    float t2 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n    #else\n    float c0 = texelFetch(sceneDepth, p, 0).x;\n    float l2 = texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n    float l1 = texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n    float r1 = texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n    float r2 = texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n    float b2 = texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n    float b1 = texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n    float t1 = texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n    float t2 = texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n    #endif\n\n    float dl = abs((2.0 * l1 - l2) - c0);\n    float dr = abs((2.0 * r1 - r2) - c0);\n    float db = abs((2.0 * b1 - b2) - c0);\n    float dt = abs((2.0 * t1 - t2) - c0);\n\n    vec3 ce = getWorldPos(c0, vUv).xyz;\n\n    vec3 dpdx = (dl < dr) ? ce - getWorldPos(l1, (vUv - vec2(1.0 / resolution.x, 0.0))).xyz\n                          : -ce + getWorldPos(r1, (vUv + vec2(1.0 / resolution.x, 0.0))).xyz;\n    vec3 dpdy = (db < dt) ? ce - getWorldPos(b1, (vUv - vec2(0.0, 1.0 / resolution.y))).xyz\n                          : -ce + getWorldPos(t1, (vUv + vec2(0.0, 1.0 / resolution.y))).xyz;\n\n    return normalize(cross(dpdx, dpdy));\n}\n\nmat3 makeRotationZ(float theta) {\n\tfloat c = cos(theta);\n\tfloat s = sin(theta);\n\treturn mat3(c, - s, 0,\n\t\t\ts,  c, 0,\n\t\t\t0,  0, 1);\n  }\n\nvoid main() {\n      vec4 diffuse = texture2D(sceneDiffuse, vUv);\n      #ifdef REVERSEDEPTH\n      float depth = 1.0 - texture2D(sceneDepth, vUv).x;\n      #else\n      float depth = texture2D(sceneDepth, vUv).x;\n      #endif\n      if (depth == 1.0) {\n        gl_FragColor = vec4(vec3(1.0), 1.0);\n        return;\n      }\n      vec3 worldPos = getWorldPos(depth, vUv);\n      #ifdef HALFRES\n        vec3 normal = texture2D(sceneNormal, vUv).rgb;\n      #else\n        vec3 normal = computeNormal(worldPos, vUv);\n      #endif\n      vec4 noise = texture2D(bluenoise, gl_FragCoord.xy / 128.0);\n      vec2 harmoniousNumbers = vec2(\n        1.618033988749895,\n        1.324717957244746\n      );\n      noise.rg += harmoniousNumbers * frame;\n      noise.rg = fract(noise.rg);\n        vec3 helperVec = vec3(0.0, 1.0, 0.0);\n        if (dot(helperVec, normal) > 0.99) {\n          helperVec = vec3(1.0, 0.0, 0.0);\n        }\n        vec3 tangent = normalize(cross(helperVec, normal));\n        vec3 bitangent = cross(normal, tangent);\n        mediump mat3 tbn = mat3(tangent, bitangent, normal) *  makeRotationZ( noise.r * 3.1415962 * 2.0) ;\n\n      mediump float occluded = 0.0;\n      mediump float totalWeight = 0.0;\n      float radiusToUse = screenSpaceRadius ? distance(\n        worldPos,\n        getWorldPos(depth, vUv +\n          vec2(radius, 0.0) / resolution)\n      ) : radius;\n      float distanceFalloffToUse =screenSpaceRadius ?\n          radiusToUse * distanceFalloff\n      : radiusToUse * distanceFalloff * 0.2;\n      float bias = (min(\n        0.1,\n        distanceFalloffToUse * 0.1\n      ) / near) * fwidth(distance(worldPos, cameraPos)) / radiusToUse;\n      bias = biasAdjustment.x + biasAdjustment.y * bias;\n      mediump float offsetMove = noise.g;\n      mediump float offsetMoveInv = 1.0 / FSAMPLES;\n      float farTimesNear = far * near;\n      float farMinusNear = far - near;\n      \n      for(int i = 0; i < SAMPLES; i++) {\n        mediump vec3 sampleDirection = tbn * samples[i];\n\n        float moveAmt = fract(offsetMove);\n        offsetMove += offsetMoveInv;\n        vec3 samplePos = worldPos + radiusToUse * moveAmt * sampleDirection;\n        vec4 offset = projMat * vec4(samplePos, 1.0);\n        offset.xyz /= offset.w;\n        offset.xyz = offset.xyz * 0.5 + 0.5;\n        \n        if (all(greaterThan(offset.xyz * (1.0 - offset.xyz), vec3(0.0)))) {\n          #ifdef REVERSEDEPTH\n          float sampleDepth = 1.0 - textureLod(sceneDepth, offset.xy, 0.0).x;\n          #else\n          float sampleDepth = textureLod(sceneDepth, offset.xy, 0.0).x;\n          #endif\n\n          /*#ifdef LOGDEPTH\n          float distSample = linearize_depth_log(sampleDepth, near, far);\n      #else\n          #ifdef ORTHO\n              float distSample = near + farMinusNear * sampleDepth;\n          #else\n              float distSample = (farTimesNear) / (far - sampleDepth * farMinusNear);\n          #endif\n      #endif*/\n      #ifdef ORTHO\n          float distSample = near + sampleDepth * farMinusNear;\n      #else\n          #ifdef LOGDEPTH\n              float distSample = linearize_depth_log(sampleDepth, near, far);\n          #else\n              float distSample = (farTimesNear) / (far - sampleDepth * farMinusNear);\n          #endif\n      #endif\n      \n      #ifdef ORTHO\n          float distWorld = near + offset.z * farMinusNear;\n      #else\n          float distWorld = (farTimesNear) / (far - offset.z * farMinusNear);\n      #endif\n          \n          mediump float rangeCheck = smoothstep(0.0, 1.0, distanceFalloffToUse / (abs(distSample - distWorld)));\n          vec2 diff = gl_FragCoord.xy - floor(offset.xy * resolution);\n          occluded += rangeCheck * float(distSample != distWorld) * float(sampleDepth != depth) * step(distSample + bias, distWorld) * step(\n            1.0,\n            dot(diff, diff)\n          );\n          \n          totalWeight ++;\n        }\n      }\n      float occ = clamp(1.0 - occluded / (totalWeight == 0.0 ? 1.0 : totalWeight), 0.0, 1.0);\n      gl_FragColor = vec4(occ, 0.5 + 0.5 * normal);\n}`\n};\n\n\n\nconst $12b21d24d1192a04$export$a815acccbd2c9a49 = {\n    uniforms: {\n        \"sceneDiffuse\": {\n            value: null\n        },\n        \"sceneDepth\": {\n            value: null\n        },\n        \"tDiffuse\": {\n            value: null\n        },\n        \"transparencyDWFalse\": {\n            value: null\n        },\n        \"transparencyDWTrue\": {\n            value: null\n        },\n        \"transparencyDWTrueDepth\": {\n            value: null\n        },\n        \"transparencyAware\": {\n            value: false\n        },\n        \"projMat\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4()\n        },\n        \"viewMat\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4()\n        },\n        \"projectionMatrixInv\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4()\n        },\n        \"viewMatrixInv\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4()\n        },\n        \"cameraPos\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Vector3()\n        },\n        \"resolution\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Vector2()\n        },\n        \"color\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Vector3(0, 0, 0)\n        },\n        \"blueNoise\": {\n            value: null\n        },\n        \"downsampledDepth\": {\n            value: null\n        },\n        \"time\": {\n            value: 0.0\n        },\n        \"intensity\": {\n            value: 10.0\n        },\n        \"renderMode\": {\n            value: 0.0\n        },\n        \"gammaCorrection\": {\n            value: false\n        },\n        \"ortho\": {\n            value: false\n        },\n        \"near\": {\n            value: 0.1\n        },\n        \"far\": {\n            value: 1000.0\n        },\n        \"screenSpaceRadius\": {\n            value: false\n        },\n        \"radius\": {\n            value: 0.0\n        },\n        \"distanceFalloff\": {\n            value: 1.0\n        },\n        \"fog\": {\n            value: false\n        },\n        \"fogExp\": {\n            value: false\n        },\n        \"fogDensity\": {\n            value: 0.0\n        },\n        \"fogNear\": {\n            value: Infinity\n        },\n        \"fogFar\": {\n            value: Infinity\n        },\n        \"colorMultiply\": {\n            value: true\n        },\n        \"aoTones\": {\n            value: 0.0\n        }\n    },\n    depthWrite: false,\n    depthTest: false,\n    vertexShader: /* glsl */ `\n\t\tvarying vec2 vUv;\n\t\tvoid main() {\n\t\t\tvUv = uv;\n\t\t\tgl_Position = vec4(position, 1);\n\t\t}`,\n    fragmentShader: /* glsl */ `\n\t\tuniform sampler2D sceneDiffuse;\n    uniform highp sampler2D sceneDepth;\n    uniform highp sampler2D downsampledDepth;\n    uniform highp sampler2D transparencyDWFalse;\n    uniform highp sampler2D transparencyDWTrue;\n    uniform highp sampler2D transparencyDWTrueDepth;\n    uniform sampler2D tDiffuse;\n    uniform sampler2D blueNoise;\n    uniform vec2 resolution;\n    uniform vec3 color;\n    uniform mat4 projectionMatrixInv;\n    uniform mat4 viewMatrixInv;\n    uniform float intensity;\n    uniform float renderMode;\n    uniform float near;\n    uniform float far;\n    uniform float aoTones;\n    uniform bool gammaCorrection;\n    uniform bool ortho;\n    uniform bool screenSpaceRadius;\n    uniform bool fog;\n    uniform bool fogExp;\n    uniform bool colorMultiply;\n    uniform bool transparencyAware;\n    uniform float fogDensity;\n    uniform float fogNear;\n    uniform float fogFar;\n    uniform float radius;\n    uniform float distanceFalloff;\n    uniform vec3 cameraPos;\n    varying vec2 vUv;\n    highp float linearize_depth(highp float d, highp float zNear,highp float zFar)\n    {\n        return (zFar * zNear) / (zFar - d * (zFar - zNear));\n    }\n    highp float linearize_depth_ortho(highp float d, highp float nearZ, highp float farZ) {\n      return nearZ + (farZ - nearZ) * d;\n    }\n    highp float linearize_depth_log(highp float d, highp float nearZ,highp float farZ) {\n      float depth = pow(2.0, d * log2(farZ + 1.0)) - 1.0;\n      float a = farZ / (farZ - nearZ);\n      float b = farZ * nearZ / (nearZ - farZ);\n      float linDepth = a + b / depth;\n      return ortho ? linearize_depth_ortho(\n        linDepth,\n        nearZ,\n        farZ\n      ) :linearize_depth(linDepth, nearZ, farZ);\n    }\n    vec3 getWorldPosLog(vec3 posS) {\n        vec2 uv = posS.xy;\n        float z = posS.z;\n        float nearZ =near;\n        float farZ = far;\n        float depth = pow(2.0, z * log2(farZ + 1.0)) - 1.0;\n        float a = farZ / (farZ - nearZ);\n        float b = farZ * nearZ / (nearZ - farZ);\n        float linDepth = a + b / depth;\n        vec4 clipVec = vec4(uv, linDepth, 1.0) * 2.0 - 1.0;\n        vec4 wpos = projectionMatrixInv * clipVec;\n        return wpos.xyz / wpos.w;\n      }\n      vec3 getWorldPos(float depth, vec2 coord) {\n        #ifdef LOGDEPTH\n          #ifndef ORTHO\n            return getWorldPosLog(vec3(coord, depth));\n          #endif\n        #endif\n      //  }\n        float z = depth * 2.0 - 1.0;\n        vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n        vec4 viewSpacePosition = projectionMatrixInv * clipSpacePosition;\n        // Perspective division\n       vec4 worldSpacePosition = viewSpacePosition;\n       worldSpacePosition.xyz /= worldSpacePosition.w;\n        return worldSpacePosition.xyz;\n    }\n  \n    vec3 computeNormal(vec3 worldPos, vec2 vUv) {\n      ivec2 p = ivec2(vUv * resolution);\n      #ifdef REVERSEDEPTH\n      float c0 = 1.0 - texelFetch(sceneDepth, p, 0).x;\n      float l2 = 1.0 - texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n      float l1 = 1.0 - texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n      float r1 = 1.0 - texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n      float r2 = 1.0 - texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n      float b2 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n      float b1 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n      float t1 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n      float t2 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n      #else\n      float c0 = texelFetch(sceneDepth, p, 0).x;\n      float l2 = texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n      float l1 = texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n      float r1 = texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n      float r2 = texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n      float b2 = texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n      float b1 = texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n      float t1 = texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n      float t2 = texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n      #endif\n  \n      float dl = abs((2.0 * l1 - l2) - c0);\n      float dr = abs((2.0 * r1 - r2) - c0);\n      float db = abs((2.0 * b1 - b2) - c0);\n      float dt = abs((2.0 * t1 - t2) - c0);\n  \n      vec3 ce = getWorldPos(c0, vUv).xyz;\n  \n      vec3 dpdx = (dl < dr) ? ce - getWorldPos(l1, (vUv - vec2(1.0 / resolution.x, 0.0))).xyz\n                            : -ce + getWorldPos(r1, (vUv + vec2(1.0 / resolution.x, 0.0))).xyz;\n      vec3 dpdy = (db < dt) ? ce - getWorldPos(b1, (vUv - vec2(0.0, 1.0 / resolution.y))).xyz\n                            : -ce + getWorldPos(t1, (vUv + vec2(0.0, 1.0 / resolution.y))).xyz;\n  \n      return normalize(cross(dpdx, dpdy));\n  }\n\n    #include <common>\n    #include <dithering_pars_fragment>\n    void main() {\n        //vec4 texel = texture2D(tDiffuse, vUv);//vec3(0.0);\n        vec4 sceneTexel = texture2D(sceneDiffuse, vUv);\n        #ifdef REVERSEDEPTH\n        float depth = 1.0 - texture2D(sceneDepth, vUv).x;\n        #else\n        float depth = texture2D(sceneDepth, vUv).x;\n        #endif\n        #ifdef HALFRES \n        vec4 texel;\n        if (depth == 1.0) {\n            texel = vec4(0.0, 0.0, 0.0, 1.0);\n        } else {\n        vec3 worldPos = getWorldPos(depth, vUv);\n        vec3 normal = computeNormal(getWorldPos(depth, vUv), vUv);\n       // vec4 texel = texture2D(tDiffuse, vUv);\n       // Find closest depth;\n       float totalWeight = 0.0;\n       float radiusToUse = screenSpaceRadius ? distance(\n        worldPos,\n        getWorldPos(depth, vUv +\n          vec2(radius, 0.0) / resolution)\n      ) : radius;\n      float distanceFalloffToUse =screenSpaceRadius ?\n          radiusToUse * distanceFalloff\n      : distanceFalloff;\n        for(float x = -1.0; x <= 1.0; x++) {\n            for(float y = -1.0; y <= 1.0; y++) {\n                vec2 offset = vec2(x, y);\n                ivec2 p = ivec2(\n                    (vUv * resolution * 0.5) + offset\n                );\n                vec2 pUv = vec2(p) / (resolution * 0.5);\n                float sampleDepth = texelFetch(downsampledDepth,p, 0).x;\n                vec4 sampleInfo = texelFetch(tDiffuse, p, 0);\n                vec3 normalSample = sampleInfo.gba * 2.0 - 1.0;\n                vec3 worldPosSample = getWorldPos(sampleDepth, pUv);\n                float tangentPlaneDist = abs(dot(worldPosSample - worldPos, normal));\n                float rangeCheck = exp(-1.0 * tangentPlaneDist * (1.0 / distanceFalloffToUse)) * max(dot(normal, normalSample), 0.0);\n                float weight = rangeCheck;\n                totalWeight += weight;\n                texel += sampleInfo * weight;\n            }\n        }\n        if (totalWeight == 0.0) {\n            texel = texture2D(tDiffuse, vUv);\n        } else {\n            texel /= totalWeight;\n        }\n    }\n        #else\n        vec4 texel = texture2D(tDiffuse, vUv);\n        #endif\n\n        #ifdef LOGDEPTH\n        texel.r = clamp(texel.r, 0.0, 1.0);\n        if (texel.r == 0.0) {\n          texel.r = 1.0;\n        }\n        #endif\n     \n        float finalAo = pow(texel.r, intensity);\n        if (aoTones > 0.0) {\n            finalAo = ceil(finalAo * aoTones) / aoTones;\n        }\n        float fogFactor;\n        float fogDepth = distance(\n            cameraPos,\n            getWorldPos(depth, vUv)\n        );\n        if (fog) {\n            if (fogExp) {\n                fogFactor = 1.0 - exp( - fogDensity * fogDensity * fogDepth * fogDepth );\n            } else {\n                fogFactor = smoothstep( fogNear, fogFar, fogDepth );\n            }\n        }\n        if (transparencyAware) {\n            float transparencyDWOff = texture2D(transparencyDWFalse, vUv).a;\n            float transparencyDWOn = texture2D(transparencyDWTrue, vUv).a;\n            float adjustmentFactorOff = transparencyDWOff;\n            #ifdef REVERSEDEPTH\n            float depthSample = 1.0 - texture2D(sceneDepth, vUv).r;\n            float trueDepthSample = 1.0 - texture2D(transparencyDWTrueDepth, vUv).r;\n            #else\n            float depthSample = texture2D(sceneDepth, vUv).r;\n            float trueDepthSample = texture2D(transparencyDWTrueDepth, vUv).r;\n            #endif\n            float adjustmentFactorOn = (1.0 - transparencyDWOn) * (\n                trueDepthSample == depthSample ? 1.0 : 0.0\n            );\n            float adjustmentFactor = max(adjustmentFactorOff, adjustmentFactorOn);\n            finalAo = mix(finalAo, 1.0, adjustmentFactor);\n        }\n        finalAo = mix(finalAo, 1.0, fogFactor);\n        vec3 aoApplied = color * mix(vec3(1.0), sceneTexel.rgb, float(colorMultiply));\n        if (renderMode == 0.0) {\n            gl_FragColor = vec4( mix(sceneTexel.rgb, aoApplied, 1.0 - finalAo), sceneTexel.a);\n        } else if (renderMode == 1.0) {\n            gl_FragColor = vec4( mix(vec3(1.0), aoApplied, 1.0 - finalAo), sceneTexel.a);\n        } else if (renderMode == 2.0) {\n            gl_FragColor = vec4( sceneTexel.rgb, sceneTexel.a);\n        } else if (renderMode == 3.0) {\n            if (vUv.x < 0.5) {\n                gl_FragColor = vec4( sceneTexel.rgb, sceneTexel.a);\n            } else if (abs(vUv.x - 0.5) < 1.0 / resolution.x) {\n                gl_FragColor = vec4(1.0);\n            } else {\n                gl_FragColor = vec4( mix(sceneTexel.rgb, aoApplied, 1.0 - finalAo), sceneTexel.a);\n            }\n        } else if (renderMode == 4.0) {\n            if (vUv.x < 0.5) {\n                gl_FragColor = vec4( sceneTexel.rgb, sceneTexel.a);\n            } else if (abs(vUv.x - 0.5) < 1.0 / resolution.x) {\n                gl_FragColor = vec4(1.0);\n            } else {\n                gl_FragColor = vec4( mix(vec3(1.0), aoApplied, 1.0 - finalAo), sceneTexel.a);\n            }\n        }\n        #include <dithering_fragment>\n        if (gammaCorrection) {\n            gl_FragColor = sRGBTransferOETF(gl_FragColor);\n        }\n    }\n    `\n};\n\n\n\nconst $e52378cd0f5a973d$export$57856b59f317262e = {\n    uniforms: {\n        \"sceneDiffuse\": {\n            value: null\n        },\n        \"sceneDepth\": {\n            value: null\n        },\n        \"tDiffuse\": {\n            value: null\n        },\n        \"projMat\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4()\n        },\n        \"viewMat\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4()\n        },\n        \"projectionMatrixInv\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4()\n        },\n        \"viewMatrixInv\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4()\n        },\n        \"cameraPos\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Vector3()\n        },\n        \"resolution\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Vector2()\n        },\n        \"time\": {\n            value: 0.0\n        },\n        \"r\": {\n            value: 5.0\n        },\n        \"blueNoise\": {\n            value: null\n        },\n        \"radius\": {\n            value: 12.0\n        },\n        \"worldRadius\": {\n            value: 5.0\n        },\n        \"index\": {\n            value: 0.0\n        },\n        \"poissonDisk\": {\n            value: []\n        },\n        \"distanceFalloff\": {\n            value: 1.0\n        },\n        \"near\": {\n            value: 0.1\n        },\n        \"far\": {\n            value: 1000.0\n        },\n        \"screenSpaceRadius\": {\n            value: false\n        }\n    },\n    depthWrite: false,\n    depthTest: false,\n    vertexShader: /* glsl */ `\n\t\tvarying vec2 vUv;\n\t\tvoid main() {\n\t\t\tvUv = uv;\n\t\t\tgl_Position = vec4(position, 1.0);\n\t\t}`,\n    fragmentShader: /* glsl */ `\n\t\tuniform sampler2D sceneDiffuse;\n    uniform highp sampler2D sceneDepth;\n    uniform sampler2D tDiffuse;\n    uniform sampler2D blueNoise;\n    uniform mat4 projectionMatrixInv;\n    uniform mat4 viewMatrixInv;\n    uniform vec2 resolution;\n    uniform float r;\n    uniform float radius;\n     uniform float worldRadius;\n    uniform float index;\n     uniform float near;\n     uniform float far;\n     uniform float distanceFalloff;\n     uniform bool screenSpaceRadius;\n    varying vec2 vUv;\n\n    highp float linearize_depth(highp float d, highp float zNear,highp float zFar)\n    {\n        highp float z_n = 2.0 * d - 1.0;\n        return 2.0 * zNear * zFar / (zFar + zNear - z_n * (zFar - zNear));\n    }\n    highp float linearize_depth_log(highp float d, highp float nearZ,highp float farZ) {\n     float depth = pow(2.0, d * log2(farZ + 1.0)) - 1.0;\n     float a = farZ / (farZ - nearZ);\n     float b = farZ * nearZ / (nearZ - farZ);\n     float linDepth = a + b / depth;\n     return linearize_depth(linDepth, nearZ, farZ);\n   }\n   highp float linearize_depth_ortho(highp float d, highp float nearZ, highp float farZ) {\n     return nearZ + (farZ - nearZ) * d;\n   }\n   vec3 getWorldPosLog(vec3 posS) {\n     vec2 uv = posS.xy;\n     float z = posS.z;\n     float nearZ =near;\n     float farZ = far;\n     float depth = pow(2.0, z * log2(farZ + 1.0)) - 1.0;\n     float a = farZ / (farZ - nearZ);\n     float b = farZ * nearZ / (nearZ - farZ);\n     float linDepth = a + b / depth;\n     vec4 clipVec = vec4(uv, linDepth, 1.0) * 2.0 - 1.0;\n     vec4 wpos = projectionMatrixInv * clipVec;\n     return wpos.xyz / wpos.w;\n   }\n    vec3 getWorldPos(float depth, vec2 coord) {\n     #ifdef LOGDEPTH\n      #ifndef ORTHO\n          return getWorldPosLog(vec3(coord, depth));\n      #endif\n     #endif\n        \n        float z = depth * 2.0 - 1.0;\n        vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n        vec4 viewSpacePosition = projectionMatrixInv * clipSpacePosition;\n        // Perspective division\n       vec4 worldSpacePosition = viewSpacePosition;\n       worldSpacePosition.xyz /= worldSpacePosition.w;\n        return worldSpacePosition.xyz;\n    }\n    #include <common>\n    #define NUM_SAMPLES 16\n    uniform vec2 poissonDisk[NUM_SAMPLES];\n    void main() {\n        const float pi = 3.14159;\n        vec2 texelSize = vec2(1.0 / resolution.x, 1.0 / resolution.y);\n        vec2 uv = vUv;\n        vec4 data = texture2D(tDiffuse, vUv);\n        float occlusion = data.r;\n        float baseOcc = data.r;\n        vec3 normal = data.gba * 2.0 - 1.0;\n        float count = 1.0;\n        float d = texture2D(sceneDepth, vUv).x;\n        if (d == 1.0) {\n          gl_FragColor = data;\n          return;\n        }\n        vec3 worldPos = getWorldPos(d, vUv);\n        float size = radius;\n        float angle;\n        if (index == 0.0) {\n             angle = texture2D(blueNoise, gl_FragCoord.xy / 128.0).w * PI2;\n        } else if (index == 1.0) {\n             angle = texture2D(blueNoise, gl_FragCoord.xy / 128.0).z * PI2;\n        } else if (index == 2.0) {\n             angle = texture2D(blueNoise, gl_FragCoord.xy / 128.0).y * PI2;\n        } else {\n             angle = texture2D(blueNoise, gl_FragCoord.xy / 128.0).x * PI2;\n        }\n\n        mat2 rotationMatrix = mat2(cos(angle), -sin(angle), sin(angle), cos(angle));\n        float radiusToUse = screenSpaceRadius ? distance(\n          worldPos,\n          getWorldPos(d, vUv +\n            vec2(worldRadius, 0.0) / resolution)\n        ) : worldRadius;\n        float distanceFalloffToUse =screenSpaceRadius ?\n        radiusToUse * distanceFalloff\n    : radiusToUse * distanceFalloff * 0.2;\n\n        float invDistance = (1.0 / distanceFalloffToUse);\n        for(int i = 0; i < NUM_SAMPLES; i++) {\n            vec2 offset = (rotationMatrix * poissonDisk[i]) * texelSize * size;\n            vec4 dataSample = texture2D(tDiffuse, uv + offset);\n            float occSample = dataSample.r;\n            vec3 normalSample = dataSample.gba * 2.0 - 1.0;\n            float dSample = texture2D(sceneDepth, uv + offset).x;\n            vec3 worldPosSample = getWorldPos(dSample, uv + offset);\n            float tangentPlaneDist = abs(dot(worldPosSample - worldPos, normal));\n            float rangeCheck = float(dSample != 1.0) * exp(-1.0 * tangentPlaneDist * invDistance ) * max(dot(normal, normalSample), 0.0);\n            occlusion += occSample * rangeCheck;\n            count += rangeCheck;\n        }\n        if (count > 0.0) {\n          occlusion /= count;\n        }\n        occlusion = clamp(occlusion, 0.0, 1.0);\n        if (occlusion == 0.0) {\n          occlusion = 1.0;\n        }\n        gl_FragColor = vec4(occlusion, 0.5 + 0.5 * normal);\n    }\n    `\n};\n\n\n\nconst $26aca173e0984d99$export$1efdf491687cd442 = {\n    uniforms: {\n        \"sceneDepth\": {\n            value: null\n        },\n        \"resolution\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Vector2()\n        },\n        \"near\": {\n            value: 0.1\n        },\n        \"far\": {\n            value: 1000.0\n        },\n        \"viewMatrixInv\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4()\n        },\n        \"projectionMatrixInv\": {\n            value: /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4()\n        },\n        \"logDepth\": {\n            value: false\n        },\n        \"ortho\": {\n            value: false\n        }\n    },\n    depthWrite: false,\n    depthTest: false,\n    vertexShader: /* glsl */ `\n    varying vec2 vUv;\n    void main() {\n        vUv = uv;\n        gl_Position = vec4(position, 1);\n    }`,\n    fragmentShader: /* glsl */ `\n    uniform highp sampler2D sceneDepth;\n    uniform vec2 resolution;\n    uniform float near;\n    uniform float far;\n    uniform bool logDepth;\n    uniform bool ortho;\n    uniform mat4 viewMatrixInv;\n    uniform mat4 projectionMatrixInv;\n    varying vec2 vUv;\n    layout(location = 1) out vec4 gNormal;\n    vec3 getWorldPosLog(vec3 posS) {\n        vec2 uv = posS.xy;\n        float z = posS.z;\n        float nearZ =near;\n        float farZ = far;\n        float depth = pow(2.0, z * log2(farZ + 1.0)) - 1.0;\n        float a = farZ / (farZ - nearZ);\n        float b = farZ * nearZ / (nearZ - farZ);\n        float linDepth = a + b / depth;\n        vec4 clipVec = vec4(uv, linDepth, 1.0) * 2.0 - 1.0;\n        vec4 wpos = projectionMatrixInv * clipVec;\n        return wpos.xyz / wpos.w;\n      }\n      vec3 getWorldPos(float depth, vec2 coord) {\n        if (logDepth && !ortho) {\n          return getWorldPosLog(vec3(coord, depth));\n        }\n        float z = depth * 2.0 - 1.0;\n        vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n        vec4 viewSpacePosition = projectionMatrixInv * clipSpacePosition;\n        // Perspective division\n       vec4 worldSpacePosition = viewSpacePosition;\n       worldSpacePosition.xyz /= worldSpacePosition.w;\n        return worldSpacePosition.xyz;\n    }\n  \n    vec3 computeNormal(vec3 worldPos, vec2 vUv) {\n      ivec2 p = ivec2(vUv * resolution);\n      #ifdef REVERSEDEPTH\n      float c0 = 1.0 - texelFetch(sceneDepth, p, 0).x;\n      float l2 = 1.0 - texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n      float l1 = 1.0 - texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n      float r1 = 1.0 - texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n      float r2 = 1.0 - texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n      float b2 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n      float b1 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n      float t1 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n      float t2 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n      #else\n      float c0 = texelFetch(sceneDepth, p, 0).x;\n      float l2 = texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n      float l1 = texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n      float r1 = texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n      float r2 = texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n      float b2 = texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n      float b1 = texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n      float t1 = texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n      float t2 = texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n      #endif\n  \n      float dl = abs((2.0 * l1 - l2) - c0);\n      float dr = abs((2.0 * r1 - r2) - c0);\n      float db = abs((2.0 * b1 - b2) - c0);\n      float dt = abs((2.0 * t1 - t2) - c0);\n  \n      vec3 ce = getWorldPos(c0, vUv).xyz;\n  \n      vec3 dpdx = (dl < dr) ? ce - getWorldPos(l1, (vUv - vec2(1.0 / resolution.x, 0.0))).xyz\n                            : -ce + getWorldPos(r1, (vUv + vec2(1.0 / resolution.x, 0.0))).xyz;\n      vec3 dpdy = (db < dt) ? ce - getWorldPos(b1, (vUv - vec2(0.0, 1.0 / resolution.y))).xyz\n                            : -ce + getWorldPos(t1, (vUv + vec2(0.0, 1.0 / resolution.y))).xyz;\n  \n      return normalize(cross(dpdx, dpdy));\n  }\n    void main() {\n        vec2 uv = vUv - vec2(0.5) / resolution;\n        vec2 pixelSize = vec2(1.0) / resolution;\n        highp vec2[4] uvSamples;\n        uvSamples[0] = uv;\n        uvSamples[1] = uv + vec2(pixelSize.x, 0.0);\n        uvSamples[2] = uv + vec2(0.0, pixelSize.y);\n        uvSamples[3] = uv + pixelSize;\n        #ifdef REVERSEDEPTH\n        float depth00 = 1.0 - texture2D(sceneDepth, uvSamples[0]).r;\n        float depth10 = 1.0 - texture2D(sceneDepth, uvSamples[1]).r;\n        float depth01 = 1.0 - texture2D(sceneDepth, uvSamples[2]).r;\n        float depth11 = 1.0 - texture2D(sceneDepth, uvSamples[3]).r;\n        #else\n        float depth00 = texture2D(sceneDepth, uvSamples[0]).r;\n        float depth10 = texture2D(sceneDepth, uvSamples[1]).r;\n        float depth01 = texture2D(sceneDepth, uvSamples[2]).r;\n        float depth11 = texture2D(sceneDepth, uvSamples[3]).r;\n        #endif\n        float minDepth = min(min(depth00, depth10), min(depth01, depth11));\n        float maxDepth = max(max(depth00, depth10), max(depth01, depth11));\n        float targetDepth = minDepth;\n        // Checkerboard pattern to avoid artifacts\n        if (mod(gl_FragCoord.x + gl_FragCoord.y, 2.0) > 0.5) { \n            targetDepth = maxDepth;\n        }\n        int chosenIndex = 0;\n        float[4] samples;\n        samples[0] = depth00;\n        samples[1] = depth10;\n        samples[2] = depth01;\n        samples[3] = depth11;\n        for(int i = 0; i < 4; ++i) {\n            if (samples[i] == targetDepth) {\n                chosenIndex = i;\n                break;\n            }\n        }\n        gl_FragColor = vec4(samples[chosenIndex], 0.0, 0.0, 1.0);\n        gNormal = vec4(computeNormal(\n            getWorldPos(samples[chosenIndex], uvSamples[chosenIndex]), uvSamples[chosenIndex]\n        ), 0.0);\n    }`\n};\n\n\n\n\n\n\n\n\n\nconst $06269ad78f3c5fdf$var$BlueNoise = `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`;\nconst $06269ad78f3c5fdf$var$bluenoiseBits = /* @__PURE__ */ (()=>Uint8Array.from(atob($06269ad78f3c5fdf$var$BlueNoise), (c)=>c.charCodeAt(0)))();\nvar $06269ad78f3c5fdf$export$2e2bcd8739ae039 = $06269ad78f3c5fdf$var$bluenoiseBits;\n\n\n\n\nconst $ff9437d9c7577f11$var$version = /* @__PURE__ */ (()=>parseInt(three__WEBPACK_IMPORTED_MODULE_0__.REVISION.replace(/\\D+/g, \"\")))();\nconst $ff9437d9c7577f11$export$156f6a58f569aa09 = $ff9437d9c7577f11$var$version >= 162 ? class extends three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget {\n    constructor(width = 1, height = 1, count = 1, options = {}){\n        super(width, height, {\n            ...options,\n            count: count\n        });\n        this.isWebGLMultipleRenderTargets = true;\n    }\n    get texture() {\n        return this.textures;\n    }\n} : class extends three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget {\n    constructor(width = 1, height = 1, count = 1, options = {}){\n        super(width, height, options);\n        this.isWebGLMultipleRenderTargets = true;\n        const texture = this.texture;\n        this.texture = [];\n        for(let i = 0; i < count; i++){\n            this.texture[i] = texture.clone();\n            this.texture[i].isRenderTargetTexture = true;\n        }\n    }\n    setSize(width, height, depth = 1) {\n        if (this.width !== width || this.height !== height || this.depth !== depth) {\n            this.width = width;\n            this.height = height;\n            this.depth = depth;\n            for(let i = 0, il = this.texture.length; i < il; i++){\n                this.texture[i].image.width = width;\n                this.texture[i].image.height = height;\n                this.texture[i].image.depth = depth;\n            }\n            this.dispose();\n        }\n        this.viewport.set(0, 0, width, height);\n        this.scissor.set(0, 0, width, height);\n    }\n    copy(source) {\n        this.dispose();\n        this.width = source.width;\n        this.height = source.height;\n        this.depth = source.depth;\n        this.scissor.copy(source.scissor);\n        this.scissorTest = source.scissorTest;\n        this.viewport.copy(source.viewport);\n        this.depthBuffer = source.depthBuffer;\n        this.stencilBuffer = source.stencilBuffer;\n        if (source.depthTexture !== null) this.depthTexture = source.depthTexture.clone();\n        this.texture.length = 0;\n        for(let i = 0, il = source.texture.length; i < il; i++){\n            this.texture[i] = source.texture[i].clone();\n            this.texture[i].isRenderTargetTexture = true;\n        }\n        return this;\n    }\n};\n\n\n/**\n * \n * @param {*} timerQuery \n * @param {THREE.WebGLRenderer} gl \n * @param {N8AOPostPass | N8AOPass} pass \n */ function $87431ee93b037844$var$checkTimerQuery(timerQuery, gl, pass) {\n    const available = gl.getQueryParameter(timerQuery, gl.QUERY_RESULT_AVAILABLE);\n    if (available) {\n        const elapsedTimeInNs = gl.getQueryParameter(timerQuery, gl.QUERY_RESULT);\n        const elapsedTimeInMs = elapsedTimeInNs / 1000000;\n        pass.lastTime = pass.lastTime === 0 ? elapsedTimeInMs : pass.timeRollingAverage * pass.lastTime + (1 - pass.timeRollingAverage) * elapsedTimeInMs;\n    } else // If the result is not available yet, check again after a delay\n    setTimeout(()=>{\n        $87431ee93b037844$var$checkTimerQuery(timerQuery, gl, pass);\n    }, 1);\n}\nclass $87431ee93b037844$export$2489f9981ab0fa82 extends (0, postprocessing__WEBPACK_IMPORTED_MODULE_1__.Pass) {\n    /**\n     * \n     * @param {THREE.Scene} scene\n     * @param {THREE.Camera} camera \n     * @param {number} width \n     * @param {number} height\n     *  \n     * @property {THREE.Scene} scene\n     * @property {THREE.Camera} camera\n     * @property {number} width\n     * @property {number} height\n     */ constructor(scene, camera, width = 512, height = 512){\n        super();\n        this.width = width;\n        this.height = height;\n        this.clear = true;\n        this.camera = camera;\n        this.scene = scene;\n        /**\n         * @type {Proxy & {\n         * aoSamples: number,\n         * aoRadius: number,\n         * denoiseSamples: number,\n         * denoiseRadius: number,\n         * distanceFalloff: number,\n         * intensity: number,\n         * denoiseIterations: number,\n         * renderMode: 0 | 1 | 2 | 3 | 4,\n         * color: THREE.Color,\n         * gammaCorrection: boolean,\n         * depthBufferType: 1 | 2 | 3,\n         * screenSpaceRadius: boolean,\n         * halfRes: boolean,\n         * depthAwareUpsampling: boolean\n         * colorMultiply: boolean\n         * }\n         */ this.autosetGamma = true;\n        this.configuration = new Proxy({\n            aoSamples: 16,\n            aoRadius: 5.0,\n            aoTones: 0.0,\n            denoiseSamples: 8,\n            denoiseRadius: 12,\n            distanceFalloff: 1.0,\n            intensity: 5,\n            denoiseIterations: 2.0,\n            renderMode: 0,\n            biasOffset: 0.0,\n            biasMultiplier: 0.0,\n            color: new three__WEBPACK_IMPORTED_MODULE_0__.Color(0, 0, 0),\n            gammaCorrection: true,\n            depthBufferType: (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Default,\n            screenSpaceRadius: false,\n            halfRes: false,\n            depthAwareUpsampling: true,\n            colorMultiply: true,\n            transparencyAware: false,\n            accumulate: false\n        }, {\n            set: (target, propName, value)=>{\n                const oldProp = target[propName];\n                target[propName] = value;\n                if (value.equals) {\n                    if (!value.equals(oldProp)) this.firstFrame();\n                } else if (oldProp !== value) this.firstFrame();\n                if (propName === \"aoSamples\" && oldProp !== value) this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                if (propName === \"denoiseSamples\" && oldProp !== value) this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                if (propName === \"halfRes\" && oldProp !== value) {\n                    this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                    this.configureHalfResTargets();\n                    this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                    this.setSize(this.width, this.height);\n                }\n                if (propName === \"depthAwareUpsampling\" && oldProp !== value) this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                if (propName === \"gammaCorrection\") this.autosetGamma = false;\n                if (propName === \"transparencyAware\" && oldProp !== value) {\n                    this.autoDetectTransparency = false;\n                    this.configureTransparencyTarget();\n                }\n                return true;\n            }\n        });\n        /** @type {THREE.Vector3[]} */ this.samples = [];\n        /** @type {THREE.Vector2[]} */ this.samplesDenoise = [];\n        this.autoDetectTransparency = true;\n        this.frames = 0;\n        this.lastViewMatrix = new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4();\n        this.lastProjectionMatrix = new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4();\n        this.configureEffectCompositer(this.configuration.depthBufferType);\n        this.configureSampleDependentPasses();\n        this.configureHalfResTargets();\n        this.detectTransparency();\n        this.configureTransparencyTarget();\n        //   this.effectCompisterQuad = new FullScreenTriangle(new THREE.ShaderMaterial(EffectCompositer));\n        this.copyQuad = new (0, $e4ca8dcb0218f846$export$dcd670d73db751f5)(new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial({\n            uniforms: {\n                tDiffuse: {\n                    value: null\n                }\n            },\n            depthWrite: false,\n            vertexShader: `\n            varying vec2 vUv;\n            void main() {\n                vUv = uv;\n                gl_Position = vec4(position, 1);\n            }\n            `,\n            fragmentShader: `\n            uniform sampler2D tDiffuse;\n            varying vec2 vUv;\n            void main() {\n                gl_FragColor = texture2D(tDiffuse, vUv);\n            }\n            `\n        }));\n        this.writeTargetInternal = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(this.width, this.height, {\n            minFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            magFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            depthBuffer: false,\n            format: three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat\n        });\n        this.readTargetInternal = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(this.width, this.height, {\n            minFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            magFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            depthBuffer: false,\n            format: three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat\n        });\n        this.outputTargetInternal = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(this.width, this.height, {\n            minFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            magFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            depthBuffer: false\n        });\n        this.accumulationRenderTarget = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(this.width, this.height, {\n            minFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            magFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            depthBuffer: false,\n            format: three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat,\n            type: three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType,\n            stencilBuffer: false,\n            depthBuffer: false,\n            alpha: true\n        });\n        this.accumulationQuad = new (0, $e4ca8dcb0218f846$export$dcd670d73db751f5)(new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial({\n            uniforms: {\n                frame: {\n                    value: 0\n                },\n                tDiffuse: {\n                    value: null\n                }\n            },\n            transparent: true,\n            opacity: 1,\n            vertexShader: `\n             varying vec2 vUv;\n             void main() {\n                 vUv = uv;\n                 gl_Position = vec4(position, 1);\n             }`,\n            fragmentShader: `\n             uniform sampler2D tDiffuse;\n             uniform float frame;\n                varying vec2 vUv;\n                void main() {\n                    vec4 color = texture2D(tDiffuse, vUv);\n                    gl_FragColor = vec4(color.rgb, 1.0 / (frame + 1.0));\n                }\n                `\n        }));\n        /** @type {THREE.DataTexture} */ this.bluenoise = new three__WEBPACK_IMPORTED_MODULE_0__.DataTexture((0, $06269ad78f3c5fdf$export$2e2bcd8739ae039), 128, 128);\n        this.bluenoise.colorSpace = three__WEBPACK_IMPORTED_MODULE_0__.NoColorSpace;\n        this.bluenoise.wrapS = three__WEBPACK_IMPORTED_MODULE_0__.RepeatWrapping;\n        this.bluenoise.wrapT = three__WEBPACK_IMPORTED_MODULE_0__.RepeatWrapping;\n        this.bluenoise.minFilter = three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter;\n        this.bluenoise.magFilter = three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter;\n        this.bluenoise.needsUpdate = true;\n        this.lastTime = 0;\n        this.timeRollingAverage = 0.99;\n        this.needsDepthTexture = true;\n        this.needsSwap = true;\n        this._r = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n        this._c = new three__WEBPACK_IMPORTED_MODULE_0__.Color();\n    }\n    configureHalfResTargets() {\n        this.firstFrame();\n        if (this.configuration.halfRes) {\n            this.depthDownsampleTarget = new (0, $ff9437d9c7577f11$export$156f6a58f569aa09)(this.width / 2, this.height / 2, 2);\n            if (three__WEBPACK_IMPORTED_MODULE_0__.REVISION <= 161) this.depthDownsampleTarget.textures = this.depthDownsampleTarget.texture;\n            this.depthDownsampleTarget.textures[0].format = three__WEBPACK_IMPORTED_MODULE_0__.RedFormat;\n            this.depthDownsampleTarget.textures[0].type = three__WEBPACK_IMPORTED_MODULE_0__.FloatType;\n            this.depthDownsampleTarget.textures[0].minFilter = three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter;\n            this.depthDownsampleTarget.textures[0].magFilter = three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter;\n            this.depthDownsampleTarget.textures[0].depthBuffer = false;\n            this.depthDownsampleTarget.textures[1].format = three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat;\n            this.depthDownsampleTarget.textures[1].type = three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType;\n            this.depthDownsampleTarget.textures[1].minFilter = three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter;\n            this.depthDownsampleTarget.textures[1].magFilter = three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter;\n            this.depthDownsampleTarget.textures[1].depthBuffer = false;\n            this.depthDownsampleQuad = new (0, $e4ca8dcb0218f846$export$dcd670d73db751f5)(new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial((0, $26aca173e0984d99$export$1efdf491687cd442)));\n        } else {\n            if (this.depthDownsampleTarget) {\n                this.depthDownsampleTarget.dispose();\n                this.depthDownsampleTarget = null;\n            }\n            if (this.depthDownsampleQuad) {\n                this.depthDownsampleQuad.dispose();\n                this.depthDownsampleQuad = null;\n            }\n        }\n    }\n    detectTransparency() {\n        if (this.autoDetectTransparency) {\n            let isTransparency = false;\n            this.scene.traverse((obj)=>{\n                if (obj.material && obj.material.transparent) isTransparency = true;\n            });\n            if (isTransparency) this.configuration.transparencyAware = true;\n        }\n    }\n    configureTransparencyTarget() {\n        if (this.configuration.transparencyAware) {\n            this.transparencyRenderTargetDWFalse = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(this.width, this.height, {\n                minFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n                magFilter: three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter,\n                type: three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType,\n                format: three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat\n            });\n            this.transparencyRenderTargetDWTrue = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(this.width, this.height, {\n                minFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n                magFilter: three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter,\n                type: three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType,\n                format: three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat\n            });\n            this.transparencyRenderTargetDWTrue.depthTexture = new three__WEBPACK_IMPORTED_MODULE_0__.DepthTexture(this.width, this.height, three__WEBPACK_IMPORTED_MODULE_0__.UnsignedIntType);\n            this.depthCopyPass = new (0, $e4ca8dcb0218f846$export$dcd670d73db751f5)(new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial({\n                uniforms: {\n                    depthTexture: {\n                        value: this.depthTexture\n                    },\n                    reverseDepthBuffer: {\n                        value: this.configuration.depthBufferType === (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Reverse\n                    }\n                },\n                vertexShader: /* glsl */ `\n            varying vec2 vUv;\n            void main() {\n                vUv = uv;\n                gl_Position = vec4(position, 1);\n            }`,\n                fragmentShader: /* glsl */ `\n            uniform sampler2D depthTexture;\n            uniform bool reverseDepthBuffer;\n            varying vec2 vUv;\n            void main() {\n                if (reverseDepthBuffer) {\n               float d = 1.0 - texture2D(depthTexture, vUv).r;\n           \n               d += 0.00001;\n               gl_FragDepth = 1.0 - d;\n            } else {\n                float d = texture2D(depthTexture, vUv).r;\n                d += 0.00001;\n                gl_FragDepth = d;\n            }\n               gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n            }\n            `\n            }));\n        } else {\n            if (this.transparencyRenderTargetDWFalse) {\n                this.transparencyRenderTargetDWFalse.dispose();\n                this.transparencyRenderTargetDWFalse = null;\n            }\n            if (this.transparencyRenderTargetDWTrue) {\n                this.transparencyRenderTargetDWTrue.dispose();\n                this.transparencyRenderTargetDWTrue = null;\n            }\n            if (this.depthCopyPass) {\n                this.depthCopyPass.dispose();\n                this.depthCopyPass = null;\n            }\n        }\n    }\n    renderTransparency(renderer) {\n        const oldBackground = this.scene.background;\n        const oldClearColor = renderer.getClearColor(new three__WEBPACK_IMPORTED_MODULE_0__.Color());\n        const oldClearAlpha = renderer.getClearAlpha();\n        const oldVisibility = new Map();\n        const oldAutoClearDepth = renderer.autoClearDepth;\n        this.scene.traverse((obj)=>{\n            oldVisibility.set(obj, obj.visible);\n        });\n        // Override the state\n        this.scene.background = null;\n        renderer.autoClearDepth = false;\n        renderer.setClearColor(new three__WEBPACK_IMPORTED_MODULE_0__.Color(0, 0, 0), 0);\n        this.depthCopyPass.material.uniforms.depthTexture.value = this.depthTexture;\n        this.depthCopyPass.material.uniforms.reverseDepthBuffer.value = this.configuration.depthBufferType === (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Reverse;\n        // Render out transparent objects WITHOUT depth write\n        renderer.setRenderTarget(this.transparencyRenderTargetDWFalse);\n        this.scene.traverse((obj)=>{\n            if (obj.material) obj.visible = oldVisibility.get(obj) && (obj.material.transparent && !obj.material.depthWrite && !obj.userData.treatAsOpaque || !!obj.userData.cannotReceiveAO);\n        });\n        renderer.clear(true, true, true);\n        this.depthCopyPass.render(renderer);\n        renderer.render(this.scene, this.camera);\n        // Render out transparent objects WITH depth write\n        renderer.setRenderTarget(this.transparencyRenderTargetDWTrue);\n        this.scene.traverse((obj)=>{\n            if (obj.material) obj.visible = oldVisibility.get(obj) && obj.material.transparent && obj.material.depthWrite && !obj.userData.treatAsOpaque;\n        });\n        renderer.clear(true, true, true);\n        this.depthCopyPass.render(renderer);\n        renderer.render(this.scene, this.camera);\n        // Restore\n        this.scene.traverse((obj)=>{\n            obj.visible = oldVisibility.get(obj);\n        });\n        renderer.setClearColor(oldClearColor, oldClearAlpha);\n        this.scene.background = oldBackground;\n        renderer.autoClearDepth = oldAutoClearDepth;\n    }\n    configureSampleDependentPasses() {\n        this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n        this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n    }\n    configureAOPass(depthBufferType = (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Default, ortho = false) {\n        this.firstFrame();\n        this.samples = this.generateHemisphereSamples(this.configuration.aoSamples);\n        const e = {\n            ...(0, $1ed45968c1160c3c$export$c9b263b9a17dffd7)\n        };\n        e.fragmentShader = e.fragmentShader.replace(\"16\", this.configuration.aoSamples).replace(\"16.0\", this.configuration.aoSamples + \".0\");\n        if (depthBufferType === (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Log) e.fragmentShader = \"#define LOGDEPTH\\n\" + e.fragmentShader;\n        else if (depthBufferType === (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Reverse) e.fragmentShader = \"#define REVERSEDEPTH\\n\" + e.fragmentShader;\n        if (ortho) e.fragmentShader = \"#define ORTHO\\n\" + e.fragmentShader;\n        if (this.configuration.halfRes) e.fragmentShader = \"#define HALFRES\\n\" + e.fragmentShader;\n        if (this.effectShaderQuad) {\n            this.effectShaderQuad.material.dispose();\n            this.effectShaderQuad.material = new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial(e);\n        } else this.effectShaderQuad = new (0, $e4ca8dcb0218f846$export$dcd670d73db751f5)(new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial(e));\n    }\n    configureDenoisePass(depthBufferType = (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Default, ortho = false) {\n        this.firstFrame();\n        this.samplesDenoise = this.generateDenoiseSamples(this.configuration.denoiseSamples, 11);\n        const p = {\n            ...(0, $e52378cd0f5a973d$export$57856b59f317262e)\n        };\n        p.fragmentShader = p.fragmentShader.replace(\"16\", this.configuration.denoiseSamples);\n        if (depthBufferType === (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Log) p.fragmentShader = \"#define LOGDEPTH\\n\" + p.fragmentShader;\n        else if (depthBufferType === (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Reverse) p.fragmentShader = \"#define REVERSEDEPTH\\n\" + p.fragmentShader;\n        if (ortho) p.fragmentShader = \"#define ORTHO\\n\" + p.fragmentShader;\n        if (this.poissonBlurQuad) {\n            this.poissonBlurQuad.material.dispose();\n            this.poissonBlurQuad.material = new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial(p);\n        } else this.poissonBlurQuad = new (0, $e4ca8dcb0218f846$export$dcd670d73db751f5)(new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial(p));\n    }\n    configureEffectCompositer(depthBufferType = (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Default, ortho = false) {\n        this.firstFrame();\n        const e = {\n            ...(0, $12b21d24d1192a04$export$a815acccbd2c9a49)\n        };\n        if (depthBufferType === (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Log) e.fragmentShader = \"#define LOGDEPTH\\n\" + e.fragmentShader;\n        else if (depthBufferType === (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Reverse) e.fragmentShader = \"#define REVERSEDEPTH\\n\" + e.fragmentShader;\n        if (ortho) e.fragmentShader = \"#define ORTHO\\n\" + e.fragmentShader;\n        if (this.configuration.halfRes && this.configuration.depthAwareUpsampling) e.fragmentShader = \"#define HALFRES\\n\" + e.fragmentShader;\n        if (this.effectCompositerQuad) {\n            this.effectCompositerQuad.material.dispose();\n            this.effectCompositerQuad.material = new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial(e);\n        } else this.effectCompositerQuad = new (0, $e4ca8dcb0218f846$export$dcd670d73db751f5)(new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial(e));\n    }\n    /**\n         * \n         * @param {Number} n \n         * @returns {THREE.Vector3[]}\n         */ generateHemisphereSamples(n) {\n        const points = [];\n        for(let k = 0; k < n; k++){\n            const theta = 2.399963 * k;\n            const r = Math.sqrt(k + 0.5) / Math.sqrt(n);\n            const x = r * Math.cos(theta);\n            const y = r * Math.sin(theta);\n            // Project to hemisphere\n            const z = Math.sqrt(1 - (x * x + y * y));\n            points.push(new three__WEBPACK_IMPORTED_MODULE_0__.Vector3(x, y, z));\n        }\n        return points;\n    }\n    /**\n         * \n         * @param {number} numSamples \n         * @param {number} numRings \n         * @returns {THREE.Vector2[]}\n         */ generateDenoiseSamples(numSamples, numRings) {\n        const angleStep = 2 * Math.PI * numRings / numSamples;\n        const invNumSamples = 1.0 / numSamples;\n        const radiusStep = invNumSamples;\n        const samples = [];\n        let radius = invNumSamples;\n        let angle = 0;\n        for(let i = 0; i < numSamples; i++){\n            samples.push(new three__WEBPACK_IMPORTED_MODULE_0__.Vector2(Math.cos(angle), Math.sin(angle)).multiplyScalar(Math.pow(radius, 0.75)));\n            radius += radiusStep;\n            angle += angleStep;\n        }\n        return samples;\n    }\n    setSize(width, height) {\n        this.firstFrame();\n        this.width = width;\n        this.height = height;\n        const c = this.configuration.halfRes ? 0.5 : 1;\n        this.writeTargetInternal.setSize(width * c, height * c);\n        this.readTargetInternal.setSize(width * c, height * c);\n        this.accumulationRenderTarget.setSize(width * c, height * c);\n        if (this.configuration.halfRes) this.depthDownsampleTarget.setSize(width * c, height * c);\n        if (this.configuration.transparencyAware) {\n            this.transparencyRenderTargetDWFalse.setSize(width, height);\n            this.transparencyRenderTargetDWTrue.setSize(width, height);\n        }\n        this.outputTargetInternal.setSize(width, height);\n    }\n    setDepthTexture(depthTexture) {\n        this.depthTexture = depthTexture;\n    }\n    firstFrame() {\n        this.needsFrame = true;\n    }\n    render(renderer, inputBuffer, outputBuffer) {\n        const xrEnabled = renderer.xr.enabled;\n        renderer.xr.enabled = false;\n        // Copy inputBuffer to outputBuffer\n        //renderer.setRenderTarget(outputBuffer);\n        //  this.copyQuad.material.uniforms.tDiffuse.value = inputBuffer.texture;\n        //   this.copyQuad.render(renderer);\n        if (renderer.capabilities.logarithmicDepthBuffer && this.configuration.depthBufferType !== (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Log || renderer.capabilities.reverseDepthBuffer && this.configuration.depthBufferType !== (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Reverse) {\n            this.configuration.depthBufferType = renderer.capabilities.logarithmicDepthBuffer ? (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Log : renderer.capabilities.reverseDepthBuffer ? (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Reverse : (0, $05f6997e4b65da14$export$ed4ee5d1e55474a5).Default;\n            this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n            this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n            this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n        }\n        this.detectTransparency();\n        if (inputBuffer.texture.type !== this.outputTargetInternal.texture.type || inputBuffer.texture.format !== this.outputTargetInternal.texture.format) {\n            this.outputTargetInternal.texture.type = inputBuffer.texture.type;\n            this.outputTargetInternal.texture.format = inputBuffer.texture.format;\n            this.outputTargetInternal.texture.needsUpdate = true;\n        }\n        this.camera.updateMatrixWorld();\n        if (this.lastViewMatrix.equals(this.camera.matrixWorldInverse) && this.lastProjectionMatrix.equals(this.camera.projectionMatrix) && this.configuration.accumulate && !this.needsFrame) this.frame++;\n        else {\n            if (this.configuration.accumulate) {\n                renderer.setRenderTarget(this.accumulationRenderTarget);\n                renderer.clear(true, true, true);\n            }\n            this.frame = 0;\n            this.needsFrame = false;\n        }\n        this.lastViewMatrix.copy(this.camera.matrixWorldInverse);\n        this.lastProjectionMatrix.copy(this.camera.projectionMatrix);\n        let gl;\n        let ext;\n        let timerQuery;\n        if (this.debugMode) {\n            gl = renderer.getContext();\n            ext = gl.getExtension(\"EXT_disjoint_timer_query_webgl2\");\n            if (ext === null) {\n                console.error(\"EXT_disjoint_timer_query_webgl2 not available, disabling debug mode.\");\n                this.debugMode = false;\n            }\n        }\n        if (this.debugMode) {\n            timerQuery = gl.createQuery();\n            gl.beginQuery(ext.TIME_ELAPSED_EXT, timerQuery);\n        }\n        if (this.configuration.transparencyAware) this.renderTransparency(renderer);\n        this._r.set(this.width, this.height);\n        let trueRadius = this.configuration.aoRadius;\n        if (this.configuration.halfRes && this.configuration.screenSpaceRadius) trueRadius *= 0.5;\n        if (this.frame < 1024 / this.configuration.aoSamples) {\n            if (this.configuration.halfRes) {\n                renderer.setRenderTarget(this.depthDownsampleTarget);\n                this.depthDownsampleQuad.material.uniforms.sceneDepth.value = this.depthTexture;\n                this.depthDownsampleQuad.material.uniforms.resolution.value = this._r;\n                this.depthDownsampleQuad.material.uniforms[\"near\"].value = this.camera.near;\n                this.depthDownsampleQuad.material.uniforms[\"far\"].value = this.camera.far;\n                this.depthDownsampleQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n                this.depthDownsampleQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n                this.depthDownsampleQuad.material.uniforms[\"logDepth\"].value = this.configuration.logarithmicDepthBuffer;\n                this.depthDownsampleQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n                this.depthDownsampleQuad.render(renderer);\n            }\n            this.effectShaderQuad.material.uniforms[\"sceneDiffuse\"].value = inputBuffer.texture;\n            this.effectShaderQuad.material.uniforms[\"sceneDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.depthTexture;\n            this.effectShaderQuad.material.uniforms[\"sceneNormal\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[1] : null;\n            this.effectShaderQuad.material.uniforms[\"projMat\"].value = this.camera.projectionMatrix;\n            this.effectShaderQuad.material.uniforms[\"viewMat\"].value = this.camera.matrixWorldInverse;\n            this.effectShaderQuad.material.uniforms[\"projViewMat\"].value = this.camera.projectionMatrix.clone().multiply(this.camera.matrixWorldInverse.clone());\n            this.effectShaderQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n            this.effectShaderQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n            this.effectShaderQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new three__WEBPACK_IMPORTED_MODULE_0__.Vector3());\n            this.effectShaderQuad.material.uniforms[\"biasAdjustment\"].value = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2(this.configuration.biasOffset, this.configuration.biasMultiplier);\n            this.effectShaderQuad.material.uniforms[\"resolution\"].value = this.configuration.halfRes ? this._r.clone().multiplyScalar(0.5).floor() : this._r;\n            this.effectShaderQuad.material.uniforms[\"time\"].value = performance.now() / 1000;\n            this.effectShaderQuad.material.uniforms[\"samples\"].value = this.samples;\n            this.effectShaderQuad.material.uniforms[\"bluenoise\"].value = this.bluenoise;\n            this.effectShaderQuad.material.uniforms[\"radius\"].value = trueRadius;\n            this.effectShaderQuad.material.uniforms[\"distanceFalloff\"].value = this.configuration.distanceFalloff;\n            this.effectShaderQuad.material.uniforms[\"near\"].value = this.camera.near;\n            this.effectShaderQuad.material.uniforms[\"far\"].value = this.camera.far;\n            this.effectShaderQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n            this.effectShaderQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n            this.effectShaderQuad.material.uniforms[\"frame\"].value = this.frame;\n            // Start the AO\n            renderer.setRenderTarget(this.writeTargetInternal);\n            this.effectShaderQuad.render(renderer);\n            // End the AO\n            // Start the blur\n            for(let i = 0; i < this.configuration.denoiseIterations; i++){\n                [this.writeTargetInternal, this.readTargetInternal] = [\n                    this.readTargetInternal,\n                    this.writeTargetInternal\n                ];\n                this.poissonBlurQuad.material.uniforms[\"tDiffuse\"].value = this.readTargetInternal.texture;\n                this.poissonBlurQuad.material.uniforms[\"sceneDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.depthTexture;\n                this.poissonBlurQuad.material.uniforms[\"projMat\"].value = this.camera.projectionMatrix;\n                this.poissonBlurQuad.material.uniforms[\"viewMat\"].value = this.camera.matrixWorldInverse;\n                this.poissonBlurQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n                this.poissonBlurQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n                this.poissonBlurQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new three__WEBPACK_IMPORTED_MODULE_0__.Vector3());\n                this.poissonBlurQuad.material.uniforms[\"resolution\"].value = this.configuration.halfRes ? this._r.clone().multiplyScalar(0.5).floor() : this._r;\n                this.poissonBlurQuad.material.uniforms[\"time\"].value = performance.now() / 1000;\n                this.poissonBlurQuad.material.uniforms[\"blueNoise\"].value = this.bluenoise;\n                this.poissonBlurQuad.material.uniforms[\"radius\"].value = this.configuration.denoiseRadius * (this.configuration.halfRes ? 0.5 : 1);\n                this.poissonBlurQuad.material.uniforms[\"worldRadius\"].value = trueRadius;\n                this.poissonBlurQuad.material.uniforms[\"distanceFalloff\"].value = this.configuration.distanceFalloff;\n                this.poissonBlurQuad.material.uniforms[\"index\"].value = i;\n                this.poissonBlurQuad.material.uniforms[\"poissonDisk\"].value = this.samplesDenoise;\n                this.poissonBlurQuad.material.uniforms[\"near\"].value = this.camera.near;\n                this.poissonBlurQuad.material.uniforms[\"far\"].value = this.camera.far;\n                this.poissonBlurQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n                renderer.setRenderTarget(this.writeTargetInternal);\n                this.poissonBlurQuad.render(renderer);\n            }\n            renderer.setRenderTarget(this.accumulationRenderTarget);\n            const oldAutoClear = renderer.autoClear;\n            renderer.autoClear = false;\n            this.accumulationQuad.material.uniforms[\"tDiffuse\"].value = this.writeTargetInternal.texture;\n            this.accumulationQuad.material.uniforms[\"frame\"].value = this.frame;\n            this.accumulationQuad.render(renderer);\n            renderer.autoClear = oldAutoClear;\n        }\n        // Now, we have the blurred AO in writeTargetInternal\n        // End the blur\n        // Start the composition\n        if (this.configuration.transparencyAware) {\n            this.effectCompositerQuad.material.uniforms[\"transparencyDWFalse\"].value = this.transparencyRenderTargetDWFalse.texture;\n            this.effectCompositerQuad.material.uniforms[\"transparencyDWTrue\"].value = this.transparencyRenderTargetDWTrue.texture;\n            this.effectCompositerQuad.material.uniforms[\"transparencyDWTrueDepth\"].value = this.transparencyRenderTargetDWTrue.depthTexture;\n            this.effectCompositerQuad.material.uniforms[\"transparencyAware\"].value = true;\n        }\n        this.effectCompositerQuad.material.uniforms[\"sceneDiffuse\"].value = inputBuffer.texture;\n        this.effectCompositerQuad.material.uniforms[\"sceneDepth\"].value = this.depthTexture;\n        this.effectCompositerQuad.material.uniforms[\"aoTones\"].value = this.configuration.aoTones;\n        this.effectCompositerQuad.material.uniforms[\"near\"].value = this.camera.near;\n        this.effectCompositerQuad.material.uniforms[\"far\"].value = this.camera.far;\n        this.effectCompositerQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n        this.effectCompositerQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n        this.effectCompositerQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n        this.effectCompositerQuad.material.uniforms[\"downsampledDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.depthTexture;\n        this.effectCompositerQuad.material.uniforms[\"resolution\"].value = this._r;\n        this.effectCompositerQuad.material.uniforms[\"blueNoise\"].value = this.bluenoise;\n        this.effectCompositerQuad.material.uniforms[\"intensity\"].value = this.configuration.intensity;\n        this.effectCompositerQuad.material.uniforms[\"renderMode\"].value = this.configuration.renderMode;\n        this.effectCompositerQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n        this.effectCompositerQuad.material.uniforms[\"radius\"].value = trueRadius;\n        this.effectCompositerQuad.material.uniforms[\"distanceFalloff\"].value = this.configuration.distanceFalloff;\n        this.effectCompositerQuad.material.uniforms[\"gammaCorrection\"].value = this.autosetGamma ? this.renderToScreen : this.configuration.gammaCorrection;\n        this.effectCompositerQuad.material.uniforms[\"tDiffuse\"].value = this.accumulationRenderTarget.texture;\n        this.effectCompositerQuad.material.uniforms[\"color\"].value = this._c.copy(this.configuration.color).convertSRGBToLinear();\n        this.effectCompositerQuad.material.uniforms[\"colorMultiply\"].value = this.configuration.colorMultiply;\n        this.effectCompositerQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new three__WEBPACK_IMPORTED_MODULE_0__.Vector3());\n        this.effectCompositerQuad.material.uniforms[\"fog\"].value = !!this.scene.fog;\n        if (this.scene.fog) {\n            if (this.scene.fog.isFog) {\n                this.effectCompositerQuad.material.uniforms[\"fogExp\"].value = false;\n                this.effectCompositerQuad.material.uniforms[\"fogNear\"].value = this.scene.fog.near;\n                this.effectCompositerQuad.material.uniforms[\"fogFar\"].value = this.scene.fog.far;\n            } else if (this.scene.fog.isFogExp2) {\n                this.effectCompositerQuad.material.uniforms[\"fogExp\"].value = true;\n                this.effectCompositerQuad.material.uniforms[\"fogDensity\"].value = this.scene.fog.density;\n            } else console.error(`Unsupported fog type ${this.scene.fog.constructor.name} in SSAOPass.`);\n        }\n        renderer.setRenderTarget(/* this.renderToScreen ? null :\n                 outputBuffer*/ this.outputTargetInternal);\n        this.effectCompositerQuad.render(renderer);\n        renderer.setRenderTarget(this.renderToScreen ? null : outputBuffer);\n        this.copyQuad.material.uniforms[\"tDiffuse\"].value = this.outputTargetInternal.texture;\n        this.copyQuad.render(renderer);\n        if (this.debugMode) {\n            gl.endQuery(ext.TIME_ELAPSED_EXT);\n            $87431ee93b037844$var$checkTimerQuery(timerQuery, gl, this);\n        }\n        renderer.xr.enabled = xrEnabled;\n    }\n    /**\n         * Enables the debug mode of the AO, meaning the lastTime value will be updated.\n         */ enableDebugMode() {\n        this.debugMode = true;\n    }\n    /**\n         * Disables the debug mode of the AO, meaning the lastTime value will not be updated.\n         */ disableDebugMode() {\n        this.debugMode = false;\n    }\n    /**\n         * Sets the display mode of the AO\n         * @param {\"Combined\" | \"AO\" | \"No AO\" | \"Split\" | \"Split AO\"} mode - The display mode. \n         */ setDisplayMode(mode) {\n        this.configuration.renderMode = [\n            \"Combined\",\n            \"AO\",\n            \"No AO\",\n            \"Split\",\n            \"Split AO\"\n        ].indexOf(mode);\n    }\n    /**\n         * \n         * @param {\"Performance\" | \"Low\" | \"Medium\" | \"High\" | \"Ultra\"} mode \n         */ setQualityMode(mode) {\n        if (mode === \"Performance\") {\n            this.configuration.aoSamples = 8;\n            this.configuration.denoiseSamples = 4;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"Low\") {\n            this.configuration.aoSamples = 16;\n            this.configuration.denoiseSamples = 4;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"Medium\") {\n            this.configuration.aoSamples = 16;\n            this.configuration.denoiseSamples = 8;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"High\") {\n            this.configuration.aoSamples = 64;\n            this.configuration.denoiseSamples = 8;\n            this.configuration.denoiseRadius = 6;\n        } else if (mode === \"Ultra\") {\n            this.configuration.aoSamples = 64;\n            this.configuration.denoiseSamples = 16;\n            this.configuration.denoiseRadius = 6;\n        }\n    }\n}\n\n\n\n\n/**\n * \n * @param {*} timerQuery \n * @param {THREE.WebGLRenderer} gl \n * @param {N8AOPass} pass \n */ function $05f6997e4b65da14$var$checkTimerQuery(timerQuery, gl, pass) {\n    const available = gl.getQueryParameter(timerQuery, gl.QUERY_RESULT_AVAILABLE);\n    if (available) {\n        const elapsedTimeInNs = gl.getQueryParameter(timerQuery, gl.QUERY_RESULT);\n        const elapsedTimeInMs = elapsedTimeInNs / 1000000;\n        pass.lastTime = pass.lastTime === 0 ? elapsedTimeInMs : pass.timeRollingAverage * pass.lastTime + (1 - pass.timeRollingAverage) * elapsedTimeInMs;\n    } else // If the result is not available yet, check again after a delay\n    setTimeout(()=>{\n        $05f6997e4b65da14$var$checkTimerQuery(timerQuery, gl, pass);\n    }, 1);\n}\nconst $05f6997e4b65da14$export$ed4ee5d1e55474a5 = {\n    Default: 1,\n    Log: 2,\n    Reverse: 3\n};\nclass $05f6997e4b65da14$export$2d57db20b5eb5e0a extends (0, three_examples_jsm_postprocessing_Pass_js__WEBPACK_IMPORTED_MODULE_2__.Pass) {\n    /**\n     * \n     * @param {THREE.Scene} scene\n     * @param {THREE.Camera} camera \n     * @param {number} width \n     * @param {number} height\n     *  \n     * @property {THREE.Scene} scene\n     * @property {THREE.Camera} camera\n     * @property {number} width\n     * @property {number} height\n     */ constructor(scene, camera, width = 512, height = 512){\n        super();\n        this.width = width;\n        this.height = height;\n        this.clear = true;\n        this.camera = camera;\n        this.scene = scene;\n        /**\n         * @type {Proxy & {\n         * aoSamples: number,\n         * aoRadius: number,\n         * denoiseSamples: number,\n         * denoiseRadius: number,\n         * distanceFalloff: number,\n         * intensity: number,\n         * denoiseIterations: number,\n         * renderMode: 0 | 1 | 2 | 3 | 4,\n         * color: THREE.Color,\n         * gammaCorrection: boolean,\n         * depthBufferType: 1 | 2 | 3,\n         * screenSpaceRadius: boolean,\n         * halfRes: boolean,\n         * depthAwareUpsampling: boolean,\n         * autoRenderBeauty: boolean\n         * colorMultiply: boolean\n         * }\n         */ this.configuration = new Proxy({\n            aoSamples: 16,\n            aoRadius: 5.0,\n            aoTones: 0.0,\n            denoiseSamples: 8,\n            denoiseRadius: 12,\n            distanceFalloff: 1.0,\n            intensity: 5,\n            denoiseIterations: 2.0,\n            renderMode: 0,\n            biasOffset: 0.0,\n            biasMultiplier: 0.0,\n            color: new three__WEBPACK_IMPORTED_MODULE_0__.Color(0, 0, 0),\n            gammaCorrection: true,\n            depthBufferType: $05f6997e4b65da14$export$ed4ee5d1e55474a5.Default,\n            screenSpaceRadius: false,\n            halfRes: false,\n            depthAwareUpsampling: true,\n            autoRenderBeauty: true,\n            colorMultiply: true,\n            transparencyAware: false,\n            stencil: false,\n            accumulate: false\n        }, {\n            set: (target, propName, value)=>{\n                const oldProp = target[propName];\n                target[propName] = value;\n                if (value.equals) {\n                    if (!value.equals(oldProp)) this.firstFrame();\n                } else if (oldProp !== value) this.firstFrame();\n                if (propName === \"aoSamples\" && oldProp !== value) this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                if (propName === \"denoiseSamples\" && oldProp !== value) this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                if (propName === \"halfRes\" && oldProp !== value) {\n                    this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                    this.configureHalfResTargets();\n                    this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                    this.setSize(this.width, this.height);\n                }\n                if (propName === \"depthAwareUpsampling\" && oldProp !== value) this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                if (propName === \"transparencyAware\" && oldProp !== value) {\n                    this.autoDetectTransparency = false;\n                    this.configureTransparencyTarget();\n                }\n                if (propName === \"stencil\" && oldProp !== value) {\n                    /*  this.beautyRenderTarget.stencilBuffer = value;\n                      this.beautyRenderTarget.depthTexture.format = value ? THREE.DepthStencilFormat : THREE.DepthFormat;\n                      this.beautyRenderTarget.depthTexture.type = value ? THREE.UnsignedInt248Type : THREE.UnsignedIntType;\n                      this.beautyRenderTarget.depthTexture.needsUpdate = true;\n                      this.beautyRenderTarget.needsUpdate = true;*/ this.beautyRenderTarget.dispose();\n                    this.beautyRenderTarget = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(this.width, this.height, {\n                        minFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n                        magFilter: three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter,\n                        type: three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType,\n                        format: three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat,\n                        stencilBuffer: value\n                    });\n                    this.beautyRenderTarget.depthTexture = new three__WEBPACK_IMPORTED_MODULE_0__.DepthTexture(this.width, this.height, value ? three__WEBPACK_IMPORTED_MODULE_0__.UnsignedInt248Type : three__WEBPACK_IMPORTED_MODULE_0__.UnsignedIntType);\n                    this.beautyRenderTarget.depthTexture.format = value ? three__WEBPACK_IMPORTED_MODULE_0__.DepthStencilFormat : three__WEBPACK_IMPORTED_MODULE_0__.DepthFormat;\n                }\n                return true;\n            }\n        });\n        /** @type {THREE.Vector3[]} */ this.samples = [];\n        /** @type {THREE.Vector2[]} */ this.samplesDenoise = [];\n        this.autoDetectTransparency = true;\n        this.frame = 0;\n        this.lastViewMatrix = new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4();\n        this.lastProjectionMatrix = new three__WEBPACK_IMPORTED_MODULE_0__.Matrix4();\n        this.beautyRenderTarget = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(this.width, this.height, {\n            minFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            magFilter: three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter,\n            type: three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType,\n            format: three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat,\n            stencilBuffer: false\n        });\n        this.beautyRenderTarget.depthTexture = new three__WEBPACK_IMPORTED_MODULE_0__.DepthTexture(this.width, this.height, three__WEBPACK_IMPORTED_MODULE_0__.UnsignedIntType);\n        this.beautyRenderTarget.depthTexture.format = three__WEBPACK_IMPORTED_MODULE_0__.DepthFormat;\n        this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n        this.configureSampleDependentPasses();\n        this.configureHalfResTargets();\n        this.detectTransparency();\n        this.configureTransparencyTarget();\n        this.writeTargetInternal = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(this.width, this.height, {\n            minFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            magFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            depthBuffer: false,\n            format: three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat\n        });\n        this.readTargetInternal = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(this.width, this.height, {\n            minFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            magFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            depthBuffer: false,\n            format: three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat\n        });\n        this.accumulationRenderTarget = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(this.width, this.height, {\n            minFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            magFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            depthBuffer: false,\n            format: three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat,\n            type: three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType,\n            stencilBuffer: false,\n            depthBuffer: false,\n            alpha: true\n        });\n        /** @type {THREE.DataTexture} */ this.bluenoise = new three__WEBPACK_IMPORTED_MODULE_0__.DataTexture((0, $06269ad78f3c5fdf$export$2e2bcd8739ae039), 128, 128);\n        this.accumulationQuad = new (0, $e4ca8dcb0218f846$export$dcd670d73db751f5)(new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial({\n            uniforms: {\n                frame: {\n                    value: 0\n                },\n                tDiffuse: {\n                    value: null\n                }\n            },\n            transparent: true,\n            opacity: 1,\n            vertexShader: `\n             varying vec2 vUv;\n             void main() {\n                 vUv = uv;\n                 gl_Position = vec4(position, 1);\n             }`,\n            fragmentShader: `\n             uniform sampler2D tDiffuse;\n             uniform float frame;\n                varying vec2 vUv;\n                void main() {\n                    vec4 color = texture2D(tDiffuse, vUv);\n                    gl_FragColor = vec4(color.rgb, 1.0 / (frame + 1.0));\n                }\n                `\n        }));\n        this.bluenoise.colorSpace = three__WEBPACK_IMPORTED_MODULE_0__.NoColorSpace;\n        this.bluenoise.wrapS = three__WEBPACK_IMPORTED_MODULE_0__.RepeatWrapping;\n        this.bluenoise.wrapT = three__WEBPACK_IMPORTED_MODULE_0__.RepeatWrapping;\n        this.bluenoise.minFilter = three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter;\n        this.bluenoise.magFilter = three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter;\n        this.bluenoise.needsUpdate = true;\n        this.lastTime = 0;\n        this.timeRollingAverage = 0.99;\n        this._r = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n        this._c = new three__WEBPACK_IMPORTED_MODULE_0__.Color();\n    }\n    configureHalfResTargets() {\n        this.firstFrame();\n        if (this.configuration.halfRes) {\n            this.depthDownsampleTarget = new (0, $ff9437d9c7577f11$export$156f6a58f569aa09)(this.width / 2, this.height / 2, 2);\n            if (three__WEBPACK_IMPORTED_MODULE_0__.REVISION <= 161) this.depthDownsampleTarget.textures = this.depthDownsampleTarget.texture;\n            this.depthDownsampleTarget.textures[0].format = three__WEBPACK_IMPORTED_MODULE_0__.RedFormat;\n            this.depthDownsampleTarget.textures[0].type = three__WEBPACK_IMPORTED_MODULE_0__.FloatType;\n            this.depthDownsampleTarget.textures[0].minFilter = three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter;\n            this.depthDownsampleTarget.textures[0].magFilter = three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter;\n            this.depthDownsampleTarget.textures[0].depthBuffer = false;\n            this.depthDownsampleTarget.textures[1].format = three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat;\n            this.depthDownsampleTarget.textures[1].type = three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType;\n            this.depthDownsampleTarget.textures[1].minFilter = three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter;\n            this.depthDownsampleTarget.textures[1].magFilter = three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter;\n            this.depthDownsampleTarget.textures[1].depthBuffer = false;\n            const e = {\n                ...(0, $26aca173e0984d99$export$1efdf491687cd442)\n            };\n            if (depthBufferType === $05f6997e4b65da14$export$ed4ee5d1e55474a5.Reverse) e.fragmentShader = \"#define REVERSEDEPTH\\n\" + e.fragmentShader;\n            this.depthDownsampleQuad = new (0, $e4ca8dcb0218f846$export$dcd670d73db751f5)(new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial(e));\n        } else {\n            if (this.depthDownsampleTarget) {\n                this.depthDownsampleTarget.dispose();\n                this.depthDownsampleTarget = null;\n            }\n            if (this.depthDownsampleQuad) {\n                this.depthDownsampleQuad.dispose();\n                this.depthDownsampleQuad = null;\n            }\n        }\n    }\n    detectTransparency() {\n        if (this.autoDetectTransparency) {\n            let isTransparency = false;\n            this.scene.traverse((obj)=>{\n                if (obj.material && obj.material.transparent) isTransparency = true;\n            });\n            this.configuration.transparencyAware = isTransparency;\n        }\n    }\n    configureTransparencyTarget() {\n        if (this.configuration.transparencyAware) {\n            this.transparencyRenderTargetDWFalse = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(this.width, this.height, {\n                minFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n                magFilter: three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter,\n                type: three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType,\n                format: three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat\n            });\n            this.transparencyRenderTargetDWTrue = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(this.width, this.height, {\n                minFilter: three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n                magFilter: three__WEBPACK_IMPORTED_MODULE_0__.NearestFilter,\n                type: three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType,\n                format: three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat\n            });\n            this.transparencyRenderTargetDWTrue.depthTexture = new three__WEBPACK_IMPORTED_MODULE_0__.DepthTexture(this.width, this.height, three__WEBPACK_IMPORTED_MODULE_0__.UnsignedIntType);\n            this.depthCopyPass = new (0, $e4ca8dcb0218f846$export$dcd670d73db751f5)(new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial({\n                uniforms: {\n                    depthTexture: {\n                        value: this.depthTexture\n                    },\n                    reverseDepthBuffer: {\n                        value: this.configuration.depthBufferType === $05f6997e4b65da14$export$ed4ee5d1e55474a5.Reverse\n                    }\n                },\n                vertexShader: /* glsl */ `\n                        varying vec2 vUv;\n                        void main() {\n                            vUv = uv;\n                            gl_Position = vec4(position, 1);\n                        }`,\n                fragmentShader: /* glsl */ `\n                        uniform sampler2D depthTexture;\n                        uniform bool reverseDepthBuffer;\n                        varying vec2 vUv;\n                        void main() {\n                            if (reverseDepthBuffer) {\n                           float d = 1.0 - texture2D(depthTexture, vUv).r;\n                       \n                           d += 0.00001;\n                           gl_FragDepth = 1.0 - d;\n                        } else {\n                            float d = texture2D(depthTexture, vUv).r;\n                            d += 0.00001;\n                            gl_FragDepth = d;\n                        }\n                           gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n                        }\n                        `\n            }));\n        } else {\n            if (this.transparencyRenderTargetDWFalse) {\n                this.transparencyRenderTargetDWFalse.dispose();\n                this.transparencyRenderTargetDWFalse = null;\n            }\n            if (this.transparencyRenderTargetDWTrue) {\n                this.transparencyRenderTargetDWTrue.dispose();\n                this.transparencyRenderTargetDWTrue = null;\n            }\n            if (this.depthCopyPass) {\n                this.depthCopyPass.dispose();\n                this.depthCopyPass = null;\n            }\n        }\n    }\n    renderTransparency(renderer) {\n        const oldBackground = this.scene.background;\n        const oldClearColor = renderer.getClearColor(new three__WEBPACK_IMPORTED_MODULE_0__.Color());\n        const oldClearAlpha = renderer.getClearAlpha();\n        const oldVisibility = new Map();\n        const oldAutoClearDepth = renderer.autoClearDepth;\n        this.scene.traverse((obj)=>{\n            oldVisibility.set(obj, obj.visible);\n        });\n        // Override the state\n        this.scene.background = null;\n        renderer.autoClearDepth = false;\n        renderer.setClearColor(new three__WEBPACK_IMPORTED_MODULE_0__.Color(0, 0, 0), 0);\n        this.depthCopyPass.material.uniforms.depthTexture.value = this.beautyRenderTarget.depthTexture;\n        this.depthCopyPass.material.uniforms.reverseDepthBuffer.value = this.configuration.depthBufferType === $05f6997e4b65da14$export$ed4ee5d1e55474a5.Reverse;\n        // Render out transparent objects WITHOUT depth write\n        renderer.setRenderTarget(this.transparencyRenderTargetDWFalse);\n        this.scene.traverse((obj)=>{\n            if (obj.material) obj.visible = oldVisibility.get(obj) && (obj.material.transparent && !obj.material.depthWrite && !obj.userData.treatAsOpaque || !!obj.userData.cannotReceiveAO);\n        });\n        renderer.clear(true, true, true);\n        this.depthCopyPass.render(renderer);\n        renderer.render(this.scene, this.camera);\n        // Render out transparent objects WITH depth write\n        renderer.setRenderTarget(this.transparencyRenderTargetDWTrue);\n        this.scene.traverse((obj)=>{\n            if (obj.material) obj.visible = oldVisibility.get(obj) && obj.material.transparent && obj.material.depthWrite && !obj.userData.treatAsOpaque;\n        });\n        renderer.clear(true, true, true);\n        this.depthCopyPass.render(renderer);\n        renderer.render(this.scene, this.camera);\n        // Restore\n        this.scene.traverse((obj)=>{\n            obj.visible = oldVisibility.get(obj);\n        });\n        renderer.setClearColor(oldClearColor, oldClearAlpha);\n        this.scene.background = oldBackground;\n        renderer.autoClearDepth = oldAutoClearDepth;\n    }\n    configureSampleDependentPasses() {\n        this.firstFrame();\n        this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n        this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n    }\n    configureAOPass(depthBufferType1 = $05f6997e4b65da14$export$ed4ee5d1e55474a5.Default, ortho = false) {\n        this.firstFrame();\n        this.samples = this.generateHemisphereSamples(this.configuration.aoSamples);\n        const e = {\n            ...(0, $1ed45968c1160c3c$export$c9b263b9a17dffd7)\n        };\n        e.fragmentShader = e.fragmentShader.replace(\"16\", this.configuration.aoSamples).replace(\"16.0\", this.configuration.aoSamples + \".0\");\n        if (depthBufferType1 === $05f6997e4b65da14$export$ed4ee5d1e55474a5.Log) e.fragmentShader = \"#define LOGDEPTH\\n\" + e.fragmentShader;\n        else if (depthBufferType1 === $05f6997e4b65da14$export$ed4ee5d1e55474a5.Reverse) e.fragmentShader = \"#define REVERSEDEPTH\\n\" + e.fragmentShader;\n        if (ortho) e.fragmentShader = \"#define ORTHO\\n\" + e.fragmentShader;\n        if (this.configuration.halfRes) e.fragmentShader = \"#define HALFRES\\n\" + e.fragmentShader;\n        if (this.effectShaderQuad) {\n            this.effectShaderQuad.material.dispose();\n            this.effectShaderQuad.material = new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial(e);\n        } else this.effectShaderQuad = new (0, $e4ca8dcb0218f846$export$dcd670d73db751f5)(new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial(e));\n    }\n    configureDenoisePass(depthBufferType1 = $05f6997e4b65da14$export$ed4ee5d1e55474a5.Default, ortho = false) {\n        this.firstFrame();\n        this.samplesDenoise = this.generateDenoiseSamples(this.configuration.denoiseSamples, 11);\n        const p = {\n            ...(0, $e52378cd0f5a973d$export$57856b59f317262e)\n        };\n        p.fragmentShader = p.fragmentShader.replace(\"16\", this.configuration.denoiseSamples);\n        if (depthBufferType1 === $05f6997e4b65da14$export$ed4ee5d1e55474a5.Log) p.fragmentShader = \"#define LOGDEPTH\\n\" + p.fragmentShader;\n        else if (depthBufferType1 === $05f6997e4b65da14$export$ed4ee5d1e55474a5.Reverse) p.fragmentShader = \"#define REVERSEDEPTH\\n\" + p.fragmentShader;\n        if (ortho) p.fragmentShader = \"#define ORTHO\\n\" + p.fragmentShader;\n        if (this.poissonBlurQuad) {\n            this.poissonBlurQuad.material.dispose();\n            this.poissonBlurQuad.material = new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial(p);\n        } else this.poissonBlurQuad = new (0, $e4ca8dcb0218f846$export$dcd670d73db751f5)(new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial(p));\n    }\n    configureEffectCompositer(depthBufferType1 = $05f6997e4b65da14$export$ed4ee5d1e55474a5.Default, ortho = false) {\n        this.firstFrame();\n        const e = {\n            ...(0, $12b21d24d1192a04$export$a815acccbd2c9a49)\n        };\n        if (depthBufferType1 === $05f6997e4b65da14$export$ed4ee5d1e55474a5.Log) e.fragmentShader = \"#define LOGDEPTH\\n\" + e.fragmentShader;\n        else if (depthBufferType1 === $05f6997e4b65da14$export$ed4ee5d1e55474a5.Reverse) e.fragmentShader = \"#define REVERSEDEPTH\\n\" + e.fragmentShader;\n        if (ortho) e.fragmentShader = \"#define ORTHO\\n\" + e.fragmentShader;\n        if (this.configuration.halfRes && this.configuration.depthAwareUpsampling) e.fragmentShader = \"#define HALFRES\\n\" + e.fragmentShader;\n        if (this.effectCompositerQuad) {\n            this.effectCompositerQuad.material.dispose();\n            this.effectCompositerQuad.material = new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial(e);\n        } else this.effectCompositerQuad = new (0, $e4ca8dcb0218f846$export$dcd670d73db751f5)(new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial(e));\n    }\n    /**\n         * \n         * @param {Number} n \n         * @returns {THREE.Vector3[]}\n         */ generateHemisphereSamples(n) {\n        const points = [];\n        for(let k = 0; k < n; k++){\n            const theta = 2.399963 * k;\n            let r = Math.sqrt(k + 0.5) / Math.sqrt(n);\n            const x = r * Math.cos(theta);\n            const y = r * Math.sin(theta);\n            // Project to hemisphere\n            const z = Math.sqrt(1 - (x * x + y * y));\n            points.push(new three__WEBPACK_IMPORTED_MODULE_0__.Vector3(x, y, z));\n        }\n        return points;\n    }\n    /**\n         * \n         * @param {number} numSamples \n         * @param {number} numRings \n         * @returns {THREE.Vector2[]}\n         */ generateDenoiseSamples(numSamples, numRings) {\n        const angleStep = 2 * Math.PI * numRings / numSamples;\n        const invNumSamples = 1.0 / numSamples;\n        const radiusStep = invNumSamples;\n        const samples = [];\n        let radius = invNumSamples;\n        let angle = 0;\n        for(let i = 0; i < numSamples; i++){\n            samples.push(new three__WEBPACK_IMPORTED_MODULE_0__.Vector2(Math.cos(angle), Math.sin(angle)).multiplyScalar(Math.pow(radius, 0.75)));\n            radius += radiusStep;\n            angle += angleStep;\n        }\n        return samples;\n    }\n    setSize(width, height) {\n        this.firstFrame();\n        this.width = width;\n        this.height = height;\n        const c = this.configuration.halfRes ? 0.5 : 1;\n        this.beautyRenderTarget.setSize(width, height);\n        this.writeTargetInternal.setSize(width * c, height * c);\n        this.readTargetInternal.setSize(width * c, height * c);\n        this.accumulationRenderTarget.setSize(width * c, height * c);\n        if (this.configuration.halfRes) this.depthDownsampleTarget.setSize(width * c, height * c);\n        if (this.configuration.transparencyAware) {\n            this.transparencyRenderTargetDWFalse.setSize(width, height);\n            this.transparencyRenderTargetDWTrue.setSize(width, height);\n        }\n    }\n    firstFrame() {\n        this.needsFrame = true;\n    }\n    render(renderer, writeBuffer, readBuffer, deltaTime, maskActive) {\n        if (renderer.capabilities.logarithmicDepthBuffer && this.configuration.depthBufferType !== $05f6997e4b65da14$export$ed4ee5d1e55474a5.Log || renderer.capabilities.reverseDepthBuffer && this.configuration.depthBufferType !== $05f6997e4b65da14$export$ed4ee5d1e55474a5.Reverse) {\n            this.configuration.depthBufferType = renderer.capabilities.logarithmicDepthBuffer ? $05f6997e4b65da14$export$ed4ee5d1e55474a5.Log : renderer.capabilities.reverseDepthBuffer ? $05f6997e4b65da14$export$ed4ee5d1e55474a5.Reverse : $05f6997e4b65da14$export$ed4ee5d1e55474a5.Default;\n            this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n            this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n            this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n        }\n        this.detectTransparency();\n        this.camera.updateMatrixWorld();\n        if (this.lastViewMatrix.equals(this.camera.matrixWorldInverse) && this.lastProjectionMatrix.equals(this.camera.projectionMatrix) && this.configuration.accumulate && !this.needsFrame) this.frame++;\n        else {\n            if (this.configuration.accumulate) {\n                renderer.setRenderTarget(this.accumulationRenderTarget);\n                renderer.clear(true, true, true);\n            }\n            this.frame = 0;\n            this.needsFrame = false;\n        }\n        this.lastViewMatrix.copy(this.camera.matrixWorldInverse);\n        this.lastProjectionMatrix.copy(this.camera.projectionMatrix);\n        let gl;\n        let ext;\n        let timerQuery;\n        if (this.debugMode) {\n            gl = renderer.getContext();\n            ext = gl.getExtension(\"EXT_disjoint_timer_query_webgl2\");\n            if (ext === null) {\n                console.error(\"EXT_disjoint_timer_query_webgl2 not available, disabling debug mode.\");\n                this.debugMode = false;\n            }\n        }\n        if (this.configuration.autoRenderBeauty) {\n            renderer.setRenderTarget(this.beautyRenderTarget);\n            renderer.render(this.scene, this.camera);\n            if (this.configuration.transparencyAware) this.renderTransparency(renderer);\n        }\n        if (this.debugMode) {\n            timerQuery = gl.createQuery();\n            gl.beginQuery(ext.TIME_ELAPSED_EXT, timerQuery);\n        }\n        const xrEnabled = renderer.xr.enabled;\n        renderer.xr.enabled = false;\n        this._r.set(this.width, this.height);\n        let trueRadius = this.configuration.aoRadius;\n        if (this.configuration.halfRes && this.configuration.screenSpaceRadius) trueRadius *= 0.5;\n        if (this.frame < 1024 / this.configuration.aoSamples) {\n            if (this.configuration.halfRes) {\n                renderer.setRenderTarget(this.depthDownsampleTarget);\n                this.depthDownsampleQuad.material.uniforms.sceneDepth.value = this.beautyRenderTarget.depthTexture;\n                this.depthDownsampleQuad.material.uniforms.resolution.value = this._r;\n                this.depthDownsampleQuad.material.uniforms[\"near\"].value = this.camera.near;\n                this.depthDownsampleQuad.material.uniforms[\"far\"].value = this.camera.far;\n                this.depthDownsampleQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n                this.depthDownsampleQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n                this.depthDownsampleQuad.material.uniforms[\"logDepth\"].value = this.configuration.depthBufferType === $05f6997e4b65da14$export$ed4ee5d1e55474a5.Log;\n                this.depthDownsampleQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n                this.depthDownsampleQuad.render(renderer);\n            }\n            this.effectShaderQuad.material.uniforms[\"sceneDiffuse\"].value = this.beautyRenderTarget.texture;\n            this.effectShaderQuad.material.uniforms[\"sceneDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.beautyRenderTarget.depthTexture;\n            this.effectShaderQuad.material.uniforms[\"sceneNormal\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[1] : null;\n            this.effectShaderQuad.material.uniforms[\"projMat\"].value = this.camera.projectionMatrix;\n            this.effectShaderQuad.material.uniforms[\"viewMat\"].value = this.camera.matrixWorldInverse;\n            this.effectShaderQuad.material.uniforms[\"projViewMat\"].value = this.camera.projectionMatrix.clone().multiply(this.camera.matrixWorldInverse.clone());\n            this.effectShaderQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n            this.effectShaderQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n            this.effectShaderQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new three__WEBPACK_IMPORTED_MODULE_0__.Vector3());\n            this.effectShaderQuad.material.uniforms[\"biasAdjustment\"].value = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2(this.configuration.biasOffset, this.configuration.biasMultiplier);\n            this.effectShaderQuad.material.uniforms[\"resolution\"].value = this.configuration.halfRes ? this._r.clone().multiplyScalar(0.5).floor() : this._r;\n            this.effectShaderQuad.material.uniforms[\"time\"].value = performance.now() / 1000;\n            this.effectShaderQuad.material.uniforms[\"samples\"].value = this.samples;\n            this.effectShaderQuad.material.uniforms[\"bluenoise\"].value = this.bluenoise;\n            this.effectShaderQuad.material.uniforms[\"radius\"].value = trueRadius;\n            this.effectShaderQuad.material.uniforms[\"distanceFalloff\"].value = this.configuration.distanceFalloff;\n            this.effectShaderQuad.material.uniforms[\"near\"].value = this.camera.near;\n            this.effectShaderQuad.material.uniforms[\"far\"].value = this.camera.far;\n            this.effectShaderQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n            this.effectShaderQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n            this.effectShaderQuad.material.uniforms[\"frame\"].value = this.frame;\n            // Start the AO\n            renderer.setRenderTarget(this.writeTargetInternal);\n            this.effectShaderQuad.render(renderer);\n            // End the AO\n            // Start the blur\n            for(let i = 0; i < this.configuration.denoiseIterations; i++){\n                [this.writeTargetInternal, this.readTargetInternal] = [\n                    this.readTargetInternal,\n                    this.writeTargetInternal\n                ];\n                this.poissonBlurQuad.material.uniforms[\"tDiffuse\"].value = this.readTargetInternal.texture;\n                this.poissonBlurQuad.material.uniforms[\"sceneDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.beautyRenderTarget.depthTexture;\n                this.poissonBlurQuad.material.uniforms[\"projMat\"].value = this.camera.projectionMatrix;\n                this.poissonBlurQuad.material.uniforms[\"viewMat\"].value = this.camera.matrixWorldInverse;\n                this.poissonBlurQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n                this.poissonBlurQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n                this.poissonBlurQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new three__WEBPACK_IMPORTED_MODULE_0__.Vector3());\n                this.poissonBlurQuad.material.uniforms[\"resolution\"].value = this.configuration.halfRes ? this._r.clone().multiplyScalar(0.5).floor() : this._r;\n                this.poissonBlurQuad.material.uniforms[\"time\"].value = performance.now() / 1000;\n                this.poissonBlurQuad.material.uniforms[\"blueNoise\"].value = this.bluenoise;\n                this.poissonBlurQuad.material.uniforms[\"radius\"].value = this.configuration.denoiseRadius * (this.configuration.halfRes ? 0.5 : 1);\n                this.poissonBlurQuad.material.uniforms[\"worldRadius\"].value = trueRadius;\n                this.poissonBlurQuad.material.uniforms[\"distanceFalloff\"].value = this.configuration.distanceFalloff;\n                this.poissonBlurQuad.material.uniforms[\"index\"].value = i;\n                this.poissonBlurQuad.material.uniforms[\"poissonDisk\"].value = this.samplesDenoise;\n                this.poissonBlurQuad.material.uniforms[\"near\"].value = this.camera.near;\n                this.poissonBlurQuad.material.uniforms[\"far\"].value = this.camera.far;\n                this.poissonBlurQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n                renderer.setRenderTarget(this.writeTargetInternal);\n                this.poissonBlurQuad.render(renderer);\n            }\n            renderer.setRenderTarget(this.accumulationRenderTarget);\n            const oldAutoClear = renderer.autoClear;\n            renderer.autoClear = false;\n            this.accumulationQuad.material.uniforms[\"tDiffuse\"].value = this.writeTargetInternal.texture;\n            this.accumulationQuad.material.uniforms[\"frame\"].value = this.frame;\n            this.accumulationQuad.render(renderer);\n            renderer.autoClear = oldAutoClear;\n        }\n        // Now, we have the blurred AO in writeTargetInternal\n        // End the blur\n        // Start the composition\n        if (this.configuration.transparencyAware) {\n            this.effectCompositerQuad.material.uniforms[\"transparencyDWFalse\"].value = this.transparencyRenderTargetDWFalse.texture;\n            this.effectCompositerQuad.material.uniforms[\"transparencyDWTrue\"].value = this.transparencyRenderTargetDWTrue.texture;\n            this.effectCompositerQuad.material.uniforms[\"transparencyDWTrueDepth\"].value = this.transparencyRenderTargetDWTrue.depthTexture;\n            this.effectCompositerQuad.material.uniforms[\"transparencyAware\"].value = true;\n        }\n        this.effectCompositerQuad.material.uniforms[\"sceneDiffuse\"].value = this.beautyRenderTarget.texture;\n        this.effectCompositerQuad.material.uniforms[\"sceneDepth\"].value = this.beautyRenderTarget.depthTexture;\n        this.effectCompositerQuad.material.uniforms[\"aoTones\"].value = this.configuration.aoTones;\n        this.effectCompositerQuad.material.uniforms[\"near\"].value = this.camera.near;\n        this.effectCompositerQuad.material.uniforms[\"far\"].value = this.camera.far;\n        this.effectCompositerQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n        this.effectCompositerQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n        this.effectCompositerQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n        this.effectCompositerQuad.material.uniforms[\"downsampledDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.beautyRenderTarget.depthTexture;\n        this.effectCompositerQuad.material.uniforms[\"resolution\"].value = this._r;\n        this.effectCompositerQuad.material.uniforms[\"blueNoise\"].value = this.bluenoise;\n        this.effectCompositerQuad.material.uniforms[\"intensity\"].value = this.configuration.intensity;\n        this.effectCompositerQuad.material.uniforms[\"renderMode\"].value = this.configuration.renderMode;\n        this.effectCompositerQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n        this.effectCompositerQuad.material.uniforms[\"radius\"].value = trueRadius;\n        this.effectCompositerQuad.material.uniforms[\"distanceFalloff\"].value = this.configuration.distanceFalloff;\n        this.effectCompositerQuad.material.uniforms[\"gammaCorrection\"].value = this.configuration.gammaCorrection;\n        this.effectCompositerQuad.material.uniforms[\"tDiffuse\"].value = this.accumulationRenderTarget.texture;\n        this.effectCompositerQuad.material.uniforms[\"color\"].value = this._c.copy(this.configuration.color).convertSRGBToLinear();\n        this.effectCompositerQuad.material.uniforms[\"colorMultiply\"].value = this.configuration.colorMultiply;\n        this.effectCompositerQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new three__WEBPACK_IMPORTED_MODULE_0__.Vector3());\n        this.effectCompositerQuad.material.uniforms[\"fog\"].value = !!this.scene.fog;\n        if (this.scene.fog) {\n            if (this.scene.fog.isFog) {\n                this.effectCompositerQuad.material.uniforms[\"fogExp\"].value = false;\n                this.effectCompositerQuad.material.uniforms[\"fogNear\"].value = this.scene.fog.near;\n                this.effectCompositerQuad.material.uniforms[\"fogFar\"].value = this.scene.fog.far;\n            } else if (this.scene.fog.isFogExp2) {\n                this.effectCompositerQuad.material.uniforms[\"fogExp\"].value = true;\n                this.effectCompositerQuad.material.uniforms[\"fogDensity\"].value = this.scene.fog.density;\n            } else console.error(`Unsupported fog type ${this.scene.fog.constructor.name} in SSAOPass.`);\n        }\n        renderer.setRenderTarget(this.renderToScreen ? null : writeBuffer);\n        this.effectCompositerQuad.render(renderer);\n        if (this.debugMode) {\n            gl.endQuery(ext.TIME_ELAPSED_EXT);\n            $05f6997e4b65da14$var$checkTimerQuery(timerQuery, gl, this);\n        }\n        renderer.xr.enabled = xrEnabled;\n    }\n    /**\n         * Enables the debug mode of the AO, meaning the lastTime value will be updated.\n         */ enableDebugMode() {\n        this.debugMode = true;\n    }\n    /**\n         * Disables the debug mode of the AO, meaning the lastTime value will not be updated.\n         */ disableDebugMode() {\n        this.debugMode = false;\n    }\n    /**\n         * Sets the display mode of the AO\n         * @param {\"Combined\" | \"AO\" | \"No AO\" | \"Split\" | \"Split AO\"} mode - The display mode. \n         */ setDisplayMode(mode) {\n        this.configuration.renderMode = [\n            \"Combined\",\n            \"AO\",\n            \"No AO\",\n            \"Split\",\n            \"Split AO\"\n        ].indexOf(mode);\n    }\n    /**\n         * \n         * @param {\"Performance\" | \"Low\" | \"Medium\" | \"High\" | \"Ultra\"} mode \n         */ setQualityMode(mode) {\n        if (mode === \"Performance\") {\n            this.configuration.aoSamples = 8;\n            this.configuration.denoiseSamples = 4;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"Low\") {\n            this.configuration.aoSamples = 16;\n            this.configuration.denoiseSamples = 4;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"Medium\") {\n            this.configuration.aoSamples = 16;\n            this.configuration.denoiseSamples = 8;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"High\") {\n            this.configuration.aoSamples = 64;\n            this.configuration.denoiseSamples = 8;\n            this.configuration.denoiseRadius = 6;\n        } else if (mode === \"Ultra\") {\n            this.configuration.aoSamples = 64;\n            this.configuration.denoiseSamples = 16;\n            this.configuration.denoiseRadius = 6;\n        }\n    }\n}\n\n\n\n//# sourceMappingURL=N8AO.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbjhhby9kaXN0L044QU8uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTY3QjtBQUMvMkI7QUFDMUI7Ozs7O0FBS3BELCtEQUErRCxpREFBcUI7QUFDcEYseUJBQXlCLHlDQUFhO0FBQ3RDO0FBQ0E7QUFDQSwwQ0FBMEMsa0RBQXNCO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLGtEQUFzQjtBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMERBQTBELHFEQUF5QjtBQUNuRjtBQUNBO0FBQ0EseUJBQXlCLHVDQUFXO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOzs7O0FBSUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsdUNBQXVDLDBDQUFjO0FBQ3JELFNBQVM7QUFDVDtBQUNBLHVDQUF1QywwQ0FBYztBQUNyRCxTQUFTO0FBQ1Q7QUFDQSx1Q0FBdUMsMENBQWM7QUFDckQsU0FBUztBQUNUO0FBQ0EsdUNBQXVDLDBDQUFjO0FBQ3JELFNBQVM7QUFDVDtBQUNBLHVDQUF1QywwQ0FBYztBQUNyRCxTQUFTO0FBQ1Q7QUFDQSx1Q0FBdUMsMENBQWM7QUFDckQsU0FBUztBQUNUO0FBQ0EsdUNBQXVDLDBDQUFjO0FBQ3JELFNBQVM7QUFDVDtBQUNBLHVDQUF1QywwQ0FBYztBQUNyRCxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0Q7QUFDaEQ7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLGFBQWE7QUFDbEM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7Ozs7QUFJQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLHVDQUF1QywwQ0FBYztBQUNyRCxTQUFTO0FBQ1Q7QUFDQSx1Q0FBdUMsMENBQWM7QUFDckQsU0FBUztBQUNUO0FBQ0EsdUNBQXVDLDBDQUFjO0FBQ3JELFNBQVM7QUFDVDtBQUNBLHVDQUF1QywwQ0FBYztBQUNyRCxTQUFTO0FBQ1Q7QUFDQSx1Q0FBdUMsMENBQWM7QUFDckQsU0FBUztBQUNUO0FBQ0EsdUNBQXVDLDBDQUFjO0FBQ3JELFNBQVM7QUFDVDtBQUNBLHVDQUF1QywwQ0FBYztBQUNyRCxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0Q7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsVUFBVTtBQUN0QyxnQ0FBZ0MsVUFBVTtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBLFVBQVU7QUFDVjtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7OztBQUlBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLHVDQUF1QywwQ0FBYztBQUNyRCxTQUFTO0FBQ1Q7QUFDQSx1Q0FBdUMsMENBQWM7QUFDckQsU0FBUztBQUNUO0FBQ0EsdUNBQXVDLDBDQUFjO0FBQ3JELFNBQVM7QUFDVDtBQUNBLHVDQUF1QywwQ0FBYztBQUNyRCxTQUFTO0FBQ1Q7QUFDQSx1Q0FBdUMsMENBQWM7QUFDckQsU0FBUztBQUNUO0FBQ0EsdUNBQXVDLDBDQUFjO0FBQ3JELFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxVQUFVO0FBQ1Y7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSx1QkFBdUIsaUJBQWlCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOzs7O0FBSUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSx1Q0FBdUMsMENBQWM7QUFDckQsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLHVDQUF1QywwQ0FBYztBQUNyRCxTQUFTO0FBQ1Q7QUFDQSx1Q0FBdUMsMENBQWM7QUFDckQsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsT0FBTztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7Ozs7Ozs7Ozs7QUFVQTtBQUNBO0FBQ0E7Ozs7O0FBS0Esb0VBQW9FLDJDQUFlO0FBQ25GLHVHQUF1RyxvREFBd0I7QUFDL0gsOERBQThEO0FBQzlEO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSxnQkFBZ0Isb0RBQXdCO0FBQzFDLDhEQUE4RDtBQUM5RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixXQUFXO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFEQUFxRCxRQUFRO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1EQUFtRCxRQUFRO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7O0FBR0E7QUFDQTtBQUNBLFdBQVcsR0FBRztBQUNkLFdBQVcscUJBQXFCO0FBQ2hDLFdBQVcseUJBQXlCO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsNERBQTRELGdEQUFZO0FBQ3hFO0FBQ0E7QUFDQSxlQUFlLGFBQWE7QUFDNUIsZUFBZSxjQUFjO0FBQzdCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkI7QUFDQSxrQkFBa0IsYUFBYTtBQUMvQixrQkFBa0IsY0FBYztBQUNoQyxrQkFBa0IsUUFBUTtBQUMxQixrQkFBa0IsUUFBUTtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsd0NBQVk7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULG1CQUFtQixpQkFBaUI7QUFDcEMsbUJBQW1CLGlCQUFpQjtBQUNwQztBQUNBO0FBQ0Esa0NBQWtDLDBDQUFjO0FBQ2hELHdDQUF3QywwQ0FBYztBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrRUFBK0UsaURBQXFCO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCx1Q0FBdUMsb0RBQXdCO0FBQy9ELHVCQUF1QiwrQ0FBbUI7QUFDMUMsdUJBQXVCLCtDQUFtQjtBQUMxQztBQUNBLG9CQUFvQiw2Q0FBaUI7QUFDckMsU0FBUztBQUNULHNDQUFzQyxvREFBd0I7QUFDOUQsdUJBQXVCLCtDQUFtQjtBQUMxQyx1QkFBdUIsK0NBQW1CO0FBQzFDO0FBQ0Esb0JBQW9CLDZDQUFpQjtBQUNyQyxTQUFTO0FBQ1Qsd0NBQXdDLG9EQUF3QjtBQUNoRSx1QkFBdUIsK0NBQW1CO0FBQzFDLHVCQUF1QiwrQ0FBbUI7QUFDMUM7QUFDQSxTQUFTO0FBQ1QsNENBQTRDLG9EQUF3QjtBQUNwRSx1QkFBdUIsK0NBQW1CO0FBQzFDLHVCQUF1QiwrQ0FBbUI7QUFDMUM7QUFDQSxvQkFBb0IsNkNBQWlCO0FBQ3JDLGtCQUFrQixnREFBb0I7QUFDdEM7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULHVGQUF1RixpREFBcUI7QUFDNUc7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsbUJBQW1CLG1CQUFtQix3QkFBd0IsOENBQWtCO0FBQ2hGLG9DQUFvQywrQ0FBbUI7QUFDdkQsK0JBQStCLGlEQUFxQjtBQUNwRCwrQkFBK0IsaURBQXFCO0FBQ3BELG1DQUFtQyxnREFBb0I7QUFDdkQsbUNBQW1DLGdEQUFvQjtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDBDQUFjO0FBQ3BDLHNCQUFzQix3Q0FBWTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDJDQUFlO0FBQy9CLDREQUE0RCw0Q0FBZ0I7QUFDNUUsMERBQTBELDRDQUFnQjtBQUMxRSwrREFBK0QsZ0RBQW9CO0FBQ25GLCtEQUErRCxnREFBb0I7QUFDbkY7QUFDQSw0REFBNEQsNkNBQWlCO0FBQzdFLDBEQUEwRCxnREFBb0I7QUFDOUUsK0RBQStELGdEQUFvQjtBQUNuRiwrREFBK0QsZ0RBQW9CO0FBQ25GO0FBQ0EsOEZBQThGLGlEQUFxQjtBQUNuSCxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQsb0RBQXdCO0FBQy9FLDJCQUEyQiwrQ0FBbUI7QUFDOUMsMkJBQTJCLGdEQUFvQjtBQUMvQyxzQkFBc0IsZ0RBQW9CO0FBQzFDLHdCQUF3Qiw2Q0FBaUI7QUFDekMsYUFBYTtBQUNiLHNEQUFzRCxvREFBd0I7QUFDOUUsMkJBQTJCLCtDQUFtQjtBQUM5QywyQkFBMkIsZ0RBQW9CO0FBQy9DLHNCQUFzQixnREFBb0I7QUFDMUMsd0JBQXdCLDZDQUFpQjtBQUN6QyxhQUFhO0FBQ2IsbUVBQW1FLCtDQUFtQiwwQkFBMEIsa0RBQXNCO0FBQ3RJLHdGQUF3RixpREFBcUI7QUFDN0c7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlEQUF5RCx3Q0FBWTtBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyx3Q0FBWTtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRCxpREFBcUI7QUFDdEUsVUFBVSxvRkFBb0YsaURBQXFCO0FBQ25IO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdELGlEQUFxQjtBQUNyRSxVQUFVLG1GQUFtRixpREFBcUI7QUFDbEg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFELGlEQUFxQjtBQUMxRSxVQUFVLHdGQUF3RixpREFBcUI7QUFDdkg7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLFFBQVE7QUFDM0IscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQSx1QkFBdUIsT0FBTztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsMENBQWM7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixRQUFRO0FBQzNCLG1CQUFtQixRQUFRO0FBQzNCLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixnQkFBZ0I7QUFDdkMsNkJBQTZCLDBDQUFjO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEdBQTBHLDBDQUFjO0FBQ3hILGtGQUFrRiwwQ0FBYztBQUNoRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQiwwQ0FBMEM7QUFDckU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2R0FBNkcsMENBQWM7QUFDM0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEdBQTBHLDBDQUFjO0FBQ3hIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0EsY0FBYywyQ0FBMkMsaUNBQWlDO0FBQzFGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLG9EQUFvRDtBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLHFEQUFxRDtBQUN4RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7Ozs7QUFLQTtBQUNBO0FBQ0EsV0FBVyxHQUFHO0FBQ2QsV0FBVyxxQkFBcUI7QUFDaEMsV0FBVyxVQUFVO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDREQUE0RCwyRUFBVztBQUN2RTtBQUNBO0FBQ0EsZUFBZSxhQUFhO0FBQzVCLGVBQWUsY0FBYztBQUM3QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCO0FBQ0Esa0JBQWtCLGFBQWE7QUFDL0Isa0JBQWtCLGNBQWM7QUFDaEMsa0JBQWtCLFFBQVE7QUFDMUIsa0JBQWtCLFFBQVE7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLHdDQUFZO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFO0FBQ2pFLGtEQUFrRCxvREFBd0I7QUFDMUUsbUNBQW1DLCtDQUFtQjtBQUN0RCxtQ0FBbUMsZ0RBQW9CO0FBQ3ZELDhCQUE4QixnREFBb0I7QUFDbEQsZ0NBQWdDLDZDQUFpQjtBQUNqRDtBQUNBLHFCQUFxQjtBQUNyQiwrREFBK0QsK0NBQW1CLGtDQUFrQyxxREFBeUIsR0FBRyxrREFBc0I7QUFDdEssMEVBQTBFLHFEQUF5QixHQUFHLDhDQUFrQjtBQUN4SDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsbUJBQW1CLGlCQUFpQjtBQUNwQyxtQkFBbUIsaUJBQWlCO0FBQ3BDO0FBQ0E7QUFDQSxrQ0FBa0MsMENBQWM7QUFDaEQsd0NBQXdDLDBDQUFjO0FBQ3RELHNDQUFzQyxvREFBd0I7QUFDOUQsdUJBQXVCLCtDQUFtQjtBQUMxQyx1QkFBdUIsZ0RBQW9CO0FBQzNDLGtCQUFrQixnREFBb0I7QUFDdEMsb0JBQW9CLDZDQUFpQjtBQUNyQztBQUNBLFNBQVM7QUFDVCxtREFBbUQsK0NBQW1CLDBCQUEwQixrREFBc0I7QUFDdEgsc0RBQXNELDhDQUFrQjtBQUN4RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLG9EQUF3QjtBQUMvRCx1QkFBdUIsK0NBQW1CO0FBQzFDLHVCQUF1QiwrQ0FBbUI7QUFDMUM7QUFDQSxvQkFBb0IsNkNBQWlCO0FBQ3JDLFNBQVM7QUFDVCxzQ0FBc0Msb0RBQXdCO0FBQzlELHVCQUF1QiwrQ0FBbUI7QUFDMUMsdUJBQXVCLCtDQUFtQjtBQUMxQztBQUNBLG9CQUFvQiw2Q0FBaUI7QUFDckMsU0FBUztBQUNULDRDQUE0QyxvREFBd0I7QUFDcEUsdUJBQXVCLCtDQUFtQjtBQUMxQyx1QkFBdUIsK0NBQW1CO0FBQzFDO0FBQ0Esb0JBQW9CLDZDQUFpQjtBQUNyQyxrQkFBa0IsZ0RBQW9CO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxtQkFBbUIsbUJBQW1CLHdCQUF3Qiw4Q0FBa0I7QUFDaEYsdUZBQXVGLGlEQUFxQjtBQUM1RztBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxvQ0FBb0MsK0NBQW1CO0FBQ3ZELCtCQUErQixpREFBcUI7QUFDcEQsK0JBQStCLGlEQUFxQjtBQUNwRCxtQ0FBbUMsZ0RBQW9CO0FBQ3ZELG1DQUFtQyxnREFBb0I7QUFDdkQ7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDBDQUFjO0FBQ3BDLHNCQUFzQix3Q0FBWTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDJDQUFlO0FBQy9CLDREQUE0RCw0Q0FBZ0I7QUFDNUUsMERBQTBELDRDQUFnQjtBQUMxRSwrREFBK0QsZ0RBQW9CO0FBQ25GLCtEQUErRCxnREFBb0I7QUFDbkY7QUFDQSw0REFBNEQsNkNBQWlCO0FBQzdFLDBEQUEwRCxnREFBb0I7QUFDOUUsK0RBQStELGdEQUFvQjtBQUNuRiwrREFBK0QsZ0RBQW9CO0FBQ25GO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4RkFBOEYsaURBQXFCO0FBQ25ILFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVEQUF1RCxvREFBd0I7QUFDL0UsMkJBQTJCLCtDQUFtQjtBQUM5QywyQkFBMkIsZ0RBQW9CO0FBQy9DLHNCQUFzQixnREFBb0I7QUFDMUMsd0JBQXdCLDZDQUFpQjtBQUN6QyxhQUFhO0FBQ2Isc0RBQXNELG9EQUF3QjtBQUM5RSwyQkFBMkIsK0NBQW1CO0FBQzlDLDJCQUEyQixnREFBb0I7QUFDL0Msc0JBQXNCLGdEQUFvQjtBQUMxQyx3QkFBd0IsNkNBQWlCO0FBQ3pDLGFBQWE7QUFDYixtRUFBbUUsK0NBQW1CLDBCQUEwQixrREFBc0I7QUFDdEksd0ZBQXdGLGlEQUFxQjtBQUM3RztBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseURBQXlELHdDQUFZO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLHdDQUFZO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpREFBaUQsaURBQXFCO0FBQ3RFLFVBQVUsb0ZBQW9GLGlEQUFxQjtBQUNuSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdEQUFnRCxpREFBcUI7QUFDckUsVUFBVSxtRkFBbUYsaURBQXFCO0FBQ2xIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFEQUFxRCxpREFBcUI7QUFDMUUsVUFBVSx3RkFBd0YsaURBQXFCO0FBQ3ZIO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixRQUFRO0FBQzNCLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0EsdUJBQXVCLE9BQU87QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLDBDQUFjO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsUUFBUTtBQUMzQixtQkFBbUIsUUFBUTtBQUMzQixxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsZ0JBQWdCO0FBQ3ZDLDZCQUE2QiwwQ0FBYztBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEdBQTBHLDBDQUFjO0FBQ3hILGtGQUFrRiwwQ0FBYztBQUNoRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQiwwQ0FBMEM7QUFDckU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2R0FBNkcsMENBQWM7QUFDM0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEdBQTBHLDBDQUFjO0FBQ3hIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0EsY0FBYywyQ0FBMkMsaUNBQWlDO0FBQzFGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixvREFBb0Q7QUFDdkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixxREFBcUQ7QUFDeEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7OztBQUdrTDtBQUNsTCIsInNvdXJjZXMiOlsiL2hvbWUvYWthc2gvY2hldGFuL3NwZWN0cmFtaW50L25vZGVfbW9kdWxlcy9uOGFvL2Rpc3QvTjhBTy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0NvbG9yIGFzICQ1V2hlMyRDb2xvciwgV2ViR0xSZW5kZXJUYXJnZXQgYXMgJDVXaGUzJFdlYkdMUmVuZGVyVGFyZ2V0LCBMaW5lYXJGaWx0ZXIgYXMgJDVXaGUzJExpbmVhckZpbHRlciwgTmVhcmVzdEZpbHRlciBhcyAkNVdoZTMkTmVhcmVzdEZpbHRlciwgSGFsZkZsb2F0VHlwZSBhcyAkNVdoZTMkSGFsZkZsb2F0VHlwZSwgUkdCQUZvcm1hdCBhcyAkNVdoZTMkUkdCQUZvcm1hdCwgRGVwdGhUZXh0dXJlIGFzICQ1V2hlMyREZXB0aFRleHR1cmUsIFVuc2lnbmVkSW50MjQ4VHlwZSBhcyAkNVdoZTMkVW5zaWduZWRJbnQyNDhUeXBlLCBVbnNpZ25lZEludFR5cGUgYXMgJDVXaGUzJFVuc2lnbmVkSW50VHlwZSwgRGVwdGhTdGVuY2lsRm9ybWF0IGFzICQ1V2hlMyREZXB0aFN0ZW5jaWxGb3JtYXQsIERlcHRoRm9ybWF0IGFzICQ1V2hlMyREZXB0aEZvcm1hdCwgTWF0cml4NCBhcyAkNVdoZTMkTWF0cml4NCwgRGF0YVRleHR1cmUgYXMgJDVXaGUzJERhdGFUZXh0dXJlLCBTaGFkZXJNYXRlcmlhbCBhcyAkNVdoZTMkU2hhZGVyTWF0ZXJpYWwsIE5vQ29sb3JTcGFjZSBhcyAkNVdoZTMkTm9Db2xvclNwYWNlLCBSZXBlYXRXcmFwcGluZyBhcyAkNVdoZTMkUmVwZWF0V3JhcHBpbmcsIFZlY3RvcjIgYXMgJDVXaGUzJFZlY3RvcjIsIFJFVklTSU9OIGFzICQ1V2hlMyRSRVZJU0lPTiwgUmVkRm9ybWF0IGFzICQ1V2hlMyRSZWRGb3JtYXQsIEZsb2F0VHlwZSBhcyAkNVdoZTMkRmxvYXRUeXBlLCBWZWN0b3IzIGFzICQ1V2hlMyRWZWN0b3IzLCBTcGhlcmUgYXMgJDVXaGUzJFNwaGVyZSwgQnVmZmVyQXR0cmlidXRlIGFzICQ1V2hlMyRCdWZmZXJBdHRyaWJ1dGUsIEJ1ZmZlckdlb21ldHJ5IGFzICQ1V2hlMyRCdWZmZXJHZW9tZXRyeSwgT3J0aG9ncmFwaGljQ2FtZXJhIGFzICQ1V2hlMyRPcnRob2dyYXBoaWNDYW1lcmEsIE1lc2ggYXMgJDVXaGUzJE1lc2h9IGZyb20gXCJ0aHJlZVwiO1xuaW1wb3J0IHtQYXNzIGFzICQ1V2hlMyRQYXNzfSBmcm9tIFwidGhyZWUvZXhhbXBsZXMvanNtL3Bvc3Rwcm9jZXNzaW5nL1Bhc3MuanNcIjtcbmltcG9ydCB7UGFzcyBhcyAkNVdoZTMkUGFzczF9IGZyb20gXCJwb3N0cHJvY2Vzc2luZ1wiO1xuXG5cblxuXG5jbGFzcyAkZTRjYThkY2IwMjE4Zjg0NiR2YXIkRnVsbFNjcmVlblRyaWFuZ2xlR2VvbWV0cnkgZXh0ZW5kcyAkNVdoZTMkQnVmZmVyR2VvbWV0cnkge1xuICAgIGJvdW5kaW5nU3BoZXJlID0gbmV3ICQ1V2hlMyRTcGhlcmUoKTtcbiAgICBjb25zdHJ1Y3Rvcigpe1xuICAgICAgICBzdXBlcigpO1xuICAgICAgICB0aGlzLnNldEF0dHJpYnV0ZShcInBvc2l0aW9uXCIsIG5ldyAkNVdoZTMkQnVmZmVyQXR0cmlidXRlKG5ldyBGbG9hdDMyQXJyYXkoW1xuICAgICAgICAgICAgLTEsXG4gICAgICAgICAgICAtMSxcbiAgICAgICAgICAgIDMsXG4gICAgICAgICAgICAtMSxcbiAgICAgICAgICAgIC0xLFxuICAgICAgICAgICAgM1xuICAgICAgICBdKSwgMikpO1xuICAgICAgICB0aGlzLnNldEF0dHJpYnV0ZShcInV2XCIsIG5ldyAkNVdoZTMkQnVmZmVyQXR0cmlidXRlKG5ldyBGbG9hdDMyQXJyYXkoW1xuICAgICAgICAgICAgMCxcbiAgICAgICAgICAgIDAsXG4gICAgICAgICAgICAyLFxuICAgICAgICAgICAgMCxcbiAgICAgICAgICAgIDAsXG4gICAgICAgICAgICAyXG4gICAgICAgIF0pLCAyKSk7XG4gICAgfVxuICAgIGNvbXB1dGVCb3VuZGluZ1NwaGVyZSgpIHt9XG59XG5jb25zdCAkZTRjYThkY2IwMjE4Zjg0NiR2YXIkX2dlb21ldHJ5ID0gLyogQF9fUFVSRV9fICovIG5ldyAkZTRjYThkY2IwMjE4Zjg0NiR2YXIkRnVsbFNjcmVlblRyaWFuZ2xlR2VvbWV0cnkoKTtcbmNvbnN0ICRlNGNhOGRjYjAyMThmODQ2JHZhciRfY2FtZXJhID0gLyogQF9fUFVSRV9fICovIG5ldyAkNVdoZTMkT3J0aG9ncmFwaGljQ2FtZXJhKCk7XG5jbGFzcyAkZTRjYThkY2IwMjE4Zjg0NiRleHBvcnQkZGNkNjcwZDczZGI3NTFmNSB7XG4gICAgY29uc3RydWN0b3IobWF0ZXJpYWwpe1xuICAgICAgICB0aGlzLl9tZXNoID0gbmV3ICQ1V2hlMyRNZXNoKCRlNGNhOGRjYjAyMThmODQ2JHZhciRfZ2VvbWV0cnksIG1hdGVyaWFsKTtcbiAgICAgICAgdGhpcy5fbWVzaC5mcnVzdHVtQ3VsbGVkID0gZmFsc2U7XG4gICAgfVxuICAgIHJlbmRlcihyZW5kZXJlcikge1xuICAgICAgICByZW5kZXJlci5yZW5kZXIodGhpcy5fbWVzaCwgJGU0Y2E4ZGNiMDIxOGY4NDYkdmFyJF9jYW1lcmEpO1xuICAgIH1cbiAgICBnZXQgbWF0ZXJpYWwoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9tZXNoLm1hdGVyaWFsO1xuICAgIH1cbiAgICBzZXQgbWF0ZXJpYWwodmFsdWUpIHtcbiAgICAgICAgdGhpcy5fbWVzaC5tYXRlcmlhbCA9IHZhbHVlO1xuICAgIH1cbiAgICBkaXNwb3NlKCkge1xuICAgICAgICB0aGlzLl9tZXNoLm1hdGVyaWFsLmRpc3Bvc2UoKTtcbiAgICAgICAgdGhpcy5fbWVzaC5nZW9tZXRyeS5kaXNwb3NlKCk7XG4gICAgfVxufVxuXG5cblxuY29uc3QgJDFlZDQ1OTY4YzExNjBjM2MkZXhwb3J0JGM5YjI2M2I5YTE3ZGZmZDcgPSB7XG4gICAgdW5pZm9ybXM6IHtcbiAgICAgICAgXCJzY2VuZURpZmZ1c2VcIjoge1xuICAgICAgICAgICAgdmFsdWU6IG51bGxcbiAgICAgICAgfSxcbiAgICAgICAgXCJzY2VuZURlcHRoXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiBudWxsXG4gICAgICAgIH0sXG4gICAgICAgIFwic2NlbmVOb3JtYWxcIjoge1xuICAgICAgICAgICAgdmFsdWU6IG51bGxcbiAgICAgICAgfSxcbiAgICAgICAgXCJwcm9qTWF0XCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiAvKiBAX19QVVJFX18gKi8gbmV3ICQ1V2hlMyRNYXRyaXg0KClcbiAgICAgICAgfSxcbiAgICAgICAgXCJ2aWV3TWF0XCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiAvKiBAX19QVVJFX18gKi8gbmV3ICQ1V2hlMyRNYXRyaXg0KClcbiAgICAgICAgfSxcbiAgICAgICAgXCJwcm9qVmlld01hdFwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogLyogQF9fUFVSRV9fICovIG5ldyAkNVdoZTMkTWF0cml4NCgpXG4gICAgICAgIH0sXG4gICAgICAgIFwicHJvamVjdGlvbk1hdHJpeEludlwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogLyogQF9fUFVSRV9fICovIG5ldyAkNVdoZTMkTWF0cml4NCgpXG4gICAgICAgIH0sXG4gICAgICAgIFwidmlld01hdHJpeEludlwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogLyogQF9fUFVSRV9fICovIG5ldyAkNVdoZTMkTWF0cml4NCgpXG4gICAgICAgIH0sXG4gICAgICAgIFwiY2FtZXJhUG9zXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiAvKiBAX19QVVJFX18gKi8gbmV3ICQ1V2hlMyRWZWN0b3IzKClcbiAgICAgICAgfSxcbiAgICAgICAgXCJyZXNvbHV0aW9uXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiAvKiBAX19QVVJFX18gKi8gbmV3ICQ1V2hlMyRWZWN0b3IyKClcbiAgICAgICAgfSxcbiAgICAgICAgXCJiaWFzQWRqdXN0bWVudFwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogLyogQF9fUFVSRV9fICovIG5ldyAkNVdoZTMkVmVjdG9yMigpXG4gICAgICAgIH0sXG4gICAgICAgIFwidGltZVwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogMC4wXG4gICAgICAgIH0sXG4gICAgICAgIFwic2FtcGxlc1wiOiB7XG4gICAgICAgICAgICB2YWx1ZTogW11cbiAgICAgICAgfSxcbiAgICAgICAgXCJibHVlbm9pc2VcIjoge1xuICAgICAgICAgICAgdmFsdWU6IG51bGxcbiAgICAgICAgfSxcbiAgICAgICAgXCJkaXN0YW5jZUZhbGxvZmZcIjoge1xuICAgICAgICAgICAgdmFsdWU6IDEuMFxuICAgICAgICB9LFxuICAgICAgICBcInJhZGl1c1wiOiB7XG4gICAgICAgICAgICB2YWx1ZTogNS4wXG4gICAgICAgIH0sXG4gICAgICAgIFwibmVhclwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogMC4xXG4gICAgICAgIH0sXG4gICAgICAgIFwiZmFyXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiAxMDAwLjBcbiAgICAgICAgfSxcbiAgICAgICAgXCJvcnRob1wiOiB7XG4gICAgICAgICAgICB2YWx1ZTogZmFsc2VcbiAgICAgICAgfSxcbiAgICAgICAgXCJzY3JlZW5TcGFjZVJhZGl1c1wiOiB7XG4gICAgICAgICAgICB2YWx1ZTogZmFsc2VcbiAgICAgICAgfSxcbiAgICAgICAgXCJmcmFtZVwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogMC4wXG4gICAgICAgIH1cbiAgICB9LFxuICAgIGRlcHRoV3JpdGU6IGZhbHNlLFxuICAgIGRlcHRoVGVzdDogZmFsc2UsXG4gICAgdmVydGV4U2hhZGVyOiAvKiBnbHNsICovIGBcbnZhcnlpbmcgdmVjMiB2VXY7XG52b2lkIG1haW4oKSB7XG4gIHZVdiA9IHV2O1xuICBnbF9Qb3NpdGlvbiA9IHZlYzQocG9zaXRpb24sIDEpO1xufWAsXG4gICAgZnJhZ21lbnRTaGFkZXI6IC8qIGdsc2wgKi8gYFxuICAgICNkZWZpbmUgU0FNUExFUyAxNlxuICAgICNkZWZpbmUgRlNBTVBMRVMgMTYuMFxudW5pZm9ybSBzYW1wbGVyMkQgc2NlbmVEaWZmdXNlO1xudW5pZm9ybSBoaWdocCBzYW1wbGVyMkQgc2NlbmVOb3JtYWw7XG51bmlmb3JtIGhpZ2hwIHNhbXBsZXIyRCBzY2VuZURlcHRoO1xudW5pZm9ybSBtYXQ0IHByb2plY3Rpb25NYXRyaXhJbnY7XG51bmlmb3JtIG1hdDQgdmlld01hdHJpeEludjtcbnVuaWZvcm0gbWF0NCBwcm9qTWF0O1xudW5pZm9ybSBtYXQ0IHZpZXdNYXQ7XG51bmlmb3JtIG1hdDQgcHJvalZpZXdNYXQ7XG51bmlmb3JtIHZlYzMgY2FtZXJhUG9zO1xudW5pZm9ybSB2ZWMyIHJlc29sdXRpb247XG51bmlmb3JtIHZlYzIgYmlhc0FkanVzdG1lbnQ7XG51bmlmb3JtIGZsb2F0IHRpbWU7XG51bmlmb3JtIHZlYzNbU0FNUExFU10gc2FtcGxlcztcbnVuaWZvcm0gZmxvYXQgcmFkaXVzO1xudW5pZm9ybSBmbG9hdCBkaXN0YW5jZUZhbGxvZmY7XG51bmlmb3JtIGZsb2F0IG5lYXI7XG51bmlmb3JtIGZsb2F0IGZhcjtcbnVuaWZvcm0gZmxvYXQgZnJhbWU7XG51bmlmb3JtIGJvb2wgb3J0aG87XG51bmlmb3JtIGJvb2wgc2NyZWVuU3BhY2VSYWRpdXM7XG51bmlmb3JtIHNhbXBsZXIyRCBibHVlbm9pc2U7XG4gICAgdmFyeWluZyB2ZWMyIHZVdjtcbiAgICBoaWdocCBmbG9hdCBsaW5lYXJpemVfZGVwdGgoaGlnaHAgZmxvYXQgZCwgaGlnaHAgZmxvYXQgek5lYXIsaGlnaHAgZmxvYXQgekZhcilcbiAgICB7XG4gICAgICAgIHJldHVybiAoekZhciAqIHpOZWFyKSAvICh6RmFyIC0gZCAqICh6RmFyIC0gek5lYXIpKTtcbiAgICB9XG4gICAgaGlnaHAgZmxvYXQgbGluZWFyaXplX2RlcHRoX29ydGhvKGhpZ2hwIGZsb2F0IGQsIGhpZ2hwIGZsb2F0IG5lYXJaLCBoaWdocCBmbG9hdCBmYXJaKSB7XG4gICAgICByZXR1cm4gbmVhclogKyAoZmFyWiAtIG5lYXJaKSAqIGQ7XG4gICAgfVxuICAgIGhpZ2hwIGZsb2F0IGxpbmVhcml6ZV9kZXB0aF9sb2coaGlnaHAgZmxvYXQgZCwgaGlnaHAgZmxvYXQgbmVhclosaGlnaHAgZmxvYXQgZmFyWikge1xuICAgICAgZmxvYXQgZGVwdGggPSBwb3coMi4wLCBkICogbG9nMihmYXJaICsgMS4wKSkgLSAxLjA7XG4gICAgICBmbG9hdCBhID0gZmFyWiAvIChmYXJaIC0gbmVhclopO1xuICAgICAgZmxvYXQgYiA9IGZhclogKiBuZWFyWiAvIChuZWFyWiAtIGZhclopO1xuICAgICAgZmxvYXQgbGluRGVwdGggPSBhICsgYiAvIGRlcHRoO1xuICAgICAgLypyZXR1cm4gb3J0aG8gPyBsaW5lYXJpemVfZGVwdGhfb3J0aG8oXG4gICAgICAgIGxpbkRlcHRoLFxuICAgICAgICBuZWFyWixcbiAgICAgICAgZmFyWlxuICAgICAgKSA6bGluZWFyaXplX2RlcHRoKGxpbkRlcHRoLCBuZWFyWiwgZmFyWik7Ki9cbiAgICAgICAjaWZkZWYgT1JUSE9cblxuICAgICAgIHJldHVybiBsaW5lYXJpemVfZGVwdGhfb3J0aG8oZCwgbmVhclosIGZhclopO1xuXG4gICAgICAgICNlbHNlXG4gICAgICAgIHJldHVybiBsaW5lYXJpemVfZGVwdGgobGluRGVwdGgsIG5lYXJaLCBmYXJaKTtcbiAgICAgICAgI2VuZGlmXG4gICAgfVxuXG4gICAgdmVjMyBnZXRXb3JsZFBvc0xvZyh2ZWMzIHBvc1MpIHtcbiAgICAgIHZlYzIgdXYgPSBwb3NTLnh5O1xuICAgICAgZmxvYXQgeiA9IHBvc1MuejtcbiAgICAgIGZsb2F0IG5lYXJaID1uZWFyO1xuICAgICAgZmxvYXQgZmFyWiA9IGZhcjtcbiAgICAgIGZsb2F0IGRlcHRoID0gcG93KDIuMCwgeiAqIGxvZzIoZmFyWiArIDEuMCkpIC0gMS4wO1xuICAgICAgZmxvYXQgYSA9IGZhclogLyAoZmFyWiAtIG5lYXJaKTtcbiAgICAgIGZsb2F0IGIgPSBmYXJaICogbmVhclogLyAobmVhclogLSBmYXJaKTtcbiAgICAgIGZsb2F0IGxpbkRlcHRoID0gYSArIGIgLyBkZXB0aDtcbiAgICAgIHZlYzQgY2xpcFZlYyA9IHZlYzQodXYsIGxpbkRlcHRoLCAxLjApICogMi4wIC0gMS4wO1xuICAgICAgdmVjNCB3cG9zID0gcHJvamVjdGlvbk1hdHJpeEludiAqIGNsaXBWZWM7XG4gICAgICByZXR1cm4gd3Bvcy54eXogLyB3cG9zLnc7XG4gICAgfVxuICAgIHZlYzMgZ2V0V29ybGRQb3MoZmxvYXQgZGVwdGgsIHZlYzIgY29vcmQpIHtcbiAgICAgICNpZmRlZiBMT0dERVBUSFxuICAgICAgICAjaWZuZGVmIE9SVEhPXG4gICAgICAgICAgcmV0dXJuIGdldFdvcmxkUG9zTG9nKHZlYzMoY29vcmQsIGRlcHRoKSk7XG4gICAgICAgICNlbmRpZlxuICAgICAgI2VuZGlmXG4gICAgICBmbG9hdCB6ID0gZGVwdGggKiAyLjAgLSAxLjA7XG4gICAgICB2ZWM0IGNsaXBTcGFjZVBvc2l0aW9uID0gdmVjNChjb29yZCAqIDIuMCAtIDEuMCwgeiwgMS4wKTtcbiAgICAgIHZlYzQgdmlld1NwYWNlUG9zaXRpb24gPSBwcm9qZWN0aW9uTWF0cml4SW52ICogY2xpcFNwYWNlUG9zaXRpb247XG4gICAgICAvLyBQZXJzcGVjdGl2ZSBkaXZpc2lvblxuICAgICB2ZWM0IHdvcmxkU3BhY2VQb3NpdGlvbiA9IHZpZXdTcGFjZVBvc2l0aW9uO1xuICAgICB3b3JsZFNwYWNlUG9zaXRpb24ueHl6IC89IHdvcmxkU3BhY2VQb3NpdGlvbi53O1xuICAgICAgcmV0dXJuIHdvcmxkU3BhY2VQb3NpdGlvbi54eXo7XG4gIH1cblxuICB2ZWMzIGNvbXB1dGVOb3JtYWwodmVjMyB3b3JsZFBvcywgdmVjMiB2VXYpIHtcbiAgICBpdmVjMiBwID0gaXZlYzIodlV2ICogcmVzb2x1dGlvbik7XG4gICAgI2lmZGVmIFJFVkVSU0VERVBUSFxuICAgIGZsb2F0IGMwID0gMS4wIC0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwLCAwKS54O1xuICAgIGZsb2F0IGwyID0gMS4wIC0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwIC0gaXZlYzIoMiwgMCksIDApLng7XG4gICAgZmxvYXQgbDEgPSAxLjAgLSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgLSBpdmVjMigxLCAwKSwgMCkueDtcbiAgICBmbG9hdCByMSA9IDEuMCAtIHRleGVsRmV0Y2goc2NlbmVEZXB0aCwgcCArIGl2ZWMyKDEsIDApLCAwKS54O1xuICAgIGZsb2F0IHIyID0gMS4wIC0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwICsgaXZlYzIoMiwgMCksIDApLng7XG4gICAgZmxvYXQgYjIgPSAxLjAgLSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgLSBpdmVjMigwLCAyKSwgMCkueDtcbiAgICBmbG9hdCBiMSA9IDEuMCAtIHRleGVsRmV0Y2goc2NlbmVEZXB0aCwgcCAtIGl2ZWMyKDAsIDEpLCAwKS54O1xuICAgIGZsb2F0IHQxID0gMS4wIC0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwICsgaXZlYzIoMCwgMSksIDApLng7XG4gICAgZmxvYXQgdDIgPSAxLjAgLSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgKyBpdmVjMigwLCAyKSwgMCkueDtcbiAgICAjZWxzZVxuICAgIGZsb2F0IGMwID0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwLCAwKS54O1xuICAgIGZsb2F0IGwyID0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwIC0gaXZlYzIoMiwgMCksIDApLng7XG4gICAgZmxvYXQgbDEgPSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgLSBpdmVjMigxLCAwKSwgMCkueDtcbiAgICBmbG9hdCByMSA9IHRleGVsRmV0Y2goc2NlbmVEZXB0aCwgcCArIGl2ZWMyKDEsIDApLCAwKS54O1xuICAgIGZsb2F0IHIyID0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwICsgaXZlYzIoMiwgMCksIDApLng7XG4gICAgZmxvYXQgYjIgPSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgLSBpdmVjMigwLCAyKSwgMCkueDtcbiAgICBmbG9hdCBiMSA9IHRleGVsRmV0Y2goc2NlbmVEZXB0aCwgcCAtIGl2ZWMyKDAsIDEpLCAwKS54O1xuICAgIGZsb2F0IHQxID0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwICsgaXZlYzIoMCwgMSksIDApLng7XG4gICAgZmxvYXQgdDIgPSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgKyBpdmVjMigwLCAyKSwgMCkueDtcbiAgICAjZW5kaWZcblxuICAgIGZsb2F0IGRsID0gYWJzKCgyLjAgKiBsMSAtIGwyKSAtIGMwKTtcbiAgICBmbG9hdCBkciA9IGFicygoMi4wICogcjEgLSByMikgLSBjMCk7XG4gICAgZmxvYXQgZGIgPSBhYnMoKDIuMCAqIGIxIC0gYjIpIC0gYzApO1xuICAgIGZsb2F0IGR0ID0gYWJzKCgyLjAgKiB0MSAtIHQyKSAtIGMwKTtcblxuICAgIHZlYzMgY2UgPSBnZXRXb3JsZFBvcyhjMCwgdlV2KS54eXo7XG5cbiAgICB2ZWMzIGRwZHggPSAoZGwgPCBkcikgPyBjZSAtIGdldFdvcmxkUG9zKGwxLCAodlV2IC0gdmVjMigxLjAgLyByZXNvbHV0aW9uLngsIDAuMCkpKS54eXpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiAtY2UgKyBnZXRXb3JsZFBvcyhyMSwgKHZVdiArIHZlYzIoMS4wIC8gcmVzb2x1dGlvbi54LCAwLjApKSkueHl6O1xuICAgIHZlYzMgZHBkeSA9IChkYiA8IGR0KSA/IGNlIC0gZ2V0V29ybGRQb3MoYjEsICh2VXYgLSB2ZWMyKDAuMCwgMS4wIC8gcmVzb2x1dGlvbi55KSkpLnh5elxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IC1jZSArIGdldFdvcmxkUG9zKHQxLCAodlV2ICsgdmVjMigwLjAsIDEuMCAvIHJlc29sdXRpb24ueSkpKS54eXo7XG5cbiAgICByZXR1cm4gbm9ybWFsaXplKGNyb3NzKGRwZHgsIGRwZHkpKTtcbn1cblxubWF0MyBtYWtlUm90YXRpb25aKGZsb2F0IHRoZXRhKSB7XG5cdGZsb2F0IGMgPSBjb3ModGhldGEpO1xuXHRmbG9hdCBzID0gc2luKHRoZXRhKTtcblx0cmV0dXJuIG1hdDMoYywgLSBzLCAwLFxuXHRcdFx0cywgIGMsIDAsXG5cdFx0XHQwLCAgMCwgMSk7XG4gIH1cblxudm9pZCBtYWluKCkge1xuICAgICAgdmVjNCBkaWZmdXNlID0gdGV4dHVyZTJEKHNjZW5lRGlmZnVzZSwgdlV2KTtcbiAgICAgICNpZmRlZiBSRVZFUlNFREVQVEhcbiAgICAgIGZsb2F0IGRlcHRoID0gMS4wIC0gdGV4dHVyZTJEKHNjZW5lRGVwdGgsIHZVdikueDtcbiAgICAgICNlbHNlXG4gICAgICBmbG9hdCBkZXB0aCA9IHRleHR1cmUyRChzY2VuZURlcHRoLCB2VXYpLng7XG4gICAgICAjZW5kaWZcbiAgICAgIGlmIChkZXB0aCA9PSAxLjApIHtcbiAgICAgICAgZ2xfRnJhZ0NvbG9yID0gdmVjNCh2ZWMzKDEuMCksIDEuMCk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIHZlYzMgd29ybGRQb3MgPSBnZXRXb3JsZFBvcyhkZXB0aCwgdlV2KTtcbiAgICAgICNpZmRlZiBIQUxGUkVTXG4gICAgICAgIHZlYzMgbm9ybWFsID0gdGV4dHVyZTJEKHNjZW5lTm9ybWFsLCB2VXYpLnJnYjtcbiAgICAgICNlbHNlXG4gICAgICAgIHZlYzMgbm9ybWFsID0gY29tcHV0ZU5vcm1hbCh3b3JsZFBvcywgdlV2KTtcbiAgICAgICNlbmRpZlxuICAgICAgdmVjNCBub2lzZSA9IHRleHR1cmUyRChibHVlbm9pc2UsIGdsX0ZyYWdDb29yZC54eSAvIDEyOC4wKTtcbiAgICAgIHZlYzIgaGFybW9uaW91c051bWJlcnMgPSB2ZWMyKFxuICAgICAgICAxLjYxODAzMzk4ODc0OTg5NSxcbiAgICAgICAgMS4zMjQ3MTc5NTcyNDQ3NDZcbiAgICAgICk7XG4gICAgICBub2lzZS5yZyArPSBoYXJtb25pb3VzTnVtYmVycyAqIGZyYW1lO1xuICAgICAgbm9pc2UucmcgPSBmcmFjdChub2lzZS5yZyk7XG4gICAgICAgIHZlYzMgaGVscGVyVmVjID0gdmVjMygwLjAsIDEuMCwgMC4wKTtcbiAgICAgICAgaWYgKGRvdChoZWxwZXJWZWMsIG5vcm1hbCkgPiAwLjk5KSB7XG4gICAgICAgICAgaGVscGVyVmVjID0gdmVjMygxLjAsIDAuMCwgMC4wKTtcbiAgICAgICAgfVxuICAgICAgICB2ZWMzIHRhbmdlbnQgPSBub3JtYWxpemUoY3Jvc3MoaGVscGVyVmVjLCBub3JtYWwpKTtcbiAgICAgICAgdmVjMyBiaXRhbmdlbnQgPSBjcm9zcyhub3JtYWwsIHRhbmdlbnQpO1xuICAgICAgICBtZWRpdW1wIG1hdDMgdGJuID0gbWF0Myh0YW5nZW50LCBiaXRhbmdlbnQsIG5vcm1hbCkgKiAgbWFrZVJvdGF0aW9uWiggbm9pc2UuciAqIDMuMTQxNTk2MiAqIDIuMCkgO1xuXG4gICAgICBtZWRpdW1wIGZsb2F0IG9jY2x1ZGVkID0gMC4wO1xuICAgICAgbWVkaXVtcCBmbG9hdCB0b3RhbFdlaWdodCA9IDAuMDtcbiAgICAgIGZsb2F0IHJhZGl1c1RvVXNlID0gc2NyZWVuU3BhY2VSYWRpdXMgPyBkaXN0YW5jZShcbiAgICAgICAgd29ybGRQb3MsXG4gICAgICAgIGdldFdvcmxkUG9zKGRlcHRoLCB2VXYgK1xuICAgICAgICAgIHZlYzIocmFkaXVzLCAwLjApIC8gcmVzb2x1dGlvbilcbiAgICAgICkgOiByYWRpdXM7XG4gICAgICBmbG9hdCBkaXN0YW5jZUZhbGxvZmZUb1VzZSA9c2NyZWVuU3BhY2VSYWRpdXMgP1xuICAgICAgICAgIHJhZGl1c1RvVXNlICogZGlzdGFuY2VGYWxsb2ZmXG4gICAgICA6IHJhZGl1c1RvVXNlICogZGlzdGFuY2VGYWxsb2ZmICogMC4yO1xuICAgICAgZmxvYXQgYmlhcyA9IChtaW4oXG4gICAgICAgIDAuMSxcbiAgICAgICAgZGlzdGFuY2VGYWxsb2ZmVG9Vc2UgKiAwLjFcbiAgICAgICkgLyBuZWFyKSAqIGZ3aWR0aChkaXN0YW5jZSh3b3JsZFBvcywgY2FtZXJhUG9zKSkgLyByYWRpdXNUb1VzZTtcbiAgICAgIGJpYXMgPSBiaWFzQWRqdXN0bWVudC54ICsgYmlhc0FkanVzdG1lbnQueSAqIGJpYXM7XG4gICAgICBtZWRpdW1wIGZsb2F0IG9mZnNldE1vdmUgPSBub2lzZS5nO1xuICAgICAgbWVkaXVtcCBmbG9hdCBvZmZzZXRNb3ZlSW52ID0gMS4wIC8gRlNBTVBMRVM7XG4gICAgICBmbG9hdCBmYXJUaW1lc05lYXIgPSBmYXIgKiBuZWFyO1xuICAgICAgZmxvYXQgZmFyTWludXNOZWFyID0gZmFyIC0gbmVhcjtcbiAgICAgIFxuICAgICAgZm9yKGludCBpID0gMDsgaSA8IFNBTVBMRVM7IGkrKykge1xuICAgICAgICBtZWRpdW1wIHZlYzMgc2FtcGxlRGlyZWN0aW9uID0gdGJuICogc2FtcGxlc1tpXTtcblxuICAgICAgICBmbG9hdCBtb3ZlQW10ID0gZnJhY3Qob2Zmc2V0TW92ZSk7XG4gICAgICAgIG9mZnNldE1vdmUgKz0gb2Zmc2V0TW92ZUludjtcbiAgICAgICAgdmVjMyBzYW1wbGVQb3MgPSB3b3JsZFBvcyArIHJhZGl1c1RvVXNlICogbW92ZUFtdCAqIHNhbXBsZURpcmVjdGlvbjtcbiAgICAgICAgdmVjNCBvZmZzZXQgPSBwcm9qTWF0ICogdmVjNChzYW1wbGVQb3MsIDEuMCk7XG4gICAgICAgIG9mZnNldC54eXogLz0gb2Zmc2V0Lnc7XG4gICAgICAgIG9mZnNldC54eXogPSBvZmZzZXQueHl6ICogMC41ICsgMC41O1xuICAgICAgICBcbiAgICAgICAgaWYgKGFsbChncmVhdGVyVGhhbihvZmZzZXQueHl6ICogKDEuMCAtIG9mZnNldC54eXopLCB2ZWMzKDAuMCkpKSkge1xuICAgICAgICAgICNpZmRlZiBSRVZFUlNFREVQVEhcbiAgICAgICAgICBmbG9hdCBzYW1wbGVEZXB0aCA9IDEuMCAtIHRleHR1cmVMb2Qoc2NlbmVEZXB0aCwgb2Zmc2V0Lnh5LCAwLjApLng7XG4gICAgICAgICAgI2Vsc2VcbiAgICAgICAgICBmbG9hdCBzYW1wbGVEZXB0aCA9IHRleHR1cmVMb2Qoc2NlbmVEZXB0aCwgb2Zmc2V0Lnh5LCAwLjApLng7XG4gICAgICAgICAgI2VuZGlmXG5cbiAgICAgICAgICAvKiNpZmRlZiBMT0dERVBUSFxuICAgICAgICAgIGZsb2F0IGRpc3RTYW1wbGUgPSBsaW5lYXJpemVfZGVwdGhfbG9nKHNhbXBsZURlcHRoLCBuZWFyLCBmYXIpO1xuICAgICAgI2Vsc2VcbiAgICAgICAgICAjaWZkZWYgT1JUSE9cbiAgICAgICAgICAgICAgZmxvYXQgZGlzdFNhbXBsZSA9IG5lYXIgKyBmYXJNaW51c05lYXIgKiBzYW1wbGVEZXB0aDtcbiAgICAgICAgICAjZWxzZVxuICAgICAgICAgICAgICBmbG9hdCBkaXN0U2FtcGxlID0gKGZhclRpbWVzTmVhcikgLyAoZmFyIC0gc2FtcGxlRGVwdGggKiBmYXJNaW51c05lYXIpO1xuICAgICAgICAgICNlbmRpZlxuICAgICAgI2VuZGlmKi9cbiAgICAgICNpZmRlZiBPUlRIT1xuICAgICAgICAgIGZsb2F0IGRpc3RTYW1wbGUgPSBuZWFyICsgc2FtcGxlRGVwdGggKiBmYXJNaW51c05lYXI7XG4gICAgICAjZWxzZVxuICAgICAgICAgICNpZmRlZiBMT0dERVBUSFxuICAgICAgICAgICAgICBmbG9hdCBkaXN0U2FtcGxlID0gbGluZWFyaXplX2RlcHRoX2xvZyhzYW1wbGVEZXB0aCwgbmVhciwgZmFyKTtcbiAgICAgICAgICAjZWxzZVxuICAgICAgICAgICAgICBmbG9hdCBkaXN0U2FtcGxlID0gKGZhclRpbWVzTmVhcikgLyAoZmFyIC0gc2FtcGxlRGVwdGggKiBmYXJNaW51c05lYXIpO1xuICAgICAgICAgICNlbmRpZlxuICAgICAgI2VuZGlmXG4gICAgICBcbiAgICAgICNpZmRlZiBPUlRIT1xuICAgICAgICAgIGZsb2F0IGRpc3RXb3JsZCA9IG5lYXIgKyBvZmZzZXQueiAqIGZhck1pbnVzTmVhcjtcbiAgICAgICNlbHNlXG4gICAgICAgICAgZmxvYXQgZGlzdFdvcmxkID0gKGZhclRpbWVzTmVhcikgLyAoZmFyIC0gb2Zmc2V0LnogKiBmYXJNaW51c05lYXIpO1xuICAgICAgI2VuZGlmXG4gICAgICAgICAgXG4gICAgICAgICAgbWVkaXVtcCBmbG9hdCByYW5nZUNoZWNrID0gc21vb3Roc3RlcCgwLjAsIDEuMCwgZGlzdGFuY2VGYWxsb2ZmVG9Vc2UgLyAoYWJzKGRpc3RTYW1wbGUgLSBkaXN0V29ybGQpKSk7XG4gICAgICAgICAgdmVjMiBkaWZmID0gZ2xfRnJhZ0Nvb3JkLnh5IC0gZmxvb3Iob2Zmc2V0Lnh5ICogcmVzb2x1dGlvbik7XG4gICAgICAgICAgb2NjbHVkZWQgKz0gcmFuZ2VDaGVjayAqIGZsb2F0KGRpc3RTYW1wbGUgIT0gZGlzdFdvcmxkKSAqIGZsb2F0KHNhbXBsZURlcHRoICE9IGRlcHRoKSAqIHN0ZXAoZGlzdFNhbXBsZSArIGJpYXMsIGRpc3RXb3JsZCkgKiBzdGVwKFxuICAgICAgICAgICAgMS4wLFxuICAgICAgICAgICAgZG90KGRpZmYsIGRpZmYpXG4gICAgICAgICAgKTtcbiAgICAgICAgICBcbiAgICAgICAgICB0b3RhbFdlaWdodCArKztcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgZmxvYXQgb2NjID0gY2xhbXAoMS4wIC0gb2NjbHVkZWQgLyAodG90YWxXZWlnaHQgPT0gMC4wID8gMS4wIDogdG90YWxXZWlnaHQpLCAwLjAsIDEuMCk7XG4gICAgICBnbF9GcmFnQ29sb3IgPSB2ZWM0KG9jYywgMC41ICsgMC41ICogbm9ybWFsKTtcbn1gXG59O1xuXG5cblxuY29uc3QgJDEyYjIxZDI0ZDExOTJhMDQkZXhwb3J0JGE4MTVhY2NjYmQyYzlhNDkgPSB7XG4gICAgdW5pZm9ybXM6IHtcbiAgICAgICAgXCJzY2VuZURpZmZ1c2VcIjoge1xuICAgICAgICAgICAgdmFsdWU6IG51bGxcbiAgICAgICAgfSxcbiAgICAgICAgXCJzY2VuZURlcHRoXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiBudWxsXG4gICAgICAgIH0sXG4gICAgICAgIFwidERpZmZ1c2VcIjoge1xuICAgICAgICAgICAgdmFsdWU6IG51bGxcbiAgICAgICAgfSxcbiAgICAgICAgXCJ0cmFuc3BhcmVuY3lEV0ZhbHNlXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiBudWxsXG4gICAgICAgIH0sXG4gICAgICAgIFwidHJhbnNwYXJlbmN5RFdUcnVlXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiBudWxsXG4gICAgICAgIH0sXG4gICAgICAgIFwidHJhbnNwYXJlbmN5RFdUcnVlRGVwdGhcIjoge1xuICAgICAgICAgICAgdmFsdWU6IG51bGxcbiAgICAgICAgfSxcbiAgICAgICAgXCJ0cmFuc3BhcmVuY3lBd2FyZVwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogZmFsc2VcbiAgICAgICAgfSxcbiAgICAgICAgXCJwcm9qTWF0XCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiAvKiBAX19QVVJFX18gKi8gbmV3ICQ1V2hlMyRNYXRyaXg0KClcbiAgICAgICAgfSxcbiAgICAgICAgXCJ2aWV3TWF0XCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiAvKiBAX19QVVJFX18gKi8gbmV3ICQ1V2hlMyRNYXRyaXg0KClcbiAgICAgICAgfSxcbiAgICAgICAgXCJwcm9qZWN0aW9uTWF0cml4SW52XCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiAvKiBAX19QVVJFX18gKi8gbmV3ICQ1V2hlMyRNYXRyaXg0KClcbiAgICAgICAgfSxcbiAgICAgICAgXCJ2aWV3TWF0cml4SW52XCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiAvKiBAX19QVVJFX18gKi8gbmV3ICQ1V2hlMyRNYXRyaXg0KClcbiAgICAgICAgfSxcbiAgICAgICAgXCJjYW1lcmFQb3NcIjoge1xuICAgICAgICAgICAgdmFsdWU6IC8qIEBfX1BVUkVfXyAqLyBuZXcgJDVXaGUzJFZlY3RvcjMoKVxuICAgICAgICB9LFxuICAgICAgICBcInJlc29sdXRpb25cIjoge1xuICAgICAgICAgICAgdmFsdWU6IC8qIEBfX1BVUkVfXyAqLyBuZXcgJDVXaGUzJFZlY3RvcjIoKVxuICAgICAgICB9LFxuICAgICAgICBcImNvbG9yXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiAvKiBAX19QVVJFX18gKi8gbmV3ICQ1V2hlMyRWZWN0b3IzKDAsIDAsIDApXG4gICAgICAgIH0sXG4gICAgICAgIFwiYmx1ZU5vaXNlXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiBudWxsXG4gICAgICAgIH0sXG4gICAgICAgIFwiZG93bnNhbXBsZWREZXB0aFwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogbnVsbFxuICAgICAgICB9LFxuICAgICAgICBcInRpbWVcIjoge1xuICAgICAgICAgICAgdmFsdWU6IDAuMFxuICAgICAgICB9LFxuICAgICAgICBcImludGVuc2l0eVwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogMTAuMFxuICAgICAgICB9LFxuICAgICAgICBcInJlbmRlck1vZGVcIjoge1xuICAgICAgICAgICAgdmFsdWU6IDAuMFxuICAgICAgICB9LFxuICAgICAgICBcImdhbW1hQ29ycmVjdGlvblwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogZmFsc2VcbiAgICAgICAgfSxcbiAgICAgICAgXCJvcnRob1wiOiB7XG4gICAgICAgICAgICB2YWx1ZTogZmFsc2VcbiAgICAgICAgfSxcbiAgICAgICAgXCJuZWFyXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiAwLjFcbiAgICAgICAgfSxcbiAgICAgICAgXCJmYXJcIjoge1xuICAgICAgICAgICAgdmFsdWU6IDEwMDAuMFxuICAgICAgICB9LFxuICAgICAgICBcInNjcmVlblNwYWNlUmFkaXVzXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiBmYWxzZVxuICAgICAgICB9LFxuICAgICAgICBcInJhZGl1c1wiOiB7XG4gICAgICAgICAgICB2YWx1ZTogMC4wXG4gICAgICAgIH0sXG4gICAgICAgIFwiZGlzdGFuY2VGYWxsb2ZmXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiAxLjBcbiAgICAgICAgfSxcbiAgICAgICAgXCJmb2dcIjoge1xuICAgICAgICAgICAgdmFsdWU6IGZhbHNlXG4gICAgICAgIH0sXG4gICAgICAgIFwiZm9nRXhwXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiBmYWxzZVxuICAgICAgICB9LFxuICAgICAgICBcImZvZ0RlbnNpdHlcIjoge1xuICAgICAgICAgICAgdmFsdWU6IDAuMFxuICAgICAgICB9LFxuICAgICAgICBcImZvZ05lYXJcIjoge1xuICAgICAgICAgICAgdmFsdWU6IEluZmluaXR5XG4gICAgICAgIH0sXG4gICAgICAgIFwiZm9nRmFyXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiBJbmZpbml0eVxuICAgICAgICB9LFxuICAgICAgICBcImNvbG9yTXVsdGlwbHlcIjoge1xuICAgICAgICAgICAgdmFsdWU6IHRydWVcbiAgICAgICAgfSxcbiAgICAgICAgXCJhb1RvbmVzXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiAwLjBcbiAgICAgICAgfVxuICAgIH0sXG4gICAgZGVwdGhXcml0ZTogZmFsc2UsXG4gICAgZGVwdGhUZXN0OiBmYWxzZSxcbiAgICB2ZXJ0ZXhTaGFkZXI6IC8qIGdsc2wgKi8gYFxuXHRcdHZhcnlpbmcgdmVjMiB2VXY7XG5cdFx0dm9pZCBtYWluKCkge1xuXHRcdFx0dlV2ID0gdXY7XG5cdFx0XHRnbF9Qb3NpdGlvbiA9IHZlYzQocG9zaXRpb24sIDEpO1xuXHRcdH1gLFxuICAgIGZyYWdtZW50U2hhZGVyOiAvKiBnbHNsICovIGBcblx0XHR1bmlmb3JtIHNhbXBsZXIyRCBzY2VuZURpZmZ1c2U7XG4gICAgdW5pZm9ybSBoaWdocCBzYW1wbGVyMkQgc2NlbmVEZXB0aDtcbiAgICB1bmlmb3JtIGhpZ2hwIHNhbXBsZXIyRCBkb3duc2FtcGxlZERlcHRoO1xuICAgIHVuaWZvcm0gaGlnaHAgc2FtcGxlcjJEIHRyYW5zcGFyZW5jeURXRmFsc2U7XG4gICAgdW5pZm9ybSBoaWdocCBzYW1wbGVyMkQgdHJhbnNwYXJlbmN5RFdUcnVlO1xuICAgIHVuaWZvcm0gaGlnaHAgc2FtcGxlcjJEIHRyYW5zcGFyZW5jeURXVHJ1ZURlcHRoO1xuICAgIHVuaWZvcm0gc2FtcGxlcjJEIHREaWZmdXNlO1xuICAgIHVuaWZvcm0gc2FtcGxlcjJEIGJsdWVOb2lzZTtcbiAgICB1bmlmb3JtIHZlYzIgcmVzb2x1dGlvbjtcbiAgICB1bmlmb3JtIHZlYzMgY29sb3I7XG4gICAgdW5pZm9ybSBtYXQ0IHByb2plY3Rpb25NYXRyaXhJbnY7XG4gICAgdW5pZm9ybSBtYXQ0IHZpZXdNYXRyaXhJbnY7XG4gICAgdW5pZm9ybSBmbG9hdCBpbnRlbnNpdHk7XG4gICAgdW5pZm9ybSBmbG9hdCByZW5kZXJNb2RlO1xuICAgIHVuaWZvcm0gZmxvYXQgbmVhcjtcbiAgICB1bmlmb3JtIGZsb2F0IGZhcjtcbiAgICB1bmlmb3JtIGZsb2F0IGFvVG9uZXM7XG4gICAgdW5pZm9ybSBib29sIGdhbW1hQ29ycmVjdGlvbjtcbiAgICB1bmlmb3JtIGJvb2wgb3J0aG87XG4gICAgdW5pZm9ybSBib29sIHNjcmVlblNwYWNlUmFkaXVzO1xuICAgIHVuaWZvcm0gYm9vbCBmb2c7XG4gICAgdW5pZm9ybSBib29sIGZvZ0V4cDtcbiAgICB1bmlmb3JtIGJvb2wgY29sb3JNdWx0aXBseTtcbiAgICB1bmlmb3JtIGJvb2wgdHJhbnNwYXJlbmN5QXdhcmU7XG4gICAgdW5pZm9ybSBmbG9hdCBmb2dEZW5zaXR5O1xuICAgIHVuaWZvcm0gZmxvYXQgZm9nTmVhcjtcbiAgICB1bmlmb3JtIGZsb2F0IGZvZ0ZhcjtcbiAgICB1bmlmb3JtIGZsb2F0IHJhZGl1cztcbiAgICB1bmlmb3JtIGZsb2F0IGRpc3RhbmNlRmFsbG9mZjtcbiAgICB1bmlmb3JtIHZlYzMgY2FtZXJhUG9zO1xuICAgIHZhcnlpbmcgdmVjMiB2VXY7XG4gICAgaGlnaHAgZmxvYXQgbGluZWFyaXplX2RlcHRoKGhpZ2hwIGZsb2F0IGQsIGhpZ2hwIGZsb2F0IHpOZWFyLGhpZ2hwIGZsb2F0IHpGYXIpXG4gICAge1xuICAgICAgICByZXR1cm4gKHpGYXIgKiB6TmVhcikgLyAoekZhciAtIGQgKiAoekZhciAtIHpOZWFyKSk7XG4gICAgfVxuICAgIGhpZ2hwIGZsb2F0IGxpbmVhcml6ZV9kZXB0aF9vcnRobyhoaWdocCBmbG9hdCBkLCBoaWdocCBmbG9hdCBuZWFyWiwgaGlnaHAgZmxvYXQgZmFyWikge1xuICAgICAgcmV0dXJuIG5lYXJaICsgKGZhclogLSBuZWFyWikgKiBkO1xuICAgIH1cbiAgICBoaWdocCBmbG9hdCBsaW5lYXJpemVfZGVwdGhfbG9nKGhpZ2hwIGZsb2F0IGQsIGhpZ2hwIGZsb2F0IG5lYXJaLGhpZ2hwIGZsb2F0IGZhclopIHtcbiAgICAgIGZsb2F0IGRlcHRoID0gcG93KDIuMCwgZCAqIGxvZzIoZmFyWiArIDEuMCkpIC0gMS4wO1xuICAgICAgZmxvYXQgYSA9IGZhclogLyAoZmFyWiAtIG5lYXJaKTtcbiAgICAgIGZsb2F0IGIgPSBmYXJaICogbmVhclogLyAobmVhclogLSBmYXJaKTtcbiAgICAgIGZsb2F0IGxpbkRlcHRoID0gYSArIGIgLyBkZXB0aDtcbiAgICAgIHJldHVybiBvcnRobyA/IGxpbmVhcml6ZV9kZXB0aF9vcnRobyhcbiAgICAgICAgbGluRGVwdGgsXG4gICAgICAgIG5lYXJaLFxuICAgICAgICBmYXJaXG4gICAgICApIDpsaW5lYXJpemVfZGVwdGgobGluRGVwdGgsIG5lYXJaLCBmYXJaKTtcbiAgICB9XG4gICAgdmVjMyBnZXRXb3JsZFBvc0xvZyh2ZWMzIHBvc1MpIHtcbiAgICAgICAgdmVjMiB1diA9IHBvc1MueHk7XG4gICAgICAgIGZsb2F0IHogPSBwb3NTLno7XG4gICAgICAgIGZsb2F0IG5lYXJaID1uZWFyO1xuICAgICAgICBmbG9hdCBmYXJaID0gZmFyO1xuICAgICAgICBmbG9hdCBkZXB0aCA9IHBvdygyLjAsIHogKiBsb2cyKGZhclogKyAxLjApKSAtIDEuMDtcbiAgICAgICAgZmxvYXQgYSA9IGZhclogLyAoZmFyWiAtIG5lYXJaKTtcbiAgICAgICAgZmxvYXQgYiA9IGZhclogKiBuZWFyWiAvIChuZWFyWiAtIGZhclopO1xuICAgICAgICBmbG9hdCBsaW5EZXB0aCA9IGEgKyBiIC8gZGVwdGg7XG4gICAgICAgIHZlYzQgY2xpcFZlYyA9IHZlYzQodXYsIGxpbkRlcHRoLCAxLjApICogMi4wIC0gMS4wO1xuICAgICAgICB2ZWM0IHdwb3MgPSBwcm9qZWN0aW9uTWF0cml4SW52ICogY2xpcFZlYztcbiAgICAgICAgcmV0dXJuIHdwb3MueHl6IC8gd3Bvcy53O1xuICAgICAgfVxuICAgICAgdmVjMyBnZXRXb3JsZFBvcyhmbG9hdCBkZXB0aCwgdmVjMiBjb29yZCkge1xuICAgICAgICAjaWZkZWYgTE9HREVQVEhcbiAgICAgICAgICAjaWZuZGVmIE9SVEhPXG4gICAgICAgICAgICByZXR1cm4gZ2V0V29ybGRQb3NMb2codmVjMyhjb29yZCwgZGVwdGgpKTtcbiAgICAgICAgICAjZW5kaWZcbiAgICAgICAgI2VuZGlmXG4gICAgICAvLyAgfVxuICAgICAgICBmbG9hdCB6ID0gZGVwdGggKiAyLjAgLSAxLjA7XG4gICAgICAgIHZlYzQgY2xpcFNwYWNlUG9zaXRpb24gPSB2ZWM0KGNvb3JkICogMi4wIC0gMS4wLCB6LCAxLjApO1xuICAgICAgICB2ZWM0IHZpZXdTcGFjZVBvc2l0aW9uID0gcHJvamVjdGlvbk1hdHJpeEludiAqIGNsaXBTcGFjZVBvc2l0aW9uO1xuICAgICAgICAvLyBQZXJzcGVjdGl2ZSBkaXZpc2lvblxuICAgICAgIHZlYzQgd29ybGRTcGFjZVBvc2l0aW9uID0gdmlld1NwYWNlUG9zaXRpb247XG4gICAgICAgd29ybGRTcGFjZVBvc2l0aW9uLnh5eiAvPSB3b3JsZFNwYWNlUG9zaXRpb24udztcbiAgICAgICAgcmV0dXJuIHdvcmxkU3BhY2VQb3NpdGlvbi54eXo7XG4gICAgfVxuICBcbiAgICB2ZWMzIGNvbXB1dGVOb3JtYWwodmVjMyB3b3JsZFBvcywgdmVjMiB2VXYpIHtcbiAgICAgIGl2ZWMyIHAgPSBpdmVjMih2VXYgKiByZXNvbHV0aW9uKTtcbiAgICAgICNpZmRlZiBSRVZFUlNFREVQVEhcbiAgICAgIGZsb2F0IGMwID0gMS4wIC0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwLCAwKS54O1xuICAgICAgZmxvYXQgbDIgPSAxLjAgLSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgLSBpdmVjMigyLCAwKSwgMCkueDtcbiAgICAgIGZsb2F0IGwxID0gMS4wIC0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwIC0gaXZlYzIoMSwgMCksIDApLng7XG4gICAgICBmbG9hdCByMSA9IDEuMCAtIHRleGVsRmV0Y2goc2NlbmVEZXB0aCwgcCArIGl2ZWMyKDEsIDApLCAwKS54O1xuICAgICAgZmxvYXQgcjIgPSAxLjAgLSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgKyBpdmVjMigyLCAwKSwgMCkueDtcbiAgICAgIGZsb2F0IGIyID0gMS4wIC0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwIC0gaXZlYzIoMCwgMiksIDApLng7XG4gICAgICBmbG9hdCBiMSA9IDEuMCAtIHRleGVsRmV0Y2goc2NlbmVEZXB0aCwgcCAtIGl2ZWMyKDAsIDEpLCAwKS54O1xuICAgICAgZmxvYXQgdDEgPSAxLjAgLSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgKyBpdmVjMigwLCAxKSwgMCkueDtcbiAgICAgIGZsb2F0IHQyID0gMS4wIC0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwICsgaXZlYzIoMCwgMiksIDApLng7XG4gICAgICAjZWxzZVxuICAgICAgZmxvYXQgYzAgPSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAsIDApLng7XG4gICAgICBmbG9hdCBsMiA9IHRleGVsRmV0Y2goc2NlbmVEZXB0aCwgcCAtIGl2ZWMyKDIsIDApLCAwKS54O1xuICAgICAgZmxvYXQgbDEgPSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgLSBpdmVjMigxLCAwKSwgMCkueDtcbiAgICAgIGZsb2F0IHIxID0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwICsgaXZlYzIoMSwgMCksIDApLng7XG4gICAgICBmbG9hdCByMiA9IHRleGVsRmV0Y2goc2NlbmVEZXB0aCwgcCArIGl2ZWMyKDIsIDApLCAwKS54O1xuICAgICAgZmxvYXQgYjIgPSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgLSBpdmVjMigwLCAyKSwgMCkueDtcbiAgICAgIGZsb2F0IGIxID0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwIC0gaXZlYzIoMCwgMSksIDApLng7XG4gICAgICBmbG9hdCB0MSA9IHRleGVsRmV0Y2goc2NlbmVEZXB0aCwgcCArIGl2ZWMyKDAsIDEpLCAwKS54O1xuICAgICAgZmxvYXQgdDIgPSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgKyBpdmVjMigwLCAyKSwgMCkueDtcbiAgICAgICNlbmRpZlxuICBcbiAgICAgIGZsb2F0IGRsID0gYWJzKCgyLjAgKiBsMSAtIGwyKSAtIGMwKTtcbiAgICAgIGZsb2F0IGRyID0gYWJzKCgyLjAgKiByMSAtIHIyKSAtIGMwKTtcbiAgICAgIGZsb2F0IGRiID0gYWJzKCgyLjAgKiBiMSAtIGIyKSAtIGMwKTtcbiAgICAgIGZsb2F0IGR0ID0gYWJzKCgyLjAgKiB0MSAtIHQyKSAtIGMwKTtcbiAgXG4gICAgICB2ZWMzIGNlID0gZ2V0V29ybGRQb3MoYzAsIHZVdikueHl6O1xuICBcbiAgICAgIHZlYzMgZHBkeCA9IChkbCA8IGRyKSA/IGNlIC0gZ2V0V29ybGRQb3MobDEsICh2VXYgLSB2ZWMyKDEuMCAvIHJlc29sdXRpb24ueCwgMC4wKSkpLnh5elxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogLWNlICsgZ2V0V29ybGRQb3MocjEsICh2VXYgKyB2ZWMyKDEuMCAvIHJlc29sdXRpb24ueCwgMC4wKSkpLnh5ejtcbiAgICAgIHZlYzMgZHBkeSA9IChkYiA8IGR0KSA/IGNlIC0gZ2V0V29ybGRQb3MoYjEsICh2VXYgLSB2ZWMyKDAuMCwgMS4wIC8gcmVzb2x1dGlvbi55KSkpLnh5elxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogLWNlICsgZ2V0V29ybGRQb3ModDEsICh2VXYgKyB2ZWMyKDAuMCwgMS4wIC8gcmVzb2x1dGlvbi55KSkpLnh5ejtcbiAgXG4gICAgICByZXR1cm4gbm9ybWFsaXplKGNyb3NzKGRwZHgsIGRwZHkpKTtcbiAgfVxuXG4gICAgI2luY2x1ZGUgPGNvbW1vbj5cbiAgICAjaW5jbHVkZSA8ZGl0aGVyaW5nX3BhcnNfZnJhZ21lbnQ+XG4gICAgdm9pZCBtYWluKCkge1xuICAgICAgICAvL3ZlYzQgdGV4ZWwgPSB0ZXh0dXJlMkQodERpZmZ1c2UsIHZVdik7Ly92ZWMzKDAuMCk7XG4gICAgICAgIHZlYzQgc2NlbmVUZXhlbCA9IHRleHR1cmUyRChzY2VuZURpZmZ1c2UsIHZVdik7XG4gICAgICAgICNpZmRlZiBSRVZFUlNFREVQVEhcbiAgICAgICAgZmxvYXQgZGVwdGggPSAxLjAgLSB0ZXh0dXJlMkQoc2NlbmVEZXB0aCwgdlV2KS54O1xuICAgICAgICAjZWxzZVxuICAgICAgICBmbG9hdCBkZXB0aCA9IHRleHR1cmUyRChzY2VuZURlcHRoLCB2VXYpLng7XG4gICAgICAgICNlbmRpZlxuICAgICAgICAjaWZkZWYgSEFMRlJFUyBcbiAgICAgICAgdmVjNCB0ZXhlbDtcbiAgICAgICAgaWYgKGRlcHRoID09IDEuMCkge1xuICAgICAgICAgICAgdGV4ZWwgPSB2ZWM0KDAuMCwgMC4wLCAwLjAsIDEuMCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgIHZlYzMgd29ybGRQb3MgPSBnZXRXb3JsZFBvcyhkZXB0aCwgdlV2KTtcbiAgICAgICAgdmVjMyBub3JtYWwgPSBjb21wdXRlTm9ybWFsKGdldFdvcmxkUG9zKGRlcHRoLCB2VXYpLCB2VXYpO1xuICAgICAgIC8vIHZlYzQgdGV4ZWwgPSB0ZXh0dXJlMkQodERpZmZ1c2UsIHZVdik7XG4gICAgICAgLy8gRmluZCBjbG9zZXN0IGRlcHRoO1xuICAgICAgIGZsb2F0IHRvdGFsV2VpZ2h0ID0gMC4wO1xuICAgICAgIGZsb2F0IHJhZGl1c1RvVXNlID0gc2NyZWVuU3BhY2VSYWRpdXMgPyBkaXN0YW5jZShcbiAgICAgICAgd29ybGRQb3MsXG4gICAgICAgIGdldFdvcmxkUG9zKGRlcHRoLCB2VXYgK1xuICAgICAgICAgIHZlYzIocmFkaXVzLCAwLjApIC8gcmVzb2x1dGlvbilcbiAgICAgICkgOiByYWRpdXM7XG4gICAgICBmbG9hdCBkaXN0YW5jZUZhbGxvZmZUb1VzZSA9c2NyZWVuU3BhY2VSYWRpdXMgP1xuICAgICAgICAgIHJhZGl1c1RvVXNlICogZGlzdGFuY2VGYWxsb2ZmXG4gICAgICA6IGRpc3RhbmNlRmFsbG9mZjtcbiAgICAgICAgZm9yKGZsb2F0IHggPSAtMS4wOyB4IDw9IDEuMDsgeCsrKSB7XG4gICAgICAgICAgICBmb3IoZmxvYXQgeSA9IC0xLjA7IHkgPD0gMS4wOyB5KyspIHtcbiAgICAgICAgICAgICAgICB2ZWMyIG9mZnNldCA9IHZlYzIoeCwgeSk7XG4gICAgICAgICAgICAgICAgaXZlYzIgcCA9IGl2ZWMyKFxuICAgICAgICAgICAgICAgICAgICAodlV2ICogcmVzb2x1dGlvbiAqIDAuNSkgKyBvZmZzZXRcbiAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgIHZlYzIgcFV2ID0gdmVjMihwKSAvIChyZXNvbHV0aW9uICogMC41KTtcbiAgICAgICAgICAgICAgICBmbG9hdCBzYW1wbGVEZXB0aCA9IHRleGVsRmV0Y2goZG93bnNhbXBsZWREZXB0aCxwLCAwKS54O1xuICAgICAgICAgICAgICAgIHZlYzQgc2FtcGxlSW5mbyA9IHRleGVsRmV0Y2godERpZmZ1c2UsIHAsIDApO1xuICAgICAgICAgICAgICAgIHZlYzMgbm9ybWFsU2FtcGxlID0gc2FtcGxlSW5mby5nYmEgKiAyLjAgLSAxLjA7XG4gICAgICAgICAgICAgICAgdmVjMyB3b3JsZFBvc1NhbXBsZSA9IGdldFdvcmxkUG9zKHNhbXBsZURlcHRoLCBwVXYpO1xuICAgICAgICAgICAgICAgIGZsb2F0IHRhbmdlbnRQbGFuZURpc3QgPSBhYnMoZG90KHdvcmxkUG9zU2FtcGxlIC0gd29ybGRQb3MsIG5vcm1hbCkpO1xuICAgICAgICAgICAgICAgIGZsb2F0IHJhbmdlQ2hlY2sgPSBleHAoLTEuMCAqIHRhbmdlbnRQbGFuZURpc3QgKiAoMS4wIC8gZGlzdGFuY2VGYWxsb2ZmVG9Vc2UpKSAqIG1heChkb3Qobm9ybWFsLCBub3JtYWxTYW1wbGUpLCAwLjApO1xuICAgICAgICAgICAgICAgIGZsb2F0IHdlaWdodCA9IHJhbmdlQ2hlY2s7XG4gICAgICAgICAgICAgICAgdG90YWxXZWlnaHQgKz0gd2VpZ2h0O1xuICAgICAgICAgICAgICAgIHRleGVsICs9IHNhbXBsZUluZm8gKiB3ZWlnaHQ7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRvdGFsV2VpZ2h0ID09IDAuMCkge1xuICAgICAgICAgICAgdGV4ZWwgPSB0ZXh0dXJlMkQodERpZmZ1c2UsIHZVdik7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0ZXhlbCAvPSB0b3RhbFdlaWdodDtcbiAgICAgICAgfVxuICAgIH1cbiAgICAgICAgI2Vsc2VcbiAgICAgICAgdmVjNCB0ZXhlbCA9IHRleHR1cmUyRCh0RGlmZnVzZSwgdlV2KTtcbiAgICAgICAgI2VuZGlmXG5cbiAgICAgICAgI2lmZGVmIExPR0RFUFRIXG4gICAgICAgIHRleGVsLnIgPSBjbGFtcCh0ZXhlbC5yLCAwLjAsIDEuMCk7XG4gICAgICAgIGlmICh0ZXhlbC5yID09IDAuMCkge1xuICAgICAgICAgIHRleGVsLnIgPSAxLjA7XG4gICAgICAgIH1cbiAgICAgICAgI2VuZGlmXG4gICAgIFxuICAgICAgICBmbG9hdCBmaW5hbEFvID0gcG93KHRleGVsLnIsIGludGVuc2l0eSk7XG4gICAgICAgIGlmIChhb1RvbmVzID4gMC4wKSB7XG4gICAgICAgICAgICBmaW5hbEFvID0gY2VpbChmaW5hbEFvICogYW9Ub25lcykgLyBhb1RvbmVzO1xuICAgICAgICB9XG4gICAgICAgIGZsb2F0IGZvZ0ZhY3RvcjtcbiAgICAgICAgZmxvYXQgZm9nRGVwdGggPSBkaXN0YW5jZShcbiAgICAgICAgICAgIGNhbWVyYVBvcyxcbiAgICAgICAgICAgIGdldFdvcmxkUG9zKGRlcHRoLCB2VXYpXG4gICAgICAgICk7XG4gICAgICAgIGlmIChmb2cpIHtcbiAgICAgICAgICAgIGlmIChmb2dFeHApIHtcbiAgICAgICAgICAgICAgICBmb2dGYWN0b3IgPSAxLjAgLSBleHAoIC0gZm9nRGVuc2l0eSAqIGZvZ0RlbnNpdHkgKiBmb2dEZXB0aCAqIGZvZ0RlcHRoICk7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGZvZ0ZhY3RvciA9IHNtb290aHN0ZXAoIGZvZ05lYXIsIGZvZ0ZhciwgZm9nRGVwdGggKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAodHJhbnNwYXJlbmN5QXdhcmUpIHtcbiAgICAgICAgICAgIGZsb2F0IHRyYW5zcGFyZW5jeURXT2ZmID0gdGV4dHVyZTJEKHRyYW5zcGFyZW5jeURXRmFsc2UsIHZVdikuYTtcbiAgICAgICAgICAgIGZsb2F0IHRyYW5zcGFyZW5jeURXT24gPSB0ZXh0dXJlMkQodHJhbnNwYXJlbmN5RFdUcnVlLCB2VXYpLmE7XG4gICAgICAgICAgICBmbG9hdCBhZGp1c3RtZW50RmFjdG9yT2ZmID0gdHJhbnNwYXJlbmN5RFdPZmY7XG4gICAgICAgICAgICAjaWZkZWYgUkVWRVJTRURFUFRIXG4gICAgICAgICAgICBmbG9hdCBkZXB0aFNhbXBsZSA9IDEuMCAtIHRleHR1cmUyRChzY2VuZURlcHRoLCB2VXYpLnI7XG4gICAgICAgICAgICBmbG9hdCB0cnVlRGVwdGhTYW1wbGUgPSAxLjAgLSB0ZXh0dXJlMkQodHJhbnNwYXJlbmN5RFdUcnVlRGVwdGgsIHZVdikucjtcbiAgICAgICAgICAgICNlbHNlXG4gICAgICAgICAgICBmbG9hdCBkZXB0aFNhbXBsZSA9IHRleHR1cmUyRChzY2VuZURlcHRoLCB2VXYpLnI7XG4gICAgICAgICAgICBmbG9hdCB0cnVlRGVwdGhTYW1wbGUgPSB0ZXh0dXJlMkQodHJhbnNwYXJlbmN5RFdUcnVlRGVwdGgsIHZVdikucjtcbiAgICAgICAgICAgICNlbmRpZlxuICAgICAgICAgICAgZmxvYXQgYWRqdXN0bWVudEZhY3Rvck9uID0gKDEuMCAtIHRyYW5zcGFyZW5jeURXT24pICogKFxuICAgICAgICAgICAgICAgIHRydWVEZXB0aFNhbXBsZSA9PSBkZXB0aFNhbXBsZSA/IDEuMCA6IDAuMFxuICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIGZsb2F0IGFkanVzdG1lbnRGYWN0b3IgPSBtYXgoYWRqdXN0bWVudEZhY3Rvck9mZiwgYWRqdXN0bWVudEZhY3Rvck9uKTtcbiAgICAgICAgICAgIGZpbmFsQW8gPSBtaXgoZmluYWxBbywgMS4wLCBhZGp1c3RtZW50RmFjdG9yKTtcbiAgICAgICAgfVxuICAgICAgICBmaW5hbEFvID0gbWl4KGZpbmFsQW8sIDEuMCwgZm9nRmFjdG9yKTtcbiAgICAgICAgdmVjMyBhb0FwcGxpZWQgPSBjb2xvciAqIG1peCh2ZWMzKDEuMCksIHNjZW5lVGV4ZWwucmdiLCBmbG9hdChjb2xvck11bHRpcGx5KSk7XG4gICAgICAgIGlmIChyZW5kZXJNb2RlID09IDAuMCkge1xuICAgICAgICAgICAgZ2xfRnJhZ0NvbG9yID0gdmVjNCggbWl4KHNjZW5lVGV4ZWwucmdiLCBhb0FwcGxpZWQsIDEuMCAtIGZpbmFsQW8pLCBzY2VuZVRleGVsLmEpO1xuICAgICAgICB9IGVsc2UgaWYgKHJlbmRlck1vZGUgPT0gMS4wKSB7XG4gICAgICAgICAgICBnbF9GcmFnQ29sb3IgPSB2ZWM0KCBtaXgodmVjMygxLjApLCBhb0FwcGxpZWQsIDEuMCAtIGZpbmFsQW8pLCBzY2VuZVRleGVsLmEpO1xuICAgICAgICB9IGVsc2UgaWYgKHJlbmRlck1vZGUgPT0gMi4wKSB7XG4gICAgICAgICAgICBnbF9GcmFnQ29sb3IgPSB2ZWM0KCBzY2VuZVRleGVsLnJnYiwgc2NlbmVUZXhlbC5hKTtcbiAgICAgICAgfSBlbHNlIGlmIChyZW5kZXJNb2RlID09IDMuMCkge1xuICAgICAgICAgICAgaWYgKHZVdi54IDwgMC41KSB7XG4gICAgICAgICAgICAgICAgZ2xfRnJhZ0NvbG9yID0gdmVjNCggc2NlbmVUZXhlbC5yZ2IsIHNjZW5lVGV4ZWwuYSk7XG4gICAgICAgICAgICB9IGVsc2UgaWYgKGFicyh2VXYueCAtIDAuNSkgPCAxLjAgLyByZXNvbHV0aW9uLngpIHtcbiAgICAgICAgICAgICAgICBnbF9GcmFnQ29sb3IgPSB2ZWM0KDEuMCk7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGdsX0ZyYWdDb2xvciA9IHZlYzQoIG1peChzY2VuZVRleGVsLnJnYiwgYW9BcHBsaWVkLCAxLjAgLSBmaW5hbEFvKSwgc2NlbmVUZXhlbC5hKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIGlmIChyZW5kZXJNb2RlID09IDQuMCkge1xuICAgICAgICAgICAgaWYgKHZVdi54IDwgMC41KSB7XG4gICAgICAgICAgICAgICAgZ2xfRnJhZ0NvbG9yID0gdmVjNCggc2NlbmVUZXhlbC5yZ2IsIHNjZW5lVGV4ZWwuYSk7XG4gICAgICAgICAgICB9IGVsc2UgaWYgKGFicyh2VXYueCAtIDAuNSkgPCAxLjAgLyByZXNvbHV0aW9uLngpIHtcbiAgICAgICAgICAgICAgICBnbF9GcmFnQ29sb3IgPSB2ZWM0KDEuMCk7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGdsX0ZyYWdDb2xvciA9IHZlYzQoIG1peCh2ZWMzKDEuMCksIGFvQXBwbGllZCwgMS4wIC0gZmluYWxBbyksIHNjZW5lVGV4ZWwuYSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgI2luY2x1ZGUgPGRpdGhlcmluZ19mcmFnbWVudD5cbiAgICAgICAgaWYgKGdhbW1hQ29ycmVjdGlvbikge1xuICAgICAgICAgICAgZ2xfRnJhZ0NvbG9yID0gc1JHQlRyYW5zZmVyT0VURihnbF9GcmFnQ29sb3IpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGBcbn07XG5cblxuXG5jb25zdCAkZTUyMzc4Y2QwZjVhOTczZCRleHBvcnQkNTc4NTZiNTlmMzE3MjYyZSA9IHtcbiAgICB1bmlmb3Jtczoge1xuICAgICAgICBcInNjZW5lRGlmZnVzZVwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogbnVsbFxuICAgICAgICB9LFxuICAgICAgICBcInNjZW5lRGVwdGhcIjoge1xuICAgICAgICAgICAgdmFsdWU6IG51bGxcbiAgICAgICAgfSxcbiAgICAgICAgXCJ0RGlmZnVzZVwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogbnVsbFxuICAgICAgICB9LFxuICAgICAgICBcInByb2pNYXRcIjoge1xuICAgICAgICAgICAgdmFsdWU6IC8qIEBfX1BVUkVfXyAqLyBuZXcgJDVXaGUzJE1hdHJpeDQoKVxuICAgICAgICB9LFxuICAgICAgICBcInZpZXdNYXRcIjoge1xuICAgICAgICAgICAgdmFsdWU6IC8qIEBfX1BVUkVfXyAqLyBuZXcgJDVXaGUzJE1hdHJpeDQoKVxuICAgICAgICB9LFxuICAgICAgICBcInByb2plY3Rpb25NYXRyaXhJbnZcIjoge1xuICAgICAgICAgICAgdmFsdWU6IC8qIEBfX1BVUkVfXyAqLyBuZXcgJDVXaGUzJE1hdHJpeDQoKVxuICAgICAgICB9LFxuICAgICAgICBcInZpZXdNYXRyaXhJbnZcIjoge1xuICAgICAgICAgICAgdmFsdWU6IC8qIEBfX1BVUkVfXyAqLyBuZXcgJDVXaGUzJE1hdHJpeDQoKVxuICAgICAgICB9LFxuICAgICAgICBcImNhbWVyYVBvc1wiOiB7XG4gICAgICAgICAgICB2YWx1ZTogLyogQF9fUFVSRV9fICovIG5ldyAkNVdoZTMkVmVjdG9yMygpXG4gICAgICAgIH0sXG4gICAgICAgIFwicmVzb2x1dGlvblwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogLyogQF9fUFVSRV9fICovIG5ldyAkNVdoZTMkVmVjdG9yMigpXG4gICAgICAgIH0sXG4gICAgICAgIFwidGltZVwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogMC4wXG4gICAgICAgIH0sXG4gICAgICAgIFwiclwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogNS4wXG4gICAgICAgIH0sXG4gICAgICAgIFwiYmx1ZU5vaXNlXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiBudWxsXG4gICAgICAgIH0sXG4gICAgICAgIFwicmFkaXVzXCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiAxMi4wXG4gICAgICAgIH0sXG4gICAgICAgIFwid29ybGRSYWRpdXNcIjoge1xuICAgICAgICAgICAgdmFsdWU6IDUuMFxuICAgICAgICB9LFxuICAgICAgICBcImluZGV4XCI6IHtcbiAgICAgICAgICAgIHZhbHVlOiAwLjBcbiAgICAgICAgfSxcbiAgICAgICAgXCJwb2lzc29uRGlza1wiOiB7XG4gICAgICAgICAgICB2YWx1ZTogW11cbiAgICAgICAgfSxcbiAgICAgICAgXCJkaXN0YW5jZUZhbGxvZmZcIjoge1xuICAgICAgICAgICAgdmFsdWU6IDEuMFxuICAgICAgICB9LFxuICAgICAgICBcIm5lYXJcIjoge1xuICAgICAgICAgICAgdmFsdWU6IDAuMVxuICAgICAgICB9LFxuICAgICAgICBcImZhclwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogMTAwMC4wXG4gICAgICAgIH0sXG4gICAgICAgIFwic2NyZWVuU3BhY2VSYWRpdXNcIjoge1xuICAgICAgICAgICAgdmFsdWU6IGZhbHNlXG4gICAgICAgIH1cbiAgICB9LFxuICAgIGRlcHRoV3JpdGU6IGZhbHNlLFxuICAgIGRlcHRoVGVzdDogZmFsc2UsXG4gICAgdmVydGV4U2hhZGVyOiAvKiBnbHNsICovIGBcblx0XHR2YXJ5aW5nIHZlYzIgdlV2O1xuXHRcdHZvaWQgbWFpbigpIHtcblx0XHRcdHZVdiA9IHV2O1xuXHRcdFx0Z2xfUG9zaXRpb24gPSB2ZWM0KHBvc2l0aW9uLCAxLjApO1xuXHRcdH1gLFxuICAgIGZyYWdtZW50U2hhZGVyOiAvKiBnbHNsICovIGBcblx0XHR1bmlmb3JtIHNhbXBsZXIyRCBzY2VuZURpZmZ1c2U7XG4gICAgdW5pZm9ybSBoaWdocCBzYW1wbGVyMkQgc2NlbmVEZXB0aDtcbiAgICB1bmlmb3JtIHNhbXBsZXIyRCB0RGlmZnVzZTtcbiAgICB1bmlmb3JtIHNhbXBsZXIyRCBibHVlTm9pc2U7XG4gICAgdW5pZm9ybSBtYXQ0IHByb2plY3Rpb25NYXRyaXhJbnY7XG4gICAgdW5pZm9ybSBtYXQ0IHZpZXdNYXRyaXhJbnY7XG4gICAgdW5pZm9ybSB2ZWMyIHJlc29sdXRpb247XG4gICAgdW5pZm9ybSBmbG9hdCByO1xuICAgIHVuaWZvcm0gZmxvYXQgcmFkaXVzO1xuICAgICB1bmlmb3JtIGZsb2F0IHdvcmxkUmFkaXVzO1xuICAgIHVuaWZvcm0gZmxvYXQgaW5kZXg7XG4gICAgIHVuaWZvcm0gZmxvYXQgbmVhcjtcbiAgICAgdW5pZm9ybSBmbG9hdCBmYXI7XG4gICAgIHVuaWZvcm0gZmxvYXQgZGlzdGFuY2VGYWxsb2ZmO1xuICAgICB1bmlmb3JtIGJvb2wgc2NyZWVuU3BhY2VSYWRpdXM7XG4gICAgdmFyeWluZyB2ZWMyIHZVdjtcblxuICAgIGhpZ2hwIGZsb2F0IGxpbmVhcml6ZV9kZXB0aChoaWdocCBmbG9hdCBkLCBoaWdocCBmbG9hdCB6TmVhcixoaWdocCBmbG9hdCB6RmFyKVxuICAgIHtcbiAgICAgICAgaGlnaHAgZmxvYXQgel9uID0gMi4wICogZCAtIDEuMDtcbiAgICAgICAgcmV0dXJuIDIuMCAqIHpOZWFyICogekZhciAvICh6RmFyICsgek5lYXIgLSB6X24gKiAoekZhciAtIHpOZWFyKSk7XG4gICAgfVxuICAgIGhpZ2hwIGZsb2F0IGxpbmVhcml6ZV9kZXB0aF9sb2coaGlnaHAgZmxvYXQgZCwgaGlnaHAgZmxvYXQgbmVhclosaGlnaHAgZmxvYXQgZmFyWikge1xuICAgICBmbG9hdCBkZXB0aCA9IHBvdygyLjAsIGQgKiBsb2cyKGZhclogKyAxLjApKSAtIDEuMDtcbiAgICAgZmxvYXQgYSA9IGZhclogLyAoZmFyWiAtIG5lYXJaKTtcbiAgICAgZmxvYXQgYiA9IGZhclogKiBuZWFyWiAvIChuZWFyWiAtIGZhclopO1xuICAgICBmbG9hdCBsaW5EZXB0aCA9IGEgKyBiIC8gZGVwdGg7XG4gICAgIHJldHVybiBsaW5lYXJpemVfZGVwdGgobGluRGVwdGgsIG5lYXJaLCBmYXJaKTtcbiAgIH1cbiAgIGhpZ2hwIGZsb2F0IGxpbmVhcml6ZV9kZXB0aF9vcnRobyhoaWdocCBmbG9hdCBkLCBoaWdocCBmbG9hdCBuZWFyWiwgaGlnaHAgZmxvYXQgZmFyWikge1xuICAgICByZXR1cm4gbmVhclogKyAoZmFyWiAtIG5lYXJaKSAqIGQ7XG4gICB9XG4gICB2ZWMzIGdldFdvcmxkUG9zTG9nKHZlYzMgcG9zUykge1xuICAgICB2ZWMyIHV2ID0gcG9zUy54eTtcbiAgICAgZmxvYXQgeiA9IHBvc1MuejtcbiAgICAgZmxvYXQgbmVhclogPW5lYXI7XG4gICAgIGZsb2F0IGZhclogPSBmYXI7XG4gICAgIGZsb2F0IGRlcHRoID0gcG93KDIuMCwgeiAqIGxvZzIoZmFyWiArIDEuMCkpIC0gMS4wO1xuICAgICBmbG9hdCBhID0gZmFyWiAvIChmYXJaIC0gbmVhclopO1xuICAgICBmbG9hdCBiID0gZmFyWiAqIG5lYXJaIC8gKG5lYXJaIC0gZmFyWik7XG4gICAgIGZsb2F0IGxpbkRlcHRoID0gYSArIGIgLyBkZXB0aDtcbiAgICAgdmVjNCBjbGlwVmVjID0gdmVjNCh1diwgbGluRGVwdGgsIDEuMCkgKiAyLjAgLSAxLjA7XG4gICAgIHZlYzQgd3BvcyA9IHByb2plY3Rpb25NYXRyaXhJbnYgKiBjbGlwVmVjO1xuICAgICByZXR1cm4gd3Bvcy54eXogLyB3cG9zLnc7XG4gICB9XG4gICAgdmVjMyBnZXRXb3JsZFBvcyhmbG9hdCBkZXB0aCwgdmVjMiBjb29yZCkge1xuICAgICAjaWZkZWYgTE9HREVQVEhcbiAgICAgICNpZm5kZWYgT1JUSE9cbiAgICAgICAgICByZXR1cm4gZ2V0V29ybGRQb3NMb2codmVjMyhjb29yZCwgZGVwdGgpKTtcbiAgICAgICNlbmRpZlxuICAgICAjZW5kaWZcbiAgICAgICAgXG4gICAgICAgIGZsb2F0IHogPSBkZXB0aCAqIDIuMCAtIDEuMDtcbiAgICAgICAgdmVjNCBjbGlwU3BhY2VQb3NpdGlvbiA9IHZlYzQoY29vcmQgKiAyLjAgLSAxLjAsIHosIDEuMCk7XG4gICAgICAgIHZlYzQgdmlld1NwYWNlUG9zaXRpb24gPSBwcm9qZWN0aW9uTWF0cml4SW52ICogY2xpcFNwYWNlUG9zaXRpb247XG4gICAgICAgIC8vIFBlcnNwZWN0aXZlIGRpdmlzaW9uXG4gICAgICAgdmVjNCB3b3JsZFNwYWNlUG9zaXRpb24gPSB2aWV3U3BhY2VQb3NpdGlvbjtcbiAgICAgICB3b3JsZFNwYWNlUG9zaXRpb24ueHl6IC89IHdvcmxkU3BhY2VQb3NpdGlvbi53O1xuICAgICAgICByZXR1cm4gd29ybGRTcGFjZVBvc2l0aW9uLnh5ejtcbiAgICB9XG4gICAgI2luY2x1ZGUgPGNvbW1vbj5cbiAgICAjZGVmaW5lIE5VTV9TQU1QTEVTIDE2XG4gICAgdW5pZm9ybSB2ZWMyIHBvaXNzb25EaXNrW05VTV9TQU1QTEVTXTtcbiAgICB2b2lkIG1haW4oKSB7XG4gICAgICAgIGNvbnN0IGZsb2F0IHBpID0gMy4xNDE1OTtcbiAgICAgICAgdmVjMiB0ZXhlbFNpemUgPSB2ZWMyKDEuMCAvIHJlc29sdXRpb24ueCwgMS4wIC8gcmVzb2x1dGlvbi55KTtcbiAgICAgICAgdmVjMiB1diA9IHZVdjtcbiAgICAgICAgdmVjNCBkYXRhID0gdGV4dHVyZTJEKHREaWZmdXNlLCB2VXYpO1xuICAgICAgICBmbG9hdCBvY2NsdXNpb24gPSBkYXRhLnI7XG4gICAgICAgIGZsb2F0IGJhc2VPY2MgPSBkYXRhLnI7XG4gICAgICAgIHZlYzMgbm9ybWFsID0gZGF0YS5nYmEgKiAyLjAgLSAxLjA7XG4gICAgICAgIGZsb2F0IGNvdW50ID0gMS4wO1xuICAgICAgICBmbG9hdCBkID0gdGV4dHVyZTJEKHNjZW5lRGVwdGgsIHZVdikueDtcbiAgICAgICAgaWYgKGQgPT0gMS4wKSB7XG4gICAgICAgICAgZ2xfRnJhZ0NvbG9yID0gZGF0YTtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgdmVjMyB3b3JsZFBvcyA9IGdldFdvcmxkUG9zKGQsIHZVdik7XG4gICAgICAgIGZsb2F0IHNpemUgPSByYWRpdXM7XG4gICAgICAgIGZsb2F0IGFuZ2xlO1xuICAgICAgICBpZiAoaW5kZXggPT0gMC4wKSB7XG4gICAgICAgICAgICAgYW5nbGUgPSB0ZXh0dXJlMkQoYmx1ZU5vaXNlLCBnbF9GcmFnQ29vcmQueHkgLyAxMjguMCkudyAqIFBJMjtcbiAgICAgICAgfSBlbHNlIGlmIChpbmRleCA9PSAxLjApIHtcbiAgICAgICAgICAgICBhbmdsZSA9IHRleHR1cmUyRChibHVlTm9pc2UsIGdsX0ZyYWdDb29yZC54eSAvIDEyOC4wKS56ICogUEkyO1xuICAgICAgICB9IGVsc2UgaWYgKGluZGV4ID09IDIuMCkge1xuICAgICAgICAgICAgIGFuZ2xlID0gdGV4dHVyZTJEKGJsdWVOb2lzZSwgZ2xfRnJhZ0Nvb3JkLnh5IC8gMTI4LjApLnkgKiBQSTI7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgYW5nbGUgPSB0ZXh0dXJlMkQoYmx1ZU5vaXNlLCBnbF9GcmFnQ29vcmQueHkgLyAxMjguMCkueCAqIFBJMjtcbiAgICAgICAgfVxuXG4gICAgICAgIG1hdDIgcm90YXRpb25NYXRyaXggPSBtYXQyKGNvcyhhbmdsZSksIC1zaW4oYW5nbGUpLCBzaW4oYW5nbGUpLCBjb3MoYW5nbGUpKTtcbiAgICAgICAgZmxvYXQgcmFkaXVzVG9Vc2UgPSBzY3JlZW5TcGFjZVJhZGl1cyA/IGRpc3RhbmNlKFxuICAgICAgICAgIHdvcmxkUG9zLFxuICAgICAgICAgIGdldFdvcmxkUG9zKGQsIHZVdiArXG4gICAgICAgICAgICB2ZWMyKHdvcmxkUmFkaXVzLCAwLjApIC8gcmVzb2x1dGlvbilcbiAgICAgICAgKSA6IHdvcmxkUmFkaXVzO1xuICAgICAgICBmbG9hdCBkaXN0YW5jZUZhbGxvZmZUb1VzZSA9c2NyZWVuU3BhY2VSYWRpdXMgP1xuICAgICAgICByYWRpdXNUb1VzZSAqIGRpc3RhbmNlRmFsbG9mZlxuICAgIDogcmFkaXVzVG9Vc2UgKiBkaXN0YW5jZUZhbGxvZmYgKiAwLjI7XG5cbiAgICAgICAgZmxvYXQgaW52RGlzdGFuY2UgPSAoMS4wIC8gZGlzdGFuY2VGYWxsb2ZmVG9Vc2UpO1xuICAgICAgICBmb3IoaW50IGkgPSAwOyBpIDwgTlVNX1NBTVBMRVM7IGkrKykge1xuICAgICAgICAgICAgdmVjMiBvZmZzZXQgPSAocm90YXRpb25NYXRyaXggKiBwb2lzc29uRGlza1tpXSkgKiB0ZXhlbFNpemUgKiBzaXplO1xuICAgICAgICAgICAgdmVjNCBkYXRhU2FtcGxlID0gdGV4dHVyZTJEKHREaWZmdXNlLCB1diArIG9mZnNldCk7XG4gICAgICAgICAgICBmbG9hdCBvY2NTYW1wbGUgPSBkYXRhU2FtcGxlLnI7XG4gICAgICAgICAgICB2ZWMzIG5vcm1hbFNhbXBsZSA9IGRhdGFTYW1wbGUuZ2JhICogMi4wIC0gMS4wO1xuICAgICAgICAgICAgZmxvYXQgZFNhbXBsZSA9IHRleHR1cmUyRChzY2VuZURlcHRoLCB1diArIG9mZnNldCkueDtcbiAgICAgICAgICAgIHZlYzMgd29ybGRQb3NTYW1wbGUgPSBnZXRXb3JsZFBvcyhkU2FtcGxlLCB1diArIG9mZnNldCk7XG4gICAgICAgICAgICBmbG9hdCB0YW5nZW50UGxhbmVEaXN0ID0gYWJzKGRvdCh3b3JsZFBvc1NhbXBsZSAtIHdvcmxkUG9zLCBub3JtYWwpKTtcbiAgICAgICAgICAgIGZsb2F0IHJhbmdlQ2hlY2sgPSBmbG9hdChkU2FtcGxlICE9IDEuMCkgKiBleHAoLTEuMCAqIHRhbmdlbnRQbGFuZURpc3QgKiBpbnZEaXN0YW5jZSApICogbWF4KGRvdChub3JtYWwsIG5vcm1hbFNhbXBsZSksIDAuMCk7XG4gICAgICAgICAgICBvY2NsdXNpb24gKz0gb2NjU2FtcGxlICogcmFuZ2VDaGVjaztcbiAgICAgICAgICAgIGNvdW50ICs9IHJhbmdlQ2hlY2s7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGNvdW50ID4gMC4wKSB7XG4gICAgICAgICAgb2NjbHVzaW9uIC89IGNvdW50O1xuICAgICAgICB9XG4gICAgICAgIG9jY2x1c2lvbiA9IGNsYW1wKG9jY2x1c2lvbiwgMC4wLCAxLjApO1xuICAgICAgICBpZiAob2NjbHVzaW9uID09IDAuMCkge1xuICAgICAgICAgIG9jY2x1c2lvbiA9IDEuMDtcbiAgICAgICAgfVxuICAgICAgICBnbF9GcmFnQ29sb3IgPSB2ZWM0KG9jY2x1c2lvbiwgMC41ICsgMC41ICogbm9ybWFsKTtcbiAgICB9XG4gICAgYFxufTtcblxuXG5cbmNvbnN0ICQyNmFjYTE3M2UwOTg0ZDk5JGV4cG9ydCQxZWZkZjQ5MTY4N2NkNDQyID0ge1xuICAgIHVuaWZvcm1zOiB7XG4gICAgICAgIFwic2NlbmVEZXB0aFwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogbnVsbFxuICAgICAgICB9LFxuICAgICAgICBcInJlc29sdXRpb25cIjoge1xuICAgICAgICAgICAgdmFsdWU6IC8qIEBfX1BVUkVfXyAqLyBuZXcgJDVXaGUzJFZlY3RvcjIoKVxuICAgICAgICB9LFxuICAgICAgICBcIm5lYXJcIjoge1xuICAgICAgICAgICAgdmFsdWU6IDAuMVxuICAgICAgICB9LFxuICAgICAgICBcImZhclwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogMTAwMC4wXG4gICAgICAgIH0sXG4gICAgICAgIFwidmlld01hdHJpeEludlwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogLyogQF9fUFVSRV9fICovIG5ldyAkNVdoZTMkTWF0cml4NCgpXG4gICAgICAgIH0sXG4gICAgICAgIFwicHJvamVjdGlvbk1hdHJpeEludlwiOiB7XG4gICAgICAgICAgICB2YWx1ZTogLyogQF9fUFVSRV9fICovIG5ldyAkNVdoZTMkTWF0cml4NCgpXG4gICAgICAgIH0sXG4gICAgICAgIFwibG9nRGVwdGhcIjoge1xuICAgICAgICAgICAgdmFsdWU6IGZhbHNlXG4gICAgICAgIH0sXG4gICAgICAgIFwib3J0aG9cIjoge1xuICAgICAgICAgICAgdmFsdWU6IGZhbHNlXG4gICAgICAgIH1cbiAgICB9LFxuICAgIGRlcHRoV3JpdGU6IGZhbHNlLFxuICAgIGRlcHRoVGVzdDogZmFsc2UsXG4gICAgdmVydGV4U2hhZGVyOiAvKiBnbHNsICovIGBcbiAgICB2YXJ5aW5nIHZlYzIgdlV2O1xuICAgIHZvaWQgbWFpbigpIHtcbiAgICAgICAgdlV2ID0gdXY7XG4gICAgICAgIGdsX1Bvc2l0aW9uID0gdmVjNChwb3NpdGlvbiwgMSk7XG4gICAgfWAsXG4gICAgZnJhZ21lbnRTaGFkZXI6IC8qIGdsc2wgKi8gYFxuICAgIHVuaWZvcm0gaGlnaHAgc2FtcGxlcjJEIHNjZW5lRGVwdGg7XG4gICAgdW5pZm9ybSB2ZWMyIHJlc29sdXRpb247XG4gICAgdW5pZm9ybSBmbG9hdCBuZWFyO1xuICAgIHVuaWZvcm0gZmxvYXQgZmFyO1xuICAgIHVuaWZvcm0gYm9vbCBsb2dEZXB0aDtcbiAgICB1bmlmb3JtIGJvb2wgb3J0aG87XG4gICAgdW5pZm9ybSBtYXQ0IHZpZXdNYXRyaXhJbnY7XG4gICAgdW5pZm9ybSBtYXQ0IHByb2plY3Rpb25NYXRyaXhJbnY7XG4gICAgdmFyeWluZyB2ZWMyIHZVdjtcbiAgICBsYXlvdXQobG9jYXRpb24gPSAxKSBvdXQgdmVjNCBnTm9ybWFsO1xuICAgIHZlYzMgZ2V0V29ybGRQb3NMb2codmVjMyBwb3NTKSB7XG4gICAgICAgIHZlYzIgdXYgPSBwb3NTLnh5O1xuICAgICAgICBmbG9hdCB6ID0gcG9zUy56O1xuICAgICAgICBmbG9hdCBuZWFyWiA9bmVhcjtcbiAgICAgICAgZmxvYXQgZmFyWiA9IGZhcjtcbiAgICAgICAgZmxvYXQgZGVwdGggPSBwb3coMi4wLCB6ICogbG9nMihmYXJaICsgMS4wKSkgLSAxLjA7XG4gICAgICAgIGZsb2F0IGEgPSBmYXJaIC8gKGZhclogLSBuZWFyWik7XG4gICAgICAgIGZsb2F0IGIgPSBmYXJaICogbmVhclogLyAobmVhclogLSBmYXJaKTtcbiAgICAgICAgZmxvYXQgbGluRGVwdGggPSBhICsgYiAvIGRlcHRoO1xuICAgICAgICB2ZWM0IGNsaXBWZWMgPSB2ZWM0KHV2LCBsaW5EZXB0aCwgMS4wKSAqIDIuMCAtIDEuMDtcbiAgICAgICAgdmVjNCB3cG9zID0gcHJvamVjdGlvbk1hdHJpeEludiAqIGNsaXBWZWM7XG4gICAgICAgIHJldHVybiB3cG9zLnh5eiAvIHdwb3MudztcbiAgICAgIH1cbiAgICAgIHZlYzMgZ2V0V29ybGRQb3MoZmxvYXQgZGVwdGgsIHZlYzIgY29vcmQpIHtcbiAgICAgICAgaWYgKGxvZ0RlcHRoICYmICFvcnRobykge1xuICAgICAgICAgIHJldHVybiBnZXRXb3JsZFBvc0xvZyh2ZWMzKGNvb3JkLCBkZXB0aCkpO1xuICAgICAgICB9XG4gICAgICAgIGZsb2F0IHogPSBkZXB0aCAqIDIuMCAtIDEuMDtcbiAgICAgICAgdmVjNCBjbGlwU3BhY2VQb3NpdGlvbiA9IHZlYzQoY29vcmQgKiAyLjAgLSAxLjAsIHosIDEuMCk7XG4gICAgICAgIHZlYzQgdmlld1NwYWNlUG9zaXRpb24gPSBwcm9qZWN0aW9uTWF0cml4SW52ICogY2xpcFNwYWNlUG9zaXRpb247XG4gICAgICAgIC8vIFBlcnNwZWN0aXZlIGRpdmlzaW9uXG4gICAgICAgdmVjNCB3b3JsZFNwYWNlUG9zaXRpb24gPSB2aWV3U3BhY2VQb3NpdGlvbjtcbiAgICAgICB3b3JsZFNwYWNlUG9zaXRpb24ueHl6IC89IHdvcmxkU3BhY2VQb3NpdGlvbi53O1xuICAgICAgICByZXR1cm4gd29ybGRTcGFjZVBvc2l0aW9uLnh5ejtcbiAgICB9XG4gIFxuICAgIHZlYzMgY29tcHV0ZU5vcm1hbCh2ZWMzIHdvcmxkUG9zLCB2ZWMyIHZVdikge1xuICAgICAgaXZlYzIgcCA9IGl2ZWMyKHZVdiAqIHJlc29sdXRpb24pO1xuICAgICAgI2lmZGVmIFJFVkVSU0VERVBUSFxuICAgICAgZmxvYXQgYzAgPSAxLjAgLSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAsIDApLng7XG4gICAgICBmbG9hdCBsMiA9IDEuMCAtIHRleGVsRmV0Y2goc2NlbmVEZXB0aCwgcCAtIGl2ZWMyKDIsIDApLCAwKS54O1xuICAgICAgZmxvYXQgbDEgPSAxLjAgLSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgLSBpdmVjMigxLCAwKSwgMCkueDtcbiAgICAgIGZsb2F0IHIxID0gMS4wIC0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwICsgaXZlYzIoMSwgMCksIDApLng7XG4gICAgICBmbG9hdCByMiA9IDEuMCAtIHRleGVsRmV0Y2goc2NlbmVEZXB0aCwgcCArIGl2ZWMyKDIsIDApLCAwKS54O1xuICAgICAgZmxvYXQgYjIgPSAxLjAgLSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgLSBpdmVjMigwLCAyKSwgMCkueDtcbiAgICAgIGZsb2F0IGIxID0gMS4wIC0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwIC0gaXZlYzIoMCwgMSksIDApLng7XG4gICAgICBmbG9hdCB0MSA9IDEuMCAtIHRleGVsRmV0Y2goc2NlbmVEZXB0aCwgcCArIGl2ZWMyKDAsIDEpLCAwKS54O1xuICAgICAgZmxvYXQgdDIgPSAxLjAgLSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgKyBpdmVjMigwLCAyKSwgMCkueDtcbiAgICAgICNlbHNlXG4gICAgICBmbG9hdCBjMCA9IHRleGVsRmV0Y2goc2NlbmVEZXB0aCwgcCwgMCkueDtcbiAgICAgIGZsb2F0IGwyID0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwIC0gaXZlYzIoMiwgMCksIDApLng7XG4gICAgICBmbG9hdCBsMSA9IHRleGVsRmV0Y2goc2NlbmVEZXB0aCwgcCAtIGl2ZWMyKDEsIDApLCAwKS54O1xuICAgICAgZmxvYXQgcjEgPSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgKyBpdmVjMigxLCAwKSwgMCkueDtcbiAgICAgIGZsb2F0IHIyID0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwICsgaXZlYzIoMiwgMCksIDApLng7XG4gICAgICBmbG9hdCBiMiA9IHRleGVsRmV0Y2goc2NlbmVEZXB0aCwgcCAtIGl2ZWMyKDAsIDIpLCAwKS54O1xuICAgICAgZmxvYXQgYjEgPSB0ZXhlbEZldGNoKHNjZW5lRGVwdGgsIHAgLSBpdmVjMigwLCAxKSwgMCkueDtcbiAgICAgIGZsb2F0IHQxID0gdGV4ZWxGZXRjaChzY2VuZURlcHRoLCBwICsgaXZlYzIoMCwgMSksIDApLng7XG4gICAgICBmbG9hdCB0MiA9IHRleGVsRmV0Y2goc2NlbmVEZXB0aCwgcCArIGl2ZWMyKDAsIDIpLCAwKS54O1xuICAgICAgI2VuZGlmXG4gIFxuICAgICAgZmxvYXQgZGwgPSBhYnMoKDIuMCAqIGwxIC0gbDIpIC0gYzApO1xuICAgICAgZmxvYXQgZHIgPSBhYnMoKDIuMCAqIHIxIC0gcjIpIC0gYzApO1xuICAgICAgZmxvYXQgZGIgPSBhYnMoKDIuMCAqIGIxIC0gYjIpIC0gYzApO1xuICAgICAgZmxvYXQgZHQgPSBhYnMoKDIuMCAqIHQxIC0gdDIpIC0gYzApO1xuICBcbiAgICAgIHZlYzMgY2UgPSBnZXRXb3JsZFBvcyhjMCwgdlV2KS54eXo7XG4gIFxuICAgICAgdmVjMyBkcGR4ID0gKGRsIDwgZHIpID8gY2UgLSBnZXRXb3JsZFBvcyhsMSwgKHZVdiAtIHZlYzIoMS4wIC8gcmVzb2x1dGlvbi54LCAwLjApKSkueHl6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAtY2UgKyBnZXRXb3JsZFBvcyhyMSwgKHZVdiArIHZlYzIoMS4wIC8gcmVzb2x1dGlvbi54LCAwLjApKSkueHl6O1xuICAgICAgdmVjMyBkcGR5ID0gKGRiIDwgZHQpID8gY2UgLSBnZXRXb3JsZFBvcyhiMSwgKHZVdiAtIHZlYzIoMC4wLCAxLjAgLyByZXNvbHV0aW9uLnkpKSkueHl6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAtY2UgKyBnZXRXb3JsZFBvcyh0MSwgKHZVdiArIHZlYzIoMC4wLCAxLjAgLyByZXNvbHV0aW9uLnkpKSkueHl6O1xuICBcbiAgICAgIHJldHVybiBub3JtYWxpemUoY3Jvc3MoZHBkeCwgZHBkeSkpO1xuICB9XG4gICAgdm9pZCBtYWluKCkge1xuICAgICAgICB2ZWMyIHV2ID0gdlV2IC0gdmVjMigwLjUpIC8gcmVzb2x1dGlvbjtcbiAgICAgICAgdmVjMiBwaXhlbFNpemUgPSB2ZWMyKDEuMCkgLyByZXNvbHV0aW9uO1xuICAgICAgICBoaWdocCB2ZWMyWzRdIHV2U2FtcGxlcztcbiAgICAgICAgdXZTYW1wbGVzWzBdID0gdXY7XG4gICAgICAgIHV2U2FtcGxlc1sxXSA9IHV2ICsgdmVjMihwaXhlbFNpemUueCwgMC4wKTtcbiAgICAgICAgdXZTYW1wbGVzWzJdID0gdXYgKyB2ZWMyKDAuMCwgcGl4ZWxTaXplLnkpO1xuICAgICAgICB1dlNhbXBsZXNbM10gPSB1diArIHBpeGVsU2l6ZTtcbiAgICAgICAgI2lmZGVmIFJFVkVSU0VERVBUSFxuICAgICAgICBmbG9hdCBkZXB0aDAwID0gMS4wIC0gdGV4dHVyZTJEKHNjZW5lRGVwdGgsIHV2U2FtcGxlc1swXSkucjtcbiAgICAgICAgZmxvYXQgZGVwdGgxMCA9IDEuMCAtIHRleHR1cmUyRChzY2VuZURlcHRoLCB1dlNhbXBsZXNbMV0pLnI7XG4gICAgICAgIGZsb2F0IGRlcHRoMDEgPSAxLjAgLSB0ZXh0dXJlMkQoc2NlbmVEZXB0aCwgdXZTYW1wbGVzWzJdKS5yO1xuICAgICAgICBmbG9hdCBkZXB0aDExID0gMS4wIC0gdGV4dHVyZTJEKHNjZW5lRGVwdGgsIHV2U2FtcGxlc1szXSkucjtcbiAgICAgICAgI2Vsc2VcbiAgICAgICAgZmxvYXQgZGVwdGgwMCA9IHRleHR1cmUyRChzY2VuZURlcHRoLCB1dlNhbXBsZXNbMF0pLnI7XG4gICAgICAgIGZsb2F0IGRlcHRoMTAgPSB0ZXh0dXJlMkQoc2NlbmVEZXB0aCwgdXZTYW1wbGVzWzFdKS5yO1xuICAgICAgICBmbG9hdCBkZXB0aDAxID0gdGV4dHVyZTJEKHNjZW5lRGVwdGgsIHV2U2FtcGxlc1syXSkucjtcbiAgICAgICAgZmxvYXQgZGVwdGgxMSA9IHRleHR1cmUyRChzY2VuZURlcHRoLCB1dlNhbXBsZXNbM10pLnI7XG4gICAgICAgICNlbmRpZlxuICAgICAgICBmbG9hdCBtaW5EZXB0aCA9IG1pbihtaW4oZGVwdGgwMCwgZGVwdGgxMCksIG1pbihkZXB0aDAxLCBkZXB0aDExKSk7XG4gICAgICAgIGZsb2F0IG1heERlcHRoID0gbWF4KG1heChkZXB0aDAwLCBkZXB0aDEwKSwgbWF4KGRlcHRoMDEsIGRlcHRoMTEpKTtcbiAgICAgICAgZmxvYXQgdGFyZ2V0RGVwdGggPSBtaW5EZXB0aDtcbiAgICAgICAgLy8gQ2hlY2tlcmJvYXJkIHBhdHRlcm4gdG8gYXZvaWQgYXJ0aWZhY3RzXG4gICAgICAgIGlmIChtb2QoZ2xfRnJhZ0Nvb3JkLnggKyBnbF9GcmFnQ29vcmQueSwgMi4wKSA+IDAuNSkgeyBcbiAgICAgICAgICAgIHRhcmdldERlcHRoID0gbWF4RGVwdGg7XG4gICAgICAgIH1cbiAgICAgICAgaW50IGNob3NlbkluZGV4ID0gMDtcbiAgICAgICAgZmxvYXRbNF0gc2FtcGxlcztcbiAgICAgICAgc2FtcGxlc1swXSA9IGRlcHRoMDA7XG4gICAgICAgIHNhbXBsZXNbMV0gPSBkZXB0aDEwO1xuICAgICAgICBzYW1wbGVzWzJdID0gZGVwdGgwMTtcbiAgICAgICAgc2FtcGxlc1szXSA9IGRlcHRoMTE7XG4gICAgICAgIGZvcihpbnQgaSA9IDA7IGkgPCA0OyArK2kpIHtcbiAgICAgICAgICAgIGlmIChzYW1wbGVzW2ldID09IHRhcmdldERlcHRoKSB7XG4gICAgICAgICAgICAgICAgY2hvc2VuSW5kZXggPSBpO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGdsX0ZyYWdDb2xvciA9IHZlYzQoc2FtcGxlc1tjaG9zZW5JbmRleF0sIDAuMCwgMC4wLCAxLjApO1xuICAgICAgICBnTm9ybWFsID0gdmVjNChjb21wdXRlTm9ybWFsKFxuICAgICAgICAgICAgZ2V0V29ybGRQb3Moc2FtcGxlc1tjaG9zZW5JbmRleF0sIHV2U2FtcGxlc1tjaG9zZW5JbmRleF0pLCB1dlNhbXBsZXNbY2hvc2VuSW5kZXhdXG4gICAgICAgICksIDAuMCk7XG4gICAgfWBcbn07XG5cblxuXG5cblxuXG5cblxuXG5jb25zdCAkMDYyNjlhZDc4ZjNjNWZkZiR2YXIkQmx1ZU5vaXNlID0gYDVMN3BQNFVYck9Jci9WWjFHM2Y2cDg5RklXVTdscWM3SjNEUHhLakpVWE9ESm9IUXpmL2FOVk0rQUJsdmhYZUJHTjdpQzBXa21UakVhQXFPSXRCZkJkYUs1S1NHVjFFVDVTT0tsM3g5Sk9YNXcyc0FsNis2S2pEaFZVSGdicXE3RFo1RWVZemJkU054dHJRTFcvS2tQSm9PVEc0dTVDQlVaa0NLSG5pWTlsN0RVZ2p1ejcwOHpHMUhJQzhxZm9oaTF2UGpQSDlMcTQ3a3NqUnJqd1hENE1sVkNqZEFxWUZHb2RROHRSbUhrT2ZxNHdWUklBSHZvYXZQSHZOMWxwazNYNFkxeXpBUEdlOFM5S0JzM2NyYzRHd2xVMWRFT1hpV29sL21nUXF4a05xQjF4ZDA0KzBCbXB3ajBHY0NjNE5VaStjNzMxRlV4anZhZXhDa0NKMHFockpKKytodFdxZXROQzROZXdDbHU4YUZSU3dycWlKRUdlK3F0VGc0Q1lDSGFGMXdKSTBzeS9aQlFBSTBxQU15QnZWaldabHYycGRrQ2FybzllV0RMSzVJNG1iYjhFNGQ3aFpyOWRESmlUSm02Qm1iNVMrMkY3eWFsL0pQZGVMVWZ3cTdqbVZMYVFmaHY0dFdNSkF0N1Y0c0c5THVBdjJvUEpnU2oxbm5sQnZQaWJmSE0yVHJsV0h3R0NMR3hXLzVKbTJUb3RhREwrcEhETTVwbjFyMFV1VFoyNE44UzVrNjhiTEhXOXRmRCsyazR6R2V2MjNFeEpiNFlUUktXcmo4Mk41TGpKMjZsajFCa0daMENzWExHR0VMb1BhWVFvbWpUcVB4WXFoZndPd0RsaU5HVnF1eDlmZnV5YnFPS2dzYkI1MUIxR2JaZkc4dkhEQkUySlFHaWIxbW5DbVdPV0FNSmNITjBjS2VESFlUZmxiRFRWWGFqdHI2OG13ZlJqZTZXdWVRLzZ5V3FtWk1MV05IN1AyN3pHRmhNRnFhcWZnMTFRODhnLzlVQS9GUk9lOXlmcTB5T08wcG5OQXh2ZXBGeTJCcEViY2dHK21DeWpDQzAxSldsT1psSVBkZjFUdGx5T3Q3TDk0VG9ZR0N1a29GdDRPcXdPcm9mYW1qRUNwU2dLTExtclJNK3NOUkF3MTJlYXFrOEt0ZEZrN3BuMkljRFFpUFhDaDE2dDFhK3BzaSt3OXRvd0hUS1B5UU0wU3RLcjYxYjJCbk4xSFUrYWV6Rk5CTGZIVGlYd2hHVGJkeExMbXJzQUdJVlNpTkFlQ0dFOEdsQjBpT3Yydjc4a1AwQ1RtQVBVRXFuSFlSU0RsUCtMNm0vcllqRUs2UTg1R1JESmkyVzIwLzdOTFBwU09hTVIrK0lGdnBrY3dSdWM1OWo4aGg5dFlsYzF4amR0MmptcDlLSmN6QjdVOVA0M2ludXhMT3YxMVA1L0hZSDVkNmdMQjBDc2JHQzhBUGpoK0VjQ1AwekZXcWxhQUNad2VMaFZmdjN5aXlkOFIzYmRWZzhzUktzeFB2aERhUHBpRnA5K01OKzBVYTBic1ByK2xoeGZaaE1obGV2a0xiUjRadmNTUlA2QXBRTHkzK2VNaDllaENCM3o1RFZBYU4zUDZKOHBpNVFhODhaUXNPdUNUV3lINnE4eU1mQnc4eThubTZqYU94SmhQSDZIZjBJNGptQUxVQnNXS0g0Z1dCbnlpakhoN3ozLzFIaFF6RkxSRFJySVF3VXR1MTF5azdVMGdEdy9GYXRPSVpPSmFCeDNVcWJVeFNaNmRib0ZQbTVwQXl5WEMyd1lkU1dscFp4L0QyQzZoRE8yc0pNNEhUOUlLV1dtRGtaSU8yc2kvNkJLSHJ1WElFRHBmQXR6M3hEbElkS25ubHFua2ZDeXk2dk5PUHl1b1dzU1dCZWlOMG1jZklybk90cDJqN2J4ak9rcjI1c2tmUy9sd09DNjkyY0VwN1RLU2x5bWJzeXpvV2cvMEFONjZTdlFZbzZCcXBOd1BwVGFVdTI1ek1XbHdWVWRmdTFFRWRjME8wNlRJMEptSGs0ZjZHWlFiZk9zLy9PZGd0R1BPNnVMb2FkSnljUjhaODBya2Q4OFFvTm1pbVpkOHZjcFFLU2NDRmt4SDFSTVRrUGxOM0s3Q0wvTlNNT2lYRXZ4cm45VnlVUEZlZTYzdVJmbGdhUE1Tc2FmdnFNZ3pUdDNUMVJhSE5MTEZhdFFiRDBWaGE0WVhaLzZBa2U3b25NNjVuQzljeUxrdGVZa0RmSG9KdGVmN3dDcldYVEswK3ZIMzhWVUJjRkpQMCt1VVhwa2lLMGdEWE5BMzlITC9xZFZjYU9BMTZrZDJnenE4YUhwTlNhS3RnTUxKQzZmZExMUy9JLzRsVVdWMitkalk5UmMzUXVKT1VybEhGUUVSdFhONHhKYUFIWkVSQ1VRWjlORDJwRXRaZzhkc25pbGNucW1xWW4zYzFzUnlLMHppS3BITnl0RXlpMmdtenhFRmNodlQxdUJXeFpVaWtrQWxXdXlxdnZodGVTRzlrRmhUTE5NOTdzM1gxaVMyVWJFNmN2QXBnYm1lSi9LcXRQME5OVDNiWmlHOVRVUkluQ1p0VnNOWnpZdXM2T24wd2NkTWxWZnFvOFhMaFQ1b2phT2s0RHRDeWVvUWtCdDFtZjVsdUZOYUxGakkvMWNuUGVmeUNRd2NxNWlhLzRwTjROQit4RS8zU0VQc2xpSnlwUzk2NFNJNm81ZkRWYTBJRVJSOERvZVErMWl5UkxVMXFHWWV4QjYxcGg0cGtHMXJmM2MyWUQ2QnkxcEZDbXd3OUIwcjJWakZlYXVia0lkZ1d4NFJLTFFSUExFTmRHbzhlekk1bWtOdGRDd3MxOWFQMXVIaGVuRCtIS2E4R0RlTHVsYjJmaU1SaFUyeEp6eno5ZTR5T01QdkVuR0VmYkNpUTE3blVEcGNGRFd0aHI2OG1oWjRXaUhVa1JwYVZXSk5FeHVVTGNHa3V5VkxzUWo1OXBmNk9IRlI3dG9maHk5Rk1yV1BDRXZYMWQ1c0NWSnQ4eUJGaUI2Tm9PdXdNeTR3bHNvOUkyRzRFNS81QjJjNnZJWlVVWTlmRnVqVDNocGtkVHVWaGJoQndMQ3RubElqQnBONGNxK3dhWjB3WFNybWViY2wrZGNyYjdzUGg5akt4RklOa1NjRFRCZ2pTVWZMa0MzaHVKSnMvTTRNOEFPRnhiYlNJVnBCVWFyWUZtTHBHc3YrVjZUSm5XTlR3STQxdHVid283UVNJMVZPZFJLVC9QcDhVM29LMmNpRGJldVduQUdBQU52UWpHZmNld2RBZG82SDgzWHpxbEsvNHl1ZHRGSEpTdjlZK3FKc2t3blZUb0gxSTArdEozdnNMQlh0bHZNekxJeFVqLzhMY3FabnJOSGZWUmdhYkZOWFcwcXBVdkRneG5QM2Y1NEtvb1IzTkkrMlEvVkhBWUZpZ01rUUU1ZExINkM2ZkdzL1RLZUU2RTJqT2haUWNQOS9yckpqSktjTFlkbjVjdzZYTENVZTlGN3F1azVZaGFjK25ZTDVIT1h2cDZRLzVxYmlRSGt1ZWJhblg3N1lTTngzNFlhV1lwY0VIdVkxdS9sRVZUQ1E3dGFQYXczb05jbi9xSmhNekdQWlVzM1hBcTQ4d2ovaENJTzJkNWFGZGZYblMweWc1Ny9qeHpESkJ3a2RPZ2VWbnl5aDE5SXoxVXFpeXNUNEoxZWVLd1V1V0VZbG4yM3lkdFA3ZzNSMUJudm54cUZQQW5PTWdPSW9wMmRrWFBmVWgvOVpLVjNaUWJaTmFjdFBENHFsNVFnOUN4U0JuSXd6bGovdHNlUUtXUnN0d05iZjE3bmVHd0RGRldkbS84ZituRFd0L1dsS1YzTVVpQW0zY2k2eFhNRFNMNXViUFhCZy9nS0VFN1RzWlZHVWNySWJkWElMY01uZ3ZHczd1bnZsUEpoNm9hZGVCRHFpQXZpSVovaXlpVU1kUVpBdWYvWUJBWTBWUDFoY2dJbnVXb0tieDMxQU9qeVROMk9PSHJsdGhCM255OUpLSE9BYzhCTXZxb3Bpa1BsZGN3SVFvRnhUY2NLS0llSTgxNUdjd2FLRExzTWJDc3hlZ3J6WGw4RTBicGljL3hmZlU5eTFEQ2dlS1pvRjJQSVk3N1JJbjZrU1JkQmlHZDhOdE53VDc0ZHllRkJNa1lyYVBrdWROMjZ4OU5QdUJ0NGlDT0FuQkZhTlNLVmdLaVpRcnV3MjJrTTFmZ0JLRzdjUFlBeGRISjhNNFYvanpCbjJqRUpnK2prL2pqVjRvTW1NTk9wS0I1b1ZwVmg3dEs1MjlaKzV2S1owTlNZMkE0WWRjVDB4NEJka29ORURycHNUbWVrU1Rqdng5WkJpVEhybTlNL24vaEdtZ3BqejRXRWp0dFJmQUV5NURZSDV2Q0svOUd1VlBhNGhvQXBGYU5sckZEL24yUHBLT3cyNGlLdWpLaFZJejQxcDFFMEh3c0NkL2MxN09BMEgwUmpaaTFWL3JqSkxleFV6cG1YVE1JTXV6YU9CYlU0ZHh2UU1neXZ4SnZSNkR5RjNCYUhrYXFUNFAzRlJZbG0remg4RUVHZ21rTnFEMVdSVXViRFc2MlZxTG9IOFVFZWxJcEw3QzhDZ3VXV0dHQ0FJRFBtYTlibmgrN0lKU3QwQ242QUNFUjJtWWs4ZExzck43MFJVVkxpRTBpZyswOHlQWTlJT3R1cUhmL0tZc1Q4NEJ3aE1jVnE3dDhxMVdWanBKR055WGR0SVBJamhBemFidHJYMDNJdG4yOVFPM1RDaXhFOVdwa0hJT2RBb0d2cUNydzFEM3g5ZzlQeDh1MHlaWnV1bFp1R3kwdmVTWTM0S0RTbGhzTzF6eDJaTXJwREJ6Q0hQQjRuaXdBcGs2TmV2SXZtQnhVMys0eWFld0R2Z0VRREo2T2Y1aVJ4akFJcHA5VU84RXpOWTRibGo0cWg4U0NTWlRxYmUvbFNoRTZ0TlU5WTVJb1dIZUp4UGNIRjlLd1lRRDdsRmNJcGNzY0hyY2ZrSEpmTDJsTDF6Y3pLeXdFRjdCd2tqWEVpcmdCY3ZOV2F5YXRxZFRWVDVvTGJ6VG1FRDNFT1lCU1hGeWIyVklZazN0MGRPWldKZEcxblArVzdRZnllYjhNU0l5VUdLRUE1N3B0UHhyUEhLWUdaUEhzdUJxUXVWU3JuMGk4S0pYK3JsekFxbzhBYXdjaHNKMjZGY2t4VGY1K2pvVGN3KzJ5OGM4YnVzaHBSWUViZ3JkcjY0bHRFWVBWMkFiVmdLWFYzWEFDb0QxZ2JzMDFDRXhiSkFMa3VJdGpmWU4zKzZJOGtiaVRZbWR6QkxhTkMreHU5ei9lWGNSUVYxTG84Y0pvU3NLeVdKUHVUbmN1NXZjbWZNVUFXbXV3aGp5bUsxcmhZUjhwUU1YTlFnOVgrNWhhNWZFbmFwK0xoVUwxZDVTVVJaejlyR2RPV0xock1jTUtTYVUzTGhPUS82YTZxU0N3Z3pReENXMmdGczUzZnB2Zld4aEgreERIZEtSVjZ3MjluUTZyTnFkOWJ5K3ptMU9well5Snd2RnlPa3JWWFFVd3Q0SGFhcG53ZUNhN1RqMk1wL3RUNFljWTNRL3RrMWN6Z2t6bFY1bXBEcmRwMXNwT1lCOGlvbkF3eHVqamRoajV5OXFFSHUwdWMzNlBBS0FZc0tMYUVvaXdQbm9iMHBkbHVQV2R2NHNOU2xHOEdXVmlJK3gvWjREa1cva1NzMmlFM0FERmpnNFRDdmdDYlgzdjBIejBLWmtlcnJwekVJdWtBdXNpZERzMmcvdzB6Z21MblpYdlZyNWtrcHdRVExaMEw2dWFUSGwwTFZpa0l1TklWUG1MM2ZPUUpxSWRmenltVU4wenVjSXJEaW50Qm42SUNsL2luajV6dGVJU3Y1aEVNR01xdEhjMmdoY0ZKdm1IM1poSVppMzR2cXFURkNiOXBsdFRZejU4MlkzZHdZYUhiOWtoZGZ2ZTFZcnl6RXdFS2JJOHFtNjJxditOeWxsQytXeExMQUpqejBaYUVGMmFUbjM1cWVGbWtiUDZMRFljYndxV3hBMFdLc3RlQjd2eThiUkhFNHI4TGh1YldEYzBwYmU5MFhja1NEREFrUmVqMFRRbG1Xc1d3YXoxOFR4MnBoeWtWdnd1SVJ6ZjRrdDlzclQ4Tjdnc01qTXMwTkxBQWxkYWJGZjJ0aU1vYWF4SGNaU1g1MVdQYzFCcndBcE14aWgyMjdxVFprY2d0a2RLMWgzMTRYdlpLVUtoL1h5c1dZbmsxU1Q0a2lCSTFCOU9sZlRqQjNXSHpUQVJlRkxvZnNHdGlrd3BJWHpRQmMvZ09qejJUaGxqMzZXTjBzeHlmNFJtQUZ0cll0NjRmd20rVGhqYmhsbVVUWnplYkxsNHlBa0FxekpTZmpQQlpTMkgvSXZra1RVZFZoMHFkQjZFdWlIRWpFaWw1bGs5QlRQenhtb1c0Sng1NDNoaXl5NEFTZFlBMkROb3Byc1I5aXdHRndGRzNGMnZJUk95NEw1Q1pybDIzMCtrNzMzSndib1NOQktuZ3NhRlB0cW8rcTNtRkZTakMxazBrSUFGbUtpaGFZU3dhU0Y3a29ubVlIWldtY2h1YXExNVRwbmVBMkFEU1J2QTA3STdVUzBsVE9PZktyZ3hoelJsMHVKaWhjRVpoaFlXeE9ianZOVEovNXNSNEFhNXdPUWhHQ2xHTGI3NDZjSmhRMkU2SmllMWhiR2dXeFVIN1lTS0VUcHRyVGVSL3hmY01OazJXTTEyUzBYRWxDOWtsUjhPN2pMWWVrRU9aZHNjUDB5cFNkb0NWWkFvSysyanUyUEhFODY5UTlyeENzOURWUWNvNEJyaWlQYkNqTi84dEJqc2FoNEl1Ym9SNVFibWJ5RHBjZFhWeEdNeHZXS0lqb2NCdUtiamIrQjRIdmt1bmJHMHdYMElGQ2pRS29OTUZJS2NKU0pYdGtQM0VPK0oxNnVoNGltZzBMUWxCQU9Zd0JMdXB1NXIxTkFMTW8wZzN4a2Q5YjRmN0tvQ0JXSGV5azI0Rm1ZVUN5L1BHTHYweEVyT1R5T1JwOFRKNW5uYzJrMWRPVkJUSm9rN2lIeWU5ZHd4d1JWUDNjN2VBUzhwTW1KWUhHcHpJSHo2aWkyV0ptOEhNVFBBWmRBNHErdWdqM1BOQ0wvTjQ1a3lnbHF2UVY0Zi8rcnlEREc1UlB5NUhWb1Y5RlZ1SmNxMmR4RjlZMGhlVm9pcFY2cTFMeWZBZXVNemJzVVYrcnNTQm1DU1YrMUNkS2x4eTBUMFk2T20wWDY3MDFVUm0yTWw2RElRZ0ovM0tPNmt3Y01ZUnJtS3NZN1RmeFdoU1habGwrMVBmeVJYZTlIUzB0MUlLVFFNWkw3WnFROEQvbytlbjU3WTlYQVE5QytrWll5a05yMHhPTXhFd3UyK0NwcG02OW1ReVRtM0g3UVg2a0h2WEYyMDFyK0tWQWYzNTRxeXBKQzVPSFNlQlU0N2JNMWJUYVZtZFZFV1ErOUNjdnZIZHU4VWU1VW5kSE0rRWV1a21SODJ2b1FwZXRaN1dKanlYcyt0UFM2MG5rMDlneW11T1JvSE50Ym0wVnV2eWlnaUV2T3N5SGlSQlc3VjZGeVRDcHBMUEVIdmVzYW45MVNsRWgxL1FFdW5xK3FnUkVGWEJ5RHdOS2NBSDVzOC9SRmc4aFA0d2NQbUZxWDB4WEdTS1kwODdicVJMc0JaZTUyalRoeDBYTGtoS1FVV1B2STE4V1FRUzNnMlJhMXB6UTFvTkZLZGZKSmp5YUg1dEpINncwL3VwSm9id0I4S1o1Y0lzOUxuVkd4ZkJhSFhCZnZMa05wYWI3ZHBVNlRkY2JCSWMrQTRicVhFL1h0OC94c0dRT2RvWHJhNFVzNW5EQU02djJCTkJRYUdNbWdNZlFRVitpa1R0ZVNIdnlsOHdVeFVMaVlSSUVLYWlEeHBCSm55ZjlPb3FRZFpWSjhhaHFPdnV3cXE1bW5EVUFVelVyL0x2czF3THUyRityNGVaTWZKUEw0Z1Y1bUtMa0lUbW96Um5UdkE3VkFCYXhabUZSdGtodlU1aUg5UlExejI2a3U3YUFCb2t2cHR4N1JLWkJWTDZkdmVMS096ZzBOQzdIQXhjZzVrRTF3dXlKaUVRTE9wTzBtYTNBdFdEMlEyV21uMm9QWmVEWUF3VnlFcHh1d0R5N2l2bWRVRFNMOTVvbDNoMkpCeVRNb3ZPQ2d4WjFxNEU1bnd3YTcrNFd0REFzZTZiRGRyMjdYZ0FpNVB4M0lXYnlaL3ZSaUVDS3dPTWVKU3VJbDhBNERzMGVtSTNTZ0tWVldWTzV1eWlFVUVUK3VjRXEwY2FzQStEUXloelJjOGorUGxvMHB4S3luQi90MHVYb2QxRlZWNGZYMXNDNGtEZndGYVVER1E0cDlIWWdhTXFJV1gzT0YvUzgrdmNSMEpTMGJEYXBXS0p3QUlJUWlSVXp2aDVZd3R6a2pjY2JiclQ5S3kvcXQ1WDdNQUdBMGx6aDQzbURGOUVCNmxDR3VPL2FGQ01oZE9xTnJ5dmQ3M0tkSk55M214dFQ4QXFnbUc0eHE3ZUUxakt1NnJWMGc4VUd5TWF0enlJTWppT0NmNGxJSkZ6QWZ3RGJJZkM3MlRKL1RLK2NHc0xSOGJscGpsRUlMakQ4TXhyN0lmZmhiRmhnbzEyQ3pYUlEyTzhKcUJKNzArdDEyMzg1dFNtRkM4T3IrVThzdk9hb0dvb2pUMS9FbWpSTVQ3eDJpVFVaN055MDJWR2VNWlR0R3kwMjl0R04xLzlrN3gzbUZ1NjNsWW5hV2pmSlQxbTF6cFdPM0hTWHBHa0ZxVmQvbTNrRE12NFg5cm1MT3B3RWV1OHI2VEk2QzJ6VUcrTVQ2djkwT1UzeTVoS3FMaHB5RkxHdGtaaERtVWcvVzFKR1NtQThOMVRhcFI0S255K1A2K0R1TWFkWjkreEJidjA2bmZPak13a29Uc2pHMHpGbU5idmx4RWp3K1BsNVFZSytWOFF5Yitua25aME5iL09maTkrVjBlb050VHJ0RDEvMHd6VUdHRzV1MkQvSjFvdU8vUGpYRkpWeDZMdXJWblBPeUZWYlp4N3MzWlNqU3ErN1lOM3d6VGJGYlV2UDhHQmg3Y0tpZUp0NTZTSW93UTJJNTc3K1VFWHJ4VUtNRk8rWGFMTENBTHVpSldCMnZVZHBzVCtrUSthZG9lVGZ3T3VsWGhkL0taN3lnamo2UGh2R1QxeHpmVDdoVHdkNmR6U0I0eFY3MENlc0hDMGRzZzJWeXVqbE1HQktqZzVzbmJySEhYL0xOajNTc29MR1NYK2JaTlRERENOVFhoK2RDVlBsajRLOCtoSi9rVmRkcmJ0WncyNkh4NXFZaXYzb05OZzVibEhSU1B0bW9qaFptQlFBejhzTEM5bkF1V05TejFkSW9mRnRscnlFS2tsYmRraEJDY3g1ZGhqN3BpblhETmxDZWF0Q2VUQ0VqWUNwWjNIUmY1UXpVY1JSMVRkYjNnd3RZdHBQZGdNeG1XZkpHb1pTdTFFc0NKYkloUzE2RWQ5Nys4YnI0QXIxbUIxR2NuWlZ4L0hQdEpsNENnYkhYcnJEUHdsRTRvZDhkZVJRWUx0OUlsc3ZDcWdlc01tTEFWeEIraWdIN1dHVGNZL2UzbExISjRya0JnaDJwMVFwVUJSYi9jU1FzSkNib3NGRGthbGJKaWdpbWxkVks3VElIS1NxMnc4bWV6a3U5aGd3OGZYSnhHZFhvTDFnZ21hNTJrWHpqUDc4bDBkMHpNd3RUVmx0MEZxblJ5R0xQR0VqbUlDemdTcDdYUEZsVXI3QWVNY2xRNG9wcXdCRkluemlNNUY4b0pKOHFldWNrR09uQWNaWk9MbDErWmhHRjE3cGZJdXVqaXB3RkpMN0NoSUlCMnZsbzBJUVpHVEpQTmEyWWpOY0dVdythL2dXWUxrQ3ArYk9HSVloV3IwOFVJRTcwOVpFSGxVb0VidW16Z3BKdjFEMCtoV1lORXBqK2xhb1pJSzV3ZU8yREZ3TEw2VUJZTnJYVG05WXZ2eGVOOVU5b0tzQjN6S0J3ekZGd0RnaWQ1RVNNaHk2OHhCblZhNTVzQ1pkK2w1QW56VDhldFlqSXdGL0JHd0V4MWpqekZ2MzJiazZFZUp1bEVTQVJoOFJaNDhvN3JLdzY3VVpwdWRQYTE1U0RuTDhBTDh4TVYyU0MwRDFQNTNwMTkwemhDRmtNbUVpaXIyb2x3eGNKcHBsL2tMbTYvMFFTVVFMTmF4aTFBQzNQZzFDVG9zWDJZUXI3M1BqRUl4SWxnNG1KNjJ2UDdaeW9IRTU1QjBTWDlZcnJyQ1B0TnNySkV3dG42S09TdDduTFQzbjNETEpUUGJMdWxjcVExa0VUUDZIdXRzMjlvUCtKTEVxUkdXZ25ycU1EK21oQ2wxWENaaWZqZ1EzOUFldWRFOHB5dTJEcW5ZVTNQeVBiSmhTdHExSGJQK1Z4Z3NlV0wraFErNHcxb2tBRGxBOVdxb2FSdW9TN0lZNzdDbTQwY0ppRTZGTG9tVU1sdFQreE8zVXBjdjVkelNoOUY1N2hvZFNCbk1IdWtjSDFrZDl0cWxwcHJCUS9JajlFK3dNUVhyWkc1UGx6d1lKNmptUmRuUXRSajY0d0MvN3ZzRGFhTUZ0ZUJPVURSNGViUnJOWkpIaHdsTkVLOUJ6M2s3anFPVjVLSnBMNzRwMnNRbmQ3dkxFMzc0SnorRzdIM1JVYlgxN1NvYllPZTl3S2tML0phL3plaUtFeE9CbVBvMFgyOWJVUlFNeEprTjRkZGJySG5Pa242K00xelRaSG8wZWZzQjIzV1NTc0J5Zm15ZTJadVRFWjEySjNZOGZmVDZGY3Y4WFZmQS9rK3AreEpHcmVLSEpSVlVJQnFmRUlsUnQ5ODcvUVhrc3NYdXZMa0VDU3BWRUJzK2dFMW1lQjZYbjFSV0lTRzZzVjMrS09WamlFOXdHZFJIUzhybVRFUlJuazBtRE5VLytrT1FZTi82amRlcTBJSGVoOWM2eGxTTklDbzlPY1gxTW1BaUV1dkdheTQzeENaZ3hIZVpxRDdldFpNaWdvSkk1VjJxN3hEY1hjUG9ydDdBRWpMd1dsRWY0b3V6eTJpUGEzbHhwY0pXZEljSGpoTFpmMXpnL0t2My95TjF2b09tQ0xySTFGZTBNdUZiQjBURlNVdCt0NFdxZTJNajFvMktTMFRGUVBHUmxGbTI2SXZWUDlPWEtJUWtqZnVlUnRNUG9xTGZWZ0RocGxLdldXSkE2NzMrNTJGZ0VFZ20rSHdFZ3pPamFUdUJ6NjM5WHRDVHdhUUwvRHJDZVJkWHVuMFZVM0hEbU5tVGtjNllyTlI2dFRWV25icUh3eWtTQnN3Y2hGTG52b3VSMEtSaERoWmlUWVlZTldkdlh6WSs2MUp6NUlCY1RKYXZHWHI5QmNIZGsvM3RxYUxid0NiZnB3anhDRlNVczF4ZkZjUnpSZk1BbCtRWXVDcHNZR3o5SDAxcG9jMUx5emhYd21PRG1VU2cveEZxL1Jvc2dZaWt6NE9tL25pOVFDY3IyOFpQSVNhS3JZN08rQ3NwTS9zK3NIdG5BOW85V2dGV2hjQlgyTEROMi9BTDV1QjZVeEwvUmFCcDdFSStKSEd6Nk1lTGZ2U05KbkJnSTlUSEZkVXdtZzFBWGI5cHZkN2NjTHFSZG1jSExSVDFJMlZ1RUFnaEJkdUJtN3BITnJaSWpiMlVWcmlqcFpQbEdMNjhocitTRGxDMzFtZGlzMEJqUDRhWkZFT2N3K3VCMTd5NXU3V09uaG82MFZjeTdnUnI3Qlo5ejV6WTF1SXdvK3RXMVlLcHVRcGRSMFZpN0F4S21hSWE0alhUalVoN01SbE5NMFcvVXQvQ1NEN2F0RmQ0c29Nc1g3UWJjclVaWmFXdU4wS09WQ0w5RTA5VWNKbFgrZXNXSzU2bXJlL3M2VU85a3Mwb3dRK2ZvYVZvcGt1S0crSFpZYkUxTDFlMFZ3WTJKNTNhQ3B3Qzc3SHF0cHlOdG9JbEJWek9QdEZ2ekJwRFY5VGppUDNDY1RUR3FMS2grbTd1ckh2dEhTQi8rY0d1Ums0U3NUbWE5c1BDVkoxOVVQdmFBdjVXQjh1NTdsTmVVZXd3S3BYbW1LbTVYWlY5MStGcUNDVDZuVnJyck9nWGZZbUdGbFZqcXNTbjMveXVma0dJZHRtZEQweVZCY1lGUjNoRHg0M2UzRTRpdWlFdFAzTWU5Z2NzQnF2ZVFkS29qS1IvL3FEMm5FRFkwSWt0TWdGdkgrU3FWV2k5bUFvcnltOTJORUdiWThNZURqcDU1M01pVFhDUlNBU1B0K0dhNXE3cEI5dndGUUNUcGFvZXZ4MHlFZnJxOXJNczNlVTZ3Y2xCTUo5VmU4bTZRdUxZWjU4SjQxWUczalcva2hXOTJoNk0vdmJGSVVQdW9wWjZWVnRwY2llc1U3NEVmN2ljOGlTeW1Eb2hHZVVuNHViVDB2UnNYbWJzamFKYVloTDhmKzhJNUVpRDVsNjgwTUpieFgvNEdZck9nNGlQUXFwS3AwcWRkU3UvSEt0em5IZVZ5eGdUd2hmRU9STUN3bmFxZXRWU3p2aWRhV045UCtmWHRHWGZFUDljVGR3eDJnS1ZmRGRJQ3E3aGVjZ1JoSXMwcWxDdDYrNXBHbENjNmtXb3BsSGEvS2pQK0ZKZFhCVS9JRG9LTXhSakZoU1lrZ2dJa2h2UktpTi9iMnVkOFVSUEYrbEI4N0FHQXd5TWpyL1dqdTJVajVJcnBwWFpXakkzZDE0QmRLRTJmaEFMeVFQbUhxcUErQVhkMkx3dlJIY0JxNG1oT1E0b05SV0g3d3B6YzZQZ2dmY2J2OWtxaEx4ckpLRWFKcUE2UnhpK1RETk9Kc3RkNURvUlZDRGptVnNwQ1Z5SEpzRkVXUGc5K05BOGwxZTRYMlBEdk9kNU1QWkFHdzZMUmhXcWVab1NRY1BmOS9kR0pZQXl6Q210dGxSbngwQmZyS1EvRzlpNURWSmZ0OWZ1SndNaTNPRC8wRHYxYlJveGNYQXlaMHdNSjZyd2s5UmpSVEY0Wks4SnZpQ0NOdVZ0L0JxUVlpcGhPeldDcG5id09adDZxWHVpQWFiUVdyUzRtTlhRN2NFRXJYUi95SmNiZEZwNW5XRTFiUEJqRDBmbUczb3ZNeG1PcTVibHBjT3MwRHROUXBjaTF0KzlES0VSV0FPNTNJVlYvUzR5aE1rbHZJcDBqMEZJUWd3amRVcHRxbW9NWUdWV1NJNVlrVEtMSFpkWFJEdjl6cytIZEZadDFRVmNkbEdPZ0FUcm8zZmc2dGljQ3JEUUtVSkM3YllYNTB3ZHZldGlsRXdWZW5IaGxyODVITUxSTFRENm5EWFdJZDRPUkx3d2U1SVhpT2hwdVpUVlR2K3hka1R4Sm9mcWVDUk0vamNacVFsVTBnRlZUbFlsZndNaTZIS1IyWUc0ZlE4VE90Z1IreVYrQk1aYjZMNU93RGMvMjgveGRmRDdHWEZhVkEyWlNPYmlJeEJ3VDJaZXY2MzdFdXZwTTZyeGNvZ2RNNEZKRmEwWmhGN25ycXROc3FXZzVNN2haTU9ScGpkNHN6Zi93UytBaHMxc2hZNTRDdDVKMWRPQk80c2RFdFNuUmMwUDlQaGd5T0N0NmFRVzk4UjIyRHBBY05URGU3MkFISzQwdnV0S1RQZnBva2doUlB1R3Z6MGR1bEJQS2ZDM080S1ZEQ3lXckpHTzdJa2R1MDZBMGtlS2xWZmkwdEdjcE8wTmh6WEVoNzVOSHlNeXNBTVYxOWZxNy8vc1BDMEZvcjFrMnVGRXZxOGx3ck1BZm1QN2FmUjY5VTJScWFJTEhlN2dscGM4SG1WZjg3UWIyb2hzdytEaTlVK2VQZEhMZWNTNjZNaEIvME93ZGNYUjVXQmNXVFpMR3Eva2lBYVQrYnpralI4R0lwV2R2NnBmSWdRK1EweGRpS3ZvK2dOQjcvTmY5a25OSkd4bmg3TGVaRUZ0TW41MTd0TmM3NFBQUzBNNEszSTZISFpxTlBBK1ZaY0JjL2c1YTJBUnlxS3JKNFoza3JzdUErVk9KSnoyS0pwQk1nQ0NXRmxuM3U3azYvcTNERVRBdWJLRy9wdDNPYmFOVDBOSTBRdWc5MEwyaXA1ZEhuWkpValBUdks1RTk2YVgvNG1SVTJ1OG44a2g2TUtiWTdBTkJybzNodUYwNlUrSnZmeUVMUVAyNW9JYWorbjBJVFE0S1Q5clhaRDRFdEJJT2o5NWZZTmxkRE4zaW8vVk1JdldOajlQL2I5NVdFTXE4VUFWZkcyWEcwTjZmU1lkbkJFQzdzVUViYXRiRElDSDlxQThUVHVXOWtFdDlEbEZPWkZQN2JkZllMYS9raFNZOFc1Sy9Ba0lJQVBYdE12eVZLeUVTakt4OW5mcmFnc3N4QzBqRk1WWTk0ZDhsT0F3Um9jZFMvbC9QNDNjQkdhM0lxRGEwaWhHUGNtd1M4TzhWajE2VXk1NXJPcm5OMHNoaFJKWmRXOEk3RjBRMEtlSGMzNUdGbzRhSk9GYzI1Z05hZkJ1MVYvVk8wcVM0UWtiNndqUnJubGVwVVdqdFl5YURBQlpjZVZhbHVPTXRvRGRlSUlUV0tPSml3R1BwQjEybFFnd2ttWGg5TTg2cG9kYjBEMTE3bU5ROEVsbHVGdmJhUzhSVEtRNmx5ajg4ZFV3b0pVL29mT2V1YmhvWFdCRjhlTnVta1ZKdStBczNFRC9BdkxsclY5MVVvd0lXSTJtOEhCRythM2syNDdaS0FHWXNPY1dlN2ZUV3FMOGVxd001WkZ1b1hiZXVnUEt1TU9BdE9zTis0ZFN3a2hyU0FsZkdOVHpGd0VtQ05XdHpwYTlDZ1BiWU5jbW9IdE84cGo4cU12bEdFVDZucmtKb1EybHA1TUVVVjFFMkE0Wkg3MEpVbENMWHZxVElwWmx6eXhkcjVwL0daaUQxL0J1Rk9HYnlmRnpodXhhQy9sM2xDMmpqdDZHTlJCYTA2QXFxUGxZdGRBN2tpaWRZYTVRaTAvWHBYaU1EeU1YTk9qM2ttSkVhWHVmVzBHTzgrREY4T29NVUxYMXZ2akNlUEtOaXM0QW14UUtMQ0YrY2pmL3d5aWxDSnZ1aXlMVlBTZHN1UlRQWjBBaHBkREYvMXVGbUR3RzdpUDNxWXdOc0t6cWQzc1lkbk1vbENPdVFPSUhXeTFlUXBXaHVWK2ptU2VBQzV6Q2MwL0tzT0lYa1pQZGl3OHZ0QjMzakVCcGV6cEdEQlA0SkxZMndIMUo3RnpwOHk4UklDcVZkMjVtRFQydERiL0wxbWg0ZnY5VE9mREg1ZFRlQVRxdStkaU9aaSsvc0l0MThoaVRvdlBzVlFWYXFYTFBSeC80Ui91SC84NnRCTWNGK1dCa1RoS0xmYmxjVkNJRUNjOERnTlJWWDk3S2Ryc0NlSUsrQ3ZKWk1md3JmdGNEWkRaeXA3RzhIZUtsN2JQWW5US1g4OGRYQXdBeXo2Nk8yY2hrUERIeS8ySzJYY1QvNjFYbmxBS2dQd3RJOHlQOVZ1NDV5aDU1S0hoSnU5M21MNG5mbzhzenAvSXlEam1GSHRTTXFxb1dzajhXYVZoYmpYZ3paeGNxWmN5T2U3cFVLNmFYRi9ZMzJMbkJPdDBXTjI4VW1IUmlPcEw1MjVDNjNJMkpRUFg4dnZPVTBmejJpajc0T2VKMUFwZ3UzSlJPYmZkbzl4R0RwcDdjdjNUZFVMRWZOUzZHdTNFSnU3ZHJCc0Jzb2dVcVVjNndBVVczdXgwLzFoTFZJL0pFS0pyQUdtOGc3MkMyYUpTc0dBc0tGVzRDQnZCWFZsTklLYTVyN0h2VDFCZUdZQmZ4VFIxdmhObEZGTk44V1FZd3IzOXlULzEzWHpSR2lGMklzZkU4SGNOMCtsTjF6Ti9Pbnpla1ZCS2tGWTExR2dySzVDTHhyRS8ySENFTXdRYjl5T3VQMnJUWGlaelRFRVRwL2lzbUZHY1RXbWJNOUcxU24yRC94M0c3NHVXWVpZNHJnS0IyWm8yYlRLUzZRbk01eDFZZWU2NlkxTDdLNDRBeWlZNUsyTUg1d3JUd3hNRmgrUzhMek5RMjV6NnN1bldaeWlSd0ZJSXZTbmlvbHRVWE5pT3IrWE1aNk85aDlIY0h4WkprZkYwdFVtNlFrVTdpSjJvelhBUml0aUw4NmFxVnNNT3BtdmRJQlJPaFVvYW5QdENqZ2Z0OHVwM2hBYUtwdzlRczlNell0QkEyaWpIWG90emFya1YzektFSzBkRkZRVXdUNzROZ0NtR0d1U0NFRG1GQ2V6WFBDOUJoeUdobXpOYTZyUWVRUXorcjlDbUdVWmpJUUVQc0h3ZTg2b0NPUWhXYUhFUnN2NWlhOXJadkovLzdVWE83QjMyOVlVa0xMQWlxcExSc1ZWNVhwY2ZkYXdsSnFpL0JWY0NxTzZkcjlZSlRGRlJNVkdoZlViQjlZV052WVBZNlJ5YXlkQUZZcTFZSUJReHVOQUdmWVdMTUFIdHQyWFJIb09LQ0x6K3FmNUhDVkJET1BPa3RRM1NkSkJmeFVrYWlENTg1Ym1Uek13VTNvZVhVSFo1NUVDOTlLejlrazRaWE1JRU53VlZwcVcySm1HSWNVaXV0SU1qMktrcGpFMlFEK2RJWlVDeGNYNTdrSDdoaXVVUG5LQ1RkYXc0S045NVhQZUZSdk1jdm81TDhMZXhXcXZhSlBFQ3p3WENzLzRYUEFsU01wV1V6QkJqSzNwRW5rYnVlTWtNSlFyWWNuWGY3UGpiQW9KcmExVkxYNFl1c2NRTHBhZVlXYlQraDI0aENGcmZjSGp4eHg2V1RTZTRBR1kvS0hSWkNRS3FUdUZXdDBEOFJtR1dtdlhTZGcxcHRJZWZZUHNodUlWWlQ3Q1Y0Tnk2N2Z2akp1Z3kwVE5ZSHFvQ080NUNCODhreHJ2SXNpaDE5RHFqRDBVcWlKc1RGUGNHVzNQL1VMT0czbmI4Q2pwZ1ZUSW9hNW5POVpZRVg0dUVIdThoTFhySlBqVjFsVFE1eFRkWlZhZ2crV2o4VjBFRTR5UHNUYzM0NUtNNmxWWHFMaUh0bStHNmVkQzRHVkVpUGdkOThnK3R3U1ltMThnQ3NQbmpxbExjRm05ZTcyQ0xKYllEK29jSVpPeHVWanJYNklLaDlmaDdXcWRJWjY2eDlQV2tER09WVkdreDdqTTc2WXdlMTZEWDluZzIwNWtnNWVxK1IycTJNZ3VUSnhZdi93V0hsaUQ5bU9ZcHpaS05YWUMzV3I0aUJHa201NGhCd2tQekZoaVgvVkJIZFZIL0tKMVpJTU9IeElONmFyS2R4cm02RUJzZ3dEdDBtUGUwTVgxSFJVTXE4Y3RjbXlzVTZ4WDBiek0xSjA3a0F2cTMzancxcTBQcTJjeU1XbWU4RjdhVmtmaHpaRUZkeWk4ZlZCUWF2MFlacXZBalo4M1dLSDcyNnJCeDVCbjdHSEZ0aFI2SDRsRnNsdHUraldtc0FpYkoza3BXTUcvUWJuY1U3bjlza0lCTDBNdVhYdGo5c0pnKzREbDBYaEtKMUxjck15ZGFJZ3lyZ1pnU2NQNGs4WVF2Y3NCbUQyNlgxaVlYS0x6TVlmWm4ySWZSanpuc3JKMWU1Y25sLzNhNXhpTm9JNm4xeDFVMzZGV2NrSmJ5eCtoaVNaZzBRcUFxZWVTdnpGWU1sWjJSRW5PL2E2eW9RaHU3UGRITVlFUEZJdmZ5R2V5Q1U4ZTdycGp1NERybE9oc3pqOXJPSXBOc3ZDa3VEK1RMeWY1SjdEL3dzUGtCcHNjRlZJMXE3b1VTVTliTjMwdkg1QXFuTzdic2YrOXJHaHRWak9KUTMySDloSFNBelIyYXBlNEwwQ3o0V3hheVNtNGp2dUdYd2tGcDVOTU1McmdaOExkQSs1dUx1eXhPNVNNT21KTkRCY2JiTGVmdjd6Nkx5eEJ3bHRuZlFMZDdxcXBHMU1tTmNvTFVjeDczQmtORi94cGRTMGNLZDZHNjQ2bnRDaFhTZVRaSkpURllHdzM5VDdmcVhEUEtvRzJjRjcvWmNUdk1FNDJnWExWalRxekFFUjFSdDVtN0dZc2gwWDArWGdPZVc5TUpxRTVqL3JwR3pZNnZVdTZBQ2NDVHpETWRaSGlXRUxwRG52Z0UxaG16dExjU1l6ME10TnlVQkxxdnlsVUpKbkp1NzlTa3U5Tk1IQ1RrZ3FvelRuaE1GZmR1VjJOTENTWXZBSTVIVXZRcDFoL00wMnZLRkQ2ZW9zSWtHVGc2bXVqVW8xVzhoeTVLbmYvZXJrQlFDOUx6TnFQQVlDZ1IraGN6Z2V2dGE4OE5OcVNsQlpyeXE5UU5lVUs3UnBidkhqb05oVUtBQWVOWUg1NUxlVFczNkt5RmFYZEFrQnZ5TlA5eG1SdUJva1BpMk9ocURieTZJWjYxbXdmekcrR21BQ2tTK0c4MEE0V0dPTjVpemdKV2VlREs5MWp6dXNmT2kwUm1Fc1ZKWHdiVlVyOHUvSjJMQ1FhTW5IaGkrd0pURVBOOXRTMmI2VzRHUkdDTm10akFNZ1BzUDM1N25PZUQzSDJ0Y0RBUHU1eFFCS01IZi9qNFpoWGxrdnZ5M1ltQkpzanNkNHBTT2xmUFpDbnc1SnZ6eEVYTTVKSWMrRTJtVTRDZ0IwbWRKbkg0TkVzQ0hZTmVWUkRYRk51eVpVRTRudXZhSmYxaCsxMUFXTGRBWjcyRDlYTlJjeGZiMitYSFpOL1NONDhVN3lsK3NOWmhnNWduL1BEOHdrQnRuUmoxekJVUElXbm9NUDZ5R1VFRXp1VCtWYVgzeDJqRUlaQVpzcjNyczl3Q2ZZMVNzMEVkSUZGekJieXJ1VXVwNEVQYW5iU1lldzV0ZjE2L1pXVnVwNWl5a3R0dXFMNHhvQy9qZFpXc0FaZVNmRFNkM2ZQOWtieUFGWVhrZjBRMmxteGFUa0tSWnJDbzlYQ29pVUc0eVAxVVJKNUc3K0hTT2hoSnAwQW56ME4wN1FadHlGVXllNnJjZ2lPRmJ0eW9PMWxrdVYwaVE2MDJNVHlGSzl4THFOSHROeTRjSmFUTzZoanRpd055blZjMzRaQTZIN2s4YWk2UzZlRjZqSUcweEp4K0pmUDk3bHp1Q1pyOHZVNVNJekltYU5waVFoeXZEYnoyMy8vUEpjT2s3aEQ0aUl2SnpmSWdPR0lSNlpQRUpwV0haUW9hY2JGK29tZUh3OGFXSGFOT2ZhSXlHZUc0bEVyeU1maHRObVdoNFJBSXBuOGRMczdaRTJlVFZEd0srK3hEb1NVZ2g0N1dEbUtsWi9rNk9vc0VVb1FqazdRK0twN094d2dNRlNoQXY2ejRwVFc4bG9WajIrcVhMUTBUM2htSXVlOHFIeTFvL0hYam0wODltNzF0Nm1yclV5RGZ0cU1ZdG1mdlFYS0RsWitLMUhSL0ZrcVBTcWNqR2xjUFBJd2JNdzN3SUZLQmRWTUo0cEZMdCtvT0lrV1pNdzhwa29ZWjNieXc0TG1BRis3QmRpY0dYRmNiNVBXdER3NVhOTlZjNmVCOWR2MHJBRXBncjVKK2JMcjAxMGJwZkd3K0lrUm94RGJrREZtUWRFUVVTRWxQNWJWaUxvMXVyLzIzS04wakV3bCtyR0M2QVVNS3hIY3YrVDlGMUt0cG44alNTckt4Sm5Wa0s4VUQvdEg1RE42blhCOG1qVWRGVTUzOWU5eXdMdExZQ3dtSFlWRVZxbkZtZHViZHVhU2QxaXZJbzRwVHNYK21KY09Ba3JSMUQ2MFJJb29jQ0JJZHdKaENCTTFyT0UyWFNsUG8wVStraEFMdncremZ4WXp3emQ0cm9XbExKa1poZUZSUjhRQjh2NFVTd21BY0Rzd1VaMlAvN3Y3WGE1MUZzN29yWWViWXl3dzRZVzU4NjlZL2M2S3EyZVRSOUhMU2pZdUNoVGtYYUR5Z29vOG56L3lKMEt6Zlg4b293YU5Bd3o4SHZRZGxMVTlWOWhqcVlNVVJ5WXZQelo2MEcwaXRtVWRad0Irc1k2clVrTUFaWnRXU3RiREZtbmsvZFFvcmh3cjMxMjFYUVdmZnJLM2FzMGcyOUFTd3hic1ozZFpBcS85NmI3L1hXY2tiam1vOCtqd2RFNjgwRHpvRVVVaXZuQmdvd011QlF4SFhvR3lwK3cvY1NHWTg4cld0bXdveU5OSXZDaHMvUXNaUm5iZFY3eTh4N3QyUmtsaUpWL2o4ZTZxZmN0clRzTVYyMnpvcWdRdVRTTkZoN1U3cC9RNDlMMGt5Z1hObkVZWENCRGdpNUJlTld4dTdWalVMY1VISStsR2orT1RDRUFUeldyRG1heW5xM3dUOUlBZWp0dmgzZXNDdTZzRXU5Sk9zWHhNRHBxeG00VHpsK3B0MldhNUJxM1RNNVRLSDRON0tMaXI4RkdJUEE1NjkrdUoxVkVMM2ZXOEp5aWd6L25FVWpBVllyZENXcTJNblM0aFFWZ2N2WHE5YUY3WGtlL2srK3JBdElRcWNrUE53aktyVjJ0N0hDT3JBMXBzODhZNVJ3MVpwKzlpdG5CNzFqOHROaVFjN21WMWtVQ1FYa29pNWZPc3ExdUM2aFVQVUw3WjY5TkFNNmxnMGMvYWVpaWZIb2kzNXYrcFZCaDdDRE0xWGZ2WXBpSzVKSWJJUUZIYWZtbmhIZlJUbk1hZ0tjamRFN3p6Z3R4a1RQS1ZyT2JUeVNUVDUxZzliQjVyby9kem4vc0IyNGZOTTJMR0p1UlFzbUM0OVBMaTFqVFJmWmFMcG84VHh4eGN6aWo1UGwydnVyK1Mxd1FXM1c1cXlWY0lVeVNaSHRGRFFIditFWURvWkcxVDFKN0Q5MXZFSVY4ZEh6VUJ6VzFVeXV4UmJQK00vQ00vdnNhczZSem1TNXRyYVhuUTBKenY5aFlYeEtIY3MxNVRRQ1A3NDRYc0xqekZqSUxZVVJYRm5oTStublYwaU82bndsczlUUjR0bHoxSjkvTnZFOEZHZzVtZ3BaQTRodFMwNUFLME5uVTJneHVxZjJ2akN5V2xtM3lwS3ZhWDR2eGg4VW0xTUhHQjJOVGVBRmhiRHlHbSs1dzJ6cUpBV3hWbGo2ZFZlUGI1eVIrYU1odXowNVl1YkNRSjBCT3RvWVE2UG9Eb1c1ZkN3Q3RYajVTSHZDZ0wvM0I1ejJtY1hXYVJUZjgvR3NGQWZYL250ZFdaV0ZjMnhnOE1KZWVud1o0ZFpVVG9jZTQzSWY0elZiMWV4M0JNQVdHaGdrUHdSNUVna3RaaFczWWkrbnNuWlRVcjlGWUkxNjBZaEFyYUIwek1WK291SHo2aFltMjUvRVRETTBNVG1jeXBvR2daSVNTa2Z3WUFRYUhHWTQ1eVo5MUs0QTRNbTRmbmJNazhHVGM0b3J5cFQzTkxCcUF4WWRjWS9xQ0g4MlBwSWttVk9FSGkxTm9ZYVV5bXVJbUxMY2liNXBtZDJNSFRCM0pSKzRyTGRSYzNndFE5emVGZGNpY2lSaVd2aXUzSGtxYUxTeEplSTJyZ2M3T0tRc2xJdHVtQUNRb3c4OWVsWG1pNFAzZ1RaZUNhdXZNSDVuRjRWckJjTGpqd0dEK0tsS3FlL1JXSUVnVDJ3R3FBZ1N1TDZiK1JUVFBuUVp6eFo1eTVIUUprRUVLSnA1TmZvQjhoSkJNOHFuNnhiT0Z0eXpCalZCcndTUzF6Q0pSM2xFYzlPRFE1V3UveGN0OS8yUTZxTEhubU54Nlh3WnVzL2k4ckVkNlVzVnhHdG9Ecm0rQnIwTDVvVW9qbHdkY3F5VlY0UElNc1I2MEpoWndKdGdYN2l6UVdqK0dPZUY5REE4V2V4ZG12NkRXamdSOExFQnA5WXVQQU04dEpEdTN1Q3VtTnFIbkYyQVRZWC90dVZPNTVPZ1F1aVVobURtSmJGOWpKeWlmQlJ0eE9WSTlEQ05MVVk3MUlYWllUdWlZY25JTFEvWEh1Vko4YUhEU3RMME4rM2VZTnZYd0hpMnZFaVRQbkJxenNDNFRzUG5GVm5ZWTA0Mmo1aTdDMTFBVmRCWjFwR1NhNTJqTTlkSUwxMTlycnkwbWdHeEZ6STh4UHMrN2JtTWZZS2gzN0E0SHRBMDgxb2xHMW05UzRaY2gyaG9OQ0dWdlZoZDZVTDdDMmQ1aEtJQkhvQitVeGFycS80YVFYaGg3SVdqU2orY2E3VmhxYjQrWndZM25IWGgyUzlKSDRYWnhRb2piZS9lSU54WWxvelRZdFQycnBVL3hiaitXMmhYakZRK3orZFE4d2g5NzUxTVAwVXBqdXRRZHh6My9GSllBRUc1QkY0MDBKWFdDQnM3S3JDUmYvbCtGK2Q5RXV3Vms2dGhPUERCK0hOUzlpV2xMbURnWHZZNkswdmdpeW9lQTNBbitqV3VmZEFHMXN1VU1CdUpUKy93MEZOSlpiT2JVVDhjNXE1V3RReEFTUUY2RSsvdThVd1ZCczFlbzhqVGFtQ3JjZGhaSmxBREpicW4zY3JjREhRbEJRTkdxN2J0Y0dLaUpYVzZxMGNuM0YweHpmK2sxSkpTMnRlc3RCM3J4MTVaUFREWG04UVY1WEUycXhCT2RNMm42dDVZYnh5Tk9tRWRzSHgraE1wK3k5cFdrY2d3MU5pa2VYdWFmSnZ6Y2phTndFMUFkNmdHNzlTNjhhTzdqV3BLZ0JFVFlMbVY0T05IaEJrN0JlOHRqZjJXVnZXTURRdlFkT25rNDQ4eWVNdjF0UUtVMXhldjBMMTcxZS9xeGtNWmJta2ZLbmQyOVhSQ0syaGdOTkpod3QxcWlZV1pHS3o3RGk2SzNmR0RUN0RPMllRN1dVMzNzdkUvV0tHYldRRXZ6VVYydytWTllEb2NJNHl4UTZpM2k0elUyVGptakN3dTVQaytKYTlIU3dMcEVvVXN3cTN0RkoxamltdGhnTVhkN0tqU2w2UWQwSyt2eFdUOEc0Lyt4SVRIc1dER1NmUVRTZEZRdGg1dVZWZmE4d3JrRFpIVEdWZ3BKeXMyaWsrM0kwZFNmNlRObzZBL3NWcHR5WS9reDFoZEFXS1BJNnQveGo2cytmUE1VM2hnMXZrRUIwUlJIcS90Q3kzS1VVaHpVL2QwSkt4VHlqdlVtczVpeTFHYk9GY28wTkE0dDgzU0s5c0JtdExXbTRrT0xMZmx5eHFnUVlQMDhpeVh3WVh6S25sUTZWVGlwdWFzcFNKOWc1SDVMdTNlTE1uUEtiaGN3dUVnMFZaODBwcEpXalVuaFMzckwzNWVyenlzcCtmSmh4c1VzODZtMjgvVXdXK0lnclM1WTB6V2F4bEZKOHhNTDV3azhzZzFyYWdGK2VOYWp5STBZNG13U3R4dDFSWkgyQmphQWh2dStTbk5OSUs4OHRoRWdaRXNvSHYraWkrT01tWEpMN2RuQWlJTlZEejN0Q25xRGdwUVg5T2d1TkdnWmozYXhjanExVWd4RHc3ODV5TklwcU5pTGd2NTczOTlqVm1KMC9SU3ROc3dhRklzNkZ0bmtpbEZabGR4ajZtNTYyakw0cDVnM1k5WENpWFJKWDZucTJQR0pGaWZGUjdFeVBHNGpETW5CTTR0K084WnBFcDN0aDdUQ3hFdytaRzRhZkhsNHNORmFxeHlMaDYrOTc5dHQwQXE5QnJxSStDUzJVN0hKb0tpR215VlUxbEZhMy8wTzVtTkMxYnpSZ05NeStHWHlpZkx3SlA3RndVU1VteG1WUnBuK2duWFdvSXVzd1B1dHNpY2l1cnZONmxzTUc3eXFFYzJZNVpJM2pyUGdQcTB4RUtQWnBGN3RlSmEwVFFuOEJRTDRUaCtoanYyQnlmd0tvb2t5WEVtajBkMUtNY3NtZkthZUtLM2NaWnViaVlxbVNDcm5HcFlUd2dQazVpdEt1Y1Z0alZpdXN3UXNEUjZUdXlHU0lIWXZsejd3a0xnMVJyMEs5a1YxbzhSZ0FCbGhiTHJONzRjVldKVzZUbmZYTjBxMTJKRk1wVWJFYTh0MStqNDQwRkErMTdvOHFhOFBROWlna2N0VlJPVklmQjNqVTV2dEdtNXBZWUhZU0R2VTJURWMxNXBJejE5a2ExcTZjLzdXWGZGOCtQT2tBcGRPdzdubjdLcXo2VjR0cnU3TlhnbkEvdTBnNitmUFJUM2hwL1FyRFF3TXNqd05DWnhkV3JSNnBnQ0JESk5jNy9LQWx3QzBVWjR5V1FzMEtzdXdiYk9nY1R4UVBLNTR3aVhyN3MrMjIxaHpaOFJWeGZvUlVLTTNlNGxweEhDODNKbGx4bHJWNzYwdGwwNmY3LzY1cWhFMWpoTWZpdkFVWElYZlJNZTN1WS9HMlRwV1l6RHJ3NUNtNWNTMDYyQng5bGhIcTlndEpwOHhad0F0U2RTdVcvS2Q3K29yRUFpc3dBNzZOOGV6bVZHWWdOYVlsUS94azkzMExBV0F0S1ZCQzRVNlIwOEw0NUlvaEIxa0ZpYTdYSnMwVGNhVDJ6QlpvTEZ1T0d1NGlKYW9BbmZqTDN1UzZnblJIN0c3QSthVDZFVGxta1lVZmdyQnVhU0xMREpmaFBKZTAxUGZOMG9xQlRlUVVSYXNsM044QlppUVNnZHIwYUR2M2hQVGlvZzROU3lmQVV5eTk4V1A3ZG5URFdRVFkrUXd6Z2sxdXh3UnFIbDVNcEMvODRDdXcxVFhmUmxnSnJ3UG9wMTBrQ0hqbWZmbkZkeENlMkozUjNKNWorM0gvc1puM0lVdTNTdXkrSStkQU9NV3Z6d0V4TlIzUlJQVmVsWkFodGFyS2xYUFdOalBSSVZQNEpzQUZTUlhzM28vZlNZQVBhVi96UDhxNkRsdEg0Ny9yWWhDTGR5L0xycE9zYmFMZjA5ZUFDY0NsSm9zTmVmZXRORWxrU0ZTdUNnZVk3b1RBQWwrOFkyek9YSmIvYmdFRHBvRFhmUXFjNmxubEJyL1dzbVZ6bmtCUzFNN3VmaXFweHZLWGp3dlI0V3hMYmg1TmJNTnk4THNuWDRVaXVBaThYb25iU1VjVlpLUU9XQllVZWNTT01qNmpNRzhnSHU3V05yZUJIWTkwbFY3Rm9jRHByU3JTYmV4a0F0TVc5S2xYY25yT3lMblpkb2RHWWR4ejhhdzcxSHp0SXFMaFJkQ09CNk55ekhQb1MyaER5NndMazBJNUpyMnQrVTBBK0E3RXNnU24vSWgwM0E1Q3NwSG5WRjRNT2ljK0xjazNtNjFVbStHSERFZTREckhCaG1ndERsUlFsMVhKL1YvVnVtQ0h0VUREY1pDa2dqVk1CT21WT0dZVzBSY2RpMWFoZGpoQmNGbGZqQSs1Y1JqQm9wMWFORHZkcmY3Q3hrTFZneGlDeGhSY3RXOHdjek04K2tWbUlyR3RrYUhHbHI4eTJEMDk4SFhFMjNyN2ZuSkZVVTY4enlleU0yNjVpZ05PR1B6RkcwZElnVURXTjZTM1pjZk1FUkpkV1Z2cEdoVkVIWE5MZVdxSGlUY0Yzd090MEZiSlk0WEhFcG1rb0c5TVFQSko0dWVRMDErTUIrU1IwckNTR3psRTh6b2QxOXE3NUxsTFdnem9ncG5Kb0Q0Z1B4VVljWCtHcGM1THk0bmsrWm04TERYY05SN1NOVnhMaDZOQWN4OGVramIvQUM3QURsUm5mdUhhSEphQm9kWnI3UkJYOUZMVHZvY1k2a1k4YmF2ZEFrUWljRTliYndHTGtadTZ3aFRDSjU2bE92TTM5aWplaHBUT0ZxUjNWNTNuUXg0aGZPdndSUFUyeTJ3N1VVOHlpUmJjeWFYNmpHSjlDUnZsOXliVjF0ZWJUcDVNTXVNbndMY3gvbHZlbjB3OVQwYXRKdWlVRTJXdFlHaVZNYVAzRWNoQUJsNUFzeWFDcHUvQktBV0RGdlUydmFDTDIvZkpCS0NLTGp4RzZ4elQ0TWg0d0hoSDMvRXFzR1NvUUFIdTJ3YkhtWEhqMkx2b1cxOUdYRGEyb3llS1J3R0cxUFUrUzdtRS9TK1VtakhpREYxb3FKMFI1UXNkakFaWU4xTXpwTlg1WURxV1lmaGZkakFYeUZRYVZ5R0trcDFvRUdUUjhNSzZqYUdmUkRGZDQxdTJFeDhhYzhqS1BZdTNwWHNrOGd1K205dHIxUlZ6VFR1RHNBQ1c0UzFoMzJ5RkhYN3FwWFNtQTBRVkVjUjhXOWoySnV1MHBjWXFUbWRpczg4VmdUM2dxN2lZdWU1SHgvM0s2aEZRYTlyWnJOU0RjamFTUWxObjRMU3FzMjBieXBuS3FwenZubnhqTWR6NVN0Ynp2b0FKS2dWWmE0RExDVm9KVzc2NS9LeVRGNHM0WXp0bUFUMWMwcFRtS0pIVHBhMTA2RmVnRG84cDJ6RDZ1T253cFlpMHZKbFJNRGU5d1BUNjk2NFVmQWY2bHEzcVd5cFVPeDlxNkJiS0VZdDdLM2dXTVhETk42d0FtMWZOblNPblo0SmtiUHE3akxRcmwwd0wxVjdRd08vc1huZUtHZlRnVUwyOEk1aVBWRzlkQTJnUzdLaTAwNUpVUjdWbXc0Z1g0VEp2eTFXUzc0Y0lYRDA4TENGNW9icWNad2FtdW9aK0ZQTUpFY2swVExIanlIMWJhUHI1NS9DeTBwdERmUko3ZDg5cGJQNDh0TE1IRzVkTzExWjh4U1NwUEdRU2dYRFdtcHNOc21tK012eEpqTUNpN09GREh4eHBtVHRqZ25PQ3ErYzdGaTFEeWJmaEFudHZpS2NjeitzaitPUEtQWU9LZVlZUEx2cTZNcFV4L2NoU3ZCY2NnOWRmYmVxZXRRTkNzM2VpQ0ZaVFUxbXJEaWRvL21pYjY0U1RNZ3NhK0lLTGs5UHl4R0diVlNRQjlHc0h0bzZmNXByQUZJYlJEU0l0RGVkejN0NStObjY5RkZTMG5FZm1rRjdoS0JtTlZjZTV4djY1VVNLR0JvSFl4Snl1dFNHblJJcTd2TURzQU12aXJPRUpPek5pNUt0N2Z5cHVTVTJjMk5wbzZVSDVqTU9rZVBIMFR3Z3BhbW1PM0ZiMkZYNmYxMTMwOXovbXFSbVE5NDlISFJqL3dNektOeDk1TTlwd0tmK1VRa01Fd2lzTDNZVm90dkhoQ3Y0eTAwVWkwUWw4ZFI3dEdxRmNTZFl0bW9BT3VBb2RrQk5zNFBaU2pBQUY3Uy9zendMZGRGTWRDeUIvZFdQZ0ZVaVVFK1dtVVVDallyS2ZKTFFmTk5wUTROS2FGNTd3N0twL2lzWlZ3UVBVSnlqSmF2TjNmUU5LVStGNzRqVkJKWVFFY0VkdzBOaWlueWVhMGw5UEoxL0FjVG0vTEk5MVJaakR2TEk4MXBuYXQ3UktVMlA0L1RuSUFhM2hJRWZlZzRpR1Erd1REbFVSSzZZak5wTjVzNVZrUVc5dzdzRFlLVTRYbWp5WnNDUUx4enRxZDRTRFF2THl1UERoVVJBSlhLZlIxYzd0cTNtUnU0dXNGSFBxejdIZ1MwWDdrTnhpV1dSM2ZiM3VWd2JnS3BtZ0xZa3dLclhLdDA5Q093NE1qaHhlWmxEWEt5N25OTEhYQUlLUHRmZXJXUW5aTGJvb25RWEs4MXgrQkIzb1VpZEJlaEsxc3dTWHhWYnNjai9Mc2ZPTnUveFlFWFlQTTNhTXFJWWQrMmhBbkZ2REhiZHJKTGhHRWQzc0c1UHl4cWh6ZWpoUUpvOXdhdUZLM3htUFlxeEI5OUo4ellVOS95enJFWk56emJ2UG9SOXZVbEUzSGE0enNwVkR6SEhmZlBaTUoxVkxaa0txR0NmOFpxdXBxTXQ2VCtOUlBmbVBtMnhlRGd2ek1yUkpFTDQvenpsdTdaMzVzbXZ6YmdlQzI1VlAyQ1VyWmtSeEVpMTVBMDc2OW9qZE8xZDdDOU9HK3N3ajFST01NM05nS2RlQkFEb1JNZUprUlpjWjFGYlF1NkMwQlM5Tk5TYW94dEZ6WVQ0bFg3K1BRN0JLYTg0eXJOK3VqVlZlZitTZ25FaWUxRzBOK2VPdGJaRi9VVSt3a2VlcldqbG9ZcUZpcW8wdkJubXhoK1R3Tk1vOUkvOGxmVTJYVENUMEs0T29XRTA4aXB5TkhqeEh2ZmhZNnFhM3g0SHpkUTgramtpTzUrajkxWWtpaFM1bWVtZnBGUkVIUC8ydmVONVhjUnVlMnpDVnVBdWI4VjZ2RGxPdnlQK1BCbStvd3lSaE1tbmc1d3dHR0lYc09rUWVrWHJYcEUvNmRGamtId3dvRm9qNWJJRmlxcCs0d0hwU1dSYnYyeEdyUnBkMmM4N0Z6TVA2SGZqLzNMV0lCcUZpTk9BeEJ3K0FBUDFYcVVCc3pkWmh6T1NRclFTNEVpbjRmeVY3TWFHc0IwVnNNRjRiUGI0bHgvZm9UR1FSSnY0NUxwb3hEZDg0eENhd0hhWDdqcFhVck9ka0Z4eDJvVXZZMnhxcGdJdmNWdWZ3ZCt6QW5hYVZUbkV5RFhEN1Mvby94cnJrNG1nVGpYaGNqajVSenJicjIzTm11WlF2cGROem55NU1DUjlid3ZJUklxek9aWkxzc3RaU0NEWWE1NkpUdnp4Z0JzMjBkWVR0VFViZTIxdWxqbFdxR2ZTaDJiWUF6T3BmNlVndUszMFp4TlhnTEhzNlk2dXJ0eEZBNWlMWXZsdWU1bURPTlcwTU90UWpocXI4ZlJiQ2tZbmVpRGt2ekhrUVZUNEY5djl2eGgyU0lHUEJIOGJaYjh1Z28vQlNnWG9qZVNkTlhiQkFJRHNCNkRVTlNYbndsdS9iRkxhQ3FTYnZ1NCtZTHBsd08xSmJ0ck1mOVpVZnN4ZXJBWmpCN0UvemwzcXdnSzI3RnN3ZW1VbVNNNGkzN1lBVmhRU29jdVY4QWNESS9DU2VDRE5QYXZFU3NoRFE4QS9sVklyQUpBTWRQL3JIWG91aU5VOFJML1RJdmZRaXVaRWI2ZGtJS01HR09XNWtUOHZPOHBpdlduVDR2N3Ftd3VKbzUyQVMxci9SeVEyZy83YzlaSmdtTUl6ZjBHdkpKUmZNTnUxdXRSTnVMV0hPbTlKSU1jSkszcWlEdFZwR0NEUDQ1VzFvVFRNVW5NQzkxa1loUDBHSGpoQ1c4VjM4eGhqSGdGRkJmdVdNc21TUTlNdk5xS1hpcXRVaERBa0l5MFBXN1lTS2FLVXY2emN0QWlJaytKdDE3a0c2THBOVk9lTXZKbmxWQmFKU2tLZTBIVEpKVU12ZjhSMnpuYTM1L3loMndObFdMeklQM0JKUjVhUk54a1Y5NElDT2x5Y0kxL0pZUlp0enZXTU5vSXBRcmROdnlCdUJ5ZGhTd2hSd1BvMDc5WGsvWFFacGJoek4vS0s0TmJkSlFWMEpJTVArWTVVQklNM1RUWWxGR1lWamN2QTV5Vm96a2ltY285MUZ4L2VvK3lkZ0F4MWdNZXpUaCtiWXhDdFhQWWtNb1BkdGFFbFJ1c3hsbWRTVjl6Z0Y0TnAraXlsdW4zTFZ4Q3ljQUZ4R0NGc21BUmY2eTRJNnpYWTB0eDgxYVF5YWxyMy9paCtaanhHTldkaEl0Z05MZEVaL0JPSUpwUG9BdmVoMmJLYkVGeFUvTTArNHhxRG8zT3g4TW5ObjhMbXYxNU5KaWdTdkpWK3kyVy9ab2dFWE5pdjAvbnVGelpHcjBwS3VqT1NoemNka0VWbE13OG1OWlhaQ2J0TTlWK21mYXd0THhDVHZvK2VuRldoSmNGdjhMVlRGeWNEalBHQlhSUUtOTit6NjhISnRZZHBIKytnNVdkaFFwQ08rREU3UWR1NlRtWmd0ZXRycFUyWmxncHNsT3grNGhiM2FYYXFiZGM5MkxDaDUxZXI4dm0xR1E5dVdEOStmQVBSVjUwaXhoZ2M1emkySnNnMXhRVnh6bGFFTFJXSjViaXlGK2VDd05WMG9GblRiQkhyM0dsbTlxbEdWT3BvT3NRQzhobE5HODhmeGVBZWtrQ0duSEZuNmk1V3p5TzdTaERZYloyS000ZXFuZHl5MDF2KzZURmhta3hnYzBkbmR0N0V6UkNjRWZCeFNhV1p3Y2V2Nk1EWmN1dlNaUTlDTlNkNFR4MjVUWTZVQWJyaGlrdVAxdk5GZlBkWmhDRzFwZTZ2eDRENkV6M3pJYjB6RGE0MkZQcHhXdklwRWVYYjdZVGNmWk9haFNwU1lhV0xIL3ZxMEYzVTFLTzdaeGxpWnBvTUJCWUpzOTFJRTBiT2tyUE5RL1VTWVkwcUtDTzNDVStBRmJPWXh6S1dCa0lnbHJYMzQzNzdCWjE4TUtRQ3YxS1dmSUhFZWd1U3B2ck5INVJRT0Q0TGVpSDJnZHgxTU9BS3BobEw0MUY0UnB4YVU0ZHk4eEVSRmdxb3lJQ1FxOVhtUThXSlNva3dxdmhRTTBmTHRzdnlDTzJQQWtKM0JaZzVJcW9SNXEvR2RUTGdPV1BGUjUzTnF3OU1hNXZCelpjUTQraVpnZXRtS2c1WkluKy83SmJpK1ZsVmlYdUQ5Q2FBVXRkRW1ud1dUUzd3Wld1c2tWdmMvU0RhYUtWK0p6NkhyWlRIbzNVckF1MElaREJrWFdtTCttVFRqZFRiMUErTWRoS2tZL2h2Rk53WGoxRnpVbmdzTjU4dS9rVGRKM1hpMGh5N2VmUjZmYUFPaTRTS0dhaU90eThseERGa2lEOXdxMkdXMUVaRXNvV0d3L1d6eFhoV0R6WVk4Q0M3V3VMRkhjK3gxOWpoSCtGaUxYd0RJQVJSdG5rSlBGMkJVUFo5K2dyWjN0anFBV2hoTjNoNzR3NXBvb1JRVU5BVHkwNUE5SERMbklMR1NDdGZFU29TaWxxdHFBSVEvVFYydDNLaE9jK3RlRGY1dCtEcVpEZEI4T2I5WVh5a2xyU083M3BSMFFBeFB2UWo1N2M2RklSNWRPY2lxZUhaMkxSQUJNUk9vOEprOFY2SkZld0NMOFRDZC9BNU1TYlhMa3kxY1c3bVhvYnFnZUVYZEZEb0V5ZEtvNW9DdXluKzJKWUkvN3BJR0ZBekVybEhaNWhPYWlUMTdIQzN6cDJIcEp3c0lBYjQvb0lvWjh4OGFrNDNZcDgzRXJtcTU1RGc4SHhLR0hYYlhzNDdzaDBQelFFTFRHRnNmNWVPM2xZQXVKak1uZW9ZV2s4Vy8zdFcyV0xudEVLQlpFVzRoT0ZnbzhLNThSajB2azVLTHllenUxZDhTTy9KY3V4cE9KcUZVTTJzeEJtYlEvOXFxd2I5MFIwV3VscFIvSnU4NGJRNS9mVGg3cG8vcGJCYjdBUWFZTmRLM2ZhdEQzSzRUTEhBYWE2Nk1RenAvK1pHeUNqem81T1hSeko4VUh5Zy9ZcE5IdnZsT3B3UUlPamFrcExId0dWNFdzTERQakVJcUcyM2lseTNMTDBkbGtZUXhqM1h4MEFwQ28zNXpZR29HT3RJY2xZUzgzTW5JNVR3VmRRK0hnNDUzV0ZRTjY5NERhcWhHYUwvZG0wS25jWFlxWExpNXBvbGdUNERPcnpENG9TVmhya2g4R1cyUGFYak9GRENMUGNuNFJRajhkUkdJSnVWODFMeE1QWjBVTDZ6cGthZWJoYkZCeGNSSmUzOFVpVGJVUERqRldrMmpCcXpyQnZYY0ttZ2REY21SeUpoSXB1cSszRFFZNDY0QWxZNDJ6MkVNMHlJSzBJNmIrVmdwYW5NZnBkV283T3hLWThSTTV0U0p2MzQwL3FEOFN4cllzeWJNdVVrRjhmSGo3SGN2eEVQQzVZWXJINExXMVlLZzZRYWVGWkx2UGJySFpIdmk0T1hMS2tOOGNHUU84MDE5T0txY3Y2UW5CbGowMWU3cVM1ZXZvR201M3J2K1ZtRHh4Q1hEaU9yRGcrSWFQZU1Qcm44VEoxb1JlWFlJM3liKzRIUWJpa3hQNVRRWEhrNFlYUFV2OTUrS21reEdzUmdUd1A3MVlpTXBxTlhwMGxvSFplWFJwOWkzZXVLclZ0eE1NMGU2WEFvQUN3TnRjYzZzT3VoWlZiMWh0Qkx1ZHphaHJERnQ1R2tkbHdIalpsNXkwTGJ2U0h3SUkrcVllRHdSS1RUenlYYUluSElNKzhyYzVUcmpVbFBSVndCNUxLRnBRblY4ZTd2THY3VDdWL2lKVFc5aDlUblJ0TkNTR2NvZkJXWW01UDd3WmNBcTNBRmFtRVcvR01ibzI3bGR6MHBsdDVISTUzZGRXa245SXVDWlkrSXkwTUFUVWgzWWVuUlRiVmdkTFl0dTg5M1N1TjZFTDRlOVY0TmhselVqSThuT1M2Qjk5ZWN5QzFPdDhzRGFoUXBXSGJtdDJZdldHeUwzUzl0RVZMS1lzK0xuZ2hCbW1TbDJ1UFdmcVBvYlB3QkhOTFcyMUxVamZaYjdqZkxNVHNNcDNpY0dPMW5wSy9yQ3NVZ2RCVktWZzBZcysvV0t1VG1WSm9DOE9lNWgzUEsxVFFoYnBaMnl0UDlubHV0UVB0TEFFdCtDVlQ5MERmVmtuN2xITE9YOEFmUzZITHpmSGVBaHUxYWxubDE5UkhLVjFMSTBHN1JQellnVmFTcFg3dGg5ZjA2dW8yV3B4akw4NmkvMnV6SzJxai9DbEhiR0R5UXIzRjkvYXhtcTRrSjd6WkZWWFZWd2ZpRnI1YmhVR1ZaZVFKSEtGQWNzbnFQS3NiOHZIeUI5U3BGcFQ5VTFVN0Q0YVM5dllncWFqeGhDK2hPa29sSlYyZEtBeHlzQ2tXQm8zU1BpUFVyU1FZWnhPV3dXQ29RemJWMG9lYURFY2dVdHFJM25xOVRTbXBRNjg4Lyt3YjI2UDJDSExZMUg3cTVseXBYU3Jud25uenRxL2pOMW85bHl2TG1MeUdndVYwVkpuRENSRWtpVU5yWnFHRzA2TXN5QStQaGQ5Q3VGb001TTFQeWs3UzZUSmFIZFR3MG5pM241eXNBdXAwa3l4cjY1bEZjODFOY0g4eFNtcHAraU9FdFFackgveTAxazFyR01SSkFHRmhpK25EZWNwVWxucmgrcUJPQ01aQ2NTQ292T1BKcnhqWm5aSkRNTGRwTVZ1K3RCU1ZTMW5LeHNZalk5RHRxMS8rK3JpVmZMVVZoem9mSWNJZ1FRUE9xSGlvRUx4VTNFcENjWk1vTDlsYWE1WWxPWkFNRXA1YXB4N0NwaHJrTCtmeUtiQkFmOGN0d1ZkOTNGVG83RjVPYy9hbE5zQ2dLNmxIcnVQUk90TjJSeWJpTHF4OFA1TFRVWlhVK0FveXowOHpZSGFzUjNVOGhQREtqKzZhcldYUjl5V2RKb01uNDVwckNTVVJLS3kzK0pIZ3ZzMk90NnY2R2JFdGRDdW1nQ3R0djJWTm9VM0tPcVV3cU5JV0hxWW00ZU1palRNOVZXQjd1bUV5cDdVUE9JOGZkdUhKWTBXOXhTQ1pkdmMyeE1qbzNaZHUyby9XWktETU9TaDlVbUx2bzQ1SUJwcEQyZEcrK0hKdThrYmZGZGx3dUl4azJLSGhnSFFlTktjSGhGa1lHUnpMMlZKVk1PQWIwQ282NHd2ZHM1Q2FZbDlabUJtNHp1R0RlYU8yZUkxWE00K3JEL0htWnlSRjYyU2FiZ0FlOFRGNDNWdU11dGlnSkpNZmJXMlVLMGF6R0xGYk9mdWpuSEQrR0dCWW1TbU9RYlVDT1k5OUhZdnN3QlFBNnI5aHJjMmp0c1VVeExWanhuWjRKbklyVHdJVmRXQ1RQdHBKcHZsQTdtMDEvNHRiVU15ejltdjFqZE4xamtpSFFDSlhYS2c4YkorYXFXNnJid2JuNXlEU0hCVGNGWEllZ3JoSEdBakpPWkkxcHlQODNaM3ZNWVRBSm9vOFY5SXd5UytVNk9WZzc4K0loU1lIRFlqUnM4RnJGOHNtSFE5aDRxQVl4cDQ5clJQMmQ1dXhMQXVQNzJHdlphWXZmZUxPa01yY2cwUGtQdXE3TnNYaE1GbWlaYTZQS0JIMWwrb0tISTVEQkxkWkN2Q3dUUGRYcW1uejhnTHpWUmIvaXhMVFNkaXQybnJ6dDB4KzVyRGVaVCthYzMxTktOc2tRczZub0tsUWNjeUQzVXh6ZlZaRm1jYnBtcmZQc1pEMFZlMzR4cEtXay9FOUtobjRBNXlWUFZxK2R3bnYwRXlZZWNQcVhHVTdSOHN1VFcwQTZOSld3ZUxJM2lTR0RsUVh6TVlzU1drU01oRlRmeUEydlREdC8zd1hrK21WVTZiUk5rWnZObnlWSFlpQTR0bW5Od2RoL1JWc2svRWdTZXJmVElmNVZCbXVBYzJJS1NlTDVOYnJnM2FjZ0ZqODBtSThTV3NjM2ROQUdDQkxMTVA4OWdINVVuTFRLcTc4ZDlTeFFIL2c3RFZuQmgvcW5CZHc1Q0Rydy91TXpjZFhTeFdxR0lGY25RWnQvMWFPSHhVZzg4TU4ydytGUHgvVjc1Z3kyd3pFVmU2RzUxUFFJUjJ0WnN4YnY2MkhoZ2p3dGx6clZSRXcveXpsYUFpdVhDMjZjbnB2UXpXWHAybU9naWh5UENXcXEzOG5FYWRYMlQ3ZjFZNXpHeEVHQmFULy9JY0wvQnNxdUFKWDVFRGJYOFgxcDhuTFdSMnl5akZSdnFDL2pzc29DSkJDREpPc1p2b0JmWHFRU0VLaE5BUkgxWWZ1ZWVLQnNsQXdMaTI0L3dBTzFCSHB0bGYxa1FGTnNPUGxEdmxZZWRuckVwM2E0U0F6L0c3TElWRXNaQnUwRUtXWnUvZXVCL1hLZGtHb25QNnQ2bGdFY0NPdzhtY2V1enZFVnp5b1BuTXl6cnFvTlFYSmI5QzhaQ1hTaWVkS2lDZ053Zk5rcFZsSGJVZ0UyUmI5V0ZTY09lRWFkK1QralQ4WGxTYzhyY3ZrSXVoQXYvZ3hSdTJlYjJHb25MVHlva2pjR0YxRUJwQ0piaHkySDNsaEwwcmRaSXcxb2tBNXBCZzJvUmZRY2VYVFB6aHVOS29yVEVGN3QxVUlnRHFJbzcvbG94eVRnYnRLdTI5bzlLOUt1anZDcVVHeVBZN3VwY2ZpWkxOQlZLaDV1WEFBWmpRamhsaEJwMHVrbU80QXZ4dTR4QVZoQ3Ruc09JQS90QW05NFUzSEV1U3Izd3ErWkxvOHB5b0M5RUIvcTNwT3pRUnlDVGtvem1Kd28xTG4vMnhFYnROblMyUzBOVUlTM3l6My9tQklkeE9OSHhxUDlGVyt1b0dJMUY0MTVsSTFuWndLMFNvUEEwK2ZsYW9rQkdFb1hnWm5PNEdPRXhVN1ZPamRQbnM1OWVrbUR4cU5oRUhlQUY1aTVOLzNXMk5DMVhHRmpUcHFMcm5DRUNpd1ZrT1RyTHRwMmVoVUlhZWpPRzYrMTMzNllRU0tNU3NMNHpoVWp3NlNRS3J5VlJ6NUxkbjNSNS9yOEFPaTAyUkprUVhQZHZQc2wvRk1nOTZFL2NKbUlGTG1FRHpyMUdraDlHM3ppc0c0cHFNL01WNlhJeitDdERVaDZobUpCOTdWek44amFQU1M5MHZnRGp2bmFObEtreTIvekloRTlPYnVnd3JmdEkrT2kyYTRWVmFCL013bjNWbWFXanNVOU5PZjJ1c2JjTi9HTFFNanZmZVUvWXZ5RUVSUEt3MWxlWFpXV2sxSFh6WTNQOU1VcTZNWnExaGtFZ0Z6ZHM1MW12OG1ucDFpNHBRcHJQd1kwVElkMXN6WHdlNVRHK1I1bU1ENzZuR1BRcjcvRWhRV2tzanNnR3M3Wnk1UVl2TWNHVjV0Y1hKUis2aGxIRklBYy9NNlhqa0tZdHdtNjczQmkrSzF0Tk85aTFZQmVQVHVyNEkrZ01zT0s3Zjc5ODBtY0pYaGdkV2RoTnpVTjJKdkZzdlhxM3paUkcyVjMwc0p0Sll4ajBhVXYxdTQvcHBWSGkxaUhuVFkzZ0RIc3JRUzhZd01YNVh3WjJnY0ZZWWUyd2Q3Wk85c3dyMGdiOHpmL2ZYeDhRV0tQWGNLMVVkSmszNzYwQi9UTWxwV0xDYmhrcVZvU1RzT3F6Z2ttRm1GdGVDQ1RHaE55dkZodzFSclRJV3pSeHE4VGo1Rmlydkt2dGtwMkdBVmhuWjd2bnI3MXB5STByS3dRYlZ4S1p1cU03R0F2bjJtUkJqNXA4ZGpsSFVzaC9yL2VCRUNwdHBiYmpQNW5GeXVONG12UUxaQ2F4ZVRrRFV6ZC9rTkdMSXpCRnYxQ0VsUU8reG1mN0R6dDFmN0dNMUJoK3dMRENKWmxoY1ZEWGJ0UHVHc3NkRWllM2xaTmlXY1hNVGpadFdBVDVNQ21wcTZKQ1J1RlNIWllHS2NTRlo5a09ZSmZFcUxJY1dkenBUQStIbXUra3RnU1V3WFZTd2thYS9hSGRaWGg3SU95cnVkQ0JhbENacGdYR1JOYmhOMlhwRVk2MERYWE8xQ2k1YXlaU294dEcwV1JDQzUwK1h0Z1d6N3FnWDVNUkE1UytqelhDWXk3TzdObjBsalZ4aUJ4UU5DWktaTVRxaTZtUGZ5MkxaeDc2dXlSVVhIam5wSkpFaW1mbEhEVXh5WDdmRmc3aUp2U3JzWk1INlV2MnhiZlFOeDVlQ2J4M29LeWNVckJZMjJLUG1nZmcvdzA3Q0RWc3c2dGI1VnhQZzUvWDM4Y1F0WEk0N1U3TUFHR2pPMjhJSTEyVCtQamFYSGxzdFB0a1VRTm4wREtrQ1lpcytrVkFrQTF3eUFKZ1lLTEduS0QzbmxWQ2FyWXFDa05JWmJpVndPMllkamw3TjZpT3R2dmJBZnVxN1ZLWkxvMGpFZHcxWWRzUmFIY3VKUXVsZ2I1MUp5RUx6WUJrUDFoZDAzSURjWmZQZzVYbU52WVFTT0lOc0NTbjNCdUx0a0NQWlJhbEs3K1M5N3p4dkpIaUpDWkpNOVhQNzg1Tlo4QjhmcURlL090MEJTM1BIMXB0RXJ3eEJ0cGdmT2o0ZC80MW5yU2pKUWY5YlYxa2ZkQkhKeFliSElMeE9zV2tadm9QL1o0U2wwWXgzYkRqVEY5NnhmOTYrNnVJb1EzNTFDZTZEZVR3VG5rUHIyMFl3QVRsbmhza1dJZGRVb2hrbE5JVENxLzA3emtpRWMzQjU4dWlCRzZkOVlBYzRoLzdzNDRGTjJSRzFVdVpXZW9qck9aSWhFbHZEUDRLcUhjT1licXFTOTVvN2lsUUg1T05KZnkrYVlpQitzUHBuMzVIZkhHM2R1THBOdkJqWGMrS2xmNElLckZIamVWdHkwMnhQVE5uYmRMNGd0a3FQcU1MaFNnUi9mRFh6eEpiU1NjcWV3aUYxd2RWb0ovZkdML25HV1pmVmxESE9RS0QrL2kvbXF3WHF2TnF4dFplUkh3b2UvYm9kazY2Qjlzb09uWnAzNmdkelZNUlJRc1FpQkZmK0hYalJjclJmOUZzR2dodzMrcW9OMEplZU12REpya1NCUHNFU0RhaS91Vk96bjJPaGdlK1VWZGkwNTBmZFdwc2pQMEQvUXVUZFlzNlF5STl4bmhVOFdUMitLQkt6b1o3QnE4Zk9kS1BlTHVsVWhKalQzNC9FT25VbG9xdXM4K3B6cU5oL1VkVU9oZ1RscmJrdVRmc2FJWURtODd1L0dOSWwzTjUzdWFVOGJnYUJqcHowamR1MWY1OUs0S0ZEdHdVVWVFVW9lWXg2REVrV0tIZGk3ZHRIaFFGNDRsYnlzazdQcUVScnN1QVF1MkQ1dERNbDdrRm9HZEk4ci9zOHJNeXRKellCVTQwd3FlRnZUbDBaVkxkT0I2WWE5RS9mOFZQYkd4NU1kcFlxWU1MTXlCMFF4VmRub0ordGdBUVZXZkgranRPSEQzUHNqdVQ4ZE9UU3J1cHV2SFdSSFFvR0kxUWoxSGM2aytNZzg0RkFaL2d6bDNTRXp1R1daS0Z3dW8yRDNFaUc5NUQyWjFzelRxQXVGUm1UMW5FaDIwdGtDNHlzbVh4Nkp0TjB0YUsxaVJSNjJzMnVOVzVyU0F2TUVKOHlvdHIzVWhKZTIyYnJsUW44R3ZjcTFJMGFPRGFISnVjUUtWZTZTWHlmY0RXT0RNdzh4ZisyQzdaeDVhNFFsaDdwSnM1NTBEaWN0TDRPeGNEWEt2Vm1MZ1ZXUndiM21veHY0a2N4em04OUVFUkpYQ2w3WC9CemlCa0dRV09IUEdGKzZLNU5GSllPRlZ2NCtOeUZxK09QTWFTV1pLb3lkcGx1ZlkrQ1l5TDYzVDhNQ01td3FMVG1BRThoMHByaGkxNzR3bng3REhaV1l1UkpTWVo2M3V6OTdBR096eUkzYWViY2xudWQ3N3puYlpldGJXVXJpcGUrQWFkTFFlWlB0V3NGK0ZOaWFYQ3kvOThrbTEzN2xXZXd5YzdHYW1haTFIZDNMcytLTU1WaDBSM05LVFEwOFRJQ2xEZk1Ld1VHS3kvN1labEpIVTN1VzYwWDByNzRBZmgwMnY1TUpnVk9Za2ptb3JzNkdBYURVN3lLSHlkZmtYWWQ2bkVqWWM3Nnh3czFMRExXQ05OS0J0VUhOeUxzZU95TkRnbUhpSjQxbFh2cTYzOFJ6REdpczhXSW5pT2IvcGJUcytIc1FWR1BpNm14RytDVStvZmxNUjYvcXgzcFZQK0dQZ3FhMFUwbG84TVZtSTFjQmdTblBHZ3JoK0orbTlUVmc4bml2dWEwRVFQN3hhaTQ0cnVDNWdzQVZPcDliTHNEWGZIUXVqbzZJcEJtcGZiYlU4UERhdlpwVHVKdG1mbFZRdU9JbW5SUTVrS29RejJOQkZqZGlISDNjRjlRTGdEUDV2ei9XNXRyQ3kyMlVrK1RDalhqZGJDQ0hCM3JKaEtZVHdpeVFVZjh4dTZ5VEt0SXdyYnc0dHpGZ1hET0RtV1lFbm5wRHVwazNiNEFQM3F6NEFaMkVuNXdpNmFaVjI4N0FnQ0Y0dkg4VGxXTG5pMUU1SGQ5M3ZMeFNZTEJXU3VqM2VYR0Z0V3lXcEJrSWVLdStZc0JoMTlWZWFrQThPZVBNMElMdTZkWVlsOUROSUsza1UxeWJIK0E1eFloRkkvRXFTWDN2dE5zNlY1ZVFneFlMdnUwaFlGamlHK244SnpxTFFWUk9pVmE4WE5RRFlKdERBZXRQRlN1RXRHSTNCOHJuYmJyTm85VEpuL3ozbFJZcTBlY0JJZTdhMDN2TEVTd2hLT20xYkdUazJrUE12L1NoOXd5Q09tSW9yZTdKaFNGVDlISWpvbkJmaStnY2RETGZGdDdkcFNoSm1XMWdrY1htaXRXd20xY0M0ODBDcmFIbS9vcjJNSHBoQjlRMWJtdC9TQlhGcVhKZGN2NUdUdDNJUzJmUmdxVGhoSW5DalJraDdEazFpUzJ2TUJMU0d0UlBwcGI0RkV1NzYySmVoVU1ReHhMUXJlMzY1Q0tvSkd2SndWZGU5MVhRK2JEcDVac011L1FIbUxnSVRtd0dYU3BRRlFsUUJhanFxdXhsd0lPZTJjeWZlemFTSElvUk5MY3dqVytlcG5tQXRtbVdBOUtVMjl2L2NBMml1V2JqOVpWN0hSNGFuaEhramJ4bnpLUEhuSVo3TW01d0FmMm8vM3hVaG5mSCsrcXVTMjBUZGhhbEhnTmh1c2lkUEtXeUtXVjhaakZMZ2IxZlgycjdpZkx5VXR4dUtISElmQ1dYUUovREtlVTYxdnhtUFQzNE1UaTJROXI3L3NLMUNZdUhWcU1Cc2d0ZmVubjMxYlV6Q295UE44OUtpTzV3SHZlcW5rM3V5SG5KU1VCVlRRUTNOeVJQbWVSS1RRdldFQlo0UVdjU2dNeVpGMFJRZ3ZVWFJjcDZLZmxGMDU2ZndhaFNpb1A2MjJUZGNUVllpNGNBd1NaTFdEdmZqb0tGTE1vd1BRcHpuNm9nWEhjOTNmRkE1TlptbndzbFN1ZXNPeU5JMUVFM1JNOGt6YXQ2dGhrbXBPaUdtbTY5WW44eU51eHoxWXV1UFdla295YmtlZTEwNlQ5V1RQWG80NGVhOUU1UUgySWc2RlpuNzE2REJhMkZ5WEhHMUIrWWZubWhiRXBBTmxPaTYxQm9HTzQrRzNXTUpEb2tKWGo5R2hOc0ZxZGFMakExcGtoTFArL21HQ1pvWXN4TkkrQStzTXZXeW9qK1BNV2VSOGtvUnorcjlwTlZFV1Q3MFdoaUFrTlRyb2pkcjBzQkx3eElNN0Q0elQrY1Z5OTZaRStBQmk5Q3FrTTlWSzdpT2ZrSlZwN0FxQ3FROUVaOWVtbjhyQjh6Zm9RWlVCclZkNllTMkFxaVRGdDBuSjhIZlBHbW5CV2YzWGk1Q2d5V29MQW1ISnAvQWZUZEhCMCtOczVEbGhMNlVKK08vNnh5cytDV1ZLdEw5UzhmVkhrcHdaWk1KbjZqVnRpVVR0WGp5d21pVlh3OWE2Zi9HN1FkNHRadGNvUzNheXR4WFlBOWFHR21FZUJvYmppYW1taFVhTURpY0gzbmxPa0R2dnoxOU5xV092SEMyU012N09RSHRESXlrWWVyUHVvTHo2U1FOT0J0dzZvWDJTajNaTElUQkRjV054OUN1WllZVmFFK3ZsZVhuQVRyd24rUG51UTM0akw1MnRwODVhSU9rNjg0U1VsUTh1eU8ydCtlSU9IbmRaM294RCtCY01BYmEvSlZ4UllVQVVab0V3M0Q4MFdXT3owL3VsK2ZZYmhGbmZmeDNQZ095MkxMaXU4MkQ1Rk1TcGkrUGQ0RWtJRlRnZnY3cC8wdm5YMXdwMFZwTnp5WHMvNVMvNHowUkZTMjF2SUY2N2sxRVJUZkZ1aExNLzhmZGJLb2dub2hNcVRORi8rb3F2WFhMdUpCN0lIZURkbjFYMmVQYXJMQkVwejh5OUNBTjJnNVZkRTdFaW1la0FPaGt3K3RUenFlRXNneVFMNGlWRG5XclAvUmNCZDZDRG0xNi81dCtJMVNBeENuOXdvOGtuem1wZzhEWVA4Vi92SHc4U3R1N2NsaUF0K0cvVlI0WFBOWlhXRjJyWkJlUU83NW9zMmpGSnJidGtmaE45QnpIVDRIR2dYVGp5VHk4TkdzaVFkZU93MTJHallLQ3l4UCszNGtSSFpxWXNuMHBGdlZ1YkIwKy9lbUtSZ2lHWE5SV1F3TVN2QUIxeHZUcHJEMFp5dDA4QmpQLzRXOUhHTmZOQmNBMFFiOXFGNWhkUTRkRHFwS0FGTG9JVzJnRkVWS09nYW53M005LzRXUDlja1AwL2c2a2FKRFJ1cnR4TmdUK1BqdldZRVdsRmE4MHdLWUNrZC8wWkNoVjk0bmpqR3lnMHQ5OFB6M0FMMkFGQWh2UlJpSndkZlJjUXFxaFdrdi9vNlg0NWQ1dzFZTEpPeWUzdjdyZ3RhN1lhMGpBbC9hbjQybmc1V3o0UzV3ZTduMisxVzk0Sm5wb0d5VjhXVzJIWWpLTGtLbXA0aEJLbE50YjV5NFcxTXJzRy93ZnEyTjVYcnoya3FoZFBRTC9Zb3hnQ1FkNlkyS05rQURWdTdUeHVnUVJXVnVOTDBCVWozSlJGeVdOZUNtQjc0V3N6NTRPUG5icTBHRkZ4elNrb2lKM1J0cTh5RUpNS3ZPTU1hbEZLSDdZRkhLamIybndyS1ZmdVVVdVJ0VGZKRGlCdWFFSEhvWCtNVXJNMmJCYUFzU2RuWTVQanFjTUJuL3d3b2pReHp0Mk1vT0NDM09FQXJyMDlnaGhzajJNMG11ZTVudFFjbWNDMVIvc0szemZTaEdKdWF6UyttSlVlS3hrNXUzNkNZajgrU0pDcThaRXY3Yk5mMStCeXdHZURRb1RER3E2WWgxeFczU3V3bzJPL3lrYXpUUEsvVGRWT0lDeWl3SzhNdVFwSytGWDNtcVNQenhmTHdGSi9pWURqczBXZ1cya3FYWWdtK2drTlRvQjUrallIODNYbHQwY2J0RW1ra0JhVkdsSHo2MXJWdVd6cksxeWpuNW5ZSEt2S0NyQlBQUnRoM0FLRFFRQjgzZmRyYmdJZUlmQjNpSHlhNU5QcEV5eGJ6bXRONURuazdHcXJRNHV1NGgzUVNvSFUrNzR6czMxY1dxSXg0U1oyYndXTHZJeFV0UjZndWZaaE5ab01jbVNCNXoxTzlUS3ZITU9SRCtWbXVpcXpzeUpLQTFPYUFwQitiOXg2dTlGVHZVa2FsZ2wwcjdyYVYrd1JxaW1jMkQ3QjF6L09pU2FnZGQ1VU1FMmlnTEdVY2dQbE1TWDFWc0tRcC85eURpWWVpODdLVEJBMk5QQ1VtZ2FMd1ZkdlFGRkZ4V3AydkdDWS9LQ1V2eHQzRk91NnhJZ3dTNFZ5YnZiajZmZVVDa3JRUHBPL3dQSEpQaEFvYlNqL2FhNVlyVXZqSE1jUWtEWndmYzltdmdocmsvUElQdmNKYTVJbmhWQmZqaDNYcjl2SXZBNGFjK20rcHl3Uy9FcWtTWDU1eGdpeWowVEIxRUUwTlQzVzJDUEZkVkQ4OFA3MlNwZEZ6SFMvNlhzbWJHdE04SkUvbThlb2p6ZDRQTTFiTkFEbGlaK1hHLzloYmNLZzZQZnRWS3lLS3QvOEJ6NGxHc0h5VDBWS2oydkRHcC9xREdCYWpTSHJxem1wRWpXNUxYc2I1a1RWNkhnYk1jblBXMmR6UWp1OU4xc0kvZ1BWbGdHbWswYkhLT1gyV3MxcTRhUGl6aGNNL1hpSjVFWk5VSzZiWk5VZUZhVUpWVHZHeGdsUlVZN3Zkbm9WT2UwUmFobzNodWgxWERlVGxIcGsvMmdCampoVVFYZThGTjVBNHpjUnFrTnRLcFNWcTB4eXc5ajN5UWxReHEvTG5xa2xwejhsWG16SGt6OHNYOUhKakh3eW44VUFqYmx2TjBaRklrNGxpZWp4MGxWQUNvS3Zwc1Q5K3BRb0xZNHdlTUhSemN1VkM2MERVRmthcUxmY2xTNFVKdGk1V0s0RkUzZFljYzBPaWxYNTB1c2NMSm9tbFI2cFhyaUQ2RUxOTkJXT1NNdDUwQ0pqUGt5dDNabi94ajFkbFBWUDF0NlhFeEsrYjNqTW9VTExQT3JFR3ZqRUxmQU1NMXFjdUJiMEFpamtJdUZjYThmOHhhcFVsa3ZMam1tSlc3Uks5NHI4SGFQenZtSEhTcVg5TVhkaXZOSTRBK0pIeTBWQ2U3OVVaWkp2ek1HenBuc2orUTZrM0VJdERCaUExMmZUTWxTYkVPTUFXQ2RRcTlUdHlVaUFhQXFKb3pNenJ5RWcwayt5VkhxQ2MvRHlKY0NFMlY0V1hJaEVuc09jNWM4ZjRDaFdmVWFPTmhQUFdvZ3BEcy9seVZDdnAzbTBOU2ZyQUpLTmlWeTVhTkM5Z1o2YzlCcXdZZ2ovY0RPM2tkYW02Z0NqaFIrYWtBTEZZbXQ0aXhIa1d4S2hEVEdzNUsrQ3dSaUtKbnZ4UDlkYnhSUENCSGJpVmE4Z3NkMkd1aU5IWkQ5OE1Od1hNZE1DME11YlZvZGQ3ZG55azNVUUZmQ0lJTDFvc1B4WTBaSjZEdlpYd3RaMkkwdGg2YXFsVE1VTFZvK2xoU0lVLzVxTzYzbFRTYTNNZ1BSSkVPaTBBSjgvVWxadXZncUx3OWR5RURRb0hUS1dPc3ErNmZ6b0F5dklwdjE0ZkxhWSticmFQZDZOa1NhcTBSQ2xNZW5LMVFMSDg3TlpyaVVhZXVDbzZTWjcvQ2ZVdDJLNlZPdDBBaklLMmpSMHZvcmY2UjgrVFZ6eFpiK1FkTGltSDlwVTV0UWM3M3hXOTNRUlBNR3kvZ0NLK1IrWXptVjRmSEs1MkdXQkVCTDA1RUVvVFk2T1lHMVdXamk2NmRXblZUZzB1UE53ODM5cC95akx4a0NmZFRhSCt2NmhWVUNkNkhsUk9qNlc4TWlsNkFZR0M3TkkyK3FrWnZKaC9kQXcvaVFzcFhRTnd3V0hyNnNsTElwMGhCSFlURGgvSjdCYTdaUjZjcDNpVTRiU1hkbXpoVGFoWURldjR5S2lJSHlONjRFQU5oSTVPSFl2MUc0S1hmSU92UWl6WVdjaFBoelFnNWVWR05NeHNxcnZXVnhqdElia0t1SHpFK0ljQTJOWjgzR0t6MEQ4ejV6bWdSbm9KR0tpZ3NlUDlUbU1TN0JnQXF0cXlpeEEvU0xjMUtFVVdyaFhPUTZrQTVaUVJhenAzd3dTYTQwNGNwcEJuZnNTOEVzRXBici9nWHlXMzZjWjlwdDFSaHp5eEd4RFVtblplQnovVWYxQVArZ3lMSWc5eDA0dTFmVGhtMncvSDFaWEd2VnFzTzFWcXV0VjVnVWhGa2Rrd29Danp6M0YzRlVyMXYwbmpHWVQybVNaWXZvRi9mU2QxVzExYzVWSWhrRU8wNlVTNXdZUm1IVlBZWG1abmJLNVlIUThwa0lESjB5cXNzcUZLMzRDdUhFOFJXYitEcjRvbWs3NzlRT09jWW9tQU1ZUTlJTHQyS1VrMnVObGFoVy9Jakd0ZW51R0x4Yi90M2FGb1Z6NG9Od01aN2l5cDR0ZDhtZHpnSkFmbkNjWXRrbHViR0FVQjlrNmJHQzVEU2tmNVZGYXJuR0VCV3o2MDBWR1I4UXl3WitqSVlGWmJ0S1QyUWRET1lQNms3RDhxVmdFWkJ5R21SZWRaUldhUURUZ2dMeU5nREQ2cFF3RWVTczgyK2hUeFd5cHF3VTN6dUFXcWZ3aWwrbXl0elZuS3p0eXZNRkp5SndQRmFQcjRaM21Uanl4Q1IySnY2NzRKVkdHTVVTV2IwbCtHdGNZdGQrTkJHQ2h3cjhtQjJobHljY2dldDlsaUpoUUViMFhnWGZnVlJsSGxiTytqbFo5Q2NBZXcwTncrdFJjV2dObnovR0w5S3VyN1JvaFJoYVlaQkJtUUE2Smh2emthekhSY2RaRG4wekRrZkJtWVAxUGZRalAzZDZxcXg2Z0U3dnJiM2xCS0VmSzNZL25DZTRDT2RwcjIzb1pDb0lwc3NHWG1xRThDR3BPMmJFd2tTTjZ1cWVxUjRVdFdSK3hzZ096TmVSNDlQVExKcEZFQWtYaGE1WWFlY0o4dC9LUitlRzcvSEtWMjN6UFpBTXZIREMxcmR4UTBsKzZ3bElnWmJVeWJqQmU2eXVzTDdpc1J1dVlZd2c0KzgrNGxpYTJveDhSQ2R2bVhsdDAwWnNoQm5BSWZMa1N3SXFVekNjc0QvZDFaRzZBejcyOEw0RkNJcUJLcGJBNmJ6a0o4N2xZUXBiYUhwd1BwcXUzUzBVcU5EQ3dnZzNxOU1FbjAyWDE2RTR4aWJ6L3JMeDdOTUR0SGN3TU90OXIxZFZVNkh3czlUdkpWSDdUSHJuU0ZFU2dONWVCeTUzTnEyRmRiOG15U1R4ejVDaXR2VkUrWmpIYVlTM2hxOUJheCt1UzdUeE1JVDRxSkU3SEdkc0hNMS85dVBOQnlsaFAwNExjazM5Sk1lOHYyZFBPU0p6eVFveThtLzhGYzZoK1grNS9tQlZBOWpBc0c0dm14L0tkVVcrTlh4Z1J0Ly9TUzJJYjdhR0lMc2pPeitaWlF1L05NZXVBc1AxcEZSVE45MHJxSVZVTGJKMjBaSmxyam9aRDFWeEhFb0RGRkdWV0NWT1QzakdLK3ZGRDA2Z2MzeURVU25aN1pIakdtdzRaaUFnbFkybm03OGFVcFh4STRCZlVIcUw2WVFLRkRDYXpVSXJ5TGk1M1JjemxhVGgwcnk3V040V3BXSzlzUEowSjQ5ZnU2UkdVTVlaZDMrTnJSdkVkT3JTNW4rRUpPVGtyNGxOem84dmF3Y1luUi9uMURxMHJDSHU1bzJCR0JFSEFCSmJzRkxpL21sV0ZPMU1qcHZVdTZVUEpqWGxYc2U2TXRCUk9UL21RZnllZ1dHbUZSUTdRL08rckpwNDcxK3RRRjEwK2J2a0V4ZkJvVFFyZXdkNVV3aEFVT0RweWVXK2FLNnZ4MkFyb1VvMmJHQlovWmpjc0pGZk1ZRU1zbTQ3TGRRU3E3VDdwZUkyRXgrNC85b0lBSkdmaGlkYlhBOVVZUE5oeGlnRlRnODNDRVROWWZZVmtvYW1iajN2djRNWk50RS93cklmVGd1Qk5xa1FrOWViTFBUbVkyVTRVQ3piWXFQS081dmphWlhlVmtzb2JEQUp6aFZqb1U3cDlUZEZtTk15THlDUUpyeUJTT2NtMGhGay9wY3djVjE1S1ovK0lJcWVRR1BrVGJpWTFoYVdTbnVRWUJleVc1dVNQSEd0WXcyOGNRUy92M3JUb05BVUdWQlNRNnpwQnQ0Q0h2YU9mRUpodURKWVpDY3h2UGVPU3RkQ3phb1NRbjluRGU4d0RjMU1YckowKzlOOVRBS2NTNnU4QU5MQ0xZNFVmSExHZjg4NC9MRkluNE9MT2xSY05sN0ZTMUlKZ3UxL3ZMbTRJTmtnSHQ1SVNwMnZDM01GSkh6MXpKbm9wbktTMUFnSnRDbWhKUlpEYVc2d2lzOENKMEtBSlcwWXkwK2tXSTNsSjlOOHlxSmh0NjhGTU5WZ2tnYUFHaTVMdUtta1pXbSt6dEt2ZjlnVDhoSnJYWmtNL1FkSEk2d3k5QnFWZVdhN2c3Wk0xWUxiVXYzN1lTbkxtR3NDcmwvVVZpL3RHK2ZaYnpZNGJHeWUwekgwOFZRcEdteWQvdisrZlM5RXRhc21ia1FFSVlubUxaTHhPK3ROSHAzbXlJR3dZQlpWWGpsV3ZyQ2lRY3NQL0Z1OWwwSFdtTEJ1M2d2dUo0cGh0SnNYWGxsSmRNOGlaSVFSOFo2ekVNcytjcVZMNytUWWh4RGQwYzBsNHNieUlFdzZOK1YwdjNaYlVsaWR5ZWtkY3ovYUlvbUdkWnRtZEkrMVFVcnJIdzdlRFhUK0czemJUWk1YeHBFZ0pjNHpZNWJINWF6OGVIendvbzhRVWxlVUtwVlJyc0VyR21TRjZHUEoyT2x0S1lMNi9DNHp4NHJIZGNmc3JRVGNXQm1yQldNTWlGaVU0Tkd0cFllQUNxWWFmUnl1OGo4eDdsdHAzbnhWYnNQTzBNU29hUjh0djYxL3ErWUNxSFgzaDR2eTRIempDWUVsKzRaRHRqMittYXd1ajRKMHJCcGNEdytzcHp1Q1Eya2hGYmtzMDlsUEd4SzhIWUpsMFkvbE5MVXhHTForMmg2K0VGU2FEMjJiWXpGN2RrL0VoQ1doNnUvdjFIVVZLQy9yL1dsNkpIdGQxVjY4Sjl6ZE9UZ2J2SnVRdWc0cjR2VVYzSkpvbFFRNXRlY0hLcWNOb1lqT0lzNkJaVGxmQit5SEdmR2R4VEtzR3hiVS80dGFLdUg4UXBkL003ZklHNXplYnJwaURIVjk3VDRqaVVOdDdLNjQvdTFlLytlclhWMzRhT2pmZGRjS05PNzZFeklmMXBmRCtLaXZCc1JsemxzamoxN2FEUHEvbG5LSFFDTHNEKzNUSzAyMUhOemhaeXV3cExSS1MzS0UwWEgvMFRxVU9yM1ZxTE1jc1NaTTYzNDlRSkR6blBHK3NVcWVTNnd3TVdwMjhUQW9ES2RtanpXNmYrMmF1NzFIc096TEllV2VuY1JhNUphcEtrVlRZcHZ3TUlDOHUyTCsvaFlHSm1rMDU4OHJxNk5ucWUwNDFOTXpVNmxqMUs1S21TajBaUmlWcHp1MkZTVGw0UEJZSEF1aGU1ZHR3blJRd3Z2TnFJRUxWeEtNRldlZHh4QjdVTzR6cFlSZTJ4MHpINFg2cEkybTRnNllkQ3MwOHZSOUI3b215L2dvUVVZYlVaQSt3SmFtcTcvYzBGaGtObTc0TXAwNU5TQ0sxRGN5MSs5cXA4MnA4WFZrVUI0K1NzVlJKL1RxdG44djJlc21lbXI3empDZmpMaWNNYjA1SnFOb0w2enp6MEthWWtYZVN0QnJGOStUN0ViWlRvMkZhL3dTNU5oSnZSb1pjOFFVZlM0NkhYOEhJWjhBNkxLOHpLdFJPbmFrQW5FRUZvb25WbHZZUjcxeFl1QkFYYmp0eGZ1L2J0ZU44V2tBckIzLy9xcCszYnRwaTJTSU15SzZyWDAzaUNMbnpPZDJPclBuRDZ4cWdWVDM1ZTZOVU1wTjdFSlN6MERSUnp5emUxSitEeDNjZngwTTU3N1c4NHFpZkQ1MW1aRzhWTmJCZis1UHhtR0dyR09ta08rUTQxWW5Da3g1MUQrWDNDWHNOQWphei9YZmNQSlVYSjAwdmFReWZZRHRtRnE0a1UxWkhkbmVwNDhUNElza3pQc1lUOW9yM3JkL3ViaVlMcWVCcWpuR2J1TldiOVpkUER4a2VCbUp3WVRqc1RVK1Z1Z1FtdHo1K0MzUUJYMHBpVmgzZDdCSytIazRtTzNxOHFKVlFYZUlxczRoS3VSdkJmSXd3VXlLZzlXMXg4ZHYrRXdFU3VrMkJnczErWmMzd3p4NGVHYXN5bldzM1YzNjB3SDNmS1haRlRja2VIWmRndHpUcWNRUEMyaENIaFNYeUZNeWxqdnJuZUxFK2MrYi9ZUTBYY0RCYW0xb0FQenZLbW1jZ0VSNkFxbnlDMzJJYzRITVA0RlFOMnJoNFkybnRyYXdCeVYrOW9xL1o4aGR3UUVQWVJZaUVMQkNudUdHWERRYmwzWkx1VW8wdmZLVS9BdU13WWZOWG1OTTJ2a24vR1JycGM1V0RQK01FTDgwdGJKRFpmRE5CUmZwZmN2VnBmNzV1MExya0lJam5VNGFkYW9sWld6QjJ5aklWd05yRjd6Ri8vbjRONXhIZWFHYzdWaDFFWVJkYzBoMmwyM3FGdkxCTlE1a0hibVg4WXRhMlZqNERVNmVCTjNYeUpCdkpmOWlMNHgraHcxaHgvN0VqNVU4RVpyL1FoZ29uaTVyOVB4QmZVM2ZkdlhJQ0dXOUR6U1Q3R1YxNDFidnlNRFhibEZHNVBpek5qSlVWQVdOU3hJQVN0ejYrZURBYmtZZUFLVGo2RElSNnlzRnZaQWxvQkxDZ1NkTUZkM29sL1dYRFFoM0JiQnRMcU85aHAwOEJmdW1aakxwVEpHUkFJSHpEaXpYWmZoYmdxZWpOU1MyN0JJWFFMVjBtdXd6Z1hHcVl0OU1jU3Z0TFdvMUZvczNrNk51MnFHeUZmdHFReUR6MC9ibWd2dFp5aUZjZS9TTFluanQyUTlCbmxtVVZCV090YkRQdlVnT1NpenZKRGhkaVNrYkxMUDk2TUo3ZEtPM2VVSzJuWm5wYjRzNGIyWEdGNFQ2Z0M0cW85VER2OXoyU1k0UmZmYi9SalBzNzZQMFlpV0FEcFBCL25RakMydERSbHh0NHNkTkNJam1Nc0xnVStjcjhjcHlhTVNZSTltYVA0SEh3dzJqVFBrR0t2RjZINitERkFGK2pBWktUOW9pMjNncFoyemF2RTB4WFBrRjdhMkZUTkozYnd4dnNKVitvMGZYWkFrbW91WXE2QjIrNmNjSGhuVUllTDEwUXRaYVBvWlBKQjcvWHJ5LzJOditKSkZtUS9wMk5TaU81YllHQThlajF2aDVRbFdoYVgzSk1zNWdNQm55eUlmWElNZjRpbTBXRVVuQ1BBSnpxOXEwNFRteHp5N25HS0tFZjMxa0FwNklGazk1YWowQW9nTDdpbGpMVkpsT1hOdlY3QndabjRkS2Z1WndlU0VaQnF5K012dWFsMFRWREhpd0h1SXVYYnZhdytPa1U3YWVBZmNrMEhjNkgwamd0OWc2UnhiNmRBdWFpS0VOMWNVWXREODh5MGI5QXJxMXE2TUw5QjIwL0Z1blRuWk5GK0lIZ3NnNjQxRmZsbERGcFErZHFySVBLUThJa0x4LzJwcHgwaXZRU3JlaE5hZjVkd3RCam5QSHJvUkd6Ry9SV09kaVcwQ09QemVweElxY3NXamhmbUJYU1VEN1lDdlBtL3FUR2NTbmhjcmlGS2V3NmE1czBBZ0swM0kxZ0VpZlg2eTkwY0pCWTlSRWJRN3lXL1hCK3pBWE4xWFpRVkVzN3IrMGFqdHg4S3ZWQktKa3NLajVZRkdkaEVlbm5NYndnQ0pKSU1kdC9wSkQ2RkljTlZlZ3QyTGlRUzcwREFKZWlOTkc4NmRRVk5ZTlptWUVmbzhvYTAwMnhLTGgxK3JIbEJYNDBpWThXbHY3RnFzd1FGa3RweUxuNW9TZG8xakJSejhWM2FSSU9taFNucnMyd3hHd0dCRVZFWHZSbThSWlZ2U1EweGxLTVZXczlZN25ubUo5akVWdURMMDhEMkVTM3BsenZDTlAzRnBLUWVTa25GZVZCWHY1VDFZazAvWDV2ZGoxSjFMWWE2RmZ4eHJ2OTBPYkxIQVJrQ0krdHo2KzBpNWNaVGludmdJWUxNVm5WL09MK200UkNzVHkvKzlWUVBzWXY2WDJxU1NsVmRRM0tNMVNPbnRNTlVCcGI0QzBNc0RoMTB4SFEwY2JKSzBnc1I2WDkzcnU2M0JEWWJSWm1QSVN0MWNhc1Z3VlZFNyt1M2w1NVhKR0owRXY2UysyenBOcU9BSDY2UnV6cFZza1hFNlg4eDZ3SE9mcDVQQUkvN1lHM1pvemgxVTI3SVhHRUVLSW0xM1J0L25URTNwS1dBN2kxTkZkVlFLUTBDTmRxRXNCa2ppdU00MWRkNXJJYlI0RE1ub0R2YTA3djFlc3hZQkdVNEpXSlVKUXllalliSTlwN3BxanJwSFpVTmx6MmV4WDFsVEFrcytXeFk2Q0V4b1BsU2xOTnY2QUlzRTBWZFBtSE9qNG0wYThiaWdEZWxUcElMMVdvZVBMaGJsbWhSbGtQREtpWnZreno2ZUc4dkxlSmpDR0pMMStWRmE0UVJFQlZ5dWhjcFptMXlnSm05a3VRKzh2NHlFTXcwVk8rVEtlZTZzTUZSVmMva1M0SWlySnVwbnc0OExvUjJhUmsrR3VEQloyNXhuS0Z4ZFNZcVpxdldsRWNlbXNiemw3d3ZRZzV6MnhLeEVVc3F1eUd6aXl6ZC9YK1hGbC9jdDlLUkx6eXliNkNvbUlMOFdhbTl4NkxQTlpYdmhPMFFRWm1ROFQyTUZqbVJKNDJXeVJ6ZnlMR2tKS2Z0OTR1TzBZeTZGZmxvM0FvSUVvbjNYQnlncGkzSmU5MzJUb1U1RUtvaWt2cWtlTEZBQ3BzQk41ZHNlZW1pTWRIeE9KS3JWSkRkVFMwcUNjVHpQQ3l6NTA2b3lFTkZkZWxza3dkZ2htVW5XeVhLMldlSlgyQ0JYdWROVUJPTi9pOGtNZHRKbTUyUkV2bUdxVm14ZTVhcmljdVRDR0xiZ1p0WXZpZ1QrK0U3eGx0RWgvWmdVb01QK2Q4dmFQVS9IZGhaYVVqc2dROE9vcVplZXp2TlIySkZtMm9uK0lsaVZ5WVEvNThMbVoyc3RnS29CYnM0U2xsd2lUcE5SdzdlY0wyV1I4YmJnMDVhVE4wMEM4YUdXdFJlV1NzWXNpckowSzBJOTdmbEkyZ0pSUk43MTd3RVNyeVdhaFhVQUZaQWR5RDA4ajlTSVpRbSt3cTVHa29Va0s1Y1Ezd2sxeDAxeDRmS0xQZ1BJajZENmxaaXlscXZXR3RsNkt4Q2ZvU1FYbE5aSUhlRHNySVJxaElOeGRyQ2luTTBpTU1rdmVOeGhxckV6aG5CbjhGNm5YVlk1elVETHpPWHBwMzM4STJIeWNGYTJwdWVPYkVvZjNIUWdGRU1uSFMzL0NES3dKQXlZbDNIeUE0WDV2WFVFOE1NYTc5Z1lFTHNlVGYwSUVVSlJzZlNhODczdmw2bjI5bEZxK0dDcUYxSSttQjVQU3lMRnZnSHY2aEc1SGQxNFBBSFRLaFkreHpDZ093d1JaeHlnUHdORVQwVWlPOXluSDBwM2o3R0FGRXMrVlNqbDRBcmhISmJ5U29oUkxmbTZCN0Z4eFlKTEp4SmxRcjVVZEQrNVZzMG5NNkNlaFNaWk5ZdzRGemNwWW9MNm5TK3dHR1NOS0xWTFhnYmd2ekFiVDRCMUo0R01TMTZJS01sbzVTL2R6TS9OTTROSSthMUZ1azRxd2Fld29IcUdwNzh2Z3ArU2t1aEx5QVZoSTJPcjUwSWQ0TGxId1JvbjlvN0pUM0QycGliY2hGdkZpMlZURXg2Y0xYL3FvclcyWUdTU21udTkrTTh0ZVc5RElSSDFUZmFidURJdUxrMTZORnoza05yNVFMUEdBZDBKek4ySVlGQTE0MHlxZmk5TGZCY1pJM2FVSy9HdDJiZk1NazhlcXR0TjhjOTJPbVVZS1VhSGJCOUM5Y3BFd2FPWXM0OU16dHVHdEkwVk1xRERITjhIaVJQNTVCcFJJSnRJV2JTeWkwL0xPQzk0WGh6cUdWeXV6YVZhQmZnMGYrK3NWOHd5N3l0eGxRWUE5dzFlakUwWGFDa3BNOXpiT3J5bWY0T3JFYUl5UVg4NFo5ZTZ3UTFjekl2T2loblNhcS9mY0Zka3hKY016RTJrV2NBUndXVDFVODBkVzZCK3Y2SGRjbFdNeU1XTFlyNDlpS1dyaG03bzF5dW1KS3hWR2l2MVJ4M1R3NjFqcmgrdnVOamlrcEZSeGEwRjlHN1pXczU3bnVoYUllVDhaUmpZenV5cTRXWkJFWHM0Q3lmdm1aeEdjUzQvRzJhV29uMk8vVWtqcXJmZGJCVUYweWF2U1BkTkphY2FhWnhGUU5lakdEUEs3U0NGODJYeGlhaGJOcHdGcy90MDdnYkNKa0RVdnZLanFhWXYxU05KQmEyMVJLc091R0pOS08vRjZIVGpjMVE1dDhscUxMNGU4M2dXVFQ0YXViWUd0RStENGU5emRQUG8yUjNkdkc3YkRyQ1Fvc3A2MlloVGFWM0Iva0VRR3F0enZ1NTlmYmdBNmxGeUdlN3VyaFlyM1RXQ0JGWUJtckVwQjc4ZlduWFVFZDF6MExTek1jV0w2dnVoNENKWVIwdGcxalg0SDB3a3c5bWtiTTA3TVhvcExKMlJ0Ny9hTDNIbDNNak84aC8xbHFObEs3NFFUYmdrdXJtZ2QyM1hmbEVjTWhqTzUyWS9Xc3orQ3F3a0JDRE44U1VjZDBodko2c3Jpa1VSZERLdzc1WlpNeW1zOE5kenZ6ZnNYcmVlQ3pwVmFQS2JrZ1dvMEJsRCtxV3FhWHppVmE3WVRTZXpOa0NEMVVCcGhNd0UzSUZ3RzMrT2phMEFJTGJ3UitWTWppcnJJa1JQdCtETXRwK09LTHBraUUxNUFWdjNqbjE5YnJaR1praGhBc3VUMnNUaVdTakx2eEprTUlDQUdkUVk2Q2NKMWJtUXN5Y3JYQ0N4b3hyTUU4QjVrN2FZUWtsMzFoNGttbnZtVUExVW81YkdFSmt6ZWJRTnVNZVZJUndLcjdzaE0zWTNpb3d6dU84Sm04MzNBTGhqZURiUjlpK2FqR2RpdjVudVFjQkRXMFBaMENCL0dIdm5tRTcwMmUzaUVtV0tpbi9TdG1rYmZ2c1ZoOW1YbmpMelpDUmZodDNnNUZ1Nk9wRFNzcTFEU1ZVaWU0aE5UaEdUU1RXa09oVEtiQVJ2NTRCeHAxbS9CcVcwQ2Z2ZlVKTVFZY2krSHpRQnJBdzdsSEpJOGtsTnpxMXdid3R4ZjB6elRGSXBZUWNzVTNkZERXRE11Y2lLbU4rQkhKNDdCNkZrZ1g0dVI1UVNXekxxZ04yd1FLMWFMcDJoZ01KR3FNSUk0ckxLNTZWY0RrODlRUWh3NmN5OFBDTTE5b2xOcHVEd2RyUUZ2UCs3N3dpeXlLeDhaNE1WSk54VjV2SldPd3ZGK2FEb3VaTVc1SE5ubzVkOTYwcWNQUE84OXFZbTZaaDZVTzdNeUZ4MjcyYVdZdHUvMCtVWjZlVGhPUDNzL3VNR1JhcnJZTkdWTjJia2wwVmJNN1pBclAyQW5DUUx1UG9JYmtyeTRuVFMvUnNJZEZtUGc5OHplWUk0UjBSWTQxRlFzQnltMU9YbkpjSHRtS1BqZkVYdXVqVlFHZkNQckNac2FUK3ZGYk1GV0l2VXk3T3hxdUl2ZGkyRFZwMytxM0UzTkdHMDZkL2N6Nzd3Z0hHV3JmY3k1TEpJekNNWkhrazZtMlFuWkNYWVZYd01zVmhKSTluSmNnRy9DclU1bGdEYi9EbFZFc1hHMDZCSEl1cVZmblR5TGRBUVpZbUpsRUVrNDNwZGdGNjlWMTJYQytzQjlXNVRmbTNqUHdpSG4vVm1Hc3prWXgrRXI0OUNMYnlrM2hEQlNLdXpEaituekNvNzdaTzQwRUlQNFpST2RTd1dsZjVTOHdmWWNBempOZGovYVo4dWtudzN0dXIxMjZSZkN6TUErY1VvNW1QYVpMOWNWcDMzWDBtUlRVSVMydmd0d0RSZ3NTU1g1eGNKVVdSOGdaYmRlcXlxUUVFQWVEdTMrQk1scmdZUDJTSC9sZTJ1MXlmVkZuNUpYOVZRMDRYOW1tQUJSL0tPZDNyQVlxUitPUXdMV2FvOU1YVlMxeSswT0tvMEZsWHVpckt1UGFZMUJRYlkzVm8wNUdmLytOK3U0ckRjRkJRcWlDclloZ1JBRWp2Vlc5ZU5DYU9zdWtjSldFYUR1by9wV0NZR0pMYWRtNHNzVENQdlZWRUpOQmZWWEFjVEl4SDRFRnRXRk1KVXk1b2Y1MFFOWE5aQmwrb1J1RklrZGJ0MDREZVU2ajJBM3Z6elArSWtNYWhMRDZ6QlZKdit4UkJJYzVmT0R2bkpNbUpSTUk4a2N5TUZxeHBlV1pBSHhDNjh0R0ZOeWw2eXlHTjk1U3dOWVh3RFNJUUNQbEw5YnpqWmFXTld2czVwdWlQMmxiRUJsRHc1dkNIdFZtYi9zRDhRQmdPaFJhc3NDaHdNNW81ZzRsaGxENHU4NndtZG1WbWhtRVhuQ3lMZVFKMHJSdHFZSVdSaGc3MmllRG5xbVB2T2tEVFd0S1IzOFRlSndySy83SVJZZmJOc3B5Z3JVNnlWOVl0Snl3M0kzdUVrRGdiUHJwY05VcElTWXZ6djNiZUZnM1pOK3N3ZWRxZjNJVktrY2RpQWV6dS9LcEhHSFB5dlg5b1Q2cXpUUzM0Mi9EZW5XOWN0TTE5N1VmRmw0cmsyMUt4U21hMUtuTElXbEdHYXNNRjQrRzNkeFRucUJzY3VsNENxTmRhNlF5OGl0YTdIQ3pLbFlhODZ5bGptK0hRQTJCNUFySm9aeTRMTnhlVDlpekZ1UWhFb0VoVVROSlFqMnBDYy9PNDRoOEdwUVg2WGdwYUF2QVFKTFZOcTB5WEdGYnpiM081NFhRNnNtNTU3K2xUM0ErVldQeUNKbjFNTGJzc3NISWRGaEpjTXRCRlFZaTBiUytleFE0UnE3NHhORTJDSVJTemkzbmo1VE55MkFvTzBnZHlCQzAvMmlINjdVQjU4MWptTTkyT0hxZ0Q0RXpBenl4RGF1UG5sSWRadTBuV3dCNGR0eFdOK21lcS9mYUl1UXBLMmhvUlAvVUx3SUo5cjN4eXh0WHhmRndKM1lxdVhsZFNFbnhvUGlZRDg1dTBPQUh2S09HNiszZUJyYVVpT2d2ZGZwMUVqaXJvZVNMTEZ1dHVQUFY5WHFoQVJlWVBhUnk4N09Ba1Y1dHpTcXZ5ZnVmQ3ZPTVR0a3B4QXBXc0o5bitjTk0ydUJXdTRsajFvRGpHYXNDZkN0NmNmZ0N6aDZVYlphbmJML3FDZ2YvaUhqS1lhYXZJaVJMSnJVMkJ1emRzUDk3WEhrWExZYmJmc0hWVGxYU29oS09YT0orM0xpUjZpeDlVRkxvOXFpZWVqWWsrUDRlNXdDNjRqR1FMU3hKell0M2NFcngxUnRjMit4bEphRUJ5bkxONGhMbC9xT3JnQk03YSt5c3dDME1oMk9pZUE0U1I2TWZNOVdLL0ZPV2JWeW9VQklVQUtPaGhJWnAyTE9ndWtrMC9ESW5uN3NGN2RSUDZOdzc3TWFBY1lnNmswZ2RqUU45LzF3dEdWU0JtKzZMd2tJK3hmY0s5bCtKaVdlcFh1bCsvRUVkVjdYWHAvOWxVc1c0UlFtSWtkYTlIMzhGSmozRVlKVHJHNGhFVTlZV3ROZDJsS0kxNjgzY1hGVnpTTWtoKzJudXU5SzBKVUJvQW5yWWtLVlpwQUtGOUc3eTVuL0tNWnJQMnhQdVVGU09hcnVxcmlmZlNFWDlFdWovazVkZ2V3RXlRQ0ZUaWY4M0xoa0lqdDVxSjFMeUk0eW5Jem5XbDFTb0FkZWNFcCtJNVdtS0JCMmZyNXl3MzNOWDk0cTZISVAwalczTnAyRTByMWY3ZlVqcWR4VitpQ1JVTFUreUF3UFhGdlRMN0hxZkZMait3Q2ZJYk9nK25zVzAzckdUZjFoYUx2QVpBL25DNTJwU0RuQzRmMHFPaUE2V3RLMjBCbGRaVWFBNkdPM201Wk9DR3llbUdLNGExMmhNM0JYbmJsYWRBL3lUUlYrcEg3SWlULzlXT2lqR0dOWHpWK0s0d21kbVJqVTNJdCtRd1VDUmF0Mm1Ha0VIaE9jUVkwNnBXZVFxQkdqSGtXY2NlWDgvZHJrayt0WXlzSE1YVms4aExoTEdqVVZnaXZLMVJhNEsrUnRVY1pPNWZrVmtXUTRXOGZ5bzJ0YWZoR0VEU3NmbFVIN3lqOHdzQVRCRTlZcHNrUityN0FjOHhxZHh0RUFmUmlvR1hTcHJqYkxJMkRBWlp6OUhBWVI3clVIenZoL1VQcEZ2ckxiZC9oRmY3c0YzUmltV05waUdzUVJaMTFScWZaa2NrOUlKdS9GUFUyRFlyL0hXVWRza0pIdUx1ZlhDdkRiS24wRjlzTTMxSG4zekl1QU1UVWMrdFFzTzlsbDZqbk5uVzlVbG83ZDMyakVRTXFKSXJXUUw1K1NlMGE4bEtScCtYaFlwNElmeVVhVFJDNTh2RkVqS3VwZUZFcFU0RU9wMUFqZUFMYzd2WlYwb3Z6YThRU2wzcnU2eEZwWTAvY2tFbE1PQ2hraExXU0RITENLYUZLL3FDL1NJZlQ1MEdKWm5rQ3I1U2dYWlJkZFhxOEdjNlhOakl6U2RDRis5WWxVRktNaXJpL3NuMUdwL2RFTWhBUmFoOTdHaWRMcWl0TE5CbEYrSDhYb1FtZHJNM0dYQlNDTjZpek5uMk9OME96cEN4T3VNOTE3T1pDdzJaQzBEU3ZOdVRPRkNHR1lmMVRZZ1ViZ0syS0tjNHptLzI1ZHozR2hWcEZxczZ4NHloWkJiaXkvNkZEMXZYVy9hSWNEaVNVb0lod3JVdHh1R0daaWpiNDdKejhKZlVUYmx6eDRlTlBiWGVZcHlna1FvMXhYb25qZW91VHVKdkFIL3pIK0ZLNTB6T0xBdGJOOUFPNnhqZlgwOUNzaktpdE1WbEhXbW1ReWJMb0JIQlBrQzVJYkFaeHZzM2NIMVZBY3kyWDkwV0w2eS8wU1hOc0dlTEJkcjFPV1Z1WWcrL3dVTmlSN1FuUDJlYzdqTnJaWk9vc1Q2T2x3bjAyRGg2elN3S29Ebk1GTGZrN2xCTzBwOW1XamV4N2dFRlhOZnhGTzE5cW1hb0lTVVpFZ2RUdXk3c0hnckQvMzZvM1hlRmR6TEZvRm5PSmE0eWFFTkJYZFRTbVZaYWN6KzVJR2RWa0VnalF0L1R4dWhOR0hHdFF1ek5EZk00aU5aMjhMeTlTOVdrVUdNTkFmRFJMcjRpcFprSnhVQTZIbmxPaTRZYjA0L1plOHJCK0hFWHBER0M1SnByNGZONjJMUWg4bzZreGtuRTFQNS9yTm16NDNqZWhGbFJVdkN5TmkzWTVTdDdsQzdhMm9nQ3QzWmE2TTdBc2hRZGJWVjIrUjJEdXVpTEVKejBNTGhubi8xL0YyWjJVM2g1NjBQcm5oUjBHYy81R1c1RHdPL0RHclIvNFB2TDA0NkJLalVwMWxmcnRLZkU0b3NSVFM5L29CMEdyTlczY1lndmhVOGxkNjFzSGhLT2Y0UDk0dDRuN2g5emRSWERhRnY0T1JQSG9ra1krTkE5UUE0OVJtc0dNZkpMdTEvUlh1bHVxMEo0ZnNVVUJvYTlkTDlUMHlESlh2R3R1b2xuOGFZck56b2FwYTdFOGNSNzMvd1g2S3dCUHB3Q1VVbHhzQnRPajBybmNhN3p1NUZxSkM1VzBVOFl0NTI5U0FJMFM2bm1XblM4emd1UUxSemYvZ1JMYXFTUTZFOVQ2UTg0dTFjczU2ZHpCTXYyZUJHK3pBS3cyVjB4MU5KWDFnQzhNMk1ZWnBTY2RYRUtQRzE0NDJVRldURVVsa005T2piUjRGdXJ0Sk5WNElxRXUxaHRsZ2x0RVNPMFNlWk1IWjFKTTdiTnRZZWdldndQU0NtVytTOHVFR2o3RlRTU1YwSGJEZzFyT250NFdzOER4cU4yVC9IT1hOZDVOR2JvWjhWVFNENmc2ckxXY29XT3dzeWVHMDhHUEc2S0hQaUxSdW5FZFRQTm1ZNzRPYlJHVDFWQ0hQN25tQlltam5IK2txSzZyRHlyRW9OamRxYzh1Rzh5WnJIV0JYVTl3ZXFENXJwUTZTL2FubnE3UC9HaVllcEEyWkRkSkEvR2JkeHBIWWF0UGdrWHQ1c29wNTY0Z1ZIWmFtVzZjcS9jZEFEYUxDWFd0MVdnSzd5MTFXYVFSOTBZT2VuOEJFQ1E1NnBtSmJMdnp6ZldCaFVVSlArZEFFRUs0bzR3WnYyK0lCQUZFZE5rTkYzbUtudHNMRTVQRExBL0lFaVYwcnppeU9SekxKc294Uk1DUVYvSGxwQ2tYc2FpemNIVC92eFU5aWFkZjJoT2tLZWhHdW0zOTczZkZzN3VSbHF4ei9vRGVyRkwwNjE3UHFHK1ZZSXhqZVJiMklSTFpKR0g4dnA4SVR6RjdVN0hVZzhDcnMzV3BWWTVyOHd4bjh0ekd2VVV3WTVjc1Z1MTVWbW0xeGNzMFVML2xVQ2tyT1hkTHRsYWE0cEhMZVFncGQvdnUxWnpqTU9jZ3pmUWFJd2laSytmTVpqUkxBSFVmODNUU0NPa292YjN4UGtEMGpFbG1iNFRCcUZyd244RzRLV3IrUk01OHFoQ25sVmltUTM5MG04WUx6K2ZOSGJCUkRzN0dKZ0hTSyt2NVo5Y3dacTRnbG5SMmVUam5xVHk4V283QkVnMjRDTC9SVDFBS3pPSUU3bXVvOG9lZ3puOFI2cWFiMDhMelRjYmIwaXBwc1NjZmpRb0poc3I0aktHMnBNVmN6cENZcXB0WmNHRDVyeFRIRmJMMytORG5FVXB0Uk15QVJoRjJGTWlNN3BnYUIvSXBBbmExQUhhNUVQdDdvQmR6TUdnN2tPZFNPcHhyUFhiZFAzbC8rUUNmQ0xNcENzeEZkM1ZBeEEvSVBWdks4SmFlbkNZQ2FkaHlaNnJKZUd4VFVoMTErT09BanJYSUp4Yi9FYkl5OHJ2Nmg3aHl3UHA5WmhQQ2NndDlCTjgwOEpoR0lhS3d0TDg1ak81bmlwUXlBRjY5MHhKOUEyRE11Q3g1NVRTRzg4Zk42cnFCTVlESStJK0R0Rm1vQXFKQjI3Qi94eE45eE1MblF3TGNMQ0hPeDRHSUZDcTMvNmk3Z3dKZVBqb0cvSEtOYjBYamh1RVFtWUZ6VGd0dC91SW8xYkJYNEMreTFqcmIrUjBtUmorUnlhRGtSdXM4VzRXVzczcWJjanBqSWgydEdVWTZLSnloRWFLaUsrTEhHNWV1UWVZWk80elhvS2JaT1dpSlR2Sk5OVnJXdWdwWGtJSUlFNHpLL2c0SktBVFFqdGFDMXFiSjZraGFKSHhPVFMyZ29VNXpHeWptYVBLdlZQckJoMjdFN0UyaVovNm9td3BCQVJWLzlFS2VVMW00TXN6OFE3eTNNekVGMEM4VklJcUF4QitGazhxRzk3MGxoVi9aSVg2Q3N4aUhxeWJlbXFpbDNRdi9jV0ttOTZmUG9NSldTQTFkY0YwM2RTd1N5Tk1kdktLQkNZVllMdXFyMnBJU0tQYU5SSkp3MlI0M1JORTZhdmgvVE5BMXRHSi9pbFcvZTRMYk92SWg3Y1MyT3NianlYY0Q2V1MwRFlhRGErb2cwbFN4ZWhaUWlEU3QyZlZkdEYrRE83L2NFVUFNM3VqdTQ3RmwxN3JVUGtSUGFoZUErNi9qcFNZSzVOaDZyU3dPOFBiaTF5NC9MMEw1U1N0dmEwTmNzY3BIMHB3LzNZOStFcXcxU0RWdlJuMnIyZDh2UkM2WWhReXdkaEtXcmFLR0JNSUxxamlVMmw1ZDNqYjF0blFJd2k5NVFpVEpXN01BakpENFBscjlGR1JHbE00TlF5QWlHOHdTQUtVYlJDcG14RSt6azlZaFhqaUMvUmJ0OTgzcFYwVnpvdkpXKzkwZEg2NUlPYjJWUytXaytNcHNSZ1o4NnVFdXhlR1B5QisrMDdIbEF3cUZqcTBzbTVMdm9tL3JjSFNhTGR1SnJEZGFidWpZSlJXYmJZMlFacHR2R3dUSEFpYXFzQWFmRTlOUWEyb3E2aFY4K0UyWVJiZEVjcmlyeHl4OUpWV3B0aTdDc0ZmQS9lZ01ldkgwTVI0MC9YMWpRek1ZYnc2bXIwMU1JODMzUmlFM0V1VTc5Y3BzcEM4dHVONlF4RkI3RXhIRjh5ckZRNHZSbmlFa1RnS2M4a1QydEMySGdOSkorbC9Gd1lYa3k2cWJIajFjTXRCR1ZPdzNTRk1IbjVsNW9kWVZyTHFoTDZSNER1aktxL0NFc0VqNzQyUWpVb2d2clNiOURPaDFNbTVaN242TUkrWUhpaTNiV3AyYWJpMjVGSklpWDNHTS8xMzdNUVZyNHd3UTVJUUVUbll4MENvWFgxbkxlcUxqUTJWbE91bGh5NThpVnhONWQwUTJURVY2TVByK3dBNmxsdUdFQzU4OTBkYjQyZWxEVXZUYmJNY2pIR3JUN1dBNGVFaE5McVZUMzVOaExydVNQa3dnMVVDQVV6OTREajIzaTZkcVMxTVBoNDBPeWkwVyt3Zm9XWVhJdytzaXdlVTNxS2RRTS9JV0xVd0RqZ01RdWlLK0NUeVJnUi9DZytYbWZhekNMaUYxSkNoSzdDMngrUk9DbDR0MldqWW5nR1J4QldSUXFxck5xeDFFZXNMeDhaOEdPaW1CSkszSXAzTzBUV3AxejZmaGliVUJ2Q3RCcENCSDdXejBNcnNZRXRXLzZnZC9yTGJCMkljTXhPcnhnVzV1Ky9aQk9qZCs5Wmc5U1JmN2xuNXRxWGdNN3daRTJyajR1N0JPZXpXdnV5Y2EyVHBKa1FPUjhVL2JSK0xSam1ONlJBUzdNQ2ZZU1B0SldTYlpZblFMOHZHbUpiMzlTeWlZaUVSMlZpYTFubFNoakpFZTNKZ0N3VE9UaUlRSjVoK05RZUVzN3FXa3BJREppUUhiN1Z3Y1I3VDFnTEdoS0FxVVQ1RFBPNXp2R1BueS9ET2grTG8rWGh4ZjV3VGtGNXA1eVkwdk0xZ3cyVVpRMm5oQ2VkUStQQnhBQ2FBZXVCWVR5QnM5YU5XdllBVFBCTFV0WEozSC8rck1JVVEzWHo1TUpLZFY2T2hMRUVLNzNyYjloZmpQbEEwZ0tPNGoxMjBVNlZIaDRBSnZMM1dxamFZL0tDYndwQ3pVQ0FEWm1uSmRwRDRwNFU1cnk2L1l1aGNXWGNWVjRkRm01SjhxQURCV3c5alBJVGpVdGtmMGxoSUpremhYTFRjWFFCWmFhdW52Q0N4eVdoNmlmWXpOVFRDR0pjVUQ2RHlmR2FtMnpqNHFkQnk3RHdCYUwyUzJJeGljRjdGMnViUER2eDArREVRVnlkQUlGNFV0bisvbml5eERRcEdsYWFHNWVSUWNmWUVIYVplSEJPZlo4eDZLblNzWm5COFlaYkxWQmNFRjNNdi84N2NqNHIvQllEWUFhVVdycm0vcldQSW1TVnB2UGxCM3hRdlZHMzA1QitiQ2o0a0lXNFpXekZuWDcvbkFwRGliUFp4bmNBVjA0bGFEc0Q4NzJnNTR6NTVEWnlsa1VLSFhGN1k1aUZ3c2MwSERvdllwSjFQK1hJQWI0cEtabncvZTJCclRabjZqQ2VBQXZBdDZaOEVkWHFTL0tvUndLMzd4aFpMN3cxN24yUFlwcW5vQ3RSQXZuVS9Db2NVcStlbCtQRkV3TTJHa2hMQkFKWHZWYnF4Qk1mUFdsQThYTU5ZMStkZnNWOVV5MEMrV2dTemNYdy95bE4yM0RsRUxLOURQWjFuekZDdnlEV3lnaDFBQnYwTFhodVZ1REVyYVlPclgwSi9OcGJZb3hqbC9tZm5jWE4xRG9yZnVtTWpPby9kV0VrL092ZFo4dy82NkN0SVNwR00yaHRHUnBUOTI5cUV6K2tSTSsyWHBBcWNTUzlHT3JMV1ZWVVZJbTNFei95SXFBV20wMTlUZC95dGJFNmVlWUphWSttSnBlbGNwMGgrNFkxaG1jRjlKNmNaUUVKaTdmb1k4bjFwc1ZUQ3pFMFFZTVgrU2NZeEt4Yi9iVTllcHJvVWFTTlR4SGVOaG9tdGJhNHkvQ2ZMQVpZWG5kbjVuZGVJakZJc1JXUnB3WDNId3JJc0t4UmdkNTJ0UnMvaXVuNXV5NDR3OHUyd1pnYXlpUGJPVFdHWFVuL0JEcWFrNUVaZWJYYmRRSHlFMHlFaFVPNUhjRG5FNnhsQXVaRkRTS0xEVFRaejliV2NmZTF3eThLaFNPd2gxNWNCUmlidCtmYVVRZ2w3LzVuYTZObDVkMW83aVVXVGpPaGpRYTR6MlBoYTFQTkdTbjBoWkZlSUNNS0d0SEo2RUdRYkIrSEY2K00yZThZU1FqSjJjbkcyU1ZwZHpYbG5renhZcXdYdjBzMFdNOG5nZ1NoN1ZpcTVqb1hOaUYzUkowQTk2MzdwMUhGSmQySTdHclE0WlRPV1JpOGpjWmFMLzI1UG94OWZlTVQ3VkRQVjZUVCsrMFJpM2ExYUxTOElBQlpoMmRXZnhuQm1YRFdQZHZyeG1CaUYzZWVQVnFkMlpNNWJJOVlBTjIzLzNxVkxFbERlRDYxeHZnUmRqa1hrbDJ0cWlmM3pzWDFnR3A5bXpFbTZzdWgxa1dMNzVYQzJrWGxyQ3JlaU5pMnBmSStpV1ZGSkRYUGQzTUJOcDdWU0FaUnAxanB0M3VnMXBRRU00NzBsWlh3b3RwRGxqa2x2R3h1TmVLd1R1S05KdzBFSzc0bmMwZDg1MVFYTDlQNHB4WmRNN3BrbWJBN0lVMlMyWGEvQUpSUDJWT3ozS3lwOW9XNkZnb1FpNG5vTmtvSGVObnByYlFvZDhuK2RRU1NiTXpOUlpJdUwvcmlIYXhvT0hrYUdZd1JPQ1p3cWNiSzF0VW5VMlF0MUorM1VUdmtsajZ3T0QvZDhsclpHN3VjalppQ3lIeEs1WFZ0enE5bERKNE4xRnZBUkNUVWZuTGVPTGM1Ym1ydEd2YjhtbXNyMGxERHlSNTYwN2s0MXd6Z2xaSDFmRXhmbXNYckVqaU5MU3pTS0diN0ZWdXNsMDcvQmdlQ2NsRHNRa2RzMkc2NTRHVmVVcFg3VUhhcVFCRW1Kc0l5dmZ4dno4NStXeVJhb1l1UWZTSDlXcEpMZVVvWHBVdDcrQ3JubDFKcXorZUFSeUNtekw1OU9VVUJ3QnVvUUFsNVZkZElyZkc2eHZEQS9SWkJPVjVBZndqT3JKMnhSbzRONDJyQ1NGQ2NuT1k3eGZld2w2dFZMZXRpTTJ0R0xxUkxjOWsvb3d5SHJpWDFBOUJubHV6ZkRjNXhkRVVLeXV3eldQRyt0WkdORFYwV0xsMUp5SFBmbHpjQnBqOTJHMEFSMGxHYU1TWnVLdWk1L0xVTW42OVg5d1BLYzZGVmtORUhFakhqUUtQUWp1RkNva2pOK04vNkRsTXNjcEU0OEloSElhMEdocmMzNkd3R0VpUFJ5bVhXS0QvZGk5MnlmalpqRE0zZmRIQmR3U3hKUlNCVktIU3doNkV5MS96V1pSWjRraytLTVM4SHVyb0l3MVVQYStQRFZwc1NJS3ZtcVpuWmlzYmZIRldOVy9kbDluNSt3TTRWSXpobXJFVHozazlXVTNzK3o4NFNIaDJmN2RHVC9HNVd2b2lzQllBZ3dtK3BxRlMwQTh4eWh5NFBpS2ZnUys2VGduUUQ1aERFZXJwemdGU2FNY3czeXZEWjAreGZMMHl6bmYwdVk4TjZBUGlxSGRvSlpPV3FUUG5USWJlQkxjNWR2RmRoK212RCtzRHRsOEJBV3pZUjdRa1NnbngzMFJ1N1RINWEvZzRieWFjdXJDTnZHMGxUZ3Brajl3NDJ1cUJwMXpNc0tyMnJpT0NRd2ZDUktrdVNYOUNHQURPWUdxQ0hoMUpVc2s2Und2STlPdk05ZkNKb0w3U2FwOE5VUTdtQXZkQjJvdWdBMDFOZHF4Vm84TmVHdGEwUjlDN1F5YmlONHVBdER4dzJ6TFRHOSswd2U2OEprcVpyajl0SmlsVVYvZjR3T0xjODNHZnN0WE9WRjJiQUo2emY1Nll3b3JRUUVEajZRbkMrbHF5TWtHQXIwUXVBaWttMGpxUzdmeTliWVNCejVoZWtQSUxjOTRiOGFVYXUzS3Q2OVFJMWtGRW1jYjE5YUZRQTRiU2VnQTkvaEZpNjFSRElWUTdpT0JxVmlZZEdhSzhkM3pINXFXSWplZDBoUjllNm80ekVMZFhXaE9WT2NQQ21aSVlZWHZnVXNBeUdVb0NzenNDaVRkd09hUEVMMmtSblloMG1OU1pHYjYva3I4WGZieVVkYkVaN21EQll5MHlURHhoa3JwSW9KbVZ1dE42RkhrL0U0Y1RFb2xhR252N3grUXhRSUtadXM4SUV5Z3BkdEJEeGorbEM1TTZIYUozMTNwTERZYmpwQ0Erb1lsMTFJU1JKL2ZCMm9JZERCSEZMZWZRbUYxdUhrN3Z0U21JeUk3UTlIRzBxeHU4UVJXZWNQOGlwS1IxbzRiR3JBaFIyS2NHRURFNms4cjJGN045bE5VWkNzd1hpL0VYYU9sUGI5ZmRzYXcxU3Nwa3UxeHJteUFESUltRXMvL1hpUHFJM0psOEJscnNIZjFtQVZDQm1scUU3dXNNYkRFcGlsdDQ1aWE1Q1h6VnFsSVo5NUZlc3U0OExFQVRTM2R5WFZFandRQXFWYkZCdHRiTGZYdlg0TGhhR0t2NlAzWEJzS1d2cUVGZnExclBZZG9oSHRRSDAzZWhsVk1wWi9CUkNCRlY2ZGZmR0NySWE3T25nUkFiT1JkNndzSWNSL2dRU3hoZnJmSEZtYjlXczNQay9TaWt3SXZBSVlsak5iWGJ2SXBLVFJPU2lQY21CRHA0aHhMa3JqUitNZkJGWkxWNUk0dXNMWTZXWW1qaFQya3pXOVhBeHhMWUNFTExJZjZsZzZwL0dGZ3BvUlRtK3lRNlBZdG1LVnZkVEh5Qnh2Mjh5M3ZUaXkrcmVZQlpxbUM3eDBURGFzaU1DY0ErVHhkS2dEWTRzNjFNcFp5STErUlV6ZU1meDFxaDlNQlhnMXRJL0hTS3BjVWo3K3FUcndwMzVKM2V6ZWZvNlVaaUVXTVBCdHgwL3RKeWFlajdOVW1VSFZSQkpmQjFxMGJzdzR5SGZ1aTJaT1BOaC82UjIvSTBqMDl0OVFHZVJ4cHVKekI2RE5iYVBUT21FUjZXVFhZRUdYcTdEaHprdkNQMjQ3dVN6NnI3TWZhYXNEczQxOWZWRjRSQXQ0WG94a0ZSbWszc2pyaHBOU2V1RG9HNVJwakU0cEkzckgvRVNQYUY2UklJSkJpQWJWVS9jdC9uS3JEbUJRUEJZbE5vYjBXbVcwN0doT3Z2ejBtL0JYVHNQQjhxQThJZXNtNlBzRHVPTEVFbTUramJuaURGeVhmbmR3SVhIZ1dCQjFHQ3lHVjUyTVUrNWlYZ3VuY1FTOFQrV3l4YVBEcUNDWE1qd1BKeEdPYmRGOG1Ca0cyK1NwcWFCUWtlTisxSUw4Q2JiNzJkM3lTUVVSL3VPK045djM2S0FpS1ZFUHg4RUVSVTB2ZktpNTNKV041MCtMU1lxZ0htRjBVcm5uSENOcGN3Zlg4ZXpva0dMNHNLL3JnRlpsWG5JcWc2YThFSmg3RGZNT3dNZ1R3UmpqWitUclhzajdTQTZFYU1Scm9GZ3hYUklPR0RQWVpna2FkbGxyQ29zZnVWWnFOUXdBWTFjREp6dUQ0b2NSN1BnWllYYkNBM2c5SmQxUFJ4N1B5UlROYWQ1NnFGTVZJdi85QVlZZDMyb3BML0tRT3VFYTJMSW95TVVIV3NIVmVKRWdEblRBaXprZGZpZ0tTbVpWVURyenRvR1hBK0IrOUIrTVlUMnE1QkVUWEpVS1JMaUV3M3VwVHBYbmxoN2hrRWs4LzBEM3JWMWxVeHhTbG5EekxmRkFyeGRuWFJoQk51MDg1UnhpVHdUSVNqSXRHUHVqME1Ra25CZkxUaTlBZUxUVDlRVUtSRzdieEhtN1AyS2VpNmZWQWVOQlAzMXEvT1ZzVHVCSlpmS2F4TG9kc0N4T2J4RmR5Sk5MVjJ0QXQrMlNDQU81L1ZXY0RPZDdPcjB3emJWR3diWEpyNzMrL1BZbjNWZk5RNENTeGRxZ1hOUFdEcWg5WkZWUlFiU2ViK2JGbU9wZGtPN0M3MHk2ZFRTSFZ1SGxJWTMzL0tWMVFIREoyMjZhdEc0bHRTNGZrMFpORHJtUFoyTHBzNnF5TVlPK1drbXN5dy9FQ3V4ZlhjWjB6TTd2bUxqa2svTHNYL1hHMHZhTDNLWmIyQzUxSTVUVmY4ZkJKbU14SEh6S3ZhWER3U1RHaXlhMGY4Wlozb2xxYnFjZDJjalhNMGppY1hsWDBjSnNhQjgxUE95dUl0d0VpWVp3c0huNGd5bXJubEQwbWZBcm8yWW9TQzdLeERkTDFEUVZPKzBhN2ZOMWZMa3Y4RWxhWHg0Nlo4RUdKL1c2YWtJcjZ1RXVpRklRQjlmSHVqZ056SXpBZ2FERVlWSVRKSk81WFFreWltZGdhVEJ2cmExaFVidzRqYjhpbXFWcGQ3RzlkU29RVk5QYXRxQmxibTdOTHNkSS9laW5mcHc2SGRGbG85YnBMYi93QnhmMkJHSy9ZV2huNkxoekV2QnVSdUJaSlREdjdIVjlXZm5BMlN5VDNIVi9GNmYrMjNhT1lDOHJ4TzdRUTFGSTQvMG0vT0FIZEN3WWVkeng2RjZUSWxTaDY2OEIrSWQzWnhOUDNWK1o4MlR0L0FIWVN6RHN4eVlDOG14eWsrWmE0UTZ1OHk3MEFLcFVtMU5QUDJXTWVTSGZxQ2M1bVVjRzY3UlIrc0pXWmc3UDVpRzRGUG5GbVdLdjFud3drK2ZNMElJQTVwN3htSG5qMXpiajg5c04waGM4MXR6STZlbkJqSXlQZDZQNUdYenNtcDlJUkhLUzUwNlNBRUs3SXhmalFMeGtOSzF4K004WUFZTHJEMXFXWHFvMDNrVHZYZ1lsbG10Ymd1WlgxRlFHcFhZamJaemdxU0x4Y1hUS3FRL0doWXFCSnpadHZQYVlHT0RCVG96dDBSdzYvdlAraFRVSkdPQVljRVdXcjVNcXk0NzkybExXbUVsa2YyazJIaUY1MjY4RFNrRUwyb1FsK1ZYbDJOWGdiZmE4eHhRb0k3bHB1TmtVUmNBL3BOei9nbzNMRCt3NDFxNGVReTIwZWNqQ3dla3IwWGZPRHVtcDBYUFVtMnZ2TmZrNFAvdEFWQTJQTGhsMjF6b0ZPclNLamQ2RDFBaU10ei9mNDF1V2xCV0NERFk0dERSTWh5R3NsczRHVzdQOGIwL2RHeDZWVGdDNm9DQ1d4TXlKeU9nbDVSUGFGREUvRXpHR0dMOVhVbTVYOUwzY3JuMER2RUVMbS9WeDZId2xHV3RuZlpLN2RBOC96SmtyOWI3UEJnTGVGbG1YeWZVQnhaSEY4a3hnVzV0Y3h2a0V6MHJvUzcwak5MdmszUU5DVFVJd0NIbnFrNU5SREVhZXdEQ3pqVFI1bEt6Tnp4MVJISEpOaVpaSjBsWHJBc1NNMDNpS1B5WU5kSmZNd1VBdlJsS1A0OXlJeDdYUzljdnNlQldWdkdOQWMySTBQbVI2WGM5S2pxYXVxamdHL1E4aTE2T0lQdFEyTGwzcURrdW5UTnEyTzY1QUVGRzVxeWNIYUIyLzE1OU40bjY3aU1FcHlOb3dOZGtxL1psRHhzWDRkUktOdkJVSmFZcWhJRDcwcWEyUmdxOCtBenFUYUpodVlycXJERE8xbi8wcldnZ3JCY0ZzWXdvN3VqSlpibEtHYW1GZiszQjVNVEFYTlVPS241UFc5MUd4NTZndHFUcXoxZFlNTUwxZEZSL0taVVpvbTdXa3k3djlFZktuWWJCc2VBdkR1QkZCRkZDdVhuaHZXYy9KUzRpcFVJZTU5THMva0wrVzVsdGVvMXh0NWJrSllmdWcxN3ZHdzZjcXJPalRHNG5RWForUmJFRENNVGY1Slo0REJjdVZ2K3RHUHl1Y2MzQjZSOU5NRi9sYzR1YnVscnFjQlBoUlVqR0JJTGJRKzR1Qko5ZVVITUFqMmlqZk1za1JNTGNWNUZkZ3FJV2hpRXZ4TlZsWlNScnpUenlTZkJValpIQ0pRdGJnRFo4blJXTHdrNnJRS1dENWFTSHVKaDB2Qmd2bE5UUCthNFA3cDU5bDBGWUJQdG9OcGlGbC9kT28wNUtIZXNRQ3VlVHhqN0lCNmlvOXNxVFd4VHUyUEsyQzNBQ2lYV055eHM1MjQ0MWh4ZzNlY284N3BTUlYxTlV2UWVhYzM1bzN0Z1VwWHRtdGwyeUhoM1FPMW1RNTV3U3FJcmkzUHRWeEo1N2wwbk91eWF2LzBpeHpMRXEzUWxMWm1MYjhZMkpWbHJkUU1qaHBjQzFqMERTK1ZIcllJQjRKZ3lYYWNWdTlQQ1JvQzVZMitwOHFmZUpBM09GcmVhYWJ4V3h6NW9teW4vbDU1K3VmUWtPNWU5aU9EQ2RMV2wyY3J3THJVcGFNQ2k4RVVjVlhHYjNaOG9CQ1Vkd3V1b2huMXNpdndRcDFPK0RhUkZZWEliSFFpYmRQZnE0ZFU4V2VpWUo0V0tNbE5FdVFyL0JSSUd3T3JBSU0zUHBqbXp2aDI3THl4NnhLMTRzVUhnTnkyZ2dORzU3Q0JiWHpuRlAvME5WclVRZWY1bU1kc28zQUozM1NKeElucVllYnpjWjJwRVZZSFljelhFLyttY3B0QkhiNEFOdEdvaHdRYWJMMXhtRkhhdi93RkgvYWw4VEtqekduWWlGTEVpZkpITDdPSkQweC9ydHpXdUNyRFRvRVdQQk50UktYRlpxei9rQkg2Z3N4enkvVFV6UDZSK0MvQTQ1NkZiR204c29LL3VZeWFmZ05tWDByZTZmZ1hlZWhVdnREQ1hkQVVKRWxKdDdBTXYrVk1kSXJyT0s3VEFhSG82RThLaHgxcnE0OHlPcU1xdEMwOHNvOWNRaC9BVjc2MENpRXRTbTZQQkw3SktDWkJWNG03dDhHYmJjNFRRUmF3cHV3VEZ5Uy92dDFKQm5BUVVCRFBkRWRkbEpsVkFmYkd5K09La29oT3c5QkIvSlk5ckRaUUsxby9rcGZsODJ1bUhpalVuajBnVnFoSkNzcnpVeFlsK3lna1JQREVQWnFVSW8vK0F0c0dwbG1CU3hMOGJVRTFpQmM4bEN0U2hGMmlxTUMxRGRISUgxRGN1Y2JTTnR4T0Y5TFk0SU1uZzRUOWVUWXpEcitnbk9QVnhXQllNYW1iSlVleFR6eHl2Rk9uZUZnM3I0RkJFSHFHM1FaUmduS0lTWVVRS3Y5QjIzQTh2aEZSZTh1TlpwQnRpTXRYcU9RbFZFYk8vSHprUmJxVmFHajRzMlhSVmxoTytld2t2RWFUcDRwTkxYRzFPVkY2bmN4ZjNGcTk0S21HdUcyOUxMc0ZJMWZ1WDM1SjBUc1JOR28rVENpb3lUclhMVkVqUHp0TlZRTDEvcTV0R1NyTVBoZkpFYVF4SGNybnFoVlZxTjFnZkYrSks5UGdjdWQvbEdhK0lnN2VLUXBKdVVOK1BZaEJZUS9iNmFoaTRuTE5lNStkOHJRbGZLL2dsM09RM1dER1d1VU1PdDFZbEJLb1grOTlKV2xacjZ0VEFWZ0RGME5TSHM1ZnFiVTBldU83Y1hLbnZWQjN0YUJGSFA2L0tLWkNCZkdxek5vNkRnWmdpQUVMaDFFWU9uaTY0ZG1PV1V1d0FRQ0t1K0w4dG5URkxsTDZ1S2thTnRPOFlHbE9CVlU5bVFGWXg0YUdQZ0dFSS9IVHljeFlYQkNsZktibVNFcnRjc3VoYWxPaDczRm56UnovdGhQanZSSmNSd1B0Wm1DSHMxbllqaXZMTVdXR3BybDRmUlVPbHJDRGl3TlUrOVRadWFWc3VDeGovNER6S2ZjbGExMzlpZ0g3WiswdXNrV2tFcS9jMG1yc1JMbFZwbDhsbjBHNzdod0s5ckxLYytSTGVJNktMS3kzVW01QzZPZjNxaUtOb1kvN2FkM0VGdmRQNFZJQ3N1VE1UaWkvYmVlOWVmbUtBaXltMEErbDNoUzdTb2Z1RUo0NkluN0JFTytLZjU5N3duZDZzNW1MMWQ1ek5SQmRPRW1mTkt5UGRVdUNXM3UvU2ZGUWVzN25ZbGZWL0IxRE9FOXAvcG1nSytieCtlWmRaVU11NDR1QkdsYVB2ZWo1d3hVOWF1bWl5dC91Q0NaNFB5TzBPWWZGQU1NcVRhWWNJOEd4WWVITy8zdERKc0ppc0xsZUxwUy9ndlBMYkVrc0ltM1I0T0NKMjFTNFAvL3V5elE0RUpaeVltV1pqdGtuS0piejB2RkVpMHpEV25aSGw0a3ZwTVNQbFZJOGNFQUc1cjBKb05ONTlqb0VzTWhVY1BaMVl0SURZWDljblI3MTF4NlNRRW5CR2dUejZkM2IxaWViSWRvdGxncUUwM3c4N3hsRDArcUV5a2NWaXphT0IzWitvY2FNR1d5YlpUSWRwUjRuaVY5bURtNjVFektLOFZRcTU5aU1sQUJrNTRBN3pBbE1ka1lObWFSdVdKTitiTEo3UnFFWmY4dnJwTTArM2N3RDBOY3R1d0pKQTEzSklKVkZsUFN0TklYekFXNHBwMU9uVHgzck1aUWZGK280cDkyV0RrRjJ0eDFNVWRDMTRFcjlsMVJsWXNFWW5PdWJqMklvdEw0dGtnS3duRTIxOVpzalhiOFBKRmt6YWthV2hSQkpBa2diUjZteWlZRnNKZ0MvbGVsbHNOOWcxTUwwajRIWDRyd0l6SGJxMjBGRGtCZGZxTjlTVW5JYkpmMFFRcitReEh4NGYwa1Jla1hhcUtaWVVYWU1iUkthNk9PYkxQT2FLR2Z0N3hGQWdUMnBIdVN3N2tkZmxvRVI5MXpzSlBXUUpia0F6eURGa2tnVWc4MGtXN243bitXQk4zQ01YQTNsVTZRUjIzSXB4Lzk4NTc3aDJPR2twY3A1WWlUWC9UaWtCa2N6YStpd0JHTkJpL2orR3dXOHRHYkt4cGlTTkVRcVVEZHFmc2NiVk1RK09TWUdvZVFLU0x3UkVmVUdEalIvZW1jK1pBSnN5M3NyYVRaa3BIRlpBSTY5ZHdPMWR2c093L1ErTy8ybGdnaG1Fc2s2Tkt6bWZJK09ZdU9HMlVvYWdQOUxlL3k5VUFCazRWSGs1NCs2Zlc4OTFxZTF5VkRUMktVYzVoTmVlUEJhUXdWYjVCUVlQdC8rMnhFcHFzSEM0R1kzN2hYeVJTR3Zmd1lhN0RHVURiTUtkOHZ1ZDI4aDY3bXBPbDdmZTR1RlJlL0hPS2YzVEZzKzlSWCtRcEwwK0MyYjRSLzhWZmtVUU9BQnQ0dGNhRFYzNG5VL1VGWEJVRHZQWU1ZZTBGMjRBWlBJV3BoWTliTHd0K3RXdm11V3dodkFnUE4xcnh2bzNocFh2UU5TUHNWS2dGVUtFTnJtU0NqV1BZQ1VvUWZKRnBlcEk2b3Fwc1Z3SnQ2SWxCRkdPNHNvQUJOT1MyS3RuRjlQN0U5c1NMSzFXV09kR3ZZTmh4S081L0Q1QUNNU00zb0x5Nlh2anpQZTU3aFAyNkRLS3NJYmhMWnFjejh0Sk9jbTF6bFZLVjg3Y1ZxRGg1aU9nR2tOSUtwN0pVOGVCcDRWUlB2djZwZXUzRFIrUk9ocm8zR09ucG82Q2RsdGtxMzk1aFVpK3BEWHp3Y09OQTJZakM0Qkt2WDNKR1ppNzd3SmJvU3p3d1BlbFJDZTUyOTdHYXUzaEhkamtOZkRNYW9DZGZvNEJYMUl0aGxGTkVIVW0yblRzdWlQZS9yT3V4N0ZTbHhJd1QwOU5xbnZ5Qm1XUVljbGVxbFBFcmV1b0NaUkZ2WEwwN3Y4NEF4bHhOZEpNL2F0RG1DanBtenVtSW9ZT2Y0dVZxVi84Wm5Td1Y3OFdXMFMwUjdBd0kwRURxNEI2SWFJNkFVQndQck5MWTBlZVN3MjR6UTZxVkFnQkdXNWFLNzlNZytTa2o0WHhkUGw4YXhNbDR4Nm53bW5BZkVCSWp1MXNzcDR5ci9nZGk5a2wrU2NHVzNyNU5WcUoxZlhSa1c5TzBBNkpCb3R0dldHeXBRaW9TSDJDNDZiZXBOcHQ1ZFhSSzI4WFkwaHNlRW5XOWZEQmFVTUh6aWF2V3k4UTdqdHR1bHJzak9kNVd1bnFHejIwclBpd1gvM2ZkS3VRZ3YwZzRDRHFHQk1hbW85aHRDeUtxTjBxVE94V1A1TW1aRzBsdXIrZUlNd3RjcmZZcUp1alQxOUozZHBzOG1yQ3lTdDFNUmRtbE5JeWtHOGNJTXN6dy9uTWxSVjFEbXB4Tm4yemYzZ2ZsWG0xc1hTSDAwRXFySUNqMjlkbnlOU2JJdGVRT3FqUExxQmYyUUREVlZDQWdjQ3o3dkVSOW01WDRYa1RJZUI0cHBxYUZhMlVIRTA1UVNrQWhzN0ZreVBmNDBVRkdsS0c4R25yZEtxMFpMVWs5bTVqbGVUQndoZERzWVA4SENES1JFNkxTNDhxTEhENHB2U2wzWEZ2bUg4S0JFbXlleU53d0p6QUpRZDhNcWhtS3NkYW5kQjZFYzFiSE93OGFnbVZHUC92dlkyQzYwWDhBblIycjJIaGRrVWJjbFc5K296am14bWlwQTFBSklabnF4ZzRhYTFMZTBSSGZVMnZrcGY2OHkvckZNWWdDWHVlN2VOcXhvUzBOa093OWE5L1djREZKT2gwR3JiOHpZalBnYVNERU5JRk1DTTBINU9sSXFxMnIyRktHa2FRU016Vm04N3I5TDdmeXNhNHh4Vk1EMGg3Q0lFeExCVmJDZTEvci9XYXZLM3lQaEhWZTNYQmp5VlRET3FJNC85ME4vQ201S25xeEZyVllPSGJ3TUlYYTNHd053Vk1FKzM4T3BYdk53RDZsK2pOOEJEQ1JERWpHREZDK1dPYlRkbSs1L3RmbTBRZUVmVlVZRnRBN2dUb2JpQ25sOHJ5d3JvTXlCSE5DbG9meitXN09oc3NyR3VvcytmUmhoOGtCQStOaTBmWWRoS0srcUNaYVkwTFVEcG4xN1VVS0NYNmRPWmNjQ1l6U3NEMmlTUVA3NHBGbmhsa096QUNzYXBkVDIwemJqRjZacUxnRUxVUFQ4SWdsYVgzOHpQNnpmZHlCRitOak5mMjQ3WE50bUl6NFFDTzVpUnkvR2NTOGpqYVdNZlR4STNFYlV2enJwcnRnUlFET3ovZU1ueVZRVmJiRmlUTVpmaGZRTGV1K2o2aVkwUXMvUVlHRmRIZWZ3ekFZdVZwUGhWWksvdFhzeTZEQWlvTGxtTkR6QXUxZVE1aWhDbm9iTytNT1p0U0QwK3VUcGlPQXZQd0dXZjUyeERVSGo0emJkRnRaVUxQVjRjMVRtV2ZsREdNa2cvSWE2a1BIcHJIRXJ3RlRHb0JnKzFENm9YOGxTUGR6NXNyQUYwUmJrdFVUbXE0NCtVU0FZWW93WlFPVmJNM0JXTWM2MDNPeTlTUUQzYnVOVGd6Sjd5YU1CYm8vcGprelZycFc1eFlIMFJhMTF5a2l6MzJ2bzRuQmc5WnZtOTJLSFdoSm03dVFKVjVETVBBMUpIQldCTWNqei91WnVwd1hxam9UZmZlSFoxN04zd2FYVWFSN2NaRHM5NGV3bGhzYlFybUk3L0E0ekpEVVpqMHFLaVZRaG4zZjNBbmVFaER3bDZHVWRDQmRLWTE0cTluNmF5NTh0d1cyUFJYWFBKNlVFNlRVczZvcUgvMHhnRHBQM2J4L21mY0NVeTVvbzkxYWdDUHRwVGZvd0daMHR5dzVtSU9zVXF2ZFVSRGhqdVdMWC9XSXFhUGxZeDN6bUozYWhUY3h0QzV4UWdLV3JRc2tGNTdMYU92d1lOMGx6SXd6L2pvTllraVp3THlCN0pvaTBDc1dXUkM2U2FwRU41VENsSWlzTlF0TlBtZndLYUtZYitIZ3VvNzZSdGNRTVhkUlpXakVKTkhxOEtaS2VnL3VXV0RPVzZheWdMUDlKRHJOTlc3SmZXRHlIUFI4R0wrMjl6QkFENUZZMVdaWHNtWWZkS1UxVlRMTHpBSEVSSkpHVHB3S1pINWswdVpyRFlNOHpHOVdYK1JWRE04YnNtTjhjSTJ3S3owVGQ4R0VxOVQ0RHZZNkZ1aE1zcVBHSEMxdGtMZHh1d0JZUDBMdTJSdmpYYXhvZHJaaEtma2tJd0djZm0rbEZTNFdNRlBDejNGd1d3dXZOTE5xdjdjODV4bmszYVhXbDQ5eUNXMFlUelRxd3lLdUtXU0lGSnVtNUc4QkJqdnh4MnlET1pNaDE4TTJXaFJHWDVWQTBwM2VBaWxCc0dhNTRQK2lFYXQyYzBsTG5UclhnN2Z6RExKcmpPLzIxM2hSbVQvOTJ6SHdIU2hudFVpUis5S1VXS1dSY3g5T3JNV2ZlZkVvL3AyRlI3ZGJOV29QL1Avc2U3SkpVZkJ6Sml4Y1B2VHpNdlNUUXJjY0RBbXB3b0xuaDZwbnNBRjM3VTlDYWt2d2IwRVp6eXdoWWhmVXlBWjRvQXU0UjFYNTV5cmJKaWZLUmJMSUM2TmFZcVp4YnB6VjllYzQvU0ZTakpLRXZtVkdhOXRIZlVKYXlBdnJQUGJWSE5heGxiZEpPT243ZjQzR1RUZEdHdWZYdS9kYUFodVl0b2wyeTVyRlZVeGxEcHlLQ2ZZUnozZk95SlpFamh4aXpldGxGNWtwSzhrVXVFcEtOV25TRzlWRWRtY243VHUwL1U5UGhvK0laaVRpbmNYZXBEOXpRWEd1c21yNmoxOVRLUkNlNGRtYkdtUmwxY0RETkFCWWVPS1Q1MWZIYzYrZDFROVQybjFVTW1rZCthaVNVZ05Jcm9ncXRuSW5lemFFczdIbXRtcGpLdHRXZzd1bExoUHZFRW5HRTVUcVBZM2lDSXRQellvakdFVDRWNzU1YitjTm1xZEc2T0JUbGJZakRzNEFBcCtobzFJcThSL2VXYTAvRk95QjRLNUpMUS9XcXdwYU5QdWFvdWZIY0pNRWxkNHBlaXcvN3VJUlo5VTRvdFYybEFDQlkyUGZTVVV1N3ZKL2laVXR2UG9KbWQ4Sy9CbWJuTm8yaXVtVHRReEVlQVJuanNIZHpmMUpyRTFMNk5HRnNJN3Q4MWM1R0NnbVdJTEtNNXBXREE1SE81M0k2YWp1NjkxNkprVWwxWWNZeWs5SHd3Zi93YUt6R2JOYWVYRDJkMWpCZCtycmlEeVBnUjVwMzJreEFiNDF2ak1NNVFqVXJWenRJU01tYlZEQm54MnFBcm5MSjZFQ1JHWmNmSzRVNkxDQU14UnRFK1kzMk1vYldJWXFiZUpMQ3NhRjRwQ1h5WmpQQUJWbU4zNk5SQWF2WDhSWE84MEp1RjJtL1NubWcyTkwwZFNXNjdFVkg5STRmY0ZTanBMNzNyNm9oTGgvVit1SzM3ODZUcHo0dTlwMWJ5WkVFRlZqbjRlSzR3Qk5lUTdER2hkYkZiUlR0Ni85YjU1RUJNZkpHYWtycVo0VStGZ25oMnVJcGlkVWNHK2lCakhFNUhNUlgyWktrS0x5WVFFbGt3L0tiajJ3OE92RGF4ZDhyeldvU1Vud2tpUDlEQjRMMUZCZHJyZjlhblRxTmZQZWhIVEJseUc5Y2djUUxyUjh0UUVaTjl6dXhzOEJWMVpmK2NJazlrU1N0Y0NPRHBoUUNiWlA3TlloZ1R1cVBoOTY3Z3lvNkRoSlZFZU0vZ3EyYXJFbzNOa1Z0WDdEN216TTR6enNqd0VhemVaYnlnWTZ4d1A1RjVOTHFQSjBIeG5jbmkyWE1uL0dkSFFtVGJRRjF6ZWU0TE9oWmFEbEJ6TVpMc0tYY0ozc0pzQm1QT0RjU1cvRktZaVZneno3d0xkejBDM2JGcFR3ZWRXcElaekcrSDBrcFM2aE9GRjV5TmoveFVHSEVRSzc1cXhZVUZ1WHEydkZJVFBWZjdhYUFXVUYrZUJWNVZiQnFGY1VjY0hOYVRtR2FEZFJUZFhUdXJLSjhBVHhYMERIV3oycU5oR1A0bnJZSlJDS0kxMmh2dmFoZGZSNlJsUit6Y2E0Mm1qeWJWdUhFRUdyVTJLdm5IeTkrbW1sUURINGpZSFpLQzZrbmtuZTVRMjhsZGdySVNBRjBwMnU4WVZUeTJiR0xacVVrSVY2eldEWGkwRHVaTWlRaE9Kd1VnWlFObnJqenBib3hpZjdDYUNBRmR4SHVrQTVmUFR1YkY2YUxPVFdDblMvRVA4WlNPSXlOR3Brbjg2QlZMRWd4Tm9DbzVYRGRKSGRuU0IwWnkrNU80TlFTc29LZFp6aWt3ZzBlU3ZYQUU2ajZXVzI3aXJsWGpOSEh4aXVPWS9MYUZzU2dYdjYySmZLMi9PMDlyMURNanB4djMyWTQ1N1dkOHdGQmY5VjZpNkNkTFAyWjlxTkZzeGNQODhTN042YjVGQWtaQWtPNzhUM2Y0bXBVVm5YZWQvUVFDMUFBdWRCcitnZzExOGkyMDIrakhmNG0xdEJ2RDJpd3QvOFBxb0FXUVNhalJlVTJrREo5MWxaOWNxZmdLVmJ6Z2U1bVVsS0RTaDdhZUNsRk9vVno5VUVkVFF5Tnlqait1N0phWDlEV3lxdHQ2OTU1ZmN2QkpGMWFLRWpqUFFqWVY0K0ZRcjlGbmQ4TnFXYXZCUkw5MU9VY0lMelhWc2VsenZMUXRQbW12dGRoa1VOaThHK08rYi9xY1Z5SHZsczlsSmpSR2JlMFlXdHVxOXpYQTAyeUlqdEJqb1FkMXZZMEVtRUZ2YjN1M3hpUHQ5V2l4Nk5aN2xqV1FWYncyMjlTQVByaC9oc0lFQ0hUTG14S3hXRDMvSzZUVWllUWVxSklmcGNJb09RY2dtdkhEeXlSVWV2ektJbWVpa1J6ZytseTErcVNpY3o3aGgvRENtLzM5RnlrNk04NlhOa2hjRWdKS0FOTnQxbWF0VUhCUHVNbXFrcVIwSXJzZWUwdUlvZmpnOGVmU3pDNE1sNk96QVYxUHV5ZEFOT0RWK1NhVnFLcmc4cVR2VDJST3BpUUhxb09BcTNFZEZSbzFRVysxYWsvQVltR0VWQTRjRjk5QTgyR1JtNW1MSGhMSHFPU3FCVk5GNWQrdGpGa28ybW9yVytiQXRXcUUzTWhpMnVZUEpFZUwrcHVXT29KYUxWOXVIdFFJajJHdmpxRW5QaUYzZ1NOazJrcTFyYit2MzFERHdjYWx1MW5zbWZFMW43SjM5dVFnbGlEeXlvQm91ZGtaclV0bklVckRzQzZpR3MvREExWVUrRXBDOFZZUTRpdzkxRDBPOGtKSVJLMFpvM1l6VXpZbm02dnhxKzlFREFQNVNXZitFeXVwd2xoY3lxN3JnZnUwVWNzUy9jeXkxOGJaQnZwb295ZzFxMEdOa1RKK013dFhCdERvYUNoSEVxTWRGL2E3R2pVZ2JvU2I4akhESnJmcVJoUS9iYkk2MnI4bkhvT2E2VWdPYUpMeHhnMUVoWHBYbWtkM1JjaDd1TnhncFB6eFAvbUJkckdzeWdub3RoMXo3US9ZTFlKYjdMd3B1R1JFZGhQK2VmNGltaTNDQm1KcnE5cFdSOC9zNDNTNHV4cU5ZSFV2OWhhOVJCQUNCaHV6K1M0eFRRVFphQ0tTb0RIbnhDOEN4R2hpSGN6dkpVVGx0NHJyV1FwdTkrQXZzclIyd012d3FwVFRkMkVUVHNPL1AzSkppTEJVdmNzMFRYQ1BDUlkyaDlOeDhacU16OFhTRXFhOUJ5RExvTk04UHh4Sy82MnYvV2t6dGI5ZGx4ZkhzbDR1NFVqSVpvNWxEN2tuTkRldk9adkZSWUhod0ZFMjJsWHJYK1NmZnJ0M3k5UjFES2FHL0dsQVBMUVFYL0hldHpwbWNlMFRUNjlVM2NGWlNVV2oxaGNKYTI1T29DWHgzTzVqWFNpempQdTY4ZUY2SlJ1NGx5MEdQbWloSkFjZFk1NExBdStQZVR0SGRHV2FSZmI2UlZwOXp4d1ArMlBvVFNRbStxRmhENUxraHNZdVQxSXdXTElBVWpVOVAwejdJT1VqMlFQNHNZQUJ0MnZYNWhKQ1ZVbmpPQlBWR1FUbXd5UjhMU1JjMld2aGxtRDRETWl0b3ZXOEFtcnVIdnN1eHhNblkveWJYQjBmNmpndlkrN3RNdTBzSk41cjREQkVCWGEzN1NINVBlcGJpQWxZNUw2KzA5cUY5ZGJnNTdxWmRYcitMa2orOU9Ed0lkb1k5T2dzOVFYQU1QQks5c05MTkRNMW1GYU9ETVZwcWVCQngzKy9YOEJreVBvZk9teGwra1lKc0cxUFA1MEZEQlhqMEE0dVZVd1NYT255RHZqSGQ1cHVwTWl5NUR5T01WRGpQRGkyMllWVGVLS1B4dEd6NS93TG0veC9EekhPNFBCS2xyaVV5UjJmZGF6WjhNWndaTzJ5em00MFJ3THFlek5oc05UN2FxaE9xV0JNZlRiWWN5VnRWenJST0tMUS9jdzhoOU1CWWdMUVo1bTdSdGFqTGhqQW13V1J1YmJPeXNWWTkrTWJUeHVsdlNxUXltanhUajAveUdtb3dYT2s4TG9yTEhieWNpSFpiaTVXaXBxNWUwMjh4T25YUHEwU08xRWkvQm1YRkNyK2l3NHRvUXdsZDFkNUtYWkphcTFlRFBkdXFMRXVWUnBLQTlDekI3S0pzVFRwZHJZcE1hT3NJRk03V2dyOU9oL2Nhb1JBb2hRTjZBNkhTcm1iVXV4ZmZZbFM0eW1jNFc0MFFZZmF1dXFwUS9KVFhlMmwzZ1cxdkJVM1EwQ1FXaStZbkdNQWxNN1FDZTgwNnZJcnJnUW1lamdZYjN6MjFiRm4wS05aajhxTWJ0azBmdWJjckRZWXdtQmhqWmV6WnRBSzdOM01RS0tDT0RXd3RtTi9XWUVHY3R1ZEtKelJCM3hyQkdJWFBiaDJveU9zUTRwc3Z3MnBhY2tQbDM2dWxHMkFsVzVydlMzeHNEclpHMGpQZ2NMTk9CWlZxdUJLdWR2dHg1RXlZbml2bUxSRVdQbjMwY2JrZkw0UnNmVHd1SlZTRlpaSkZoNlVrb2ZHcS9ia3ovV3FiUHd5RGs4eHBwQ1ZOejdKUXN0aWp2eEVXcmI0MFRITVFKZWJMbnp5WTJxMmp4MlNMZWNhUjcvMGI2NzZmNWRkUjNhRFFxUXh6UzZZbFB2RmNZYncrOHZpYzVTQWs3NUg5Q1NzRW9yUUNWbEpTazdEVTVIQlJrekRuVjJRdFRKZTlmc2ZxeTFzUU5CWHFVWHp2KzNIRFZEU2psSE5QS0VtTkdtNSt6bEVQL1BhMG1MUjhoeE9HNVBldUhmc080WUFhQytidHhHd0tWV0M5U2U3dHY4ZkJKQngxbitLb3g2R3lQQjFTVnVra05Ra2poOWRsOHM2ZFI4dXdSbzZFcDN6cnB5b0RId052cEdVMHpWNS8yN2dwdmVVakN5cnQyWkY0VE9Qc1MvV3lnTGtmRTJkYk5Yc05EWGpVMGtnZ2JoK1JFbmJyT0dWTmJlWUFvYzRaWDBhUmR5VFlPRnpsUkthR280TW9ITGtNSDlGTXdZbFkrakl0QllWYkl6c0J5TElVbXU3eE03TjNxNFZ0T0F6ZEJ0WXB3WXgvNXlUSUlKOXloMlZaV2cvdVBaaW1EUmdBU1VlYUllRi9UVStuM05CTE9rUXZzZjRDS3VKaTlzNEZxcEUycDBITGF3NnlJY0ZVOG1jbDhKeDZYUFd2K2VMOVV2K0V5cjFRVllRZmFKY1Z3SjZrakZuOUdTWjN1dmJJeGFaTXdpN3grbk5McDYwc2dkem9nb3RxYzVvVlQrTERzeWdVRGsrUzM2MW1lN0wyQldZRmtjREVSL1J4K0owdGdEWjZ3d0tSdTdrRnR4Q3BxdHQxOVdnc0Y2THpwcW1EbExPUnZPc1k2OEpudVpnQmRvN296Rm1GUjZ1R1h4YnlTTmVDdlBLbDkydmtWc1lFWWpaNzBuU3NOUXo5V2lJeTBwY2Q0Q2puZDE2Z0hWajNYK0lJcitaSC9nVG5ZeTBKUXZWdHBvUUtBM3lxVEg4Wks1V0FXRkxTWGpOZUhDd3RZbWFhbjZ1Sm9PV1cza3RtUjBuOWowdXhTRW5pQ0hmb2JjYWE0YWRoaDZVNjVpS0NIZXI5RHN2cG9GSnhrajVqaEdMaFBTakoraExkZHphdFYvMU9jbjFDRTV1Wm9aQU10Z2toVVlONXprOStWVWpKeE9UakRzWDhrUUZhbitmQ1N3MHJLOEloWE5wM2R5bmZIWFNZQ05xMDc2UG42MGxwc2diTEM0MXBsNzVVTmpBdGRrWEowT0ZCUDlTT0Z4WWQvcXhvQUNtQ2YyYzRCTmpnbGwzUDhQNzdpa0dRUExiS2U2QnByZjVSUjdTTFRjb0xqK1dFcmlZRCtYdmxuQ1E2Z3dOMDlNSWtjNlBIK3hTOEpmSkQ3aXlCb1NzTHgvTC8xQXpheEc3ZTBlSVAyZHhyb0VSaHBDNmpnOGFycmc3WFFCa3NESElKWklQUmh5MTZXaldhdWNNVU9MdHhyZ0JVOXJlekVUam9DdE1uQllkYU9BYWdrVkhkdWVSa3ArcDArU1JvWjRlalFhQ3doT2lZUllZSkM3TnNWNzNvTzhkd1lMaW9DM3FJTG9vOUIvZU11ZDV1RVJKZFRCK0wzZ2FaY1hPYm50WjQzZmVnZXpocG1Td0h5dzRkTTEweGZzWEYxTVk1WEFSMVhtR1I5UXo4WXJjMkJTQmlVVWYxd1N5ZTF0R1FMS3Rtc2hlQkkweldFS3pKdTgvdGRXUTg0bGNXZ25YbzlJTlB3RFU1WGlKaTBPeUJRYndSSDFhaFIxNEwxMGc5a0FZV2xESy8wTjNWemNnWVl1cnNqVHR3LzJ3U0htZlRHSnN4NU5PWG1NbVZsaUJMTEhHdTZHMGpGQkxadFVrSDdFekZ6b3JobEtoS1JyTHFYWGxYcE84Y3JRM0NIRWNaTHU5WHp3Q2M5U3ZrUGU5NGd4d29uaWpkaXpMSHRHZkxMS0xGMWNkdFhNRmE3TWY0UC9KUUhpQlpJUlhCekNLb3FQYUl1dmg3WDQvU1FkRUpueGJzSUVDVUY5MFpuckxVcEJqVFhpWDRYQWMzTXNlN2VUWEt5WnA4UTNTZjFTM2VzWnlEUWwrQkJFUjRQbWJHT2VRK0sxMTEyRmJFZXlxUVpnNTZXaVEwalJDVW1QK0tldzlBMVp4U2p1dExWT2ZrcHVCd29Ta1A0UkdOb2U3V3JteVRYS0k2bmsxVG56MG9lMlZtM1BqQkRmOEd3aGUrZndBWVNBamxQcmExVHRDajF1dTFHY2RJQW02VmlRbjlTcnFmMXltOWZQSXhJbkx4dDQ4bUNJbDZEU1RpNFpKK1hrSnJ6MmRYV1FxaHBTRjRuTldhcGRJakpIK3AxT3BlZHVma3cweEhscjR2T1JiOUJDSjNXOHZBUGRaU3FJN1Z4Yk5hYU9mcWhJLzh3N0w5aG9yVkt2N01MbkVyMmwyWGdVTTYraTVJeDU4eGdSbFlWeGErbHRFZGF1cEQ1eWt0UEVPbGxkTUlhdEVIVE05ajdoN2h4VnZRUEVidFFQNkJtRGRWYVB6MnUvbzcrQWl5NGxzWEdFK0ttMnNzNjgyOHVxWTR5Mjhjcm94Y3dRQmFlbVAyKzRoRUE4OFdtbVhuUVRtSU1GamUvaTVxVnpQL2R5bmhBcHk1R0VCNTVoVTcralBkdmVleHh5clVMdXBaQjFoanlxSVN2S3NjdUtYT1haVW5wOGRQTGxUa09JbE9oTXU5dDRWeDVQTFBJREswU2RVaVo5NUFsUzArLzFtYWNucTZoWFlZZWpnWGlndDlOZVB4TjJQWTlDQzBIZnRIMHE4aHR0dkJlTFo0OG9vdGJtU0laZ0s3L1dtMXpxcS9sVURaQkw2Q1lDNUtEeUxnL1dmUktJUU1OeU4yWDQzMnVMci9mLzlBb1YxMzJodkROV3ZJYmRnSkttekZ3bnFqZDgrTWp3ckNJTlc0ODBZLzB2ZTdFcHZ0WEhnNFd6SnY1TXVJTGc4OWdqZE1rODZRUk85US9ZS2RtYitIVjZlTXFSVHEvb3VkTy9FNnp2SDNOekdnSE56L3pJNENsYzFrWFVNRFRybkRwQkkyS2JXZS8vN2lJNmQxQThuaFg0Ris0dEdraTdoZnNBNFZPSzgzZmRMbWNkQUdxUVJqdEl0VlhhM0o3dmhFK3gwaDNLK2ZWSnBNMkZaRGRZN2dWRjlNRTFydFFteVFPRStGN2I2dlFBVXJlZ3FNbkllZ3B4dElLUmh5VHZmeCtERldaTGYrVlVaSFVPK0NpY0g4c0UrOUxwbGRBQ0ZVcEcrV01mRTU2WCs4eElCNWwrRXU0aWoya0JVTll5dGhxNG8xa3lJRXVEMWt0OVhROTdnUzkrd2FhSUhva1dhZTZqbS9ZOEdvdmdtazMxWjJNMFNCWkFJZXVkYkEveTZSa0J5czN6c1dWSG9QeEQ3M2pJczkyY291Z3BwSjNVeGYvcFFjb093L3F0MjBlcGRWSmdIaFQ1L1JnNW1OZitidlE0TEpud1N4czdWRTlRYy9teVpGNElGQlVBb200OWJNVElnaFZXNlJKMmdmWGtQNm92YzBUSFRFcHhaV3g0elRrQVJWVGZINzV2ZnRhSWtacHRTK2gzRVJjaXdMK3pGQmZ4b2pxcmRScXFka1lXQVZtWHBmK3VlY2tPZlhQck41YjllRXdsOE9KV2dvWHd5UE03M1JEbjVpeDA5K3FZVFViaElScXVCQUlIbk8wM0gzcTVURmRTWHpQK3NQREYrRlY2MUFMaUp3THR0dHM3L05GMnFoRkpJNTdwNHNpeGVaZm9FdG0wRGc1d0d3UENINnRjNmFxTzhvZTVSK0lrRFI4VHV5RkVOMncya0JkVHh4dmVqYVNvYXAzYlFsQ1c0c3Zha1VJalZycGU3ekNiYmNHTDB4U2UvVDNoeXNDZmIyMFhqMG9GaXRtbVkxUSsxUUFiSEpqM01mZWVaZnh1dllZb0Y3bUxuYjlzRjJTUFFFRnJSd3QwOHFhcFkwT0R3NFJlRU0zVGFtVmc0ajNCdmdLV1dMSWVXck1YUFNNK0kzaEJ6alVuNlRicU1OV0lQRFdqNUZCWXJXQndYWUI3MUJPcG1YKzVpWW9takhvUTdMVWNRODY3UVJTM3FaWFluQmJMeS9GTzJ0RUdmekUvckd5TnhFRDJudk15U0lJczRGeDNmWklzSVpuL3RDa29jRzlrclo1VFdoYTRlREkzem15Q1FlQk1Zc1hsUkROc01makVFQkZoNi9RaHExMmM5SVVwNjA2a0VZNWJ3YkcvUW5VK0lBeUpobGZ0bjJmOGlSTDVBN3Y0UjlvQUpHVTJHWWpOSHFaVUdnMno2YXo0WU10UXlYY1Y5WDlXQlJsYVluZlZJUnNtdVZHRGhEQklvRzZDOEFrQ0s2TGRYZDBOZ2VTaGdWQ05weDdpYWNkNkw1cjRyVmkxR2NvNnJDQndCZnd5SUpzNEZobnE4SVpyVVJuOXpoa0oyRmVuVVBpam5iSW9tNGNETkpUM3pxTWZ2eVNHdDRrbzJLcXdvR0RIMjVRTGZ1V01iY3VSaHVRd1lLZ0NYOVZnQ2x4RVRSNkRNNUROalR2N0YzeXNHMGtJOE5LWjVBWkR6akpuSkQ0VlZQd1ZSL2ZOS0hwemdNOFFRR1NhcFZFYlFDdWlTdzB4akhwaHAwZUR4WmVhbWVzMU1wOVd3UTJwdWhtaGo1cWwxTHYwZVlKRXBOOFJGYTAxeWZOWTBLWmtUcFl6Y08vQ2toYmIzNms5ZXNWWFNNUGwxRy9LNy9zUjlNY3F2ejd0RW1kRndHYU8wMmM2YXpmTHhsUmc2Ynl4NXk1YXFIWEJnSCtOOFgrMHBHU2pIc2FFTnMwdEVjSlU0WHRMclJMQkpHSUZWRWUzVHZJWWt2YzNzaWFVMWQzeGk5dDdUUHExTC8raE1ScW9qcW1wOGpCTHlvN0tFdVlaZU9LSEZNM21Va1YrWGt5aGlGaG13eHRMZ1NzR01iaDhmRTZoQ1IyclRPSWlubG1zRjc0eWo3SXBWaVFrTGJ5Q2JydkR0NS95WDZJN1kxYWJyRnM3UUJJM0Q5UW5seGx3YmdaSHZGVEtlYUZLY0kzTnZVUUZRVVJNaW1RNU0rZUY2dndTbFlmZis3L2NXcFltdlBySWg5QlZPTnpWWU9lMnRRZEFXV1Q1ZkpTWUw1VXB0MEw2RGwvcFpPYkJFZG8rRlBDNGIyK2lVMDllSjZ2Yi9rYzIvdXE5Q3ZDVVY5S0IrQy9DUEFKZE91N3ZxOHdmL1l4eTgwODFQRW5tN1ZHc0l6em9GWW5EdmZZVFV5UGhkWFYyeUlDV2xqeFdxa3lFZTRlMW4rU1pDUkFDRHlpTFRkemo1RHE1VGhNZEErQ05KaFYwOWlNMmlXMVBnZjJYaUxEa0lwTm84dWdEdE5kVlRNRUJzTyt1SHpycUVJK0V3TU9GcjJnZXZEOFRrbXlqdnJZSDlCdzZya0FSVUZ3YzdEUnBPQ0lhQUNuMkVkanY3Ym1pUzNNRmVWZ2RqMXkwUnYrdjFEWXFZNkV3SHN0M0NObHBxNlhCVzdRL2Z1K0YxUjIwYUhVUjVaMUxJWjd3dlkwRS93OTliS3pBeVVqRzc2NzFaVVlGNkY1K1ludjRDbTB0d0xaK0dUckJwOFZML0xNZXE4WFlnellsZHJrbE1nbHlXSlM3aVdCaGRBNUdyYU8zbTNyTzJBb3JONE42MmJIY3BJaEc4a2J2SWt5Ym5SVlRFV3Q1YTVmN2lJWUpONjFPTzFnTHArbE1LYTlDdWFVUi95OWVvRjMvakhncWg2aVBTYWRnbEZZUS9HVHNMa3pJWE1URnRCZWxYd0pIdHZtUXRvWEl0dU9zTEd2TDJJSy9NMjk1WUQ4U2FOZlNORDh6VGZnVVhHWVFSeXJ6c1BZQzFjeFdPdG8rWWtXOVIzRWluWkJGVXkvNUhXWEY2V2VxTGNQQURHZUpIM1U2NDJtalY5aE1xQS9HWSs3RGNOMmJwbHMyNVZpemxHditGeUgwcWhEbW1kMGdVUzh5OTByRFgrWGs2eTZNY0o2UzdnTS9EWWNvVEh2LzJOZUtnNHJqTXc4VHFybEw5TEJjTEtXUXh0dUp4Vlg3T2JLRENzNmZObGZVajZpUnJHUEZkSkQremlGa25DSktnaXhaNVJKUUVRWmkyTWVmUm1VWWk1Y3JZdTNPaDUwYTVKZit1cHZOekZBbzdLaHhPOFdSdm9xbkxPMHd2dmRjUHNhVlVPSWN2ZlpvVWllcmRUeUZ5b3h3bkpJOTFLQ0Jyb0VvZHlidEJHc2h1THNlZXdPTDhSSlArSDJPcXNjYS9TWWRlZVJ0aXZYWStGRlFlVFEzM2VlWDNEZHRTMCt3Z0hYVkNDUWsvQ2tHL2F6NGFZK0V4TzlleUpSbXBlS0FYb3NlNTdVU1BaRW9SS282bTN1SVkwcnNHaGp3MHhBUzdYMUR1QlRGVnVvMjl2M2RDaGd1NzBjUGpwbDUveFFtclBkQTM2UFhOWlJXT3N6cjlGdFRZWXhHN2RIVW9vcmVtbllvMVFuVUdXc04veHlnTHE5VERHTExoVkgvcGM0cEQrMTV1R2lBTEZ6VTRQSU5tZkQyNUc4TEFzSmVhMWRRbHBDMXM3cmtZSlVRcUl3Rk5EWTRFaDBkYXdMbjhmQ29sL3JoVUNFYkVITTFkSmxDQnBYeEtmbTd6dC9acHNiWGd5NjhuRWtFb0xqczlyazBFOUdGRlpvWUxadi80cVpSN25sN3FCYmVBTHUwRld2ZFdvTmI0aEN2bGtNRStpNW5iTWFmbjl1Vnh4WGxwWEJsT3hIQTdJS3ZLSkxNWFFhbldrdUs5QSsyVkkxSlNEb1kwNitSMC9nNVRQSklIZk8zcm9samZoTTluY3g2UXJrNjZ4WTFIMCsyVWdGK29RZ20yOEEyN3U5K1Q0ckdvMHNUNnN1QThKZHd0aGcxVDlnb2pacm8zM2RGYjVwdWJrWjVaSGNoTHpzS2tpYmFSM0RIeGY3NjlWNGlJbU51S0tycGdNTUs4dmN2RjRZZ0Z4OUFzY2E2M01WeU5QdHA1K3pYUEFTbnMzYndkbXN4bm4xUzU0R1Rka0I0RHdYNEw3SlhNblFHcUlhUyttUGdXeGJJWmJGY0ROSXJNaWxFSUVHRmN6ZnZjQUN0bVJlVHl6cW5wSVR5ZnNoNVFLNFJLWDlaV3R2VXk0YldYanNMWWJOVjdNcnJac1Q4MmM5Y21mNGY4STBzU1lxVklsY1VZZ0k3ODJpbXhCdUVLczNPV2NvZ1dEbXdscjlUR0x0VlNTVGx5ekhVVzRQVTlmN1d2MDZnTGlvQlNvQWY1ZXNUajNGRDlrS3RUS1FaZlRLRUlPY0NZV2NmSWs0SWtjZm9GR0tTTHFzSGhCcEJPVGZFSjZkeGtCSlhDU2xrbkRyYjhYSllPNC85NlhGZDRUaEFnNC9IZWczdTVwMWtQM1FHMnlNdVVydHkyY0ZRYVQzY1dNQUJJQjJkaUV1LzFLZkZGU0tiZmpUcDhhVWhiOTlDL1pBNW03aDhKV3NHd1Q1TWw5VWh3NkNtTkh5UkExNVR5VndJc09IMEkxdEZlVnFRYW9xVDd3R2p5cXJKOWJJK1d0cGpNdjVDQUdRZmorazJhUE9KWi96THZ4QXRrZC9Cemg5QlpQRXdWRTBJMERJODJ1V0s3MlA1K21IS2lnNXpiWFlyUUU1YlNOQTkvZ0h2U05EMnFMVjNoTFBub0pwNXEvTmVaWDdtaGIyYVdmN3FrRjhpTTRIRUhRNllpWUErRStrUG1mTUdhYkhxNjJRQmk4c1NKM3liNjhpVGNBNFlUNmYrZ0piNkczYWRHa1k5ZWV1N1hRWmlRRWkyZlhSU0tVT2ovekxreWg0UjNoT0FYNnhoVDF5Q3ZDSFQySmI5dEF6U014ZTBSRmJNM2c2Yi9WSGdQOG55Wmt0NDVqMVpZQlR3T3BRSWFGVTduVTVmb2NOYmljbE5PZHM5YjZJK0ZPbkJYd3lBZjFWaUpQTUtCQm9mbVI4d2crNzdnNW8zQ2lZVXpRK0tkTnhVbzE0WFFjNTgvR0tySXEzWFNJZWZNOWF6cWw1c1g3S2xUc1U4REdUMUhsSElZbmQxMGNKWXNBRUhvTjBtTEtjSFR5U0hzalRGZXNLV3NtSytzaVpGWGhsYXZFNkY0NG13ZVhPclg2RkJvRUxScnZJcnNzdDRPSCtPNDdWYU1MNENLL2NOcmpsVG9kZlJyM3UyWFpzSENjdzlrWExHWC8xNXNtMTBEWW1QM0czMzg3eDdMRHlWb3BscnMwcHpJdmZjeTQxZWIyT2Ivd002dFFOTHhRS25mU2JMMGV5WUwrUldSMDlxZUhUL2xXcENGdmNJU1lsbWRGL2pNYUlXRHl4RS9MQTF0Z3VZT1NpUXRTcUhmZ3FIcjFuL2s1bkZoblVCblUxSjFleXMvOHF5U21Xd0lwbGdmRDN1TmNGSGxnNnRyZjJCMTFPbS9mN0U5b25PNTNzV0hoYXM0bk51aEJKc1VuMk9qT25PQUZaaTJkY0F2ZXhIeXRWeElkeWJqSGNFZFhVY3AwamthYjE5aHdaMFJkZFRVR2p0eXVsQm1wYmZHRCs0ZCtveW5URWptTWxZUy9wZm9DeWhFazlYYmdiQmY3d3RGczVxbGVGckNtQjBOclVZWkx4bXcrMndGcVlFVXkyaFlQM1p4WTh1aFJaZUZYWmZoT0Q1OHpHQng3bG80eU1qaUJjMHp2T0dxVlFtOGQ0dGsxQ1JweUdKT0dKV1ZVNEVwSFB4cWdNUDZoVjdmMEl4SnVnemlJRUpIYXZyWmF1UlhlMC9USFlFT0twbC9hNGptL2ZhaCtvQXpIUkJxd2V0akpCU2pOcDVMYVozWlVOUUVsWkpCRE9GMWU0bXV1bVNIRjZkYTM5NEN2cHBxNDVRTjFCMndZQmZieDRZOWZucTViK2hlVE5UQ21QOVhoTVFHbmlEaG1kaEd6ZlBVWTVZUHZUVWhFY2FhQTJ1Y05EVU8veHZhVVZoWERJb2RyTS8wNVIzMWJuRmtqVWpuMzRON0FpdWFnbDlWQjlTallzdTgzV3M5ZW9ldmFaVndaTUM0dWlaa28yR3ROelpDeU1IUnE2R0todkVHQmlNMWdMeXZNWmszZVIyZEdjbjE5WVg3MkpuREJZNlJXbmNHN2xHQWcwWVpSOWx5b0N5UTEzZ3RueUJpMDVnUGxPOXlPZUlZR3FRcmhnUnBSK3BBdng0Y3pkYUJNcFZJN1NnWk1BaE1Tc2RQVUVROXN0VHR3U2FiQm1ybG4wdUhzT01oRHZpMGJOUlVXVW1xbnUzZWlMZ3prMlhLR3lUYUhDZTU5dlpaY21Ea2s4YU9PNnBUdzVIK0RXQUxCUE1jQ09tZkl6NGNGOUU1emVzWGJRa1FOREZrN3ZsbkFjZXRicGlkK0NlOU1uVGIzQ2xodjBsTDdseXVzSllDcExwYWxWWG1RNjdZTlIrSUlEaDl2VzdYZVduVTNGRmZkbk8weXFDT04xam9zU0xWTVRUYUgvVDNRN1krZ09Vb2ZEd3dYYUd5R1JCKzRHUkMya2s3ekFObGdkN1BtRTVrWGRhNElwbVRiUDJPcVVKL085RVhXNGFzbFFSNVB0WXkzdE5NYW10azRMd3piNldJRmxsN01WQm5lRzV2UGZFR3NsYmx2SzR1bnpMTEl2Y2VJNld4aGlaTmMvbnIxMGs5bm44aWtLUHo1am1BOW9DK2xXSUU4UVI0WFlUY082V1o3Vk1PUnlrbVdMQmJURTFOUWM4L1RCcFlTYVlqbHN5T0s1MEVFd1pDNi9oeU1pbHRGRFUvT2NWZlNzLzRzMFJrNjhxSmtVNW1JRnh6UWN5U1FTekxLbXFRemtiYjJabEM4TUxNUDhUdC91aTJVSzNyM0lveU9XakROZkFWKzIvaVlBYmFVL2djRXVDOVBxWmJCQ3BIcG9icnNNU0pwSXBBYmRrK2xaQXJNYVFmZFFQMmtZOUtyazZUc2pOYi9hZDdHaGMvSFRsSnl4UklTRW9pakd5dUxoVUpCNUNoMzVQclIxb2libVJFM3Z2aEM1Y1dqL0FGRk1sbGlUNUVMSG9qOWllTUxFRzBCT2tWUlVYS3V2MmJmYUY4QWRYT1JuelR0TWZYWXFCOFVWWTVUdnliWDRNa2c5WVhhaUREcnA3S1Y4d1ZIcG14M01JbG1Sa3puRzRRN0RiWU5UWkJFaTJ5eFFmUVczN05yQU95Q1A4QVhQL0VIaS9CTExGZy9pcDF0bGVaTG9qbG5wZHpLZ1NtSnlpNElSRFdOaWZDdEZ4VFJqemgyejlETmEzS1VaTFpuaXhya3NRV0h3cDJnUmttdXU3SFlQSFlJUXJkamloMFduTmI3Q0w3aEZETGpiZkdhVkxRaDVGdTdTSHRaVHFEWXpnWTRRbk0veDJQQzh2NitxbUNBTWJPdld4Wk9JeGpncFVGMXVkMi9lNDFLMWJKQVhQVFowY3RKTHNpZ0pEcU5INmZOc1hHR1hOeDdjd0pQZ1A2SU5LM1F4YzN5bGZ2MEwxZTltMzdrK0Nxa0pKVE42TXZ2UXVhZThXak8xbDBKdkJoNnlISXJaZ2YvQnQvRE5TMVFVTGdIZlVDTGR3SDZHVlh4bjhKQ2h6clRFSkw0ZFRaR0Q2bkN3UFdEK2VlVS9qeE5jL3dwaC9IWW5nSVpjU1RPbkE3Wm9IZW1jN3BVWVh4ME5yNDVTYmNlOUN5QXZGbkN6b0lZYlh4b0RYWVZ3dC83c2Y1MDlWRWZ2b0x6amJGclJLcjR2bnRiNWRnZURpd1JYNm5lTzB5UVpzT1NvVmpWdk9PU0F1UDRQVCtlektnT1RMNUNNZUJGaDVmVHlDVG5lWEhOZXhMcnMxcEJwTEhIM2ttdC9HaTY5MzhCeWpKeUdSMXdNNy9ydlJRUW9TMWRyUWpRMHZlZnFJSktsYXZ4VUF5aTBQdUlMQXlHR2ZhZUN6ejAwREtqWTFjb3dwUnV3d2Y3cllQRVpPQnlqdHRucWo2RVVaODRGNWdacCs0SEptVHBNak5xMHEvbHlLRmh3SEtHMHdrVnA1aCtnRVN4ODJWS0dSK21iYW84WU9oMjNKbkV5K2VOSjQ1eW9zN2QxZ0ZjNkdDNjdkdCtPekU1VHBBWWljRXBlMll0dXVJSE50MGhRcGRMQmRTOGVxeDlEOVJTcnlhM2gxNmpZSXA5T2dmdjU4VVNUclFhNmJPSmdDNkZ1dzNWU29ob1VPUXBRL1hZK1BWS3cyZVY4UTFONnl4enltVDZRSWlMaXptM2tjQStqdEZWSlZqL0lsVFRHcjdUajZQOGZRbWgwYWczQUpmUmJMczhubUVRMVFIR1V0YVV2OWRqVGdLTkc1aFZMeWl1akhMTDc3dE5sSGNZTHdxcXVVNloyVitXTW9Ed2ZCaU1EcUszOS90TmhzN2RYUWhRVEhZa29sZDVWZ05tVitXSnI4RVR5b0tUSFRTOGcxUlpMK0tDYlp3MUxab0dUZ1I2ZU5sZXErWEdSZ2dHOXBidzErV2NXMGp6SnB2UWxlK3BEV1RBM3lQYUpvZ2V1b2hnN0VpalIvNDhTZTZrandOcEdTdGVsQUhXTk90enJmZ21OeHRIOXIxZVNSV0x6NzluUk5GNXRoNDNWeStyWjlGY3dLN1BsZkpvalFtazZ5RElnRFZwUzJJSnRGZmxIa2wycGRyQS9aSzRHcmtzOWRmVVJHVU5rNTRIaW1wbEthWUVaWDVkRTJNOVcvNjB2eFRMQkU2WGVJWjAxaDRZaUhCSEdNWCtlQUhaQUhwU2syZEZaVWJRTC95bGJxOFZkenlPQ253ekI1MzJ4QXN6MlhxbUpGTkpDWjZZdXZFcHladExhMDdHdWhQa2k4TWVaVUk2M0tONGpDMzBTU1g3L2JXcHNNeWZwcXJ6bU1JK2NDWWxtUlVCME11NGtHL3VudHVJbEZ6V0cySm51U1RoT3ZOQjg3V3V4REY0SzlNUEx0QXBBMm5QVisyeU1xWnRRdS81ZUJnTXpnOC82RkJoZGRKejNrVjBvbks0SmJvNzF3NmRoSTRjekYza3NoNy93VmUwdkFIOEIvcFZHYjF2N3hzY1BJaGc2S0wraHZUdHE2ZzEra0NQcEJVUlVoa2o2eXJmUGdaMy9YdGMyMk1hUUpwMG91SThzbUYwSVc3UDhaZmtDTlJseHlvejVyT2xYSjJZb0JZZitoWkpBQ0xwSVc2RWNnN3MyZnB0SVd0dnVBZ0d2R1Y3ZFNOTGtZdjE3Z2hqa0pReDZ0THVjbkFwZDZWNTZQQUtOai83WXlpNk1PQzl1d3ZYQzRIblFTb2xNVDQ5YzYvNVpSSWZXYXVPeXcrYXJRQnhFVDNncWpnWlBsZEhEdWhQRGRZeGZmdUoxaXR5dXdhNzVPVXdWekNmUTNEaGhLQWZ1aWVCRllxcU4xaTV1c3hqTkZ3S2FkNFYzOWdqdDJ3TGpjUzF5WDU5cXowTEN5Vlc5S2JTWVU5QTI4aHk1REM3aGR0ZFF4UlU5UFg0dmZnOFI0S1p6cFQ3T2hKZTRSd251b2I4OEtzWUpUM1hkYjV1UWovaUkyYjlrK0lBTDJSYXpSZWcybnh3aTNpYTc3MWpIOG1XY1N0QXMxTkp1K2NNZ3g2b2FyRnFMZThiMUhTUnhRN3phMFd0UWhWS2RoT1NvK2w1TXlVYk83bDRydE1mOHZPaWRSRFlTQm9FU3lpRGlyWlIvbGlyYjdtTndPSFI5QjAwVTNLREhqUisvNi9wMEZqSENWcFdOT3pKY1dmSVJRa1o2WG1iZFhvR05iWWkrLzZLMzFrVlFTcEVpRkhsZjBYVEF6UUtEaDAzQkp2NmFvbGRTWEluUWZBRUlOWTM0bU43VEd2YUlMSTFpcTFGOHFRRDlMZFV5TTF5MUdrbUljb1ZpQXlhcVBtVEY2c3J0YW51eVRNNEwxRDB3eXVqMHRFVkFmdXljR2R3RU9ONGZuc0NxbHQ1VDZTMW9iZ25VdXRwclM0czVXcHpRZ3pkNFU5VFJYSkVybGkyK28yYlM3QS91SVNCWmhnaC82NzlLL3pMZGE2Z1d0dVp3QXZUR05kQ2JBTjl1d1p0aTNIazlrS1dySXEvekRIejAwK2ZTWUxjYzVzZ2pnWTVzV2QvRjluR2lyZ0dvaklDTVR4VXpHbVZWeWpzQyswaVo3aSsrVUt1TEEyS0Nla0lneWxYaitEQVpWS1VGZ0JnWFlXNSsxYnd5QVNNVWx0QjVNaENjYU11aXZ5eWhadzNNSjdPamptSnlIK3NIN3p3V093RmF6dHcrS1FwbDZFVHVuR1o0d2dYRGtrZXA5UkRwWEhLZEVSeTVSMUtmT2ZpNjFsNGtYa2xPVmkrVXZJUGJHdUt4VHFTdUt4amdnNWFVVTBYM1YvRUtkT3VnYll5ZVlLbFlUeWZlNlB5NnUyWitBMGs0azJnaUhpVVZxa29DOE1LeFRYeG1DaFNzNjhXcnlBTWhVeHlvODRPUmR3VE9OY0xkbXJWSmJueUgrdWdteXl4OWlLRVBBRHNNaWp1bzJVM3VKRGE3V25mcjlnY3ljUXEwMDZWeEl3cmhrMEZWL0JEanF6cXVOT3NFSlhkcmltR3cwRytKVlU0LzVCTmsrbEU1a1NDWXo5Y09PZk5CdGJ0UFVvVkhudTFqZlB3d0dsYVRjN0dVeFBjREZuRWd3YUhoNXpuVm5Td1BBQWRYejVvNnZJMzRFcHowTktmeDExd21VamZXOG5UQW42MC9Dd1BWNFhqSE0yeXpYYnEvRUE5aFVpbXBQeUgrZ01XUWM4ZmlFcGFUdGs3bDFpQUR4dkRPOEVNZGxhUTBuWGRYbmhDdUNyc29DK1V2bGI5SWFYcFRiaER5elR6WVlVUFJzSjFraFlVNitVTVBrMVlIbjdtRTVWMy9GMjhZaWEvd3J3RGRGK1I2VG1WenNxdWR6aXg3TnlVR2s0NndYczBXYUhJVVJjWkRpY0dpVjdTRWhvVk5UVTB6Z0JvYVNkNDlMTm5DY21TZ1dSTVVhMEpLZHBjVm5mb3ZkRGNJeUVjcU9YRDRWZVAxYmFXMU81WEtpOER1WnpOdUVML2RyYWZ4bGtIejJSSWxhMEpwOElMTm43UzNmZGVnOVVoQXg5cTArU0t0a1pxMktzSnJkamp5QWpyM0dmVGpWSURBejk4NDE0TnhZT3RTN0VXczJaYUZLNys0V0JZb0M1SGtlcTRiL1RWWGVuMlc1c3hHVVhHVmJlYTBQZklPaWVFenF0YWNZOWlaSDhKQndyTHZhTzltUXg4UzhYczFxb1FBNW1SdWhMVUZJY0RHTWoxd0pLL0srdmNsQjVCbDA3MVBscnBxNStMNFdKNzdmL2hhZW1SM1FCRFZOK0RZby9OTU1Ga3Fva0k3YjFuUnd1ekRtSTVkRXg0WE1sR0FOZDZVdFpaVlExMitDSGp3aUxmQU05eVBXYWVpNndSakdieEJSWlVXeHl0L2xBM0JhbmxxVmJyZFNkTUJHNXAzajRQYTlzU2ZZalVyNzd6QjloMnFwbkM2Vjh1MStYRm1HQlRQM3k5N0tDQ0h5a0dmQjZtYkNObmcyT1ljRGZGeFNwMTJNYXF0cU93cnkreEI5Z1VrSGxuZlc5REVOQUdxY1lPeEZPV3daSEFKRWVJdVB1eUxyM3BjOGV1UUdrSkE2SzFybUhKRG9lQWwzNzBobUhZK1drMDJXQk5yNmJPajhvd2xiRVBYWm9iQlEveFU0SlZOOWwyR0gwbm5JZWRva1h5Q3ZCaXErak9mOTB3RUNGaGh5WGdhS2lPb3MrSjV0NWk3MitjeVNDb29TZXlyODhVTFQybXdVdU1DTER3OVB0eTcyUEJ5aUV0YXRwaXFOZVpGOEtsYWRnNGpEKzhpWSt3OHJ1L1B2ZUFWbXJBQk1mdC9ZZXZGeXpteUIxTE5pZFV6OHlybm9sS21pdHdLMmJQSnJRelNmeU1nN1JDWnRuajgwMVFteEIySGgxUmRPREowNE5ZQ1I4NG1reWVWbUxyeVNRc1BmV0JpWmF3SVB1c2ozVzgwM1lUckNJRlpoNTVhN1JoWVNBaDV1b2xHc3YwVE1DK3BmWjhDSkZNZmhyaklrUFg0aVBscG9WaWowbSsxRURQYU9iTWhzc29oeGlRTGpBYjh1bjg4ZUgvNlo4U25KeG9ERFk5SmpJa00yOHhlOUc5Qk1xRThDZFJpek5xWEYreXpGb3EraTBKWG1HQ3VuazZtR3dWejdkdzBBaHQyeVpMWEwxamdyclVwUDg0aWtCVmxqTGlKbUFCV2NPVXQ1YXE0ZTJGTFBQNElZd053Ni82a0JHaFV3OTJqcUd2enpTejJJWEZvU0drRlRoQ1o2SGRpOTVrM2hiVFIrVXlPdE5YeEtmM3FPSHRvRzErdE81dTJINlh2Q2U0T1owSXNTZFYyQzIyZjRYMFhSam5vTEk5ZGtBSmNtYVB6eUxiZ3JXZ2ovZGl6V0hzck56NVB6R0NDWjd6eXdoWk15azZSckVKNXVjWjVrNEZvc204K1U5NFp5SkZIWWFIdGhNaEpTTGdvSGQ5cGxwZ2d4TkZlYUJNeDJCZFNnOGQwcU0xUDlzM3hIVHI3bit1dkZzZlU1cUphZkFreWZBaS9nQytPTHhDdzB1TWwvWEoraWQzYnBkRzRWeFF3eUt2WmF4Q1dyUGFSSEl5OUtjZFI0M2p2OWpmeWtHVVR6QjlLanlGMUcwU2t5TUhNZVk1d2dBbWNFcDlCOGZmRDkyR1I0RlFFeFhBRC9SbTcweHlmOW1yZzBIb3dKK1k1bzF0cnozZ0p4NkVtK3BHUHQwUHZDVlNYc215QTdCTE1xSWlMOGlLeXZtRnpSME83RkpQb1VENWRaSjFlS240dERVSko0VW1iNzJYVEhxUjFxczhLc0hQcHUxQmFzMmpNNkZvVE15b1g1YVNjVHoyUlZKSDB4c282U2t4eHVNQmczdVVibHo0Zmo4M1NuSzFHQURYOFpKdHJZNmw1bHJiRjEvWnVTaTFCU2hWQWRGbmZCQjNTaDFTVzRLUXoybUwrWTRzdld3c3B6ZUdwNFc2cFRGS2RNRGpPeEh6a0pIa0FmTGpManFmK1QxQXhhOW9nK0NsN2dSVGk3MGJTV2pzUU05RjE5SHFIMUlkSk9vZXJMTVFUTHB1VnBGVS8vRzYvaHN4RzZzRnNuek1KN243M1NiSWl6QnJjcmlxSlFvdDZzS2UrdVAxZ09OVVZ1QklQbERKQTQ5YXRrdmFmU2RrUzROUit6Y2lBRnJ3b0hqZElzVlNKS3FEeEFWck0xNXVGSmI0Y1VJMVo1ajNXZ280Z0xxTFpETWROdFlLSjFQN29CVEdTQktaR1RxZ3VBWVhqOUZ0Y1E0c1NidXdBdkVLajBpU0hmR3pOWXBBek1oSVZFbCtPNXRWTGU0cy8zdUVkOUdzcmw2Ym9nUzVIS1F3WDNYSzhWbmo3bGYrNXFJUWlUU3pSbmZrRXBkeHhnVTBMQVpHN09TeGppSGtWRDJnRmFaMUdqS2hJZWRjZTdkRlV3YWM4cUE4VXQyNTB3d0g3TzRyS0hGRUNXRWhoUGZ5eU5ORkZXZUZyY0lqQ0I5UWtwWHV6MFU4MERYRmlyZXhnZ3Y2YkN2eGx6cnBZTDJBMDJIeWtIb2dlSUl1bTE0QVR5elpuS1NmS05acVlVSGtGcjZxTjIvbVBPMVdLMDFDOUNwd1hjbDNmTEVmaWNuK3FNaUZOSDVhL0pGSkJBRjJaWldKNUVQOG1HelBDRjlDRGxyMHowWUhydVArNmJBVUc0N0NOdzV5RGRSMFdEVGpxL0RxREU4VysvZmM2aVRCNHI5OTQ1WWJIalI3Nlpxb09GQWtwM0tuUm5pUkxkV0s1aUt2TENDSC9KZjl2ekhuWDRMZmRIbEFpRXVjT0FEZDZhYVRKbk1EVEIwRG5Mb1c5cHZBL1R2SlBvSDJHWU93VXlCZ0RrR3Y3VkxxUlB6ano5bklXeWxubldxSWxtN0w5WVJBdXVjSElsZUthVFFDZVVyWFAwV255cDJubUJ4emVEaVZPUHNhcDZsNk1ZTEhPNHhnOEhCQUszSjFkZ3ZCcElqY1lES1pleEpWNW1mOGMwaHB3NU9ES1R3ZGtLQ2VlVGV6Y1BYaC85bkkvRmxSY0lZeThzSDNuS0NRMEVFdWNWaSt1aW5MTlhHVG1aWFN1QjVqWUMyazFSNlg4RllETFNzN0czcWcrV2EzMC9TWlpWc04rdmJJV1BEUnFzOUhNei9WMmVYUnJ4Q2xHd3pNUlpUbnB3dXFyRDFHVGpMVWx1T2Y5dVB5Z0pHeGUrL0VCNkFrNVVDQ3NDV2UyR0xENWlaWDh5d3FHeWFQOUNHS09Pc1E1MDR0U1ZqQU1QUHBLbzdFeDhMVDN4WWRoNFFSZWlqZmFzTHZNS2Q4L2J1Njg5eStXWStTOElPOUxYVjdLWXptT095Y25iN2ltc2plaUJQQ1pnTmQySGQyZkxJUU9hTG9yUGtLakZaY0dSYU5PNmxwK3BCUFRNdnc5UUliWXVRWkJsaHU0OFZtVjNpLzNZMG03MUJDaFVXUjNjZE5TUzREOTZZQzVKMFk3WkZxTUhCVzZHOXA5cGYxRU12c29xMmR6WDJ3U3ZOWVhxZFA0N3p5ZVBMcmsrbnJlYjk3Y0JOYW83VTM0bEhEWGVGUStIcVQ4WHZjRTI2ZzQyU3lRWm1IRlJsSDJVWjBrb2hwY2dtN0xpMndBbzBJSE1yZS8wWGZSVjBIdGFyQjZvZzExS0MzWjcvUlVjcUt6RVBBN1pFSlFnWk5nQlpFMDJNRlQ3MDJITjY3cDUxNk52cWttMEdqeDgzd1FkUU1lcXhsbWw4TERLMFY1U2RUZG5hdEVLN0MrYmhpUTNDTFJCdXBWdVRlR1loSlkvQmJycWlFMVNZMXZkWFoyU0Z1dk5iY3JJNkVyR0pWOC9xSDFhY0RFdHU1OENtOUlZWGxSNFIvLzhGUytzaktqaUlQY3V6VlErOWJWMjVNT0RyUllUenhGSlliTGhwMlVtL0hLT25jZ0xkS0hqN3RPck1aZnhSNkNyVjFxUkFHaCt2RDVkTU1Ea3F2aDNSdEZJOE0vQis5NWdPbTQ4Nzl6TGpBUmtmVnljQU9xakpkb0JmZ1dqV05zSm5hZlRrbWM3QjNuSVF2L0RvZW9sOXphR1cvRGxwZUVISExTQ1ZBRnBQY29SRmJYcUlCME5JZkNuc0tjSzhHbWFOVmUxUzFXbURqUjlrVjJXallkRHB1M2QrZ1gzZWRqWjM2M2Y5alFFYlVoRlh0dVJYT1F2K2dtWUN1YnFCcnFVb2FnVWRQN3hqMEhJRkVaZzkzL0taMkNyWmZOOXQwQTZXY3BVSkJJNVdMeW9MbnFmMTFqSnh6aTdYUDdpY1RHaWZYaDhIUGRQd092bWI3QTFCRmNmWTJIMXlyZ3BROUxMMVdQYzhmNGRxZnVFOTFCTnE4RHRjRXFsMy8wNnJHazRnc055V0k3N0duSDlJS3dVc0FGbHJwVW1BM3p6VVBvam9yaWc4LzJDYmQzVGpzQ0tNOXd4bGlDTHlLUG5nS3NNMUtGa3FNNmJNRnR5eFlZclUyZWV3Y3hZTTZSa0xJenVDYnQydGpqa3JXa1NWb0lTNWxHYWVIOUFDc2dzQ0Q4dUJKVGcyRkcrak9Yd1RUU0N2R0lXT2lTUG1ySUtLY3FFSVNWdlVjTVdoSEVlVUtqWFRNZHRCbVBsOHM0V2lwd1RZYTJqN3JtYWEwUk5mN0lYQU9UNzdOR2VwL3EwaDBLZFdSbzVVUEVSVHVmZ0FxSGd0dW0xZFpFUHE2T0g4SUxBK25va2Q4TVhQaENrbyt6Z2tOcU5sckxRZXc1dWdpVkJJK1RTYUYwK05oLzBsSXBzQ29CUVdsRGFjVkQrVngzeDNhU1hUYmtwNlVSYWZCbzdyNFcwWU1KWUwwTW53Rk01bXpTQnZINDU5bUhBWjB5elQwOWRFWGdqVlc5L2dnZzJMeFJPNnlHbzVGVHBHUVM1RXdNU2pHM2NydGQzVTRYNENPK0tYNVc0NlRDNUIvWC9EcEVpcEZoV0xhRTZycFlPMHI0NEt3c1M5R2U5SDJkZkZZM1FOdlhBMXNXSE42V1IyNUhnUTA5MXUvRm14Y21UWHB2WGVySDBiNXhSaTFNd21HbXJLNFpBVDFUYXBvRDgrc216WHVXNHhmRldrVkRPTDd6azl4TnRCNTNBMytkSnJJemM1T1RCNjAxVVhTRnRRa1gzaFdhU25oQjBmSVdheHA5dzd2R1FEWXREQWVUVERpZ3JMTWhWTmZMVXBKY0l4aHJNak8wQW1pY2IrVWJhdWV2NmdBcEpiQnl6VlFSVFdxMDQ3R0dSU1lneHVrSG5sazUreFdUWVRpMzFjUVFDSjlJTFpSSjN0VjA1TTFBSWdOZWVEVzJIOElCSnFrelNsOW5uS1NhakdZT0Q3ZU15akhIV2JHNFNFVjhDdkFIOElldzZTb2RQU2xYNHNwT3liNE84WGRZUTJibmU5OGpNTW9sZ0JJYmM4ajFWZlBobWRQY3FWY21mNXFNalpjQzJWekdTTUY5czQ4NjNoWVBWR3E4Nkh1eTVjbWc2ekJ6K3FEVTN5amU5dm1FcjN5SjZrWmhGNXo4VWRsa0pkanEvNTgxTzlWdUNSMkIzbHlFQWZRb1Vab3Q5SGRWSUxhd3JleVJ4QXkxMUpscEUzVW9PL2ZpNS81b21rVXMwQTdHdmI1K2JzdGVGVklXKzlsK3FSMmRJTm93NDdzbUFpZHYwYkxMRXIveXFLY1Vhbmp2aXh5ekFRQ001Q1Z6cTByN3JEUjlNN3dqTHhCcTllQldSVm15SzlUZlNKcVhIakw4VDNsOHBocXpXR1pya1JDNW9pUE82QzVXZjU5ZkZEUCtpdHVVYWlFcXl0ZWJYMEZleXU3VTVMZXFsNWdCTVRkRFBzbUs3S1VPeUE1VHVXeGpHYzdkTjdrSktFWXBybzBWV1Joak1Bck1JR2J1dHU2dk4yT1NIYjZudmQ1MDhTNFEzNHVDUkt1OTZiU0FEN1lIQVNOVmh6WHY4Tjhqcm9ZZjVZN0U5czR3VHBrdm8zQlpra1dxcEYwTTF2a2EzampVQy9KdVp2dzlWOGF2WCtEOWJjaUlDbDEydnIvYlFKeERlK1ROOU1Rd0RKd09lNUhSV1pLdEN0SC8xLzJickhWREUzODFGRjNKSUlMalpmMjBVVEZMNE1Md21adEZ2M004OEJ2MXg2aEV5b2FBbFo1cDVRRVd6bHc4YkpCdDhvckFSaGlvZG9kdGR1WXRKQlNGN29jdFQ5SnpiZUtkb3phaWYwTEJXTC91OVJqYmVWTkxaOFVWNDRZZTZTejU2Vm44UWx3ZnRXTDAxV29QcnlpaTNaWjkzMFp4Nklucy9IR3ZHUW1IQUQrMnF2dUtRQXM4WTZ1YmxiK0R2aHAzWTJOTk1qc3V6T3ZiNm00WXRrUHpiaGxjdEthZGV4OHRCUXVvMHpobVN4ZkRJWm01Vm5FRGRHMnZaNmtjeWtZRnhnQXozd3JrVnlYUW53eHlRSWVZTUlIUVlUKzI1N2pCV0QweUpJaUMzUHFtb2hNelRDLzY1WFZnU3Nvd0cya2dubFI3cFlZMThuQlE4YVZmSjY0RDc5ckgycHltTTR4TVUxWmsvT1MxNFhpRGNsZGhPMGMwUmhReGlQU1k3MlhZeHBpYUtWWW16T2NFdkkxUHpRYTcrTFZaNnBCSXduOGZmV3ZocWEzOGIzSXNrVHM0UkJrWXM5aStpOS9BcWRBUWcySU9lV3YyZnVvNXRFY0Z5ZWZJOW5BVEpYUWNoYkJFUU8yQ2oza2FCZTJYKzgxbzk3QjIya1lTd2pPa2daeWJmNTNxWkZRNnAvTjBkTC9WbnVMMWNZVEdpOGs2ck1wa0tHeDRqK01jL2ZjSFVWTlhUS2h5TzEwRmt2SGlOK3FTYkpHZXBKL2FMWG9MWjhSRVQwQnNodi80aEFRZ3plUzd5bDBuNzRjZWRxZG5tQWVIbVEyQ3lYdk1NME1XcEV2QTJlelpJS1UrV3ZVU2FHcFR0MWt2TWxvZXJxbnF4SExmVDAxWWgybjNpRDI5RVduclFzeWplZGkxSTVTVWd2UUtCTTlHK29BYWkxNWNPMWNvbjJRRnozVUs3dzdaZ3pNK3ZQbWJrMlFxUjg3ZnpsYmRUU0FockxYenFWZkxuV0JBLzQrNWFDKzBCUk1aNmlYOWxIM1FYdEtVOUQwMUszSHByZGlsTDQ1Nnk1bHNsMzhWUWFNYno5aGswTGdxdXppTVkwMVpuejJXRTRDbEhHOWNGL2U3c3RWbW44OW9ORlVFOU5aMVJBYzk3S3pERVdITG9Ld2xDRzZMMjAvMkdqNy9NNlBEaHN2aFkrRk16WVJnK3YvMGpvMmdQVDBVVENmYUxCRFJWdktRZ1VTWVBNRzFkcjZveDdvaGVwQlVTMG1zSHEvVjdBNlk5V2ZLRGdTTGF0cVR6d2hPWG51WEFvRmMxTHNkbFYvTnY3WEhxZzVUQW9oWkdhMW1PbjQ0U3lZMWZ5UE1DeEwxUW14dmhCQzdteER5ajlEVW5CcGJqZEF6ckJXMG1Velo1MWJyRFZXM2YwQThvS0w2RllCZjBtd0s2WXhETUpvZ3E5NE9QZ3BaeUtIS0JZdkpYTWZzNnUwcFluRW4valBlVFZRTUs2dVk5RWd3dzVzZXRqcXdkUW13aTFlYTAvdW9OdzdRS1BvckNXWm9oRnQ0VkIrSFV5L09iakNEZHhyeUlnL3kwd1hHTXdGeWZ0U3lmMHYvRVNPVmFVTk9IZzFhQTBTUTBLT3d4L29xQm5lTXZTb3haYzdTcXZRYUhjeDNaTGc3STBGUWdROTc5OUt1VkdUZkdOZ1d2eklNbkhxTU5uQ3lDTEpNTm9OUUs5WEE0V2txKzZ0VnVDVVJFZWhLaitzekU2S2xhU3dnQVBmYjZKZUdxSXlCcmpKSy93TncyeVBhWUI5d0hpYTNBNTZNNXI0T3BsQXZkVmpPMXZyc2M0SThMQXkxenFxcG8weU0xaGZpeEhlTE5ERzZ1ZlhhWC80bVd4WXBxTDNoQkhwUGJub3g0OVAzamovd0dnZFpGYUplMUpUZXIwMzZ4ZDBYYWs1cUNJNlNWODZ4cUFkQUNodjZzajdFU3cwU1U3dzBsZUNpLzA4bGZZZnVjUlFIZHpqTzNKa0E3bHZIdzBvdU1DU0N3ZVArbXM1SGxTdFQxSExsZ1EvcGtMUTBIaURrdW9QdFRZNmZEVzBVUGxIM2ViS0pLSnNpSWxFd0FuV1ExRXhmUWhmczFJUmRiRU82c2d5Qzd1MllxU3llOVdGb0gzczArZDRQMlg3OFVQY1VzUml0YmlTZmxNZHMzKzVpeGs0N3dFQWJ3SE9vdXYzbDBBVWI5elpJUDMyaGgrOG4zZkp4M0xYVDR3cUVySlhSbXVmeWR2eUp1S1c1SWtBK3JEN0I1eTNoSkdVRnJmK2plOHgyV0VaOTNNTVpaaktGM1I0aFk0RTgySjd5MHo5em5XRVhxdG5HY2UwZGVqT0JrcmY2Q2JQMVZDaDRpeGhSdm1PWE85eUEwQTJYUXFlV1lOZmsxZVVrUldseWJSREJpRTVTT090anVkeE9wcUM2SHYwWFJxZEw1OC9kc3JFSXRWb3BwdmIxM2w5TXJaUkt6T2UvdnR3OUpQOWFBa09hN3JhNk1iVC8zWUU0TGxFSjV0aWNLV0tlK3JPR2liZytOMjBWeDZWZzdKM2J5Wkc5K2hJcFVMblpXSDRUcTNMbWxNQStvVWZnQWJielBsM3R3YkR1UW96U0VsSTk1S1NzWGFCV2V2VXhJV1BRZFkrNGVvbE1sVHRMd24rNTFTUDZCV0ZFaWlvWXkrcjJSemE0T3FLSlBNYng3dDBDWkN0cE1LeFlRNUpDb3diQUg3SjRZM0VoM0MwNGoxSC8yYTdxSDNjVm8wMW1nMEtqVlZSNTlxRU5tTExDblE0TE5NUzNpMlhzaEVLN1FBSXZpNEQrZWdaUHBNVXl3b2czcyt0cVJpYUdYSUVNRnAzcmQzVHV2TFhWVDl0cEpHeGpnUUxHTUtYbUdMMU1Wam9OOTdieTJOYU9uMEpvSWJPUXFlQklIVFZiQllOT041REQzWFArclN0UElmVmJ1SGQrOTBUSnBHaDhCbGZWMGRMbmVLMndETW5uZFZHVnZRTGh2YVF4dTZzTDNYc3Z0eG1RemVGV1VTSExlQWxtVGM5eU5RS2tYdE9KV1M5ZmFld1M4eW90aVhkSlE2RUkxdnBWT0hnaDQ2Z2xqU2xsVkRSeDlxbEg3aTJRRlUvZEtwYVFFYnBBRlVCSS9lU1VHYnBnVDJPUkdjVUdYWERXalFKUW8rbkNrUVZuSU1SVUNQMzY3b3M1SXc0UmIzTER2T2krL213Y0JvenpVYTRXa2pWY1NJVVJLTzNSVEZDaVk5ajNPNkM1TUJTNlkwV2JCb29DMG5PemhLeEw4eE1JSWFNL3RueUV6SWRsQUJyejNmOVhsQ2lRMGhoK0M3L2JOcDE0ZVV2bmpjSFdqQk9TdzhFN0JqemVYa1JRa3BJdVpTT3Jpd1o4UGlPTFp4Q2tYRk9RNGhiWGE0VHU2OWxjY0o5SGQwRjFseGtnNVFuQWhoZng1V2RjVGtCSDNTaWJCVU1DTFBiL2NZeXB6NnM0R0dETVY1c21ZaWJsZHAvL2o5Z2JDRWhxYW5weExzb2V4T01pazRTT3Q4Nzl6MjFpeis4VjN3Z0c4Q2ljUXNteGNzcUNjNVFVcU9aaG5wTzRxQUZnekhGK25veE44MzVQNHhmNUVzT2NQdllXd3R6SzNXRVlWR3k1dHV2eEU1V1pCMjQ2U0dJRGdlQzRzTWdlMEI0cDcwVHNlNGI2TmpsUEhXKzkwR21xbnlTcVk4M3IwaWxhZXc0NnFtd2k0UnptT2NQZWhibjRZUENvSVNqUTQ0UlVSVisrZGZVNTN2Y0toa1NqNmNXdWg3NXRkU1NVTk15c0Z3b1ArbE4yZ0dUd3hPZnJoYTl3V3hEUHBpbWhFQlZydDZkY0JJdmRvVWJDTFREUURadVVPVlZoWlA0c0FUcXE4ejdBaTBTVG5HeHpLbUFIRyszSSsvdHZyRE4vT09USHdSNlc1YVdTUmorTTV3bVM1aGZkdmltbHVzMno0cEU2UlYrbDZzY1NFWDNYakZVVmdiU3V1dWZsbjRxWmZtZ0J4TnZJWm1rUHRNaDRXSEF0dXFSVmRnRE9Ma3NxZGhqcWM5anJOVnBSc1lMNEw1ZlhhS2hOWFlOSmZUb3J4YmFvU3BvcWo2WkVwMDV4c2M0eTRRcnl4N0JSczNpWXZ1SFJiQ1VzaUNQbW1HZFVQWERuNkg3d29Faml6MVllcmlINk5QRjVhdTVhVnJ0Y3cwRHZFZ0xMS011VnE2UXZ6RTFtdSt4OUFGaGhJRUUzalZ2ekdXczd4K0lCR0oyaGZHOEtiNTdxNXNEc1BtZGRyYzBzMmRvYXZHdDNqNTlTcEtrYkVUQVZ4Y1N3d0hicEFFc1lUTlBNMUtoVmw3RVBwUXArZ05vdHlQeDdoSTExeEc0N0NyWUU3KzR4bENGcGFEd3ZmOUZXZXNjakU5cU5yY2dDWHZTZW1lMEdBT282UWpzdHRXUWNSZ3V3V1piNk9HMVZQTjJ4WmNmeVVlRUdMSGhQa3J6aUREZjRTSE5hQ2NYWEo5Q3RGZHlSTVZ1ZVpOV3Fhb1NLaHBGSTkxTU1MU1hqdTNwR2JTekpsTThGUGYvb3haYlJBRHZsWlpDeWI4ZmJiNG1RVkJaWjNHV1Y0aGo0UENyTEExcVF2RXFzOVhMc1Jub2FsOVdhU1FoV1J6TEptQ3VybkdHUmM2d3h5QUFlanAwcEFSNzBrME04Uit6aVhwaFRiU3o1alUyeHAyY0ZlMUVoZWdycVBxakZBdFlXYll3c205WDk2OW9ZZjc2UlNWcEQ1RGZJOGlEZkZJTEJrZnZuWmFadEhpa1EydGZOWTFUMFFPWWFmWitkZmlRaldaeHFyRHhYRFdiYy9qWVpTYk96cGdKMEh2Qzl3b2RPZ1RrNWQ1ZDlkbU5ybk0wTEg4YnZ0STR6Z2t0VVpkZi9Ea1lNMTBFRjh5TWhiRnF2cE1UaStUYUxCVU5kOWFMU3pTR0FxdTQxeHNLeHNFWUhGUGh4b3pZWk1QQ2FmYzRVNXQ4SmE3azM0Y3piOXBUc04ySkZud2w4QW1aU3BJMzlLekJvRWNEOGZ6MENBY2lvMktsYURJaFBGOFYwSGtFYndjMmMwbWtwQmF6aE9NSTFkNGN4bktHMTVubEoraGFQNEQ5Zy9IMXo3aklFSFM3ZW5MOXN0K3IxOWlKcHFMRnVKaUtEMk5UN0xYeUJ6YUFjRnhJSi9mbzRyb2VaU3ZIVXlmZ3FValNWY1Bpc3pFQXVrNEZncWp4aWgrbG42VFpXOGI1c2JESXZyQjFVbCsrYzFCNjNYYkZnSGRWSlRhUlB6SVhlaDVmNXUrUVl2ZmE3cEh5UVYwWlVJdjRTbmZGTXZUQzBnMC9mZGFhQmQ5cmNweHUvQ0JwYm9iS1pnQ0l5VlJEWkdkUGxaczhVR3l1NytIeGI2NEUvazBZSUl5RzBkN1pTSWNVMWRPd3lBUXQyNU93NUI0Vy9vVWhnVStHZitxQi9FcWYrVjExK0d5bEVraXlHYWcyc1NhYm5Bd2dhcVRyNTQ5dTdVU1g4Rkg2RW5LTHYxZzlqbDJ6SVU3QzZHTTNhZURuOGtQKzlhQk0wQWdybDE2NVJWNC9VSGFYUG5yQmpzM1lPSGxyTUs5anppTmt3d3Q2K3JDNUZQUHZTbTJ1VnVPUW91RDQrUmsvOFgyVm9UKzhiaWpCOVBOcGZzT3NOaGlTT1ZnbnR1N2R6ZnpKSXRyYUZFeHMyeWxQdDB2YW5UZ1pKUDNTSXhQdlpzZ2FEU0JObXhJaDBLUExTK0Vaa0oxWHkwZ1k4V1ZPWkRiWUY5djBHSnRhNitHVXk3ZWs4bGlzWXVtSjFueXc5ME5GNW43TDZIMWFGTVlxQS9XSTJDT0pBN3BXYWY5VWdmNXBuaUVUSUpOeU5YdG9ud1pPTGVDRzM4MHAyYTJtNUZzNFdESkliVkN0a0o3N2FoK2gzSE12SkowZnpXOE9YZm5aRHV6YldCOTM1bFA1enIyK3ZPYzdDTDQ0TGpOdDhwMmRlSkpLZCtkOG4xbXdLd3hXeFVqa3hKUlZscElxd3ExYStTZmV1MW9OR0RhT1h5Uy9MVm9pV0FpNC9SRkZLNzdqOHNWQld5VGVxYzEzRENZV0tkRWJIVGdFY0lkdEJld20zZnZVOTlWOEo0Z1lMSmlqZGlzMk8vRCszRkJ6OGtHL1N3QVh3anpLZ08xVG1YdUEzc3lMUHh4Zm5FVXh0dGtVUHB6UUpnQXpjTjZvNzl0cEhyM1FXWDNUVnk0VVNLWkpQWC9HNy9zRnY3VEIyUkthTTlMdkc4NTE4VVRsL29OSzYvbXFNcFNPcXN2MHhSVnpOanVtZ2FtcXovZTNMRzNlMWxrclc1U3F1cWxyREpJck45MEFQcm9qTzJoc3ZhMnZBdjFaTlBiSFZmdkg2SzhLbk1tRGJYY1pJbVMrWUFYYWZkWExWSUxTL1EwTVNLdVJhTFBRQUJUNkFzSDFTcEJsa2lTTFh5aFQvZ1Q1SWJmRDZaMUp4MG43bDMzbzJ1R1c0bGdkOEJSbjhXVWVFSEJIRW4yU0NYVlF3bFJFUXR2TjdpU0MyeThxU25nRjR5dGMzdmdPdWNyR2NjYXVlYnlVbjlzZEtta2hNb20rWEhSR0xnNHlyN05XL1pBcThVRENUamltdzB1bmoyMDROWW9paHRaVE5kWHdnbUNwcXpBNlk0YTNTL2JyYUk3RkVYRUxncGpWU25CK2Rxa3lGcTNUbnkyRzhsQXoxT3ROMFRaZEUzd2dicUw4WHRzRTVVdDFOYXlUcW1QTm1FaEpWQzBmNlpmTW9wMEhQNVZhd1R4QStscTFYb2VSQW9JR0gwb2p1Vis5TzEzc2gyVjJ6b3hqNWpWeU5HdVpEdHFaVmxFZVNJUkkwNVBWaTduWmZLdytFdVQ1WVRrZFgvcW54L0FtUVhBQkpSOG1FYnQ1QThPYWIyUnFNZEcrUDB6dkRJMGdPRG5HRFNPMnc0Wk9yRDF6aTVMbllhSWxqaWJiT01ocERXY3dzZDZSeTVlVW1pTFEyNE9wYUVyTzZhMy9zWUx5Ym05eE9KTHFmbjdETmcvNVNLQnhFZktOeXlVWVA0S3RrU01RSTVYbzdkSGNJaHFINGwzQ1JLL2dCN1d0RlU2YmowbVJlTkpJaXRMOGdyWWJVeVpwcUR1TURUNXM1V1FzV2pPRW1SU2JNaUg3SElrRUlQdlJ1MFd4TW5SQ0pLakdGV2RsS0dxSzk2VDdqbHNFSENqc1Bqay85VkVRNFc1cUIydFJBRkdKNVlHZ2JteVl4cXhHeGR1dmtOZGQzSVpLY0lidnRFdEg0WDdhSGV5VjREY240d2tFelVOUlJoSVNNNTFBdjVJMW13aTJsajNEUDhkNks5aUZ6TlZEQ1NiK2ViOXBCdStTRXFZcnZGQzhXS1NpOE9jWkRqNTBLVjg3MTEyMGhnejZuNk9aeTFLT2g4T3pLTnVDS0Z0OW1WbFVmSkt6RDlnY3VMNTNxK29USEdHSUtGejQrNC96TEMxM04zbDN5NEZuOWR6TTAydUd5QkdvSlhtRjNqcndXOU9ndU9zaDFGVnlrRTFzdU02a0MvZTAwNVZSbmdrZ2NuMjl0aXhiZkdTeDdrOEp6VElkKzV3VFhFMUhnS1hDdEdsd0E3TDZGeFMrUlVHR1AyYXoxRW05MUQ3VEhBQ2pqcWxWZG9ET2x0UTdZYjRTOG40a0cvbS9DdnRGZlFCMGUvZS9KTWdJQ0xHS2RzNnY1VEhFTkI3V1lPZEowUDVzM0dRemRiZVhqVUFHNVkyV0NVQnM1TFo2eERaenYxTDdqZlVIcUJibW5IVzdVNGcrVVRZQi90VzdCMFlhMEpBYnB6V0ZTb1ZRSDZDYlk2cTlmTThjY2Vsd1dkeGVXZGpabStUY21CQUhwamUrZW13OFQ1bVVnbDdPbXZrczdEMnhrMDQvSGp5bnpWeUJOMmRJM2RCZ3hUa0Ixa2VMOXRNTjBXZ3lqWTBkZEtJOHBpZ0hQOWxPYThoYjdGMmJaSWEvRnFTNkpKUFBIbmx5UGJWbCt3ZUlHN2o0b2NtV0gvT2t2YVQ0cXRjYm5hZmsyb2N3T2tqU3FVb2I2NmVoaXQxVURNd0tYcmVEMlI5Mk1adWdUSE5lL1BXQVplc0FOZzllQmJtMnArNGtxSzUyajhNVzNBaHFhZmZETitrSzE5NURVTTRGTFZZbThCUWhPRitPV29NNXRURDhMSW1DTlJlbnV0YlU2cVJ4cGFNRFhDQlUzNy9LM1k3ZW9iY2cvSWFaYUJ1dzQ0RnRlSTY3SGRndWZrNVZxQ0RqbEs3akRCVXRWcTA3aHBQSTl5bVdXL20zbk5MUWx1c05HRFNCTllYT1VCRFJXTm5IaXJhLzFlbzlHRXdWZ3BYbjJ0RzFQVVV4VDE1cC9mYmZHWEN2cHNqMFFsendFckMwZ2UvT3Fsc2g3RTBRaHBxREFjdmxCSk9pWEREL2J2MDFTa00yNjlybWdoV0hKUFVibXBxNHRyajdINmNDTVhNSXdXZ09MYVRYUjB3M3RhbXpKcFJlQzhGWEROd2t4U0NibWcvYWcxN0pkUHlwdHo3bVIzazZLdlhvcjZ0RkNmRXY4NVRXN0NEV0xFYXAxQUMxMlltK0xLOS9DeGRLUG5YejlRejR4TlhHbjNzRzF3QWZ0aGlmUWZqRHlpQ25MbzJ1aHVNekk5eUt4SDRQVVR0NTJtUmVNTG1uSEZyckxwRFljUEMrY1U3Z2U1NWd1WWhHdi9BTkI5Mll6b1hySStIczZnZFhubmZFOEdHaGZ5ZEd3dktCS0N0cERlY0dudTQxTXoyOGo5L0xUVnRTVjlXWkVveEFOTWdQR280QkRiWTJwNjlpeFlHUVdBVGR5ZzlUUkRBSzdmL0xybHViYXQ2MHl1Vlo5d2N3cVo3TkJQNzFtWDZORWdkdmZLMUVnTW5rWnpzRFFsL3dXREhkQW9PWUNvNHBLd1k1SS9WMjZjS1RPNGFNWWNWL1lEZGdnbE90YXMyS3RJWEJKQWNnb3RzVjRZZkYrQ0RONFQ1V2RYODA4VmRYaDMvVVhMckFkY01ERjNRSVhqMUh5VUhJT2tYQkg3RFhJQ2JKdDllTmlvd1JYaXVCMGQxSi9GcWpQRmUySWxOZFhud0Z3cFJ1c0I1UExTdjBMay9BZEkxZ1FtYW84d3dMbW5vaC9MOXJpTWJNTXNXQU9JKzVCNzFkK2xHVEtseHg0aFFuNGl4UmZlZHlaVVVzUmNwR3JnQVMxWHFDS3pnZ2wwL0xGdXlRcGU5QnNndlpHa0VIUTRFTGtsNmJjTHRpSForN3VGeG1Sam5WN3Y4UFAxV2h1ZzFpZ0lUM09UTW5tYi9kR0pQdUdLWTVmUmR2V29hdHhmTlUzQUJpK2ZZN2VIaVBxQzBnUURwQUMxOXR3VmZXQnRCdXIrU1QreTdmem1TRTVRMEMzbWNwOC8zMVhJZHFtN3NFWkpIdEZuWEJnYVR5RytmV1JHQVk3MEsxMElCdktIMlRFNklNem0xazkyL0NuMnBheVR1cEtUdG9qZ1AzdWFXSWdGVmdWMGxEMFdHUjBQYW5xaUt0ckJGd3F6bnZiL3J6MlBncFNqV2QyQkVTTFFweFkrNnRtS1habmp2WTl4ZlIxMkNROG8vYUt6MXQrWHhDU3p5MHVFNWYva2FGVUNyd3hqTDhnVDdTRVVKc2hwLy81L3l2UEZKSGdKbGdzdlhwK2dSUUNTenordlM2cmwzQmhNc2JqL0h6d0pZejhHc1dwcE9RREdWc3dsT0hFYUZFL3FoSW1oRHJ0MkRVZk54dHQyMUdXN0t3SlJuOS9tdFlJamxubndnRVNQRXB3b0x5VHJ1M1NzVkd6UnhuWkc2eCtCaXNlVXM1N2xUZGIzSDhLRzdVUGVIMVNTank5d1pIRUxuYXI5eDVjT3RPUjdsT3Z5aldtNEFiMThRK3FvTXh4TENGaXQwVjhTbU91N0FVOFhHWTNlU1hiNkx5K2thUW1Ea1JsT3N0Z21jaityRDM0S056N0xUdkxMME8xWjlKL25DanArMWZsT0ZndGJkN1lnMHQ1ZU5yUHVwcHhZeEpmU3BuSlJOTDRTM1lUZmZuVit4K3pWc3Vpb3NlRVQvT24yd05pL1RuTDJyQVFJS3N3aTdFcjNTdjQ4RC8rUExzYTJXSk9TazZEcWNDTG11c0lMRGl6MEZ3S0VoTWV3cnh0TnlNMklBRTAvNmhpb3BJUW9VZ0M2VThDTGlyaFdiZlZpYlNuQ0dabEY1dXl3SWNhVWxjRWFZUC9ldm9rYmkxTlNxdU82MlhObldSNCtmQjNNMU43TGFJNXB3ZEhZT0tFamc5T2FTaVR0RUR5cEtHT1Z4WmhkUVMwakV2WjQ2Zm9OUzRTQnB3WmZQbjYwcDZwUWxkTlVtaW1oV2VVNUxVbkVwWllqUEpVNmhtQXNoNEFLYUxGZkpBTnJaOW91NDI4eW9FSUZ1aVk5VWdPWWtxdFNVb2NXeHlpanhLK05UdHVEZGJoN05KY3lMSWw2Q1VCV1FqWmlMMzRCazBRZTN2bVQ5dHBJS3VzM3I1Q3ZFZEV1NVZhMld4bThDUUpUOWJFU3p1RkJlSDBRSVJ5YktGQVVWcU5hOXRDWHVrZDFqd0xYWUtXc3VNdUZkYThSMVVqVkcyY3ZBWitSM2xCVituTGtzTDRUaTZsdWJYM2hLRmNTeUZzRzVySzlwSnQ1bmxTR0lrQkxQL0hGcUxML0tYMFM5Nk5kT280Q1MrR1lQQmsrbEJaeHo2WWllMTJ2dlVqOGw0dDFpay81UG12YkxPVFBDY2FvUGVaN0FQVVFJS0ljeGNOVURpbjNSMW9rYmVBVUd3dDdKYTNHMG50UW9rQmhsYWppc3lYZXFiZlBMclRUS3BUYXVjbEtwK0RHZHlCc2J6RkhFWXRJcVpubExlNXdqbHVGL1VJRDZFZ3dXUEdqMEZWS001OUpvbTMrMFkxUVRiK0lLcUhadi8wRklFRXVWSXRsSkhTaXhkemEydzBVTjgwSHljL2VVR3Y2U0J5YkMvRUVzOWNPY0xCUjFlZVFYWGU3cDdoZklodHh4QnJHaGs5bjdqb20vNExYRjEyNVd6UG1NQ1VpTnlFOGlPN3NWU21SZi9pU05GQnZlWldHUGVDaXJmSjhhNDNmazVqQ2ZBM05QRUp5TUFhbXUzUTVpbTBES284YW9uV1h0eWU5aUU4dnJhaXhsVlRBR1NYRk1qUDMrWGlPRTlqcm5YVER6QVJudDcrOWd2SGN0UXBhQUkwemE2TjdicTlSMWxiNTVqSUx3bXg0SWg0T0EwSzEvWHg3QjlqeXRQRkJSaEVPOHhxWExoeG90c0lSam5HUnZua01LL0tKMVloRTlUMm1ObWNsTFlnTVNuKzdkemlrOEJ6b0h0K0VjWHN0Vjh5TnBUc3Bxc25TOTZBVHEzQTY2TmJGNDQ5dzlKcVZpQnQ0Z1dpN3lWenQza1I0WFNKOGlFQjVhbk1xRytFc1N5ck1RVnYwc01lRXlzR3greVlzNkcyeFBKdzN6cVRxNFJ6RFFYUGhZcmEvVk1sdDdFOHp6bDREN0wzSFMza2tXZjRaa21GbW5qY0VOUFFka21vaGw2cC9ncWtPZys4TWN5ek54eGI1RmwxOURzU3IzTVR1U01xaFNLRG45NWliellDRWRyWlhKaUthcXU3QkZCdWp1K2pTT2JPUGNob2cySXNFL3UvM1UvVUsybW50dlNuRDBxTmtQWW9SVHNrQm5MSjNOSmFtTDBWNHNFYnJ5WDhOTXI3TUtNSjAraDIreE1LWTRLRVJwdlVyZDBjNkFCWFdIcUxkWTFRVHVnQy81ZGhkb0x5MytLd2dHNUZuTDBNWnc2cXZPdkhrS1FSb1FyY0tMdXdVbGQxNXMwNVF4dXJINjdBOWVBcjAyYS92VVdOQklnUDZ2T2E2OVpadVpLRWxXdHRJZXJSREdJQWtaNTRmdzdIQmN0U1p0ZnNwUHhhbGl3Yk9FSC9MYXhvdDNaUW9uenZYa25TVm9kelpIQTFKdzdCY05Sc1l2bCtLSjBZNnBNUlBwSWJhTi9RU3VIdG5qVW9lait2bFZocTUwMjF4TVVQS3hDSy9EOHJTUmJPbWR1SEc4NS9KcklpbWdvNXdYV1A4M2xMdlJheHdDeGVUR1Z0NDRmVFVxc2ZVQVJtUWNTM2Y1RGJIUjlTWjRuSllJRXZjQ2pJcUxlekozSTZTN3hCb3A1N2ozWnlNUVgwWHhyNW1jNklVbXJsT1hNOWZKRzVpRFpRUTlyV3NHWjBZMjZHelRBRXNENnBqUHVEYTFYQVQxTVJweHlaOHpONTNzbDFZRVYwRTBFSHZacWNuQm5xTVRYUmg2ekM5UHdEWEVrM09IczJ6TExJakJoWTUrN2xEeHAxWDBxY204WHRXb3JhdDMzbVV4K2tFRERnYURVZHBjbFFxL1pNNm1NWW9GNDMzbktiQ0tEeENvenVnU1BWYVJqTlBvc01EeThGdWp2SUpTYjc2M1h1QkdCSVlMUzl4K0haaFlpVWE5eG9kMHhLVjlhUnQ3eWN6V1dsTGdmSzhxbjRmVUxITUJTUDQ4bS93VFdmREJkVEg4dURBS3Q1V00wMzMrMmJDcHhEaG1adEUrZDdYUDY1eUJUT2Y5L0VXYUNHK0dzOS81a1ZiV1MwSmxmb0RINlNpMnRWQ3pDUkdmVjBYWkFVV2ZYT01KNUY5ZGtNYWdid2FlcVZxcWJWT05EUUdnOHpJRDVNVVY3SWthemRBejRKTE9Yc24xUnVabm9aTklHVjJOYTE1K2RSS1lVQW1YRm1rV0JKcFBNQndUOE40YmQ4Vlp3Qm5obTNXekg5UzBzYnBvUDBzZ2YyT21QdlE2c21NeWZrVksrT0xqWFl1Ym10aW9BaGR3RGI1L3BMUmczUEd3ZkhFejZ2OU9PZTRBSzhpdzJjbWE0OXRWNDRJbjhSYzlqR2NxU1FsRlhQZGxDODM2NmtlNFUvSVRGeTAvU1FCbDF2V3ZHazQwS3ljd1dHYUxmOGNDdEVpLzRYMlc4OTYxaTZsWW5wZk5RaEdjUXlDOHMyb0lPVytQdzU0NVRocTNaQkV5TkM4WURyL3B6Q0VtQkk4VTNBNElpUUpvSGlEOWtVTU5kOHdmenlzQzJLcWM0T0dlV1lzSnhtRGV2NEpuNEhWK3ZxcGdONnh4U0VNQUJoUk1kVHRlSGlKQWduUUVYOUJSMlYxc05xaDVFY012UU5ZWWE1K2JibFFuN1JsaTFVRkN0UWtQNkVDbUdreG1QTmtnMkNHUzJtbWYwL1dFdVRaU3lQTXRiYnJuZnRQZ2xlT21KM2pTbTBtMUVVOWZRSFFvMU5adGkrS2N6cEo4bVNZSVZ0WHpYaDRyTkpjTDNGbTdCYmZ0cGptajVVbnVEcFBrOEh2cUtPajJER0p5azRSME1kMXg3dW1pSDBEVE9YYUx3TzBFSTk0azduNlI4bmZxaXdla2dVUVoxclJlazBIVmlNNVlOMEpMV3A0ZjROUkU4RXJjR05TSFpkNTgrOUt4OGxta2M5b2dmUW1YMHJYMWtCOFFRek5iSCtlVkRlZTBqT1FOVWdRY2V3M3krMFFiaWZYcnRMSFhESXhzcXNlajQxS3o3dmZjUVJFMXpVblkycGhZTklMSzhhNjU3enlITk16UGlSaHhzMjhzMUpYMmtpQ01FbG91Yk9YbmM4QnpVK243TE05d3p0ZjYzZUZXTi9lV0hYVml2U2RDV2c1RGZXc2syQ0Y4YUZKck9QMjc3UUVQZGtXbE9sZXdDVkVrTGp5ZDV3VW45WnphS09KS25EUURMZmxpaVJMVEtsVThUT2VRajhqT1U4RmZwTTl0YXlKVERweHc2c1ZsWnVKUkFJTGZ4bitRQUdJQi9XMUZHRGp1dVZ1NjJoRkRCZHZ6VlNmZ2U5NUViZjlwY2xwMEdycFYzUytnd0JXbjVKN2FHaWltL2ZSeUlON1lWVlhKc25BblZlcTkwdkRkQVYwWGVhclRxalQyQ2svQU1rQlc2VC9scy82VlVWbkZXczAxd3hrYWhLUjB0Und5TFJLZ0hlZm0zUldpZS9wVFZRcFVNWncrLzdvelFTVys3dnVaZDhsc3ZUMWlYNXJ3bHBpYUZuT25EYkhzcjFBczZ2TEVUZDVIVmJjQkNHYkpIY1M3YXg5QnlkNTBqZFl5YWdVdGpBYUhZWDhyeXl1Ui9iRGt3MW80ajgraFhNZmJ6eStDVm1ncmZSRHlsNGRuKzVMeHJxUkFYTG9ES3BRUkVBSHFkTFNzVlNKaDFzOEtuWi9Tc1VWcTI3Y3ErTzZMTVNCbWhUNFgzRTc1MHJtV3dDc29DcmU2YlQvL29GV1lBTGpwMlNiY3huVUxCYVR2bllESHRmRWJPMW0vM2M5bkprOFpPNUtIUVRWODhpdlRXTi9TMkVYd21pc1RQZGN1cE1ydkk4ZTQ4UVpka1p1OVdIeUtyb243TUtoR0ZKdzZaMEtaM3RsZVZydnZKbzg5c2lVd0J5UFkrSHM0Z2tLUEJRYkxRT2FlZGN2L3hlTStJaDhybDFlSEVDL0M2NXhXVmNpVG9WcVNHcDlIZmJoVnpGU3JPNmtCbnY3bUp3blJMdk1Fd3FpTmFua1ZkSkpNdzRpY1UzbEt5dy9lY05TV0lVZGRxbGJUaFlNaXE4bkhqUlJ1ZnMrMjhjcTBPSTl6aHB2eEZ2RmdTWkUvZUFZdm0weCs5bFpPK0VIOU5rQm5nYXFVMU5NWWhkb21iTnV5M2F3VU45cDBtSlEvL2U5TDY1WWJTaGdvYytaVWxOeStjNkY2Z0RFSFhWMEpyemV2UElaRkFlMlJ5UmEyZE5xekx2aWhBQU1Dc3pZdWVxc3p6WFJrU3lvYng1K0xUTEsyVjNsZmczd2JTOUR6UDNRVzdWSGRIYmpaY3R0UVJ2dGpyR3ZlSm5ObjJERTJaRElidmtDclQwSDhSemJHRGRtSXE0UDFleStob1kvVzZOdVpLT3o0ZHY0SFVOem54ZEtWMVdmM012cVV2MzVyMmpUS3ZwUFdCVVdObTVmeXRYL1FKd3A2cWtJT3NTeDdZNjdCU0NiQ0RWTE04L1ZjTUcrVDBqK0lOcmdMOXNmVDFJQ3RBQ0g4QkkwRzZWaVVaUFZ6ekNtUUhXMm9WSXdaakFvRmw2K21lTy9wRDh0ZU8xRSsxeTAzbUNwWWZXOVM4cWh0SDJHaGxGbGViUGY0TmJlelZ2OXhiWEtXejB4ZXpSTlFXcVVxdFlSVFVidXpLN0tUdmpHNHJRSGZ6QnBWbUs0d0RMblNJd2RTelRTazFmUE5lWTBXT3BQWlRMbHZRNTl4d2dmRnJiMzI2dlQyaFMxSkFaOUU2c3VqRnRLVGlKN2J4STZvNGNCaERhWCthZFhSRVRoaFIrTXdBNFRxRDdyZ2EvbzlpWTdkNlRWUmUxNENTMlMzaVNRc0QwUjZBcG5oRy8yV2EwQTBBWTJOdFdUam1hYmRLVStLZ0lSRFA5UlFZVmpYaUYxcUMreHlOVkcwM0k5dnBtRXBZL0cvekM0bkxPS2dYQVovdVRpa0hJOUFmYmtoZmdmZ285YXJXYml4NWVIN1dVbzlSUXlnRHp3Q25WU2piWGM3TWloRXVmVmo2V0diSzk2M3B3OFZqWTNSUzhJSDFjeTJ5WmJJY0tMTzVDZ0FVY1hKZkYyK01jbkRMS3RYeHlaYWY3U1BBNktKcSt6RjJOSHlmb2VUT3dIaEdxTmNuSFZyMWhUNzNwY295WHlmdkNZQm5HMUJwL2FSOXQ4aG9JN0NYTTNVWk9pc1dHQTFTSFoyamY3azlHbFJucDNtRi9jMUFWK0pqdlVzblpyc3liRU9RSmcvZG4vOWVKa3l5a1FIamJGNTZ6Z2NQWDZEZE1HMDNXS1VNbFl6K3VPWis1RFp5OUU5TVpPWjlHTW9MRmRySVBQUVFManYrR2xDTXBveUhQWGt6SU9EakhBSUQyUHJuYVJwcVdWSGgwcm5pZURJTEtxK0VtcmQ1Um5qZ0U5cERVWFdUbUhhS3VxcVlsY2dFejR6Ymk0NmRiV3JBQUZCanNRcTFyTEhJaVBKRWN3RkxDT1k0Sk5sWFJYUUpxQ1VLWGsyZDFSU0JHekRQNkhEU3BvODYzQmhWUkZGRjZ1SXBqUVY3ajVlYkZlM1Vra08vK2NvSW8yQlRBY2dCcU90UTEzNHM5YTRRSnZvZnVxQllNR09CTXNXWitzbi8yQU94RHg2U2ZBbkRGR3c9PWA7XG5jb25zdCAkMDYyNjlhZDc4ZjNjNWZkZiR2YXIkYmx1ZW5vaXNlQml0cyA9IC8qIEBfX1BVUkVfXyAqLyAoKCk9PlVpbnQ4QXJyYXkuZnJvbShhdG9iKCQwNjI2OWFkNzhmM2M1ZmRmJHZhciRCbHVlTm9pc2UpLCAoYyk9PmMuY2hhckNvZGVBdCgwKSkpKCk7XG52YXIgJDA2MjY5YWQ3OGYzYzVmZGYkZXhwb3J0JDJlMmJjZDg3MzlhZTAzOSA9ICQwNjI2OWFkNzhmM2M1ZmRmJHZhciRibHVlbm9pc2VCaXRzO1xuXG5cblxuXG5jb25zdCAkZmY5NDM3ZDljNzU3N2YxMSR2YXIkdmVyc2lvbiA9IC8qIEBfX1BVUkVfXyAqLyAoKCk9PnBhcnNlSW50KCQ1V2hlMyRSRVZJU0lPTi5yZXBsYWNlKC9cXEQrL2csIFwiXCIpKSkoKTtcbmNvbnN0ICRmZjk0MzdkOWM3NTc3ZjExJGV4cG9ydCQxNTZmNmE1OGY1NjlhYTA5ID0gJGZmOTQzN2Q5Yzc1NzdmMTEkdmFyJHZlcnNpb24gPj0gMTYyID8gY2xhc3MgZXh0ZW5kcyAkNVdoZTMkV2ViR0xSZW5kZXJUYXJnZXQge1xuICAgIGNvbnN0cnVjdG9yKHdpZHRoID0gMSwgaGVpZ2h0ID0gMSwgY291bnQgPSAxLCBvcHRpb25zID0ge30pe1xuICAgICAgICBzdXBlcih3aWR0aCwgaGVpZ2h0LCB7XG4gICAgICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICAgICAgY291bnQ6IGNvdW50XG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLmlzV2ViR0xNdWx0aXBsZVJlbmRlclRhcmdldHMgPSB0cnVlO1xuICAgIH1cbiAgICBnZXQgdGV4dHVyZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudGV4dHVyZXM7XG4gICAgfVxufSA6IGNsYXNzIGV4dGVuZHMgJDVXaGUzJFdlYkdMUmVuZGVyVGFyZ2V0IHtcbiAgICBjb25zdHJ1Y3Rvcih3aWR0aCA9IDEsIGhlaWdodCA9IDEsIGNvdW50ID0gMSwgb3B0aW9ucyA9IHt9KXtcbiAgICAgICAgc3VwZXIod2lkdGgsIGhlaWdodCwgb3B0aW9ucyk7XG4gICAgICAgIHRoaXMuaXNXZWJHTE11bHRpcGxlUmVuZGVyVGFyZ2V0cyA9IHRydWU7XG4gICAgICAgIGNvbnN0IHRleHR1cmUgPSB0aGlzLnRleHR1cmU7XG4gICAgICAgIHRoaXMudGV4dHVyZSA9IFtdO1xuICAgICAgICBmb3IobGV0IGkgPSAwOyBpIDwgY291bnQ7IGkrKyl7XG4gICAgICAgICAgICB0aGlzLnRleHR1cmVbaV0gPSB0ZXh0dXJlLmNsb25lKCk7XG4gICAgICAgICAgICB0aGlzLnRleHR1cmVbaV0uaXNSZW5kZXJUYXJnZXRUZXh0dXJlID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzZXRTaXplKHdpZHRoLCBoZWlnaHQsIGRlcHRoID0gMSkge1xuICAgICAgICBpZiAodGhpcy53aWR0aCAhPT0gd2lkdGggfHwgdGhpcy5oZWlnaHQgIT09IGhlaWdodCB8fCB0aGlzLmRlcHRoICE9PSBkZXB0aCkge1xuICAgICAgICAgICAgdGhpcy53aWR0aCA9IHdpZHRoO1xuICAgICAgICAgICAgdGhpcy5oZWlnaHQgPSBoZWlnaHQ7XG4gICAgICAgICAgICB0aGlzLmRlcHRoID0gZGVwdGg7XG4gICAgICAgICAgICBmb3IobGV0IGkgPSAwLCBpbCA9IHRoaXMudGV4dHVyZS5sZW5ndGg7IGkgPCBpbDsgaSsrKXtcbiAgICAgICAgICAgICAgICB0aGlzLnRleHR1cmVbaV0uaW1hZ2Uud2lkdGggPSB3aWR0aDtcbiAgICAgICAgICAgICAgICB0aGlzLnRleHR1cmVbaV0uaW1hZ2UuaGVpZ2h0ID0gaGVpZ2h0O1xuICAgICAgICAgICAgICAgIHRoaXMudGV4dHVyZVtpXS5pbWFnZS5kZXB0aCA9IGRlcHRoO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5kaXNwb3NlKCk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy52aWV3cG9ydC5zZXQoMCwgMCwgd2lkdGgsIGhlaWdodCk7XG4gICAgICAgIHRoaXMuc2Npc3Nvci5zZXQoMCwgMCwgd2lkdGgsIGhlaWdodCk7XG4gICAgfVxuICAgIGNvcHkoc291cmNlKSB7XG4gICAgICAgIHRoaXMuZGlzcG9zZSgpO1xuICAgICAgICB0aGlzLndpZHRoID0gc291cmNlLndpZHRoO1xuICAgICAgICB0aGlzLmhlaWdodCA9IHNvdXJjZS5oZWlnaHQ7XG4gICAgICAgIHRoaXMuZGVwdGggPSBzb3VyY2UuZGVwdGg7XG4gICAgICAgIHRoaXMuc2Npc3Nvci5jb3B5KHNvdXJjZS5zY2lzc29yKTtcbiAgICAgICAgdGhpcy5zY2lzc29yVGVzdCA9IHNvdXJjZS5zY2lzc29yVGVzdDtcbiAgICAgICAgdGhpcy52aWV3cG9ydC5jb3B5KHNvdXJjZS52aWV3cG9ydCk7XG4gICAgICAgIHRoaXMuZGVwdGhCdWZmZXIgPSBzb3VyY2UuZGVwdGhCdWZmZXI7XG4gICAgICAgIHRoaXMuc3RlbmNpbEJ1ZmZlciA9IHNvdXJjZS5zdGVuY2lsQnVmZmVyO1xuICAgICAgICBpZiAoc291cmNlLmRlcHRoVGV4dHVyZSAhPT0gbnVsbCkgdGhpcy5kZXB0aFRleHR1cmUgPSBzb3VyY2UuZGVwdGhUZXh0dXJlLmNsb25lKCk7XG4gICAgICAgIHRoaXMudGV4dHVyZS5sZW5ndGggPSAwO1xuICAgICAgICBmb3IobGV0IGkgPSAwLCBpbCA9IHNvdXJjZS50ZXh0dXJlLmxlbmd0aDsgaSA8IGlsOyBpKyspe1xuICAgICAgICAgICAgdGhpcy50ZXh0dXJlW2ldID0gc291cmNlLnRleHR1cmVbaV0uY2xvbmUoKTtcbiAgICAgICAgICAgIHRoaXMudGV4dHVyZVtpXS5pc1JlbmRlclRhcmdldFRleHR1cmUgPSB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbn07XG5cblxuLyoqXG4gKiBcbiAqIEBwYXJhbSB7Kn0gdGltZXJRdWVyeSBcbiAqIEBwYXJhbSB7VEhSRUUuV2ViR0xSZW5kZXJlcn0gZ2wgXG4gKiBAcGFyYW0ge044QU9Qb3N0UGFzcyB8IE44QU9QYXNzfSBwYXNzIFxuICovIGZ1bmN0aW9uICQ4NzQzMWVlOTNiMDM3ODQ0JHZhciRjaGVja1RpbWVyUXVlcnkodGltZXJRdWVyeSwgZ2wsIHBhc3MpIHtcbiAgICBjb25zdCBhdmFpbGFibGUgPSBnbC5nZXRRdWVyeVBhcmFtZXRlcih0aW1lclF1ZXJ5LCBnbC5RVUVSWV9SRVNVTFRfQVZBSUxBQkxFKTtcbiAgICBpZiAoYXZhaWxhYmxlKSB7XG4gICAgICAgIGNvbnN0IGVsYXBzZWRUaW1lSW5OcyA9IGdsLmdldFF1ZXJ5UGFyYW1ldGVyKHRpbWVyUXVlcnksIGdsLlFVRVJZX1JFU1VMVCk7XG4gICAgICAgIGNvbnN0IGVsYXBzZWRUaW1lSW5NcyA9IGVsYXBzZWRUaW1lSW5OcyAvIDEwMDAwMDA7XG4gICAgICAgIHBhc3MubGFzdFRpbWUgPSBwYXNzLmxhc3RUaW1lID09PSAwID8gZWxhcHNlZFRpbWVJbk1zIDogcGFzcy50aW1lUm9sbGluZ0F2ZXJhZ2UgKiBwYXNzLmxhc3RUaW1lICsgKDEgLSBwYXNzLnRpbWVSb2xsaW5nQXZlcmFnZSkgKiBlbGFwc2VkVGltZUluTXM7XG4gICAgfSBlbHNlIC8vIElmIHRoZSByZXN1bHQgaXMgbm90IGF2YWlsYWJsZSB5ZXQsIGNoZWNrIGFnYWluIGFmdGVyIGEgZGVsYXlcbiAgICBzZXRUaW1lb3V0KCgpPT57XG4gICAgICAgICQ4NzQzMWVlOTNiMDM3ODQ0JHZhciRjaGVja1RpbWVyUXVlcnkodGltZXJRdWVyeSwgZ2wsIHBhc3MpO1xuICAgIH0sIDEpO1xufVxuY2xhc3MgJDg3NDMxZWU5M2IwMzc4NDQkZXhwb3J0JDI0ODlmOTk4MWFiMGZhODIgZXh0ZW5kcyAoMCwgJDVXaGUzJFBhc3MxKSB7XG4gICAgLyoqXG4gICAgICogXG4gICAgICogQHBhcmFtIHtUSFJFRS5TY2VuZX0gc2NlbmVcbiAgICAgKiBAcGFyYW0ge1RIUkVFLkNhbWVyYX0gY2FtZXJhIFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSB3aWR0aCBcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gaGVpZ2h0XG4gICAgICogIFxuICAgICAqIEBwcm9wZXJ0eSB7VEhSRUUuU2NlbmV9IHNjZW5lXG4gICAgICogQHByb3BlcnR5IHtUSFJFRS5DYW1lcmF9IGNhbWVyYVxuICAgICAqIEBwcm9wZXJ0eSB7bnVtYmVyfSB3aWR0aFxuICAgICAqIEBwcm9wZXJ0eSB7bnVtYmVyfSBoZWlnaHRcbiAgICAgKi8gY29uc3RydWN0b3Ioc2NlbmUsIGNhbWVyYSwgd2lkdGggPSA1MTIsIGhlaWdodCA9IDUxMil7XG4gICAgICAgIHN1cGVyKCk7XG4gICAgICAgIHRoaXMud2lkdGggPSB3aWR0aDtcbiAgICAgICAgdGhpcy5oZWlnaHQgPSBoZWlnaHQ7XG4gICAgICAgIHRoaXMuY2xlYXIgPSB0cnVlO1xuICAgICAgICB0aGlzLmNhbWVyYSA9IGNhbWVyYTtcbiAgICAgICAgdGhpcy5zY2VuZSA9IHNjZW5lO1xuICAgICAgICAvKipcbiAgICAgICAgICogQHR5cGUge1Byb3h5ICYge1xuICAgICAgICAgKiBhb1NhbXBsZXM6IG51bWJlcixcbiAgICAgICAgICogYW9SYWRpdXM6IG51bWJlcixcbiAgICAgICAgICogZGVub2lzZVNhbXBsZXM6IG51bWJlcixcbiAgICAgICAgICogZGVub2lzZVJhZGl1czogbnVtYmVyLFxuICAgICAgICAgKiBkaXN0YW5jZUZhbGxvZmY6IG51bWJlcixcbiAgICAgICAgICogaW50ZW5zaXR5OiBudW1iZXIsXG4gICAgICAgICAqIGRlbm9pc2VJdGVyYXRpb25zOiBudW1iZXIsXG4gICAgICAgICAqIHJlbmRlck1vZGU6IDAgfCAxIHwgMiB8IDMgfCA0LFxuICAgICAgICAgKiBjb2xvcjogVEhSRUUuQ29sb3IsXG4gICAgICAgICAqIGdhbW1hQ29ycmVjdGlvbjogYm9vbGVhbixcbiAgICAgICAgICogZGVwdGhCdWZmZXJUeXBlOiAxIHwgMiB8IDMsXG4gICAgICAgICAqIHNjcmVlblNwYWNlUmFkaXVzOiBib29sZWFuLFxuICAgICAgICAgKiBoYWxmUmVzOiBib29sZWFuLFxuICAgICAgICAgKiBkZXB0aEF3YXJlVXBzYW1wbGluZzogYm9vbGVhblxuICAgICAgICAgKiBjb2xvck11bHRpcGx5OiBib29sZWFuXG4gICAgICAgICAqIH1cbiAgICAgICAgICovIHRoaXMuYXV0b3NldEdhbW1hID0gdHJ1ZTtcbiAgICAgICAgdGhpcy5jb25maWd1cmF0aW9uID0gbmV3IFByb3h5KHtcbiAgICAgICAgICAgIGFvU2FtcGxlczogMTYsXG4gICAgICAgICAgICBhb1JhZGl1czogNS4wLFxuICAgICAgICAgICAgYW9Ub25lczogMC4wLFxuICAgICAgICAgICAgZGVub2lzZVNhbXBsZXM6IDgsXG4gICAgICAgICAgICBkZW5vaXNlUmFkaXVzOiAxMixcbiAgICAgICAgICAgIGRpc3RhbmNlRmFsbG9mZjogMS4wLFxuICAgICAgICAgICAgaW50ZW5zaXR5OiA1LFxuICAgICAgICAgICAgZGVub2lzZUl0ZXJhdGlvbnM6IDIuMCxcbiAgICAgICAgICAgIHJlbmRlck1vZGU6IDAsXG4gICAgICAgICAgICBiaWFzT2Zmc2V0OiAwLjAsXG4gICAgICAgICAgICBiaWFzTXVsdGlwbGllcjogMC4wLFxuICAgICAgICAgICAgY29sb3I6IG5ldyAkNVdoZTMkQ29sb3IoMCwgMCwgMCksXG4gICAgICAgICAgICBnYW1tYUNvcnJlY3Rpb246IHRydWUsXG4gICAgICAgICAgICBkZXB0aEJ1ZmZlclR5cGU6ICgwLCAkMDVmNjk5N2U0YjY1ZGExNCRleHBvcnQkZWQ0ZWU1ZDFlNTU0NzRhNSkuRGVmYXVsdCxcbiAgICAgICAgICAgIHNjcmVlblNwYWNlUmFkaXVzOiBmYWxzZSxcbiAgICAgICAgICAgIGhhbGZSZXM6IGZhbHNlLFxuICAgICAgICAgICAgZGVwdGhBd2FyZVVwc2FtcGxpbmc6IHRydWUsXG4gICAgICAgICAgICBjb2xvck11bHRpcGx5OiB0cnVlLFxuICAgICAgICAgICAgdHJhbnNwYXJlbmN5QXdhcmU6IGZhbHNlLFxuICAgICAgICAgICAgYWNjdW11bGF0ZTogZmFsc2VcbiAgICAgICAgfSwge1xuICAgICAgICAgICAgc2V0OiAodGFyZ2V0LCBwcm9wTmFtZSwgdmFsdWUpPT57XG4gICAgICAgICAgICAgICAgY29uc3Qgb2xkUHJvcCA9IHRhcmdldFtwcm9wTmFtZV07XG4gICAgICAgICAgICAgICAgdGFyZ2V0W3Byb3BOYW1lXSA9IHZhbHVlO1xuICAgICAgICAgICAgICAgIGlmICh2YWx1ZS5lcXVhbHMpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCF2YWx1ZS5lcXVhbHMob2xkUHJvcCkpIHRoaXMuZmlyc3RGcmFtZSgpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAob2xkUHJvcCAhPT0gdmFsdWUpIHRoaXMuZmlyc3RGcmFtZSgpO1xuICAgICAgICAgICAgICAgIGlmIChwcm9wTmFtZSA9PT0gXCJhb1NhbXBsZXNcIiAmJiBvbGRQcm9wICE9PSB2YWx1ZSkgdGhpcy5jb25maWd1cmVBT1Bhc3ModGhpcy5jb25maWd1cmF0aW9uLmRlcHRoQnVmZmVyVHlwZSwgdGhpcy5jYW1lcmEuaXNPcnRob2dyYXBoaWNDYW1lcmEpO1xuICAgICAgICAgICAgICAgIGlmIChwcm9wTmFtZSA9PT0gXCJkZW5vaXNlU2FtcGxlc1wiICYmIG9sZFByb3AgIT09IHZhbHVlKSB0aGlzLmNvbmZpZ3VyZURlbm9pc2VQYXNzKHRoaXMuY29uZmlndXJhdGlvbi5kZXB0aEJ1ZmZlclR5cGUsIHRoaXMuY2FtZXJhLmlzT3J0aG9ncmFwaGljQ2FtZXJhKTtcbiAgICAgICAgICAgICAgICBpZiAocHJvcE5hbWUgPT09IFwiaGFsZlJlc1wiICYmIG9sZFByb3AgIT09IHZhbHVlKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlndXJlQU9QYXNzKHRoaXMuY29uZmlndXJhdGlvbi5kZXB0aEJ1ZmZlclR5cGUsIHRoaXMuY2FtZXJhLmlzT3J0aG9ncmFwaGljQ2FtZXJhKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jb25maWd1cmVIYWxmUmVzVGFyZ2V0cygpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ3VyZUVmZmVjdENvbXBvc2l0ZXIodGhpcy5jb25maWd1cmF0aW9uLmRlcHRoQnVmZmVyVHlwZSwgdGhpcy5jYW1lcmEuaXNPcnRob2dyYXBoaWNDYW1lcmEpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnNldFNpemUodGhpcy53aWR0aCwgdGhpcy5oZWlnaHQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAocHJvcE5hbWUgPT09IFwiZGVwdGhBd2FyZVVwc2FtcGxpbmdcIiAmJiBvbGRQcm9wICE9PSB2YWx1ZSkgdGhpcy5jb25maWd1cmVFZmZlY3RDb21wb3NpdGVyKHRoaXMuY29uZmlndXJhdGlvbi5kZXB0aEJ1ZmZlclR5cGUsIHRoaXMuY2FtZXJhLmlzT3J0aG9ncmFwaGljQ2FtZXJhKTtcbiAgICAgICAgICAgICAgICBpZiAocHJvcE5hbWUgPT09IFwiZ2FtbWFDb3JyZWN0aW9uXCIpIHRoaXMuYXV0b3NldEdhbW1hID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgaWYgKHByb3BOYW1lID09PSBcInRyYW5zcGFyZW5jeUF3YXJlXCIgJiYgb2xkUHJvcCAhPT0gdmFsdWUpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5hdXRvRGV0ZWN0VHJhbnNwYXJlbmN5ID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlndXJlVHJhbnNwYXJlbmN5VGFyZ2V0KCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgICAgLyoqIEB0eXBlIHtUSFJFRS5WZWN0b3IzW119ICovIHRoaXMuc2FtcGxlcyA9IFtdO1xuICAgICAgICAvKiogQHR5cGUge1RIUkVFLlZlY3RvcjJbXX0gKi8gdGhpcy5zYW1wbGVzRGVub2lzZSA9IFtdO1xuICAgICAgICB0aGlzLmF1dG9EZXRlY3RUcmFuc3BhcmVuY3kgPSB0cnVlO1xuICAgICAgICB0aGlzLmZyYW1lcyA9IDA7XG4gICAgICAgIHRoaXMubGFzdFZpZXdNYXRyaXggPSBuZXcgJDVXaGUzJE1hdHJpeDQoKTtcbiAgICAgICAgdGhpcy5sYXN0UHJvamVjdGlvbk1hdHJpeCA9IG5ldyAkNVdoZTMkTWF0cml4NCgpO1xuICAgICAgICB0aGlzLmNvbmZpZ3VyZUVmZmVjdENvbXBvc2l0ZXIodGhpcy5jb25maWd1cmF0aW9uLmRlcHRoQnVmZmVyVHlwZSk7XG4gICAgICAgIHRoaXMuY29uZmlndXJlU2FtcGxlRGVwZW5kZW50UGFzc2VzKCk7XG4gICAgICAgIHRoaXMuY29uZmlndXJlSGFsZlJlc1RhcmdldHMoKTtcbiAgICAgICAgdGhpcy5kZXRlY3RUcmFuc3BhcmVuY3koKTtcbiAgICAgICAgdGhpcy5jb25maWd1cmVUcmFuc3BhcmVuY3lUYXJnZXQoKTtcbiAgICAgICAgLy8gICB0aGlzLmVmZmVjdENvbXBpc3RlclF1YWQgPSBuZXcgRnVsbFNjcmVlblRyaWFuZ2xlKG5ldyBUSFJFRS5TaGFkZXJNYXRlcmlhbChFZmZlY3RDb21wb3NpdGVyKSk7XG4gICAgICAgIHRoaXMuY29weVF1YWQgPSBuZXcgKDAsICRlNGNhOGRjYjAyMThmODQ2JGV4cG9ydCRkY2Q2NzBkNzNkYjc1MWY1KShuZXcgJDVXaGUzJFNoYWRlck1hdGVyaWFsKHtcbiAgICAgICAgICAgIHVuaWZvcm1zOiB7XG4gICAgICAgICAgICAgICAgdERpZmZ1c2U6IHtcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IG51bGxcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZGVwdGhXcml0ZTogZmFsc2UsXG4gICAgICAgICAgICB2ZXJ0ZXhTaGFkZXI6IGBcbiAgICAgICAgICAgIHZhcnlpbmcgdmVjMiB2VXY7XG4gICAgICAgICAgICB2b2lkIG1haW4oKSB7XG4gICAgICAgICAgICAgICAgdlV2ID0gdXY7XG4gICAgICAgICAgICAgICAgZ2xfUG9zaXRpb24gPSB2ZWM0KHBvc2l0aW9uLCAxKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGAsXG4gICAgICAgICAgICBmcmFnbWVudFNoYWRlcjogYFxuICAgICAgICAgICAgdW5pZm9ybSBzYW1wbGVyMkQgdERpZmZ1c2U7XG4gICAgICAgICAgICB2YXJ5aW5nIHZlYzIgdlV2O1xuICAgICAgICAgICAgdm9pZCBtYWluKCkge1xuICAgICAgICAgICAgICAgIGdsX0ZyYWdDb2xvciA9IHRleHR1cmUyRCh0RGlmZnVzZSwgdlV2KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGBcbiAgICAgICAgfSkpO1xuICAgICAgICB0aGlzLndyaXRlVGFyZ2V0SW50ZXJuYWwgPSBuZXcgJDVXaGUzJFdlYkdMUmVuZGVyVGFyZ2V0KHRoaXMud2lkdGgsIHRoaXMuaGVpZ2h0LCB7XG4gICAgICAgICAgICBtaW5GaWx0ZXI6ICQ1V2hlMyRMaW5lYXJGaWx0ZXIsXG4gICAgICAgICAgICBtYWdGaWx0ZXI6ICQ1V2hlMyRMaW5lYXJGaWx0ZXIsXG4gICAgICAgICAgICBkZXB0aEJ1ZmZlcjogZmFsc2UsXG4gICAgICAgICAgICBmb3JtYXQ6ICQ1V2hlMyRSR0JBRm9ybWF0XG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLnJlYWRUYXJnZXRJbnRlcm5hbCA9IG5ldyAkNVdoZTMkV2ViR0xSZW5kZXJUYXJnZXQodGhpcy53aWR0aCwgdGhpcy5oZWlnaHQsIHtcbiAgICAgICAgICAgIG1pbkZpbHRlcjogJDVXaGUzJExpbmVhckZpbHRlcixcbiAgICAgICAgICAgIG1hZ0ZpbHRlcjogJDVXaGUzJExpbmVhckZpbHRlcixcbiAgICAgICAgICAgIGRlcHRoQnVmZmVyOiBmYWxzZSxcbiAgICAgICAgICAgIGZvcm1hdDogJDVXaGUzJFJHQkFGb3JtYXRcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMub3V0cHV0VGFyZ2V0SW50ZXJuYWwgPSBuZXcgJDVXaGUzJFdlYkdMUmVuZGVyVGFyZ2V0KHRoaXMud2lkdGgsIHRoaXMuaGVpZ2h0LCB7XG4gICAgICAgICAgICBtaW5GaWx0ZXI6ICQ1V2hlMyRMaW5lYXJGaWx0ZXIsXG4gICAgICAgICAgICBtYWdGaWx0ZXI6ICQ1V2hlMyRMaW5lYXJGaWx0ZXIsXG4gICAgICAgICAgICBkZXB0aEJ1ZmZlcjogZmFsc2VcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMuYWNjdW11bGF0aW9uUmVuZGVyVGFyZ2V0ID0gbmV3ICQ1V2hlMyRXZWJHTFJlbmRlclRhcmdldCh0aGlzLndpZHRoLCB0aGlzLmhlaWdodCwge1xuICAgICAgICAgICAgbWluRmlsdGVyOiAkNVdoZTMkTGluZWFyRmlsdGVyLFxuICAgICAgICAgICAgbWFnRmlsdGVyOiAkNVdoZTMkTGluZWFyRmlsdGVyLFxuICAgICAgICAgICAgZGVwdGhCdWZmZXI6IGZhbHNlLFxuICAgICAgICAgICAgZm9ybWF0OiAkNVdoZTMkUkdCQUZvcm1hdCxcbiAgICAgICAgICAgIHR5cGU6ICQ1V2hlMyRIYWxmRmxvYXRUeXBlLFxuICAgICAgICAgICAgc3RlbmNpbEJ1ZmZlcjogZmFsc2UsXG4gICAgICAgICAgICBkZXB0aEJ1ZmZlcjogZmFsc2UsXG4gICAgICAgICAgICBhbHBoYTogdHJ1ZVxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5hY2N1bXVsYXRpb25RdWFkID0gbmV3ICgwLCAkZTRjYThkY2IwMjE4Zjg0NiRleHBvcnQkZGNkNjcwZDczZGI3NTFmNSkobmV3ICQ1V2hlMyRTaGFkZXJNYXRlcmlhbCh7XG4gICAgICAgICAgICB1bmlmb3Jtczoge1xuICAgICAgICAgICAgICAgIGZyYW1lOiB7XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiAwXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICB0RGlmZnVzZToge1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZTogbnVsbFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB0cmFuc3BhcmVudDogdHJ1ZSxcbiAgICAgICAgICAgIG9wYWNpdHk6IDEsXG4gICAgICAgICAgICB2ZXJ0ZXhTaGFkZXI6IGBcbiAgICAgICAgICAgICB2YXJ5aW5nIHZlYzIgdlV2O1xuICAgICAgICAgICAgIHZvaWQgbWFpbigpIHtcbiAgICAgICAgICAgICAgICAgdlV2ID0gdXY7XG4gICAgICAgICAgICAgICAgIGdsX1Bvc2l0aW9uID0gdmVjNChwb3NpdGlvbiwgMSk7XG4gICAgICAgICAgICAgfWAsXG4gICAgICAgICAgICBmcmFnbWVudFNoYWRlcjogYFxuICAgICAgICAgICAgIHVuaWZvcm0gc2FtcGxlcjJEIHREaWZmdXNlO1xuICAgICAgICAgICAgIHVuaWZvcm0gZmxvYXQgZnJhbWU7XG4gICAgICAgICAgICAgICAgdmFyeWluZyB2ZWMyIHZVdjtcbiAgICAgICAgICAgICAgICB2b2lkIG1haW4oKSB7XG4gICAgICAgICAgICAgICAgICAgIHZlYzQgY29sb3IgPSB0ZXh0dXJlMkQodERpZmZ1c2UsIHZVdik7XG4gICAgICAgICAgICAgICAgICAgIGdsX0ZyYWdDb2xvciA9IHZlYzQoY29sb3IucmdiLCAxLjAgLyAoZnJhbWUgKyAxLjApKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgYFxuICAgICAgICB9KSk7XG4gICAgICAgIC8qKiBAdHlwZSB7VEhSRUUuRGF0YVRleHR1cmV9ICovIHRoaXMuYmx1ZW5vaXNlID0gbmV3ICQ1V2hlMyREYXRhVGV4dHVyZSgoMCwgJDA2MjY5YWQ3OGYzYzVmZGYkZXhwb3J0JDJlMmJjZDg3MzlhZTAzOSksIDEyOCwgMTI4KTtcbiAgICAgICAgdGhpcy5ibHVlbm9pc2UuY29sb3JTcGFjZSA9ICQ1V2hlMyROb0NvbG9yU3BhY2U7XG4gICAgICAgIHRoaXMuYmx1ZW5vaXNlLndyYXBTID0gJDVXaGUzJFJlcGVhdFdyYXBwaW5nO1xuICAgICAgICB0aGlzLmJsdWVub2lzZS53cmFwVCA9ICQ1V2hlMyRSZXBlYXRXcmFwcGluZztcbiAgICAgICAgdGhpcy5ibHVlbm9pc2UubWluRmlsdGVyID0gJDVXaGUzJE5lYXJlc3RGaWx0ZXI7XG4gICAgICAgIHRoaXMuYmx1ZW5vaXNlLm1hZ0ZpbHRlciA9ICQ1V2hlMyROZWFyZXN0RmlsdGVyO1xuICAgICAgICB0aGlzLmJsdWVub2lzZS5uZWVkc1VwZGF0ZSA9IHRydWU7XG4gICAgICAgIHRoaXMubGFzdFRpbWUgPSAwO1xuICAgICAgICB0aGlzLnRpbWVSb2xsaW5nQXZlcmFnZSA9IDAuOTk7XG4gICAgICAgIHRoaXMubmVlZHNEZXB0aFRleHR1cmUgPSB0cnVlO1xuICAgICAgICB0aGlzLm5lZWRzU3dhcCA9IHRydWU7XG4gICAgICAgIHRoaXMuX3IgPSBuZXcgJDVXaGUzJFZlY3RvcjIoKTtcbiAgICAgICAgdGhpcy5fYyA9IG5ldyAkNVdoZTMkQ29sb3IoKTtcbiAgICB9XG4gICAgY29uZmlndXJlSGFsZlJlc1RhcmdldHMoKSB7XG4gICAgICAgIHRoaXMuZmlyc3RGcmFtZSgpO1xuICAgICAgICBpZiAodGhpcy5jb25maWd1cmF0aW9uLmhhbGZSZXMpIHtcbiAgICAgICAgICAgIHRoaXMuZGVwdGhEb3duc2FtcGxlVGFyZ2V0ID0gbmV3ICgwLCAkZmY5NDM3ZDljNzU3N2YxMSRleHBvcnQkMTU2ZjZhNThmNTY5YWEwOSkodGhpcy53aWR0aCAvIDIsIHRoaXMuaGVpZ2h0IC8gMiwgMik7XG4gICAgICAgICAgICBpZiAoJDVXaGUzJFJFVklTSU9OIDw9IDE2MSkgdGhpcy5kZXB0aERvd25zYW1wbGVUYXJnZXQudGV4dHVyZXMgPSB0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldC50ZXh0dXJlO1xuICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVUYXJnZXQudGV4dHVyZXNbMF0uZm9ybWF0ID0gJDVXaGUzJFJlZEZvcm1hdDtcbiAgICAgICAgICAgIHRoaXMuZGVwdGhEb3duc2FtcGxlVGFyZ2V0LnRleHR1cmVzWzBdLnR5cGUgPSAkNVdoZTMkRmxvYXRUeXBlO1xuICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVUYXJnZXQudGV4dHVyZXNbMF0ubWluRmlsdGVyID0gJDVXaGUzJE5lYXJlc3RGaWx0ZXI7XG4gICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldC50ZXh0dXJlc1swXS5tYWdGaWx0ZXIgPSAkNVdoZTMkTmVhcmVzdEZpbHRlcjtcbiAgICAgICAgICAgIHRoaXMuZGVwdGhEb3duc2FtcGxlVGFyZ2V0LnRleHR1cmVzWzBdLmRlcHRoQnVmZmVyID0gZmFsc2U7XG4gICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldC50ZXh0dXJlc1sxXS5mb3JtYXQgPSAkNVdoZTMkUkdCQUZvcm1hdDtcbiAgICAgICAgICAgIHRoaXMuZGVwdGhEb3duc2FtcGxlVGFyZ2V0LnRleHR1cmVzWzFdLnR5cGUgPSAkNVdoZTMkSGFsZkZsb2F0VHlwZTtcbiAgICAgICAgICAgIHRoaXMuZGVwdGhEb3duc2FtcGxlVGFyZ2V0LnRleHR1cmVzWzFdLm1pbkZpbHRlciA9ICQ1V2hlMyROZWFyZXN0RmlsdGVyO1xuICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVUYXJnZXQudGV4dHVyZXNbMV0ubWFnRmlsdGVyID0gJDVXaGUzJE5lYXJlc3RGaWx0ZXI7XG4gICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldC50ZXh0dXJlc1sxXS5kZXB0aEJ1ZmZlciA9IGZhbHNlO1xuICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVRdWFkID0gbmV3ICgwLCAkZTRjYThkY2IwMjE4Zjg0NiRleHBvcnQkZGNkNjcwZDczZGI3NTFmNSkobmV3ICQ1V2hlMyRTaGFkZXJNYXRlcmlhbCgoMCwgJDI2YWNhMTczZTA5ODRkOTkkZXhwb3J0JDFlZmRmNDkxNjg3Y2Q0NDIpKSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBpZiAodGhpcy5kZXB0aERvd25zYW1wbGVUYXJnZXQpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldC5kaXNwb3NlKCk7XG4gICAgICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVUYXJnZXQgPSBudWxsO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHRoaXMuZGVwdGhEb3duc2FtcGxlUXVhZCkge1xuICAgICAgICAgICAgICAgIHRoaXMuZGVwdGhEb3duc2FtcGxlUXVhZC5kaXNwb3NlKCk7XG4gICAgICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVRdWFkID0gbnVsbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBkZXRlY3RUcmFuc3BhcmVuY3koKSB7XG4gICAgICAgIGlmICh0aGlzLmF1dG9EZXRlY3RUcmFuc3BhcmVuY3kpIHtcbiAgICAgICAgICAgIGxldCBpc1RyYW5zcGFyZW5jeSA9IGZhbHNlO1xuICAgICAgICAgICAgdGhpcy5zY2VuZS50cmF2ZXJzZSgob2JqKT0+e1xuICAgICAgICAgICAgICAgIGlmIChvYmoubWF0ZXJpYWwgJiYgb2JqLm1hdGVyaWFsLnRyYW5zcGFyZW50KSBpc1RyYW5zcGFyZW5jeSA9IHRydWU7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGlmIChpc1RyYW5zcGFyZW5jeSkgdGhpcy5jb25maWd1cmF0aW9uLnRyYW5zcGFyZW5jeUF3YXJlID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBjb25maWd1cmVUcmFuc3BhcmVuY3lUYXJnZXQoKSB7XG4gICAgICAgIGlmICh0aGlzLmNvbmZpZ3VyYXRpb24udHJhbnNwYXJlbmN5QXdhcmUpIHtcbiAgICAgICAgICAgIHRoaXMudHJhbnNwYXJlbmN5UmVuZGVyVGFyZ2V0RFdGYWxzZSA9IG5ldyAkNVdoZTMkV2ViR0xSZW5kZXJUYXJnZXQodGhpcy53aWR0aCwgdGhpcy5oZWlnaHQsIHtcbiAgICAgICAgICAgICAgICBtaW5GaWx0ZXI6ICQ1V2hlMyRMaW5lYXJGaWx0ZXIsXG4gICAgICAgICAgICAgICAgbWFnRmlsdGVyOiAkNVdoZTMkTmVhcmVzdEZpbHRlcixcbiAgICAgICAgICAgICAgICB0eXBlOiAkNVdoZTMkSGFsZkZsb2F0VHlwZSxcbiAgICAgICAgICAgICAgICBmb3JtYXQ6ICQ1V2hlMyRSR0JBRm9ybWF0XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRoaXMudHJhbnNwYXJlbmN5UmVuZGVyVGFyZ2V0RFdUcnVlID0gbmV3ICQ1V2hlMyRXZWJHTFJlbmRlclRhcmdldCh0aGlzLndpZHRoLCB0aGlzLmhlaWdodCwge1xuICAgICAgICAgICAgICAgIG1pbkZpbHRlcjogJDVXaGUzJExpbmVhckZpbHRlcixcbiAgICAgICAgICAgICAgICBtYWdGaWx0ZXI6ICQ1V2hlMyROZWFyZXN0RmlsdGVyLFxuICAgICAgICAgICAgICAgIHR5cGU6ICQ1V2hlMyRIYWxmRmxvYXRUeXBlLFxuICAgICAgICAgICAgICAgIGZvcm1hdDogJDVXaGUzJFJHQkFGb3JtYXRcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgdGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV1RydWUuZGVwdGhUZXh0dXJlID0gbmV3ICQ1V2hlMyREZXB0aFRleHR1cmUodGhpcy53aWR0aCwgdGhpcy5oZWlnaHQsICQ1V2hlMyRVbnNpZ25lZEludFR5cGUpO1xuICAgICAgICAgICAgdGhpcy5kZXB0aENvcHlQYXNzID0gbmV3ICgwLCAkZTRjYThkY2IwMjE4Zjg0NiRleHBvcnQkZGNkNjcwZDczZGI3NTFmNSkobmV3ICQ1V2hlMyRTaGFkZXJNYXRlcmlhbCh7XG4gICAgICAgICAgICAgICAgdW5pZm9ybXM6IHtcbiAgICAgICAgICAgICAgICAgICAgZGVwdGhUZXh0dXJlOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5kZXB0aFRleHR1cmVcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgcmV2ZXJzZURlcHRoQnVmZmVyOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5jb25maWd1cmF0aW9uLmRlcHRoQnVmZmVyVHlwZSA9PT0gKDAsICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1KS5SZXZlcnNlXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIHZlcnRleFNoYWRlcjogLyogZ2xzbCAqLyBgXG4gICAgICAgICAgICB2YXJ5aW5nIHZlYzIgdlV2O1xuICAgICAgICAgICAgdm9pZCBtYWluKCkge1xuICAgICAgICAgICAgICAgIHZVdiA9IHV2O1xuICAgICAgICAgICAgICAgIGdsX1Bvc2l0aW9uID0gdmVjNChwb3NpdGlvbiwgMSk7XG4gICAgICAgICAgICB9YCxcbiAgICAgICAgICAgICAgICBmcmFnbWVudFNoYWRlcjogLyogZ2xzbCAqLyBgXG4gICAgICAgICAgICB1bmlmb3JtIHNhbXBsZXIyRCBkZXB0aFRleHR1cmU7XG4gICAgICAgICAgICB1bmlmb3JtIGJvb2wgcmV2ZXJzZURlcHRoQnVmZmVyO1xuICAgICAgICAgICAgdmFyeWluZyB2ZWMyIHZVdjtcbiAgICAgICAgICAgIHZvaWQgbWFpbigpIHtcbiAgICAgICAgICAgICAgICBpZiAocmV2ZXJzZURlcHRoQnVmZmVyKSB7XG4gICAgICAgICAgICAgICBmbG9hdCBkID0gMS4wIC0gdGV4dHVyZTJEKGRlcHRoVGV4dHVyZSwgdlV2KS5yO1xuICAgICAgICAgICBcbiAgICAgICAgICAgICAgIGQgKz0gMC4wMDAwMTtcbiAgICAgICAgICAgICAgIGdsX0ZyYWdEZXB0aCA9IDEuMCAtIGQ7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGZsb2F0IGQgPSB0ZXh0dXJlMkQoZGVwdGhUZXh0dXJlLCB2VXYpLnI7XG4gICAgICAgICAgICAgICAgZCArPSAwLjAwMDAxO1xuICAgICAgICAgICAgICAgIGdsX0ZyYWdEZXB0aCA9IGQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAgICBnbF9GcmFnQ29sb3IgPSB2ZWM0KDAuMCwgMC4wLCAwLjAsIDAuMCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBgXG4gICAgICAgICAgICB9KSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBpZiAodGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV0ZhbHNlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV0ZhbHNlLmRpc3Bvc2UoKTtcbiAgICAgICAgICAgICAgICB0aGlzLnRyYW5zcGFyZW5jeVJlbmRlclRhcmdldERXRmFsc2UgPSBudWxsO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHRoaXMudHJhbnNwYXJlbmN5UmVuZGVyVGFyZ2V0RFdUcnVlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV1RydWUuZGlzcG9zZSgpO1xuICAgICAgICAgICAgICAgIHRoaXMudHJhbnNwYXJlbmN5UmVuZGVyVGFyZ2V0RFdUcnVlID0gbnVsbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICh0aGlzLmRlcHRoQ29weVBhc3MpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmRlcHRoQ29weVBhc3MuZGlzcG9zZSgpO1xuICAgICAgICAgICAgICAgIHRoaXMuZGVwdGhDb3B5UGFzcyA9IG51bGw7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmVuZGVyVHJhbnNwYXJlbmN5KHJlbmRlcmVyKSB7XG4gICAgICAgIGNvbnN0IG9sZEJhY2tncm91bmQgPSB0aGlzLnNjZW5lLmJhY2tncm91bmQ7XG4gICAgICAgIGNvbnN0IG9sZENsZWFyQ29sb3IgPSByZW5kZXJlci5nZXRDbGVhckNvbG9yKG5ldyAkNVdoZTMkQ29sb3IoKSk7XG4gICAgICAgIGNvbnN0IG9sZENsZWFyQWxwaGEgPSByZW5kZXJlci5nZXRDbGVhckFscGhhKCk7XG4gICAgICAgIGNvbnN0IG9sZFZpc2liaWxpdHkgPSBuZXcgTWFwKCk7XG4gICAgICAgIGNvbnN0IG9sZEF1dG9DbGVhckRlcHRoID0gcmVuZGVyZXIuYXV0b0NsZWFyRGVwdGg7XG4gICAgICAgIHRoaXMuc2NlbmUudHJhdmVyc2UoKG9iaik9PntcbiAgICAgICAgICAgIG9sZFZpc2liaWxpdHkuc2V0KG9iaiwgb2JqLnZpc2libGUpO1xuICAgICAgICB9KTtcbiAgICAgICAgLy8gT3ZlcnJpZGUgdGhlIHN0YXRlXG4gICAgICAgIHRoaXMuc2NlbmUuYmFja2dyb3VuZCA9IG51bGw7XG4gICAgICAgIHJlbmRlcmVyLmF1dG9DbGVhckRlcHRoID0gZmFsc2U7XG4gICAgICAgIHJlbmRlcmVyLnNldENsZWFyQ29sb3IobmV3ICQ1V2hlMyRDb2xvcigwLCAwLCAwKSwgMCk7XG4gICAgICAgIHRoaXMuZGVwdGhDb3B5UGFzcy5tYXRlcmlhbC51bmlmb3Jtcy5kZXB0aFRleHR1cmUudmFsdWUgPSB0aGlzLmRlcHRoVGV4dHVyZTtcbiAgICAgICAgdGhpcy5kZXB0aENvcHlQYXNzLm1hdGVyaWFsLnVuaWZvcm1zLnJldmVyc2VEZXB0aEJ1ZmZlci52YWx1ZSA9IHRoaXMuY29uZmlndXJhdGlvbi5kZXB0aEJ1ZmZlclR5cGUgPT09ICgwLCAkMDVmNjk5N2U0YjY1ZGExNCRleHBvcnQkZWQ0ZWU1ZDFlNTU0NzRhNSkuUmV2ZXJzZTtcbiAgICAgICAgLy8gUmVuZGVyIG91dCB0cmFuc3BhcmVudCBvYmplY3RzIFdJVEhPVVQgZGVwdGggd3JpdGVcbiAgICAgICAgcmVuZGVyZXIuc2V0UmVuZGVyVGFyZ2V0KHRoaXMudHJhbnNwYXJlbmN5UmVuZGVyVGFyZ2V0RFdGYWxzZSk7XG4gICAgICAgIHRoaXMuc2NlbmUudHJhdmVyc2UoKG9iaik9PntcbiAgICAgICAgICAgIGlmIChvYmoubWF0ZXJpYWwpIG9iai52aXNpYmxlID0gb2xkVmlzaWJpbGl0eS5nZXQob2JqKSAmJiAob2JqLm1hdGVyaWFsLnRyYW5zcGFyZW50ICYmICFvYmoubWF0ZXJpYWwuZGVwdGhXcml0ZSAmJiAhb2JqLnVzZXJEYXRhLnRyZWF0QXNPcGFxdWUgfHwgISFvYmoudXNlckRhdGEuY2Fubm90UmVjZWl2ZUFPKTtcbiAgICAgICAgfSk7XG4gICAgICAgIHJlbmRlcmVyLmNsZWFyKHRydWUsIHRydWUsIHRydWUpO1xuICAgICAgICB0aGlzLmRlcHRoQ29weVBhc3MucmVuZGVyKHJlbmRlcmVyKTtcbiAgICAgICAgcmVuZGVyZXIucmVuZGVyKHRoaXMuc2NlbmUsIHRoaXMuY2FtZXJhKTtcbiAgICAgICAgLy8gUmVuZGVyIG91dCB0cmFuc3BhcmVudCBvYmplY3RzIFdJVEggZGVwdGggd3JpdGVcbiAgICAgICAgcmVuZGVyZXIuc2V0UmVuZGVyVGFyZ2V0KHRoaXMudHJhbnNwYXJlbmN5UmVuZGVyVGFyZ2V0RFdUcnVlKTtcbiAgICAgICAgdGhpcy5zY2VuZS50cmF2ZXJzZSgob2JqKT0+e1xuICAgICAgICAgICAgaWYgKG9iai5tYXRlcmlhbCkgb2JqLnZpc2libGUgPSBvbGRWaXNpYmlsaXR5LmdldChvYmopICYmIG9iai5tYXRlcmlhbC50cmFuc3BhcmVudCAmJiBvYmoubWF0ZXJpYWwuZGVwdGhXcml0ZSAmJiAhb2JqLnVzZXJEYXRhLnRyZWF0QXNPcGFxdWU7XG4gICAgICAgIH0pO1xuICAgICAgICByZW5kZXJlci5jbGVhcih0cnVlLCB0cnVlLCB0cnVlKTtcbiAgICAgICAgdGhpcy5kZXB0aENvcHlQYXNzLnJlbmRlcihyZW5kZXJlcik7XG4gICAgICAgIHJlbmRlcmVyLnJlbmRlcih0aGlzLnNjZW5lLCB0aGlzLmNhbWVyYSk7XG4gICAgICAgIC8vIFJlc3RvcmVcbiAgICAgICAgdGhpcy5zY2VuZS50cmF2ZXJzZSgob2JqKT0+e1xuICAgICAgICAgICAgb2JqLnZpc2libGUgPSBvbGRWaXNpYmlsaXR5LmdldChvYmopO1xuICAgICAgICB9KTtcbiAgICAgICAgcmVuZGVyZXIuc2V0Q2xlYXJDb2xvcihvbGRDbGVhckNvbG9yLCBvbGRDbGVhckFscGhhKTtcbiAgICAgICAgdGhpcy5zY2VuZS5iYWNrZ3JvdW5kID0gb2xkQmFja2dyb3VuZDtcbiAgICAgICAgcmVuZGVyZXIuYXV0b0NsZWFyRGVwdGggPSBvbGRBdXRvQ2xlYXJEZXB0aDtcbiAgICB9XG4gICAgY29uZmlndXJlU2FtcGxlRGVwZW5kZW50UGFzc2VzKCkge1xuICAgICAgICB0aGlzLmNvbmZpZ3VyZUFPUGFzcyh0aGlzLmNvbmZpZ3VyYXRpb24uZGVwdGhCdWZmZXJUeXBlLCB0aGlzLmNhbWVyYS5pc09ydGhvZ3JhcGhpY0NhbWVyYSk7XG4gICAgICAgIHRoaXMuY29uZmlndXJlRGVub2lzZVBhc3ModGhpcy5jb25maWd1cmF0aW9uLmRlcHRoQnVmZmVyVHlwZSwgdGhpcy5jYW1lcmEuaXNPcnRob2dyYXBoaWNDYW1lcmEpO1xuICAgIH1cbiAgICBjb25maWd1cmVBT1Bhc3MoZGVwdGhCdWZmZXJUeXBlID0gKDAsICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1KS5EZWZhdWx0LCBvcnRobyA9IGZhbHNlKSB7XG4gICAgICAgIHRoaXMuZmlyc3RGcmFtZSgpO1xuICAgICAgICB0aGlzLnNhbXBsZXMgPSB0aGlzLmdlbmVyYXRlSGVtaXNwaGVyZVNhbXBsZXModGhpcy5jb25maWd1cmF0aW9uLmFvU2FtcGxlcyk7XG4gICAgICAgIGNvbnN0IGUgPSB7XG4gICAgICAgICAgICAuLi4oMCwgJDFlZDQ1OTY4YzExNjBjM2MkZXhwb3J0JGM5YjI2M2I5YTE3ZGZmZDcpXG4gICAgICAgIH07XG4gICAgICAgIGUuZnJhZ21lbnRTaGFkZXIgPSBlLmZyYWdtZW50U2hhZGVyLnJlcGxhY2UoXCIxNlwiLCB0aGlzLmNvbmZpZ3VyYXRpb24uYW9TYW1wbGVzKS5yZXBsYWNlKFwiMTYuMFwiLCB0aGlzLmNvbmZpZ3VyYXRpb24uYW9TYW1wbGVzICsgXCIuMFwiKTtcbiAgICAgICAgaWYgKGRlcHRoQnVmZmVyVHlwZSA9PT0gKDAsICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1KS5Mb2cpIGUuZnJhZ21lbnRTaGFkZXIgPSBcIiNkZWZpbmUgTE9HREVQVEhcXG5cIiArIGUuZnJhZ21lbnRTaGFkZXI7XG4gICAgICAgIGVsc2UgaWYgKGRlcHRoQnVmZmVyVHlwZSA9PT0gKDAsICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1KS5SZXZlcnNlKSBlLmZyYWdtZW50U2hhZGVyID0gXCIjZGVmaW5lIFJFVkVSU0VERVBUSFxcblwiICsgZS5mcmFnbWVudFNoYWRlcjtcbiAgICAgICAgaWYgKG9ydGhvKSBlLmZyYWdtZW50U2hhZGVyID0gXCIjZGVmaW5lIE9SVEhPXFxuXCIgKyBlLmZyYWdtZW50U2hhZGVyO1xuICAgICAgICBpZiAodGhpcy5jb25maWd1cmF0aW9uLmhhbGZSZXMpIGUuZnJhZ21lbnRTaGFkZXIgPSBcIiNkZWZpbmUgSEFMRlJFU1xcblwiICsgZS5mcmFnbWVudFNoYWRlcjtcbiAgICAgICAgaWYgKHRoaXMuZWZmZWN0U2hhZGVyUXVhZCkge1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RTaGFkZXJRdWFkLm1hdGVyaWFsLmRpc3Bvc2UoKTtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0U2hhZGVyUXVhZC5tYXRlcmlhbCA9IG5ldyAkNVdoZTMkU2hhZGVyTWF0ZXJpYWwoZSk7XG4gICAgICAgIH0gZWxzZSB0aGlzLmVmZmVjdFNoYWRlclF1YWQgPSBuZXcgKDAsICRlNGNhOGRjYjAyMThmODQ2JGV4cG9ydCRkY2Q2NzBkNzNkYjc1MWY1KShuZXcgJDVXaGUzJFNoYWRlck1hdGVyaWFsKGUpKTtcbiAgICB9XG4gICAgY29uZmlndXJlRGVub2lzZVBhc3MoZGVwdGhCdWZmZXJUeXBlID0gKDAsICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1KS5EZWZhdWx0LCBvcnRobyA9IGZhbHNlKSB7XG4gICAgICAgIHRoaXMuZmlyc3RGcmFtZSgpO1xuICAgICAgICB0aGlzLnNhbXBsZXNEZW5vaXNlID0gdGhpcy5nZW5lcmF0ZURlbm9pc2VTYW1wbGVzKHRoaXMuY29uZmlndXJhdGlvbi5kZW5vaXNlU2FtcGxlcywgMTEpO1xuICAgICAgICBjb25zdCBwID0ge1xuICAgICAgICAgICAgLi4uKDAsICRlNTIzNzhjZDBmNWE5NzNkJGV4cG9ydCQ1Nzg1NmI1OWYzMTcyNjJlKVxuICAgICAgICB9O1xuICAgICAgICBwLmZyYWdtZW50U2hhZGVyID0gcC5mcmFnbWVudFNoYWRlci5yZXBsYWNlKFwiMTZcIiwgdGhpcy5jb25maWd1cmF0aW9uLmRlbm9pc2VTYW1wbGVzKTtcbiAgICAgICAgaWYgKGRlcHRoQnVmZmVyVHlwZSA9PT0gKDAsICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1KS5Mb2cpIHAuZnJhZ21lbnRTaGFkZXIgPSBcIiNkZWZpbmUgTE9HREVQVEhcXG5cIiArIHAuZnJhZ21lbnRTaGFkZXI7XG4gICAgICAgIGVsc2UgaWYgKGRlcHRoQnVmZmVyVHlwZSA9PT0gKDAsICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1KS5SZXZlcnNlKSBwLmZyYWdtZW50U2hhZGVyID0gXCIjZGVmaW5lIFJFVkVSU0VERVBUSFxcblwiICsgcC5mcmFnbWVudFNoYWRlcjtcbiAgICAgICAgaWYgKG9ydGhvKSBwLmZyYWdtZW50U2hhZGVyID0gXCIjZGVmaW5lIE9SVEhPXFxuXCIgKyBwLmZyYWdtZW50U2hhZGVyO1xuICAgICAgICBpZiAodGhpcy5wb2lzc29uQmx1clF1YWQpIHtcbiAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLmRpc3Bvc2UoKTtcbiAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsID0gbmV3ICQ1V2hlMyRTaGFkZXJNYXRlcmlhbChwKTtcbiAgICAgICAgfSBlbHNlIHRoaXMucG9pc3NvbkJsdXJRdWFkID0gbmV3ICgwLCAkZTRjYThkY2IwMjE4Zjg0NiRleHBvcnQkZGNkNjcwZDczZGI3NTFmNSkobmV3ICQ1V2hlMyRTaGFkZXJNYXRlcmlhbChwKSk7XG4gICAgfVxuICAgIGNvbmZpZ3VyZUVmZmVjdENvbXBvc2l0ZXIoZGVwdGhCdWZmZXJUeXBlID0gKDAsICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1KS5EZWZhdWx0LCBvcnRobyA9IGZhbHNlKSB7XG4gICAgICAgIHRoaXMuZmlyc3RGcmFtZSgpO1xuICAgICAgICBjb25zdCBlID0ge1xuICAgICAgICAgICAgLi4uKDAsICQxMmIyMWQyNGQxMTkyYTA0JGV4cG9ydCRhODE1YWNjY2JkMmM5YTQ5KVxuICAgICAgICB9O1xuICAgICAgICBpZiAoZGVwdGhCdWZmZXJUeXBlID09PSAoMCwgJDA1ZjY5OTdlNGI2NWRhMTQkZXhwb3J0JGVkNGVlNWQxZTU1NDc0YTUpLkxvZykgZS5mcmFnbWVudFNoYWRlciA9IFwiI2RlZmluZSBMT0dERVBUSFxcblwiICsgZS5mcmFnbWVudFNoYWRlcjtcbiAgICAgICAgZWxzZSBpZiAoZGVwdGhCdWZmZXJUeXBlID09PSAoMCwgJDA1ZjY5OTdlNGI2NWRhMTQkZXhwb3J0JGVkNGVlNWQxZTU1NDc0YTUpLlJldmVyc2UpIGUuZnJhZ21lbnRTaGFkZXIgPSBcIiNkZWZpbmUgUkVWRVJTRURFUFRIXFxuXCIgKyBlLmZyYWdtZW50U2hhZGVyO1xuICAgICAgICBpZiAob3J0aG8pIGUuZnJhZ21lbnRTaGFkZXIgPSBcIiNkZWZpbmUgT1JUSE9cXG5cIiArIGUuZnJhZ21lbnRTaGFkZXI7XG4gICAgICAgIGlmICh0aGlzLmNvbmZpZ3VyYXRpb24uaGFsZlJlcyAmJiB0aGlzLmNvbmZpZ3VyYXRpb24uZGVwdGhBd2FyZVVwc2FtcGxpbmcpIGUuZnJhZ21lbnRTaGFkZXIgPSBcIiNkZWZpbmUgSEFMRlJFU1xcblwiICsgZS5mcmFnbWVudFNoYWRlcjtcbiAgICAgICAgaWYgKHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQpIHtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwuZGlzcG9zZSgpO1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbCA9IG5ldyAkNVdoZTMkU2hhZGVyTWF0ZXJpYWwoZSk7XG4gICAgICAgIH0gZWxzZSB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkID0gbmV3ICgwLCAkZTRjYThkY2IwMjE4Zjg0NiRleHBvcnQkZGNkNjcwZDczZGI3NTFmNSkobmV3ICQ1V2hlMyRTaGFkZXJNYXRlcmlhbChlKSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAgICAgKiBcbiAgICAgICAgICogQHBhcmFtIHtOdW1iZXJ9IG4gXG4gICAgICAgICAqIEByZXR1cm5zIHtUSFJFRS5WZWN0b3IzW119XG4gICAgICAgICAqLyBnZW5lcmF0ZUhlbWlzcGhlcmVTYW1wbGVzKG4pIHtcbiAgICAgICAgY29uc3QgcG9pbnRzID0gW107XG4gICAgICAgIGZvcihsZXQgayA9IDA7IGsgPCBuOyBrKyspe1xuICAgICAgICAgICAgY29uc3QgdGhldGEgPSAyLjM5OTk2MyAqIGs7XG4gICAgICAgICAgICBjb25zdCByID0gTWF0aC5zcXJ0KGsgKyAwLjUpIC8gTWF0aC5zcXJ0KG4pO1xuICAgICAgICAgICAgY29uc3QgeCA9IHIgKiBNYXRoLmNvcyh0aGV0YSk7XG4gICAgICAgICAgICBjb25zdCB5ID0gciAqIE1hdGguc2luKHRoZXRhKTtcbiAgICAgICAgICAgIC8vIFByb2plY3QgdG8gaGVtaXNwaGVyZVxuICAgICAgICAgICAgY29uc3QgeiA9IE1hdGguc3FydCgxIC0gKHggKiB4ICsgeSAqIHkpKTtcbiAgICAgICAgICAgIHBvaW50cy5wdXNoKG5ldyAkNVdoZTMkVmVjdG9yMyh4LCB5LCB6KSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHBvaW50cztcbiAgICB9XG4gICAgLyoqXG4gICAgICAgICAqIFxuICAgICAgICAgKiBAcGFyYW0ge251bWJlcn0gbnVtU2FtcGxlcyBcbiAgICAgICAgICogQHBhcmFtIHtudW1iZXJ9IG51bVJpbmdzIFxuICAgICAgICAgKiBAcmV0dXJucyB7VEhSRUUuVmVjdG9yMltdfVxuICAgICAgICAgKi8gZ2VuZXJhdGVEZW5vaXNlU2FtcGxlcyhudW1TYW1wbGVzLCBudW1SaW5ncykge1xuICAgICAgICBjb25zdCBhbmdsZVN0ZXAgPSAyICogTWF0aC5QSSAqIG51bVJpbmdzIC8gbnVtU2FtcGxlcztcbiAgICAgICAgY29uc3QgaW52TnVtU2FtcGxlcyA9IDEuMCAvIG51bVNhbXBsZXM7XG4gICAgICAgIGNvbnN0IHJhZGl1c1N0ZXAgPSBpbnZOdW1TYW1wbGVzO1xuICAgICAgICBjb25zdCBzYW1wbGVzID0gW107XG4gICAgICAgIGxldCByYWRpdXMgPSBpbnZOdW1TYW1wbGVzO1xuICAgICAgICBsZXQgYW5nbGUgPSAwO1xuICAgICAgICBmb3IobGV0IGkgPSAwOyBpIDwgbnVtU2FtcGxlczsgaSsrKXtcbiAgICAgICAgICAgIHNhbXBsZXMucHVzaChuZXcgJDVXaGUzJFZlY3RvcjIoTWF0aC5jb3MoYW5nbGUpLCBNYXRoLnNpbihhbmdsZSkpLm11bHRpcGx5U2NhbGFyKE1hdGgucG93KHJhZGl1cywgMC43NSkpKTtcbiAgICAgICAgICAgIHJhZGl1cyArPSByYWRpdXNTdGVwO1xuICAgICAgICAgICAgYW5nbGUgKz0gYW5nbGVTdGVwO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBzYW1wbGVzO1xuICAgIH1cbiAgICBzZXRTaXplKHdpZHRoLCBoZWlnaHQpIHtcbiAgICAgICAgdGhpcy5maXJzdEZyYW1lKCk7XG4gICAgICAgIHRoaXMud2lkdGggPSB3aWR0aDtcbiAgICAgICAgdGhpcy5oZWlnaHQgPSBoZWlnaHQ7XG4gICAgICAgIGNvbnN0IGMgPSB0aGlzLmNvbmZpZ3VyYXRpb24uaGFsZlJlcyA/IDAuNSA6IDE7XG4gICAgICAgIHRoaXMud3JpdGVUYXJnZXRJbnRlcm5hbC5zZXRTaXplKHdpZHRoICogYywgaGVpZ2h0ICogYyk7XG4gICAgICAgIHRoaXMucmVhZFRhcmdldEludGVybmFsLnNldFNpemUod2lkdGggKiBjLCBoZWlnaHQgKiBjKTtcbiAgICAgICAgdGhpcy5hY2N1bXVsYXRpb25SZW5kZXJUYXJnZXQuc2V0U2l6ZSh3aWR0aCAqIGMsIGhlaWdodCAqIGMpO1xuICAgICAgICBpZiAodGhpcy5jb25maWd1cmF0aW9uLmhhbGZSZXMpIHRoaXMuZGVwdGhEb3duc2FtcGxlVGFyZ2V0LnNldFNpemUod2lkdGggKiBjLCBoZWlnaHQgKiBjKTtcbiAgICAgICAgaWYgKHRoaXMuY29uZmlndXJhdGlvbi50cmFuc3BhcmVuY3lBd2FyZSkge1xuICAgICAgICAgICAgdGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV0ZhbHNlLnNldFNpemUod2lkdGgsIGhlaWdodCk7XG4gICAgICAgICAgICB0aGlzLnRyYW5zcGFyZW5jeVJlbmRlclRhcmdldERXVHJ1ZS5zZXRTaXplKHdpZHRoLCBoZWlnaHQpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMub3V0cHV0VGFyZ2V0SW50ZXJuYWwuc2V0U2l6ZSh3aWR0aCwgaGVpZ2h0KTtcbiAgICB9XG4gICAgc2V0RGVwdGhUZXh0dXJlKGRlcHRoVGV4dHVyZSkge1xuICAgICAgICB0aGlzLmRlcHRoVGV4dHVyZSA9IGRlcHRoVGV4dHVyZTtcbiAgICB9XG4gICAgZmlyc3RGcmFtZSgpIHtcbiAgICAgICAgdGhpcy5uZWVkc0ZyYW1lID0gdHJ1ZTtcbiAgICB9XG4gICAgcmVuZGVyKHJlbmRlcmVyLCBpbnB1dEJ1ZmZlciwgb3V0cHV0QnVmZmVyKSB7XG4gICAgICAgIGNvbnN0IHhyRW5hYmxlZCA9IHJlbmRlcmVyLnhyLmVuYWJsZWQ7XG4gICAgICAgIHJlbmRlcmVyLnhyLmVuYWJsZWQgPSBmYWxzZTtcbiAgICAgICAgLy8gQ29weSBpbnB1dEJ1ZmZlciB0byBvdXRwdXRCdWZmZXJcbiAgICAgICAgLy9yZW5kZXJlci5zZXRSZW5kZXJUYXJnZXQob3V0cHV0QnVmZmVyKTtcbiAgICAgICAgLy8gIHRoaXMuY29weVF1YWQubWF0ZXJpYWwudW5pZm9ybXMudERpZmZ1c2UudmFsdWUgPSBpbnB1dEJ1ZmZlci50ZXh0dXJlO1xuICAgICAgICAvLyAgIHRoaXMuY29weVF1YWQucmVuZGVyKHJlbmRlcmVyKTtcbiAgICAgICAgaWYgKHJlbmRlcmVyLmNhcGFiaWxpdGllcy5sb2dhcml0aG1pY0RlcHRoQnVmZmVyICYmIHRoaXMuY29uZmlndXJhdGlvbi5kZXB0aEJ1ZmZlclR5cGUgIT09ICgwLCAkMDVmNjk5N2U0YjY1ZGExNCRleHBvcnQkZWQ0ZWU1ZDFlNTU0NzRhNSkuTG9nIHx8IHJlbmRlcmVyLmNhcGFiaWxpdGllcy5yZXZlcnNlRGVwdGhCdWZmZXIgJiYgdGhpcy5jb25maWd1cmF0aW9uLmRlcHRoQnVmZmVyVHlwZSAhPT0gKDAsICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1KS5SZXZlcnNlKSB7XG4gICAgICAgICAgICB0aGlzLmNvbmZpZ3VyYXRpb24uZGVwdGhCdWZmZXJUeXBlID0gcmVuZGVyZXIuY2FwYWJpbGl0aWVzLmxvZ2FyaXRobWljRGVwdGhCdWZmZXIgPyAoMCwgJDA1ZjY5OTdlNGI2NWRhMTQkZXhwb3J0JGVkNGVlNWQxZTU1NDc0YTUpLkxvZyA6IHJlbmRlcmVyLmNhcGFiaWxpdGllcy5yZXZlcnNlRGVwdGhCdWZmZXIgPyAoMCwgJDA1ZjY5OTdlNGI2NWRhMTQkZXhwb3J0JGVkNGVlNWQxZTU1NDc0YTUpLlJldmVyc2UgOiAoMCwgJDA1ZjY5OTdlNGI2NWRhMTQkZXhwb3J0JGVkNGVlNWQxZTU1NDc0YTUpLkRlZmF1bHQ7XG4gICAgICAgICAgICB0aGlzLmNvbmZpZ3VyZUFPUGFzcyh0aGlzLmNvbmZpZ3VyYXRpb24uZGVwdGhCdWZmZXJUeXBlLCB0aGlzLmNhbWVyYS5pc09ydGhvZ3JhcGhpY0NhbWVyYSk7XG4gICAgICAgICAgICB0aGlzLmNvbmZpZ3VyZURlbm9pc2VQYXNzKHRoaXMuY29uZmlndXJhdGlvbi5kZXB0aEJ1ZmZlclR5cGUsIHRoaXMuY2FtZXJhLmlzT3J0aG9ncmFwaGljQ2FtZXJhKTtcbiAgICAgICAgICAgIHRoaXMuY29uZmlndXJlRWZmZWN0Q29tcG9zaXRlcih0aGlzLmNvbmZpZ3VyYXRpb24uZGVwdGhCdWZmZXJUeXBlLCB0aGlzLmNhbWVyYS5pc09ydGhvZ3JhcGhpY0NhbWVyYSk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5kZXRlY3RUcmFuc3BhcmVuY3koKTtcbiAgICAgICAgaWYgKGlucHV0QnVmZmVyLnRleHR1cmUudHlwZSAhPT0gdGhpcy5vdXRwdXRUYXJnZXRJbnRlcm5hbC50ZXh0dXJlLnR5cGUgfHwgaW5wdXRCdWZmZXIudGV4dHVyZS5mb3JtYXQgIT09IHRoaXMub3V0cHV0VGFyZ2V0SW50ZXJuYWwudGV4dHVyZS5mb3JtYXQpIHtcbiAgICAgICAgICAgIHRoaXMub3V0cHV0VGFyZ2V0SW50ZXJuYWwudGV4dHVyZS50eXBlID0gaW5wdXRCdWZmZXIudGV4dHVyZS50eXBlO1xuICAgICAgICAgICAgdGhpcy5vdXRwdXRUYXJnZXRJbnRlcm5hbC50ZXh0dXJlLmZvcm1hdCA9IGlucHV0QnVmZmVyLnRleHR1cmUuZm9ybWF0O1xuICAgICAgICAgICAgdGhpcy5vdXRwdXRUYXJnZXRJbnRlcm5hbC50ZXh0dXJlLm5lZWRzVXBkYXRlID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmNhbWVyYS51cGRhdGVNYXRyaXhXb3JsZCgpO1xuICAgICAgICBpZiAodGhpcy5sYXN0Vmlld01hdHJpeC5lcXVhbHModGhpcy5jYW1lcmEubWF0cml4V29ybGRJbnZlcnNlKSAmJiB0aGlzLmxhc3RQcm9qZWN0aW9uTWF0cml4LmVxdWFscyh0aGlzLmNhbWVyYS5wcm9qZWN0aW9uTWF0cml4KSAmJiB0aGlzLmNvbmZpZ3VyYXRpb24uYWNjdW11bGF0ZSAmJiAhdGhpcy5uZWVkc0ZyYW1lKSB0aGlzLmZyYW1lKys7XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgaWYgKHRoaXMuY29uZmlndXJhdGlvbi5hY2N1bXVsYXRlKSB7XG4gICAgICAgICAgICAgICAgcmVuZGVyZXIuc2V0UmVuZGVyVGFyZ2V0KHRoaXMuYWNjdW11bGF0aW9uUmVuZGVyVGFyZ2V0KTtcbiAgICAgICAgICAgICAgICByZW5kZXJlci5jbGVhcih0cnVlLCB0cnVlLCB0cnVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuZnJhbWUgPSAwO1xuICAgICAgICAgICAgdGhpcy5uZWVkc0ZyYW1lID0gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5sYXN0Vmlld01hdHJpeC5jb3B5KHRoaXMuY2FtZXJhLm1hdHJpeFdvcmxkSW52ZXJzZSk7XG4gICAgICAgIHRoaXMubGFzdFByb2plY3Rpb25NYXRyaXguY29weSh0aGlzLmNhbWVyYS5wcm9qZWN0aW9uTWF0cml4KTtcbiAgICAgICAgbGV0IGdsO1xuICAgICAgICBsZXQgZXh0O1xuICAgICAgICBsZXQgdGltZXJRdWVyeTtcbiAgICAgICAgaWYgKHRoaXMuZGVidWdNb2RlKSB7XG4gICAgICAgICAgICBnbCA9IHJlbmRlcmVyLmdldENvbnRleHQoKTtcbiAgICAgICAgICAgIGV4dCA9IGdsLmdldEV4dGVuc2lvbihcIkVYVF9kaXNqb2ludF90aW1lcl9xdWVyeV93ZWJnbDJcIik7XG4gICAgICAgICAgICBpZiAoZXh0ID09PSBudWxsKSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkVYVF9kaXNqb2ludF90aW1lcl9xdWVyeV93ZWJnbDIgbm90IGF2YWlsYWJsZSwgZGlzYWJsaW5nIGRlYnVnIG1vZGUuXCIpO1xuICAgICAgICAgICAgICAgIHRoaXMuZGVidWdNb2RlID0gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMuZGVidWdNb2RlKSB7XG4gICAgICAgICAgICB0aW1lclF1ZXJ5ID0gZ2wuY3JlYXRlUXVlcnkoKTtcbiAgICAgICAgICAgIGdsLmJlZ2luUXVlcnkoZXh0LlRJTUVfRUxBUFNFRF9FWFQsIHRpbWVyUXVlcnkpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLmNvbmZpZ3VyYXRpb24udHJhbnNwYXJlbmN5QXdhcmUpIHRoaXMucmVuZGVyVHJhbnNwYXJlbmN5KHJlbmRlcmVyKTtcbiAgICAgICAgdGhpcy5fci5zZXQodGhpcy53aWR0aCwgdGhpcy5oZWlnaHQpO1xuICAgICAgICBsZXQgdHJ1ZVJhZGl1cyA9IHRoaXMuY29uZmlndXJhdGlvbi5hb1JhZGl1cztcbiAgICAgICAgaWYgKHRoaXMuY29uZmlndXJhdGlvbi5oYWxmUmVzICYmIHRoaXMuY29uZmlndXJhdGlvbi5zY3JlZW5TcGFjZVJhZGl1cykgdHJ1ZVJhZGl1cyAqPSAwLjU7XG4gICAgICAgIGlmICh0aGlzLmZyYW1lIDwgMTAyNCAvIHRoaXMuY29uZmlndXJhdGlvbi5hb1NhbXBsZXMpIHtcbiAgICAgICAgICAgIGlmICh0aGlzLmNvbmZpZ3VyYXRpb24uaGFsZlJlcykge1xuICAgICAgICAgICAgICAgIHJlbmRlcmVyLnNldFJlbmRlclRhcmdldCh0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldCk7XG4gICAgICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVRdWFkLm1hdGVyaWFsLnVuaWZvcm1zLnNjZW5lRGVwdGgudmFsdWUgPSB0aGlzLmRlcHRoVGV4dHVyZTtcbiAgICAgICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVF1YWQubWF0ZXJpYWwudW5pZm9ybXMucmVzb2x1dGlvbi52YWx1ZSA9IHRoaXMuX3I7XG4gICAgICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wibmVhclwiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLm5lYXI7XG4gICAgICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiZmFyXCJdLnZhbHVlID0gdGhpcy5jYW1lcmEuZmFyO1xuICAgICAgICAgICAgICAgIHRoaXMuZGVwdGhEb3duc2FtcGxlUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInByb2plY3Rpb25NYXRyaXhJbnZcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5wcm9qZWN0aW9uTWF0cml4SW52ZXJzZTtcbiAgICAgICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJ2aWV3TWF0cml4SW52XCJdLnZhbHVlID0gdGhpcy5jYW1lcmEubWF0cml4V29ybGQ7XG4gICAgICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wibG9nRGVwdGhcIl0udmFsdWUgPSB0aGlzLmNvbmZpZ3VyYXRpb24ubG9nYXJpdGhtaWNEZXB0aEJ1ZmZlcjtcbiAgICAgICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJvcnRob1wiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLmlzT3J0aG9ncmFwaGljQ2FtZXJhO1xuICAgICAgICAgICAgICAgIHRoaXMuZGVwdGhEb3duc2FtcGxlUXVhZC5yZW5kZXIocmVuZGVyZXIpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5lZmZlY3RTaGFkZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wic2NlbmVEaWZmdXNlXCJdLnZhbHVlID0gaW5wdXRCdWZmZXIudGV4dHVyZTtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0U2hhZGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInNjZW5lRGVwdGhcIl0udmFsdWUgPSB0aGlzLmNvbmZpZ3VyYXRpb24uaGFsZlJlcyA/IHRoaXMuZGVwdGhEb3duc2FtcGxlVGFyZ2V0LnRleHR1cmVzWzBdIDogdGhpcy5kZXB0aFRleHR1cmU7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJzY2VuZU5vcm1hbFwiXS52YWx1ZSA9IHRoaXMuY29uZmlndXJhdGlvbi5oYWxmUmVzID8gdGhpcy5kZXB0aERvd25zYW1wbGVUYXJnZXQudGV4dHVyZXNbMV0gOiBudWxsO1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RTaGFkZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wicHJvak1hdFwiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLnByb2plY3Rpb25NYXRyaXg7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJ2aWV3TWF0XCJdLnZhbHVlID0gdGhpcy5jYW1lcmEubWF0cml4V29ybGRJbnZlcnNlO1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RTaGFkZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wicHJvalZpZXdNYXRcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5wcm9qZWN0aW9uTWF0cml4LmNsb25lKCkubXVsdGlwbHkodGhpcy5jYW1lcmEubWF0cml4V29ybGRJbnZlcnNlLmNsb25lKCkpO1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RTaGFkZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wicHJvamVjdGlvbk1hdHJpeEludlwiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLnByb2plY3Rpb25NYXRyaXhJbnZlcnNlO1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RTaGFkZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1widmlld01hdHJpeEludlwiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLm1hdHJpeFdvcmxkO1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RTaGFkZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiY2FtZXJhUG9zXCJdLnZhbHVlID0gdGhpcy5jYW1lcmEuZ2V0V29ybGRQb3NpdGlvbihuZXcgJDVXaGUzJFZlY3RvcjMoKSk7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJiaWFzQWRqdXN0bWVudFwiXS52YWx1ZSA9IG5ldyAkNVdoZTMkVmVjdG9yMih0aGlzLmNvbmZpZ3VyYXRpb24uYmlhc09mZnNldCwgdGhpcy5jb25maWd1cmF0aW9uLmJpYXNNdWx0aXBsaWVyKTtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0U2hhZGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInJlc29sdXRpb25cIl0udmFsdWUgPSB0aGlzLmNvbmZpZ3VyYXRpb24uaGFsZlJlcyA/IHRoaXMuX3IuY2xvbmUoKS5tdWx0aXBseVNjYWxhcigwLjUpLmZsb29yKCkgOiB0aGlzLl9yO1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RTaGFkZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1widGltZVwiXS52YWx1ZSA9IHBlcmZvcm1hbmNlLm5vdygpIC8gMTAwMDtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0U2hhZGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInNhbXBsZXNcIl0udmFsdWUgPSB0aGlzLnNhbXBsZXM7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJibHVlbm9pc2VcIl0udmFsdWUgPSB0aGlzLmJsdWVub2lzZTtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0U2hhZGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInJhZGl1c1wiXS52YWx1ZSA9IHRydWVSYWRpdXM7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJkaXN0YW5jZUZhbGxvZmZcIl0udmFsdWUgPSB0aGlzLmNvbmZpZ3VyYXRpb24uZGlzdGFuY2VGYWxsb2ZmO1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RTaGFkZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wibmVhclwiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLm5lYXI7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJmYXJcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5mYXI7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJvcnRob1wiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLmlzT3J0aG9ncmFwaGljQ2FtZXJhO1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RTaGFkZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wic2NyZWVuU3BhY2VSYWRpdXNcIl0udmFsdWUgPSB0aGlzLmNvbmZpZ3VyYXRpb24uc2NyZWVuU3BhY2VSYWRpdXM7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJmcmFtZVwiXS52YWx1ZSA9IHRoaXMuZnJhbWU7XG4gICAgICAgICAgICAvLyBTdGFydCB0aGUgQU9cbiAgICAgICAgICAgIHJlbmRlcmVyLnNldFJlbmRlclRhcmdldCh0aGlzLndyaXRlVGFyZ2V0SW50ZXJuYWwpO1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RTaGFkZXJRdWFkLnJlbmRlcihyZW5kZXJlcik7XG4gICAgICAgICAgICAvLyBFbmQgdGhlIEFPXG4gICAgICAgICAgICAvLyBTdGFydCB0aGUgYmx1clxuICAgICAgICAgICAgZm9yKGxldCBpID0gMDsgaSA8IHRoaXMuY29uZmlndXJhdGlvbi5kZW5vaXNlSXRlcmF0aW9uczsgaSsrKXtcbiAgICAgICAgICAgICAgICBbdGhpcy53cml0ZVRhcmdldEludGVybmFsLCB0aGlzLnJlYWRUYXJnZXRJbnRlcm5hbF0gPSBbXG4gICAgICAgICAgICAgICAgICAgIHRoaXMucmVhZFRhcmdldEludGVybmFsLFxuICAgICAgICAgICAgICAgICAgICB0aGlzLndyaXRlVGFyZ2V0SW50ZXJuYWxcbiAgICAgICAgICAgICAgICBdO1xuICAgICAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1widERpZmZ1c2VcIl0udmFsdWUgPSB0aGlzLnJlYWRUYXJnZXRJbnRlcm5hbC50ZXh0dXJlO1xuICAgICAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wic2NlbmVEZXB0aFwiXS52YWx1ZSA9IHRoaXMuY29uZmlndXJhdGlvbi5oYWxmUmVzID8gdGhpcy5kZXB0aERvd25zYW1wbGVUYXJnZXQudGV4dHVyZXNbMF0gOiB0aGlzLmRlcHRoVGV4dHVyZTtcbiAgICAgICAgICAgICAgICB0aGlzLnBvaXNzb25CbHVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInByb2pNYXRcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5wcm9qZWN0aW9uTWF0cml4O1xuICAgICAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1widmlld01hdFwiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLm1hdHJpeFdvcmxkSW52ZXJzZTtcbiAgICAgICAgICAgICAgICB0aGlzLnBvaXNzb25CbHVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInByb2plY3Rpb25NYXRyaXhJbnZcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5wcm9qZWN0aW9uTWF0cml4SW52ZXJzZTtcbiAgICAgICAgICAgICAgICB0aGlzLnBvaXNzb25CbHVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInZpZXdNYXRyaXhJbnZcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5tYXRyaXhXb3JsZDtcbiAgICAgICAgICAgICAgICB0aGlzLnBvaXNzb25CbHVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImNhbWVyYVBvc1wiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLmdldFdvcmxkUG9zaXRpb24obmV3ICQ1V2hlMyRWZWN0b3IzKCkpO1xuICAgICAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wicmVzb2x1dGlvblwiXS52YWx1ZSA9IHRoaXMuY29uZmlndXJhdGlvbi5oYWxmUmVzID8gdGhpcy5fci5jbG9uZSgpLm11bHRpcGx5U2NhbGFyKDAuNSkuZmxvb3IoKSA6IHRoaXMuX3I7XG4gICAgICAgICAgICAgICAgdGhpcy5wb2lzc29uQmx1clF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJ0aW1lXCJdLnZhbHVlID0gcGVyZm9ybWFuY2Uubm93KCkgLyAxMDAwO1xuICAgICAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiYmx1ZU5vaXNlXCJdLnZhbHVlID0gdGhpcy5ibHVlbm9pc2U7XG4gICAgICAgICAgICAgICAgdGhpcy5wb2lzc29uQmx1clF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJyYWRpdXNcIl0udmFsdWUgPSB0aGlzLmNvbmZpZ3VyYXRpb24uZGVub2lzZVJhZGl1cyAqICh0aGlzLmNvbmZpZ3VyYXRpb24uaGFsZlJlcyA/IDAuNSA6IDEpO1xuICAgICAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wid29ybGRSYWRpdXNcIl0udmFsdWUgPSB0cnVlUmFkaXVzO1xuICAgICAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiZGlzdGFuY2VGYWxsb2ZmXCJdLnZhbHVlID0gdGhpcy5jb25maWd1cmF0aW9uLmRpc3RhbmNlRmFsbG9mZjtcbiAgICAgICAgICAgICAgICB0aGlzLnBvaXNzb25CbHVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImluZGV4XCJdLnZhbHVlID0gaTtcbiAgICAgICAgICAgICAgICB0aGlzLnBvaXNzb25CbHVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInBvaXNzb25EaXNrXCJdLnZhbHVlID0gdGhpcy5zYW1wbGVzRGVub2lzZTtcbiAgICAgICAgICAgICAgICB0aGlzLnBvaXNzb25CbHVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcIm5lYXJcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5uZWFyO1xuICAgICAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiZmFyXCJdLnZhbHVlID0gdGhpcy5jYW1lcmEuZmFyO1xuICAgICAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wic2NyZWVuU3BhY2VSYWRpdXNcIl0udmFsdWUgPSB0aGlzLmNvbmZpZ3VyYXRpb24uc2NyZWVuU3BhY2VSYWRpdXM7XG4gICAgICAgICAgICAgICAgcmVuZGVyZXIuc2V0UmVuZGVyVGFyZ2V0KHRoaXMud3JpdGVUYXJnZXRJbnRlcm5hbCk7XG4gICAgICAgICAgICAgICAgdGhpcy5wb2lzc29uQmx1clF1YWQucmVuZGVyKHJlbmRlcmVyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJlbmRlcmVyLnNldFJlbmRlclRhcmdldCh0aGlzLmFjY3VtdWxhdGlvblJlbmRlclRhcmdldCk7XG4gICAgICAgICAgICBjb25zdCBvbGRBdXRvQ2xlYXIgPSByZW5kZXJlci5hdXRvQ2xlYXI7XG4gICAgICAgICAgICByZW5kZXJlci5hdXRvQ2xlYXIgPSBmYWxzZTtcbiAgICAgICAgICAgIHRoaXMuYWNjdW11bGF0aW9uUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInREaWZmdXNlXCJdLnZhbHVlID0gdGhpcy53cml0ZVRhcmdldEludGVybmFsLnRleHR1cmU7XG4gICAgICAgICAgICB0aGlzLmFjY3VtdWxhdGlvblF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJmcmFtZVwiXS52YWx1ZSA9IHRoaXMuZnJhbWU7XG4gICAgICAgICAgICB0aGlzLmFjY3VtdWxhdGlvblF1YWQucmVuZGVyKHJlbmRlcmVyKTtcbiAgICAgICAgICAgIHJlbmRlcmVyLmF1dG9DbGVhciA9IG9sZEF1dG9DbGVhcjtcbiAgICAgICAgfVxuICAgICAgICAvLyBOb3csIHdlIGhhdmUgdGhlIGJsdXJyZWQgQU8gaW4gd3JpdGVUYXJnZXRJbnRlcm5hbFxuICAgICAgICAvLyBFbmQgdGhlIGJsdXJcbiAgICAgICAgLy8gU3RhcnQgdGhlIGNvbXBvc2l0aW9uXG4gICAgICAgIGlmICh0aGlzLmNvbmZpZ3VyYXRpb24udHJhbnNwYXJlbmN5QXdhcmUpIHtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJ0cmFuc3BhcmVuY3lEV0ZhbHNlXCJdLnZhbHVlID0gdGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV0ZhbHNlLnRleHR1cmU7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1widHJhbnNwYXJlbmN5RFdUcnVlXCJdLnZhbHVlID0gdGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV1RydWUudGV4dHVyZTtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJ0cmFuc3BhcmVuY3lEV1RydWVEZXB0aFwiXS52YWx1ZSA9IHRoaXMudHJhbnNwYXJlbmN5UmVuZGVyVGFyZ2V0RFdUcnVlLmRlcHRoVGV4dHVyZTtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJ0cmFuc3BhcmVuY3lBd2FyZVwiXS52YWx1ZSA9IHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInNjZW5lRGlmZnVzZVwiXS52YWx1ZSA9IGlucHV0QnVmZmVyLnRleHR1cmU7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJzY2VuZURlcHRoXCJdLnZhbHVlID0gdGhpcy5kZXB0aFRleHR1cmU7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJhb1RvbmVzXCJdLnZhbHVlID0gdGhpcy5jb25maWd1cmF0aW9uLmFvVG9uZXM7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJuZWFyXCJdLnZhbHVlID0gdGhpcy5jYW1lcmEubmVhcjtcbiAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImZhclwiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLmZhcjtcbiAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInByb2plY3Rpb25NYXRyaXhJbnZcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5wcm9qZWN0aW9uTWF0cml4SW52ZXJzZTtcbiAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInZpZXdNYXRyaXhJbnZcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5tYXRyaXhXb3JsZDtcbiAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcIm9ydGhvXCJdLnZhbHVlID0gdGhpcy5jYW1lcmEuaXNPcnRob2dyYXBoaWNDYW1lcmE7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJkb3duc2FtcGxlZERlcHRoXCJdLnZhbHVlID0gdGhpcy5jb25maWd1cmF0aW9uLmhhbGZSZXMgPyB0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldC50ZXh0dXJlc1swXSA6IHRoaXMuZGVwdGhUZXh0dXJlO1xuICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wicmVzb2x1dGlvblwiXS52YWx1ZSA9IHRoaXMuX3I7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJibHVlTm9pc2VcIl0udmFsdWUgPSB0aGlzLmJsdWVub2lzZTtcbiAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImludGVuc2l0eVwiXS52YWx1ZSA9IHRoaXMuY29uZmlndXJhdGlvbi5pbnRlbnNpdHk7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJyZW5kZXJNb2RlXCJdLnZhbHVlID0gdGhpcy5jb25maWd1cmF0aW9uLnJlbmRlck1vZGU7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJzY3JlZW5TcGFjZVJhZGl1c1wiXS52YWx1ZSA9IHRoaXMuY29uZmlndXJhdGlvbi5zY3JlZW5TcGFjZVJhZGl1cztcbiAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInJhZGl1c1wiXS52YWx1ZSA9IHRydWVSYWRpdXM7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJkaXN0YW5jZUZhbGxvZmZcIl0udmFsdWUgPSB0aGlzLmNvbmZpZ3VyYXRpb24uZGlzdGFuY2VGYWxsb2ZmO1xuICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiZ2FtbWFDb3JyZWN0aW9uXCJdLnZhbHVlID0gdGhpcy5hdXRvc2V0R2FtbWEgPyB0aGlzLnJlbmRlclRvU2NyZWVuIDogdGhpcy5jb25maWd1cmF0aW9uLmdhbW1hQ29ycmVjdGlvbjtcbiAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInREaWZmdXNlXCJdLnZhbHVlID0gdGhpcy5hY2N1bXVsYXRpb25SZW5kZXJUYXJnZXQudGV4dHVyZTtcbiAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImNvbG9yXCJdLnZhbHVlID0gdGhpcy5fYy5jb3B5KHRoaXMuY29uZmlndXJhdGlvbi5jb2xvcikuY29udmVydFNSR0JUb0xpbmVhcigpO1xuICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiY29sb3JNdWx0aXBseVwiXS52YWx1ZSA9IHRoaXMuY29uZmlndXJhdGlvbi5jb2xvck11bHRpcGx5O1xuICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiY2FtZXJhUG9zXCJdLnZhbHVlID0gdGhpcy5jYW1lcmEuZ2V0V29ybGRQb3NpdGlvbihuZXcgJDVXaGUzJFZlY3RvcjMoKSk7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJmb2dcIl0udmFsdWUgPSAhIXRoaXMuc2NlbmUuZm9nO1xuICAgICAgICBpZiAodGhpcy5zY2VuZS5mb2cpIHtcbiAgICAgICAgICAgIGlmICh0aGlzLnNjZW5lLmZvZy5pc0ZvZykge1xuICAgICAgICAgICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJmb2dFeHBcIl0udmFsdWUgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiZm9nTmVhclwiXS52YWx1ZSA9IHRoaXMuc2NlbmUuZm9nLm5lYXI7XG4gICAgICAgICAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImZvZ0ZhclwiXS52YWx1ZSA9IHRoaXMuc2NlbmUuZm9nLmZhcjtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5zY2VuZS5mb2cuaXNGb2dFeHAyKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImZvZ0V4cFwiXS52YWx1ZSA9IHRydWU7XG4gICAgICAgICAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImZvZ0RlbnNpdHlcIl0udmFsdWUgPSB0aGlzLnNjZW5lLmZvZy5kZW5zaXR5O1xuICAgICAgICAgICAgfSBlbHNlIGNvbnNvbGUuZXJyb3IoYFVuc3VwcG9ydGVkIGZvZyB0eXBlICR7dGhpcy5zY2VuZS5mb2cuY29uc3RydWN0b3IubmFtZX0gaW4gU1NBT1Bhc3MuYCk7XG4gICAgICAgIH1cbiAgICAgICAgcmVuZGVyZXIuc2V0UmVuZGVyVGFyZ2V0KC8qIHRoaXMucmVuZGVyVG9TY3JlZW4gPyBudWxsIDpcbiAgICAgICAgICAgICAgICAgb3V0cHV0QnVmZmVyKi8gdGhpcy5vdXRwdXRUYXJnZXRJbnRlcm5hbCk7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQucmVuZGVyKHJlbmRlcmVyKTtcbiAgICAgICAgcmVuZGVyZXIuc2V0UmVuZGVyVGFyZ2V0KHRoaXMucmVuZGVyVG9TY3JlZW4gPyBudWxsIDogb3V0cHV0QnVmZmVyKTtcbiAgICAgICAgdGhpcy5jb3B5UXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInREaWZmdXNlXCJdLnZhbHVlID0gdGhpcy5vdXRwdXRUYXJnZXRJbnRlcm5hbC50ZXh0dXJlO1xuICAgICAgICB0aGlzLmNvcHlRdWFkLnJlbmRlcihyZW5kZXJlcik7XG4gICAgICAgIGlmICh0aGlzLmRlYnVnTW9kZSkge1xuICAgICAgICAgICAgZ2wuZW5kUXVlcnkoZXh0LlRJTUVfRUxBUFNFRF9FWFQpO1xuICAgICAgICAgICAgJDg3NDMxZWU5M2IwMzc4NDQkdmFyJGNoZWNrVGltZXJRdWVyeSh0aW1lclF1ZXJ5LCBnbCwgdGhpcyk7XG4gICAgICAgIH1cbiAgICAgICAgcmVuZGVyZXIueHIuZW5hYmxlZCA9IHhyRW5hYmxlZDtcbiAgICB9XG4gICAgLyoqXG4gICAgICAgICAqIEVuYWJsZXMgdGhlIGRlYnVnIG1vZGUgb2YgdGhlIEFPLCBtZWFuaW5nIHRoZSBsYXN0VGltZSB2YWx1ZSB3aWxsIGJlIHVwZGF0ZWQuXG4gICAgICAgICAqLyBlbmFibGVEZWJ1Z01vZGUoKSB7XG4gICAgICAgIHRoaXMuZGVidWdNb2RlID0gdHJ1ZTtcbiAgICB9XG4gICAgLyoqXG4gICAgICAgICAqIERpc2FibGVzIHRoZSBkZWJ1ZyBtb2RlIG9mIHRoZSBBTywgbWVhbmluZyB0aGUgbGFzdFRpbWUgdmFsdWUgd2lsbCBub3QgYmUgdXBkYXRlZC5cbiAgICAgICAgICovIGRpc2FibGVEZWJ1Z01vZGUoKSB7XG4gICAgICAgIHRoaXMuZGVidWdNb2RlID0gZmFsc2U7XG4gICAgfVxuICAgIC8qKlxuICAgICAgICAgKiBTZXRzIHRoZSBkaXNwbGF5IG1vZGUgb2YgdGhlIEFPXG4gICAgICAgICAqIEBwYXJhbSB7XCJDb21iaW5lZFwiIHwgXCJBT1wiIHwgXCJObyBBT1wiIHwgXCJTcGxpdFwiIHwgXCJTcGxpdCBBT1wifSBtb2RlIC0gVGhlIGRpc3BsYXkgbW9kZS4gXG4gICAgICAgICAqLyBzZXREaXNwbGF5TW9kZShtb2RlKSB7XG4gICAgICAgIHRoaXMuY29uZmlndXJhdGlvbi5yZW5kZXJNb2RlID0gW1xuICAgICAgICAgICAgXCJDb21iaW5lZFwiLFxuICAgICAgICAgICAgXCJBT1wiLFxuICAgICAgICAgICAgXCJObyBBT1wiLFxuICAgICAgICAgICAgXCJTcGxpdFwiLFxuICAgICAgICAgICAgXCJTcGxpdCBBT1wiXG4gICAgICAgIF0uaW5kZXhPZihtb2RlKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICAgICAqIFxuICAgICAgICAgKiBAcGFyYW0ge1wiUGVyZm9ybWFuY2VcIiB8IFwiTG93XCIgfCBcIk1lZGl1bVwiIHwgXCJIaWdoXCIgfCBcIlVsdHJhXCJ9IG1vZGUgXG4gICAgICAgICAqLyBzZXRRdWFsaXR5TW9kZShtb2RlKSB7XG4gICAgICAgIGlmIChtb2RlID09PSBcIlBlcmZvcm1hbmNlXCIpIHtcbiAgICAgICAgICAgIHRoaXMuY29uZmlndXJhdGlvbi5hb1NhbXBsZXMgPSA4O1xuICAgICAgICAgICAgdGhpcy5jb25maWd1cmF0aW9uLmRlbm9pc2VTYW1wbGVzID0gNDtcbiAgICAgICAgICAgIHRoaXMuY29uZmlndXJhdGlvbi5kZW5vaXNlUmFkaXVzID0gMTI7XG4gICAgICAgIH0gZWxzZSBpZiAobW9kZSA9PT0gXCJMb3dcIikge1xuICAgICAgICAgICAgdGhpcy5jb25maWd1cmF0aW9uLmFvU2FtcGxlcyA9IDE2O1xuICAgICAgICAgICAgdGhpcy5jb25maWd1cmF0aW9uLmRlbm9pc2VTYW1wbGVzID0gNDtcbiAgICAgICAgICAgIHRoaXMuY29uZmlndXJhdGlvbi5kZW5vaXNlUmFkaXVzID0gMTI7XG4gICAgICAgIH0gZWxzZSBpZiAobW9kZSA9PT0gXCJNZWRpdW1cIikge1xuICAgICAgICAgICAgdGhpcy5jb25maWd1cmF0aW9uLmFvU2FtcGxlcyA9IDE2O1xuICAgICAgICAgICAgdGhpcy5jb25maWd1cmF0aW9uLmRlbm9pc2VTYW1wbGVzID0gODtcbiAgICAgICAgICAgIHRoaXMuY29uZmlndXJhdGlvbi5kZW5vaXNlUmFkaXVzID0gMTI7XG4gICAgICAgIH0gZWxzZSBpZiAobW9kZSA9PT0gXCJIaWdoXCIpIHtcbiAgICAgICAgICAgIHRoaXMuY29uZmlndXJhdGlvbi5hb1NhbXBsZXMgPSA2NDtcbiAgICAgICAgICAgIHRoaXMuY29uZmlndXJhdGlvbi5kZW5vaXNlU2FtcGxlcyA9IDg7XG4gICAgICAgICAgICB0aGlzLmNvbmZpZ3VyYXRpb24uZGVub2lzZVJhZGl1cyA9IDY7XG4gICAgICAgIH0gZWxzZSBpZiAobW9kZSA9PT0gXCJVbHRyYVwiKSB7XG4gICAgICAgICAgICB0aGlzLmNvbmZpZ3VyYXRpb24uYW9TYW1wbGVzID0gNjQ7XG4gICAgICAgICAgICB0aGlzLmNvbmZpZ3VyYXRpb24uZGVub2lzZVNhbXBsZXMgPSAxNjtcbiAgICAgICAgICAgIHRoaXMuY29uZmlndXJhdGlvbi5kZW5vaXNlUmFkaXVzID0gNjtcbiAgICAgICAgfVxuICAgIH1cbn1cblxuXG5cblxuLyoqXG4gKiBcbiAqIEBwYXJhbSB7Kn0gdGltZXJRdWVyeSBcbiAqIEBwYXJhbSB7VEhSRUUuV2ViR0xSZW5kZXJlcn0gZ2wgXG4gKiBAcGFyYW0ge044QU9QYXNzfSBwYXNzIFxuICovIGZ1bmN0aW9uICQwNWY2OTk3ZTRiNjVkYTE0JHZhciRjaGVja1RpbWVyUXVlcnkodGltZXJRdWVyeSwgZ2wsIHBhc3MpIHtcbiAgICBjb25zdCBhdmFpbGFibGUgPSBnbC5nZXRRdWVyeVBhcmFtZXRlcih0aW1lclF1ZXJ5LCBnbC5RVUVSWV9SRVNVTFRfQVZBSUxBQkxFKTtcbiAgICBpZiAoYXZhaWxhYmxlKSB7XG4gICAgICAgIGNvbnN0IGVsYXBzZWRUaW1lSW5OcyA9IGdsLmdldFF1ZXJ5UGFyYW1ldGVyKHRpbWVyUXVlcnksIGdsLlFVRVJZX1JFU1VMVCk7XG4gICAgICAgIGNvbnN0IGVsYXBzZWRUaW1lSW5NcyA9IGVsYXBzZWRUaW1lSW5OcyAvIDEwMDAwMDA7XG4gICAgICAgIHBhc3MubGFzdFRpbWUgPSBwYXNzLmxhc3RUaW1lID09PSAwID8gZWxhcHNlZFRpbWVJbk1zIDogcGFzcy50aW1lUm9sbGluZ0F2ZXJhZ2UgKiBwYXNzLmxhc3RUaW1lICsgKDEgLSBwYXNzLnRpbWVSb2xsaW5nQXZlcmFnZSkgKiBlbGFwc2VkVGltZUluTXM7XG4gICAgfSBlbHNlIC8vIElmIHRoZSByZXN1bHQgaXMgbm90IGF2YWlsYWJsZSB5ZXQsIGNoZWNrIGFnYWluIGFmdGVyIGEgZGVsYXlcbiAgICBzZXRUaW1lb3V0KCgpPT57XG4gICAgICAgICQwNWY2OTk3ZTRiNjVkYTE0JHZhciRjaGVja1RpbWVyUXVlcnkodGltZXJRdWVyeSwgZ2wsIHBhc3MpO1xuICAgIH0sIDEpO1xufVxuY29uc3QgJDA1ZjY5OTdlNGI2NWRhMTQkZXhwb3J0JGVkNGVlNWQxZTU1NDc0YTUgPSB7XG4gICAgRGVmYXVsdDogMSxcbiAgICBMb2c6IDIsXG4gICAgUmV2ZXJzZTogM1xufTtcbmNsYXNzICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCQyZDU3ZGIyMGI1ZWI1ZTBhIGV4dGVuZHMgKDAsICQ1V2hlMyRQYXNzKSB7XG4gICAgLyoqXG4gICAgICogXG4gICAgICogQHBhcmFtIHtUSFJFRS5TY2VuZX0gc2NlbmVcbiAgICAgKiBAcGFyYW0ge1RIUkVFLkNhbWVyYX0gY2FtZXJhIFxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSB3aWR0aCBcbiAgICAgKiBAcGFyYW0ge251bWJlcn0gaGVpZ2h0XG4gICAgICogIFxuICAgICAqIEBwcm9wZXJ0eSB7VEhSRUUuU2NlbmV9IHNjZW5lXG4gICAgICogQHByb3BlcnR5IHtUSFJFRS5DYW1lcmF9IGNhbWVyYVxuICAgICAqIEBwcm9wZXJ0eSB7bnVtYmVyfSB3aWR0aFxuICAgICAqIEBwcm9wZXJ0eSB7bnVtYmVyfSBoZWlnaHRcbiAgICAgKi8gY29uc3RydWN0b3Ioc2NlbmUsIGNhbWVyYSwgd2lkdGggPSA1MTIsIGhlaWdodCA9IDUxMil7XG4gICAgICAgIHN1cGVyKCk7XG4gICAgICAgIHRoaXMud2lkdGggPSB3aWR0aDtcbiAgICAgICAgdGhpcy5oZWlnaHQgPSBoZWlnaHQ7XG4gICAgICAgIHRoaXMuY2xlYXIgPSB0cnVlO1xuICAgICAgICB0aGlzLmNhbWVyYSA9IGNhbWVyYTtcbiAgICAgICAgdGhpcy5zY2VuZSA9IHNjZW5lO1xuICAgICAgICAvKipcbiAgICAgICAgICogQHR5cGUge1Byb3h5ICYge1xuICAgICAgICAgKiBhb1NhbXBsZXM6IG51bWJlcixcbiAgICAgICAgICogYW9SYWRpdXM6IG51bWJlcixcbiAgICAgICAgICogZGVub2lzZVNhbXBsZXM6IG51bWJlcixcbiAgICAgICAgICogZGVub2lzZVJhZGl1czogbnVtYmVyLFxuICAgICAgICAgKiBkaXN0YW5jZUZhbGxvZmY6IG51bWJlcixcbiAgICAgICAgICogaW50ZW5zaXR5OiBudW1iZXIsXG4gICAgICAgICAqIGRlbm9pc2VJdGVyYXRpb25zOiBudW1iZXIsXG4gICAgICAgICAqIHJlbmRlck1vZGU6IDAgfCAxIHwgMiB8IDMgfCA0LFxuICAgICAgICAgKiBjb2xvcjogVEhSRUUuQ29sb3IsXG4gICAgICAgICAqIGdhbW1hQ29ycmVjdGlvbjogYm9vbGVhbixcbiAgICAgICAgICogZGVwdGhCdWZmZXJUeXBlOiAxIHwgMiB8IDMsXG4gICAgICAgICAqIHNjcmVlblNwYWNlUmFkaXVzOiBib29sZWFuLFxuICAgICAgICAgKiBoYWxmUmVzOiBib29sZWFuLFxuICAgICAgICAgKiBkZXB0aEF3YXJlVXBzYW1wbGluZzogYm9vbGVhbixcbiAgICAgICAgICogYXV0b1JlbmRlckJlYXV0eTogYm9vbGVhblxuICAgICAgICAgKiBjb2xvck11bHRpcGx5OiBib29sZWFuXG4gICAgICAgICAqIH1cbiAgICAgICAgICovIHRoaXMuY29uZmlndXJhdGlvbiA9IG5ldyBQcm94eSh7XG4gICAgICAgICAgICBhb1NhbXBsZXM6IDE2LFxuICAgICAgICAgICAgYW9SYWRpdXM6IDUuMCxcbiAgICAgICAgICAgIGFvVG9uZXM6IDAuMCxcbiAgICAgICAgICAgIGRlbm9pc2VTYW1wbGVzOiA4LFxuICAgICAgICAgICAgZGVub2lzZVJhZGl1czogMTIsXG4gICAgICAgICAgICBkaXN0YW5jZUZhbGxvZmY6IDEuMCxcbiAgICAgICAgICAgIGludGVuc2l0eTogNSxcbiAgICAgICAgICAgIGRlbm9pc2VJdGVyYXRpb25zOiAyLjAsXG4gICAgICAgICAgICByZW5kZXJNb2RlOiAwLFxuICAgICAgICAgICAgYmlhc09mZnNldDogMC4wLFxuICAgICAgICAgICAgYmlhc011bHRpcGxpZXI6IDAuMCxcbiAgICAgICAgICAgIGNvbG9yOiBuZXcgJDVXaGUzJENvbG9yKDAsIDAsIDApLFxuICAgICAgICAgICAgZ2FtbWFDb3JyZWN0aW9uOiB0cnVlLFxuICAgICAgICAgICAgZGVwdGhCdWZmZXJUeXBlOiAkMDVmNjk5N2U0YjY1ZGExNCRleHBvcnQkZWQ0ZWU1ZDFlNTU0NzRhNS5EZWZhdWx0LFxuICAgICAgICAgICAgc2NyZWVuU3BhY2VSYWRpdXM6IGZhbHNlLFxuICAgICAgICAgICAgaGFsZlJlczogZmFsc2UsXG4gICAgICAgICAgICBkZXB0aEF3YXJlVXBzYW1wbGluZzogdHJ1ZSxcbiAgICAgICAgICAgIGF1dG9SZW5kZXJCZWF1dHk6IHRydWUsXG4gICAgICAgICAgICBjb2xvck11bHRpcGx5OiB0cnVlLFxuICAgICAgICAgICAgdHJhbnNwYXJlbmN5QXdhcmU6IGZhbHNlLFxuICAgICAgICAgICAgc3RlbmNpbDogZmFsc2UsXG4gICAgICAgICAgICBhY2N1bXVsYXRlOiBmYWxzZVxuICAgICAgICB9LCB7XG4gICAgICAgICAgICBzZXQ6ICh0YXJnZXQsIHByb3BOYW1lLCB2YWx1ZSk9PntcbiAgICAgICAgICAgICAgICBjb25zdCBvbGRQcm9wID0gdGFyZ2V0W3Byb3BOYW1lXTtcbiAgICAgICAgICAgICAgICB0YXJnZXRbcHJvcE5hbWVdID0gdmFsdWU7XG4gICAgICAgICAgICAgICAgaWYgKHZhbHVlLmVxdWFscykge1xuICAgICAgICAgICAgICAgICAgICBpZiAoIXZhbHVlLmVxdWFscyhvbGRQcm9wKSkgdGhpcy5maXJzdEZyYW1lKCk7XG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmIChvbGRQcm9wICE9PSB2YWx1ZSkgdGhpcy5maXJzdEZyYW1lKCk7XG4gICAgICAgICAgICAgICAgaWYgKHByb3BOYW1lID09PSBcImFvU2FtcGxlc1wiICYmIG9sZFByb3AgIT09IHZhbHVlKSB0aGlzLmNvbmZpZ3VyZUFPUGFzcyh0aGlzLmNvbmZpZ3VyYXRpb24uZGVwdGhCdWZmZXJUeXBlLCB0aGlzLmNhbWVyYS5pc09ydGhvZ3JhcGhpY0NhbWVyYSk7XG4gICAgICAgICAgICAgICAgaWYgKHByb3BOYW1lID09PSBcImRlbm9pc2VTYW1wbGVzXCIgJiYgb2xkUHJvcCAhPT0gdmFsdWUpIHRoaXMuY29uZmlndXJlRGVub2lzZVBhc3ModGhpcy5jb25maWd1cmF0aW9uLmRlcHRoQnVmZmVyVHlwZSwgdGhpcy5jYW1lcmEuaXNPcnRob2dyYXBoaWNDYW1lcmEpO1xuICAgICAgICAgICAgICAgIGlmIChwcm9wTmFtZSA9PT0gXCJoYWxmUmVzXCIgJiYgb2xkUHJvcCAhPT0gdmFsdWUpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jb25maWd1cmVBT1Bhc3ModGhpcy5jb25maWd1cmF0aW9uLmRlcHRoQnVmZmVyVHlwZSwgdGhpcy5jYW1lcmEuaXNPcnRob2dyYXBoaWNDYW1lcmEpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ3VyZUhhbGZSZXNUYXJnZXRzKCk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY29uZmlndXJlRWZmZWN0Q29tcG9zaXRlcih0aGlzLmNvbmZpZ3VyYXRpb24uZGVwdGhCdWZmZXJUeXBlLCB0aGlzLmNhbWVyYS5pc09ydGhvZ3JhcGhpY0NhbWVyYSk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0U2l6ZSh0aGlzLndpZHRoLCB0aGlzLmhlaWdodCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChwcm9wTmFtZSA9PT0gXCJkZXB0aEF3YXJlVXBzYW1wbGluZ1wiICYmIG9sZFByb3AgIT09IHZhbHVlKSB0aGlzLmNvbmZpZ3VyZUVmZmVjdENvbXBvc2l0ZXIodGhpcy5jb25maWd1cmF0aW9uLmRlcHRoQnVmZmVyVHlwZSwgdGhpcy5jYW1lcmEuaXNPcnRob2dyYXBoaWNDYW1lcmEpO1xuICAgICAgICAgICAgICAgIGlmIChwcm9wTmFtZSA9PT0gXCJ0cmFuc3BhcmVuY3lBd2FyZVwiICYmIG9sZFByb3AgIT09IHZhbHVlKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuYXV0b0RldGVjdFRyYW5zcGFyZW5jeSA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmNvbmZpZ3VyZVRyYW5zcGFyZW5jeVRhcmdldCgpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAocHJvcE5hbWUgPT09IFwic3RlbmNpbFwiICYmIG9sZFByb3AgIT09IHZhbHVlKSB7XG4gICAgICAgICAgICAgICAgICAgIC8qICB0aGlzLmJlYXV0eVJlbmRlclRhcmdldC5zdGVuY2lsQnVmZmVyID0gdmFsdWU7XG4gICAgICAgICAgICAgICAgICAgICAgdGhpcy5iZWF1dHlSZW5kZXJUYXJnZXQuZGVwdGhUZXh0dXJlLmZvcm1hdCA9IHZhbHVlID8gVEhSRUUuRGVwdGhTdGVuY2lsRm9ybWF0IDogVEhSRUUuRGVwdGhGb3JtYXQ7XG4gICAgICAgICAgICAgICAgICAgICAgdGhpcy5iZWF1dHlSZW5kZXJUYXJnZXQuZGVwdGhUZXh0dXJlLnR5cGUgPSB2YWx1ZSA/IFRIUkVFLlVuc2lnbmVkSW50MjQ4VHlwZSA6IFRIUkVFLlVuc2lnbmVkSW50VHlwZTtcbiAgICAgICAgICAgICAgICAgICAgICB0aGlzLmJlYXV0eVJlbmRlclRhcmdldC5kZXB0aFRleHR1cmUubmVlZHNVcGRhdGUgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICAgIHRoaXMuYmVhdXR5UmVuZGVyVGFyZ2V0Lm5lZWRzVXBkYXRlID0gdHJ1ZTsqLyB0aGlzLmJlYXV0eVJlbmRlclRhcmdldC5kaXNwb3NlKCk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuYmVhdXR5UmVuZGVyVGFyZ2V0ID0gbmV3ICQ1V2hlMyRXZWJHTFJlbmRlclRhcmdldCh0aGlzLndpZHRoLCB0aGlzLmhlaWdodCwge1xuICAgICAgICAgICAgICAgICAgICAgICAgbWluRmlsdGVyOiAkNVdoZTMkTGluZWFyRmlsdGVyLFxuICAgICAgICAgICAgICAgICAgICAgICAgbWFnRmlsdGVyOiAkNVdoZTMkTmVhcmVzdEZpbHRlcixcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICQ1V2hlMyRIYWxmRmxvYXRUeXBlLFxuICAgICAgICAgICAgICAgICAgICAgICAgZm9ybWF0OiAkNVdoZTMkUkdCQUZvcm1hdCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0ZW5jaWxCdWZmZXI6IHZhbHVlXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmJlYXV0eVJlbmRlclRhcmdldC5kZXB0aFRleHR1cmUgPSBuZXcgJDVXaGUzJERlcHRoVGV4dHVyZSh0aGlzLndpZHRoLCB0aGlzLmhlaWdodCwgdmFsdWUgPyAkNVdoZTMkVW5zaWduZWRJbnQyNDhUeXBlIDogJDVXaGUzJFVuc2lnbmVkSW50VHlwZSk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuYmVhdXR5UmVuZGVyVGFyZ2V0LmRlcHRoVGV4dHVyZS5mb3JtYXQgPSB2YWx1ZSA/ICQ1V2hlMyREZXB0aFN0ZW5jaWxGb3JtYXQgOiAkNVdoZTMkRGVwdGhGb3JtYXQ7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgICAgLyoqIEB0eXBlIHtUSFJFRS5WZWN0b3IzW119ICovIHRoaXMuc2FtcGxlcyA9IFtdO1xuICAgICAgICAvKiogQHR5cGUge1RIUkVFLlZlY3RvcjJbXX0gKi8gdGhpcy5zYW1wbGVzRGVub2lzZSA9IFtdO1xuICAgICAgICB0aGlzLmF1dG9EZXRlY3RUcmFuc3BhcmVuY3kgPSB0cnVlO1xuICAgICAgICB0aGlzLmZyYW1lID0gMDtcbiAgICAgICAgdGhpcy5sYXN0Vmlld01hdHJpeCA9IG5ldyAkNVdoZTMkTWF0cml4NCgpO1xuICAgICAgICB0aGlzLmxhc3RQcm9qZWN0aW9uTWF0cml4ID0gbmV3ICQ1V2hlMyRNYXRyaXg0KCk7XG4gICAgICAgIHRoaXMuYmVhdXR5UmVuZGVyVGFyZ2V0ID0gbmV3ICQ1V2hlMyRXZWJHTFJlbmRlclRhcmdldCh0aGlzLndpZHRoLCB0aGlzLmhlaWdodCwge1xuICAgICAgICAgICAgbWluRmlsdGVyOiAkNVdoZTMkTGluZWFyRmlsdGVyLFxuICAgICAgICAgICAgbWFnRmlsdGVyOiAkNVdoZTMkTmVhcmVzdEZpbHRlcixcbiAgICAgICAgICAgIHR5cGU6ICQ1V2hlMyRIYWxmRmxvYXRUeXBlLFxuICAgICAgICAgICAgZm9ybWF0OiAkNVdoZTMkUkdCQUZvcm1hdCxcbiAgICAgICAgICAgIHN0ZW5jaWxCdWZmZXI6IGZhbHNlXG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLmJlYXV0eVJlbmRlclRhcmdldC5kZXB0aFRleHR1cmUgPSBuZXcgJDVXaGUzJERlcHRoVGV4dHVyZSh0aGlzLndpZHRoLCB0aGlzLmhlaWdodCwgJDVXaGUzJFVuc2lnbmVkSW50VHlwZSk7XG4gICAgICAgIHRoaXMuYmVhdXR5UmVuZGVyVGFyZ2V0LmRlcHRoVGV4dHVyZS5mb3JtYXQgPSAkNVdoZTMkRGVwdGhGb3JtYXQ7XG4gICAgICAgIHRoaXMuY29uZmlndXJlRWZmZWN0Q29tcG9zaXRlcih0aGlzLmNvbmZpZ3VyYXRpb24uZGVwdGhCdWZmZXJUeXBlLCB0aGlzLmNhbWVyYS5pc09ydGhvZ3JhcGhpY0NhbWVyYSk7XG4gICAgICAgIHRoaXMuY29uZmlndXJlU2FtcGxlRGVwZW5kZW50UGFzc2VzKCk7XG4gICAgICAgIHRoaXMuY29uZmlndXJlSGFsZlJlc1RhcmdldHMoKTtcbiAgICAgICAgdGhpcy5kZXRlY3RUcmFuc3BhcmVuY3koKTtcbiAgICAgICAgdGhpcy5jb25maWd1cmVUcmFuc3BhcmVuY3lUYXJnZXQoKTtcbiAgICAgICAgdGhpcy53cml0ZVRhcmdldEludGVybmFsID0gbmV3ICQ1V2hlMyRXZWJHTFJlbmRlclRhcmdldCh0aGlzLndpZHRoLCB0aGlzLmhlaWdodCwge1xuICAgICAgICAgICAgbWluRmlsdGVyOiAkNVdoZTMkTGluZWFyRmlsdGVyLFxuICAgICAgICAgICAgbWFnRmlsdGVyOiAkNVdoZTMkTGluZWFyRmlsdGVyLFxuICAgICAgICAgICAgZGVwdGhCdWZmZXI6IGZhbHNlLFxuICAgICAgICAgICAgZm9ybWF0OiAkNVdoZTMkUkdCQUZvcm1hdFxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5yZWFkVGFyZ2V0SW50ZXJuYWwgPSBuZXcgJDVXaGUzJFdlYkdMUmVuZGVyVGFyZ2V0KHRoaXMud2lkdGgsIHRoaXMuaGVpZ2h0LCB7XG4gICAgICAgICAgICBtaW5GaWx0ZXI6ICQ1V2hlMyRMaW5lYXJGaWx0ZXIsXG4gICAgICAgICAgICBtYWdGaWx0ZXI6ICQ1V2hlMyRMaW5lYXJGaWx0ZXIsXG4gICAgICAgICAgICBkZXB0aEJ1ZmZlcjogZmFsc2UsXG4gICAgICAgICAgICBmb3JtYXQ6ICQ1V2hlMyRSR0JBRm9ybWF0XG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLmFjY3VtdWxhdGlvblJlbmRlclRhcmdldCA9IG5ldyAkNVdoZTMkV2ViR0xSZW5kZXJUYXJnZXQodGhpcy53aWR0aCwgdGhpcy5oZWlnaHQsIHtcbiAgICAgICAgICAgIG1pbkZpbHRlcjogJDVXaGUzJExpbmVhckZpbHRlcixcbiAgICAgICAgICAgIG1hZ0ZpbHRlcjogJDVXaGUzJExpbmVhckZpbHRlcixcbiAgICAgICAgICAgIGRlcHRoQnVmZmVyOiBmYWxzZSxcbiAgICAgICAgICAgIGZvcm1hdDogJDVXaGUzJFJHQkFGb3JtYXQsXG4gICAgICAgICAgICB0eXBlOiAkNVdoZTMkSGFsZkZsb2F0VHlwZSxcbiAgICAgICAgICAgIHN0ZW5jaWxCdWZmZXI6IGZhbHNlLFxuICAgICAgICAgICAgZGVwdGhCdWZmZXI6IGZhbHNlLFxuICAgICAgICAgICAgYWxwaGE6IHRydWVcbiAgICAgICAgfSk7XG4gICAgICAgIC8qKiBAdHlwZSB7VEhSRUUuRGF0YVRleHR1cmV9ICovIHRoaXMuYmx1ZW5vaXNlID0gbmV3ICQ1V2hlMyREYXRhVGV4dHVyZSgoMCwgJDA2MjY5YWQ3OGYzYzVmZGYkZXhwb3J0JDJlMmJjZDg3MzlhZTAzOSksIDEyOCwgMTI4KTtcbiAgICAgICAgdGhpcy5hY2N1bXVsYXRpb25RdWFkID0gbmV3ICgwLCAkZTRjYThkY2IwMjE4Zjg0NiRleHBvcnQkZGNkNjcwZDczZGI3NTFmNSkobmV3ICQ1V2hlMyRTaGFkZXJNYXRlcmlhbCh7XG4gICAgICAgICAgICB1bmlmb3Jtczoge1xuICAgICAgICAgICAgICAgIGZyYW1lOiB7XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiAwXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICB0RGlmZnVzZToge1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZTogbnVsbFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB0cmFuc3BhcmVudDogdHJ1ZSxcbiAgICAgICAgICAgIG9wYWNpdHk6IDEsXG4gICAgICAgICAgICB2ZXJ0ZXhTaGFkZXI6IGBcbiAgICAgICAgICAgICB2YXJ5aW5nIHZlYzIgdlV2O1xuICAgICAgICAgICAgIHZvaWQgbWFpbigpIHtcbiAgICAgICAgICAgICAgICAgdlV2ID0gdXY7XG4gICAgICAgICAgICAgICAgIGdsX1Bvc2l0aW9uID0gdmVjNChwb3NpdGlvbiwgMSk7XG4gICAgICAgICAgICAgfWAsXG4gICAgICAgICAgICBmcmFnbWVudFNoYWRlcjogYFxuICAgICAgICAgICAgIHVuaWZvcm0gc2FtcGxlcjJEIHREaWZmdXNlO1xuICAgICAgICAgICAgIHVuaWZvcm0gZmxvYXQgZnJhbWU7XG4gICAgICAgICAgICAgICAgdmFyeWluZyB2ZWMyIHZVdjtcbiAgICAgICAgICAgICAgICB2b2lkIG1haW4oKSB7XG4gICAgICAgICAgICAgICAgICAgIHZlYzQgY29sb3IgPSB0ZXh0dXJlMkQodERpZmZ1c2UsIHZVdik7XG4gICAgICAgICAgICAgICAgICAgIGdsX0ZyYWdDb2xvciA9IHZlYzQoY29sb3IucmdiLCAxLjAgLyAoZnJhbWUgKyAxLjApKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgYFxuICAgICAgICB9KSk7XG4gICAgICAgIHRoaXMuYmx1ZW5vaXNlLmNvbG9yU3BhY2UgPSAkNVdoZTMkTm9Db2xvclNwYWNlO1xuICAgICAgICB0aGlzLmJsdWVub2lzZS53cmFwUyA9ICQ1V2hlMyRSZXBlYXRXcmFwcGluZztcbiAgICAgICAgdGhpcy5ibHVlbm9pc2Uud3JhcFQgPSAkNVdoZTMkUmVwZWF0V3JhcHBpbmc7XG4gICAgICAgIHRoaXMuYmx1ZW5vaXNlLm1pbkZpbHRlciA9ICQ1V2hlMyROZWFyZXN0RmlsdGVyO1xuICAgICAgICB0aGlzLmJsdWVub2lzZS5tYWdGaWx0ZXIgPSAkNVdoZTMkTmVhcmVzdEZpbHRlcjtcbiAgICAgICAgdGhpcy5ibHVlbm9pc2UubmVlZHNVcGRhdGUgPSB0cnVlO1xuICAgICAgICB0aGlzLmxhc3RUaW1lID0gMDtcbiAgICAgICAgdGhpcy50aW1lUm9sbGluZ0F2ZXJhZ2UgPSAwLjk5O1xuICAgICAgICB0aGlzLl9yID0gbmV3ICQ1V2hlMyRWZWN0b3IyKCk7XG4gICAgICAgIHRoaXMuX2MgPSBuZXcgJDVXaGUzJENvbG9yKCk7XG4gICAgfVxuICAgIGNvbmZpZ3VyZUhhbGZSZXNUYXJnZXRzKCkge1xuICAgICAgICB0aGlzLmZpcnN0RnJhbWUoKTtcbiAgICAgICAgaWYgKHRoaXMuY29uZmlndXJhdGlvbi5oYWxmUmVzKSB7XG4gICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldCA9IG5ldyAoMCwgJGZmOTQzN2Q5Yzc1NzdmMTEkZXhwb3J0JDE1NmY2YTU4ZjU2OWFhMDkpKHRoaXMud2lkdGggLyAyLCB0aGlzLmhlaWdodCAvIDIsIDIpO1xuICAgICAgICAgICAgaWYgKCQ1V2hlMyRSRVZJU0lPTiA8PSAxNjEpIHRoaXMuZGVwdGhEb3duc2FtcGxlVGFyZ2V0LnRleHR1cmVzID0gdGhpcy5kZXB0aERvd25zYW1wbGVUYXJnZXQudGV4dHVyZTtcbiAgICAgICAgICAgIHRoaXMuZGVwdGhEb3duc2FtcGxlVGFyZ2V0LnRleHR1cmVzWzBdLmZvcm1hdCA9ICQ1V2hlMyRSZWRGb3JtYXQ7XG4gICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldC50ZXh0dXJlc1swXS50eXBlID0gJDVXaGUzJEZsb2F0VHlwZTtcbiAgICAgICAgICAgIHRoaXMuZGVwdGhEb3duc2FtcGxlVGFyZ2V0LnRleHR1cmVzWzBdLm1pbkZpbHRlciA9ICQ1V2hlMyROZWFyZXN0RmlsdGVyO1xuICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVUYXJnZXQudGV4dHVyZXNbMF0ubWFnRmlsdGVyID0gJDVXaGUzJE5lYXJlc3RGaWx0ZXI7XG4gICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldC50ZXh0dXJlc1swXS5kZXB0aEJ1ZmZlciA9IGZhbHNlO1xuICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVUYXJnZXQudGV4dHVyZXNbMV0uZm9ybWF0ID0gJDVXaGUzJFJHQkFGb3JtYXQ7XG4gICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldC50ZXh0dXJlc1sxXS50eXBlID0gJDVXaGUzJEhhbGZGbG9hdFR5cGU7XG4gICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldC50ZXh0dXJlc1sxXS5taW5GaWx0ZXIgPSAkNVdoZTMkTmVhcmVzdEZpbHRlcjtcbiAgICAgICAgICAgIHRoaXMuZGVwdGhEb3duc2FtcGxlVGFyZ2V0LnRleHR1cmVzWzFdLm1hZ0ZpbHRlciA9ICQ1V2hlMyROZWFyZXN0RmlsdGVyO1xuICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVUYXJnZXQudGV4dHVyZXNbMV0uZGVwdGhCdWZmZXIgPSBmYWxzZTtcbiAgICAgICAgICAgIGNvbnN0IGUgPSB7XG4gICAgICAgICAgICAgICAgLi4uKDAsICQyNmFjYTE3M2UwOTg0ZDk5JGV4cG9ydCQxZWZkZjQ5MTY4N2NkNDQyKVxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGlmIChkZXB0aEJ1ZmZlclR5cGUgPT09ICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1LlJldmVyc2UpIGUuZnJhZ21lbnRTaGFkZXIgPSBcIiNkZWZpbmUgUkVWRVJTRURFUFRIXFxuXCIgKyBlLmZyYWdtZW50U2hhZGVyO1xuICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVRdWFkID0gbmV3ICgwLCAkZTRjYThkY2IwMjE4Zjg0NiRleHBvcnQkZGNkNjcwZDczZGI3NTFmNSkobmV3ICQ1V2hlMyRTaGFkZXJNYXRlcmlhbChlKSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBpZiAodGhpcy5kZXB0aERvd25zYW1wbGVUYXJnZXQpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldC5kaXNwb3NlKCk7XG4gICAgICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVUYXJnZXQgPSBudWxsO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHRoaXMuZGVwdGhEb3duc2FtcGxlUXVhZCkge1xuICAgICAgICAgICAgICAgIHRoaXMuZGVwdGhEb3duc2FtcGxlUXVhZC5kaXNwb3NlKCk7XG4gICAgICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVRdWFkID0gbnVsbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBkZXRlY3RUcmFuc3BhcmVuY3koKSB7XG4gICAgICAgIGlmICh0aGlzLmF1dG9EZXRlY3RUcmFuc3BhcmVuY3kpIHtcbiAgICAgICAgICAgIGxldCBpc1RyYW5zcGFyZW5jeSA9IGZhbHNlO1xuICAgICAgICAgICAgdGhpcy5zY2VuZS50cmF2ZXJzZSgob2JqKT0+e1xuICAgICAgICAgICAgICAgIGlmIChvYmoubWF0ZXJpYWwgJiYgb2JqLm1hdGVyaWFsLnRyYW5zcGFyZW50KSBpc1RyYW5zcGFyZW5jeSA9IHRydWU7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRoaXMuY29uZmlndXJhdGlvbi50cmFuc3BhcmVuY3lBd2FyZSA9IGlzVHJhbnNwYXJlbmN5O1xuICAgICAgICB9XG4gICAgfVxuICAgIGNvbmZpZ3VyZVRyYW5zcGFyZW5jeVRhcmdldCgpIHtcbiAgICAgICAgaWYgKHRoaXMuY29uZmlndXJhdGlvbi50cmFuc3BhcmVuY3lBd2FyZSkge1xuICAgICAgICAgICAgdGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV0ZhbHNlID0gbmV3ICQ1V2hlMyRXZWJHTFJlbmRlclRhcmdldCh0aGlzLndpZHRoLCB0aGlzLmhlaWdodCwge1xuICAgICAgICAgICAgICAgIG1pbkZpbHRlcjogJDVXaGUzJExpbmVhckZpbHRlcixcbiAgICAgICAgICAgICAgICBtYWdGaWx0ZXI6ICQ1V2hlMyROZWFyZXN0RmlsdGVyLFxuICAgICAgICAgICAgICAgIHR5cGU6ICQ1V2hlMyRIYWxmRmxvYXRUeXBlLFxuICAgICAgICAgICAgICAgIGZvcm1hdDogJDVXaGUzJFJHQkFGb3JtYXRcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgdGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV1RydWUgPSBuZXcgJDVXaGUzJFdlYkdMUmVuZGVyVGFyZ2V0KHRoaXMud2lkdGgsIHRoaXMuaGVpZ2h0LCB7XG4gICAgICAgICAgICAgICAgbWluRmlsdGVyOiAkNVdoZTMkTGluZWFyRmlsdGVyLFxuICAgICAgICAgICAgICAgIG1hZ0ZpbHRlcjogJDVXaGUzJE5lYXJlc3RGaWx0ZXIsXG4gICAgICAgICAgICAgICAgdHlwZTogJDVXaGUzJEhhbGZGbG9hdFR5cGUsXG4gICAgICAgICAgICAgICAgZm9ybWF0OiAkNVdoZTMkUkdCQUZvcm1hdFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB0aGlzLnRyYW5zcGFyZW5jeVJlbmRlclRhcmdldERXVHJ1ZS5kZXB0aFRleHR1cmUgPSBuZXcgJDVXaGUzJERlcHRoVGV4dHVyZSh0aGlzLndpZHRoLCB0aGlzLmhlaWdodCwgJDVXaGUzJFVuc2lnbmVkSW50VHlwZSk7XG4gICAgICAgICAgICB0aGlzLmRlcHRoQ29weVBhc3MgPSBuZXcgKDAsICRlNGNhOGRjYjAyMThmODQ2JGV4cG9ydCRkY2Q2NzBkNzNkYjc1MWY1KShuZXcgJDVXaGUzJFNoYWRlck1hdGVyaWFsKHtcbiAgICAgICAgICAgICAgICB1bmlmb3Jtczoge1xuICAgICAgICAgICAgICAgICAgICBkZXB0aFRleHR1cmU6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiB0aGlzLmRlcHRoVGV4dHVyZVxuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICByZXZlcnNlRGVwdGhCdWZmZXI6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiB0aGlzLmNvbmZpZ3VyYXRpb24uZGVwdGhCdWZmZXJUeXBlID09PSAkMDVmNjk5N2U0YjY1ZGExNCRleHBvcnQkZWQ0ZWU1ZDFlNTU0NzRhNS5SZXZlcnNlXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIHZlcnRleFNoYWRlcjogLyogZ2xzbCAqLyBgXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJ5aW5nIHZlYzIgdlV2O1xuICAgICAgICAgICAgICAgICAgICAgICAgdm9pZCBtYWluKCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZVdiA9IHV2O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdsX1Bvc2l0aW9uID0gdmVjNChwb3NpdGlvbiwgMSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9YCxcbiAgICAgICAgICAgICAgICBmcmFnbWVudFNoYWRlcjogLyogZ2xzbCAqLyBgXG4gICAgICAgICAgICAgICAgICAgICAgICB1bmlmb3JtIHNhbXBsZXIyRCBkZXB0aFRleHR1cmU7XG4gICAgICAgICAgICAgICAgICAgICAgICB1bmlmb3JtIGJvb2wgcmV2ZXJzZURlcHRoQnVmZmVyO1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFyeWluZyB2ZWMyIHZVdjtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZvaWQgbWFpbigpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocmV2ZXJzZURlcHRoQnVmZmVyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICBmbG9hdCBkID0gMS4wIC0gdGV4dHVyZTJEKGRlcHRoVGV4dHVyZSwgdlV2KS5yO1xuICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgIGQgKz0gMC4wMDAwMTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgIGdsX0ZyYWdEZXB0aCA9IDEuMCAtIGQ7XG4gICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZsb2F0IGQgPSB0ZXh0dXJlMkQoZGVwdGhUZXh0dXJlLCB2VXYpLnI7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZCArPSAwLjAwMDAxO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdsX0ZyYWdEZXB0aCA9IGQ7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICBnbF9GcmFnQ29sb3IgPSB2ZWM0KDAuMCwgMC4wLCAwLjAsIDAuMCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBgXG4gICAgICAgICAgICB9KSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBpZiAodGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV0ZhbHNlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV0ZhbHNlLmRpc3Bvc2UoKTtcbiAgICAgICAgICAgICAgICB0aGlzLnRyYW5zcGFyZW5jeVJlbmRlclRhcmdldERXRmFsc2UgPSBudWxsO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHRoaXMudHJhbnNwYXJlbmN5UmVuZGVyVGFyZ2V0RFdUcnVlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV1RydWUuZGlzcG9zZSgpO1xuICAgICAgICAgICAgICAgIHRoaXMudHJhbnNwYXJlbmN5UmVuZGVyVGFyZ2V0RFdUcnVlID0gbnVsbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICh0aGlzLmRlcHRoQ29weVBhc3MpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmRlcHRoQ29weVBhc3MuZGlzcG9zZSgpO1xuICAgICAgICAgICAgICAgIHRoaXMuZGVwdGhDb3B5UGFzcyA9IG51bGw7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmVuZGVyVHJhbnNwYXJlbmN5KHJlbmRlcmVyKSB7XG4gICAgICAgIGNvbnN0IG9sZEJhY2tncm91bmQgPSB0aGlzLnNjZW5lLmJhY2tncm91bmQ7XG4gICAgICAgIGNvbnN0IG9sZENsZWFyQ29sb3IgPSByZW5kZXJlci5nZXRDbGVhckNvbG9yKG5ldyAkNVdoZTMkQ29sb3IoKSk7XG4gICAgICAgIGNvbnN0IG9sZENsZWFyQWxwaGEgPSByZW5kZXJlci5nZXRDbGVhckFscGhhKCk7XG4gICAgICAgIGNvbnN0IG9sZFZpc2liaWxpdHkgPSBuZXcgTWFwKCk7XG4gICAgICAgIGNvbnN0IG9sZEF1dG9DbGVhckRlcHRoID0gcmVuZGVyZXIuYXV0b0NsZWFyRGVwdGg7XG4gICAgICAgIHRoaXMuc2NlbmUudHJhdmVyc2UoKG9iaik9PntcbiAgICAgICAgICAgIG9sZFZpc2liaWxpdHkuc2V0KG9iaiwgb2JqLnZpc2libGUpO1xuICAgICAgICB9KTtcbiAgICAgICAgLy8gT3ZlcnJpZGUgdGhlIHN0YXRlXG4gICAgICAgIHRoaXMuc2NlbmUuYmFja2dyb3VuZCA9IG51bGw7XG4gICAgICAgIHJlbmRlcmVyLmF1dG9DbGVhckRlcHRoID0gZmFsc2U7XG4gICAgICAgIHJlbmRlcmVyLnNldENsZWFyQ29sb3IobmV3ICQ1V2hlMyRDb2xvcigwLCAwLCAwKSwgMCk7XG4gICAgICAgIHRoaXMuZGVwdGhDb3B5UGFzcy5tYXRlcmlhbC51bmlmb3Jtcy5kZXB0aFRleHR1cmUudmFsdWUgPSB0aGlzLmJlYXV0eVJlbmRlclRhcmdldC5kZXB0aFRleHR1cmU7XG4gICAgICAgIHRoaXMuZGVwdGhDb3B5UGFzcy5tYXRlcmlhbC51bmlmb3Jtcy5yZXZlcnNlRGVwdGhCdWZmZXIudmFsdWUgPSB0aGlzLmNvbmZpZ3VyYXRpb24uZGVwdGhCdWZmZXJUeXBlID09PSAkMDVmNjk5N2U0YjY1ZGExNCRleHBvcnQkZWQ0ZWU1ZDFlNTU0NzRhNS5SZXZlcnNlO1xuICAgICAgICAvLyBSZW5kZXIgb3V0IHRyYW5zcGFyZW50IG9iamVjdHMgV0lUSE9VVCBkZXB0aCB3cml0ZVxuICAgICAgICByZW5kZXJlci5zZXRSZW5kZXJUYXJnZXQodGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV0ZhbHNlKTtcbiAgICAgICAgdGhpcy5zY2VuZS50cmF2ZXJzZSgob2JqKT0+e1xuICAgICAgICAgICAgaWYgKG9iai5tYXRlcmlhbCkgb2JqLnZpc2libGUgPSBvbGRWaXNpYmlsaXR5LmdldChvYmopICYmIChvYmoubWF0ZXJpYWwudHJhbnNwYXJlbnQgJiYgIW9iai5tYXRlcmlhbC5kZXB0aFdyaXRlICYmICFvYmoudXNlckRhdGEudHJlYXRBc09wYXF1ZSB8fCAhIW9iai51c2VyRGF0YS5jYW5ub3RSZWNlaXZlQU8pO1xuICAgICAgICB9KTtcbiAgICAgICAgcmVuZGVyZXIuY2xlYXIodHJ1ZSwgdHJ1ZSwgdHJ1ZSk7XG4gICAgICAgIHRoaXMuZGVwdGhDb3B5UGFzcy5yZW5kZXIocmVuZGVyZXIpO1xuICAgICAgICByZW5kZXJlci5yZW5kZXIodGhpcy5zY2VuZSwgdGhpcy5jYW1lcmEpO1xuICAgICAgICAvLyBSZW5kZXIgb3V0IHRyYW5zcGFyZW50IG9iamVjdHMgV0lUSCBkZXB0aCB3cml0ZVxuICAgICAgICByZW5kZXJlci5zZXRSZW5kZXJUYXJnZXQodGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV1RydWUpO1xuICAgICAgICB0aGlzLnNjZW5lLnRyYXZlcnNlKChvYmopPT57XG4gICAgICAgICAgICBpZiAob2JqLm1hdGVyaWFsKSBvYmoudmlzaWJsZSA9IG9sZFZpc2liaWxpdHkuZ2V0KG9iaikgJiYgb2JqLm1hdGVyaWFsLnRyYW5zcGFyZW50ICYmIG9iai5tYXRlcmlhbC5kZXB0aFdyaXRlICYmICFvYmoudXNlckRhdGEudHJlYXRBc09wYXF1ZTtcbiAgICAgICAgfSk7XG4gICAgICAgIHJlbmRlcmVyLmNsZWFyKHRydWUsIHRydWUsIHRydWUpO1xuICAgICAgICB0aGlzLmRlcHRoQ29weVBhc3MucmVuZGVyKHJlbmRlcmVyKTtcbiAgICAgICAgcmVuZGVyZXIucmVuZGVyKHRoaXMuc2NlbmUsIHRoaXMuY2FtZXJhKTtcbiAgICAgICAgLy8gUmVzdG9yZVxuICAgICAgICB0aGlzLnNjZW5lLnRyYXZlcnNlKChvYmopPT57XG4gICAgICAgICAgICBvYmoudmlzaWJsZSA9IG9sZFZpc2liaWxpdHkuZ2V0KG9iaik7XG4gICAgICAgIH0pO1xuICAgICAgICByZW5kZXJlci5zZXRDbGVhckNvbG9yKG9sZENsZWFyQ29sb3IsIG9sZENsZWFyQWxwaGEpO1xuICAgICAgICB0aGlzLnNjZW5lLmJhY2tncm91bmQgPSBvbGRCYWNrZ3JvdW5kO1xuICAgICAgICByZW5kZXJlci5hdXRvQ2xlYXJEZXB0aCA9IG9sZEF1dG9DbGVhckRlcHRoO1xuICAgIH1cbiAgICBjb25maWd1cmVTYW1wbGVEZXBlbmRlbnRQYXNzZXMoKSB7XG4gICAgICAgIHRoaXMuZmlyc3RGcmFtZSgpO1xuICAgICAgICB0aGlzLmNvbmZpZ3VyZUFPUGFzcyh0aGlzLmNvbmZpZ3VyYXRpb24uZGVwdGhCdWZmZXJUeXBlLCB0aGlzLmNhbWVyYS5pc09ydGhvZ3JhcGhpY0NhbWVyYSk7XG4gICAgICAgIHRoaXMuY29uZmlndXJlRGVub2lzZVBhc3ModGhpcy5jb25maWd1cmF0aW9uLmRlcHRoQnVmZmVyVHlwZSwgdGhpcy5jYW1lcmEuaXNPcnRob2dyYXBoaWNDYW1lcmEpO1xuICAgIH1cbiAgICBjb25maWd1cmVBT1Bhc3MoZGVwdGhCdWZmZXJUeXBlMSA9ICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1LkRlZmF1bHQsIG9ydGhvID0gZmFsc2UpIHtcbiAgICAgICAgdGhpcy5maXJzdEZyYW1lKCk7XG4gICAgICAgIHRoaXMuc2FtcGxlcyA9IHRoaXMuZ2VuZXJhdGVIZW1pc3BoZXJlU2FtcGxlcyh0aGlzLmNvbmZpZ3VyYXRpb24uYW9TYW1wbGVzKTtcbiAgICAgICAgY29uc3QgZSA9IHtcbiAgICAgICAgICAgIC4uLigwLCAkMWVkNDU5NjhjMTE2MGMzYyRleHBvcnQkYzliMjYzYjlhMTdkZmZkNylcbiAgICAgICAgfTtcbiAgICAgICAgZS5mcmFnbWVudFNoYWRlciA9IGUuZnJhZ21lbnRTaGFkZXIucmVwbGFjZShcIjE2XCIsIHRoaXMuY29uZmlndXJhdGlvbi5hb1NhbXBsZXMpLnJlcGxhY2UoXCIxNi4wXCIsIHRoaXMuY29uZmlndXJhdGlvbi5hb1NhbXBsZXMgKyBcIi4wXCIpO1xuICAgICAgICBpZiAoZGVwdGhCdWZmZXJUeXBlMSA9PT0gJDA1ZjY5OTdlNGI2NWRhMTQkZXhwb3J0JGVkNGVlNWQxZTU1NDc0YTUuTG9nKSBlLmZyYWdtZW50U2hhZGVyID0gXCIjZGVmaW5lIExPR0RFUFRIXFxuXCIgKyBlLmZyYWdtZW50U2hhZGVyO1xuICAgICAgICBlbHNlIGlmIChkZXB0aEJ1ZmZlclR5cGUxID09PSAkMDVmNjk5N2U0YjY1ZGExNCRleHBvcnQkZWQ0ZWU1ZDFlNTU0NzRhNS5SZXZlcnNlKSBlLmZyYWdtZW50U2hhZGVyID0gXCIjZGVmaW5lIFJFVkVSU0VERVBUSFxcblwiICsgZS5mcmFnbWVudFNoYWRlcjtcbiAgICAgICAgaWYgKG9ydGhvKSBlLmZyYWdtZW50U2hhZGVyID0gXCIjZGVmaW5lIE9SVEhPXFxuXCIgKyBlLmZyYWdtZW50U2hhZGVyO1xuICAgICAgICBpZiAodGhpcy5jb25maWd1cmF0aW9uLmhhbGZSZXMpIGUuZnJhZ21lbnRTaGFkZXIgPSBcIiNkZWZpbmUgSEFMRlJFU1xcblwiICsgZS5mcmFnbWVudFNoYWRlcjtcbiAgICAgICAgaWYgKHRoaXMuZWZmZWN0U2hhZGVyUXVhZCkge1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RTaGFkZXJRdWFkLm1hdGVyaWFsLmRpc3Bvc2UoKTtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0U2hhZGVyUXVhZC5tYXRlcmlhbCA9IG5ldyAkNVdoZTMkU2hhZGVyTWF0ZXJpYWwoZSk7XG4gICAgICAgIH0gZWxzZSB0aGlzLmVmZmVjdFNoYWRlclF1YWQgPSBuZXcgKDAsICRlNGNhOGRjYjAyMThmODQ2JGV4cG9ydCRkY2Q2NzBkNzNkYjc1MWY1KShuZXcgJDVXaGUzJFNoYWRlck1hdGVyaWFsKGUpKTtcbiAgICB9XG4gICAgY29uZmlndXJlRGVub2lzZVBhc3MoZGVwdGhCdWZmZXJUeXBlMSA9ICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1LkRlZmF1bHQsIG9ydGhvID0gZmFsc2UpIHtcbiAgICAgICAgdGhpcy5maXJzdEZyYW1lKCk7XG4gICAgICAgIHRoaXMuc2FtcGxlc0Rlbm9pc2UgPSB0aGlzLmdlbmVyYXRlRGVub2lzZVNhbXBsZXModGhpcy5jb25maWd1cmF0aW9uLmRlbm9pc2VTYW1wbGVzLCAxMSk7XG4gICAgICAgIGNvbnN0IHAgPSB7XG4gICAgICAgICAgICAuLi4oMCwgJGU1MjM3OGNkMGY1YTk3M2QkZXhwb3J0JDU3ODU2YjU5ZjMxNzI2MmUpXG4gICAgICAgIH07XG4gICAgICAgIHAuZnJhZ21lbnRTaGFkZXIgPSBwLmZyYWdtZW50U2hhZGVyLnJlcGxhY2UoXCIxNlwiLCB0aGlzLmNvbmZpZ3VyYXRpb24uZGVub2lzZVNhbXBsZXMpO1xuICAgICAgICBpZiAoZGVwdGhCdWZmZXJUeXBlMSA9PT0gJDA1ZjY5OTdlNGI2NWRhMTQkZXhwb3J0JGVkNGVlNWQxZTU1NDc0YTUuTG9nKSBwLmZyYWdtZW50U2hhZGVyID0gXCIjZGVmaW5lIExPR0RFUFRIXFxuXCIgKyBwLmZyYWdtZW50U2hhZGVyO1xuICAgICAgICBlbHNlIGlmIChkZXB0aEJ1ZmZlclR5cGUxID09PSAkMDVmNjk5N2U0YjY1ZGExNCRleHBvcnQkZWQ0ZWU1ZDFlNTU0NzRhNS5SZXZlcnNlKSBwLmZyYWdtZW50U2hhZGVyID0gXCIjZGVmaW5lIFJFVkVSU0VERVBUSFxcblwiICsgcC5mcmFnbWVudFNoYWRlcjtcbiAgICAgICAgaWYgKG9ydGhvKSBwLmZyYWdtZW50U2hhZGVyID0gXCIjZGVmaW5lIE9SVEhPXFxuXCIgKyBwLmZyYWdtZW50U2hhZGVyO1xuICAgICAgICBpZiAodGhpcy5wb2lzc29uQmx1clF1YWQpIHtcbiAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLmRpc3Bvc2UoKTtcbiAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsID0gbmV3ICQ1V2hlMyRTaGFkZXJNYXRlcmlhbChwKTtcbiAgICAgICAgfSBlbHNlIHRoaXMucG9pc3NvbkJsdXJRdWFkID0gbmV3ICgwLCAkZTRjYThkY2IwMjE4Zjg0NiRleHBvcnQkZGNkNjcwZDczZGI3NTFmNSkobmV3ICQ1V2hlMyRTaGFkZXJNYXRlcmlhbChwKSk7XG4gICAgfVxuICAgIGNvbmZpZ3VyZUVmZmVjdENvbXBvc2l0ZXIoZGVwdGhCdWZmZXJUeXBlMSA9ICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1LkRlZmF1bHQsIG9ydGhvID0gZmFsc2UpIHtcbiAgICAgICAgdGhpcy5maXJzdEZyYW1lKCk7XG4gICAgICAgIGNvbnN0IGUgPSB7XG4gICAgICAgICAgICAuLi4oMCwgJDEyYjIxZDI0ZDExOTJhMDQkZXhwb3J0JGE4MTVhY2NjYmQyYzlhNDkpXG4gICAgICAgIH07XG4gICAgICAgIGlmIChkZXB0aEJ1ZmZlclR5cGUxID09PSAkMDVmNjk5N2U0YjY1ZGExNCRleHBvcnQkZWQ0ZWU1ZDFlNTU0NzRhNS5Mb2cpIGUuZnJhZ21lbnRTaGFkZXIgPSBcIiNkZWZpbmUgTE9HREVQVEhcXG5cIiArIGUuZnJhZ21lbnRTaGFkZXI7XG4gICAgICAgIGVsc2UgaWYgKGRlcHRoQnVmZmVyVHlwZTEgPT09ICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1LlJldmVyc2UpIGUuZnJhZ21lbnRTaGFkZXIgPSBcIiNkZWZpbmUgUkVWRVJTRURFUFRIXFxuXCIgKyBlLmZyYWdtZW50U2hhZGVyO1xuICAgICAgICBpZiAob3J0aG8pIGUuZnJhZ21lbnRTaGFkZXIgPSBcIiNkZWZpbmUgT1JUSE9cXG5cIiArIGUuZnJhZ21lbnRTaGFkZXI7XG4gICAgICAgIGlmICh0aGlzLmNvbmZpZ3VyYXRpb24uaGFsZlJlcyAmJiB0aGlzLmNvbmZpZ3VyYXRpb24uZGVwdGhBd2FyZVVwc2FtcGxpbmcpIGUuZnJhZ21lbnRTaGFkZXIgPSBcIiNkZWZpbmUgSEFMRlJFU1xcblwiICsgZS5mcmFnbWVudFNoYWRlcjtcbiAgICAgICAgaWYgKHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQpIHtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwuZGlzcG9zZSgpO1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbCA9IG5ldyAkNVdoZTMkU2hhZGVyTWF0ZXJpYWwoZSk7XG4gICAgICAgIH0gZWxzZSB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkID0gbmV3ICgwLCAkZTRjYThkY2IwMjE4Zjg0NiRleHBvcnQkZGNkNjcwZDczZGI3NTFmNSkobmV3ICQ1V2hlMyRTaGFkZXJNYXRlcmlhbChlKSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAgICAgKiBcbiAgICAgICAgICogQHBhcmFtIHtOdW1iZXJ9IG4gXG4gICAgICAgICAqIEByZXR1cm5zIHtUSFJFRS5WZWN0b3IzW119XG4gICAgICAgICAqLyBnZW5lcmF0ZUhlbWlzcGhlcmVTYW1wbGVzKG4pIHtcbiAgICAgICAgY29uc3QgcG9pbnRzID0gW107XG4gICAgICAgIGZvcihsZXQgayA9IDA7IGsgPCBuOyBrKyspe1xuICAgICAgICAgICAgY29uc3QgdGhldGEgPSAyLjM5OTk2MyAqIGs7XG4gICAgICAgICAgICBsZXQgciA9IE1hdGguc3FydChrICsgMC41KSAvIE1hdGguc3FydChuKTtcbiAgICAgICAgICAgIGNvbnN0IHggPSByICogTWF0aC5jb3ModGhldGEpO1xuICAgICAgICAgICAgY29uc3QgeSA9IHIgKiBNYXRoLnNpbih0aGV0YSk7XG4gICAgICAgICAgICAvLyBQcm9qZWN0IHRvIGhlbWlzcGhlcmVcbiAgICAgICAgICAgIGNvbnN0IHogPSBNYXRoLnNxcnQoMSAtICh4ICogeCArIHkgKiB5KSk7XG4gICAgICAgICAgICBwb2ludHMucHVzaChuZXcgJDVXaGUzJFZlY3RvcjMoeCwgeSwgeikpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBwb2ludHM7XG4gICAgfVxuICAgIC8qKlxuICAgICAgICAgKiBcbiAgICAgICAgICogQHBhcmFtIHtudW1iZXJ9IG51bVNhbXBsZXMgXG4gICAgICAgICAqIEBwYXJhbSB7bnVtYmVyfSBudW1SaW5ncyBcbiAgICAgICAgICogQHJldHVybnMge1RIUkVFLlZlY3RvcjJbXX1cbiAgICAgICAgICovIGdlbmVyYXRlRGVub2lzZVNhbXBsZXMobnVtU2FtcGxlcywgbnVtUmluZ3MpIHtcbiAgICAgICAgY29uc3QgYW5nbGVTdGVwID0gMiAqIE1hdGguUEkgKiBudW1SaW5ncyAvIG51bVNhbXBsZXM7XG4gICAgICAgIGNvbnN0IGludk51bVNhbXBsZXMgPSAxLjAgLyBudW1TYW1wbGVzO1xuICAgICAgICBjb25zdCByYWRpdXNTdGVwID0gaW52TnVtU2FtcGxlcztcbiAgICAgICAgY29uc3Qgc2FtcGxlcyA9IFtdO1xuICAgICAgICBsZXQgcmFkaXVzID0gaW52TnVtU2FtcGxlcztcbiAgICAgICAgbGV0IGFuZ2xlID0gMDtcbiAgICAgICAgZm9yKGxldCBpID0gMDsgaSA8IG51bVNhbXBsZXM7IGkrKyl7XG4gICAgICAgICAgICBzYW1wbGVzLnB1c2gobmV3ICQ1V2hlMyRWZWN0b3IyKE1hdGguY29zKGFuZ2xlKSwgTWF0aC5zaW4oYW5nbGUpKS5tdWx0aXBseVNjYWxhcihNYXRoLnBvdyhyYWRpdXMsIDAuNzUpKSk7XG4gICAgICAgICAgICByYWRpdXMgKz0gcmFkaXVzU3RlcDtcbiAgICAgICAgICAgIGFuZ2xlICs9IGFuZ2xlU3RlcDtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gc2FtcGxlcztcbiAgICB9XG4gICAgc2V0U2l6ZSh3aWR0aCwgaGVpZ2h0KSB7XG4gICAgICAgIHRoaXMuZmlyc3RGcmFtZSgpO1xuICAgICAgICB0aGlzLndpZHRoID0gd2lkdGg7XG4gICAgICAgIHRoaXMuaGVpZ2h0ID0gaGVpZ2h0O1xuICAgICAgICBjb25zdCBjID0gdGhpcy5jb25maWd1cmF0aW9uLmhhbGZSZXMgPyAwLjUgOiAxO1xuICAgICAgICB0aGlzLmJlYXV0eVJlbmRlclRhcmdldC5zZXRTaXplKHdpZHRoLCBoZWlnaHQpO1xuICAgICAgICB0aGlzLndyaXRlVGFyZ2V0SW50ZXJuYWwuc2V0U2l6ZSh3aWR0aCAqIGMsIGhlaWdodCAqIGMpO1xuICAgICAgICB0aGlzLnJlYWRUYXJnZXRJbnRlcm5hbC5zZXRTaXplKHdpZHRoICogYywgaGVpZ2h0ICogYyk7XG4gICAgICAgIHRoaXMuYWNjdW11bGF0aW9uUmVuZGVyVGFyZ2V0LnNldFNpemUod2lkdGggKiBjLCBoZWlnaHQgKiBjKTtcbiAgICAgICAgaWYgKHRoaXMuY29uZmlndXJhdGlvbi5oYWxmUmVzKSB0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldC5zZXRTaXplKHdpZHRoICogYywgaGVpZ2h0ICogYyk7XG4gICAgICAgIGlmICh0aGlzLmNvbmZpZ3VyYXRpb24udHJhbnNwYXJlbmN5QXdhcmUpIHtcbiAgICAgICAgICAgIHRoaXMudHJhbnNwYXJlbmN5UmVuZGVyVGFyZ2V0RFdGYWxzZS5zZXRTaXplKHdpZHRoLCBoZWlnaHQpO1xuICAgICAgICAgICAgdGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV1RydWUuc2V0U2l6ZSh3aWR0aCwgaGVpZ2h0KTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBmaXJzdEZyYW1lKCkge1xuICAgICAgICB0aGlzLm5lZWRzRnJhbWUgPSB0cnVlO1xuICAgIH1cbiAgICByZW5kZXIocmVuZGVyZXIsIHdyaXRlQnVmZmVyLCByZWFkQnVmZmVyLCBkZWx0YVRpbWUsIG1hc2tBY3RpdmUpIHtcbiAgICAgICAgaWYgKHJlbmRlcmVyLmNhcGFiaWxpdGllcy5sb2dhcml0aG1pY0RlcHRoQnVmZmVyICYmIHRoaXMuY29uZmlndXJhdGlvbi5kZXB0aEJ1ZmZlclR5cGUgIT09ICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1LkxvZyB8fCByZW5kZXJlci5jYXBhYmlsaXRpZXMucmV2ZXJzZURlcHRoQnVmZmVyICYmIHRoaXMuY29uZmlndXJhdGlvbi5kZXB0aEJ1ZmZlclR5cGUgIT09ICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1LlJldmVyc2UpIHtcbiAgICAgICAgICAgIHRoaXMuY29uZmlndXJhdGlvbi5kZXB0aEJ1ZmZlclR5cGUgPSByZW5kZXJlci5jYXBhYmlsaXRpZXMubG9nYXJpdGhtaWNEZXB0aEJ1ZmZlciA/ICQwNWY2OTk3ZTRiNjVkYTE0JGV4cG9ydCRlZDRlZTVkMWU1NTQ3NGE1LkxvZyA6IHJlbmRlcmVyLmNhcGFiaWxpdGllcy5yZXZlcnNlRGVwdGhCdWZmZXIgPyAkMDVmNjk5N2U0YjY1ZGExNCRleHBvcnQkZWQ0ZWU1ZDFlNTU0NzRhNS5SZXZlcnNlIDogJDA1ZjY5OTdlNGI2NWRhMTQkZXhwb3J0JGVkNGVlNWQxZTU1NDc0YTUuRGVmYXVsdDtcbiAgICAgICAgICAgIHRoaXMuY29uZmlndXJlQU9QYXNzKHRoaXMuY29uZmlndXJhdGlvbi5kZXB0aEJ1ZmZlclR5cGUsIHRoaXMuY2FtZXJhLmlzT3J0aG9ncmFwaGljQ2FtZXJhKTtcbiAgICAgICAgICAgIHRoaXMuY29uZmlndXJlRGVub2lzZVBhc3ModGhpcy5jb25maWd1cmF0aW9uLmRlcHRoQnVmZmVyVHlwZSwgdGhpcy5jYW1lcmEuaXNPcnRob2dyYXBoaWNDYW1lcmEpO1xuICAgICAgICAgICAgdGhpcy5jb25maWd1cmVFZmZlY3RDb21wb3NpdGVyKHRoaXMuY29uZmlndXJhdGlvbi5kZXB0aEJ1ZmZlclR5cGUsIHRoaXMuY2FtZXJhLmlzT3J0aG9ncmFwaGljQ2FtZXJhKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmRldGVjdFRyYW5zcGFyZW5jeSgpO1xuICAgICAgICB0aGlzLmNhbWVyYS51cGRhdGVNYXRyaXhXb3JsZCgpO1xuICAgICAgICBpZiAodGhpcy5sYXN0Vmlld01hdHJpeC5lcXVhbHModGhpcy5jYW1lcmEubWF0cml4V29ybGRJbnZlcnNlKSAmJiB0aGlzLmxhc3RQcm9qZWN0aW9uTWF0cml4LmVxdWFscyh0aGlzLmNhbWVyYS5wcm9qZWN0aW9uTWF0cml4KSAmJiB0aGlzLmNvbmZpZ3VyYXRpb24uYWNjdW11bGF0ZSAmJiAhdGhpcy5uZWVkc0ZyYW1lKSB0aGlzLmZyYW1lKys7XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgaWYgKHRoaXMuY29uZmlndXJhdGlvbi5hY2N1bXVsYXRlKSB7XG4gICAgICAgICAgICAgICAgcmVuZGVyZXIuc2V0UmVuZGVyVGFyZ2V0KHRoaXMuYWNjdW11bGF0aW9uUmVuZGVyVGFyZ2V0KTtcbiAgICAgICAgICAgICAgICByZW5kZXJlci5jbGVhcih0cnVlLCB0cnVlLCB0cnVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuZnJhbWUgPSAwO1xuICAgICAgICAgICAgdGhpcy5uZWVkc0ZyYW1lID0gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5sYXN0Vmlld01hdHJpeC5jb3B5KHRoaXMuY2FtZXJhLm1hdHJpeFdvcmxkSW52ZXJzZSk7XG4gICAgICAgIHRoaXMubGFzdFByb2plY3Rpb25NYXRyaXguY29weSh0aGlzLmNhbWVyYS5wcm9qZWN0aW9uTWF0cml4KTtcbiAgICAgICAgbGV0IGdsO1xuICAgICAgICBsZXQgZXh0O1xuICAgICAgICBsZXQgdGltZXJRdWVyeTtcbiAgICAgICAgaWYgKHRoaXMuZGVidWdNb2RlKSB7XG4gICAgICAgICAgICBnbCA9IHJlbmRlcmVyLmdldENvbnRleHQoKTtcbiAgICAgICAgICAgIGV4dCA9IGdsLmdldEV4dGVuc2lvbihcIkVYVF9kaXNqb2ludF90aW1lcl9xdWVyeV93ZWJnbDJcIik7XG4gICAgICAgICAgICBpZiAoZXh0ID09PSBudWxsKSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkVYVF9kaXNqb2ludF90aW1lcl9xdWVyeV93ZWJnbDIgbm90IGF2YWlsYWJsZSwgZGlzYWJsaW5nIGRlYnVnIG1vZGUuXCIpO1xuICAgICAgICAgICAgICAgIHRoaXMuZGVidWdNb2RlID0gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMuY29uZmlndXJhdGlvbi5hdXRvUmVuZGVyQmVhdXR5KSB7XG4gICAgICAgICAgICByZW5kZXJlci5zZXRSZW5kZXJUYXJnZXQodGhpcy5iZWF1dHlSZW5kZXJUYXJnZXQpO1xuICAgICAgICAgICAgcmVuZGVyZXIucmVuZGVyKHRoaXMuc2NlbmUsIHRoaXMuY2FtZXJhKTtcbiAgICAgICAgICAgIGlmICh0aGlzLmNvbmZpZ3VyYXRpb24udHJhbnNwYXJlbmN5QXdhcmUpIHRoaXMucmVuZGVyVHJhbnNwYXJlbmN5KHJlbmRlcmVyKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5kZWJ1Z01vZGUpIHtcbiAgICAgICAgICAgIHRpbWVyUXVlcnkgPSBnbC5jcmVhdGVRdWVyeSgpO1xuICAgICAgICAgICAgZ2wuYmVnaW5RdWVyeShleHQuVElNRV9FTEFQU0VEX0VYVCwgdGltZXJRdWVyeSk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgeHJFbmFibGVkID0gcmVuZGVyZXIueHIuZW5hYmxlZDtcbiAgICAgICAgcmVuZGVyZXIueHIuZW5hYmxlZCA9IGZhbHNlO1xuICAgICAgICB0aGlzLl9yLnNldCh0aGlzLndpZHRoLCB0aGlzLmhlaWdodCk7XG4gICAgICAgIGxldCB0cnVlUmFkaXVzID0gdGhpcy5jb25maWd1cmF0aW9uLmFvUmFkaXVzO1xuICAgICAgICBpZiAodGhpcy5jb25maWd1cmF0aW9uLmhhbGZSZXMgJiYgdGhpcy5jb25maWd1cmF0aW9uLnNjcmVlblNwYWNlUmFkaXVzKSB0cnVlUmFkaXVzICo9IDAuNTtcbiAgICAgICAgaWYgKHRoaXMuZnJhbWUgPCAxMDI0IC8gdGhpcy5jb25maWd1cmF0aW9uLmFvU2FtcGxlcykge1xuICAgICAgICAgICAgaWYgKHRoaXMuY29uZmlndXJhdGlvbi5oYWxmUmVzKSB7XG4gICAgICAgICAgICAgICAgcmVuZGVyZXIuc2V0UmVuZGVyVGFyZ2V0KHRoaXMuZGVwdGhEb3duc2FtcGxlVGFyZ2V0KTtcbiAgICAgICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVF1YWQubWF0ZXJpYWwudW5pZm9ybXMuc2NlbmVEZXB0aC52YWx1ZSA9IHRoaXMuYmVhdXR5UmVuZGVyVGFyZ2V0LmRlcHRoVGV4dHVyZTtcbiAgICAgICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVF1YWQubWF0ZXJpYWwudW5pZm9ybXMucmVzb2x1dGlvbi52YWx1ZSA9IHRoaXMuX3I7XG4gICAgICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wibmVhclwiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLm5lYXI7XG4gICAgICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiZmFyXCJdLnZhbHVlID0gdGhpcy5jYW1lcmEuZmFyO1xuICAgICAgICAgICAgICAgIHRoaXMuZGVwdGhEb3duc2FtcGxlUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInByb2plY3Rpb25NYXRyaXhJbnZcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5wcm9qZWN0aW9uTWF0cml4SW52ZXJzZTtcbiAgICAgICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJ2aWV3TWF0cml4SW52XCJdLnZhbHVlID0gdGhpcy5jYW1lcmEubWF0cml4V29ybGQ7XG4gICAgICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wibG9nRGVwdGhcIl0udmFsdWUgPSB0aGlzLmNvbmZpZ3VyYXRpb24uZGVwdGhCdWZmZXJUeXBlID09PSAkMDVmNjk5N2U0YjY1ZGExNCRleHBvcnQkZWQ0ZWU1ZDFlNTU0NzRhNS5Mb2c7XG4gICAgICAgICAgICAgICAgdGhpcy5kZXB0aERvd25zYW1wbGVRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wib3J0aG9cIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5pc09ydGhvZ3JhcGhpY0NhbWVyYTtcbiAgICAgICAgICAgICAgICB0aGlzLmRlcHRoRG93bnNhbXBsZVF1YWQucmVuZGVyKHJlbmRlcmVyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuZWZmZWN0U2hhZGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInNjZW5lRGlmZnVzZVwiXS52YWx1ZSA9IHRoaXMuYmVhdXR5UmVuZGVyVGFyZ2V0LnRleHR1cmU7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJzY2VuZURlcHRoXCJdLnZhbHVlID0gdGhpcy5jb25maWd1cmF0aW9uLmhhbGZSZXMgPyB0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldC50ZXh0dXJlc1swXSA6IHRoaXMuYmVhdXR5UmVuZGVyVGFyZ2V0LmRlcHRoVGV4dHVyZTtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0U2hhZGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInNjZW5lTm9ybWFsXCJdLnZhbHVlID0gdGhpcy5jb25maWd1cmF0aW9uLmhhbGZSZXMgPyB0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldC50ZXh0dXJlc1sxXSA6IG51bGw7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJwcm9qTWF0XCJdLnZhbHVlID0gdGhpcy5jYW1lcmEucHJvamVjdGlvbk1hdHJpeDtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0U2hhZGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInZpZXdNYXRcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5tYXRyaXhXb3JsZEludmVyc2U7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJwcm9qVmlld01hdFwiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLnByb2plY3Rpb25NYXRyaXguY2xvbmUoKS5tdWx0aXBseSh0aGlzLmNhbWVyYS5tYXRyaXhXb3JsZEludmVyc2UuY2xvbmUoKSk7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJwcm9qZWN0aW9uTWF0cml4SW52XCJdLnZhbHVlID0gdGhpcy5jYW1lcmEucHJvamVjdGlvbk1hdHJpeEludmVyc2U7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJ2aWV3TWF0cml4SW52XCJdLnZhbHVlID0gdGhpcy5jYW1lcmEubWF0cml4V29ybGQ7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJjYW1lcmFQb3NcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5nZXRXb3JsZFBvc2l0aW9uKG5ldyAkNVdoZTMkVmVjdG9yMygpKTtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0U2hhZGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImJpYXNBZGp1c3RtZW50XCJdLnZhbHVlID0gbmV3ICQ1V2hlMyRWZWN0b3IyKHRoaXMuY29uZmlndXJhdGlvbi5iaWFzT2Zmc2V0LCB0aGlzLmNvbmZpZ3VyYXRpb24uYmlhc011bHRpcGxpZXIpO1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RTaGFkZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wicmVzb2x1dGlvblwiXS52YWx1ZSA9IHRoaXMuY29uZmlndXJhdGlvbi5oYWxmUmVzID8gdGhpcy5fci5jbG9uZSgpLm11bHRpcGx5U2NhbGFyKDAuNSkuZmxvb3IoKSA6IHRoaXMuX3I7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJ0aW1lXCJdLnZhbHVlID0gcGVyZm9ybWFuY2Uubm93KCkgLyAxMDAwO1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RTaGFkZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wic2FtcGxlc1wiXS52YWx1ZSA9IHRoaXMuc2FtcGxlcztcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0U2hhZGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImJsdWVub2lzZVwiXS52YWx1ZSA9IHRoaXMuYmx1ZW5vaXNlO1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RTaGFkZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wicmFkaXVzXCJdLnZhbHVlID0gdHJ1ZVJhZGl1cztcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0U2hhZGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImRpc3RhbmNlRmFsbG9mZlwiXS52YWx1ZSA9IHRoaXMuY29uZmlndXJhdGlvbi5kaXN0YW5jZUZhbGxvZmY7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJuZWFyXCJdLnZhbHVlID0gdGhpcy5jYW1lcmEubmVhcjtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0U2hhZGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImZhclwiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLmZhcjtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0U2hhZGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcIm9ydGhvXCJdLnZhbHVlID0gdGhpcy5jYW1lcmEuaXNPcnRob2dyYXBoaWNDYW1lcmE7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJzY3JlZW5TcGFjZVJhZGl1c1wiXS52YWx1ZSA9IHRoaXMuY29uZmlndXJhdGlvbi5zY3JlZW5TcGFjZVJhZGl1cztcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0U2hhZGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImZyYW1lXCJdLnZhbHVlID0gdGhpcy5mcmFtZTtcbiAgICAgICAgICAgIC8vIFN0YXJ0IHRoZSBBT1xuICAgICAgICAgICAgcmVuZGVyZXIuc2V0UmVuZGVyVGFyZ2V0KHRoaXMud3JpdGVUYXJnZXRJbnRlcm5hbCk7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdFNoYWRlclF1YWQucmVuZGVyKHJlbmRlcmVyKTtcbiAgICAgICAgICAgIC8vIEVuZCB0aGUgQU9cbiAgICAgICAgICAgIC8vIFN0YXJ0IHRoZSBibHVyXG4gICAgICAgICAgICBmb3IobGV0IGkgPSAwOyBpIDwgdGhpcy5jb25maWd1cmF0aW9uLmRlbm9pc2VJdGVyYXRpb25zOyBpKyspe1xuICAgICAgICAgICAgICAgIFt0aGlzLndyaXRlVGFyZ2V0SW50ZXJuYWwsIHRoaXMucmVhZFRhcmdldEludGVybmFsXSA9IFtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWFkVGFyZ2V0SW50ZXJuYWwsXG4gICAgICAgICAgICAgICAgICAgIHRoaXMud3JpdGVUYXJnZXRJbnRlcm5hbFxuICAgICAgICAgICAgICAgIF07XG4gICAgICAgICAgICAgICAgdGhpcy5wb2lzc29uQmx1clF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJ0RGlmZnVzZVwiXS52YWx1ZSA9IHRoaXMucmVhZFRhcmdldEludGVybmFsLnRleHR1cmU7XG4gICAgICAgICAgICAgICAgdGhpcy5wb2lzc29uQmx1clF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJzY2VuZURlcHRoXCJdLnZhbHVlID0gdGhpcy5jb25maWd1cmF0aW9uLmhhbGZSZXMgPyB0aGlzLmRlcHRoRG93bnNhbXBsZVRhcmdldC50ZXh0dXJlc1swXSA6IHRoaXMuYmVhdXR5UmVuZGVyVGFyZ2V0LmRlcHRoVGV4dHVyZTtcbiAgICAgICAgICAgICAgICB0aGlzLnBvaXNzb25CbHVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInByb2pNYXRcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5wcm9qZWN0aW9uTWF0cml4O1xuICAgICAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1widmlld01hdFwiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLm1hdHJpeFdvcmxkSW52ZXJzZTtcbiAgICAgICAgICAgICAgICB0aGlzLnBvaXNzb25CbHVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInByb2plY3Rpb25NYXRyaXhJbnZcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5wcm9qZWN0aW9uTWF0cml4SW52ZXJzZTtcbiAgICAgICAgICAgICAgICB0aGlzLnBvaXNzb25CbHVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInZpZXdNYXRyaXhJbnZcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5tYXRyaXhXb3JsZDtcbiAgICAgICAgICAgICAgICB0aGlzLnBvaXNzb25CbHVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImNhbWVyYVBvc1wiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLmdldFdvcmxkUG9zaXRpb24obmV3ICQ1V2hlMyRWZWN0b3IzKCkpO1xuICAgICAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wicmVzb2x1dGlvblwiXS52YWx1ZSA9IHRoaXMuY29uZmlndXJhdGlvbi5oYWxmUmVzID8gdGhpcy5fci5jbG9uZSgpLm11bHRpcGx5U2NhbGFyKDAuNSkuZmxvb3IoKSA6IHRoaXMuX3I7XG4gICAgICAgICAgICAgICAgdGhpcy5wb2lzc29uQmx1clF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJ0aW1lXCJdLnZhbHVlID0gcGVyZm9ybWFuY2Uubm93KCkgLyAxMDAwO1xuICAgICAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiYmx1ZU5vaXNlXCJdLnZhbHVlID0gdGhpcy5ibHVlbm9pc2U7XG4gICAgICAgICAgICAgICAgdGhpcy5wb2lzc29uQmx1clF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJyYWRpdXNcIl0udmFsdWUgPSB0aGlzLmNvbmZpZ3VyYXRpb24uZGVub2lzZVJhZGl1cyAqICh0aGlzLmNvbmZpZ3VyYXRpb24uaGFsZlJlcyA/IDAuNSA6IDEpO1xuICAgICAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wid29ybGRSYWRpdXNcIl0udmFsdWUgPSB0cnVlUmFkaXVzO1xuICAgICAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiZGlzdGFuY2VGYWxsb2ZmXCJdLnZhbHVlID0gdGhpcy5jb25maWd1cmF0aW9uLmRpc3RhbmNlRmFsbG9mZjtcbiAgICAgICAgICAgICAgICB0aGlzLnBvaXNzb25CbHVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImluZGV4XCJdLnZhbHVlID0gaTtcbiAgICAgICAgICAgICAgICB0aGlzLnBvaXNzb25CbHVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInBvaXNzb25EaXNrXCJdLnZhbHVlID0gdGhpcy5zYW1wbGVzRGVub2lzZTtcbiAgICAgICAgICAgICAgICB0aGlzLnBvaXNzb25CbHVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcIm5lYXJcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5uZWFyO1xuICAgICAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiZmFyXCJdLnZhbHVlID0gdGhpcy5jYW1lcmEuZmFyO1xuICAgICAgICAgICAgICAgIHRoaXMucG9pc3NvbkJsdXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wic2NyZWVuU3BhY2VSYWRpdXNcIl0udmFsdWUgPSB0aGlzLmNvbmZpZ3VyYXRpb24uc2NyZWVuU3BhY2VSYWRpdXM7XG4gICAgICAgICAgICAgICAgcmVuZGVyZXIuc2V0UmVuZGVyVGFyZ2V0KHRoaXMud3JpdGVUYXJnZXRJbnRlcm5hbCk7XG4gICAgICAgICAgICAgICAgdGhpcy5wb2lzc29uQmx1clF1YWQucmVuZGVyKHJlbmRlcmVyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJlbmRlcmVyLnNldFJlbmRlclRhcmdldCh0aGlzLmFjY3VtdWxhdGlvblJlbmRlclRhcmdldCk7XG4gICAgICAgICAgICBjb25zdCBvbGRBdXRvQ2xlYXIgPSByZW5kZXJlci5hdXRvQ2xlYXI7XG4gICAgICAgICAgICByZW5kZXJlci5hdXRvQ2xlYXIgPSBmYWxzZTtcbiAgICAgICAgICAgIHRoaXMuYWNjdW11bGF0aW9uUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInREaWZmdXNlXCJdLnZhbHVlID0gdGhpcy53cml0ZVRhcmdldEludGVybmFsLnRleHR1cmU7XG4gICAgICAgICAgICB0aGlzLmFjY3VtdWxhdGlvblF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJmcmFtZVwiXS52YWx1ZSA9IHRoaXMuZnJhbWU7XG4gICAgICAgICAgICB0aGlzLmFjY3VtdWxhdGlvblF1YWQucmVuZGVyKHJlbmRlcmVyKTtcbiAgICAgICAgICAgIHJlbmRlcmVyLmF1dG9DbGVhciA9IG9sZEF1dG9DbGVhcjtcbiAgICAgICAgfVxuICAgICAgICAvLyBOb3csIHdlIGhhdmUgdGhlIGJsdXJyZWQgQU8gaW4gd3JpdGVUYXJnZXRJbnRlcm5hbFxuICAgICAgICAvLyBFbmQgdGhlIGJsdXJcbiAgICAgICAgLy8gU3RhcnQgdGhlIGNvbXBvc2l0aW9uXG4gICAgICAgIGlmICh0aGlzLmNvbmZpZ3VyYXRpb24udHJhbnNwYXJlbmN5QXdhcmUpIHtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJ0cmFuc3BhcmVuY3lEV0ZhbHNlXCJdLnZhbHVlID0gdGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV0ZhbHNlLnRleHR1cmU7XG4gICAgICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1widHJhbnNwYXJlbmN5RFdUcnVlXCJdLnZhbHVlID0gdGhpcy50cmFuc3BhcmVuY3lSZW5kZXJUYXJnZXREV1RydWUudGV4dHVyZTtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJ0cmFuc3BhcmVuY3lEV1RydWVEZXB0aFwiXS52YWx1ZSA9IHRoaXMudHJhbnNwYXJlbmN5UmVuZGVyVGFyZ2V0RFdUcnVlLmRlcHRoVGV4dHVyZTtcbiAgICAgICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJ0cmFuc3BhcmVuY3lBd2FyZVwiXS52YWx1ZSA9IHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInNjZW5lRGlmZnVzZVwiXS52YWx1ZSA9IHRoaXMuYmVhdXR5UmVuZGVyVGFyZ2V0LnRleHR1cmU7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJzY2VuZURlcHRoXCJdLnZhbHVlID0gdGhpcy5iZWF1dHlSZW5kZXJUYXJnZXQuZGVwdGhUZXh0dXJlO1xuICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiYW9Ub25lc1wiXS52YWx1ZSA9IHRoaXMuY29uZmlndXJhdGlvbi5hb1RvbmVzO1xuICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wibmVhclwiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLm5lYXI7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJmYXJcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5mYXI7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJwcm9qZWN0aW9uTWF0cml4SW52XCJdLnZhbHVlID0gdGhpcy5jYW1lcmEucHJvamVjdGlvbk1hdHJpeEludmVyc2U7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJ2aWV3TWF0cml4SW52XCJdLnZhbHVlID0gdGhpcy5jYW1lcmEubWF0cml4V29ybGQ7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJvcnRob1wiXS52YWx1ZSA9IHRoaXMuY2FtZXJhLmlzT3J0aG9ncmFwaGljQ2FtZXJhO1xuICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiZG93bnNhbXBsZWREZXB0aFwiXS52YWx1ZSA9IHRoaXMuY29uZmlndXJhdGlvbi5oYWxmUmVzID8gdGhpcy5kZXB0aERvd25zYW1wbGVUYXJnZXQudGV4dHVyZXNbMF0gOiB0aGlzLmJlYXV0eVJlbmRlclRhcmdldC5kZXB0aFRleHR1cmU7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJyZXNvbHV0aW9uXCJdLnZhbHVlID0gdGhpcy5fcjtcbiAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImJsdWVOb2lzZVwiXS52YWx1ZSA9IHRoaXMuYmx1ZW5vaXNlO1xuICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiaW50ZW5zaXR5XCJdLnZhbHVlID0gdGhpcy5jb25maWd1cmF0aW9uLmludGVuc2l0eTtcbiAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInJlbmRlck1vZGVcIl0udmFsdWUgPSB0aGlzLmNvbmZpZ3VyYXRpb24ucmVuZGVyTW9kZTtcbiAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcInNjcmVlblNwYWNlUmFkaXVzXCJdLnZhbHVlID0gdGhpcy5jb25maWd1cmF0aW9uLnNjcmVlblNwYWNlUmFkaXVzO1xuICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wicmFkaXVzXCJdLnZhbHVlID0gdHJ1ZVJhZGl1cztcbiAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImRpc3RhbmNlRmFsbG9mZlwiXS52YWx1ZSA9IHRoaXMuY29uZmlndXJhdGlvbi5kaXN0YW5jZUZhbGxvZmY7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJnYW1tYUNvcnJlY3Rpb25cIl0udmFsdWUgPSB0aGlzLmNvbmZpZ3VyYXRpb24uZ2FtbWFDb3JyZWN0aW9uO1xuICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1widERpZmZ1c2VcIl0udmFsdWUgPSB0aGlzLmFjY3VtdWxhdGlvblJlbmRlclRhcmdldC50ZXh0dXJlO1xuICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiY29sb3JcIl0udmFsdWUgPSB0aGlzLl9jLmNvcHkodGhpcy5jb25maWd1cmF0aW9uLmNvbG9yKS5jb252ZXJ0U1JHQlRvTGluZWFyKCk7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJjb2xvck11bHRpcGx5XCJdLnZhbHVlID0gdGhpcy5jb25maWd1cmF0aW9uLmNvbG9yTXVsdGlwbHk7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJjYW1lcmFQb3NcIl0udmFsdWUgPSB0aGlzLmNhbWVyYS5nZXRXb3JsZFBvc2l0aW9uKG5ldyAkNVdoZTMkVmVjdG9yMygpKTtcbiAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImZvZ1wiXS52YWx1ZSA9ICEhdGhpcy5zY2VuZS5mb2c7XG4gICAgICAgIGlmICh0aGlzLnNjZW5lLmZvZykge1xuICAgICAgICAgICAgaWYgKHRoaXMuc2NlbmUuZm9nLmlzRm9nKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5lZmZlY3RDb21wb3NpdGVyUXVhZC5tYXRlcmlhbC51bmlmb3Jtc1tcImZvZ0V4cFwiXS52YWx1ZSA9IGZhbHNlO1xuICAgICAgICAgICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQubWF0ZXJpYWwudW5pZm9ybXNbXCJmb2dOZWFyXCJdLnZhbHVlID0gdGhpcy5zY2VuZS5mb2cubmVhcjtcbiAgICAgICAgICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiZm9nRmFyXCJdLnZhbHVlID0gdGhpcy5zY2VuZS5mb2cuZmFyO1xuICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlzLnNjZW5lLmZvZy5pc0ZvZ0V4cDIpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiZm9nRXhwXCJdLnZhbHVlID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICB0aGlzLmVmZmVjdENvbXBvc2l0ZXJRdWFkLm1hdGVyaWFsLnVuaWZvcm1zW1wiZm9nRGVuc2l0eVwiXS52YWx1ZSA9IHRoaXMuc2NlbmUuZm9nLmRlbnNpdHk7XG4gICAgICAgICAgICB9IGVsc2UgY29uc29sZS5lcnJvcihgVW5zdXBwb3J0ZWQgZm9nIHR5cGUgJHt0aGlzLnNjZW5lLmZvZy5jb25zdHJ1Y3Rvci5uYW1lfSBpbiBTU0FPUGFzcy5gKTtcbiAgICAgICAgfVxuICAgICAgICByZW5kZXJlci5zZXRSZW5kZXJUYXJnZXQodGhpcy5yZW5kZXJUb1NjcmVlbiA/IG51bGwgOiB3cml0ZUJ1ZmZlcik7XG4gICAgICAgIHRoaXMuZWZmZWN0Q29tcG9zaXRlclF1YWQucmVuZGVyKHJlbmRlcmVyKTtcbiAgICAgICAgaWYgKHRoaXMuZGVidWdNb2RlKSB7XG4gICAgICAgICAgICBnbC5lbmRRdWVyeShleHQuVElNRV9FTEFQU0VEX0VYVCk7XG4gICAgICAgICAgICAkMDVmNjk5N2U0YjY1ZGExNCR2YXIkY2hlY2tUaW1lclF1ZXJ5KHRpbWVyUXVlcnksIGdsLCB0aGlzKTtcbiAgICAgICAgfVxuICAgICAgICByZW5kZXJlci54ci5lbmFibGVkID0geHJFbmFibGVkO1xuICAgIH1cbiAgICAvKipcbiAgICAgICAgICogRW5hYmxlcyB0aGUgZGVidWcgbW9kZSBvZiB0aGUgQU8sIG1lYW5pbmcgdGhlIGxhc3RUaW1lIHZhbHVlIHdpbGwgYmUgdXBkYXRlZC5cbiAgICAgICAgICovIGVuYWJsZURlYnVnTW9kZSgpIHtcbiAgICAgICAgdGhpcy5kZWJ1Z01vZGUgPSB0cnVlO1xuICAgIH1cbiAgICAvKipcbiAgICAgICAgICogRGlzYWJsZXMgdGhlIGRlYnVnIG1vZGUgb2YgdGhlIEFPLCBtZWFuaW5nIHRoZSBsYXN0VGltZSB2YWx1ZSB3aWxsIG5vdCBiZSB1cGRhdGVkLlxuICAgICAgICAgKi8gZGlzYWJsZURlYnVnTW9kZSgpIHtcbiAgICAgICAgdGhpcy5kZWJ1Z01vZGUgPSBmYWxzZTtcbiAgICB9XG4gICAgLyoqXG4gICAgICAgICAqIFNldHMgdGhlIGRpc3BsYXkgbW9kZSBvZiB0aGUgQU9cbiAgICAgICAgICogQHBhcmFtIHtcIkNvbWJpbmVkXCIgfCBcIkFPXCIgfCBcIk5vIEFPXCIgfCBcIlNwbGl0XCIgfCBcIlNwbGl0IEFPXCJ9IG1vZGUgLSBUaGUgZGlzcGxheSBtb2RlLiBcbiAgICAgICAgICovIHNldERpc3BsYXlNb2RlKG1vZGUpIHtcbiAgICAgICAgdGhpcy5jb25maWd1cmF0aW9uLnJlbmRlck1vZGUgPSBbXG4gICAgICAgICAgICBcIkNvbWJpbmVkXCIsXG4gICAgICAgICAgICBcIkFPXCIsXG4gICAgICAgICAgICBcIk5vIEFPXCIsXG4gICAgICAgICAgICBcIlNwbGl0XCIsXG4gICAgICAgICAgICBcIlNwbGl0IEFPXCJcbiAgICAgICAgXS5pbmRleE9mKG1vZGUpO1xuICAgIH1cbiAgICAvKipcbiAgICAgICAgICogXG4gICAgICAgICAqIEBwYXJhbSB7XCJQZXJmb3JtYW5jZVwiIHwgXCJMb3dcIiB8IFwiTWVkaXVtXCIgfCBcIkhpZ2hcIiB8IFwiVWx0cmFcIn0gbW9kZSBcbiAgICAgICAgICovIHNldFF1YWxpdHlNb2RlKG1vZGUpIHtcbiAgICAgICAgaWYgKG1vZGUgPT09IFwiUGVyZm9ybWFuY2VcIikge1xuICAgICAgICAgICAgdGhpcy5jb25maWd1cmF0aW9uLmFvU2FtcGxlcyA9IDg7XG4gICAgICAgICAgICB0aGlzLmNvbmZpZ3VyYXRpb24uZGVub2lzZVNhbXBsZXMgPSA0O1xuICAgICAgICAgICAgdGhpcy5jb25maWd1cmF0aW9uLmRlbm9pc2VSYWRpdXMgPSAxMjtcbiAgICAgICAgfSBlbHNlIGlmIChtb2RlID09PSBcIkxvd1wiKSB7XG4gICAgICAgICAgICB0aGlzLmNvbmZpZ3VyYXRpb24uYW9TYW1wbGVzID0gMTY7XG4gICAgICAgICAgICB0aGlzLmNvbmZpZ3VyYXRpb24uZGVub2lzZVNhbXBsZXMgPSA0O1xuICAgICAgICAgICAgdGhpcy5jb25maWd1cmF0aW9uLmRlbm9pc2VSYWRpdXMgPSAxMjtcbiAgICAgICAgfSBlbHNlIGlmIChtb2RlID09PSBcIk1lZGl1bVwiKSB7XG4gICAgICAgICAgICB0aGlzLmNvbmZpZ3VyYXRpb24uYW9TYW1wbGVzID0gMTY7XG4gICAgICAgICAgICB0aGlzLmNvbmZpZ3VyYXRpb24uZGVub2lzZVNhbXBsZXMgPSA4O1xuICAgICAgICAgICAgdGhpcy5jb25maWd1cmF0aW9uLmRlbm9pc2VSYWRpdXMgPSAxMjtcbiAgICAgICAgfSBlbHNlIGlmIChtb2RlID09PSBcIkhpZ2hcIikge1xuICAgICAgICAgICAgdGhpcy5jb25maWd1cmF0aW9uLmFvU2FtcGxlcyA9IDY0O1xuICAgICAgICAgICAgdGhpcy5jb25maWd1cmF0aW9uLmRlbm9pc2VTYW1wbGVzID0gODtcbiAgICAgICAgICAgIHRoaXMuY29uZmlndXJhdGlvbi5kZW5vaXNlUmFkaXVzID0gNjtcbiAgICAgICAgfSBlbHNlIGlmIChtb2RlID09PSBcIlVsdHJhXCIpIHtcbiAgICAgICAgICAgIHRoaXMuY29uZmlndXJhdGlvbi5hb1NhbXBsZXMgPSA2NDtcbiAgICAgICAgICAgIHRoaXMuY29uZmlndXJhdGlvbi5kZW5vaXNlU2FtcGxlcyA9IDE2O1xuICAgICAgICAgICAgdGhpcy5jb25maWd1cmF0aW9uLmRlbm9pc2VSYWRpdXMgPSA2O1xuICAgICAgICB9XG4gICAgfVxufVxuXG5cbmV4cG9ydCB7JDA1ZjY5OTdlNGI2NWRhMTQkZXhwb3J0JGVkNGVlNWQxZTU1NDc0YTUgYXMgRGVwdGhUeXBlLCAkMDVmNjk5N2U0YjY1ZGExNCRleHBvcnQkMmQ1N2RiMjBiNWViNWUwYSBhcyBOOEFPUGFzcywgJDg3NDMxZWU5M2IwMzc4NDQkZXhwb3J0JDI0ODlmOTk4MWFiMGZhODIgYXMgTjhBT1Bvc3RQYXNzfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPU44QU8uanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/n8ao/dist/N8AO.js\n");

/***/ })

};
;