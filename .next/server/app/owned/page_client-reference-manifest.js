globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/owned/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/sonner/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/providers.tsx":{"*":{"id":"(ssr)/./src/app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/header.tsx":{"*":{"id":"(ssr)/./src/components/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/debug-owned/page.tsx":{"*":{"id":"(ssr)/./src/app/debug-owned/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/create/page.tsx":{"*":{"id":"(ssr)/./src/app/create/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/owned/page.tsx":{"*":{"id":"(ssr)/./src/app/owned/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/home/<USER>/chetan/spectramint/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/chetan/spectramint/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/chetan/spectramint/node_modules/sonner/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/chetan/spectramint/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/chetan/spectramint/src/app/providers.tsx":{"id":"(app-pages-browser)/./src/app/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/chetan/spectramint/src/components/header.tsx":{"id":"(app-pages-browser)/./src/components/header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/chetan/spectramint/src/app/debug-owned/page.tsx":{"id":"(app-pages-browser)/./src/app/debug-owned/page.tsx","name":"*","chunks":[],"async":false},"/home/<USER>/chetan/spectramint/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/chetan/spectramint/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/chetan/spectramint/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/chetan/spectramint/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/chetan/spectramint/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/chetan/spectramint/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/chetan/spectramint/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/chetan/spectramint/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/chetan/spectramint/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/chetan/spectramint/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/chetan/spectramint/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/chetan/spectramint/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/chetan/spectramint/node_modules/next/dist/lib/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/chetan/spectramint/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/chetan/spectramint/src/app/create/page.tsx":{"id":"(app-pages-browser)/./src/app/create/page.tsx","name":"*","chunks":[],"async":false},"/home/<USER>/chetan/spectramint/src/app/owned/page.tsx":{"id":"(app-pages-browser)/./src/app/owned/page.tsx","name":"*","chunks":["app/owned/page","static/chunks/app/owned/page.js"],"async":false}},"entryCSSFiles":{"/home/<USER>/chetan/spectramint/src/":[],"/home/<USER>/chetan/spectramint/src/app/layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"/home/<USER>/chetan/spectramint/src/app/owned/page":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/sonner/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/providers.tsx":{"*":{"id":"(rsc)/./src/app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/header.tsx":{"*":{"id":"(rsc)/./src/components/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/debug-owned/page.tsx":{"*":{"id":"(rsc)/./src/app/debug-owned/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/create/page.tsx":{"*":{"id":"(rsc)/./src/app/create/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/owned/page.tsx":{"*":{"id":"(rsc)/./src/app/owned/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}