// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {<PERSON>ript} from "forge-std/Script.sol";
import {console} from "forge-std/console.sol";
import {Glb3dNft} from "../src/Glb3dNft.sol";
import {NftMarketplace} from "../src/NftMarketplace.sol";

contract DeployToAnvil is Script {
    function run() external {
        // Use the default Anvil private key (account 0)
        uint256 deployerPrivateKey = 0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80;
        address deployer = vm.addr(deployerPrivateKey);

        console.log("Deploying contracts with account:", deployer);
        console.log("Account balance:", deployer.balance);

        vm.startBroadcast(deployerPrivateKey);

        // Deploy GLB3D NFT Contract
        Glb3dNft glb3dNft = new Glb3dNft();
        console.log("GLB3D NFT Contract deployed at:", address(glb3dNft));

        // Deploy NFT Marketplace Contract
        NftMarketplace marketplace = new NftMarketplace();
        console.log("NFT Marketplace Contract deployed at:", address(marketplace));

        vm.stopBroadcast();

        // Create a TypeScript formatted output matching our existing structure
        string memory jsOutput = string(
            abi.encodePacked(
                "// Contract addresses for different networks\n",
                "// This file will be auto-generated by the deployment script\n\n",
                "export const CONTRACT_ADDRESSES = {\n",
                "  // Anvil local development\n",
                "  31337: {\n",
                "    GLB3D_NFT: \"",
                vm.toString(address(glb3dNft)),
                "\",\n",
                "    NFT_MARKETPLACE: \"",
                vm.toString(address(marketplace)),
                "\",\n",
                "  },\n",
                "  // Add other networks as needed\n",
                "  // 1: { // Ethereum Mainnet\n",
                "  //   GLB3D_NFT: \"\",\n",
                "  //   NFT_MARKETPLACE: \"\",\n",
                "  // },\n",
                "  // 11155111: { // Sepolia Testnet\n",
                "  //   GLB3D_NFT: \"\",\n",
                "  //   NFT_MARKETPLACE: \"\",\n",
                "  // },\n",
                "} as const;\n\n",
                "export type SupportedChainId = keyof typeof CONTRACT_ADDRESSES;\n\n",
                "export function getContractAddress(\n",
                "  chainId: SupportedChainId,\n",
                "  contract: \"GLB3D_NFT\" | \"NFT_MARKETPLACE\"\n",
                "): `0x${string}` {\n",
                "  const address = CONTRACT_ADDRESSES[chainId]?.[contract];\n",
                "  if (!address || address === \"******************************************\") {\n",
                "    throw new Error(`Contract ${contract} not deployed on chain ${chainId}`);\n",
                "  }\n",
                "  return address as `0x${string}`;\n",
                "}\n"
            )
        );

        vm.writeFile("../src/lib/contracts/addresses.ts", jsOutput);
        console.log("Contract addresses saved to ../src/lib/contracts/addresses.ts");

        // Also create a simple .env format for backup
        string memory envOutput = string(
            abi.encodePacked(
                "GLB3D_NFT_ADDRESS=",
                vm.toString(address(glb3dNft)),
                "\n",
                "NFT_MARKETPLACE_ADDRESS=",
                vm.toString(address(marketplace)),
                "\n"
            )
        );

        vm.writeFile("./deployments.env", envOutput);
        console.log("Deployment addresses also saved to ./deployments.env");
    }
}
