"use client";

import SingleMarketItem from "@/components/market/single-item";
import MaxWidthWrapper from "@/components/wrappers/max-width-wrapper";
import { getAllFilesFromIPFS } from "@/lib/utils/pinata";
import { LoaderCircle } from "lucide-react";
import React, { useEffect, useState } from "react";
import NFTCard from "@/components/nft/nft-card";
import { useAccount, useChainId, useReadContract } from "wagmi";
import { getContractAddress } from "@/lib/contracts/addresses";
import { GLB3D_NFT_ABI } from "@/lib/contracts/abis";
import { foundry } from "wagmi/chains";
import ConnectWalletButton from "@/components/auth/connect-wallet-button";

interface IPFSFile {
  id: string;
  ipfs_pin_hash: string;
  size: number;
  user_id: string;
  date_pinned: string;
  date_unpinned: string | null;
  metadata: {
    name: string | null;
    keyvalues: Record<string, any> | null;
  };
  regions: Array<{
    regionId: string;
    currentReplicationCount: number;
    desiredReplicationCount: number;
  }>;
  mime_type: string;
  number_of_files: number;
}

const Market = () => {
  const { isConnected } = useAccount();
  const chainId = useChainId();
  const [nftTokenIds, setNftTokenIds] = useState<bigint[]>([]);
  const [isLoadingNFTs, setIsLoadingNFTs] = useState(false);

  // Get total supply of NFTs to know how many exist
  const { data: totalSupply, isLoading: isLoadingSupply } = useReadContract({
    address: chainId === foundry.id ? getContractAddress(chainId as 31337, 'GLB3D_NFT') : undefined,
    abi: GLB3D_NFT_ABI,
    functionName: 'balanceOf',
    args: chainId === foundry.id ? [getContractAddress(chainId as 31337, 'GLB3D_NFT')] : undefined,
  });

  // Generate array of token IDs (assuming sequential minting starting from 0)
  useEffect(() => {
    if (chainId === foundry.id) {
      setIsLoadingNFTs(true);
      // For now, we'll check for the first 100 possible token IDs
      // In a real app, you'd want to use events or a subgraph to track minted tokens
      const checkTokens = async () => {
        const tokenIds: bigint[] = [];

        // Check tokens 0-99 (you can adjust this range)
        for (let i = 0; i < 100; i++) {
          try {
            // Try to get the owner of each token ID
            const response = await fetch('http://localhost:8545', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'eth_call',
                params: [
                  {
                    to: getContractAddress(chainId as 31337, 'GLB3D_NFT'),
                    data: `0x6352211e${i.toString(16).padStart(64, '0')}`, // ownerOf(tokenId)
                  },
                  'latest'
                ],
                id: 1,
              }),
            });

            const result = await response.json();

            // If the call doesn't revert, the token exists
            if (result.result && result.result !== '0x') {
              tokenIds.push(BigInt(i));
            }
          } catch (error) {
            // Token doesn't exist, continue
            break;
          }
        }

        setNftTokenIds(tokenIds);
        setIsLoadingNFTs(false);
      };

      checkTokens();
    }
  }, [chainId]);

  if (!isConnected) {
    return (
      <div className="flex flex-col items-center justify-center py-20">
        <p className="mb-4 text-lg font-bold">
          Please connect your wallet to view the marketplace.
        </p>
        <ConnectWalletButton />
      </div>
    );
  }

  if (chainId !== foundry.id) {
    return (
      <div className="flex flex-col items-center justify-center py-20">
        <p className="mb-4 text-lg font-bold text-destructive">
          Please switch to Anvil network (Chain ID: 31337)
        </p>
        <p className="text-sm text-muted-foreground mb-4">
          Current network: {chainId}
        </p>
        <ConnectWalletButton />
      </div>
    );
  }

  if (isLoadingNFTs || isLoadingSupply) {
    return (
      <div className="flex items-center justify-center pt-20">
        <LoaderCircle className="animate-spin mr-2" />
        <span>Loading NFTs...</span>
      </div>
    );
  }

  if (nftTokenIds.length === 0) {
    return (
      <div className="p-5 sm:p-2 sm:pt-20 pt-20">
        <h1 className="text-4xl font-bold mb-10 text-center">Explore Market</h1>
        <div className="text-center py-10">
          <p className="text-xl text-muted-foreground">No NFTs have been minted yet</p>
          <p className="text-sm text-muted-foreground mt-2">
            Be the first to create an NFT!
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-5 sm:p-2 sm:pt-20 pt-20">
      <h1 className="text-4xl font-bold mb-10 text-center">Explore Market</h1>
      <MaxWidthWrapper className="grid grid-cols-1 lg:grid-cols-4 sm:grid-cols-2 gap-6">
        {nftTokenIds.map((tokenId) => (
          <NFTCard
            key={tokenId.toString()}
            tokenId={tokenId}
            showMarketplaceActions={true}
          />
        ))}
      </MaxWidthWrapper>
    </div>
  );
};

export default Market;
