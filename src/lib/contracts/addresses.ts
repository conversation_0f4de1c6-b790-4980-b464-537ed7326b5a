// Contract addresses for different networks
// This file will be auto-generated by the deployment script

export const CONTRACT_ADDRESSES = {
  // Anvil local development
  31337: {
    GLB3D_NFT: "******************************************",
    NFT_MARKETPLACE: "******************************************",
  },
  // Add other networks as needed
  // 1: { // Ethereum Mainnet
  //   GLB3D_NFT: "",
  //   NFT_MARKETPLACE: "",
  // },
  // 11155111: { // Sepolia Testnet
  //   GLB3D_NFT: "",
  //   NFT_MARKETPLACE: "",
  // },
} as const;

export type SupportedChainId = keyof typeof CONTRACT_ADDRESSES;

export function getContractAddress(
  chainId: SupportedChainId,
  contract: "GLB3D_NFT" | "NFT_MARKETPLACE"
): `0x${string}` {
  const address = CONTRACT_ADDRESSES[chainId]?.[contract];
  if (!address || address === "******************************************") {
    throw new Error(`Contract ${contract} not deployed on chain ${chainId}`);
  }
  return address as `0x${string}`;
}
