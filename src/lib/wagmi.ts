import { http, createConfig } from 'wagmi'
import { mainnet, sepolia, foundry } from 'wagmi/chains'
import { injected, metaMask } from 'wagmi/connectors'

// Define the chains we support
export const supportedChains = [foundry, sepolia, mainnet] as const

// Create wagmi config without WalletConnect for now (to avoid project ID requirement)
export const config = createConfig({
  chains: supportedChains,
  connectors: [
    injected(),
    metaMask(),
  ],
  transports: {
    [foundry.id]: http('http://localhost:8545'),
    [sepolia.id]: http(),
    [mainnet.id]: http(),
  },
})

declare module 'wagmi' {
  interface Register {
    config: typeof config
  }
}
